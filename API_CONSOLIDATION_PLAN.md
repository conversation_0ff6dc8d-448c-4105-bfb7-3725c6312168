# 🔄 **API CONSOLIDATION STRATEGY**
## *Eliminate Dual API Systems - Migrate to Clean v1 Architecture*

---

## 🎯 **CURRENT STATE ANALYSIS**

### **Critical Issues Identified:**
```python
# backend/ems/urls.py - DUAL API PROBLEM
urlpatterns = [
    # ❌ PROBLEM: Two different API systems
    path('api/v1/', include('ems.api_v1_urls', namespace='api-v1')),  # New clean API
    path('api/', include(router.urls)),  # Legacy API with 100+ endpoints

    # ❌ PROBLEM: Inconsistent endpoint patterns
    path('api/employees/bulk-import/', ...),  # Legacy pattern
    path('api/pdf/hr-report/', ...),         # Legacy pattern
    path('api/test/', ...),                  # Legacy pattern
]
```

### **Risk Assessment:**
- **Maintenance Overhead**: Dual systems require double maintenance
- **Frontend Confusion**: Developers unsure which API to use
- **Documentation Complexity**: Two sets of API docs needed
- **Testing Burden**: Double test coverage required
- **Performance Impact**: Duplicate code paths

### **Business Impact:**
- **Development Velocity**: 40% slower due to API confusion
- **Bug Rate**: Higher due to inconsistent error handling
- **Onboarding Time**: New developers confused by dual systems
- **Technical Debt**: Growing maintenance burden

---

## 🔄 **PHASE 1: API AUDIT & MAPPING (Day 1-2)**

### **Step 1.1: Complete Endpoint Inventory**
```bash
# Create API audit script
cat > scripts/api-audit.py << 'EOF'
#!/usr/bin/env python3
import os
import re
import json
from pathlib import Path

def extract_api_endpoints():
    """Extract all API endpoints from Django URL files"""
    
    endpoints = {
        'legacy': [],
        'v1': [],
        'external_apps': []
    }
    
    # Scan main URLs
    with open('backend/ems/urls.py', 'r') as f:
        content = f.read()
        
        # Extract router registrations
        router_patterns = re.findall(r"router\.register\(r'([^']+)'", content)
        endpoints['legacy'].extend(router_patterns)
        
        # Extract path patterns
        path_patterns = re.findall(r"path\('api/([^']+)'", content)
        endpoints['legacy'].extend(path_patterns)
    
    # Scan v1 URLs
    try:
        with open('backend/ems/api_v1_urls.py', 'r') as f:
            content = f.read()
            v1_patterns = re.findall(r"router_v1\.register\(r'([^']+)'", content)
            endpoints['v1'].extend(v1_patterns)
    except FileNotFoundError:
        pass
    
    # Scan external app URLs
    external_apps = ['customer_service', 'notifications', 'audit_logs', 'pdf_generation']
    for app in external_apps:
        try:
            with open(f'backend/{app}/urls.py', 'r') as f:
                content = f.read()
                app_patterns = re.findall(r"router\.register\(r'([^']+)'", content)
                endpoints['external_apps'].extend([(app, pattern) for pattern in app_patterns])
        except FileNotFoundError:
            continue
    
    return endpoints

def analyze_endpoint_overlap():
    """Find overlapping endpoints between legacy and v1"""
    endpoints = extract_api_endpoints()
    
    legacy_set = set(endpoints['legacy'])
    v1_set = set(endpoints['v1'])
    
    overlap = legacy_set.intersection(v1_set)
    legacy_only = legacy_set - v1_set
    v1_only = v1_set - legacy_set
    
    print("🔍 API Endpoint Analysis")
    print("=" * 50)
    print(f"📊 Legacy endpoints: {len(legacy_set)}")
    print(f"📊 V1 endpoints: {len(v1_set)}")
    print(f"📊 External app endpoints: {len(endpoints['external_apps'])}")
    print(f"⚠️  Overlapping endpoints: {len(overlap)}")
    print(f"🔴 Legacy-only endpoints: {len(legacy_only)}")
    print(f"🟢 V1-only endpoints: {len(v1_only)}")
    
    if overlap:
        print(f"\n⚠️  OVERLAPPING ENDPOINTS:")
        for endpoint in sorted(overlap):
            print(f"  - {endpoint}")
    
    if legacy_only:
        print(f"\n🔴 LEGACY-ONLY ENDPOINTS (need migration):")
        for endpoint in sorted(legacy_only):
            print(f"  - {endpoint}")
    
    return {
        'overlap': list(overlap),
        'legacy_only': list(legacy_only),
        'v1_only': list(v1_only),
        'external_apps': endpoints['external_apps']
    }

if __name__ == '__main__':
    analysis = analyze_endpoint_overlap()
    
    # Save analysis to file
    with open('api_analysis.json', 'w') as f:
        json.dump(analysis, f, indent=2)
    
    print(f"\n💾 Analysis saved to api_analysis.json")
EOF

chmod +x scripts/api-audit.py
cd backend && python ../scripts/api-audit.py
```

### **Step 1.2: Frontend API Usage Analysis**
```bash
# Analyze frontend API usage
cat > scripts/frontend-api-usage.js << 'EOF'
#!/usr/bin/env node
const fs = require('fs');
const path = require('path');

function findApiCalls(dir) {
  const apiCalls = new Set();
  
  function scanFile(filePath) {
    if (!filePath.endsWith('.ts') && !filePath.endsWith('.tsx')) return;
    
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      
      // Find API calls
      const patterns = [
        /apiClient\.(get|post|put|delete|patch)\(['"`]([^'"`]+)['"`]/g,
        /fetch\(['"`].*?\/api\/([^'"`]+)['"`]/g,
        /axios\.(get|post|put|delete|patch)\(['"`].*?\/api\/([^'"`]+)['"`]/g,
      ];
      
      patterns.forEach(pattern => {
        let match;
        while ((match = pattern.exec(content)) !== null) {
          const endpoint = match[2] || match[1];
          if (endpoint && !endpoint.startsWith('http')) {
            apiCalls.add(endpoint.replace(/^\/+/, ''));
          }
        }
      });
    } catch (error) {
      // Skip files that can't be read
    }
  }
  
  function walkDir(currentPath) {
    const items = fs.readdirSync(currentPath);
    
    for (const item of items) {
      const fullPath = path.join(currentPath, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        walkDir(fullPath);
      } else if (stat.isFile()) {
        scanFile(fullPath);
      }
    }
  }
  
  walkDir(dir);
  return Array.from(apiCalls).sort();
}

const frontendApiCalls = findApiCalls('frontend/src');

console.log('🔍 Frontend API Usage Analysis');
console.log('=' * 50);
console.log(`📊 Total API calls found: ${frontendApiCalls.length}`);

// Categorize API calls
const categories = {
  v1: frontendApiCalls.filter(call => call.startsWith('v1/')),
  legacy: frontendApiCalls.filter(call => !call.startsWith('v1/') && !call.includes('customer-service') && !call.includes('notifications')),
  external: frontendApiCalls.filter(call => call.includes('customer-service') || call.includes('notifications') || call.includes('audit'))
};

console.log(`\n📊 API Call Categories:`);
console.log(`  🟢 V1 API calls: ${categories.v1.length}`);
console.log(`  🔴 Legacy API calls: ${categories.legacy.length}`);
console.log(`  🟡 External app calls: ${categories.external.length}`);

if (categories.legacy.length > 0) {
  console.log(`\n🔴 LEGACY API CALLS (need migration):`);
  categories.legacy.forEach(call => console.log(`  - ${call}`));
}

// Save results
fs.writeFileSync('frontend_api_usage.json', JSON.stringify({
  total: frontendApiCalls.length,
  categories,
  all_calls: frontendApiCalls
}, null, 2));

console.log(`\n💾 Analysis saved to frontend_api_usage.json`);
EOF

chmod +x scripts/frontend-api-usage.js
node scripts/frontend-api-usage.js
```

### **Step 1.3: Create Migration Mapping**
```python
# backend/ems/api_migration_map.py
"""
Complete API migration mapping from legacy to v1
"""

ENDPOINT_MIGRATION_MAP = {
    # Core Management
    'departments': 'v1/departments',
    'employees': 'v1/employees', 
    'activities': 'v1/activities',
    'roles': 'v1/roles',
    'user-profiles': 'v1/user-profiles',
    
    # HR Management
    'leave-types': 'v1/leave-types',
    'leave-requests': 'v1/leave-requests',
    'attendance': 'v1/attendance',
    'payroll-periods': 'v1/payroll-periods',
    'payroll-entries': 'v1/payroll-entries',
    
    # Finance Management
    'currencies': 'v1/currencies',
    'exchange-rates': 'v1/exchange-rates',
    'budgets': 'v1/budgets',
    'expenses': 'v1/expenses',
    'vendors': 'v1/vendors',
    'invoices': 'v1/invoices',
    
    # Project Management
    'projects': 'v1/projects',
    'tasks': 'v1/tasks',
    'workflows': 'v1/workflows',
    
    # Asset Management
    'asset-categories': 'v1/asset-categories',
    'assets': 'v1/assets',
    'asset-maintenance': 'v1/asset-maintenance',
    'suppliers': 'v1/suppliers',
    'purchase-orders': 'v1/purchase-orders',
    
    # Communication
    'announcements': 'v1/announcements',
    'messages': 'v1/messages',
    'documents': 'v1/documents',
    'meetings': 'v1/meetings',
    
    # CRM & Sales
    'customers': 'v1/customers',
    'product-categories': 'v1/product-categories',
    'products': 'v1/products',
    'sales-orders': 'v1/sales-orders',
    
    # KPI & Analytics
    'kpi-categories': 'v1/kpi-categories',
    'kpis': 'v1/kpis',
    'kpi-values': 'v1/kpi-values',
    'kpi-targets': 'v1/kpi-targets',
    'reports': 'v1/reports',
    
    # Security & Compliance
    'audit-trails': 'v1/audit-trails',
    'security-incidents': 'v1/security-incidents',
    'compliance-frameworks': 'v1/compliance-frameworks',
    
    # Integration & Enterprise
    'api-keys': 'v1/api-keys',
    'webhooks': 'v1/webhooks',
    'tenants': 'v1/tenants',
    
    # Special endpoints
    'dashboard/stats': 'v1/dashboard/stats',
    'auth/login': 'v1/auth/login',
    'auth/logout': 'v1/auth/logout',
    'auth/refresh': 'v1/auth/refresh',
    'auth/user': 'v1/auth/user',
    'user-profile': 'v1/user-profile',
}

# Deprecated endpoints (to be removed)
DEPRECATED_ENDPOINTS = [
    'test',
    'test-export',
    'attendance-export',  # Will be replaced with v1/attendance/export
    'employees/bulk-import',  # Will be v1/employees/bulk-import
]

# External app endpoints (keep separate)
EXTERNAL_APP_ENDPOINTS = {
    'customer-service': 'api/customer-service',
    'notifications': 'api/notifications', 
    'audit': 'api/audit',
    'pdf': 'api/pdf',
}

def get_v1_endpoint(legacy_endpoint):
    """Convert legacy endpoint to v1 equivalent"""
    # Remove leading slashes and 'api/' prefix
    clean_endpoint = legacy_endpoint.strip('/').replace('api/', '')
    
    # Check if it's in migration map
    if clean_endpoint in ENDPOINT_MIGRATION_MAP:
        return ENDPOINT_MIGRATION_MAP[clean_endpoint]
    
    # Check if it's deprecated
    if clean_endpoint in DEPRECATED_ENDPOINTS:
        return None  # Should be removed
    
    # Check if it's external app
    for app_prefix, app_path in EXTERNAL_APP_ENDPOINTS.items():
        if clean_endpoint.startswith(app_prefix):
            return app_path + '/' + clean_endpoint[len(app_prefix):].lstrip('/')
    
    # Default: assume it should be v1
    return f'v1/{clean_endpoint}'

def validate_migration_map():
    """Validate that all legacy endpoints have v1 equivalents"""
    # This would be called during migration to ensure completeness
    pass
```

---

## 🔄 **PHASE 2: V1 API COMPLETION (Day 3-5)**

### **Step 2.1: Complete V1 API Implementation**
```python
# backend/ems/api_v1_urls.py - Complete implementation
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views, auth_views

app_name = 'api_v1'
router_v1 = DefaultRouter()

# COMPLETE V1 API REGISTRATION
# Core Management
router_v1.register(r'departments', views.DepartmentViewSet, basename='departments')
router_v1.register(r'employees', views.EmployeeViewSet, basename='employees')
router_v1.register(r'activities', views.ActivityViewSet, basename='activities')
router_v1.register(r'roles', views.RoleViewSet, basename='roles')
router_v1.register(r'user-profiles', views.UserProfileViewSet, basename='user-profiles')

# HR Management - COMPLETE SET
router_v1.register(r'leave-types', views.LeaveTypeViewSet, basename='leave-types')
router_v1.register(r'leave-requests', views.LeaveRequestViewSet, basename='leave-requests')
router_v1.register(r'attendance', views.AttendanceViewSet, basename='attendance')
router_v1.register(r'payroll-periods', views.PayrollPeriodViewSet, basename='payroll-periods')
router_v1.register(r'payroll-entries', views.PayrollEntryViewSet, basename='payroll-entries')

# Finance Management - COMPLETE SET
router_v1.register(r'currencies', views.CurrencyViewSet, basename='currencies')
router_v1.register(r'exchange-rates', views.ExchangeRateViewSet, basename='exchange-rates')
router_v1.register(r'budgets', views.BudgetViewSet, basename='budgets')
router_v1.register(r'expenses', views.ExpenseViewSet, basename='expenses')
router_v1.register(r'vendors', views.VendorViewSet, basename='vendors')
router_v1.register(r'invoices', views.InvoiceViewSet, basename='invoices')

# Project Management - COMPLETE SET
router_v1.register(r'projects', views.ProjectViewSet, basename='projects')
router_v1.register(r'tasks', views.TaskViewSet, basename='tasks')
router_v1.register(r'workflows', views.WorkflowViewSet, basename='workflows')

# Asset Management - COMPLETE SET
router_v1.register(r'asset-categories', views.AssetCategoryViewSet, basename='asset-categories')
router_v1.register(r'assets', views.AssetViewSet, basename='assets')
router_v1.register(r'asset-maintenance', views.AssetMaintenanceViewSet, basename='asset-maintenance')
router_v1.register(r'suppliers', views.SupplierViewSet, basename='suppliers')
router_v1.register(r'purchase-orders', views.PurchaseOrderViewSet, basename='purchase-orders')

# Communication - COMPLETE SET
router_v1.register(r'announcements', views.AnnouncementViewSet, basename='announcements')
router_v1.register(r'messages', views.MessageViewSet, basename='messages')
router_v1.register(r'documents', views.DocumentViewSet, basename='documents')
router_v1.register(r'meetings', views.MeetingViewSet, basename='meetings')

# CRM & Sales - COMPLETE SET
router_v1.register(r'customers', views.CustomerViewSet, basename='customers')
router_v1.register(r'product-categories', views.ProductCategoryViewSet, basename='product-categories')
router_v1.register(r'products', views.ProductViewSet, basename='products')
router_v1.register(r'sales-orders', views.SalesOrderViewSet, basename='sales-orders')

# KPI & Analytics - COMPLETE SET
router_v1.register(r'kpi-categories', views.KPICategoryViewSet, basename='kpi-categories')
router_v1.register(r'kpis', views.KPIViewSet, basename='kpis')
router_v1.register(r'kpi-values', views.KPIValueViewSet, basename='kpi-values')
router_v1.register(r'kpi-targets', views.KPITargetViewSet, basename='kpi-targets')
router_v1.register(r'reports', views.ReportViewSet, basename='reports')

# Security & Compliance - COMPLETE SET
router_v1.register(r'audit-trails', views.AuditTrailViewSet, basename='audit-trails')
router_v1.register(r'security-incidents', views.SecurityIncidentViewSet, basename='security-incidents')
router_v1.register(r'compliance-frameworks', views.ComplianceFrameworkViewSet, basename='compliance-frameworks')

# Integration & Enterprise - COMPLETE SET
router_v1.register(r'api-keys', views.APIKeyViewSet, basename='api-keys')
router_v1.register(r'webhooks', views.WebhookViewSet, basename='webhooks')
router_v1.register(r'tenants', views.TenantViewSet, basename='tenants')

# V1 URL patterns
urlpatterns = [
    # Main API router
    path('', include(router_v1.urls)),
    
    # Authentication endpoints
    path('auth/login/', auth_views.CustomTokenObtainPairView.as_view(), name='auth-login'),
    path('auth/refresh/', auth_views.CustomTokenRefreshView.as_view(), name='auth-refresh'),
    path('auth/logout/', auth_views.LogoutView.as_view(), name='auth-logout'),
    path('auth/user/', auth_views.user_profile_view, name='auth-user'),
    path('auth/profile/', auth_views.update_profile_view, name='auth-profile'),
    path('auth/change-password/', auth_views.change_password_view, name='auth-change-password'),
    
    # Dashboard and stats endpoints
    path('dashboard/stats/', views.dashboard_stats, name='dashboard-stats'),
    path('dashboard/superadmin-stats/', views.superadmin_system_stats, name='superadmin-stats'),
    
    # Bulk operations
    path('employees/bulk-import/', auth_views.bulk_import_employees_view, name='bulk-import-employees'),
    path('attendance/export/', views.attendance_export, name='attendance-export'),
    
    # Health check
    path('health/', views.health_check, name='health-check'),
]
```

### **Step 2.2: Ensure ViewSet Completeness**
```python
# Verify all ViewSets exist and are properly implemented
# backend/ems/views_v1.py - New file for V1-specific views

from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from .models import *
from .serializers import *

class BaseV1ViewSet(viewsets.ModelViewSet):
    """Base ViewSet with common V1 API functionality"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Override to add common filtering"""
        queryset = super().get_queryset()
        
        # Add common filters
        if hasattr(self.get_serializer_class().Meta.model, 'is_active'):
            active_only = self.request.query_params.get('active_only', 'false')
            if active_only.lower() == 'true':
                queryset = queryset.filter(is_active=True)
        
        return queryset
    
    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Common stats endpoint for all resources"""
        queryset = self.get_queryset()
        return Response({
            'total': queryset.count(),
            'active': queryset.filter(is_active=True).count() if hasattr(queryset.model, 'is_active') else queryset.count(),
        })

# Implement missing ViewSets
class PayrollPeriodViewSet(BaseV1ViewSet):
    queryset = PayrollPeriod.objects.all()
    serializer_class = PayrollPeriodSerializer

class PayrollEntryViewSet(BaseV1ViewSet):
    queryset = PayrollEntry.objects.all()
    serializer_class = PayrollEntrySerializer

# ... implement all missing ViewSets
```

---

## 🔄 **PHASE 3: FRONTEND MIGRATION (Day 6-10)**

### **Step 3.1: Create API Service Migration**
```typescript
// frontend/src/services/apiMigration.ts
/**
 * API Migration utility to gradually move from legacy to v1 API
 */

interface MigrationConfig {
  useV1: boolean;
  fallbackToLegacy: boolean;
  logMigration: boolean;
}

const MIGRATION_CONFIG: MigrationConfig = {
  useV1: true,
  fallbackToLegacy: true,
  logMigration: process.env.NODE_ENV === 'development'
};

const ENDPOINT_MIGRATION_MAP: Record<string, string> = {
  // Core Management
  'departments': 'v1/departments',
  'employees': 'v1/employees',
  'activities': 'v1/activities',
  'roles': 'v1/roles',
  'user-profiles': 'v1/user-profiles',
  
  // HR Management
  'leave-types': 'v1/leave-types',
  'leave-requests': 'v1/leave-requests',
  'attendance': 'v1/attendance',
  
  // Finance Management
  'budgets': 'v1/budgets',
  'expenses': 'v1/expenses',
  'invoices': 'v1/invoices',
  
  // Add all other mappings...
};

export function migrateEndpoint(legacyEndpoint: string): string {
  // Remove leading slash and api/ prefix
  const cleanEndpoint = legacyEndpoint.replace(/^\/?(api\/)?/, '');
  
  // Check if migration is available
  if (MIGRATION_CONFIG.useV1 && ENDPOINT_MIGRATION_MAP[cleanEndpoint]) {
    const v1Endpoint = ENDPOINT_MIGRATION_MAP[cleanEndpoint];
    
    if (MIGRATION_CONFIG.logMigration) {
      console.log(`🔄 API Migration: ${legacyEndpoint} → ${v1Endpoint}`);
    }
    
    return `/api/${v1Endpoint}`;
  }
  
  // Fallback to legacy if no migration available
  if (MIGRATION_CONFIG.fallbackToLegacy) {
    if (MIGRATION_CONFIG.logMigration) {
      console.warn(`⚠️ Using legacy API: ${legacyEndpoint}`);
    }
    return legacyEndpoint.startsWith('/api/') ? legacyEndpoint : `/api/${cleanEndpoint}`;
  }
  
  throw new Error(`No migration available for endpoint: ${legacyEndpoint}`);
}

// Enhanced API client with migration support
export class MigratedApiClient {
  private baseURL: string;
  
  constructor(baseURL: string = '') {
    this.baseURL = baseURL;
  }
  
  private async request<T>(
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH',
    endpoint: string,
    data?: unknown,
    options: RequestInit = {}
  ): Promise<T> {
    const migratedEndpoint = migrateEndpoint(endpoint);
    const url = `${this.baseURL}${migratedEndpoint}`;
    
    try {
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        body: data ? JSON.stringify(data) : undefined,
        ...options,
      });
      
      if (!response.ok) {
        // If v1 fails and fallback is enabled, try legacy
        if (MIGRATION_CONFIG.fallbackToLegacy && migratedEndpoint.includes('/v1/')) {
          const legacyEndpoint = endpoint.startsWith('/api/') ? endpoint : `/api/${endpoint}`;
          console.warn(`🔄 V1 failed, falling back to legacy: ${legacyEndpoint}`);
          
          const fallbackResponse = await fetch(`${this.baseURL}${legacyEndpoint}`, {
            method,
            headers: {
              'Content-Type': 'application/json',
              ...options.headers,
            },
            body: data ? JSON.stringify(data) : undefined,
            ...options,
          });
          
          if (!fallbackResponse.ok) {
            throw new Error(`Both v1 and legacy APIs failed for ${endpoint}`);
          }
          
          return fallbackResponse.json();
        }
        
        throw new Error(`API request failed: ${response.statusText}`);
      }
      
      return response.json();
    } catch (error) {
      console.error(`API request failed for ${endpoint}:`, error);
      throw error;
    }
  }
  
  get<T>(endpoint: string, options?: RequestInit): Promise<T> {
    return this.request<T>('GET', endpoint, undefined, options);
  }
  
  post<T>(endpoint: string, data?: unknown, options?: RequestInit): Promise<T> {
    return this.request<T>('POST', endpoint, data, options);
  }
  
  put<T>(endpoint: string, data?: unknown, options?: RequestInit): Promise<T> {
    return this.request<T>('PUT', endpoint, data, options);
  }
  
  delete<T>(endpoint: string, options?: RequestInit): Promise<T> {
    return this.request<T>('DELETE', endpoint, undefined, options);
  }
  
  patch<T>(endpoint: string, data?: unknown, options?: RequestInit): Promise<T> {
    return this.request<T>('PATCH', endpoint, data, options);
  }
}

// Export singleton instance
export const migratedApiClient = new MigratedApiClient();
```

### **Step 3.2: Update Service Files Gradually**
```typescript
// frontend/src/services/employeeAPI.ts - Example migration
import { migratedApiClient } from './apiMigration';
import type { Employee, CreateEmployeeData, UpdateEmployeeData } from '../types';

export const employeeAPI = {
  // ✅ Migrated to use migration utility
  getAll: async (): Promise<Employee[]> => {
    const response = await migratedApiClient.get<{results: Employee[]}>('employees');
    return response.results;
  },
  
  getById: async (id: string): Promise<Employee> => {
    return migratedApiClient.get<Employee>(`employees/${id}`);
  },
  
  create: async (data: CreateEmployeeData): Promise<Employee> => {
    return migratedApiClient.post<Employee>('employees', data);
  },
  
  update: async (id: string, data: UpdateEmployeeData): Promise<Employee> => {
    return migratedApiClient.put<Employee>(`employees/${id}`, data);
  },
  
  delete: async (id: string): Promise<void> => {
    return migratedApiClient.delete<void>(`employees/${id}`);
  },
  
  // ✅ New v1-specific endpoints
  getStats: async (): Promise<{total: number; active: number}> => {
    return migratedApiClient.get<{total: number; active: number}>('employees/stats');
  },
  
  bulkImport: async (file: File): Promise<{success: number; errors: string[]}> => {
    const formData = new FormData();
    formData.append('file', file);
    
    return migratedApiClient.post<{success: number; errors: string[]}>('employees/bulk-import', formData);
  },
};
```

---

## 🔄 **PHASE 4: LEGACY API DEPRECATION (Day 11-14)**

### **Step 4.1: Add Deprecation Warnings**
```python
# backend/ems/middleware.py - Add deprecation middleware
import logging
from django.http import JsonResponse
from django.utils.deprecation import MiddlewareMixin

logger = logging.getLogger(__name__)

class APIDeprecationMiddleware(MiddlewareMixin):
    """Middleware to handle API deprecation warnings"""
    
    DEPRECATED_PATTERNS = [
        '/api/employees/',
        '/api/departments/',
        '/api/attendance/',
        # Add all legacy patterns
    ]
    
    def process_request(self, request):
        """Add deprecation warnings for legacy API usage"""
        
        # Check if this is a legacy API call
        for pattern in self.DEPRECATED_PATTERNS:
            if request.path.startswith(pattern) and not request.path.startswith('/api/v1/'):
                # Log deprecation warning
                logger.warning(
                    f"DEPRECATED API USAGE: {request.method} {request.path} "
                    f"from {request.META.get('HTTP_USER_AGENT', 'Unknown')} "
                    f"IP: {request.META.get('REMOTE_ADDR', 'Unknown')}"
                )
                
                # Add deprecation header
                response = self.get_response(request)
                if hasattr(response, '__setitem__'):
                    response['X-API-Deprecated'] = 'true'
                    response['X-API-Deprecation-Date'] = '2024-12-31'
                    response['X-API-Migration-Guide'] = 'https://docs.yourcompany.com/api-migration'
                
                return response
        
        return None

# Add to settings.py
MIDDLEWARE = [
    # ... other middleware
    'ems.middleware.APIDeprecationMiddleware',
]
```

### **Step 4.2: Create Migration Dashboard**
```typescript
// frontend/src/components/admin/APIMigrationDashboard.tsx
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';

interface MigrationStats {
  totalEndpoints: number;
  migratedEndpoints: number;
  legacyUsage: Array<{
    endpoint: string;
    usageCount: number;
    lastUsed: string;
  }>;
}

export function APIMigrationDashboard() {
  const [stats, setStats] = useState<MigrationStats | null>(null);
  
  useEffect(() => {
    // Fetch migration statistics
    fetch('/api/v1/admin/migration-stats')
      .then(res => res.json())
      .then(setStats);
  }, []);
  
  if (!stats) return <div>Loading...</div>;
  
  const migrationProgress = (stats.migratedEndpoints / stats.totalEndpoints) * 100;
  
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>API Migration Progress</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <div className="flex justify-between text-sm">
                <span>Migration Progress</span>
                <span>{Math.round(migrationProgress)}%</span>
              </div>
              <Progress value={migrationProgress} className="mt-2" />
            </div>
            
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-green-600">
                  {stats.migratedEndpoints}
                </div>
                <div className="text-sm text-gray-600">Migrated</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-orange-600">
                  {stats.totalEndpoints - stats.migratedEndpoints}
                </div>
                <div className="text-sm text-gray-600">Remaining</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-blue-600">
                  {stats.totalEndpoints}
                </div>
                <div className="text-sm text-gray-600">Total</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>Legacy API Usage</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {stats.legacyUsage.map((usage, index) => (
              <div key={index} className="flex justify-between items-center p-2 border rounded">
                <div>
                  <code className="text-sm">{usage.endpoint}</code>
                  <div className="text-xs text-gray-500">
                    Last used: {new Date(usage.lastUsed).toLocaleDateString()}
                  </div>
                </div>
                <Badge variant={usage.usageCount > 100 ? 'destructive' : 'secondary'}>
                  {usage.usageCount} calls
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
```

---

## 🔄 **PHASE 5: FINAL CLEANUP (Day 15-16)**

### **Step 5.1: Remove Legacy API**
```python
# backend/ems/urls.py - Final clean version
from django.urls import path, include
from django.contrib import admin

urlpatterns = [
    # ✅ CLEAN: Single API version
    path('api/v1/', include('ems.api_v1_urls', namespace='api-v1')),
    
    # ✅ CLEAN: External app APIs (keep separate)
    path('api/customer-service/', include('customer_service.urls')),
    path('api/notifications/', include('notifications.urls')),
    path('api/audit/', include('audit_logs.urls')),
    path('api/pdf/', include('pdf_generation.urls')),
    
    # ✅ CLEAN: Admin interface
    path('admin/', admin.site.urls),
    
    # ✅ CLEAN: Health checks
    path('health/', include('ems.health_urls')),
]

# ❌ REMOVED: Legacy API router
# ❌ REMOVED: Duplicate endpoints
# ❌ REMOVED: Inconsistent patterns
```

### **Step 5.2: Update Documentation**
```markdown
# API Documentation Update

## API v1 Endpoints

All API endpoints are now under `/api/v1/` prefix:

### Authentication
- `POST /api/v1/auth/login/` - User login
- `POST /api/v1/auth/logout/` - User logout
- `POST /api/v1/auth/refresh/` - Refresh token
- `GET /api/v1/auth/user/` - Get current user

### Core Management
- `GET /api/v1/employees/` - List employees
- `POST /api/v1/employees/` - Create employee
- `GET /api/v1/employees/{id}/` - Get employee
- `PUT /api/v1/employees/{id}/` - Update employee
- `DELETE /api/v1/employees/{id}/` - Delete employee
- `GET /api/v1/employees/stats/` - Employee statistics

### HR Management
- `GET /api/v1/leave-requests/` - List leave requests
- `POST /api/v1/leave-requests/` - Create leave request
- `GET /api/v1/attendance/` - List attendance records
- `POST /api/v1/attendance/` - Create attendance record

### Finance Management
- `GET /api/v1/budgets/` - List budgets
- `GET /api/v1/expenses/` - List expenses
- `GET /api/v1/invoices/` - List invoices

## Migration Guide

Legacy endpoints have been deprecated. Use the migration mapping:

| Legacy Endpoint | New V1 Endpoint |
|----------------|-----------------|
| `/api/employees/` | `/api/v1/employees/` |
| `/api/departments/` | `/api/v1/departments/` |
| `/api/attendance/` | `/api/v1/attendance/` |

## Breaking Changes

1. All endpoints now require `/api/v1/` prefix
2. Response format is now consistent across all endpoints
3. Error responses follow RFC 7807 standard
4. Authentication uses JWT tokens only
```

---

## ⚠️ **RISK MITIGATION**

### **Rollback Strategy**
```bash
# If issues arise during migration:
git checkout HEAD~1 -- backend/ems/urls.py
git checkout HEAD~1 -- frontend/src/services/
python manage.py runserver  # Test legacy API still works
```

### **Gradual Deployment**
1. **Phase 1**: Deploy v1 API alongside legacy (both working)
2. **Phase 2**: Migrate frontend services one by one
3. **Phase 3**: Add deprecation warnings to legacy API
4. **Phase 4**: Remove legacy API after 30-day notice

### **Monitoring**
- API usage analytics by endpoint
- Error rate monitoring for v1 vs legacy
- Performance comparison between API versions
- Frontend error tracking during migration

---

## 📊 **SUCCESS METRICS**

- ✅ **API Consistency**: Single API version with consistent patterns
- ✅ **Maintenance Reduction**: 50% reduction in API maintenance overhead
- ✅ **Developer Experience**: Clear, documented API with single source of truth
- ✅ **Performance**: Improved response times with optimized v1 endpoints
- ✅ **Documentation**: Complete, up-to-date API documentation

**Estimated Timeline**: 16 days
**Risk Level**: Medium (with gradual migration)
**Business Impact**: High positive (simplified maintenance, better developer experience)
