# 🗄️ **DATABASE OPTIMIZATION STRATEGY**
## *Performance, Indexing, and Query Optimization*

---

## 🎯 **CURRENT STATE ANALYSIS**

### **Critical Issues Identified:**
- **Missing Indexes**: Foreign keys without proper indexing
- **N+1 Query Problems**: ViewSets loading related objects inefficiently
- **Large Table Scans**: Queries without proper WHERE clause optimization
- **Inefficient Joins**: Complex relationships causing performance issues
- **No Query Monitoring**: Lack of slow query identification

### **Performance Impact:**
- **Dashboard Load Time**: 3-5 seconds (should be <1 second)
- **Employee List**: 2-3 seconds for 1000+ records
- **Report Generation**: 10-15 seconds for complex reports
- **API Response Time**: 500ms-2s average (should be <200ms)

---

## 🔄 **PHASE 1: INDEX ANALYSIS & OPTIMIZATION (Day 1-3)**

### **Step 1.1: Identify Missing Indexes**
```python
# scripts/analyze_database_performance.py
import os
import django
from django.db import connection
from django.apps import apps

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

def analyze_missing_indexes():
    """Analyze database for missing indexes"""
    
    print("🔍 Database Index Analysis")
    print("=" * 50)
    
    # Get all models
    models = apps.get_models()
    missing_indexes = []
    
    for model in models:
        if hasattr(model, '_meta'):
            # Check foreign key fields
            for field in model._meta.get_fields():
                if hasattr(field, 'related_model') and field.related_model:
                    # Check if foreign key has index
                    if not field.db_index and not any(
                        field.name in idx.fields for idx in model._meta.indexes
                    ):
                        missing_indexes.append({
                            'model': model.__name__,
                            'field': field.name,
                            'type': 'foreign_key',
                            'related_model': field.related_model.__name__
                        })
            
            # Check frequently queried fields
            frequent_query_fields = ['created_at', 'updated_at', 'status', 'is_active', 'date']
            for field_name in frequent_query_fields:
                if hasattr(model, field_name):
                    field = model._meta.get_field(field_name)
                    if not field.db_index and not any(
                        field_name in idx.fields for idx in model._meta.indexes
                    ):
                        missing_indexes.append({
                            'model': model.__name__,
                            'field': field_name,
                            'type': 'frequent_query',
                            'reason': 'Often used in WHERE clauses'
                        })
    
    print(f"📊 Found {len(missing_indexes)} missing indexes:")
    for idx in missing_indexes:
        print(f"  - {idx['model']}.{idx['field']} ({idx['type']})")
    
    return missing_indexes

def analyze_slow_queries():
    """Analyze slow queries from database logs"""
    
    with connection.cursor() as cursor:
        # Enable query logging
        cursor.execute("SET log_statement = 'all'")
        cursor.execute("SET log_min_duration_statement = 100")  # Log queries > 100ms
        
        # Get current slow queries (PostgreSQL specific)
        cursor.execute("""
            SELECT query, mean_time, calls, total_time
            FROM pg_stat_statements 
            WHERE mean_time > 100 
            ORDER BY mean_time DESC 
            LIMIT 20
        """)
        
        slow_queries = cursor.fetchall()
        
        print(f"\n🐌 Top 20 Slow Queries:")
        for query, mean_time, calls, total_time in slow_queries:
            print(f"  - {mean_time:.2f}ms avg ({calls} calls): {query[:100]}...")
    
    return slow_queries

if __name__ == '__main__':
    missing_indexes = analyze_missing_indexes()
    slow_queries = analyze_slow_queries()
```

### **Step 1.2: Create Index Migration**
```python
# backend/ems/migrations/XXXX_add_performance_indexes.py
from django.db import migrations, models

class Migration(migrations.Migration):
    dependencies = [
        ('ems', 'XXXX_previous_migration'),
    ]

    operations = [
        # Employee indexes
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_employee_department_active ON ems_employee(department_id, is_active);",
            reverse_sql="DROP INDEX IF EXISTS idx_employee_department_active;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_employee_hire_date ON ems_employee(hire_date);",
            reverse_sql="DROP INDEX IF EXISTS idx_employee_hire_date;"
        ),
        
        # Attendance indexes
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_attendance_employee_date ON ems_attendance(employee_id, date);",
            reverse_sql="DROP INDEX IF EXISTS idx_attendance_employee_date;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_attendance_date_present ON ems_attendance(date, is_present);",
            reverse_sql="DROP INDEX IF EXISTS idx_attendance_date_present;"
        ),
        
        # Leave Request indexes
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_leave_request_employee_status ON ems_leaverequest(employee_id, status);",
            reverse_sql="DROP INDEX IF EXISTS idx_leave_request_employee_status;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_leave_request_dates ON ems_leaverequest(start_date, end_date);",
            reverse_sql="DROP INDEX IF EXISTS idx_leave_request_dates;"
        ),
        
        # Activity indexes
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_activity_user_created ON ems_activity(user_id, created_at);",
            reverse_sql="DROP INDEX IF EXISTS idx_activity_user_created;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_activity_type_created ON ems_activity(activity_type, created_at);",
            reverse_sql="DROP INDEX IF EXISTS idx_activity_type_created;"
        ),
        
        # Expense indexes
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_expense_employee_date ON ems_expense(employee_id, expense_date);",
            reverse_sql="DROP INDEX IF EXISTS idx_expense_employee_date;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_expense_budget_approved ON ems_expense(budget_id, is_approved);",
            reverse_sql="DROP INDEX IF EXISTS idx_expense_budget_approved;"
        ),
        
        # Asset indexes
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_asset_category_status ON ems_asset(category_id, status);",
            reverse_sql="DROP INDEX IF EXISTS idx_asset_category_status;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_asset_employee_assigned ON ems_asset(assigned_to_id, assigned_date);",
            reverse_sql="DROP INDEX IF EXISTS idx_asset_employee_assigned;"
        ),
        
        # Project indexes
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_project_status_dates ON ems_project(status, start_date, end_date);",
            reverse_sql="DROP INDEX IF EXISTS idx_project_status_dates;"
        ),
        
        # Task indexes
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_task_project_status ON ems_task(project_id, status);",
            reverse_sql="DROP INDEX IF EXISTS idx_task_project_status;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_task_assignee_due ON ems_task(assigned_to_id, due_date);",
            reverse_sql="DROP INDEX IF EXISTS idx_task_assignee_due;"
        ),
        
        # KPI indexes
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_kpi_value_kpi_date ON ems_kpivalue(kpi_id, date);",
            reverse_sql="DROP INDEX IF EXISTS idx_kpi_value_kpi_date;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_kpi_category_active ON ems_kpi(category_id, is_active);",
            reverse_sql="DROP INDEX IF EXISTS idx_kpi_category_active;"
        ),
        
        # Invoice indexes
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_invoice_customer_status ON ems_customerinvoice(customer_id, status);",
            reverse_sql="DROP INDEX IF EXISTS idx_invoice_customer_status;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_invoice_due_date ON ems_customerinvoice(due_date);",
            reverse_sql="DROP INDEX IF EXISTS idx_invoice_due_date;"
        ),
        
        # Composite indexes for common query patterns
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_employee_dept_status_hire ON ems_employee(department_id, is_active, hire_date);",
            reverse_sql="DROP INDEX IF EXISTS idx_employee_dept_status_hire;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_attendance_month_year ON ems_attendance(EXTRACT(year FROM date), EXTRACT(month FROM date), employee_id);",
            reverse_sql="DROP INDEX IF EXISTS idx_attendance_month_year;"
        ),
    ]
```

### **Step 1.3: Optimize Model Meta Classes**
```python
# backend/ems/models/optimized.py - Add to existing models
class Employee(BaseModel):
    # ... existing fields ...
    
    class Meta:
        ordering = ['employee_id']
        indexes = [
            models.Index(fields=['department', 'is_active']),
            models.Index(fields=['hire_date']),
            models.Index(fields=['manager', 'is_active']),
            models.Index(fields=['user', 'is_active']),
        ]
        # Add database constraints
        constraints = [
            models.CheckConstraint(
                check=models.Q(salary__gte=0),
                name='positive_salary'
            ),
            models.UniqueConstraint(
                fields=['employee_id'],
                name='unique_employee_id'
            ),
        ]

class Attendance(BaseModel):
    # ... existing fields ...
    
    class Meta:
        unique_together = ['employee', 'date']
        ordering = ['-date']
        indexes = [
            models.Index(fields=['employee', 'date']),
            models.Index(fields=['date', 'is_present']),
            models.Index(fields=['employee', '-date']),  # For recent attendance
        ]

class LeaveRequest(BaseModel):
    # ... existing fields ...
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['employee', 'status']),
            models.Index(fields=['start_date', 'end_date']),
            models.Index(fields=['status', 'created_at']),
            models.Index(fields=['approved_by', 'status']),
        ]

class Expense(BaseModel):
    # ... existing fields ...
    
    class Meta:
        ordering = ['-expense_date']
        indexes = [
            models.Index(fields=['employee', 'expense_date']),
            models.Index(fields=['budget', 'is_approved']),
            models.Index(fields=['category', 'expense_date']),
            models.Index(fields=['is_approved', 'expense_date']),
        ]
```

---

## 🔄 **PHASE 2: QUERY OPTIMIZATION (Day 4-6)**

### **Step 2.1: Optimize ViewSets with select_related and prefetch_related**
```python
# backend/ems/views_optimized.py
from django.db.models import Prefetch, Q, Count, Sum, Avg
from rest_framework import viewsets

class OptimizedEmployeeViewSet(viewsets.ModelViewSet):
    serializer_class = EmployeeSerializer
    
    def get_queryset(self):
        """Optimized queryset with proper joins"""
        return Employee.objects.select_related(
            'user',
            'department', 
            'manager',
            'user__profile'
        ).prefetch_related(
            'subordinates',
            'leave_requests',
            Prefetch(
                'attendance_set',
                queryset=Attendance.objects.filter(
                    date__gte=timezone.now().date() - timedelta(days=30)
                ).order_by('-date')
            )
        ).annotate(
            total_leave_days=Sum('leave_requests__days_requested'),
            attendance_rate=Avg('attendance_set__hours_worked')
        )
    
    @action(detail=False, methods=['get'])
    def dashboard_stats(self, request):
        """Optimized dashboard statistics"""
        # Use database aggregation instead of Python loops
        stats = Employee.objects.aggregate(
            total_employees=Count('id'),
            active_employees=Count('id', filter=Q(is_active=True)),
            departments_count=Count('department', distinct=True),
            avg_salary=Avg('salary'),
            total_salary_cost=Sum('salary')
        )
        
        # Get department breakdown efficiently
        dept_stats = Employee.objects.values('department__name').annotate(
            count=Count('id'),
            avg_salary=Avg('salary')
        ).order_by('-count')
        
        return Response({
            'overview': stats,
            'by_department': dept_stats
        })

class OptimizedAttendanceViewSet(viewsets.ModelViewSet):
    serializer_class = AttendanceSerializer
    
    def get_queryset(self):
        """Optimized attendance queryset"""
        queryset = Attendance.objects.select_related(
            'employee__user',
            'employee__department'
        )
        
        # Add date filtering for performance
        date_from = self.request.query_params.get('date_from')
        date_to = self.request.query_params.get('date_to')
        
        if date_from:
            queryset = queryset.filter(date__gte=date_from)
        if date_to:
            queryset = queryset.filter(date__lte=date_to)
        else:
            # Default to last 3 months if no date range specified
            three_months_ago = timezone.now().date() - timedelta(days=90)
            queryset = queryset.filter(date__gte=three_months_ago)
        
        return queryset.order_by('-date')
    
    @action(detail=False, methods=['get'])
    def monthly_report(self, request):
        """Optimized monthly attendance report"""
        year = int(request.query_params.get('year', timezone.now().year))
        month = int(request.query_params.get('month', timezone.now().month))
        
        # Use database aggregation for monthly stats
        monthly_stats = Attendance.objects.filter(
            date__year=year,
            date__month=month
        ).values(
            'employee__id',
            'employee__user__first_name',
            'employee__user__last_name',
            'employee__employee_id'
        ).annotate(
            total_days=Count('id'),
            present_days=Count('id', filter=Q(is_present=True)),
            total_hours=Sum('hours_worked'),
            avg_hours=Avg('hours_worked')
        ).order_by('employee__employee_id')
        
        return Response(monthly_stats)

class OptimizedLeaveRequestViewSet(viewsets.ModelViewSet):
    serializer_class = LeaveRequestSerializer
    
    def get_queryset(self):
        """Optimized leave request queryset"""
        return LeaveRequest.objects.select_related(
            'employee__user',
            'employee__department',
            'leave_type',
            'approved_by__user'
        ).order_by('-created_at')
    
    @action(detail=False, methods=['get'])
    def approval_queue(self, request):
        """Optimized approval queue"""
        pending_requests = self.get_queryset().filter(
            status='pending'
        ).order_by('start_date')
        
        serializer = self.get_serializer(pending_requests, many=True)
        return Response(serializer.data)

class OptimizedExpenseViewSet(viewsets.ModelViewSet):
    serializer_class = ExpenseSerializer
    
    def get_queryset(self):
        """Optimized expense queryset"""
        return Expense.objects.select_related(
            'employee__user',
            'budget__department',
            'budget'
        ).order_by('-expense_date')
    
    @action(detail=False, methods=['get'])
    def budget_analysis(self, request):
        """Optimized budget analysis"""
        budget_id = request.query_params.get('budget_id')
        
        if budget_id:
            expenses = Expense.objects.filter(budget_id=budget_id)
        else:
            expenses = Expense.objects.all()
        
        # Use database aggregation
        analysis = expenses.aggregate(
            total_expenses=Sum('amount'),
            approved_expenses=Sum('amount', filter=Q(is_approved=True)),
            pending_expenses=Sum('amount', filter=Q(is_approved=False)),
            expense_count=Count('id'),
            avg_expense=Avg('amount')
        )
        
        # Category breakdown
        by_category = expenses.values('category').annotate(
            total=Sum('amount'),
            count=Count('id'),
            avg=Avg('amount')
        ).order_by('-total')
        
        return Response({
            'summary': analysis,
            'by_category': by_category
        })
```

### **Step 2.2: Implement Database Connection Pooling**
```python
# backend/backend/settings.py - Database optimization
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': os.getenv('DB_NAME', 'ems_db'),
        'USER': os.getenv('DB_USER', 'ems_user'),
        'PASSWORD': os.getenv('DB_PASSWORD', ''),
        'HOST': os.getenv('DB_HOST', 'localhost'),
        'PORT': os.getenv('DB_PORT', '5432'),
        'OPTIONS': {
            # Connection pooling
            'MAX_CONNS': 20,
            'MIN_CONNS': 5,
            # Performance optimizations
            'CONN_MAX_AGE': 600,  # 10 minutes
            'AUTOCOMMIT': True,
            # Query optimization
            'OPTIONS': {
                'statement_timeout': '30000',  # 30 seconds
                'lock_timeout': '10000',       # 10 seconds
                'idle_in_transaction_session_timeout': '300000',  # 5 minutes
            }
        },
        'ATOMIC_REQUESTS': False,  # Use manual transactions for better control
        'CONN_HEALTH_CHECKS': True,
    }
}

# Database query logging for development
if DEBUG:
    LOGGING = {
        'version': 1,
        'disable_existing_loggers': False,
        'handlers': {
            'console': {
                'class': 'logging.StreamHandler',
            },
        },
        'loggers': {
            'django.db.backends': {
                'handlers': ['console'],
                'level': 'DEBUG',
                'propagate': False,
            },
        },
    }

# Query optimization settings
DATABASE_QUERY_TIMEOUT = 30  # seconds
DATABASE_MAX_CONNECTIONS = 20
DATABASE_CONNECTION_POOL_SIZE = 10
```

### **Step 2.3: Add Query Monitoring Middleware**
```python
# backend/ems/middleware/query_monitor.py
import time
import logging
from django.db import connection
from django.utils.deprecation import MiddlewareMixin

logger = logging.getLogger(__name__)

class QueryMonitoringMiddleware(MiddlewareMixin):
    """Monitor database queries for performance issues"""
    
    def process_request(self, request):
        """Reset query count at start of request"""
        self.start_time = time.time()
        self.start_queries = len(connection.queries)
    
    def process_response(self, request, response):
        """Log query statistics"""
        if hasattr(self, 'start_time'):
            end_time = time.time()
            end_queries = len(connection.queries)
            
            query_count = end_queries - self.start_queries
            request_time = end_time - self.start_time
            
            # Log slow requests
            if request_time > 1.0 or query_count > 10:
                logger.warning(
                    f"SLOW REQUEST: {request.method} {request.path} "
                    f"took {request_time:.2f}s with {query_count} queries"
                )
                
                # Log individual slow queries
                for query in connection.queries[self.start_queries:]:
                    query_time = float(query['time'])
                    if query_time > 0.1:  # Log queries > 100ms
                        logger.warning(
                            f"SLOW QUERY ({query_time:.3f}s): {query['sql'][:200]}..."
                        )
            
            # Add performance headers
            response['X-Query-Count'] = str(query_count)
            response['X-Query-Time'] = f"{request_time:.3f}"
        
        return response

# Add to settings.py
MIDDLEWARE = [
    # ... other middleware
    'ems.middleware.query_monitor.QueryMonitoringMiddleware',
]
```

---

## 🔄 **PHASE 3: CACHING STRATEGY (Day 7-8)**

### **Step 3.1: Implement Smart Caching**
```python
# backend/ems/cache_utils.py
from django.core.cache import cache
from django.core.cache.utils import make_template_fragment_key
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
import hashlib
import json

class SmartCache:
    """Intelligent caching with automatic invalidation"""
    
    @staticmethod
    def get_cache_key(prefix, *args, **kwargs):
        """Generate consistent cache keys"""
        key_data = f"{prefix}:{':'.join(map(str, args))}"
        if kwargs:
            key_data += f":{hashlib.md5(json.dumps(kwargs, sort_keys=True).encode()).hexdigest()}"
        return key_data
    
    @staticmethod
    def cache_queryset(queryset, cache_key, timeout=300):
        """Cache queryset results"""
        try:
            data = list(queryset.values())
            cache.set(cache_key, data, timeout)
            return data
        except Exception as e:
            logger.error(f"Cache set failed for {cache_key}: {e}")
            return list(queryset.values())
    
    @staticmethod
    def get_or_cache(cache_key, callable_func, timeout=300):
        """Get from cache or execute function and cache result"""
        data = cache.get(cache_key)
        if data is None:
            data = callable_func()
            cache.set(cache_key, data, timeout)
        return data

# Cache invalidation signals
@receiver([post_save, post_delete], sender=Employee)
def invalidate_employee_cache(sender, **kwargs):
    """Invalidate employee-related caches"""
    cache.delete_many([
        'employee_stats',
        'department_stats',
        'dashboard_employee_count',
    ])
    # Invalidate pattern-based caches
    cache.delete_pattern('employee_list:*')
    cache.delete_pattern('department_employees:*')

@receiver([post_save, post_delete], sender=Attendance)
def invalidate_attendance_cache(sender, **kwargs):
    """Invalidate attendance-related caches"""
    instance = kwargs.get('instance')
    if instance:
        cache.delete_many([
            f'attendance_monthly:{instance.employee_id}',
            f'attendance_stats:{instance.employee_id}',
            'attendance_dashboard_stats',
        ])

# Cached ViewSet methods
class CachedEmployeeViewSet(OptimizedEmployeeViewSet):
    """Employee ViewSet with intelligent caching"""
    
    @action(detail=False, methods=['get'])
    def dashboard_stats(self, request):
        """Cached dashboard statistics"""
        cache_key = SmartCache.get_cache_key(
            'employee_dashboard_stats',
            request.user.id,
            request.query_params.get('department', 'all')
        )
        
        def get_stats():
            return super().dashboard_stats(request).data
        
        data = SmartCache.get_or_cache(cache_key, get_stats, timeout=600)
        return Response(data)
    
    def list(self, request, *args, **kwargs):
        """Cached employee list"""
        # Create cache key based on query parameters
        cache_key = SmartCache.get_cache_key(
            'employee_list',
            request.query_params.get('page', 1),
            request.query_params.get('department', ''),
            request.query_params.get('is_active', ''),
            request.query_params.get('search', '')
        )
        
        cached_data = cache.get(cache_key)
        if cached_data:
            return Response(cached_data)
        
        # Get fresh data
        response = super().list(request, *args, **kwargs)
        
        # Cache the response
        cache.set(cache_key, response.data, timeout=300)
        
        return response
```

---

## 📊 **SUCCESS METRICS**

### **Performance Targets:**
- ✅ **Dashboard Load Time**: <1 second (from 3-5 seconds)
- ✅ **API Response Time**: <200ms average (from 500ms-2s)
- ✅ **Employee List**: <500ms for 1000+ records (from 2-3 seconds)
- ✅ **Report Generation**: <3 seconds (from 10-15 seconds)
- ✅ **Database Query Count**: <5 queries per request (from 20+)

### **Database Metrics:**
- ✅ **Index Coverage**: 95%+ of queries using indexes
- ✅ **Slow Query Reduction**: 90% reduction in queries >100ms
- ✅ **Connection Pool Efficiency**: 80%+ pool utilization
- ✅ **Cache Hit Rate**: 70%+ for frequently accessed data

**Estimated Timeline**: 8 days
**Risk Level**: Low (non-breaking optimizations)
**Business Impact**: High positive (significantly improved user experience)
