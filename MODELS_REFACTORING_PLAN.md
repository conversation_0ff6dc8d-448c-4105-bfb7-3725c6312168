# 🔄 **MODELS REFACTORING IMPLEMENTATION PLAN**
## *Safe Migration from Monolithic to Domain-Driven Architecture*

---

## 🎯 **PHASE 1: PREPARATION (Day 1-2)**

### **Step 1.1: Create New Directory Structure**
```bash
mkdir -p backend/ems/models/
touch backend/ems/models/__init__.py
touch backend/ems/models/core.py
touch backend/ems/models/hr.py
touch backend/ems/models/finance.py
touch backend/ems/models/projects.py
touch backend/ems/models/assets.py
touch backend/ems/models/crm.py
touch backend/ems/models/communication.py
touch backend/ems/models/kpi.py
touch backend/ems/models/integrations.py
touch backend/ems/models/security.py
touch backend/ems/models/enterprise.py
```

### **Step 1.2: Create Base Model Classes**
```python
# backend/ems/models/base.py
from django.db import models
from django.utils import timezone
import uuid

class BaseModel(models.Model):
    """Base model with common fields"""
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        abstract = True

class UUIDBaseModel(BaseModel):
    """Base model with UUID primary key"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    class Meta:
        abstract = True

class NamedModel(BaseModel):
    """Base model for named entities with Arabic support"""
    name = models.CharField(max_length=200)
    name_ar = models.CharField(max_length=200, blank=True)
    description = models.TextField(blank=True)
    description_ar = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        abstract = True
        
    def __str__(self):
        return self.name
```

### **Step 1.3: Create Migration Backup**
```bash
# Backup current database
python manage.py dumpdata > backup_before_refactoring.json

# Create git branch for refactoring
git checkout -b models-refactoring
git add .
git commit -m "Backup before models refactoring"
```

---

## 🔄 **PHASE 2: DOMAIN EXTRACTION (Day 3-7)**

### **Step 2.1: Start with Core Domain (Safest)**
```python
# backend/ems/models/core.py
from django.db import models
from django.contrib.auth.models import User
from .base import BaseModel, NamedModel

class Role(NamedModel):
    """User roles in the system"""
    permissions = models.JSONField(default=list)
    is_system_role = models.BooleanField(default=False)
    
    class Meta:
        ordering = ['name']

class Department(NamedModel):
    """Company departments"""
    manager = models.ForeignKey('Employee', on_delete=models.SET_NULL, 
                               null=True, blank=True, related_name='managed_departments')
    parent_department = models.ForeignKey('self', on_delete=models.SET_NULL, 
                                        null=True, blank=True)
    budget = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    
    class Meta:
        ordering = ['name']

class UserProfile(BaseModel):
    """Extended user profile"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')
    role = models.ForeignKey(Role, on_delete=models.SET_NULL, null=True)
    preferred_language = models.CharField(max_length=5, default='en')
    avatar = models.ImageField(upload_to='avatars/', blank=True, null=True)
    phone = models.CharField(max_length=20, blank=True)
    
    def __str__(self):
        return f"{self.user.get_full_name()} - {self.role.name if self.role else 'No Role'}"

class Employee(BaseModel):
    """Employee information"""
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    employee_id = models.CharField(max_length=20, unique=True)
    department = models.ForeignKey(Department, on_delete=models.SET_NULL, null=True)
    position = models.CharField(max_length=100)
    hire_date = models.DateField()
    salary = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    is_active = models.BooleanField(default=True)
    manager = models.ForeignKey('self', on_delete=models.SET_NULL, 
                               null=True, blank=True, related_name='subordinates')
    
    class Meta:
        ordering = ['employee_id']
    
    def __str__(self):
        return f"{self.employee_id} - {self.user.get_full_name()}"

class Activity(BaseModel):
    """System activity log"""
    ACTIVITY_TYPES = [
        ('LOGIN', 'User Login'),
        ('LOGOUT', 'User Logout'),
        ('CREATE', 'Record Created'),
        ('UPDATE', 'Record Updated'),
        ('DELETE', 'Record Deleted'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    activity_type = models.CharField(max_length=20, choices=ACTIVITY_TYPES)
    description = models.TextField()
    description_ar = models.TextField(blank=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    
    class Meta:
        ordering = ['-created_at']
        verbose_name_plural = 'Activities'
```

### **Step 2.2: Update __init__.py Gradually**
```python
# backend/ems/models/__init__.py
# Import core models first
from .core import Role, Department, UserProfile, Employee, Activity

# Keep original imports for backward compatibility during migration
# TODO: Remove after all imports are updated
from ..models_legacy import *  # Temporary during migration
```

### **Step 2.3: Test Core Domain Migration**
```bash
# Test that nothing breaks
python manage.py makemigrations
python manage.py migrate --dry-run
python manage.py test ems.tests.test_core_models
```

---

## 🔄 **PHASE 3: INCREMENTAL MIGRATION (Day 8-14)**

### **Step 3.1: Migrate One Domain Per Day**

#### **Day 8: HR Domain**
```python
# backend/ems/models/hr.py
from django.db import models
from .base import BaseModel, NamedModel
from .core import Employee, Department

class LeaveType(NamedModel):
    """Types of leave available"""
    days_allowed = models.IntegerField(default=0)
    is_paid = models.BooleanField(default=True)
    requires_approval = models.BooleanField(default=True)
    
class LeaveRequest(BaseModel):
    """Employee leave requests"""
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
    ]
    
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE)
    leave_type = models.ForeignKey(LeaveType, on_delete=models.CASCADE)
    start_date = models.DateField()
    end_date = models.DateField()
    days_requested = models.IntegerField()
    reason = models.TextField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    approved_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, 
                                  null=True, blank=True, related_name='approved_leaves')
    
    class Meta:
        ordering = ['-created_at']

class Attendance(BaseModel):
    """Daily attendance records"""
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE)
    date = models.DateField()
    check_in = models.TimeField(null=True, blank=True)
    check_out = models.TimeField(null=True, blank=True)
    hours_worked = models.DecimalField(max_digits=4, decimal_places=2, default=0)
    is_present = models.BooleanField(default=False)
    notes = models.TextField(blank=True)
    
    class Meta:
        unique_together = ['employee', 'date']
        ordering = ['-date']
```

#### **Day 9: Finance Domain**
```python
# backend/ems/models/finance.py
from django.db import models
from decimal import Decimal
from .base import BaseModel, NamedModel, UUIDBaseModel
from .core import Employee

class Currency(NamedModel):
    """Multi-currency support"""
    code = models.CharField(max_length=3, unique=True)
    symbol = models.CharField(max_length=10)
    decimal_places = models.PositiveIntegerField(default=2)
    is_base_currency = models.BooleanField(default=False)
    
    class Meta:
        verbose_name_plural = 'Currencies'
        ordering = ['code']

class Budget(BaseModel):
    """Budget management"""
    name = models.CharField(max_length=200)
    department = models.ForeignKey('core.Department', on_delete=models.CASCADE)
    total_amount = models.DecimalField(max_digits=12, decimal_places=2)
    spent_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    fiscal_year = models.IntegerField()
    is_active = models.BooleanField(default=True)
    
    class Meta:
        unique_together = ['department', 'fiscal_year']
        ordering = ['-fiscal_year', 'name']

class Expense(BaseModel):
    """Expense tracking"""
    CATEGORY_CHOICES = [
        ('travel', 'Travel'),
        ('office', 'Office Supplies'),
        ('equipment', 'Equipment'),
        ('training', 'Training'),
        ('other', 'Other'),
    ]
    
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE)
    budget = models.ForeignKey(Budget, on_delete=models.CASCADE)
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    description = models.TextField()
    expense_date = models.DateField()
    receipt = models.FileField(upload_to='receipts/', blank=True, null=True)
    is_approved = models.BooleanField(default=False)
    
    class Meta:
        ordering = ['-expense_date']
```

### **Step 3.2: Update Imports Incrementally**
```python
# backend/ems/models/__init__.py - Update after each domain
from .core import Role, Department, UserProfile, Employee, Activity
from .hr import LeaveType, LeaveRequest, Attendance
from .finance import Currency, Budget, Expense

# Gradually remove from legacy imports
```

---

## 🔄 **PHASE 4: TESTING & VALIDATION (Day 15-16)**

### **Step 4.1: Comprehensive Testing**
```bash
# Run all tests
python manage.py test

# Check for missing migrations
python manage.py makemigrations --dry-run

# Validate model relationships
python manage.py check

# Test admin interface
python manage.py runserver
# Visit /admin/ and test all model CRUD operations
```

### **Step 4.2: Performance Validation**
```python
# Test query performance
from django.test import TestCase
from django.test.utils import override_settings
from django.db import connection

class ModelPerformanceTest(TestCase):
    def test_employee_queries(self):
        with self.assertNumQueries(1):
            list(Employee.objects.select_related('user', 'department')[:10])
    
    def test_leave_request_queries(self):
        with self.assertNumQueries(1):
            list(LeaveRequest.objects.select_related('employee__user', 'leave_type')[:10])
```

---

## 🔄 **PHASE 5: CLEANUP & FINALIZATION (Day 17-18)**

### **Step 5.1: Remove Legacy File**
```bash
# Rename original file as backup
mv backend/ems/models.py backend/ems/models_legacy.py

# Update all imports across the codebase
find . -name "*.py" -exec sed -i 's/from \.models import/from \.models import/g' {} \;
```

### **Step 5.2: Update Related Files**
```python
# Update serializers.py imports
from .models.core import Role, Department, Employee
from .models.hr import LeaveType, LeaveRequest, Attendance
from .models.finance import Currency, Budget, Expense
# ... etc

# Update views.py imports
from .models.core import Role, Department, Employee
from .models.hr import LeaveType, LeaveRequest, Attendance
# ... etc

# Update admin.py
from .models.core import Role, Department, Employee
from .models.hr import LeaveType, LeaveRequest, Attendance
# ... etc
```

### **Step 5.3: Final Validation**
```bash
# Full system test
python manage.py test
python manage.py runserver

# Performance test
python manage.py shell
>>> from django.db import connection
>>> connection.queries_log.clear()
>>> # Run typical operations
>>> len(connection.queries)  # Should be minimal
```

---

## ⚠️ **RISK MITIGATION**

### **Rollback Plan**
```bash
# If anything goes wrong:
git checkout main
python manage.py loaddata backup_before_refactoring.json
```

### **Gradual Deployment**
1. **Development**: Complete refactoring
2. **Staging**: Test with production data copy
3. **Production**: Deploy during maintenance window

### **Monitoring**
- Database query count monitoring
- Error rate monitoring
- Performance metrics tracking

---

## 📊 **SUCCESS METRICS**

- ✅ **File Size**: Reduce from 3,984 lines to <500 lines per domain
- ✅ **Maintainability**: Each domain independently maintainable
- ✅ **Performance**: No degradation in query performance
- ✅ **Tests**: 100% test pass rate
- ✅ **Team Velocity**: Faster development due to better organization

**Estimated Timeline**: 18 days
**Risk Level**: Medium (with proper testing)
**Business Impact**: High positive (long-term maintainability)
