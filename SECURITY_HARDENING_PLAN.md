# 🔒 **SECURITY HARDENING STRATEGY**
## *Comprehensive Security Assessment and Remediation*

---

## 🎯 **CURRENT SECURITY ANALYSIS**

### **Critical Vulnerabilities Identified:**
- **CSRF Exemptions**: Multiple endpoints bypass CSRF protection
- **Authentication Complexity**: Dual auth systems create security gaps
- **Input Validation**: Inconsistent validation across endpoints
- **Rate Limiting**: Basic implementation, needs enhancement
- **Security Headers**: Inconsistent implementation
- **Permission Classes**: Some endpoints lack proper authorization

### **Security Risk Assessment:**
- **Authentication Bypass**: Medium risk from complex auth flow
- **CSRF Attacks**: High risk from exempt endpoints
- **SQL Injection**: Low risk (Django ORM protection)
- **XSS Attacks**: Medium risk from user input handling
- **Data Exposure**: Medium risk from insufficient access controls

---

## 🔄 **PHASE 1: AUTHENTICATION & AUTHORIZATION HARDENING (Day 1-3)**

### **Step 1.1: Consolidate Authentication System**
```python
# backend/ems/auth/secure_auth.py
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.exceptions import TokenError, InvalidToken
from django.contrib.auth import authenticate
from django.contrib.auth.models import User
from django.utils import timezone
from datetime import timedelta
import logging

logger = logging.getLogger(__name__)

class SecureTokenObtainPairView(TokenObtainPairView):
    """Enhanced token view with security logging and rate limiting"""
    
    def post(self, request, *args, **kwargs):
        # Log authentication attempt
        ip_address = self.get_client_ip(request)
        username = request.data.get('username', 'unknown')
        
        logger.info(f"Authentication attempt for {username} from {ip_address}")
        
        # Check for brute force attempts
        if self.is_brute_force_attempt(username, ip_address):
            logger.warning(f"Brute force attempt detected for {username} from {ip_address}")
            return Response(
                {'error': 'Too many failed attempts. Please try again later.'},
                status=status.HTTP_429_TOO_MANY_REQUESTS
            )
        
        response = super().post(request, *args, **kwargs)
        
        if response.status_code == 200:
            # Successful login
            user = User.objects.get(username=username)
            self.log_successful_login(user, ip_address)
            self.clear_failed_attempts(username, ip_address)
            
            # Set secure cookies
            self.set_secure_cookies(response, response.data)
        else:
            # Failed login
            self.log_failed_attempt(username, ip_address)
        
        return response
    
    def get_client_ip(self, request):
        """Get client IP address safely"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
    
    def is_brute_force_attempt(self, username, ip_address):
        """Check for brute force attempts"""
        from django.core.cache import cache
        
        # Check failed attempts in last 15 minutes
        cache_key = f"failed_login:{username}:{ip_address}"
        failed_attempts = cache.get(cache_key, 0)
        
        return failed_attempts >= 5
    
    def log_failed_attempt(self, username, ip_address):
        """Log and track failed login attempts"""
        from django.core.cache import cache
        
        cache_key = f"failed_login:{username}:{ip_address}"
        failed_attempts = cache.get(cache_key, 0) + 1
        cache.set(cache_key, failed_attempts, timeout=900)  # 15 minutes
        
        # Log to security events
        from ems.models import Activity
        Activity.objects.create(
            user=None,
            activity_type='FAILED_LOGIN',
            description=f"Failed login attempt for {username}",
            ip_address=ip_address
        )
    
    def log_successful_login(self, user, ip_address):
        """Log successful login"""
        from ems.models import Activity
        Activity.objects.create(
            user=user,
            activity_type='LOGIN',
            description=f"Successful login",
            ip_address=ip_address
        )
        
        # Update user's last login
        user.last_login = timezone.now()
        user.save(update_fields=['last_login'])
    
    def clear_failed_attempts(self, username, ip_address):
        """Clear failed attempts after successful login"""
        from django.core.cache import cache
        cache_key = f"failed_login:{username}:{ip_address}"
        cache.delete(cache_key)
    
    def set_secure_cookies(self, response, data):
        """Set secure HTTP-only cookies for tokens"""
        if 'access' in data:
            response.set_cookie(
                'access_token',
                data['access'],
                max_age=settings.SIMPLE_JWT['ACCESS_TOKEN_LIFETIME'].total_seconds(),
                httponly=True,
                secure=not settings.DEBUG,
                samesite='Strict'
            )
        
        if 'refresh' in data:
            response.set_cookie(
                'refresh_token',
                data['refresh'],
                max_age=settings.SIMPLE_JWT['REFRESH_TOKEN_LIFETIME'].total_seconds(),
                httponly=True,
                secure=not settings.DEBUG,
                samesite='Strict'
            )

class SecureTokenRefreshView(TokenRefreshView):
    """Enhanced token refresh with security checks"""
    
    def post(self, request, *args, **kwargs):
        # Get refresh token from cookie if not in request data
        if 'refresh' not in request.data:
            refresh_token = request.COOKIES.get('refresh_token')
            if refresh_token:
                request.data['refresh'] = refresh_token
        
        response = super().post(request, *args, **kwargs)
        
        if response.status_code == 200:
            # Set new access token in cookie
            response.set_cookie(
                'access_token',
                response.data['access'],
                max_age=settings.SIMPLE_JWT['ACCESS_TOKEN_LIFETIME'].total_seconds(),
                httponly=True,
                secure=not settings.DEBUG,
                samesite='Strict'
            )
        
        return response

class SecureLogoutView(APIView):
    """Secure logout with token blacklisting"""
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        try:
            # Get refresh token
            refresh_token = request.data.get('refresh') or request.COOKIES.get('refresh_token')
            
            if refresh_token:
                # Blacklist the refresh token
                token = RefreshToken(refresh_token)
                token.blacklist()
            
            # Log logout activity
            from ems.models import Activity
            Activity.objects.create(
                user=request.user,
                activity_type='LOGOUT',
                description="User logged out",
                ip_address=self.get_client_ip(request)
            )
            
            # Clear cookies
            response = Response({'message': 'Successfully logged out'})
            response.delete_cookie('access_token')
            response.delete_cookie('refresh_token')
            
            return response
            
        except TokenError:
            return Response({'error': 'Invalid token'}, status=status.HTTP_400_BAD_REQUEST)
    
    def get_client_ip(self, request):
        """Get client IP address safely"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
```

### **Step 1.2: Enhanced Permission System**
```python
# backend/ems/permissions.py
from rest_framework import permissions
from rest_framework.exceptions import PermissionDenied
from django.core.cache import cache
import logging

logger = logging.getLogger(__name__)

class RoleBasedPermission(permissions.BasePermission):
    """Enhanced role-based permission with caching and logging"""
    
    def has_permission(self, request, view):
        """Check if user has permission for this view"""
        if not request.user or not request.user.is_authenticated:
            return False
        
        # Cache user permissions for 5 minutes
        cache_key = f"user_permissions:{request.user.id}"
        user_permissions = cache.get(cache_key)
        
        if user_permissions is None:
            user_permissions = self.get_user_permissions(request.user)
            cache.set(cache_key, user_permissions, timeout=300)
        
        # Check view permission
        required_permission = self.get_required_permission(view, request.method)
        has_permission = required_permission in user_permissions
        
        if not has_permission:
            logger.warning(
                f"Permission denied for user {request.user.username} "
                f"on {view.__class__.__name__}.{request.method.lower()}"
            )
        
        return has_permission
    
    def has_object_permission(self, request, view, obj):
        """Check if user has permission for specific object"""
        if not self.has_permission(request, view):
            return False
        
        # Check object-level permissions
        return self.check_object_permission(request.user, obj, request.method)
    
    def get_user_permissions(self, user):
        """Get all permissions for user"""
        permissions = set()
        
        try:
            user_profile = user.profile
            if user_profile.role:
                permissions.update(user_profile.role.permissions or [])
        except AttributeError:
            pass
        
        # Add superuser permissions
        if user.is_superuser:
            permissions.update([
                'view_all', 'add_all', 'change_all', 'delete_all'
            ])
        
        return list(permissions)
    
    def get_required_permission(self, view, method):
        """Get required permission for view and method"""
        view_name = view.__class__.__name__.lower().replace('viewset', '')
        
        permission_map = {
            'GET': f'view_{view_name}',
            'POST': f'add_{view_name}',
            'PUT': f'change_{view_name}',
            'PATCH': f'change_{view_name}',
            'DELETE': f'delete_{view_name}',
        }
        
        return permission_map.get(method, f'view_{view_name}')
    
    def check_object_permission(self, user, obj, method):
        """Check object-level permissions"""
        # Example: Users can only modify their own data
        if hasattr(obj, 'user') and obj.user == user:
            return True
        
        # Example: Managers can modify their department's data
        if hasattr(obj, 'department') and hasattr(user, 'profile'):
            if user.profile.role and 'manager' in user.profile.role.name.lower():
                return obj.department == user.profile.department
        
        # Example: HR can access employee data
        if hasattr(user, 'profile') and user.profile.role:
            if 'hr' in user.profile.role.name.lower():
                return True
        
        return False

class DepartmentPermission(RoleBasedPermission):
    """Department-specific permissions"""
    
    def has_object_permission(self, request, view, obj):
        """Check department access"""
        if not super().has_object_permission(request, view, obj):
            return False
        
        # Users can only access their own department's data
        user_department = getattr(request.user.profile, 'department', None)
        obj_department = getattr(obj, 'department', None)
        
        if user_department and obj_department:
            return user_department == obj_department
        
        return True

class OwnerOrReadOnlyPermission(permissions.BasePermission):
    """Object owner can edit, others can only read"""
    
    def has_object_permission(self, request, view, obj):
        # Read permissions for any request
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # Write permissions only to the owner
        return obj.user == request.user
```

### **Step 1.3: CSRF Protection Enhancement**
```python
# backend/ems/middleware/csrf_security.py
from django.middleware.csrf import CsrfViewMiddleware
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
import logging

logger = logging.getLogger(__name__)

class EnhancedCSRFMiddleware(CsrfViewMiddleware):
    """Enhanced CSRF protection with better error handling"""
    
    def process_view(self, request, callback, callback_args, callback_kwargs):
        """Enhanced CSRF checking with logging"""
        
        # Skip CSRF for specific safe endpoints
        safe_endpoints = [
            '/api/v1/health/',
            '/api/v1/auth/login/',  # Only login should be exempt
        ]
        
        if request.path in safe_endpoints:
            return None
        
        # Log CSRF failures
        response = super().process_view(request, callback, callback_args, callback_kwargs)
        
        if response and response.status_code == 403:
            logger.warning(
                f"CSRF failure for {request.method} {request.path} "
                f"from {request.META.get('REMOTE_ADDR', 'unknown')} "
                f"User-Agent: {request.META.get('HTTP_USER_AGENT', 'unknown')}"
            )
            
            # Return JSON response for API endpoints
            if request.path.startswith('/api/'):
                return JsonResponse({
                    'error': 'CSRF token missing or incorrect',
                    'code': 'CSRF_FAILURE'
                }, status=403)
        
        return response

# Remove CSRF exemptions from views
# backend/ems/views.py - Remove @csrf_exempt decorators
class SecureViewSet(viewsets.ModelViewSet):
    """Base ViewSet with proper CSRF protection"""
    
    # Remove @csrf_exempt - all views should have CSRF protection
    def create(self, request, *args, **kwargs):
        """Create with CSRF protection"""
        return super().create(request, *args, **kwargs)
    
    def update(self, request, *args, **kwargs):
        """Update with CSRF protection"""
        return super().update(request, *args, **kwargs)
    
    def destroy(self, request, *args, **kwargs):
        """Delete with CSRF protection"""
        return super().destroy(request, *args, **kwargs)

# Update URL patterns to remove CSRF exemptions
# backend/ems/api_v1_urls.py
urlpatterns = [
    # ✅ SECURE: Remove csrf_exempt decorators
    path('auth/login/', SecureTokenObtainPairView.as_view(), name='auth-login'),
    path('auth/refresh/', SecureTokenRefreshView.as_view(), name='auth-refresh'),
    path('auth/logout/', SecureLogoutView.as_view(), name='auth-logout'),
    # ... other patterns without csrf_exempt
]
```

---

## 🔄 **PHASE 2: INPUT VALIDATION & SANITIZATION (Day 4-5)**

### **Step 2.1: Enhanced Input Validation**
```python
# backend/ems/validators.py
import re
from django.core.exceptions import ValidationError
from django.core.validators import RegexValidator
from django.utils.html import strip_tags
import bleach

class SecureValidator:
    """Comprehensive input validation and sanitization"""
    
    @staticmethod
    def validate_name(value):
        """Validate name fields"""
        if not value or len(value.strip()) < 2:
            raise ValidationError("Name must be at least 2 characters long")
        
        # Allow only letters, spaces, hyphens, and apostrophes
        if not re.match(r"^[a-zA-Z\s\-']+$", value):
            raise ValidationError("Name contains invalid characters")
        
        return value.strip()
    
    @staticmethod
    def validate_email(value):
        """Enhanced email validation"""
        from django.core.validators import validate_email as django_validate_email
        
        try:
            django_validate_email(value)
        except ValidationError:
            raise ValidationError("Invalid email format")
        
        # Check for suspicious patterns
        suspicious_patterns = [
            r'[<>]',  # HTML tags
            r'javascript:',  # JavaScript injection
            r'data:',  # Data URLs
        ]
        
        for pattern in suspicious_patterns:
            if re.search(pattern, value, re.IGNORECASE):
                raise ValidationError("Email contains suspicious content")
        
        return value.lower().strip()
    
    @staticmethod
    def sanitize_html(value):
        """Sanitize HTML content"""
        if not value:
            return value
        
        # Allow only safe HTML tags
        allowed_tags = ['p', 'br', 'strong', 'em', 'ul', 'ol', 'li']
        allowed_attributes = {}
        
        cleaned = bleach.clean(
            value,
            tags=allowed_tags,
            attributes=allowed_attributes,
            strip=True
        )
        
        return cleaned
    
    @staticmethod
    def validate_phone(value):
        """Validate phone numbers"""
        if not value:
            return value
        
        # Remove all non-digit characters
        digits_only = re.sub(r'\D', '', value)
        
        # Check length (10-15 digits)
        if len(digits_only) < 10 or len(digits_only) > 15:
            raise ValidationError("Phone number must be 10-15 digits")
        
        return digits_only
    
    @staticmethod
    def validate_file_upload(file):
        """Validate file uploads"""
        if not file:
            return file
        
        # Check file size (max 10MB)
        max_size = 10 * 1024 * 1024
        if file.size > max_size:
            raise ValidationError("File size must be less than 10MB")
        
        # Check file extension
        allowed_extensions = ['.pdf', '.doc', '.docx', '.jpg', '.jpeg', '.png', '.gif']
        file_extension = file.name.lower().split('.')[-1]
        if f'.{file_extension}' not in allowed_extensions:
            raise ValidationError("File type not allowed")
        
        # Check for malicious content
        if file.name.lower().endswith('.exe') or 'script' in file.name.lower():
            raise ValidationError("Suspicious file detected")
        
        return file

# Enhanced serializers with validation
# backend/ems/serializers_secure.py
from rest_framework import serializers
from .validators import SecureValidator

class SecureEmployeeSerializer(serializers.ModelSerializer):
    """Employee serializer with enhanced validation"""
    
    class Meta:
        model = Employee
        fields = '__all__'
    
    def validate_first_name(self, value):
        return SecureValidator.validate_name(value)
    
    def validate_last_name(self, value):
        return SecureValidator.validate_name(value)
    
    def validate_email(self, value):
        return SecureValidator.validate_email(value)
    
    def validate_phone(self, value):
        return SecureValidator.validate_phone(value)
    
    def validate(self, data):
        """Cross-field validation"""
        # Check for duplicate employee ID
        employee_id = data.get('employee_id')
        if employee_id:
            existing = Employee.objects.filter(employee_id=employee_id)
            if self.instance:
                existing = existing.exclude(id=self.instance.id)
            if existing.exists():
                raise serializers.ValidationError({
                    'employee_id': 'Employee ID already exists'
                })
        
        return data

class SecureMessageSerializer(serializers.ModelSerializer):
    """Message serializer with HTML sanitization"""
    
    class Meta:
        model = Message
        fields = '__all__'
    
    def validate_content(self, value):
        return SecureValidator.sanitize_html(value)
    
    def validate_subject(self, value):
        # Strip HTML tags from subject
        return strip_tags(value).strip()
```

### **Step 2.2: Rate Limiting Enhancement**
```python
# backend/ems/middleware/rate_limiting.py
from django.core.cache import cache
from django.http import JsonResponse
from django.utils.deprecation import MiddlewareMixin
import time
import logging

logger = logging.getLogger(__name__)

class EnhancedRateLimitMiddleware(MiddlewareMixin):
    """Enhanced rate limiting with different limits for different endpoints"""
    
    # Rate limits: (requests, time_window_seconds)
    RATE_LIMITS = {
        '/api/v1/auth/login/': (5, 300),      # 5 attempts per 5 minutes
        '/api/v1/auth/refresh/': (10, 60),    # 10 attempts per minute
        '/api/v1/employees/': (100, 60),      # 100 requests per minute
        '/api/v1/attendance/': (200, 60),     # 200 requests per minute
        'default': (1000, 60),                # 1000 requests per minute default
    }
    
    def process_request(self, request):
        """Check rate limits"""
        if not self.should_rate_limit(request):
            return None
        
        client_ip = self.get_client_ip(request)
        user_id = request.user.id if request.user.is_authenticated else 'anonymous'
        
        # Create rate limit key
        rate_limit_key = f"rate_limit:{client_ip}:{user_id}:{request.path}"
        
        # Get rate limit for this endpoint
        max_requests, time_window = self.get_rate_limit(request.path)
        
        # Check current request count
        current_requests = cache.get(rate_limit_key, 0)
        
        if current_requests >= max_requests:
            logger.warning(
                f"Rate limit exceeded for {client_ip} on {request.path} "
                f"({current_requests}/{max_requests} in {time_window}s)"
            )
            
            return JsonResponse({
                'error': 'Rate limit exceeded',
                'retry_after': time_window
            }, status=429)
        
        # Increment request count
        cache.set(rate_limit_key, current_requests + 1, timeout=time_window)
        
        return None
    
    def should_rate_limit(self, request):
        """Determine if request should be rate limited"""
        # Skip rate limiting for static files
        if request.path.startswith('/static/') or request.path.startswith('/media/'):
            return False
        
        # Skip for health checks
        if request.path.startswith('/health/'):
            return False
        
        return True
    
    def get_client_ip(self, request):
        """Get client IP address"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
    
    def get_rate_limit(self, path):
        """Get rate limit for specific path"""
        for pattern, limit in self.RATE_LIMITS.items():
            if pattern == 'default':
                continue
            if path.startswith(pattern):
                return limit
        
        return self.RATE_LIMITS['default']
```

---

## 🔄 **PHASE 3: SECURITY HEADERS & MONITORING (Day 6-7)**

### **Step 3.1: Security Headers Implementation**
```python
# backend/ems/middleware/security_headers.py
from django.utils.deprecation import MiddlewareMixin

class SecurityHeadersMiddleware(MiddlewareMixin):
    """Add comprehensive security headers"""
    
    def process_response(self, request, response):
        """Add security headers to all responses"""
        
        # Content Security Policy
        csp_policy = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
            "style-src 'self' 'unsafe-inline'; "
            "img-src 'self' data: https:; "
            "font-src 'self' data:; "
            "connect-src 'self'; "
            "frame-ancestors 'none'; "
            "base-uri 'self'; "
            "form-action 'self';"
        )
        response['Content-Security-Policy'] = csp_policy
        
        # Prevent clickjacking
        response['X-Frame-Options'] = 'DENY'
        
        # Prevent MIME type sniffing
        response['X-Content-Type-Options'] = 'nosniff'
        
        # XSS Protection
        response['X-XSS-Protection'] = '1; mode=block'
        
        # Referrer Policy
        response['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        
        # Permissions Policy
        response['Permissions-Policy'] = (
            'geolocation=(), '
            'microphone=(), '
            'camera=(), '
            'payment=(), '
            'usb=(), '
            'magnetometer=(), '
            'gyroscope=(), '
            'accelerometer=()'
        )
        
        # HSTS (only in production)
        if not request.is_secure() and hasattr(request, 'is_secure'):
            response['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
        
        # Remove server information
        if 'Server' in response:
            del response['Server']
        
        return response

# Update settings.py
MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'ems.middleware.security_headers.SecurityHeadersMiddleware',
    'ems.middleware.rate_limiting.EnhancedRateLimitMiddleware',
    'ems.middleware.csrf_security.EnhancedCSRFMiddleware',
    # ... other middleware
]

# Security settings
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_HSTS_SECONDS = 31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True
SECURE_REFERRER_POLICY = 'strict-origin-when-cross-origin'

# Session security
SESSION_COOKIE_SECURE = not DEBUG
SESSION_COOKIE_HTTPONLY = True
SESSION_COOKIE_SAMESITE = 'Strict'
SESSION_COOKIE_AGE = 3600  # 1 hour

# CSRF security
CSRF_COOKIE_SECURE = not DEBUG
CSRF_COOKIE_HTTPONLY = True
CSRF_COOKIE_SAMESITE = 'Strict'
```

### **Step 3.2: Security Monitoring System**
```python
# backend/ems/security/monitoring.py
import logging
from django.core.mail import send_mail
from django.conf import settings
from django.utils import timezone
from datetime import timedelta
from .models import SecurityEvent

logger = logging.getLogger('security')

class SecurityMonitor:
    """Comprehensive security monitoring and alerting"""
    
    @staticmethod
    def log_security_event(event_type, description, user=None, ip_address=None, severity='medium'):
        """Log security events"""
        SecurityEvent.objects.create(
            event_type=event_type,
            description=description,
            user=user,
            ip_address=ip_address,
            severity=severity,
            timestamp=timezone.now()
        )
        
        # Send alert for high severity events
        if severity == 'high':
            SecurityMonitor.send_security_alert(event_type, description, ip_address)
    
    @staticmethod
    def send_security_alert(event_type, description, ip_address):
        """Send security alerts to administrators"""
        subject = f"Security Alert: {event_type}"
        message = f"""
        Security Event Detected:
        
        Type: {event_type}
        Description: {description}
        IP Address: {ip_address}
        Time: {timezone.now()}
        
        Please investigate immediately.
        """
        
        admin_emails = [admin[1] for admin in settings.ADMINS]
        if admin_emails:
            send_mail(
                subject,
                message,
                settings.DEFAULT_FROM_EMAIL,
                admin_emails,
                fail_silently=False
            )
    
    @staticmethod
    def check_suspicious_activity():
        """Check for suspicious activity patterns"""
        now = timezone.now()
        last_hour = now - timedelta(hours=1)
        
        # Check for multiple failed logins
        failed_logins = SecurityEvent.objects.filter(
            event_type='FAILED_LOGIN',
            timestamp__gte=last_hour
        ).count()
        
        if failed_logins > 50:
            SecurityMonitor.log_security_event(
                'BRUTE_FORCE_ATTACK',
                f'{failed_logins} failed login attempts in the last hour',
                severity='high'
            )
        
        # Check for unusual access patterns
        unusual_access = SecurityEvent.objects.filter(
            event_type='UNUSUAL_ACCESS',
            timestamp__gte=last_hour
        ).count()
        
        if unusual_access > 20:
            SecurityMonitor.log_security_event(
                'SUSPICIOUS_ACCESS_PATTERN',
                f'{unusual_access} unusual access attempts in the last hour',
                severity='high'
            )

# Security event model
# backend/ems/models/security.py
class SecurityEvent(models.Model):
    """Security event logging"""
    
    EVENT_TYPES = [
        ('FAILED_LOGIN', 'Failed Login'),
        ('BRUTE_FORCE_ATTACK', 'Brute Force Attack'),
        ('SUSPICIOUS_ACCESS_PATTERN', 'Suspicious Access Pattern'),
        ('UNAUTHORIZED_ACCESS', 'Unauthorized Access'),
        ('DATA_BREACH_ATTEMPT', 'Data Breach Attempt'),
        ('MALICIOUS_REQUEST', 'Malicious Request'),
    ]
    
    SEVERITY_LEVELS = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical'),
    ]
    
    event_type = models.CharField(max_length=50, choices=EVENT_TYPES)
    description = models.TextField()
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    severity = models.CharField(max_length=20, choices=SEVERITY_LEVELS, default='medium')
    timestamp = models.DateTimeField(auto_now_add=True)
    resolved = models.BooleanField(default=False)
    
    class Meta:
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['event_type', 'timestamp']),
            models.Index(fields=['severity', 'resolved']),
            models.Index(fields=['ip_address', 'timestamp']),
        ]
```

---

## 📊 **SUCCESS METRICS**

### **Security Targets:**
- ✅ **CSRF Protection**: 100% of endpoints protected (from 70%)
- ✅ **Authentication Security**: Single secure auth system
- ✅ **Input Validation**: 100% of inputs validated and sanitized
- ✅ **Rate Limiting**: Comprehensive protection against abuse
- ✅ **Security Headers**: All recommended headers implemented
- ✅ **Security Monitoring**: Real-time threat detection and alerting

### **Security Metrics:**
- ✅ **Failed Login Attempts**: <5 per IP per 15 minutes
- ✅ **Security Events**: 100% logged and monitored
- ✅ **Response Time**: Security alerts within 5 minutes
- ✅ **Vulnerability Score**: Reduce from 6.5/10 to 9/10

**Estimated Timeline**: 7 days
**Risk Level**: Medium (security changes require careful testing)
**Business Impact**: High positive (significantly improved security posture)
