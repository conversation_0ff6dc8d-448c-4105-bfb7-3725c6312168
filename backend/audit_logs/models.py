from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import GenericForeignKey
import uuid
import json

class AuditAction(models.TextChoices):
    CREATE = 'create', 'Create'
    READ = 'read', 'Read'
    UPDATE = 'update', 'Update'
    DELETE = 'delete', 'Delete'
    LOGIN = 'login', 'Login'
    LOGOUT = 'logout', 'Logout'
    LOGIN_FAILED = 'login_failed', 'Login Failed'
    PASSWORD_CHANGE = 'password_change', 'Password Change'
    PERMISSION_DENIED = 'permission_denied', 'Permission Denied'
    EXPORT = 'export', 'Export'
    IMPORT = 'import', 'Import'
    BULK_UPDATE = 'bulk_update', 'Bulk Update'
    BULK_DELETE = 'bulk_delete', 'Bulk Delete'
    SYSTEM_CONFIG = 'system_config', 'System Configuration'
    SECURITY_EVENT = 'security_event', 'Security Event'

class AuditSeverity(models.TextChoices):
    LOW = 'low', 'Low'
    MEDIUM = 'medium', 'Medium'
    HIGH = 'high', 'High'
    CRITICAL = 'critical', 'Critical'

class AuditLog(models.Model):
    """Comprehensive audit logging for all system activities"""
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    # User and session information
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='audit_logs')
    username = models.CharField(max_length=150, help_text="Username at time of action (preserved even if user deleted)")
    session_key = models.CharField(max_length=40, blank=True, help_text="Django session key")
    
    # Action details
    action = models.CharField(max_length=20, choices=AuditAction.choices)
    severity = models.CharField(max_length=10, choices=AuditSeverity.choices, default=AuditSeverity.LOW)
    
    # Object being acted upon (generic foreign key)
    content_type = models.ForeignKey(ContentType, on_delete=models.SET_NULL, null=True, blank=True)
    object_id = models.CharField(max_length=255, null=True, blank=True)
    content_object = GenericForeignKey('content_type', 'object_id')
    object_repr = models.CharField(max_length=200, blank=True, help_text="String representation of the object")
    
    # Request information
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
    request_method = models.CharField(max_length=10, blank=True)
    request_path = models.CharField(max_length=500, blank=True)
    request_params = models.JSONField(default=dict, blank=True, help_text="GET/POST parameters")
    
    # Change tracking
    changes = models.JSONField(default=dict, blank=True, help_text="Before/after values for updates")
    additional_data = models.JSONField(default=dict, blank=True, help_text="Additional context data")
    
    # Outcome
    success = models.BooleanField(default=True)
    error_message = models.TextField(blank=True)
    
    # Timestamps
    timestamp = models.DateTimeField(default=timezone.now, db_index=True)
    
    # Compliance and retention
    retention_date = models.DateTimeField(null=True, blank=True, help_text="When this log can be deleted")
    is_sensitive = models.BooleanField(default=False, help_text="Contains sensitive data")
    compliance_tags = models.JSONField(default=list, blank=True, help_text="Tags for compliance requirements")
    
    class Meta:
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['user', '-timestamp']),
            models.Index(fields=['action', '-timestamp']),
            models.Index(fields=['severity', '-timestamp']),
            models.Index(fields=['content_type', 'object_id']),
            models.Index(fields=['ip_address', '-timestamp']),
            models.Index(fields=['success', '-timestamp']),
        ]
        
    def __str__(self):
        return f"{self.username} - {self.action} - {self.timestamp}"
    
    @property
    def formatted_changes(self):
        """Return formatted changes for display"""
        if not self.changes:
            return "No changes recorded"
        
        formatted = []
        for field, change in self.changes.items():
            if isinstance(change, dict) and 'old' in change and 'new' in change:
                formatted.append(f"{field}: '{change['old']}' → '{change['new']}'")
            else:
                formatted.append(f"{field}: {change}")
        
        return "; ".join(formatted)

class SecurityEvent(models.Model):
    """Specific security-related events that require special attention"""
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    audit_log = models.OneToOneField(AuditLog, on_delete=models.CASCADE, related_name='security_event')
    
    # Security event details
    event_type = models.CharField(max_length=50, choices=[
        ('suspicious_login', 'Suspicious Login'),
        ('multiple_failed_logins', 'Multiple Failed Logins'),
        ('privilege_escalation', 'Privilege Escalation'),
        ('data_breach_attempt', 'Data Breach Attempt'),
        ('unauthorized_access', 'Unauthorized Access'),
        ('malicious_request', 'Malicious Request'),
        ('account_lockout', 'Account Lockout'),
        ('password_policy_violation', 'Password Policy Violation'),
        ('session_hijacking', 'Session Hijacking'),
        ('sql_injection_attempt', 'SQL Injection Attempt'),
        ('xss_attempt', 'XSS Attempt'),
        ('csrf_attempt', 'CSRF Attempt'),
    ])
    
    risk_score = models.IntegerField(default=0, help_text="Risk score from 0-100")
    is_blocked = models.BooleanField(default=False, help_text="Was the action blocked")
    investigation_status = models.CharField(max_length=20, choices=[
        ('open', 'Open'),
        ('investigating', 'Investigating'),
        ('resolved', 'Resolved'),
        ('false_positive', 'False Positive'),
    ], default='open')
    
    # Response actions
    automated_response = models.JSONField(default=dict, blank=True, help_text="Automated responses taken")
    manual_response = models.TextField(blank=True, help_text="Manual investigation notes")
    
    # Notification
    notifications_sent = models.JSONField(default=list, blank=True, help_text="List of notifications sent")
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['event_type', '-created_at']),
            models.Index(fields=['risk_score', '-created_at']),
            models.Index(fields=['investigation_status']),
        ]
    
    def __str__(self):
        return f"{self.event_type} - {self.audit_log.username} - {self.created_at}"

class AuditConfiguration(models.Model):
    """Configuration for audit logging behavior"""
    
    # What to log
    log_read_operations = models.BooleanField(default=False, help_text="Log read/view operations")
    log_failed_requests = models.BooleanField(default=True, help_text="Log failed requests")
    log_admin_actions = models.BooleanField(default=True, help_text="Log all admin actions")
    log_data_exports = models.BooleanField(default=True, help_text="Log data exports")
    log_bulk_operations = models.BooleanField(default=True, help_text="Log bulk operations")
    
    # Sensitive data handling
    mask_sensitive_fields = models.BooleanField(default=True, help_text="Mask sensitive data in logs")
    sensitive_field_patterns = models.JSONField(default=list, blank=True, help_text="Regex patterns for sensitive fields")
    
    # Retention policies
    default_retention_days = models.IntegerField(default=2555, help_text="Default retention period (7 years)")
    security_event_retention_days = models.IntegerField(default=3650, help_text="Security events retention (10 years)")
    compliance_retention_days = models.IntegerField(default=2555, help_text="Compliance logs retention (7 years)")
    
    # Performance settings
    async_logging = models.BooleanField(default=True, help_text="Use async logging for performance")
    batch_size = models.IntegerField(default=100, help_text="Batch size for bulk logging")
    
    # Alerting thresholds
    failed_login_threshold = models.IntegerField(default=5, help_text="Failed logins before alert")
    suspicious_activity_threshold = models.IntegerField(default=10, help_text="Suspicious activities before alert")
    
    # Compliance settings
    gdpr_compliance = models.BooleanField(default=True, help_text="Enable GDPR compliance features")
    hipaa_compliance = models.BooleanField(default=False, help_text="Enable HIPAA compliance features")
    sox_compliance = models.BooleanField(default=False, help_text="Enable SOX compliance features")
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = "Audit Configuration"
        verbose_name_plural = "Audit Configurations"
    
    def __str__(self):
        return f"Audit Configuration - Updated {self.updated_at}"
    
    @classmethod
    def get_config(cls):
        """Get the current audit configuration"""
        try:
            config, created = cls.objects.get_or_create(pk=1)
            return config
        except Exception:
            # Return default config if database is not ready (during migrations)
            return cls(
                log_read_operations=False,
                log_failed_requests=True,
                log_admin_actions=True,
                log_data_exports=True,
                log_bulk_operations=True,
                mask_sensitive_fields=True,
                default_retention_days=2555,
                security_event_retention_days=3650,
                compliance_retention_days=2555,
                async_logging=True,
                batch_size=100,
                failed_login_threshold=5,
                suspicious_activity_threshold=10,
                gdpr_compliance=True,
                hipaa_compliance=False,
                sox_compliance=False
            )
