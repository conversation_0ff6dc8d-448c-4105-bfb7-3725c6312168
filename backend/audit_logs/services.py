from django.contrib.auth.models import User
from django.contrib.contenttypes.models import ContentType
from django.utils import timezone
from django.conf import settings
from django.db import models
from typing import Dict, Any, Optional, List
import json
import re
import logging
from datetime import <PERSON><PERSON><PERSON>

from .models import AuditLog, SecurityEvent, AuditConfiguration, AuditAction, AuditSeverity

logger = logging.getLogger(__name__)

class AuditLogger:
    """Service for creating audit logs"""
    
    @staticmethod
    def log_action(
        user: Optional[User],
        action: str,
        content_object: Any = None,
        request: Any = None,
        changes: Dict[str, Any] = None,
        additional_data: Dict[str, Any] = None,
        severity: str = AuditSeverity.LOW,
        success: bool = True,
        error_message: str = "",
        session_key: str = "",
        compliance_tags: List[str] = None
    ) -> AuditLog:
        """Create an audit log entry"""

        config = AuditConfiguration.get_config()
        
        # Skip read operations if not configured to log them
        if action == AuditAction.READ and not config.log_read_operations:
            return None
        
        # Extract request information
        ip_address = None
        user_agent = ""
        request_method = ""
        request_path = ""
        request_params = {}
        
        if request:
            ip_address = AuditLogger._get_client_ip(request)
            user_agent = request.META.get('HTTP_USER_AGENT', '')[:500]
            request_method = request.method
            request_path = request.path[:500]
            
            # Safely extract request parameters
            try:
                if request.method == 'GET':
                    request_params = dict(request.GET.items())
                elif request.method == 'POST':
                    # Don't log sensitive POST data like passwords
                    request_params = AuditLogger._filter_sensitive_data(dict(request.POST.items()))
            except Exception:
                request_params = {}
        
        # Get content type and object info
        content_type = None
        object_id = ""
        object_repr = ""
        
        if content_object:
            content_type = ContentType.objects.get_for_model(content_object)
            object_id = str(content_object.pk) if hasattr(content_object, 'pk') else ""
            object_repr = str(content_object)[:200]
        
        # Process changes data
        if changes and config.mask_sensitive_fields:
            changes = AuditLogger._mask_sensitive_data(changes, config.sensitive_field_patterns)
        
        # Create audit log
        audit_log = AuditLog.objects.create(
            user=user,
            username=user.username if user else "Anonymous",
            session_key=session_key,
            action=action,
            severity=severity,
            content_type=content_type,
            object_id=object_id,
            content_object=content_object,
            object_repr=object_repr,
            ip_address=ip_address,
            user_agent=user_agent,
            request_method=request_method,
            request_path=request_path,
            request_params=request_params,
            changes=changes or {},
            additional_data=additional_data or {},
            success=success,
            error_message=error_message,
            compliance_tags=compliance_tags or [],
            retention_date=timezone.now() + timedelta(days=config.default_retention_days)
        )
        
        # Check for security events
        AuditLogger._check_security_events(audit_log, config)
        
        return audit_log
    
    @staticmethod
    def log_login_attempt(user: Optional[User], request: Any, success: bool, error_message: str = ""):
        """Log login attempts"""
        action = AuditAction.LOGIN if success else AuditAction.LOGIN_FAILED
        severity = AuditSeverity.LOW if success else AuditSeverity.MEDIUM
        
        additional_data = {
            'login_time': timezone.now().isoformat(),
            'success': success
        }
        
        if not success:
            additional_data['error'] = error_message
        
        return AuditLogger.log_action(
            user=user,
            action=action,
            request=request,
            additional_data=additional_data,
            severity=severity,
            success=success,
            error_message=error_message,
            compliance_tags=['authentication', 'security']
        )
    
    @staticmethod
    def log_data_change(user: User, action: str, instance: Any, changes: Dict[str, Any], request: Any = None):
        """Log data changes (create, update, delete)"""
        severity = AuditSeverity.MEDIUM if action == AuditAction.DELETE else AuditSeverity.LOW
        
        return AuditLogger.log_action(
            user=user,
            action=action,
            content_object=instance,
            request=request,
            changes=changes,
            severity=severity,
            compliance_tags=['data_change']
        )
    
    @staticmethod
    def log_bulk_operation(user: User, action: str, model_class: Any, count: int, request: Any = None):
        """Log bulk operations"""
        additional_data = {
            'model': model_class.__name__,
            'affected_count': count,
            'bulk_operation': True
        }
        
        return AuditLogger.log_action(
            user=user,
            action=action,
            request=request,
            additional_data=additional_data,
            severity=AuditSeverity.MEDIUM,
            compliance_tags=['bulk_operation']
        )
    
    @staticmethod
    def log_security_event(
        user: Optional[User],
        event_type: str,
        request: Any = None,
        risk_score: int = 50,
        additional_data: Dict[str, Any] = None
    ) -> SecurityEvent:
        """Log security events"""
        
        # Create audit log first
        audit_log = AuditLogger.log_action(
            user=user,
            action=AuditAction.SECURITY_EVENT,
            request=request,
            additional_data=additional_data,
            severity=AuditSeverity.HIGH if risk_score > 70 else AuditSeverity.MEDIUM,
            compliance_tags=['security', 'incident']
        )
        
        # Create security event
        security_event = SecurityEvent.objects.create(
            audit_log=audit_log,
            event_type=event_type,
            risk_score=risk_score,
            is_blocked=risk_score > 80,  # Auto-block high-risk events
        )
        
        # Send notifications for high-risk events
        if risk_score > 70:
            AuditLogger._send_security_alert(security_event)
        
        return security_event
    
    @staticmethod
    def _get_client_ip(request):
        """Extract client IP address from request"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
    
    @staticmethod
    def _filter_sensitive_data(data: Dict[str, Any]) -> Dict[str, Any]:
        """Filter out sensitive data from request parameters"""
        sensitive_keys = ['password', 'token', 'secret', 'key', 'auth', 'csrf']
        filtered = {}
        
        for key, value in data.items():
            if any(sensitive in key.lower() for sensitive in sensitive_keys):
                filtered[key] = "[FILTERED]"
            else:
                filtered[key] = value
        
        return filtered
    
    @staticmethod
    def _mask_sensitive_data(data: Dict[str, Any], patterns: List[str]) -> Dict[str, Any]:
        """Mask sensitive data based on patterns"""
        if not patterns:
            patterns = [
                r'.*password.*',
                r'.*secret.*',
                r'.*token.*',
                r'.*key.*',
                r'.*ssn.*',
                r'.*social.*',
                r'.*credit.*card.*',
                r'.*bank.*account.*'
            ]
        
        masked = {}
        for key, value in data.items():
            if any(re.match(pattern, key.lower()) for pattern in patterns):
                if isinstance(value, str) and len(value) > 4:
                    masked[key] = value[:2] + "*" * (len(value) - 4) + value[-2:]
                else:
                    masked[key] = "[MASKED]"
            else:
                masked[key] = value
        
        return masked
    
    @staticmethod
    def _check_security_events(audit_log: AuditLog, config: AuditConfiguration):
        """Check for potential security events"""
        
        # Check for multiple failed logins
        if audit_log.action == AuditAction.LOGIN_FAILED:
            recent_failures = AuditLog.objects.filter(
                ip_address=audit_log.ip_address,
                action=AuditAction.LOGIN_FAILED,
                timestamp__gte=timezone.now() - timedelta(minutes=15)
            ).count()
            
            if recent_failures >= config.failed_login_threshold:
                AuditLogger.log_security_event(
                    user=audit_log.user,
                    event_type='multiple_failed_logins',
                    risk_score=min(recent_failures * 10, 100),
                    additional_data={
                        'failed_attempts': recent_failures,
                        'ip_address': audit_log.ip_address,
                        'time_window': '15 minutes'
                    }
                )
        
        # Check for privilege escalation
        if audit_log.action in [AuditAction.UPDATE, AuditAction.CREATE] and audit_log.user:
            if 'role' in audit_log.changes or 'permissions' in audit_log.changes:
                AuditLogger.log_security_event(
                    user=audit_log.user,
                    event_type='privilege_escalation',
                    risk_score=80,
                    additional_data={
                        'changes': audit_log.changes,
                        'target_object': audit_log.object_repr
                    }
                )
    
    @staticmethod
    def _send_security_alert(security_event: SecurityEvent):
        """Send security alert notifications"""
        try:
            # Import here to avoid circular imports
            from notifications.services import NotificationService
            from notifications.models import NotificationType, NotificationPriority
            from django.contrib.auth.models import User
            
            # Get admin users
            admin_users = User.objects.filter(is_superuser=True)
            
            for admin in admin_users:
                NotificationService.create_notification(
                    recipient=admin,
                    title=f"Security Alert: {security_event.event_type}",
                    title_ar=f"تنبيه أمني: {security_event.event_type}",
                    message=f"Security event detected: {security_event.event_type} (Risk Score: {security_event.risk_score})",
                    message_ar=f"تم اكتشاف حدث أمني: {security_event.event_type} (درجة المخاطر: {security_event.risk_score})",
                    notification_type=NotificationType.SYSTEM_ALERT,
                    priority=NotificationPriority.URGENT,
                    action_url=f"/admin/security/events/{security_event.id}",
                    send_email=True,
                    compliance_tags=['security', 'alert']
                )
        except Exception as e:
            logger.error(f"Failed to send security alert: {str(e)}")

class AuditReporter:
    """Service for generating audit reports"""
    
    @staticmethod
    def generate_user_activity_report(user: User, start_date: timezone.datetime, end_date: timezone.datetime):
        """Generate user activity report"""
        logs = AuditLog.objects.filter(
            user=user,
            timestamp__range=[start_date, end_date]
        ).order_by('-timestamp')
        
        return {
            'user': user.username,
            'period': f"{start_date.date()} to {end_date.date()}",
            'total_actions': logs.count(),
            'actions_by_type': dict(logs.values_list('action').annotate(count=models.Count('action'))),
            'failed_actions': logs.filter(success=False).count(),
            'security_events': SecurityEvent.objects.filter(
                audit_log__user=user,
                created_at__range=[start_date, end_date]
            ).count(),
            'logs': logs[:100]  # Limit for performance
        }
    
    @staticmethod
    def generate_security_summary(days: int = 30):
        """Generate security summary report"""
        start_date = timezone.now() - timedelta(days=days)
        
        security_events = SecurityEvent.objects.filter(created_at__gte=start_date)
        failed_logins = AuditLog.objects.filter(
            action=AuditAction.LOGIN_FAILED,
            timestamp__gte=start_date
        )
        
        return {
            'period_days': days,
            'total_security_events': security_events.count(),
            'high_risk_events': security_events.filter(risk_score__gte=70).count(),
            'failed_logins': failed_logins.count(),
            'unique_ips_failed_login': failed_logins.values('ip_address').distinct().count(),
            'events_by_type': dict(security_events.values_list('event_type').annotate(count=models.Count('event_type'))),
            'top_risk_events': security_events.order_by('-risk_score')[:10]
        }
