"""
Production-ready database configuration with PostgreSQL support
"""

import os
from django.core.exceptions import ImproperlyConfigured

def get_database_config():
    """Get database configuration - PostgreSQL for production, SQLite for development"""

    # Check if we should use PostgreSQL (production/staging)
    use_postgresql = os.environ.get('USE_POSTGRESQL', 'false').lower() == 'true'

    if use_postgresql:
        # PostgreSQL Configuration
        db_config = {
            'ENGINE': 'django.db.backends.postgresql',
            'NAME': os.environ.get('DB_NAME', 'ems_production'),
            'USER': os.environ.get('DB_USER', 'ems_user'),
            'PASSWORD': os.environ.get('DB_PASSWORD'),
            'HOST': os.environ.get('DB_HOST', 'localhost'),
            'PORT': os.environ.get('DB_PORT', '5432'),
            'CONN_MAX_AGE': int(os.environ.get('DB_CONN_MAX_AGE', '600')),
            'OPTIONS': {
                'connect_timeout': 10,
            },
            'TEST': {
                'NAME': 'test_ems_db',
            }
        }

        # Validate required PostgreSQL settings
        if not db_config['PASSWORD']:
            raise ImproperlyConfigured(
                "DB_PASSWORD environment variable is required for PostgreSQL"
            )

        return db_config
    else:
        # SQLite Configuration (development only)
        return {
            'ENGINE': 'django.db.backends.sqlite3',
            'NAME': os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'db.sqlite3'),
            'OPTIONS': {
                'timeout': 20,
            }
        }

def get_database_health_check():
    """Database health check with connection testing"""
    try:
        from django.db import connection
        from django.db.utils import DatabaseError

        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            return True
    except (DatabaseError, Exception):
        return False

DATABASE_MONITORING = {
    'enabled': True,
    'LOG_SLOW_QUERIES': True,
    'SLOW_QUERY_THRESHOLD': 0.5,  # 500ms threshold for production
    'LOG_QUERY_COUNTS': True,
    'ENABLE_QUERY_ANALYSIS': True,
    'MAX_QUERY_TIME': 5.0,  # 5 second max query time
}