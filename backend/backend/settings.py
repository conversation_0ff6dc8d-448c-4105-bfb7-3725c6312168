"""
Django settings for backend project.

Generated by 'django-admin startproject' using Django 4.2.21.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.2/ref/settings/
"""

import os
from pathlib import Path

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Load environment variables from .env file
from dotenv import load_dotenv
load_dotenv(BASE_DIR / '.env')


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
# SECURITY FIX: Remove hardcoded fallback - force environment variable
SECRET_KEY = os.environ.get('SECRET_KEY')
if not SECRET_KEY:
    raise ValueError("SECRET_KEY environment variable is required. Generate one with: python -c 'from django.core.management.utils import get_random_secret_key; print(get_random_secret_key())'")

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = os.environ.get('DEBUG', 'False').lower() == 'true'

# Development mode for cookie security (allows HTTP cookies in development)
DEVELOPMENT_MODE = os.environ.get('DEVELOPMENT_MODE', 'True').lower() == 'true'

ALLOWED_HOSTS = os.environ.get('ALLOWED_HOSTS', 'localhost,127.0.0.1,testserver').split(',')


# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'rest_framework',
    'rest_framework_simplejwt',
    'rest_framework_simplejwt.token_blacklist',  # MODERN: Enable token blacklisting
    'corsheaders',
    'channels',  # WebSocket support for real-time KPI updates
    # 'django_ratelimit',  # Temporarily disabled
    'ems',
    'customer_service',
    'pdf_generation',
    'notifications',
    'audit_logs',  # ARCHITECTURE FIX: Re-enabled audit logging
]

MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'ems.middleware.security.SecurityHeadersMiddleware',
    # 'ems.middleware.security.InputValidationMiddleware',  # Temporarily disabled for testing
    'ems.rate_limiting.RateLimitMiddleware',  # SECURITY FIX: Re-enabled rate limiting
    'ems.middleware.security.RequestLoggingMiddleware',
    'ems.middleware.api_response_standardizer.APIResponseStandardizerMiddleware',  # API: Response standardization
    # 'ems.middleware.query_monitor.QueryMonitoringMiddleware',  # DATABASE: Query performance monitoring (temporarily disabled)
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',  # SECURITY FIX: Re-enabled CSRF protection
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'audit_logs.middleware.AuditMiddleware',  # ARCHITECTURE FIX: Re-enabled audit middleware
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'backend.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'backend.wsgi.application'


# Database Configuration
# CRITICAL FIX: Production-ready database setup
from .database_config import get_database_config, get_database_health_check, DATABASE_MONITORING

DATABASES = {
    'default': get_database_config()
}

# Database health monitoring
DATABASE_HEALTH = get_database_health_check()

# Query monitoring and optimization
SLOW_QUERY_THRESHOLD = 0.5  # 500ms threshold for slow queries
DATABASE_QUERY_TIMEOUT = 30  # 30 seconds max query time
DATABASE_MAX_CONNECTIONS = 20
DATABASE_CONNECTION_POOL_SIZE = 10

if DATABASE_MONITORING['LOG_SLOW_QUERIES']:
    LOGGING = {
        'version': 1,
        'disable_existing_loggers': False,
        'handlers': {
            'db_queries': {
                'level': 'DEBUG',
                'class': 'logging.StreamHandler',
            },
            'slow_queries': {
                'level': 'WARNING',
                'class': 'logging.FileHandler',
                'filename': 'logs/slow_queries.log',
            },
        },
        'loggers': {
            'django.db.backends': {
                'handlers': ['db_queries'],
                'level': 'DEBUG',
                'propagate': False,
            },
            'ems.middleware.query_monitor': {
                'handlers': ['slow_queries'],
                'level': 'WARNING',
                'propagate': False,
            },
        },
    }


# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_TZ = True

# Security Settings
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_HSTS_SECONDS = 31536000 if not DEBUG else 0  # 1 year in production
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True
X_FRAME_OPTIONS = 'DENY'
SECURE_REFERRER_POLICY = 'strict-origin-when-cross-origin'

# Session Security - SECURITY FIX: Enhanced session security
SESSION_COOKIE_SECURE = not DEBUG  # HTTPS only in production
SESSION_COOKIE_HTTPONLY = True
SESSION_COOKIE_SAMESITE = 'Strict' if not DEBUG else 'Lax'  # Strict in production
SESSION_COOKIE_AGE = 3600  # 1 hour
SESSION_EXPIRE_AT_BROWSER_CLOSE = True  # SECURITY: Expire session when browser closes
SESSION_SAVE_EVERY_REQUEST = True  # Update session on every request

# CSRF Protection (SECURITY FIX: Secure CSRF configuration for SPA)
CSRF_COOKIE_SECURE = not DEBUG  # HTTPS only in production
CSRF_COOKIE_HTTPONLY = False  # SECURITY FIX: Allow JS access for SPA
CSRF_COOKIE_SAMESITE = 'Strict' if not DEBUG else 'Lax'  # Strict in production
CSRF_USE_SESSIONS = False  # Use cookie-based CSRF for SPA compatibility
CSRF_COOKIE_NAME = 'csrftoken'  # Standard name for frontend frameworks
CSRF_TRUSTED_ORIGINS = [
    'http://localhost:5173',  # Vite dev server
    'http://localhost:5174',
    'http://localhost:5175',
    'http://localhost:5176',
    'http://127.0.0.1:5173',
    'http://127.0.0.1:5174',
    'http://127.0.0.1:5175',
    'http://127.0.0.1:5176'
]
CSRF_HEADER_NAME = 'HTTP_X_CSRFTOKEN'  # Header name for CSRF token
CSRF_COOKIE_NAME = 'csrftoken'  # Cookie name for CSRF token

# Password Validation Enhancement
AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
        'OPTIONS': {
            'min_length': 8,
        }
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/

STATIC_URL = 'static/'

# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# CORS settings
CORS_ALLOWED_ORIGINS = [
    "http://localhost:5173",  # Vite default port
    "http://127.0.0.1:5173",
    "http://localhost:5174",  # Alternative Vite port
    "http://127.0.0.1:5174",
    "http://localhost:5175",  # Additional Vite ports
    "http://127.0.0.1:5175",
    "http://localhost:5176",
    "http://127.0.0.1:5176",
    "http://localhost:5177",
    "http://127.0.0.1:5177",
    "http://localhost:5178",
    "http://127.0.0.1:5178",
    "http://localhost:5179",  # Current frontend port
    "http://127.0.0.1:5179",
    "http://localhost:5180",
    "http://127.0.0.1:5180",
    "http://localhost:5181",  # Current frontend port
    "http://127.0.0.1:5181",
    "http://localhost:5182",
    "http://127.0.0.1:5182",
]

CORS_ALLOW_CREDENTIALS = True

# Cookie settings for cross-origin authentication
SESSION_COOKIE_SAMESITE = 'None' if not DEVELOPMENT_MODE else 'Lax'
SESSION_COOKIE_SECURE = not DEVELOPMENT_MODE
CSRF_COOKIE_SAMESITE = 'None' if not DEVELOPMENT_MODE else 'Lax'
CSRF_COOKIE_SECURE = not DEVELOPMENT_MODE

# Additional CORS settings for development
CORS_ALLOW_ALL_ORIGINS = DEBUG  # Only allow all origins in debug mode
CORS_ALLOWED_HEADERS = [
    'accept',
    'accept-encoding',
    'authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
]

CORS_ALLOW_METHODS = [
    'DELETE',
    'GET',
    'OPTIONS',
    'PATCH',
    'POST',
    'PUT',
]

# REST Framework settings (SECURITY FIX: Custom cookie-based JWT authentication)
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'ems.authentication.CookieJWTAuthentication',  # SECURITY FIX: Custom httpOnly cookie auth
        'rest_framework_simplejwt.authentication.JWTAuthentication',  # Fallback for migration
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 20,
    'DEFAULT_RENDERER_CLASSES': [
        'rest_framework.renderers.JSONRenderer',
    ],
    'DEFAULT_PARSER_CLASSES': [
        'rest_framework.parsers.JSONParser',
    ],
    'EXCEPTION_HANDLER': 'ems.error_handlers.custom_exception_handler',  # ARCHITECTURE FIX: Custom error handling
}

# JWT Settings
from datetime import timedelta

SIMPLE_JWT = {
    # FIXED JWT SETTINGS - Balanced security and functionality
    'ACCESS_TOKEN_LIFETIME': timedelta(minutes=60),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=7),
    'ROTATE_REFRESH_TOKENS': False,  # FIXED: Disable rotation to prevent refresh loop
    'BLACKLIST_AFTER_ROTATION': False,  # FIXED: Disable auto-blacklisting to prevent refresh loop
    'UPDATE_LAST_LOGIN': True,

    # Cryptographic settings
    'ALGORITHM': 'HS256',
    'SIGNING_KEY': SECRET_KEY,
    'VERIFYING_KEY': None,
    'AUDIENCE': None,
    'ISSUER': None,

    # Header settings
    'AUTH_HEADER_TYPES': ('Bearer',),
    'AUTH_HEADER_NAME': 'HTTP_AUTHORIZATION',

    # User identification
    'USER_ID_FIELD': 'id',
    'USER_ID_CLAIM': 'user_id',

    # Token classes
    'AUTH_TOKEN_CLASSES': ('rest_framework_simplejwt.tokens.AccessToken',),
    'TOKEN_TYPE_CLAIM': 'token_type',

    # Additional settings
    'JTI_CLAIM': 'jti',
}

# Security Settings
SECURE_SSL_REDIRECT = os.environ.get('SECURE_SSL_REDIRECT', 'False').lower() == 'true'
SECURE_HSTS_SECONDS = int(os.environ.get('SECURE_HSTS_SECONDS', '0'))
SECURE_HSTS_INCLUDE_SUBDOMAINS = os.environ.get('SECURE_HSTS_INCLUDE_SUBDOMAINS', 'False').lower() == 'true'
SECURE_HSTS_PRELOAD = os.environ.get('SECURE_HSTS_PRELOAD', 'False').lower() == 'true'
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_BROWSER_XSS_FILTER = True
X_FRAME_OPTIONS = 'DENY'

# Session Security
SESSION_COOKIE_SECURE = not DEBUG
SESSION_COOKIE_HTTPONLY = True
SESSION_COOKIE_SAMESITE = 'Lax'
CSRF_COOKIE_SECURE = not DEBUG
CSRF_COOKIE_HTTPONLY = True
CSRF_COOKIE_SAMESITE = 'Lax'

# Logging Configuration
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': 'django.log',
            'formatter': 'verbose',
        },
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
        },
    },
    'root': {
        'handlers': ['console', 'file'],
        'level': os.environ.get('LOG_LEVEL', 'INFO'),
    },
    'loggers': {
        'django': {
            'handlers': ['console', 'file'],
            'level': os.environ.get('LOG_LEVEL', 'INFO'),
            'propagate': False,
        },
        'ems.authentication': {
            'handlers': ['console', 'file'],
            'level': 'DEBUG',
            'propagate': False,
        },
    },
}

# CRITICAL FIX: Rate Limiting Configuration - ENABLED FOR SECURITY
RATELIMIT_ENABLE = True  # SECURITY FIX: Enabled to prevent brute force attacks
RATELIMIT_USE_CACHE = 'default'
RATELIMIT_VIEW = 'django_ratelimit.views.ratelimited'

# Rate limiting rules
RATELIMIT_RULES = {
    'login': '5/m',  # 5 login attempts per minute
    'api_general': '100/m',  # 100 API calls per minute
    'api_heavy': '20/m',  # 20 heavy operations per minute
    'password_reset': '3/h',  # 3 password resets per hour
    'registration': '5/h',  # 5 registrations per hour
}

# CRITICAL FIX: Cache Configuration - Using local memory for development
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'unique-snowflake',
    }
}

# Redis cache configuration (commented out for development without Redis)
# CACHES = {
#     'default': {
#         'BACKEND': 'django_redis.cache.RedisCache',
#         'LOCATION': os.environ.get('REDIS_URL', 'redis://127.0.0.1:6379/0'),
#         'OPTIONS': {
#             'CLIENT_CLASS': 'django_redis.client.DefaultClient',
#             'CONNECTION_POOL_KWARGS': {
#                 'max_connections': 20,
#                 'retry_on_timeout': True,
#             },
#             'COMPRESSOR': 'django_redis.compressors.zlib.ZlibCompressor',
#             'SERIALIZER': 'django_redis.serializers.json.JSONSerializer',
#         },
#         'TIMEOUT': 300,
#         'KEY_PREFIX': 'ems',
#         'VERSION': 1,
#     },
#     # Redis cache (disabled - not running)
#     'redis': {
#         'BACKEND': 'django_redis.cache.RedisCache',
#         'LOCATION': os.environ.get('REDIS_URL', 'redis://127.0.0.1:6379/0'),
#         'OPTIONS': {
#             'CLIENT_CLASS': 'django_redis.client.DefaultClient',
#             'CONNECTION_POOL_KWARGS': {
#                 'max_connections': 20,
#                 'retry_on_timeout': True,
#             },
#             'COMPRESSOR': 'django_redis.compressors.zlib.ZlibCompressor',
#             'SERIALIZER': 'django_redis.serializers.json.JSONSerializer',
#         },
#         'TIMEOUT': 300,
#         'KEY_PREFIX': 'ems',
#         'VERSION': 1,
#     },
#     # Fallback to database cache if Redis is unavailable
#     'fallback': {
#         'BACKEND': 'django.core.cache.backends.db.DatabaseCache',
#         'LOCATION': 'cache_table',
#         'TIMEOUT': 300,
#         'OPTIONS': {
#             'MAX_ENTRIES': 1000,
#         }
#     }

# Email Configuration
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'  # For development
# EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'  # For production

# SMTP Configuration (uncomment for production)
# EMAIL_HOST = 'smtp.gmail.com'
# EMAIL_PORT = 587
# EMAIL_USE_TLS = True
# EMAIL_HOST_USER = '<EMAIL>'
# EMAIL_HOST_PASSWORD = 'your-app-password'

DEFAULT_FROM_EMAIL = '<EMAIL>'
ADMIN_EMAIL = '<EMAIL>'
SUPPORT_EMAIL = '<EMAIL>'
COMPANY_NAME = 'شركة نمو'
FRONTEND_URL = 'http://localhost:5178'

# ============================================================================
# CHANNELS CONFIGURATION (WebSocket Support)
# ============================================================================

# ASGI application for WebSocket support
ASGI_APPLICATION = 'backend.asgi.application'

# Channel layers configuration for real-time features
CHANNEL_LAYERS = {
    'default': {
        'BACKEND': 'channels.layers.InMemoryChannelLayer',
        # For production, use Redis:
        # 'BACKEND': 'channels_redis.core.RedisChannelLayer',
        # 'CONFIG': {
        #     "hosts": [('127.0.0.1', 6379)],
        # },
    },
}

# WebSocket settings
WEBSOCKET_ACCEPT_ALL = False  # Require authentication
WEBSOCKET_TIMEOUT = 300  # 5 minutes timeout
WEBSOCKET_HEARTBEAT_INTERVAL = 30  # 30 seconds heartbeat

# Real-time KPI update settings
KPI_REALTIME_ENABLED = True
KPI_BROADCAST_BATCH_SIZE = 10
KPI_BROADCAST_INTERVAL = 5  # seconds

# SECURITY HARDENING: Enhanced security settings
SECURE_SSL_REDIRECT = not DEVELOPMENT_MODE  # Redirect HTTP to HTTPS in production
SESSION_COOKIE_SECURE = not DEVELOPMENT_MODE  # Secure cookies in production
CSRF_COOKIE_SECURE = not DEVELOPMENT_MODE  # Secure CSRF cookies in production
SECURE_HSTS_SECONDS = 31536000 if not DEVELOPMENT_MODE else 0  # 1 year HSTS in production
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_BROWSER_XSS_FILTER = True
SECURE_REFERRER_POLICY = 'strict-origin-when-cross-origin'

# Session security
SESSION_COOKIE_HTTPONLY = True
SESSION_COOKIE_SAMESITE = 'Lax'
SESSION_EXPIRE_AT_BROWSER_CLOSE = True
SESSION_COOKIE_AGE = 3600  # 1 hour session timeout

# CSRF security
CSRF_COOKIE_HTTPONLY = True
CSRF_COOKIE_SAMESITE = 'Lax'
CSRF_USE_SESSIONS = False  # Use cookies for CSRF tokens
CSRF_TRUSTED_ORIGINS = ['http://localhost:3000', 'http://127.0.0.1:3000']  # Frontend origins
