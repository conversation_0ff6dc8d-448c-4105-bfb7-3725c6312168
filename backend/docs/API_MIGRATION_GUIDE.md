# API Migration Guide

## Overview
This guide helps migrate from legacy API endpoints to the new v1 API architecture.

## Endpoint Migration Map

### Core Management
| Legacy Endpoint | New v1 Endpoint | Status |
|----------------|-----------------|---------|
| `/api/departments/` | `/api/v1/departments/` | ✅ Available |
| `/api/employees/` | `/api/v1/employees/` | ✅ Available |
| `/api/activities/` | `/api/v1/activities/` | ✅ Available |

### HR Management
| Legacy Endpoint | New v1 Endpoint | Status |
|----------------|-----------------|---------|
| `/api/roles/` | `/api/v1/roles/` | ✅ Available |
| `/api/leave-requests/` | `/api/v1/leave-requests/` | ✅ Available |
| `/api/attendance/` | `/api/v1/attendance/` | ✅ Available |

### Authentication
| Legacy Endpoint | New v1 Endpoint | Status |
|----------------|-----------------|---------|
| `/api/auth/login/` | `/api/v1/auth/login/` | ✅ Available |
| `/api/auth/refresh/` | `/api/v1/auth/refresh/` | ✅ Available |
| `/api/auth/logout/` | `/api/v1/auth/logout/` | ✅ Available |

## Migration Steps

1. **Update Frontend API Calls**
   - Replace `/api/` with `/api/v1/` in all API calls
   - Update error handling for new response format
   - Test all functionality thoroughly

2. **Response Format Changes**
   - All responses now include standard headers
   - Error responses follow RFC 7807 format
   - Pagination format is consistent across endpoints

3. **Breaking Changes**
   - Some field names may have changed for consistency
   - Date formats are now ISO 8601 standard
   - Authentication tokens have longer expiry times

## Testing Your Migration

```javascript
// Old way
fetch('/api/employees/')

// New way
fetch('/api/v1/employees/')
```

## Support

For migration support, check the API documentation or contact the development team.
