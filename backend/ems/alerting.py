"""
EMS Alerting System
Monitors system health and sends alerts for critical issues
"""

import logging
import time
from django.core.mail import send_mail
from django.conf import settings
from django.core.management.base import BaseCommand
from .monitoring_dashboard import SystemMonitor

logger = logging.getLogger(__name__)

class AlertManager:
    """Manages system alerts and notifications"""
    
    def __init__(self):
        self.alert_thresholds = {
            'database_response_time_ms': 1000,
            'cpu_percent': 80,
            'memory_percent': 85,
            'disk_percent': 90,
            'api_success_rate': 95,
            'security_score': 80
        }
        
        self.alert_cooldown = 300  # 5 minutes between same alerts
        self.last_alerts = {}
    
    def check_system_health(self):
        """Check system health and trigger alerts if needed"""
        monitor = SystemMonitor()
        
        # Get metrics
        db_metrics = monitor.get_database_metrics()
        system_metrics = monitor.get_system_metrics()
        api_metrics = monitor.get_api_metrics()
        security_metrics = monitor.get_security_metrics()
        
        alerts = []
        
        # Database alerts
        if db_metrics.get('status') == 'error':
            alerts.append({
                'type': 'critical',
                'component': 'database',
                'message': f"Database connection failed: {db_metrics.get('error', 'Unknown error')}"
            })
        elif db_metrics.get('response_time_ms', 0) > self.alert_thresholds['database_response_time_ms']:
            alerts.append({
                'type': 'warning',
                'component': 'database',
                'message': f"Database response time high: {db_metrics.get('response_time_ms')}ms"
            })
        
        # System alerts
        if system_metrics.get('status') == 'healthy':
            cpu_percent = system_metrics.get('cpu_percent', 0)
            memory_percent = system_metrics.get('memory', {}).get('percent_used', 0)
            disk_percent = system_metrics.get('disk', {}).get('percent_used', 0)
            
            if cpu_percent > self.alert_thresholds['cpu_percent']:
                alerts.append({
                    'type': 'warning',
                    'component': 'system',
                    'message': f"High CPU usage: {cpu_percent}%"
                })
            
            if memory_percent > self.alert_thresholds['memory_percent']:
                alerts.append({
                    'type': 'warning',
                    'component': 'system',
                    'message': f"High memory usage: {memory_percent}%"
                })
            
            if disk_percent > self.alert_thresholds['disk_percent']:
                alerts.append({
                    'type': 'critical',
                    'component': 'system',
                    'message': f"High disk usage: {disk_percent}%"
                })
        
        # API alerts
        if api_metrics.get('status') == 'error':
            alerts.append({
                'type': 'critical',
                'component': 'api',
                'message': f"API system error: {api_metrics.get('error', 'Unknown error')}"
            })
        elif api_metrics.get('success_rate_percent', 100) < self.alert_thresholds['api_success_rate']:
            alerts.append({
                'type': 'warning',
                'component': 'api',
                'message': f"API success rate low: {api_metrics.get('success_rate_percent')}%"
            })
        
        # Security alerts
        if security_metrics.get('status') == 'warning':
            security_score = security_metrics.get('security_score_percent', 0)
            if security_score < self.alert_thresholds['security_score']:
                alerts.append({
                    'type': 'warning',
                    'component': 'security',
                    'message': f"Security score low: {security_score}%"
                })
        
        # Send alerts
        for alert in alerts:
            self.send_alert(alert)
        
        return alerts
    
    def send_alert(self, alert):
        """Send alert notification"""
        alert_key = f"{alert['component']}_{alert['type']}"
        current_time = time.time()
        
        # Check cooldown
        if alert_key in self.last_alerts:
            if current_time - self.last_alerts[alert_key] < self.alert_cooldown:
                return  # Skip due to cooldown
        
        # Log alert
        log_level = logging.CRITICAL if alert['type'] == 'critical' else logging.WARNING
        logger.log(log_level, f"ALERT [{alert['type'].upper()}] {alert['component']}: {alert['message']}")
        
        # Send email if configured
        if hasattr(settings, 'ALERT_EMAIL_RECIPIENTS') and settings.ALERT_EMAIL_RECIPIENTS:
            try:
                subject = f"EMS Alert [{alert['type'].upper()}] - {alert['component']}"
                message = f"""
EMS System Alert

Component: {alert['component']}
Type: {alert['type']}
Message: {alert['message']}
Timestamp: {time.strftime('%Y-%m-%d %H:%M:%S')}

Please check the system monitoring dashboard for more details.
"""
                
                send_mail(
                    subject,
                    message,
                    settings.DEFAULT_FROM_EMAIL,
                    settings.ALERT_EMAIL_RECIPIENTS,
                    fail_silently=False
                )
                
                logger.info(f"Alert email sent for {alert_key}")
                
            except Exception as e:
                logger.error(f"Failed to send alert email: {e}")
        
        # Update last alert time
        self.last_alerts[alert_key] = current_time

class Command(BaseCommand):
    """Management command to run health checks"""
    help = 'Run system health checks and send alerts'
    
    def handle(self, *args, **options):
        alert_manager = AlertManager()
        alerts = alert_manager.check_system_health()
        
        if alerts:
            self.stdout.write(f"Generated {len(alerts)} alerts")
            for alert in alerts:
                self.stdout.write(f"  {alert['type']}: {alert['message']}")
        else:
            self.stdout.write("No alerts generated - system healthy")
