"""
CRITICAL FIX: Clean API v1 URL Configuration
Consolidated endpoints with proper versioning and no duplicates
"""

from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer
from . import views
from . import auth_views

app_name = 'api_v1'

# Create API v1 router
router_v1 = DefaultRouter()

# Core Management Endpoints
router_v1.register(r'departments', views.DepartmentViewSet, basename='departments')
router_v1.register(r'employees', views.EmployeeViewSet, basename='employees')
router_v1.register(r'activities', views.ActivityViewSet, basename='activities')

# HR Management Endpoints
router_v1.register(r'roles', views.RoleViewSet, basename='roles')
router_v1.register(r'user-profiles', views.UserProfileViewSet, basename='user-profiles')
router_v1.register(r'leave-types', views.LeaveTypeViewSet, basename='leave-types')
router_v1.register(r'leave-requests', views.LeaveRequestViewSet, basename='leave-requests')
router_v1.register(r'attendance', views.AttendanceViewSet, basename='attendance')

# Project Management Endpoints
router_v1.register(r'projects', views.ProjectViewSet, basename='projects')
router_v1.register(r'tasks', views.TaskViewSet, basename='tasks')

# Financial Management Endpoints
router_v1.register(r'budgets', views.BudgetViewSet, basename='budgets')
router_v1.register(r'expenses', views.ExpenseViewSet, basename='expenses')

# Asset Management Endpoints
router_v1.register(r'asset-categories', views.AssetCategoryViewSet, basename='asset-categories')
router_v1.register(r'assets', views.AssetViewSet, basename='assets')
router_v1.register(r'suppliers', views.SupplierViewSet, basename='suppliers')
router_v1.register(r'purchase-orders', views.PurchaseOrderViewSet, basename='purchase-orders')

# Communication & Collaboration Endpoints
router_v1.register(r'announcements', views.AnnouncementViewSet, basename='announcements')
router_v1.register(r'messages', views.MessageViewSet, basename='messages')
router_v1.register(r'documents', views.DocumentViewSet, basename='documents')
router_v1.register(r'meetings', views.MeetingViewSet, basename='meetings')

# Customer & Sales Management Endpoints
router_v1.register(r'customers', views.CustomerViewSet, basename='customers')
router_v1.register(r'product-categories', views.ProductCategoryViewSet, basename='product-categories')
router_v1.register(r'products', views.ProductViewSet, basename='products')
router_v1.register(r'sales-orders', views.SalesOrderViewSet, basename='sales-orders')

# Report Management Endpoints
router_v1.register(r'reports', views.ReportViewSet, basename='reports')

# API v1 URL patterns
api_v1_patterns = [
    # Main API router
    path('', include(router_v1.urls)),
    
    # Authentication endpoints
    path('auth/login/', auth_views.CustomTokenObtainPairView.as_view(), name='auth-login'),
    path('auth/refresh/', auth_views.CustomTokenRefreshView.as_view(), name='auth-refresh'),
    path('auth/logout/', auth_views.LogoutView.as_view(), name='auth-logout'),
    path('auth/user/', auth_views.user_profile_view, name='auth-user'),
    path('auth/profile/', auth_views.update_profile_view, name='auth-profile'),
    path('auth/change-password/', auth_views.change_password_view, name='auth-change-password'),
    
    # Dashboard and stats endpoints
    path('dashboard/stats/', views.dashboard_stats, name='dashboard-stats'),
    path('dashboard/superadmin-stats/', views.superadmin_system_stats, name='superadmin-stats'),
    
    # Utility endpoints
    path('user-profile/', views.user_profile, name='user-profile'),
    path('test/', views.dashboard_stats, name='test-endpoint'),
]

# Endpoint mapping for frontend migration
ENDPOINT_MIGRATION_MAP = {
    # Old endpoint -> New endpoint
    'employee-tasks': 'tasks',
    'employee-leave': 'leave-requests', 
    'hr-employees': 'employees',
    'hr-departments': 'departments',
    'hr-reports': 'reports',
    'quotations': 'sales-orders',
    'inventory': 'assets',
    'analytics/reports': 'reports',
    'compliance/audits': 'reports',
    'security/logs': 'activities',
    'users': 'employees',
    'auth/users': 'employees',
}

def get_migration_map():
    """Return endpoint migration mapping for frontend updates"""
    return ENDPOINT_MIGRATION_MAP

# URL patterns for API v1
urlpatterns = api_v1_patterns
