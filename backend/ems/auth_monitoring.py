"""
Authentication Monitoring System
Comprehensive logging and monitoring for authentication flows
"""

import logging
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from django.conf import settings
from django.core.cache import cache
from django.utils import timezone
from django.contrib.auth.models import User
import json

# Configure authentication logger
auth_logger = logging.getLogger('ems.authentication')

class AuthenticationMonitor:
    """
    Comprehensive authentication monitoring and analytics
    """
    
    # Cache keys for metrics
    CACHE_PREFIX = 'auth_monitor'
    LOGIN_ATTEMPTS_KEY = f'{CACHE_PREFIX}:login_attempts'
    LOGIN_SUCCESS_KEY = f'{CACHE_PREFIX}:login_success'
    LOGIN_FAILURES_KEY = f'{CACHE_PREFIX}:login_failures'
    TOKEN_REFRESH_KEY = f'{CACHE_PREFIX}:token_refresh'
    LOGOUT_COUNT_KEY = f'{CACHE_PREFIX}:logout_count'
    ACTIVE_SESSIONS_KEY = f'{CACHE_PREFIX}:active_sessions'
    
    @classmethod
    def log_login_attempt(cls, username: str, ip_address: str, user_agent: str, success: bool, 
                         error_code: Optional[str] = None, user_id: Optional[int] = None):
        """
        Log authentication attempt with comprehensive details
        """
        timestamp = timezone.now()
        
        # Prepare log data
        log_data = {
            'event': 'login_attempt',
            'username': username,
            'ip_address': ip_address,
            'user_agent': user_agent,
            'success': success,
            'timestamp': timestamp.isoformat(),
            'user_id': user_id,
            'error_code': error_code
        }
        
        # Log to file
        if success:
            auth_logger.info(f"LOGIN_SUCCESS: {username} from {ip_address}", extra=log_data)
        else:
            auth_logger.warning(f"LOGIN_FAILURE: {username} from {ip_address} - {error_code}", extra=log_data)
        
        # Update metrics
        cls._update_login_metrics(success, username, ip_address)
        
        # Track suspicious activity
        cls._check_suspicious_activity(username, ip_address, success)
    
    @classmethod
    def log_token_refresh(cls, user_id: int, ip_address: str, success: bool, 
                         error_code: Optional[str] = None):
        """
        Log token refresh attempts
        """
        timestamp = timezone.now()
        
        log_data = {
            'event': 'token_refresh',
            'user_id': user_id,
            'ip_address': ip_address,
            'success': success,
            'timestamp': timestamp.isoformat(),
            'error_code': error_code
        }
        
        if success:
            auth_logger.info(f"TOKEN_REFRESH_SUCCESS: User {user_id} from {ip_address}", extra=log_data)
        else:
            auth_logger.warning(f"TOKEN_REFRESH_FAILURE: User {user_id} from {ip_address} - {error_code}", extra=log_data)
        
        # Update metrics
        cls._increment_counter(cls.TOKEN_REFRESH_KEY)
    
    @classmethod
    def log_logout(cls, user_id: int, username: str, ip_address: str, 
                  method: str = 'manual'):
        """
        Log logout events
        """
        timestamp = timezone.now()
        
        log_data = {
            'event': 'logout',
            'user_id': user_id,
            'username': username,
            'ip_address': ip_address,
            'method': method,  # manual, timeout, forced
            'timestamp': timestamp.isoformat()
        }
        
        auth_logger.info(f"LOGOUT: {username} from {ip_address} ({method})", extra=log_data)
        
        # Update metrics
        cls._increment_counter(cls.LOGOUT_COUNT_KEY)
        cls._remove_active_session(user_id)
    
    @classmethod
    def log_authentication_error(cls, error_type: str, details: Dict[str, Any], 
                                ip_address: str):
        """
        Log authentication errors and security issues
        """
        timestamp = timezone.now()
        
        log_data = {
            'event': 'auth_error',
            'error_type': error_type,
            'details': details,
            'ip_address': ip_address,
            'timestamp': timestamp.isoformat()
        }
        
        auth_logger.error(f"AUTH_ERROR: {error_type} from {ip_address}", extra=log_data)
    
    @classmethod
    def get_authentication_metrics(cls, hours: int = 24) -> Dict[str, Any]:
        """
        Get authentication metrics for the specified time period
        """
        try:
            # Get cached metrics
            login_attempts = cache.get(cls.LOGIN_ATTEMPTS_KEY, 0)
            login_success = cache.get(cls.LOGIN_SUCCESS_KEY, 0)
            login_failures = cache.get(cls.LOGIN_FAILURES_KEY, 0)
            token_refreshes = cache.get(cls.TOKEN_REFRESH_KEY, 0)
            logout_count = cache.get(cls.LOGOUT_COUNT_KEY, 0)
            active_sessions = cache.get(cls.ACTIVE_SESSIONS_KEY, {})
            
            # Calculate success rate
            success_rate = (login_success / login_attempts * 100) if login_attempts > 0 else 0
            
            return {
                'period_hours': hours,
                'login_attempts': login_attempts,
                'login_success': login_success,
                'login_failures': login_failures,
                'success_rate': round(success_rate, 2),
                'token_refreshes': token_refreshes,
                'logout_count': logout_count,
                'active_sessions': len(active_sessions),
                'timestamp': timezone.now().isoformat()
            }
        except Exception as e:
            auth_logger.error(f"Failed to get authentication metrics: {e}")
            return {}
    
    @classmethod
    def _update_login_metrics(cls, success: bool, username: str, ip_address: str):
        """
        Update login metrics in cache
        """
        try:
            # Increment total attempts
            cls._increment_counter(cls.LOGIN_ATTEMPTS_KEY)
            
            if success:
                cls._increment_counter(cls.LOGIN_SUCCESS_KEY)
                # Track active session
                user = User.objects.filter(username=username).first()
                if user:
                    cls._add_active_session(user.id, ip_address)
            else:
                cls._increment_counter(cls.LOGIN_FAILURES_KEY)
        except Exception as e:
            auth_logger.error(f"Failed to update login metrics: {e}")
    
    @classmethod
    def _increment_counter(cls, key: str, ttl: int = 86400):  # 24 hours
        """
        Increment a counter in cache with TTL
        """
        try:
            current = cache.get(key, 0)
            cache.set(key, current + 1, ttl)
        except Exception as e:
            auth_logger.error(f"Failed to increment counter {key}: {e}")
    
    @classmethod
    def _add_active_session(cls, user_id: int, ip_address: str):
        """
        Add active session to tracking
        """
        try:
            active_sessions = cache.get(cls.ACTIVE_SESSIONS_KEY, {})
            active_sessions[str(user_id)] = {
                'ip_address': ip_address,
                'login_time': timezone.now().isoformat()
            }
            cache.set(cls.ACTIVE_SESSIONS_KEY, active_sessions, 86400)
        except Exception as e:
            auth_logger.error(f"Failed to add active session: {e}")
    
    @classmethod
    def _remove_active_session(cls, user_id: int):
        """
        Remove active session from tracking
        """
        try:
            active_sessions = cache.get(cls.ACTIVE_SESSIONS_KEY, {})
            active_sessions.pop(str(user_id), None)
            cache.set(cls.ACTIVE_SESSIONS_KEY, active_sessions, 86400)
        except Exception as e:
            auth_logger.error(f"Failed to remove active session: {e}")
    
    @classmethod
    def _check_suspicious_activity(cls, username: str, ip_address: str, success: bool):
        """
        Check for suspicious authentication activity
        """
        try:
            # Track failed attempts per IP
            if not success:
                failed_key = f'{cls.CACHE_PREFIX}:failed:{ip_address}'
                failed_count = cache.get(failed_key, 0) + 1
                cache.set(failed_key, failed_count, 3600)  # 1 hour
                
                # Alert on multiple failures
                if failed_count >= 5:
                    cls.log_authentication_error(
                        'SUSPICIOUS_ACTIVITY',
                        {
                            'type': 'multiple_failed_attempts',
                            'username': username,
                            'failed_count': failed_count,
                            'threshold': 5
                        },
                        ip_address
                    )
            
            # Track successful logins from new IPs
            if success:
                user_ip_key = f'{cls.CACHE_PREFIX}:user_ips:{username}'
                known_ips = cache.get(user_ip_key, set())
                
                if ip_address not in known_ips:
                    # New IP for this user
                    auth_logger.info(f"NEW_IP_LOGIN: {username} from new IP {ip_address}")
                    known_ips.add(ip_address)
                    cache.set(user_ip_key, known_ips, 86400 * 30)  # 30 days
                    
        except Exception as e:
            auth_logger.error(f"Failed to check suspicious activity: {e}")

# Utility functions for easy integration
def log_login_attempt(username: str, request, success: bool, error_code: Optional[str] = None, user_id: Optional[int] = None):
    """
    Convenience function to log login attempts
    """
    ip_address = get_client_ip(request)
    user_agent = request.META.get('HTTP_USER_AGENT', 'Unknown')
    
    AuthenticationMonitor.log_login_attempt(
        username=username,
        ip_address=ip_address,
        user_agent=user_agent,
        success=success,
        error_code=error_code,
        user_id=user_id
    )

def get_client_ip(request) -> str:
    """
    Get client IP address from request
    """
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip or 'Unknown'
