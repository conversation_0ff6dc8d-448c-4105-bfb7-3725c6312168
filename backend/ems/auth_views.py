"""
Authentication Views for EMS API
Handles JWT authentication, user registration, password reset, etc.
"""

from rest_framework import status, permissions, serializers
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.parsers import <PERSON><PERSON>art<PERSON>ars<PERSON>, FormParser
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.exceptions import TokenError, InvalidToken
from django.contrib.auth import authenticate
from django.contrib.auth.models import User
from django.core.validators import validate_email
from django.core.exceptions import ValidationError as DjangoValidationError
from django.core.mail import send_mail
from django.conf import settings
from django.utils.crypto import get_random_string
from django.utils import timezone
from django.utils.decorators import method_decorator
from django.middleware.csrf import get_token
from django.views.decorators.csrf import ensure_csrf_cookie
# SECURITY FIX: Rate limiting imports enabled for security
from django_ratelimit.decorators import ratelimit
from django_ratelimit.exceptions import Ratelimited
from datetime import timedelta
import logging

from .models import Employee, Role, UserProfile, EmployeeActivation
from .serializers import UserSerializer, EmployeeSerializer, UserProfileSerializer
from .authentication import SecureTokenManager
from .password_validators import validate_employee_password, PasswordValidationError
from .auth_monitoring import AuthenticationMonitor, log_login_attempt, get_client_ip

logger = logging.getLogger(__name__)


class LoginSerializer(serializers.Serializer):
    """Serializer for login credentials with validation"""
    username = serializers.CharField(
        min_length=3,
        max_length=150,
        error_messages={
            'required': 'Username is required',
            'blank': 'Username cannot be empty',
            'min_length': 'Username must be at least 3 characters long',
            'max_length': 'Username cannot exceed 150 characters'
        }
    )
    password = serializers.CharField(
        min_length=4,
        max_length=128,
        error_messages={
            'required': 'Password is required',
            'blank': 'Password cannot be empty',
            'min_length': 'Password must be at least 4 characters long',
            'max_length': 'Password cannot exceed 128 characters'
        }
    )


# SECURITY FIX: Rate limiting enabled for login protection
@method_decorator(ratelimit(key='ip', rate='5/m', method='POST', block=True), name='post')
class CustomTokenObtainPairView(APIView):
    """
    Custom JWT token obtain view that returns user data along with tokens
    Rate limited to 5 attempts per minute per IP address
    """
    permission_classes = [permissions.AllowAny]  # Allow unauthenticated access

    def post(self, request, *args, **kwargs):
        try:
            # Parse request data manually to handle both DRF and raw requests
            import json

            # Try to get data from different sources
            data = None
            if hasattr(request, 'data') and request.data:
                data = request.data
            elif request.content_type == 'application/json':
                try:
                    data = json.loads(request.body.decode('utf-8'))
                except (json.JSONDecodeError, UnicodeDecodeError):
                    return Response({
                        'message': 'Invalid JSON data'
                    }, status=status.HTTP_400_BAD_REQUEST)
            else:
                data = request.POST

            if not data:
                return Response({
                    'message': 'No data provided'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Use serializer to validate input data
            serializer = LoginSerializer(data=data)
            if not serializer.is_valid():
                # Extract first error message for better UX
                first_error = None
                for field, errors in serializer.errors.items():
                    if errors:
                        first_error = errors[0] if isinstance(errors, list) else str(errors)
                        break

                return Response({
                    'message': first_error or 'Invalid input data',
                    'errors': serializer.errors,
                    'field_errors': serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)

            username = serializer.validated_data['username']
            password = serializer.validated_data['password']

            # Authenticate user
            user = authenticate(username=username, password=password)

            if not user:
                # Check if user exists to provide better error message
                from django.contrib.auth import get_user_model
                User = get_user_model()
                try:
                    existing_user = User.objects.get(username=username)
                    if not existing_user.is_active:
                        return Response({
                            'message': 'Your account has been deactivated. Please contact support.',
                            'error_code': 'ACCOUNT_DEACTIVATED'
                        }, status=status.HTTP_401_UNAUTHORIZED)
                    else:
                        # MONITORING: Log failed login attempt
                        log_login_attempt(
                            username=username,
                            request=request,
                            success=False,
                            error_code='INVALID_PASSWORD'
                        )
                        return Response({
                            'message': 'Invalid password. Please check your password and try again.',
                            'error_code': 'INVALID_PASSWORD'
                        }, status=status.HTTP_401_UNAUTHORIZED)
                except User.DoesNotExist:
                    # MONITORING: Log failed login attempt
                    log_login_attempt(
                        username=username,
                        request=request,
                        success=False,
                        error_code='USER_NOT_FOUND'
                    )
                    return Response({
                        'message': 'Username not found. Please check your username and try again.',
                        'error_code': 'USER_NOT_FOUND'
                    }, status=status.HTTP_401_UNAUTHORIZED)

            if not user.is_active:
                return Response({
                    'message': 'Your account has been deactivated. Please contact support.',
                    'error_code': 'ACCOUNT_DEACTIVATED'
                }, status=status.HTTP_401_UNAUTHORIZED)

            # Generate tokens
            refresh = RefreshToken.for_user(user)
            access_token = refresh.access_token

            # Get user profile data
            user_data = self._get_user_data(user)

            # Update last login
            user.last_login = timezone.now()
            user.save(update_fields=['last_login'])

            # SECURITY MIGRATION: httpOnly cookies only - no tokens in response body
            response_data = {
                'user': user_data,
                'message': 'Login successful'
            }

            response = Response(response_data, status=status.HTTP_200_OK)

            # SECURITY FIX: Set httpOnly cookies for tokens
            # Get JWT settings safely
            jwt_settings = getattr(settings, 'SIMPLE_JWT', {})
            access_lifetime = jwt_settings.get('ACCESS_TOKEN_LIFETIME', timedelta(minutes=15))
            refresh_lifetime = jwt_settings.get('REFRESH_TOKEN_LIFETIME', timedelta(days=7))

            # SECURITY FIX: Use SecureTokenManager for consistent cookie security
            SecureTokenManager.set_auth_cookies(response, str(access_token), str(refresh))

            # MONITORING: Log successful login
            log_login_attempt(
                username=username,
                request=request,
                success=True,
                user_id=user.id
            )

            return response

        # SECURITY FIX: Rate limiting exception handling enabled
        except Ratelimited:
            logger.warning(f"Rate limit exceeded for login from {request.META.get('REMOTE_ADDR')}")
            return Response({
                'message': 'Too many login attempts. Please try again later.',
                'error_code': 'RATE_LIMIT_EXCEEDED'
            }, status=status.HTTP_429_TOO_MANY_REQUESTS)
        except Exception as e:
            logger.error(f"Login error: {str(e)}")
            return Response({
                'message': 'An error occurred during login'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _get_user_data(self, user):
        """Get comprehensive user data including profile and role information"""
        try:
            # Try to get employee profile
            employee = Employee.objects.select_related(
                'user', 'department', 'manager'
            ).get(user=user)

            # Get user profile for role information
            try:
                user_profile = UserProfile.objects.select_related('role').get(user=user)
                if user_profile.role:
                    # Map role name to frontend role ID
                    role_id_map = {
                        'SUPERADMIN': 'super_admin',
                        'ADMIN': 'admin',
                        'HR_MANAGER': 'hr_manager',
                        'FINANCE_MANAGER': 'finance_manager',
                        'DEPARTMENT_MANAGER': 'department_manager',
                        'PROJECT_MANAGER': 'project_manager',
                        'EMPLOYEE': 'employee'
                    }

                    role_data = {
                        'id': role_id_map.get(user_profile.role.name, 'employee'),
                        'name': user_profile.role.name,  # FIXED: Use raw role name instead of get_name_display()
                        'nameAr': user_profile.role.name_ar,
                        'permissions': user_profile.role.permissions or [],
                        'level': user_profile.role.permissions.get('level', 5) if user_profile.role.permissions else 5,
                        'dashboardConfig': {
                            'allowedRoutes': self._get_allowed_routes(user_profile.role.name),
                            'defaultWidgets': self._get_default_widgets(user_profile.role.name),
                            'customizations': {}
                        }
                    }
                else:
                    # Default role if role is None
                    role_data = {
                        'id': 'employee',
                        'name': 'Employee',
                        'nameAr': 'موظف',
                        'permissions': [],
                        'level': 5,
                        'dashboardConfig': {
                            'allowedRoutes': ['/dashboard', '/profile'],
                            'defaultWidgets': ['tasks', 'schedule'],
                            'customizations': {}
                        }
                    }
            except UserProfile.DoesNotExist:
                # Default role if no profile exists
                role_data = {
                    'id': 'employee',
                    'name': 'Employee',
                    'nameAr': 'موظف',
                    'permissions': [],
                    'dashboardConfig': {
                        'allowedRoutes': ['/dashboard', '/profile'],
                        'defaultWidgets': ['tasks', 'schedule'],
                        'customizations': {}
                    }
                }

            return {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'role': role_data,
                'profile': {
                    'avatar': None,  # Add avatar field to Employee model if needed
                    'phone': employee.phone,
                    'department': employee.department.name if employee.department else None,
                    'position': employee.position,
                    'preferred_language': 'ar',  # Default to Arabic
                    'timezone': 'Asia/Riyadh'
                }
            }

        except Employee.DoesNotExist:
            # User exists but no employee profile - check for UserProfile (for ADMIN, SUPERADMIN, etc.)
            try:
                user_profile = UserProfile.objects.select_related('role').get(user=user)
                if user_profile.role:
                    # Map role name to frontend role ID
                    role_id_map = {
                        'SUPERADMIN': 'super_admin',
                        'ADMIN': 'admin',
                        'HR_MANAGER': 'hr_manager',
                        'FINANCE_MANAGER': 'finance_manager',
                        'DEPARTMENT_MANAGER': 'department_manager',
                        'PROJECT_MANAGER': 'project_manager',
                        'EMPLOYEE': 'employee'
                    }

                    role_data = {
                        'id': role_id_map.get(user_profile.role.name, 'employee'),
                        'name': user_profile.role.name,  # FIXED: Use raw role name instead of get_name_display()
                        'nameAr': user_profile.role.name_ar,
                        'permissions': user_profile.role.permissions or [],
                        'level': user_profile.role.permissions.get('level', 5) if user_profile.role.permissions else 5,
                        'dashboardConfig': {
                            'allowedRoutes': self._get_allowed_routes(user_profile.role.name),
                            'defaultWidgets': self._get_default_widgets(user_profile.role.name),
                            'customizations': {}
                        }
                    }

                    return {
                        'id': user.id,
                        'username': user.username,
                        'email': user.email,
                        'first_name': user.first_name,
                        'last_name': user.last_name,
                        'role': role_data,
                        'profile': {
                            'avatar': None,
                            'phone': user_profile.phone,
                            'department': None,  # UserProfile doesn't have department
                            'position': None,    # UserProfile doesn't have position
                            'preferred_language': user_profile.preferred_language,
                            'timezone': user_profile.timezone
                        }
                    }

            except UserProfile.DoesNotExist:
                pass

            # Fallback for users with no profile at all
            return {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'role': {
                    'id': 'user',
                    'name': 'User',
                    'nameAr': 'مستخدم',
                    'permissions': [],
                    'dashboardConfig': {
                        'allowedRoutes': ['/profile'],
                        'defaultWidgets': [],
                        'customizations': {}
                    }
                },
                'profile': {
                    'avatar': None,
                    'phone': None,
                    'department': None,
                    'position': None,
                    'preferred_language': 'ar',
                    'timezone': 'Asia/Riyadh'
                }
            }

    def _get_allowed_routes(self, role_name):
        """Get allowed routes based on role"""
        route_map = {
            'SUPERADMIN': ['/admin/*', '/hr/*', '/finance/*', '/department/*', '/projects/*', '/employee/*'],
            'ADMIN': ['/admin/*'],
            'HR_MANAGER': ['/hr/*'],
            'FINANCE_MANAGER': ['/finance/*'],
            'DEPARTMENT_MANAGER': ['/department/*'],
            'PROJECT_MANAGER': ['/projects/*'],
            'EMPLOYEE': ['/employee/*']
        }
        return route_map.get(role_name, ['/employee/*'])

    def _get_default_widgets(self, role_name):
        """Get default widgets based on role"""
        widget_map = {
            'SUPERADMIN': ['system_overview', 'user_analytics', 'performance_metrics', 'security_dashboard', 'compliance_overview', 'ai_insights'],
            'ADMIN': ['system_overview', 'user_analytics', 'performance_metrics'],
            'HR_MANAGER': ['employee_stats', 'leave_requests', 'attendance_summary'],
            'FINANCE_MANAGER': ['budget_overview', 'expense_tracking', 'financial_reports'],
            'DEPARTMENT_MANAGER': ['team_overview', 'project_progress', 'department_metrics'],
            'PROJECT_MANAGER': ['project_status', 'task_overview', 'team_performance'],
            'EMPLOYEE': ['my_tasks', 'my_schedule', 'my_attendance']
        }
        return widget_map.get(role_name, ['my_tasks', 'my_schedule'])


# SECURITY FIX: Rate limiting enabled for token refresh protection
@method_decorator(ratelimit(key='ip', rate='10/m', method='POST', block=True), name='post')
class CustomTokenRefreshView(APIView):
    """
    SECURITY FIX: Custom token refresh view using httpOnly cookies
    Rate limited to 10 attempts per minute per IP address
    """
    permission_classes = [permissions.AllowAny]  # Allow unauthenticated access

    def post(self, request, *args, **kwargs):
        try:
            logger.info(f"Auth attempt from {request.META.get('REMOTE_ADDR', 'unknown')} to /api/auth/refresh/")

            # SECURITY FIX: Get refresh token from httpOnly cookie
            refresh_token = request.COOKIES.get('refresh_token')

            if not refresh_token:
                logger.warning("Unauthorized: /api/auth/refresh/")
                return Response({
                    'message': 'Refresh token not found'
                }, status=status.HTTP_401_UNAUTHORIZED)

            try:
                # Validate and refresh the token
                refresh = RefreshToken(refresh_token)
                access_token = refresh.access_token

                # SECURITY FIX: Create response with new httpOnly cookies
                response = Response({
                    'message': 'Token refreshed successfully'
                }, status=status.HTTP_200_OK)

                # Set new access token cookie
                jwt_settings = getattr(settings, 'SIMPLE_JWT', {})
                access_lifetime = jwt_settings.get('ACCESS_TOKEN_LIFETIME', timedelta(minutes=15))

                response.set_cookie(
                    'access_token',
                    str(access_token),
                    max_age=int(access_lifetime.total_seconds()),
                    httponly=True,
                    secure=False,  # Allow HTTP in development
                    samesite='Lax',  # Allow cross-origin in development
                    domain=None  # Allow all domains in development
                )

                logger.info("Token refreshed successfully")
                return response

            except TokenError as e:
                logger.warning(f"Unauthorized: /api/auth/refresh/ - {str(e)}")
                return Response({
                    'message': 'Invalid or expired refresh token'
                }, status=status.HTTP_401_UNAUTHORIZED)

        except Exception as e:
            logger.error(f"Token refresh error: {str(e)}")
            return Response({
                'message': 'An error occurred during token refresh'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class LogoutView(APIView):
    """
    SECURITY FIX: Logout view that clears httpOnly cookies and blacklists tokens
    """
    permission_classes = [permissions.AllowAny]  # Allow logout even if token is invalid

    def post(self, request):
        try:
            logger.info("Authentication cookies cleared")

            # SECURITY FIX: Get refresh token from httpOnly cookie
            refresh_token = request.COOKIES.get('refresh_token')

            if refresh_token:
                try:
                    token = RefreshToken(refresh_token)
                    # FIXED: Manual blacklisting for logout (since auto-blacklisting is disabled)
                    try:
                        token.blacklist()
                        logger.info("Refresh token blacklisted successfully")
                    except Exception as blacklist_error:
                        # Blacklisting may fail if token is already invalid - that's OK
                        logger.info(f"Token blacklist skipped: {str(blacklist_error)}")
                except Exception as token_error:
                    # Token parsing may fail if token is already invalid - that's OK
                    logger.info(f"Token parsing failed during logout: {str(token_error)}")

            # SECURITY FIX: Create response that clears httpOnly cookies
            response = Response({
                'message': 'Successfully logged out'
            }, status=status.HTTP_200_OK)

            # SECURITY FIX: Use SecureTokenManager for cookie cleanup
            response = SecureTokenManager.clear_auth_cookies(response)

            # MONITORING: Log logout
            if hasattr(request, 'user') and request.user.is_authenticated:
                AuthenticationMonitor.log_logout(
                    user_id=request.user.id,
                    username=request.user.username,
                    ip_address=get_client_ip(request),
                    method='manual'
                )

            return response

        except Exception as e:
            logger.error(f"Logout error: {str(e)}")
            logger.info("Authentication cookies cleared")
            # Even on error, clear cookies
            response = Response({
                'message': 'Logged out (with errors)'
            }, status=status.HTTP_200_OK)

            response.delete_cookie('access_token', samesite='Lax')
            response.delete_cookie('refresh_token', samesite='Lax')

            return response


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def auth_metrics_view(request):
    """
    Get authentication metrics for monitoring
    Requires admin privileges
    """
    try:
        # Check if user has admin privileges
        if not (request.user.is_staff or request.user.is_superuser):
            return Response({
                'message': 'Admin privileges required'
            }, status=status.HTTP_403_FORBIDDEN)

        # Get metrics for the last 24 hours
        hours = int(request.GET.get('hours', 24))
        metrics = AuthenticationMonitor.get_authentication_metrics(hours)

        return Response(metrics, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Auth metrics error: {str(e)}")
        return Response({
            'message': 'An error occurred while fetching authentication metrics'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def user_profile_view(request):
    """
    Get current authenticated user's profile
    """
    try:
        logger.info(f"User profile request from {request.user.username}")

        # Use the same method as login to get user data
        token_view = CustomTokenObtainPairView()
        user_data = token_view._get_user_data(request.user)

        return Response(user_data, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"User profile error: {str(e)}")
        logger.warning(f"Unauthorized: /api/auth/user/")
        return Response({
            'message': 'An error occurred while fetching user profile'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['PATCH'])
@permission_classes([permissions.IsAuthenticated])
def update_profile_view(request):
    """
    Update current user's profile
    """
    try:
        user = request.user
        data = request.data

        # Update User model fields
        if 'first_name' in data:
            user.first_name = data['first_name']
        if 'last_name' in data:
            user.last_name = data['last_name']
        if 'email' in data:
            user.email = data['email']

        user.save()

        # Update Employee model fields if exists
        try:
            employee = Employee.objects.get(user=user)
            if 'phone' in data.get('profile', {}):
                employee.phone = data['profile']['phone']
            employee.save()
        except Employee.DoesNotExist:
            pass

        # Return updated user data
        token_view = CustomTokenObtainPairView()
        user_data = token_view._get_user_data(user)

        return Response(user_data, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Profile update error: {str(e)}")
        return Response({
            'message': 'An error occurred while updating profile'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
# TEMPORARILY DISABLED: Rate limiting for development
# @ratelimit(key='user', rate='3/m', method='POST', block=True)
def change_password_view(request):
    """
    Change user password
    """
    try:
        user = request.user
        current_password = request.data.get('current_password')
        new_password = request.data.get('new_password')

        if not current_password or not new_password:
            return Response({
                'message': 'Current password and new password are required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Verify current password
        if not user.check_password(current_password):
            return Response({
                'message': 'Current password is incorrect'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Set new password
        user.set_password(new_password)
        user.save()

        return Response({
            'message': 'Password changed successfully'
        }, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Password change error: {str(e)}")
        return Response({
            'message': 'An error occurred while changing password'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# SECURITY FIX: CSRF Token Endpoint
@api_view(['GET'])
@permission_classes([permissions.AllowAny])
@ensure_csrf_cookie
def csrf_token_view(request):
    """
    SECURITY FIX: Provide CSRF token for frontend SPA
    This endpoint ensures the frontend can get a CSRF token
    """
    try:
        # Get CSRF token for the request
        csrf_token = get_token(request)

        return Response({
            'csrfToken': csrf_token,
            'message': 'CSRF token provided'
        }, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"CSRF token error: {str(e)}")
        return Response({
            'message': 'Failed to get CSRF token'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# Employee Activation Views
@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def employee_activation_view(request, token):
    """
    Employee account activation endpoint
    Allows employees to activate their accounts using the token sent via email
    """
    try:
        # Find the activation record
        activation = EmployeeActivation.objects.select_related('employee__user').get(
            activation_token=token
        )

        # Check if already activated
        if activation.is_activated:
            return Response({
                'message': 'Account is already activated',
                'employee': {
                    'name': activation.employee.user.get_full_name(),
                    'email': activation.employee.user.email,
                    'position': activation.employee.position
                }
            }, status=status.HTTP_200_OK)

        # Check if token is expired
        if activation.is_expired():
            return Response({
                'message': 'Activation link has expired. Please contact your administrator.',
                'expired': True
            }, status=status.HTTP_400_BAD_REQUEST)

        # Return employee info for password setup
        return Response({
            'message': 'Activation link is valid',
            'employee': {
                'name': activation.employee.user.get_full_name(),
                'email': activation.employee.user.email,
                'position': activation.employee.position,
                'department': activation.employee.department.name if activation.employee.department else None
            },
            'token': str(activation.activation_token)
        }, status=status.HTTP_200_OK)

    except EmployeeActivation.DoesNotExist:
        return Response({
            'message': 'Invalid activation link',
            'invalid': True
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.error(f"Activation view error: {str(e)}")
        return Response({
            'message': 'An error occurred while processing activation'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
# TEMPORARILY DISABLED: Rate limiting for development
# @ratelimit(key='ip', rate='5/m', method='POST', block=True)
def employee_activate_account_view(request, token):
    """
    Complete employee account activation with password setup
    """
    try:
        # Find the activation record
        activation = EmployeeActivation.objects.select_related('employee__user').get(
            activation_token=token
        )

        # Check if already activated
        if activation.is_activated:
            return Response({
                'message': 'Account is already activated'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Check if token is expired
        if activation.is_expired():
            return Response({
                'message': 'Activation link has expired. Please contact your administrator.'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Get password from request
        password = request.data.get('password')
        confirm_password = request.data.get('confirm_password')

        if not password or not confirm_password:
            return Response({
                'message': 'Password and confirmation are required'
            }, status=status.HTTP_400_BAD_REQUEST)

        if password != confirm_password:
            return Response({
                'message': 'Passwords do not match'
            }, status=status.HTTP_400_BAD_REQUEST)

        # SECURITY FIX: Enhanced password validation
        try:
            user = activation.employee.user
            validation_results = validate_employee_password(password, user)

            # Log successful validation
            logger.info(f"Password validation successful for user {user.username}: strength={validation_results['strength_level']}")

        except PasswordValidationError as e:
            logger.warning(f"Password validation failed for user {activation.employee.user.username}: {e.validation_results['errors']}")
            return Response({
                'message': 'Password does not meet security requirements',
                'errors': e.validation_results['errors'],
                'feedback': e.validation_results.get('feedback', []),
                'strength_score': e.validation_results.get('strength_score', 0)
            }, status=status.HTTP_400_BAD_REQUEST)

        # Set the password and activate the account
        user.set_password(password)
        user.is_active = True
        user.save()

        # Mark activation as complete
        activation.activate()

        return Response({
            'message': 'Account activated successfully! You can now log in.',
            'employee': {
                'name': user.get_full_name(),
                'email': user.email,
                'username': user.username
            }
        }, status=status.HTTP_200_OK)

    except EmployeeActivation.DoesNotExist:
        return Response({
            'message': 'Invalid activation link'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.error(f"Account activation error: {str(e)}")
        return Response({
            'message': 'An error occurred while activating account'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def validate_password_strength_view(request):
    """
    API endpoint to validate password strength in real-time
    Used by frontend to provide immediate feedback during password entry
    """
    try:
        password = request.data.get('password', '')

        if not password:
            return Response({
                'message': 'Password is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Validate password strength
        validation_results = validate_employee_password(password)

        return Response({
            'is_valid': validation_results['is_valid'],
            'strength_score': validation_results['strength_score'],
            'strength_level': validation_results['strength_level'],
            'feedback': validation_results['feedback'],
            'warnings': validation_results.get('warnings', [])
        })

    except PasswordValidationError as e:
        return Response({
            'is_valid': False,
            'errors': e.validation_results['errors'],
            'strength_score': e.validation_results['strength_score'],
            'strength_level': e.validation_results['strength_level'],
            'feedback': e.validation_results['feedback']
        })
    except Exception as e:
        logger.error(f"Error in password validation endpoint: {str(e)}")
        return Response({
            'message': 'An error occurred while validating password'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET', 'POST'])
@permission_classes([IsAuthenticated])
def activation_cleanup_view(request):
    """
    Admin endpoint for managing activation token cleanup
    GET: Preview what would be cleaned up
    POST: Execute cleanup operation
    """
    from .cleanup_service import cleanup_service
    from .permissions import AdminPermission

    # Check admin permissions
    if not AdminPermission().has_permission(request, None):
        return Response({
            'message': 'Admin permissions required'
        }, status=status.HTTP_403_FORBIDDEN)

    try:
        if request.method == 'GET':
            # Preview cleanup statistics
            stats = cleanup_service.get_cleanup_statistics()
            return Response({
                'preview': stats,
                'message': 'Cleanup preview generated successfully'
            })

        elif request.method == 'POST':
            # Execute cleanup
            dry_run = request.data.get('dry_run', False)
            cleanup_orphaned = request.data.get('cleanup_orphaned', True)
            expiry_days = request.data.get('expiry_days', 30)

            results = cleanup_service.run_cleanup(
                expiry_days=expiry_days,
                cleanup_orphaned=cleanup_orphaned,
                dry_run=dry_run
            )

            return Response({
                'results': results,
                'message': 'Cleanup completed successfully' if not dry_run else 'Cleanup preview completed'
            })

    except Exception as e:
        logger.error(f"Error in activation cleanup endpoint: {str(e)}")
        return Response({
            'message': 'An error occurred during cleanup operation'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def registration_analytics_view(request):
    """
    HR/Admin endpoint for registration analytics and insights
    """
    from .registration_analytics import registration_analytics
    from .permissions import HRManagerPermission

    # Check HR permissions
    if not HRManagerPermission().has_permission(request, None):
        return Response({
            'message': 'HR permissions required'
        }, status=status.HTTP_403_FORBIDDEN)

    try:
        days = int(request.GET.get('days', 30))

        # Get comprehensive analytics
        overview = registration_analytics.get_registration_overview(days)
        pending_details = registration_analytics.get_pending_approvals_details()
        trends = registration_analytics.get_registration_trends(days)
        department_breakdown = registration_analytics.get_department_breakdown()

        return Response({
            'overview': overview,
            'pending_approvals': pending_details,
            'trends': trends,
            'department_breakdown': department_breakdown,
            'generated_at': timezone.now().isoformat()
        })

    except Exception as e:
        logger.error(f"Error in registration analytics endpoint: {str(e)}")
        return Response({
            'message': 'An error occurred while generating analytics'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def approve_employee_activation_view(request, employee_id):
    """
    HR/Admin endpoint to approve employee activation
    """
    from .permissions import HRManagerPermission

    # Check HR permissions
    if not HRManagerPermission().has_permission(request, None):
        return Response({
            'message': 'HR permissions required'
        }, status=status.HTTP_403_FORBIDDEN)

    try:
        # Get the employee and activation record
        employee = Employee.objects.select_related('user', 'activation').get(id=employee_id)
        activation = employee.activation

        if activation.approval_status != 'PENDING':
            return Response({
                'message': f'Employee activation is already {activation.approval_status.lower()}'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Get the approving employee
        approving_employee = Employee.objects.get(user=request.user)

        # Approve the activation
        activation.approve(approving_employee)

        logger.info(f"Employee {employee.employee_id} approved by {approving_employee.employee_id}")

        return Response({
            'message': 'Employee approved successfully. Activation email has been sent.',
            'employee': {
                'id': employee.id,
                'employee_id': employee.employee_id,
                'name': employee.user.get_full_name(),
                'email': employee.user.email,
                'approved_by': approving_employee.user.get_full_name(),
                'approved_at': activation.approved_at.isoformat()
            }
        })

    except Employee.DoesNotExist:
        return Response({
            'message': 'Employee not found'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.error(f"Error approving employee {employee_id}: {str(e)}")
        return Response({
            'message': 'An error occurred while approving employee'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@parser_classes([MultiPartParser, FormParser])
def bulk_import_employees_view(request):
    """
    HR/Admin endpoint for bulk importing employees from CSV/Excel files
    """
    import logging
    logger = logging.getLogger(__name__)
    logger.info(f"Bulk import view reached by user: {request.user.username}")

    from .bulk_import_service import BulkEmployeeImporter
    from .permissions import HRManagerPermission

    # Check HR permissions
    if not HRManagerPermission().has_permission(request, None):
        logger.warning(f"User {request.user.username} denied HR permission for bulk import")
        return Response({
            'message': 'HR permissions required'
        }, status=status.HTTP_403_FORBIDDEN)

    logger.info(f"User {request.user.username} has HR permission for bulk import")

    try:
        # Check if file is provided
        if 'file' not in request.FILES:
            return Response({
                'message': 'No file provided'
            }, status=status.HTTP_400_BAD_REQUEST)

        file = request.FILES['file']

        # Determine file type
        file_extension = file.name.split('.')[-1].lower()
        if file_extension == 'csv':
            file_type = 'csv'
        elif file_extension in ['xlsx', 'xls']:
            file_type = 'excel'
        else:
            return Response({
                'message': 'Unsupported file type. Please upload CSV or Excel files.'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Validate file size (max 10MB)
        if file.size > 10 * 1024 * 1024:
            return Response({
                'message': 'File too large. Maximum size is 10MB.'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Import employees
        importer = BulkEmployeeImporter(request.user)
        results = importer.import_from_file(file, file_type)

        # Log the import operation
        logger.info(f"Bulk import completed by {request.user.username}: {results['successful_imports']} successful, {results['failed_imports']} failed")

        # Determine response status
        if results['failed_imports'] > 0 and results['successful_imports'] == 0:
            status_code = status.HTTP_400_BAD_REQUEST
        elif results['failed_imports'] > 0:
            status_code = status.HTTP_207_MULTI_STATUS  # Partial success
        else:
            status_code = status.HTTP_200_OK

        return Response({
            'message': f"Import completed: {results['successful_imports']} successful, {results['failed_imports']} failed",
            'results': results
        }, status=status_code)

    except Exception as e:
        logger.error(f"Error in bulk import endpoint: {str(e)}")
        return Response({
            'message': 'An error occurred during bulk import'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def password_reset_request_view(request):
    """
    Request password reset - sends email with reset link
    """
    try:
        email = request.data.get('email')

        if not email:
            return Response({
                'message': 'Email address is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Validate email format
        from django.core.validators import validate_email
        from django.core.exceptions import ValidationError

        try:
            validate_email(email)
        except ValidationError:
            return Response({
                'message': 'Please enter a valid email address'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Find user by email
        from django.contrib.auth import get_user_model
        User = get_user_model()

        try:
            user = User.objects.get(email=email, is_active=True)
        except User.DoesNotExist:
            # For security, don't reveal if email exists or not
            return Response({
                'message': 'If an account with this email exists, a password reset link has been sent.'
            }, status=status.HTTP_200_OK)

        # Generate password reset token
        import uuid
        from django.utils import timezone
        from datetime import timedelta

        # Create or update password reset record
        from .models import PasswordReset

        # Delete any existing reset tokens for this user
        PasswordReset.objects.filter(user=user).delete()

        # Create new reset token
        reset_token = str(uuid.uuid4())
        expires_at = timezone.now() + timedelta(hours=24)  # 24 hour expiry

        PasswordReset.objects.create(
            user=user,
            token=reset_token,
            expires_at=expires_at
        )

        # Send password reset email
        from .email_service import EmailService
        email_sent = EmailService.send_password_reset_email(user, reset_token)

        if email_sent:
            logger.info(f"Password reset email sent to {email}")
        else:
            logger.error(f"Failed to send password reset email to {email}")

        # Always return success for security
        return Response({
            'message': 'If an account with this email exists, a password reset link has been sent.'
        }, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Password reset request error: {str(e)}")
        return Response({
            'message': 'An error occurred while processing your request'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def password_reset_confirm_view(request):
    """
    Confirm password reset with token and set new password
    """
    try:
        token = request.data.get('token')
        new_password = request.data.get('password')

        if not token or not new_password:
            return Response({
                'message': 'Token and new password are required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Validate password strength
        if len(new_password) < 8:
            return Response({
                'message': 'Password must be at least 8 characters long'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Find the password reset record
        from .models import PasswordReset

        try:
            reset_record = PasswordReset.objects.select_related('user').get(token=token)
        except PasswordReset.DoesNotExist:
            return Response({
                'message': 'Invalid or expired reset token'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Check if token is valid
        if not reset_record.is_valid():
            return Response({
                'message': 'Invalid or expired reset token'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Set new password
        user = reset_record.user
        user.set_password(new_password)
        user.save()

        # Mark token as used
        reset_record.use_token()

        # Delete all other reset tokens for this user
        PasswordReset.objects.filter(user=user).exclude(id=reset_record.id).delete()

        logger.info(f"Password reset completed for user {user.email}")

        return Response({
            'message': 'Password has been reset successfully. You can now log in with your new password.'
        }, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Password reset confirm error: {str(e)}")
        return Response({
            'message': 'An error occurred while resetting your password'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


