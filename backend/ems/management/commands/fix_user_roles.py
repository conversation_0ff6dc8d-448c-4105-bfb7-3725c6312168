from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from ems.models import Employee, UserProfile, Role, Department
from datetime import date

class Command(BaseCommand):
    help = 'Fix user roles and create missing profiles'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('=== FIXING USER ROLES ==='))
        
        # Get or create departments
        admin_dept, _ = Department.objects.get_or_create(
            name='Administration',
            defaults={
                'name_ar': 'الإدارة',
                'description': 'Administrative department',
                'description_ar': 'قسم الإدارة'
            }
        )
        
        finance_dept, _ = Department.objects.get_or_create(
            name='Finance',
            defaults={
                'name_ar': 'المالية',
                'description': 'Finance department',
                'description_ar': 'قسم المالية'
            }
        )
        
        # Get roles
        try:
            superadmin_role = Role.objects.get(name='SUPERADMIN')
            admin_role = Role.objects.get(name='ADMIN')
            finance_role = Role.objects.get(name='FINANCE_MANAGER')
        except Role.DoesNotExist as e:
            self.stdout.write(self.style.ERROR(f'Role not found: {e}'))
            return
        
        # Fix users without Employee records
        users_without_employee = User.objects.filter(employee__isnull=True, is_active=True)
        self.stdout.write(f'Found {users_without_employee.count()} users without Employee records')

        for user in users_without_employee:
            self.stdout.write(f'Creating Employee record for: {user.username} ({user.get_full_name()})')

            # Determine department based on username/role
            try:
                profile = user.userprofile
                role_name = profile.role.name if profile.role else None

                if role_name == 'HR_MANAGER' or 'hr' in user.username.lower():
                    department = hr_dept
                elif role_name == 'FINANCE_MANAGER' or 'finance' in user.username.lower():
                    department = finance_dept
                elif role_name in ['ADMIN', 'SUPERADMIN'] or 'admin' in user.username.lower():
                    department = admin_dept
                else:
                    department = admin_dept  # Default department

                # Determine position based on role
                if role_name == 'SUPERADMIN':
                    position = 'Super Administrator'
                    position_ar = 'مدير عام'
                elif role_name == 'ADMIN':
                    position = 'Administrator'
                    position_ar = 'مدير نظام'
                elif role_name == 'HR_MANAGER':
                    position = 'HR Manager'
                    position_ar = 'مدير الموارد البشرية'
                elif role_name == 'FINANCE_MANAGER':
                    position = 'Finance Manager'
                    position_ar = 'مدير المالية'
                elif 'manager' in user.username.lower():
                    position = 'Manager'
                    position_ar = 'مدير'
                else:
                    position = 'Employee'
                    position_ar = 'موظف'

            except UserProfile.DoesNotExist:
                self.stdout.write(f'  Warning: No UserProfile found for {user.username}')
                department = admin_dept
                position = 'Employee'
                position_ar = 'موظف'

            employee = Employee.objects.create(
                user=user,
                employee_id=f'EMP{user.id:04d}',
                department=department,
                position=position,
                position_ar=position_ar,
                first_name_ar=user.first_name,  # Use English name as fallback
                last_name_ar=user.last_name,
                hire_date=user.date_joined.date() if user.date_joined else date.today(),
                phone='+966500000000',
                gender='M',  # Default gender
                employment_status='FULL_TIME',
                work_location='Riyadh, Saudi Arabia',
                is_active=True
            )
            self.stdout.write(f'  ✓ Created Employee ID: {employee.employee_id} for {user.username}')
        
        # Fix users without UserProfile
        users_without_profile = User.objects.filter(userprofile__isnull=True)
        for user in users_without_profile:
            self.stdout.write(f'Creating UserProfile for: {user.username}')
            
            # Determine role based on username
            if user.is_superuser or 'superadmin' in user.username.lower():
                role = superadmin_role
            elif 'finance' in user.username.lower():
                role = finance_role
            elif user.is_staff:
                role = admin_role
            else:
                role = None
            
            user_profile = UserProfile.objects.create(
                user=user,
                role=role,
                preferred_language='ar'
            )
            self.stdout.write(f'  Created UserProfile with role: {role.name if role else "None"}')
        
        # Fix users with empty permissions
        self.stdout.write('\n--- Fixing Empty Permissions ---')
        user_profiles = UserProfile.objects.select_related('role').all()
        
        for profile in user_profiles:
            if profile.role and not profile.role.permissions:
                self.stdout.write(f'Fixing permissions for role: {profile.role.name}')
                
                if profile.role.name == 'SUPERADMIN':
                    profile.role.permissions = {
                        'level': 1,
                        'system_admin': True,
                        'full_access': True,
                        'all_permissions': True
                    }
                elif profile.role.name == 'ADMIN':
                    profile.role.permissions = {
                        'level': 2,
                        'system_admin': True,
                        'manage_users': True,
                        'view_all_data': True,
                        'system_configuration': True
                    }
                elif profile.role.name == 'FINANCE_MANAGER':
                    profile.role.permissions = {
                        'level': 3,
                        'financial_access': True,
                        'budget_management': True,
                        'expense_management': True,
                        'financial_reports': True,
                        'export_financial_data': True,
                        'security_access': True,
                        'compliance_access': True,
                        'analytics_access': True,
                        'integration_access': True
                    }
                elif profile.role.name == 'HR_MANAGER':
                    profile.role.permissions = {
                        'level': 3,
                        'hr_management': True,
                        'view_all_employees': True,
                        'manage_employees': True,
                        'manage_leave_requests': True,
                        'view_attendance': True,
                        'performance_management': True
                    }
                
                profile.role.save()
                self.stdout.write(f'  Updated permissions for {profile.role.name}')
        
        # Create security profiles for users who don't have them
        from ems.models import UserSecurityProfile
        
        employees_without_security = Employee.objects.filter(security_profile__isnull=True)
        for employee in employees_without_security:
            self.stdout.write(f'Creating security profile for: {employee.user.username}')
            
            # Determine security level based on role
            security_level = 'MEDIUM'
            try:
                user_profile = UserProfile.objects.get(user=employee.user)
                if user_profile.role:
                    if user_profile.role.name in ['SUPERADMIN', 'ADMIN']:
                        security_level = 'CRITICAL'
                    elif user_profile.role.name in ['FINANCE_MANAGER', 'HR_MANAGER']:
                        security_level = 'HIGH'
                    else:
                        security_level = 'MEDIUM'
            except UserProfile.DoesNotExist:
                pass
            
            UserSecurityProfile.objects.create(
                employee=employee,
                security_level=security_level,
                mfa_enabled=False,
                mfa_method='NONE',
                max_concurrent_sessions=3 if security_level in ['HIGH', 'CRITICAL'] else 2,
                login_notifications=True,
                suspicious_activity_alerts=True
            )
            self.stdout.write(f'  Created security profile with level: {security_level}')
        
        self.stdout.write(self.style.SUCCESS('\n=== ROLE FIXES COMPLETE ==='))
        
        # Show final status
        self.stdout.write('\n--- Final Status ---')
        total_users = User.objects.count()
        users_with_profiles = User.objects.filter(userprofile__isnull=False).count()
        users_with_employees = User.objects.filter(employee__isnull=False).count()
        
        self.stdout.write(f'Total Users: {total_users}')
        self.stdout.write(f'Users with UserProfile: {users_with_profiles}')
        self.stdout.write(f'Users with Employee records: {users_with_employees}')
        
        if total_users == users_with_profiles == users_with_employees:
            self.stdout.write(self.style.SUCCESS('✅ All users have complete profiles!'))
        else:
            self.stdout.write(self.style.WARNING('⚠️  Some users still missing profiles'))
