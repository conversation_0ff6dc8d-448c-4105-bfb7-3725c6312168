"""
API Response Standardization Middleware
Ensures consistent response formats across all API endpoints
"""

import json
from django.http import JsonResponse
from django.utils.deprecation import MiddlewareMixin
from rest_framework.response import Response

class APIResponseStandardizerMiddleware(MiddlewareMixin):
    """Standardize API responses across all endpoints"""
    
    def process_response(self, request, response):
        """Standardize API response format"""
        
        # Only process API requests
        if not request.path.startswith('/api/'):
            return response
        
        # Skip if not JSON response
        if not isinstance(response, (JsonResponse, Response)):
            return response
        
        # Add API version header
        if request.path.startswith('/api/v1/'):
            response['X-API-Version'] = 'v1'
        else:
            response['X-API-Version'] = 'legacy'
            # Add deprecation warning for legacy endpoints
            response['X-API-Deprecated'] = 'true'
            response['X-API-Migration-Info'] = 'Please migrate to /api/v1/ endpoints'
        
        # Add standard headers
        response['X-API-Response-Time'] = getattr(request, '_api_start_time', 'unknown')
        response['X-Content-Type-Options'] = 'nosniff'
        
        return response
