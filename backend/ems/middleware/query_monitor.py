"""
Query Monitoring Middleware for EMS
Monitors database query performance and logs slow queries
"""

import time
import logging
from django.db import connection
from django.utils.deprecation import MiddlewareMixin
from django.conf import settings

logger = logging.getLogger(__name__)

class QueryMonitoringMiddleware(MiddlewareMixin):
    """Monitor database queries for performance issues"""
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.slow_query_threshold = getattr(settings, 'SLOW_QUERY_THRESHOLD', 0.5)  # 500ms
        
    def process_request(self, request):
        """Reset query count at start of request"""
        self.start_time = time.time()
        self.start_queries = len(connection.queries) if settings.DEBUG else 0
        
    def process_response(self, request, response):
        """Log query performance after request"""
        if settings.DEBUG:
            end_time = time.time()
            total_time = end_time - self.start_time
            query_count = len(connection.queries) - self.start_queries
            
            # Log slow requests
            if total_time > self.slow_query_threshold:
                logger.warning(
                    f"Slow request: {request.path} took {total_time:.3f}s "
                    f"with {query_count} queries"
                )
                
                # Log individual slow queries
                for query in connection.queries[self.start_queries:]:
                    query_time = float(query['time'])
                    if query_time > 0.1:  # 100ms threshold for individual queries
                        logger.warning(
                            f"Slow query ({query_time:.3f}s): {query['sql'][:200]}..."
                        )
            
            # Add performance headers for debugging
            response['X-DB-Query-Count'] = str(query_count)
            response['X-DB-Query-Time'] = f"{total_time:.3f}"
            
        return response
