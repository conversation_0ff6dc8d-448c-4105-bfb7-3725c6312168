"""
Security middleware for EMS application
"""
from django.http import JsonResponse
from django_ratelimit.exceptions import Ratelimited
import logging
import re

logger = logging.getLogger(__name__)


class RateLimitMiddleware:
    """
    Middleware to handle rate limiting exceptions and return JSON responses
    """
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        return self.get_response(request)

    def process_exception(self, request, exception):
        if isinstance(exception, Ratelimited):
            logger.warning(f"Rate limit exceeded for {request.META.get('REMOTE_ADDR')} on {request.path}")
            return JsonResponse({
                'error': 'Rate limit exceeded',
                'message': 'Too many requests. Please try again later.',
                'status_code': 429
            }, status=429)
        return None


class SecurityHeadersMiddleware:
    """
    Middleware to add security headers to all responses
    """
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)
        
        # Add security headers
        response['X-Content-Type-Options'] = 'nosniff'
        response['X-Frame-Options'] = 'DENY'
        response['X-XSS-Protection'] = '1; mode=block'
        response['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        response['Permissions-Policy'] = 'geolocation=(), microphone=(), camera=()'
        
        # Add CSP header for API responses
        if request.path.startswith('/api/'):
            response['Content-Security-Policy'] = "default-src 'self'"
        
        return response


class RequestLoggingMiddleware:
    """
    Middleware to log API requests for security monitoring
    """
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Log authentication attempts
        if request.path in ['/api/auth/login/', '/api/auth/refresh/']:
            logger.info(f"Auth attempt from {request.META.get('REMOTE_ADDR')} to {request.path}")

        response = self.get_response(request)

        # Log failed authentication attempts
        if request.path == '/api/auth/login/' and response.status_code in [400, 401]:
            logger.warning(f"Failed login attempt from {request.META.get('REMOTE_ADDR')}")

        return response


class InputValidationMiddleware:
    """
    Middleware to validate input for potential security threats
    """
    def __init__(self, get_response):
        self.get_response = get_response
        
        # Compile patterns for better performance
        self.compiled_patterns = [
            re.compile(pattern, re.IGNORECASE) for pattern in [
                # SQL Injection patterns
                r'union\s+select',
                r'drop\s+table',
                r'delete\s+from',
                r'insert\s+into',
                r'update\s+.*set',
                r'alter\s+table',
                r'create\s+table',
                r'exec\s*\(',
                r'sp_\w+',
                r'xp_\w+',
                r'--\s*$',
                r'/\*.*\*/',

                # XSS patterns
                r'<script[^>]*>',
                r'</script>',
                r'javascript:',
                r'on\w+\s*=',
                r'eval\s*\(',
                r'expression\s*\(',
                r'<iframe[^>]*>',
                r'<object[^>]*>',
                r'<embed[^>]*>',
                r'<link[^>]*>',
                r'<meta[^>]*>',

                # Command injection patterns
                r';\s*rm\s+',
                r';\s*cat\s+',
                r';\s*ls\s+',
                r';\s*pwd',
                r';\s*whoami',
                r'\|\s*nc\s+',
                r'&&\s*rm\s+',
                r'\$\([^)]+\)',
            ]
        ]

    def _contains_suspicious_content(self, content):
        """Check if content contains suspicious patterns"""
        if not isinstance(content, str):
            return False
        
        for pattern in self.compiled_patterns:
            if pattern.search(content):
                return True
        return False

    def __call__(self, request):
        # Skip validation for file uploads (multipart/form-data)
        content_type = request.META.get('CONTENT_TYPE', '')
        if content_type.startswith('multipart/form-data'):
            # For file uploads, only validate form fields, not file content
            for key, value in request.POST.items():
                if self._contains_suspicious_content(key) or self._contains_suspicious_content(value):
                    logger.warning(f"Suspicious form data from {request.META.get('REMOTE_ADDR')}: {key}")
                    return JsonResponse({
                        'error': 'Invalid form data',
                        'message': 'Form contains potentially malicious content'
                    }, status=400)
        else:
            # Check for suspicious patterns in request data for non-file uploads
            if hasattr(request, 'body') and request.body:
                try:
                    body_str = request.body.decode('utf-8')
                    for pattern in self.compiled_patterns:
                        if pattern.search(body_str):
                            logger.warning(f"Suspicious input detected from {request.META.get('REMOTE_ADDR')}: {pattern.pattern}")
                            return JsonResponse({
                                'error': 'Invalid input detected',
                                'message': 'Request contains potentially malicious content'
                            }, status=400)
                except UnicodeDecodeError:
                    pass  # Binary data, skip validation

        # Enhanced query parameter validation
        for key, value in request.GET.items():
            # Check parameter name for suspicious patterns
            if self._contains_suspicious_content(key):
                logger.warning(f"Suspicious query parameter name from {request.META.get('REMOTE_ADDR')}: {key}")
                return JsonResponse({
                    'error': 'Invalid query parameter name',
                    'message': 'Query parameter name contains suspicious content'
                }, status=400)

            # Check parameter value
            if self._contains_suspicious_content(value):
                logger.warning(f"Suspicious query parameter from {request.META.get('REMOTE_ADDR')}: {key}={value[:50]}")
                return JsonResponse({
                    'error': 'Invalid query parameter',
                    'message': 'Query contains potentially malicious content'
                }, status=400)

        return self.get_response(request)
