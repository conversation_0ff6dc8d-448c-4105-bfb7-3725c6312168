"""
EMS Models Package
Imports all models from domain-specific modules for backward compatibility
"""

# Import base classes
from .base import (
    BaseModel, UUIDBaseModel, NamedModel, AuditableModel, StatusModel,
    MoneyField, PercentageField,
    PRIORITY_CHOICES, APPROVAL_STATUS_CHOICES, GENDER_CHOICES, MARITAL_STATUS_CHOICES
)

# Core domain models (User, Role, Department, Employee, Activity)
from .core import (
    Role, Department, UserProfile, Employee, Activity
)

# HR domain models (Leave, Attendance, Payroll, Performance)
from .hr import (
    EmployeeActivation, LeaveType, LeaveRequest, Attendance, 
    PerformanceReview, PayrollPeriod, PayrollEntry, TrainingProgram
)

# Finance domain models (Currency, Budget, Expense, Invoice, Payment)
from .finance import (
    Currency, ExchangeRate, AccountType, ChartOfAccounts, FiscalYear,
    JournalEntryBatch, JournalEntry, Budget, Expense, Vendor,
    VendorInvoice, CustomerInvoice, Payment
)

# Project domain models (Project, Task, Workflow)
from .projects import (
    Project, Task, Workflow
)

# Asset domain models (Asset, Maintenance, Transfer, Audit)
from .assets import (
    AssetCategory, Asset, AssetDepreciation, AssetMaintenance,
    AssetTransfer, AssetAudit, Supplier, PurchaseOrder
)

# CRM domain models (Customer, Product, Sales)
from .crm import (
    ProductCategory, Product, Report, SalesOrder, Customer
)

# Communication domain models (Announcement, Message, Document, Meeting)
from .communication import (
    Announcement, Message, Document, Meeting
)

# KPI domain models (KPI, Metrics, Analytics, Dashboard)
from .kpi import (
    KPIMetric, KPIMetricValue, ReportTemplate, ReportExecution,
    Dashboard, AnalyticsQuery, KPICategory, KPI, KPIValue, KPITarget, KPIAlert
)

# Integration domain models (API, Webhook, External Services)
from .integrations import (
    APIKey, ExternalService, WebhookEndpoint, WebhookEvent, IntegrationLog
)

# Security domain models (Security Profile, Audit, Compliance)
from .security import (
    UserSecurityProfile, AuditTrail, SecurityIncident, ComplianceFramework,
    ComplianceControl, DataClassification, SecurityAlert
)

# Enterprise domain models (Multi-tenant, ML)
from .enterprise import (
    Tenant, TenantUser, MLModel, MLPrediction
)

# Uncategorized models (will be categorized later)
from .misc import (
    RegistrationAnalytics, RegistrationAuditLog, PasswordReset,
    AutomationRule, AutomationExecution, DataProtectionRecord,
    DataSubjectRequest, JobPosting, Invoice, CostCenter
)

# Export all models for backward compatibility
__all__ = [
    # Base classes
    'BaseModel', 'UUIDBaseModel', 'NamedModel', 'AuditableModel', 'StatusModel',
    'MoneyField', 'PercentageField',
    'PRIORITY_CHOICES', 'APPROVAL_STATUS_CHOICES', 'GENDER_CHOICES', 'MARITAL_STATUS_CHOICES',
    
    # Core models
    'Role', 'Department', 'UserProfile', 'Employee', 'Activity',
    
    # HR models
    'EmployeeActivation', 'LeaveType', 'LeaveRequest', 'Attendance',
    'PerformanceReview', 'PayrollPeriod', 'PayrollEntry', 'TrainingProgram',
    
    # Finance models
    'Currency', 'ExchangeRate', 'AccountType', 'ChartOfAccounts', 'FiscalYear',
    'JournalEntryBatch', 'JournalEntry', 'Budget', 'Expense', 'Vendor',
    'VendorInvoice', 'CustomerInvoice', 'Payment',
    
    # Project models
    'Project', 'Task', 'Workflow',
    
    # Asset models
    'AssetCategory', 'Asset', 'AssetDepreciation', 'AssetMaintenance',
    'AssetTransfer', 'AssetAudit', 'Supplier', 'PurchaseOrder',
    
    # CRM models
    'ProductCategory', 'Product', 'Report', 'SalesOrder', 'Customer',
    
    # Communication models
    'Announcement', 'Message', 'Document', 'Meeting',
    
    # KPI models
    'KPIMetric', 'KPIMetricValue', 'ReportTemplate', 'ReportExecution',
    'Dashboard', 'AnalyticsQuery', 'KPICategory', 'KPI', 'KPIValue', 'KPITarget', 'KPIAlert',
    
    # Integration models
    'APIKey', 'ExternalService', 'WebhookEndpoint', 'WebhookEvent', 'IntegrationLog',
    
    # Security models
    'UserSecurityProfile', 'AuditTrail', 'SecurityIncident', 'ComplianceFramework',
    'ComplianceControl', 'DataClassification', 'SecurityAlert',
    
    # Enterprise models
    'Tenant', 'TenantUser', 'MLModel', 'MLPrediction',
    
    # Miscellaneous models
    'RegistrationAnalytics', 'RegistrationAuditLog', 'PasswordReset',
    'AutomationRule', 'AutomationExecution', 'DataProtectionRecord',
    'DataSubjectRequest', 'JobPosting', 'Invoice', 'CostCenter',
]
