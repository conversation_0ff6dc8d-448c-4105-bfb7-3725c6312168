"""
Assets Domain Models for EMS
Contains asset management related models:
- AssetCategory: Asset categorization
- Asset: Asset tracking and management
- AssetDepreciation: Asset depreciation tracking
- AssetMaintenance: Maintenance scheduling
- AssetTransfer: Asset transfer tracking
- AssetAudit: Asset audit records
- Supplier: Supplier management
- PurchaseOrder: Purchase order processing
"""

from django.db import models
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator
from .base import BaseModel, NamedModel, AuditableModel, MoneyField, APPROVAL_STATUS_CHOICES


class AssetCategory(NamedModel):
    """Asset categories for organization"""

    depreciation_rate = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=0,
        help_text="Annual depreciation rate percentage"
    )
    useful_life_years = models.IntegerField(
        default=5,
        help_text="Expected useful life in years"
    )

    class Meta:
        verbose_name_plural = "Asset Categories"
        ordering = ['name']


class Asset(BaseModel):
    """Asset tracking and management"""

    STATUS_CHOICES = [
        ('ACTIVE', 'Active'),
        ('INACTIVE', 'Inactive'),
        ('MAINTENANCE', 'Under Maintenance'),
        ('DISPOSED', 'Disposed'),
        ('LOST', 'Lost'),
    ]

    asset_tag = models.CharField(max_length=50, unique=True, help_text="Asset tag number")
    name = models.CharField(max_length=200, help_text="Asset name")
    name_ar = models.CharField(max_length=200, blank=True, help_text="Arabic name")
    category = models.ForeignKey(AssetCategory, on_delete=models.CASCADE, related_name='assets')
    description = models.TextField(blank=True)
    serial_number = models.CharField(max_length=100, blank=True)
    model = models.CharField(max_length=100, blank=True)
    manufacturer = models.CharField(max_length=100, blank=True)
    purchase_date = models.DateField()
    purchase_cost = MoneyField(help_text="Original purchase cost")
    current_value = MoneyField(help_text="Current book value")
    location = models.CharField(max_length=200, blank=True)
    assigned_to = models.ForeignKey(
        'Employee',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='assigned_assets'
    )
    assigned_date = models.DateField(null=True, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='ACTIVE')
    warranty_expiry = models.DateField(null=True, blank=True)

    def __str__(self):
        return f"{self.asset_tag} - {self.name}"

    def calculate_depreciation(self):
        """Calculate current depreciation"""
        if self.category.depreciation_rate > 0:
            years_owned = (timezone.now().date() - self.purchase_date).days / 365.25
            depreciation = self.purchase_cost * (self.category.depreciation_rate / 100) * years_owned
            self.current_value = max(0, self.purchase_cost - depreciation)
            self.save(update_fields=['current_value'])
        return self.current_value

    class Meta:
        ordering = ['asset_tag']


class Supplier(BaseModel):
    """Supplier management for asset procurement"""

    supplier_code = models.CharField(max_length=20, unique=True)
    company_name = models.CharField(max_length=200)
    company_name_ar = models.CharField(max_length=200, blank=True)
    contact_person = models.CharField(max_length=100)
    email = models.EmailField()
    phone = models.CharField(max_length=20)
    address = models.TextField()
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return f"{self.supplier_code} - {self.company_name}"

    class Meta:
        ordering = ['supplier_code']


class PurchaseOrder(BaseModel):
    """Purchase order processing"""

    STATUS_CHOICES = [
        ('DRAFT', 'Draft'),
        ('PENDING', 'Pending Approval'),
        ('APPROVED', 'Approved'),
        ('ORDERED', 'Ordered'),
        ('RECEIVED', 'Received'),
        ('CANCELLED', 'Cancelled'),
    ]

    po_number = models.CharField(max_length=50, unique=True)
    supplier = models.ForeignKey(Supplier, on_delete=models.CASCADE, related_name='purchase_orders')
    order_date = models.DateField()
    expected_delivery = models.DateField(null=True, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='DRAFT')
    total_amount = MoneyField()
    requested_by = models.ForeignKey('Employee', on_delete=models.CASCADE, related_name='requested_pos')
    approved_by = models.ForeignKey(
        'Employee',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_pos'
    )

    def __str__(self):
        return f"{self.po_number} - {self.supplier.company_name}"

    class Meta:
        ordering = ['-order_date']


class AssetDepreciation(BaseModel):
    """Asset depreciation tracking"""

    asset = models.ForeignKey(Asset, on_delete=models.CASCADE, related_name='depreciations')
    depreciation_date = models.DateField()
    depreciation_amount = MoneyField()
    accumulated_depreciation = MoneyField()
    book_value = MoneyField()
    notes = models.TextField(blank=True)

    class Meta:
        ordering = ['-depreciation_date']


class AssetMaintenance(BaseModel):
    """Asset maintenance scheduling and tracking"""

    STATUS_CHOICES = [
        ('SCHEDULED', 'Scheduled'),
        ('IN_PROGRESS', 'In Progress'),
        ('COMPLETED', 'Completed'),
        ('CANCELLED', 'Cancelled'),
    ]

    asset = models.ForeignKey(Asset, on_delete=models.CASCADE, related_name='maintenances')
    maintenance_type = models.CharField(max_length=100)
    scheduled_date = models.DateField()
    completed_date = models.DateField(null=True, blank=True)
    cost = MoneyField(default=0)
    description = models.TextField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='SCHEDULED')
    performed_by = models.CharField(max_length=200, blank=True)

    class Meta:
        ordering = ['-scheduled_date']


class AssetTransfer(BaseModel):
    """Asset transfer tracking"""

    asset = models.ForeignKey(Asset, on_delete=models.CASCADE, related_name='transfers')
    from_employee = models.ForeignKey(
        'Employee',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='transferred_assets_from'
    )
    to_employee = models.ForeignKey(
        'Employee',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='transferred_assets_to'
    )
    transfer_date = models.DateField()
    reason = models.TextField()
    approved_by = models.ForeignKey(
        'Employee',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_transfers'
    )

    class Meta:
        ordering = ['-transfer_date']


class AssetAudit(BaseModel):
    """Asset audit records"""

    STATUS_CHOICES = [
        ('FOUND', 'Found'),
        ('MISSING', 'Missing'),
        ('DAMAGED', 'Damaged'),
        ('DISPOSED', 'Disposed'),
    ]

    asset = models.ForeignKey(Asset, on_delete=models.CASCADE, related_name='audits')
    audit_date = models.DateField()
    auditor = models.ForeignKey('Employee', on_delete=models.CASCADE, related_name='conducted_audits')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES)
    condition_notes = models.TextField(blank=True)
    location_verified = models.BooleanField(default=True)

    class Meta:
        ordering = ['-audit_date']