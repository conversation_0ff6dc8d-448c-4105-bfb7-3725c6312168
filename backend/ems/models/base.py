"""
Base model classes for EMS application
Provides common functionality and patterns for all domain models
"""

from django.db import models
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator
import uuid


class BaseModel(models.Model):
    """
    Abstract base model with common fields for all models
    """
    created_at = models.DateTimeField(auto_now_add=True, help_text="When this record was created")
    updated_at = models.DateTimeField(auto_now=True, help_text="When this record was last updated")
    
    class Meta:
        abstract = True


class UUIDBaseModel(BaseModel):
    """
    Abstract base model with UUID primary key
    """
    id = models.UUIDField(
        primary_key=True, 
        default=uuid.uuid4, 
        editable=False,
        help_text="Unique identifier for this record"
    )
    
    class Meta:
        abstract = True


class NamedModel(BaseModel):
    """
    Abstract base model for entities with names and descriptions
    Includes Arabic language support
    """
    name = models.CharField(max_length=200, help_text="Name in English")
    name_ar = models.Char<PERSON>ield(max_length=200, blank=True, help_text="Name in Arabic")
    description = models.TextField(blank=True, help_text="Description in English")
    description_ar = models.TextField(blank=True, help_text="Description in Arabic")
    is_active = models.BooleanField(default=True, help_text="Is this record active?")
    
    class Meta:
        abstract = True
        
    def __str__(self):
        return self.name
    
    def get_display_name(self, language='en'):
        """Get display name in specified language"""
        if language == 'ar' and self.name_ar:
            return self.name_ar
        return self.name
    
    def get_display_description(self, language='en'):
        """Get description in specified language"""
        if language == 'ar' and self.description_ar:
            return self.description_ar
        return self.description


class AuditableModel(BaseModel):
    """
    Abstract base model with audit trail fields
    """
    created_by = models.ForeignKey(
        'auth.User', 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='%(class)s_created',
        help_text="User who created this record"
    )
    updated_by = models.ForeignKey(
        'auth.User', 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='%(class)s_updated',
        help_text="User who last updated this record"
    )
    
    class Meta:
        abstract = True


class StatusModel(BaseModel):
    """
    Abstract base model for entities with status tracking
    """
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('active', 'Active'),
        ('inactive', 'Inactive'),
        ('archived', 'Archived'),
    ]
    
    status = models.CharField(
        max_length=20, 
        choices=STATUS_CHOICES, 
        default='draft',
        help_text="Current status of this record"
    )
    
    class Meta:
        abstract = True
    
    def activate(self):
        """Activate this record"""
        self.status = 'active'
        self.save(update_fields=['status', 'updated_at'])
    
    def deactivate(self):
        """Deactivate this record"""
        self.status = 'inactive'
        self.save(update_fields=['status', 'updated_at'])
    
    def archive(self):
        """Archive this record"""
        self.status = 'archived'
        self.save(update_fields=['status', 'updated_at'])


class MoneyField(models.DecimalField):
    """
    Custom field for monetary values with proper precision
    """
    def __init__(self, *args, **kwargs):
        kwargs.setdefault('max_digits', 12)
        kwargs.setdefault('decimal_places', 2)
        kwargs.setdefault('validators', [MinValueValidator(0)])
        super().__init__(*args, **kwargs)


class PercentageField(models.DecimalField):
    """
    Custom field for percentage values (0-100)
    """
    def __init__(self, *args, **kwargs):
        kwargs.setdefault('max_digits', 5)
        kwargs.setdefault('decimal_places', 2)
        kwargs.setdefault('validators', [
            MinValueValidator(0),
            MaxValueValidator(100)
        ])
        super().__init__(*args, **kwargs)


# Common choices for reuse across models
PRIORITY_CHOICES = [
    ('low', 'Low'),
    ('medium', 'Medium'),
    ('high', 'High'),
    ('urgent', 'Urgent'),
]

APPROVAL_STATUS_CHOICES = [
    ('pending', 'Pending'),
    ('approved', 'Approved'),
    ('rejected', 'Rejected'),
    ('cancelled', 'Cancelled'),
]

GENDER_CHOICES = [
    ('M', 'Male'),
    ('F', 'Female'),
    ('O', 'Other'),
    ('N', 'Prefer not to say'),
]

MARITAL_STATUS_CHOICES = [
    ('single', 'Single'),
    ('married', 'Married'),
    ('divorced', 'Divorced'),
    ('widowed', 'Widowed'),
    ('separated', 'Separated'),
]
