"""
Communication Domain Models for EMS
Contains communication and collaboration models:
- Announcement: Company announcements
- Message: Internal messaging
- Document: Document management
- Meeting: Meeting management
"""

from django.db import models
from django.utils import timezone
from .base import BaseModel, NamedModel, AuditableModel


class Announcement(BaseModel):
    """Company announcements"""
    
    title = models.CharField(max_length=200)
    title_ar = models.CharField(max_length=200, blank=True)
    content = models.TextField()
    content_ar = models.TextField(blank=True)
    is_urgent = models.BooleanField(default=False)
    is_published = models.BooleanField(default=False)
    published_at = models.DateTimeField(null=True, blank=True)
    expires_at = models.DateTimeField(null=True, blank=True)
    created_by = models.ForeignKey('Employee', on_delete=models.CASCADE)
    
    def __str__(self):
        return self.title
    
    class Meta:
        ordering = ['-created_at']


class Message(BaseModel):
    """Internal messaging system"""
    
    sender = models.ForeignKey('Employee', on_delete=models.CASCADE, related_name='sent_messages')
    recipient = models.ForeignKey('Employee', on_delete=models.CASCADE, related_name='received_messages')
    subject = models.CharField(max_length=200)
    content = models.TextField()
    is_read = models.BooleanField(default=False)
    read_at = models.DateTimeField(null=True, blank=True)
    
    def __str__(self):
        return f"{self.sender} to {self.recipient}: {self.subject}"
    
    class Meta:
        ordering = ['-created_at']


class Document(BaseModel):
    """Document management"""
    
    title = models.CharField(max_length=200)
    title_ar = models.CharField(max_length=200, blank=True)
    description = models.TextField(blank=True)
    file = models.FileField(upload_to='documents/')
    category = models.CharField(max_length=50)
    is_public = models.BooleanField(default=False)
    uploaded_by = models.ForeignKey('Employee', on_delete=models.CASCADE)
    
    def __str__(self):
        return self.title
    
    class Meta:
        ordering = ['-created_at']


class Meeting(BaseModel):
    """Meeting management"""
    
    title = models.CharField(max_length=200)
    title_ar = models.CharField(max_length=200, blank=True)
    description = models.TextField(blank=True)
    start_time = models.DateTimeField()
    end_time = models.DateTimeField()
    location = models.CharField(max_length=200, blank=True)
    organizer = models.ForeignKey('Employee', on_delete=models.CASCADE, related_name='organized_meetings')
    attendees = models.ManyToManyField('Employee', related_name='meetings')
    
    def __str__(self):
        return f"{self.title} - {self.start_time.strftime('%Y-%m-%d %H:%M')}"
    
    class Meta:
        ordering = ['-start_time']
