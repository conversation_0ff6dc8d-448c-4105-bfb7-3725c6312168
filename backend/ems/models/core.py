"""
Core Domain Models for EMS
Contains fundamental models that other domains depend on:
- Role: User roles and permissions
- Department: Organizational departments
- UserProfile: Extended user information
- Employee: Employee records
- Activity: System activity logging
"""

from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator
import uuid
from .base import BaseModel, NamedModel, AuditableModel, GENDER_CHOICES


class Role(BaseModel):
    """
    User roles and permissions in the system
    """
    ROLE_TYPES = [
        ('SUPERADMIN', 'Super Administrator'),
        ('ADMIN', 'Administrator'),
        ('HR_MANAGER', 'HR Manager'),
        ('DEPARTMENT_MANAGER', 'Department Manager'),
        ('PROJECT_MANAGER', 'Project Manager'),
        ('FINANCE_MANAGER', 'Finance Manager'),
        ('EMPLOYEE', 'Employee'),
        ('INTERN', 'Intern'),
    ]

    name = models.Char<PERSON>ield(max_length=50, choices=ROLE_TYPES, unique=True)
    name_ar = models.CharField(max_length=50, help_text="Arabic name")
    description = models.TextField(blank=True)
    description_ar = models.TextField(blank=True, help_text="Arabic description")
    permissions = models.JSONField(default=dict, help_text="Store permissions as JSON")

    def __str__(self):
        return self.get_name_display()

    class Meta:
        ordering = ['name']


class Department(NamedModel):
    """
    Organizational departments
    """
    manager = models.ForeignKey(
        'Employee',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='managed_departments',
        help_text="Department manager"
    )
    budget_amount = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Department budget amount"
    )
    location = models.CharField(max_length=200, blank=True, help_text="Department location")
    phone = models.CharField(max_length=20, blank=True, help_text="Department phone")
    email = models.EmailField(blank=True, help_text="Department email")

    class Meta:
        ordering = ['name']


class UserProfile(BaseModel):
    """
    Extended user profile information
    """
    LANGUAGE_CHOICES = [
        ('ar', 'Arabic'),
        ('en', 'English'),
    ]

    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')
    role = models.ForeignKey(Role, on_delete=models.SET_NULL, null=True, blank=True)
    avatar = models.ImageField(upload_to='avatars/', null=True, blank=True)
    phone = models.CharField(max_length=20, blank=True)
    address = models.TextField(blank=True)
    date_of_birth = models.DateField(null=True, blank=True)
    emergency_contact_name = models.CharField(max_length=100, blank=True)
    emergency_contact_phone = models.CharField(max_length=20, blank=True)
    preferred_language = models.CharField(
        max_length=2,
        choices=LANGUAGE_CHOICES,
        default='ar',
        help_text="User's preferred language"
    )
    timezone = models.CharField(max_length=50, default='Asia/Riyadh')

    def __str__(self):
        return f"{self.user.get_full_name()} - {self.role}"

    class Meta:
        ordering = ['user__first_name', 'user__last_name']


class Employee(BaseModel):
    """
    Employee records with comprehensive information
    """
    EMPLOYMENT_STATUS = [
        ('FULL_TIME', 'Full Time'),
        ('PART_TIME', 'Part Time'),
        ('CONTRACT', 'Contract'),
        ('INTERN', 'Intern'),
        ('CONSULTANT', 'Consultant'),
    ]

    # Core employee information
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='employee')
    employee_id = models.CharField(max_length=20, unique=True, help_text="Unique employee identifier")
    department = models.ForeignKey(
        Department,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='employees'
    )
    position = models.CharField(max_length=100, help_text="Job position")
    position_ar = models.CharField(max_length=100, help_text="Arabic job position")

    # Arabic name fields
    first_name_ar = models.CharField(max_length=150, blank=True, help_text="Arabic first name")
    last_name_ar = models.CharField(max_length=150, blank=True, help_text="Arabic last name")

    # Contact information
    phone = models.CharField(max_length=20, blank=True)

    # Personal information
    gender = models.CharField(max_length=1, choices=GENDER_CHOICES)

    # Employment details
    hire_date = models.DateField(help_text="Date of hire")
    salary = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        validators=[MinValueValidator(0)],
        help_text="Employee salary"
    )
    employment_status = models.CharField(
        max_length=20,
        choices=EMPLOYMENT_STATUS,
        default='FULL_TIME'
    )
    manager = models.ForeignKey(
        'self',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='subordinates',
        help_text="Direct manager"
    )
    work_location = models.CharField(max_length=200, blank=True)
    contract_end_date = models.DateField(null=True, blank=True)
    probation_end_date = models.DateField(null=True, blank=True)

    # Identification
    national_id = models.CharField(max_length=20, blank=True)
    passport_number = models.CharField(max_length=20, blank=True)

    # Financial information
    bank_account = models.CharField(max_length=50, blank=True)

    # Emergency contact
    emergency_contact = models.CharField(max_length=100, blank=True)
    emergency_phone = models.CharField(max_length=20, blank=True)

    # Professional information
    skills = models.TextField(blank=True, help_text="Employee skills")
    education = models.TextField(blank=True, help_text="Educational background")
    certifications = models.TextField(blank=True, help_text="Professional certifications")

    # Status and audit
    is_active = models.BooleanField(default=True)
    created_by = models.ForeignKey(
        'self',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_employees',
        help_text="Employee who created this record"
    )

    def __str__(self):
        return f"{self.employee_id} - {self.user.get_full_name()}"

    def get_full_name_ar(self):
        """Get full name in Arabic"""
        if self.first_name_ar and self.last_name_ar:
            return f"{self.first_name_ar} {self.last_name_ar}"
        return self.user.get_full_name()

    class Meta:
        ordering = ['employee_id']
        indexes = [
            models.Index(fields=['employee_id']),
            models.Index(fields=['department', 'is_active']),
            models.Index(fields=['hire_date']),
            models.Index(fields=['manager', 'is_active']),
        ]


class Activity(BaseModel):
    """
    System activity logging for audit trail
    """
    ACTIVITY_TYPES = [
        ('LOGIN', 'User Login'),
        ('LOGOUT', 'User Logout'),
        ('CREATE', 'Record Created'),
        ('UPDATE', 'Record Updated'),
        ('DELETE', 'Record Deleted'),
        ('REPORT', 'Report Generated'),
        ('APPROVAL', 'Approval Action'),
        ('MESSAGE', 'Message Sent'),
        ('MEETING', 'Meeting Action'),
        ('PROJECT', 'Project Action'),
        ('TASK', 'Task Action'),
        ('EXPENSE', 'Expense Action'),
        ('LEAVE', 'Leave Action'),
        ('ASSET', 'Asset Action'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='activities')
    activity_type = models.CharField(max_length=20, choices=ACTIVITY_TYPES)
    description = models.TextField(help_text="Activity description")
    description_ar = models.TextField(help_text="Arabic description")
    related_object_type = models.CharField(
        max_length=50,
        blank=True,
        help_text="Type of related object (e.g., 'project', 'task', 'employee')"
    )
    related_object_id = models.PositiveIntegerField(null=True, blank=True)
    metadata = models.JSONField(default=dict, blank=True, help_text="Additional data")
    timestamp = models.DateTimeField(auto_now_add=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)

    def __str__(self):
        return f"{self.user.username} - {self.activity_type} - {self.timestamp}"

    class Meta:
        ordering = ['-timestamp']
        verbose_name_plural = "Activities"
        indexes = [
            models.Index(fields=['user', '-timestamp']),
            models.Index(fields=['activity_type', '-timestamp']),
            models.Index(fields=['related_object_type', 'related_object_id']),
        ]