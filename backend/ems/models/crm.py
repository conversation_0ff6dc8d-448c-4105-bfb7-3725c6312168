"""
CRM Domain Models for EMS
Contains customer relationship management models:
- ProductCategory: Product categorization
- Product: Product management
- Customer: Customer management
- SalesOrder: Sales order processing
- Report: Reporting system
"""

from django.db import models
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator
from .base import BaseModel, NamedModel, AuditableModel, MoneyField, APPROVAL_STATUS_CHOICES


class ProductCategory(NamedModel):
    """Product categories for organization"""
    
    parent_category = models.ForeignKey(
        'self', 
        on_delete=models.CASCADE, 
        null=True, 
        blank=True, 
        related_name='subcategories'
    )
    
    class Meta:
        verbose_name_plural = "Product Categories"
        ordering = ['name']


class Product(NamedModel):
    """Product management"""
    
    category = models.ForeignKey(
        ProductCategory, 
        on_delete=models.CASCADE,
        related_name='products'
    )
    sku = models.CharField(max_length=50, unique=True, help_text="Stock Keeping Unit")
    price = MoneyField(help_text="Product price")
    cost = MoneyField(default=0, help_text="Product cost")
    stock_quantity = models.IntegerField(default=0, help_text="Current stock")
    min_stock_level = models.IntegerField(default=0, help_text="Minimum stock level")
    
    def __str__(self):
        return f"{self.sku} - {self.name}"
    
    class Meta:
        ordering = ['sku']


class Customer(BaseModel):
    """Customer management"""
    
    customer_code = models.CharField(max_length=20, unique=True)
    company_name = models.CharField(max_length=200)
    company_name_ar = models.CharField(max_length=200, blank=True)
    contact_person = models.CharField(max_length=100)
    email = models.EmailField()
    phone = models.CharField(max_length=20)
    address = models.TextField()
    address_ar = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    
    def __str__(self):
        return f"{self.customer_code} - {self.company_name}"
    
    class Meta:
        ordering = ['customer_code']


class SalesOrder(BaseModel):
    """Sales order processing"""
    
    STATUS_CHOICES = [
        ('DRAFT', 'Draft'),
        ('CONFIRMED', 'Confirmed'),
        ('SHIPPED', 'Shipped'),
        ('DELIVERED', 'Delivered'),
        ('CANCELLED', 'Cancelled'),
    ]
    
    order_number = models.CharField(max_length=50, unique=True)
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, related_name='orders')
    order_date = models.DateField()
    delivery_date = models.DateField(null=True, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='DRAFT')
    total_amount = MoneyField()
    
    def __str__(self):
        return f"{self.order_number} - {self.customer.company_name}"
    
    class Meta:
        ordering = ['-order_date']


class Report(BaseModel):
    """Reporting system"""
    
    title = models.CharField(max_length=200)
    title_ar = models.CharField(max_length=200, blank=True)
    description = models.TextField()
    report_type = models.CharField(max_length=50)
    generated_by = models.ForeignKey('Employee', on_delete=models.CASCADE)
    file_path = models.CharField(max_length=500, blank=True)
    
    def __str__(self):
        return self.title
    
    class Meta:
        ordering = ['-created_at']
