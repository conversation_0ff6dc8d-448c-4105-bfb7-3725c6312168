"""
Enterprise Domain Models for EMS
Contains enterprise and multi-tenant features
"""

from django.db import models
from django.utils import timezone
from .base import BaseModel, NamedModel, AuditableModel


class Tenant(BaseModel):
    """Multi-tenant support"""
    
    name = models.CharField(max_length=200)
    subdomain = models.CharField(max_length=100, unique=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    subscription_expires = models.DateTimeField(null=True, blank=True)


class TenantUser(BaseModel):
    """Tenant user relationships"""
    
    tenant = models.ForeignKey(Tenant, on_delete=models.CASCADE, related_name='users')
    user = models.ForeignKey('Employee', on_delete=models.CASCADE, related_name='tenants')
    is_admin = models.BooleanField(default=False)
    joined_at = models.DateTimeField(auto_now_add=True)


class MLModel(BaseModel):
    """Machine learning model management"""
    
    name = models.Char<PERSON>ield(max_length=200)
    version = models.CharField(max_length=50)
    model_type = models.CharField(max_length=100)
    file_path = models.CharField(max_length=500)
    is_active = models.BooleanField(default=True)
    accuracy = models.DecimalField(max_digits=5, decimal_places=4, null=True, blank=True)
    created_by = models.ForeignKey('Employee', on_delete=models.CASCADE)


class MLPrediction(BaseModel):
    """ML prediction results"""
    
    model = models.ForeignKey(MLModel, on_delete=models.CASCADE, related_name='predictions')
    input_data = models.JSONField()
    prediction = models.JSONField()
    confidence = models.DecimalField(max_digits=5, decimal_places=4, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
