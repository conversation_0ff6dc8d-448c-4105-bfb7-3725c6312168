"""
Integration Domain Models for EMS
Contains external integration related models
"""

from django.db import models
from django.utils import timezone
from .base import BaseModel, NamedModel, AuditableModel


class APIKey(BaseModel):
    """API key management"""
    
    name = models.CharField(max_length=200)
    key = models.CharField(max_length=255, unique=True)
    is_active = models.BooleanField(default=True)
    expires_at = models.DateTimeField(null=True, blank=True)
    created_by = models.ForeignKey('Employee', on_delete=models.CASCADE)
    last_used = models.DateTimeField(null=True, blank=True)


class ExternalService(BaseModel):
    """External service configurations"""
    
    name = models.CharField(max_length=200)
    base_url = models.URLField()
    api_key = models.CharField(max_length=255, blank=True)
    is_active = models.BooleanField(default=True)
    timeout_seconds = models.IntegerField(default=30)
    retry_attempts = models.IntegerField(default=3)


class WebhookEndpoint(BaseModel):
    """Webhook endpoint management"""
    
    name = models.CharField(max_length=200)
    url = models.URLField()
    secret = models.CharField(max_length=255)
    is_active = models.BooleanField(default=True)
    events = models.JSONField(default=list)


class WebhookEvent(BaseModel):
    """Webhook event log"""

    endpoint = models.ForeignKey(WebhookEndpoint, on_delete=models.CASCADE, related_name='webhook_events')
    event_type = models.CharField(max_length=100)
    payload = models.JSONField()
    response_status = models.IntegerField(null=True, blank=True)
    response_body = models.TextField(blank=True)
    delivered_at = models.DateTimeField(null=True, blank=True)


class IntegrationLog(BaseModel):
    """Integration activity log"""
    
    service = models.ForeignKey(ExternalService, on_delete=models.CASCADE, related_name='logs')
    operation = models.CharField(max_length=100)
    request_data = models.JSONField(default=dict)
    response_data = models.JSONField(default=dict)
    status = models.CharField(max_length=50)
    duration_ms = models.IntegerField(null=True, blank=True)
