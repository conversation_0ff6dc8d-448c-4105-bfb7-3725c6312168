"""
KPI Domain Models for EMS
Contains key performance indicator and analytics models
"""

from django.db import models
from django.utils import timezone
from .base import BaseModel, NamedModel, AuditableModel


class KPICategory(NamedModel):
    """KPI categories for organization"""
    pass


class KPI(BaseModel):
    """Key Performance Indicators"""
    
    name = models.CharField(max_length=200)
    name_ar = models.CharField(max_length=200, blank=True)
    category = models.ForeignKey(KPICategory, on_delete=models.CASCADE, related_name='kpis')
    target_value = models.DecimalField(max_digits=15, decimal_places=2)
    current_value = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    unit = models.CharField(max_length=50, blank=True)
    is_active = models.BooleanField(default=True)
    
    def __str__(self):
        return self.name


class KPIValue(BaseModel):
    """KPI value tracking"""
    
    kpi = models.ForeignKey(KPI, on_delete=models.CASCADE, related_name='values')
    value = models.DecimalField(max_digits=15, decimal_places=2)
    date = models.DateField()
    notes = models.TextField(blank=True)
    
    class Meta:
        unique_together = ['kpi', 'date']


class KPITarget(BaseModel):
    """KPI targets"""
    
    kpi = models.ForeignKey(KPI, on_delete=models.CASCADE, related_name='targets')
    target_value = models.DecimalField(max_digits=15, decimal_places=2)
    start_date = models.DateField()
    end_date = models.DateField()


class KPIAlert(BaseModel):
    """KPI alerts and notifications"""
    
    kpi = models.ForeignKey(KPI, on_delete=models.CASCADE, related_name='alerts')
    alert_type = models.CharField(max_length=50)
    threshold_value = models.DecimalField(max_digits=15, decimal_places=2)
    is_active = models.BooleanField(default=True)


class KPIMetric(BaseModel):
    """KPI metrics"""
    
    name = models.CharField(max_length=200)
    formula = models.TextField()
    is_active = models.BooleanField(default=True)


class KPIMetricValue(BaseModel):
    """KPI metric values"""
    
    metric = models.ForeignKey(KPIMetric, on_delete=models.CASCADE, related_name='values')
    value = models.DecimalField(max_digits=15, decimal_places=2)
    date = models.DateField()


class ReportTemplate(BaseModel):
    """Report templates"""
    
    name = models.CharField(max_length=200)
    template_content = models.TextField()
    is_active = models.BooleanField(default=True)


class ReportExecution(BaseModel):
    """Report execution tracking"""
    
    template = models.ForeignKey(ReportTemplate, on_delete=models.CASCADE, related_name='executions')
    executed_by = models.ForeignKey('Employee', on_delete=models.CASCADE)
    execution_time = models.DateTimeField(auto_now_add=True)
    status = models.CharField(max_length=50)


class Dashboard(BaseModel):
    """Dashboard configuration"""
    
    name = models.CharField(max_length=200)
    config = models.JSONField(default=dict)
    is_default = models.BooleanField(default=False)
    created_by = models.ForeignKey('Employee', on_delete=models.CASCADE)


class AnalyticsQuery(BaseModel):
    """Analytics query storage"""
    
    name = models.CharField(max_length=200)
    query = models.TextField()
    parameters = models.JSONField(default=dict)
    created_by = models.ForeignKey('Employee', on_delete=models.CASCADE)
