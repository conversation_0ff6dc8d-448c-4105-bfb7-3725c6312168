"""
Miscellaneous Domain Models for EMS
Contains uncategorized models that need to be properly categorized:
- RegistrationAnalytics: Registration tracking
- RegistrationAuditLog: Registration audit
- PasswordReset: Password reset functionality
- AutomationRule: Automation rules
- AutomationExecution: Automation execution tracking
- DataProtectionRecord: Data protection compliance
- DataSubjectRequest: GDPR data subject requests
- JobPosting: Job posting management
- Invoice: General invoice model
- CostCenter: Cost center management
"""

from django.db import models
from django.utils import timezone
from django.contrib.auth.models import User
from .base import BaseModel, NamedModel, AuditableModel, MoneyField


class RegistrationAnalytics(BaseModel):
    """Registration analytics tracking"""

    registration_date = models.DateField()
    user_count = models.IntegerField(default=0)
    department = models.ForeignKey(
        'Department',
        on_delete=models.SET_NULL,
        null=True,
        blank=True
    )
    source = models.CharField(max_length=100, blank=True)

    class Meta:
        unique_together = ['registration_date', 'department']


class RegistrationAuditLog(BaseModel):
    """Registration audit log"""

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='registration_audits')
    action = models.CharField(max_length=100)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
    success = models.BooleanField(default=True)
    error_message = models.TextField(blank=True)


class PasswordReset(BaseModel):
    """Password reset token management"""

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='password_resets')
    token = models.CharField(max_length=255, unique=True)
    expires_at = models.DateTimeField()
    used = models.BooleanField(default=False)
    used_at = models.DateTimeField(null=True, blank=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)


class AutomationRule(BaseModel):
    """Automation rules configuration"""

    name = models.CharField(max_length=200)
    description = models.TextField()
    trigger_event = models.CharField(max_length=100)
    conditions = models.JSONField(default=dict)
    actions = models.JSONField(default=dict)
    is_active = models.BooleanField(default=True)
    created_by = models.ForeignKey('Employee', on_delete=models.CASCADE)


class AutomationExecution(BaseModel):
    """Automation execution tracking"""

    rule = models.ForeignKey(AutomationRule, on_delete=models.CASCADE, related_name='executions')
    triggered_by = models.CharField(max_length=100)
    execution_time = models.DateTimeField(auto_now_add=True)
    success = models.BooleanField(default=True)
    error_message = models.TextField(blank=True)
    execution_data = models.JSONField(default=dict)


class DataProtectionRecord(BaseModel):
    """Data protection compliance records"""

    RECORD_TYPES = [
        ('PROCESSING', 'Data Processing'),
        ('TRANSFER', 'Data Transfer'),
        ('DELETION', 'Data Deletion'),
        ('BREACH', 'Data Breach'),
    ]

    record_type = models.CharField(max_length=20, choices=RECORD_TYPES)
    description = models.TextField()
    data_subject = models.CharField(max_length=200, blank=True)
    legal_basis = models.CharField(max_length=200)
    retention_period = models.CharField(max_length=100)
    created_by = models.ForeignKey('Employee', on_delete=models.CASCADE)


class DataSubjectRequest(BaseModel):
    """GDPR data subject requests"""

    REQUEST_TYPES = [
        ('ACCESS', 'Data Access'),
        ('RECTIFICATION', 'Data Rectification'),
        ('ERASURE', 'Data Erasure'),
        ('PORTABILITY', 'Data Portability'),
        ('RESTRICTION', 'Processing Restriction'),
        ('OBJECTION', 'Processing Objection'),
    ]

    STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('IN_PROGRESS', 'In Progress'),
        ('COMPLETED', 'Completed'),
        ('REJECTED', 'Rejected'),
    ]

    request_type = models.CharField(max_length=20, choices=REQUEST_TYPES)
    requester_name = models.CharField(max_length=200)
    requester_email = models.EmailField()
    description = models.TextField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PENDING')
    assigned_to = models.ForeignKey(
        'Employee',
        on_delete=models.SET_NULL,
        null=True,
        blank=True
    )
    due_date = models.DateField()
    completed_at = models.DateTimeField(null=True, blank=True)


class JobPosting(BaseModel):
    """Job posting management"""

    STATUS_CHOICES = [
        ('DRAFT', 'Draft'),
        ('PUBLISHED', 'Published'),
        ('CLOSED', 'Closed'),
        ('CANCELLED', 'Cancelled'),
    ]

    title = models.CharField(max_length=200)
    title_ar = models.CharField(max_length=200, blank=True)
    description = models.TextField()
    description_ar = models.TextField(blank=True)
    department = models.ForeignKey(
        'Department',
        on_delete=models.CASCADE,
        related_name='job_postings'
    )
    requirements = models.TextField()
    requirements_ar = models.TextField(blank=True)
    salary_range_min = MoneyField(null=True, blank=True)
    salary_range_max = MoneyField(null=True, blank=True)
    location = models.CharField(max_length=200)
    employment_type = models.CharField(max_length=50)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='DRAFT')
    posted_date = models.DateField(null=True, blank=True)
    closing_date = models.DateField()
    created_by = models.ForeignKey('Employee', on_delete=models.CASCADE)


class Invoice(BaseModel):
    """General invoice model"""

    STATUS_CHOICES = [
        ('DRAFT', 'Draft'),
        ('SENT', 'Sent'),
        ('PAID', 'Paid'),
        ('OVERDUE', 'Overdue'),
        ('CANCELLED', 'Cancelled'),
    ]

    invoice_number = models.CharField(max_length=50, unique=True)
    invoice_date = models.DateField()
    due_date = models.DateField()
    subtotal = MoneyField()
    tax_amount = MoneyField(default=0)
    total_amount = MoneyField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='DRAFT')
    notes = models.TextField(blank=True)
    created_by = models.ForeignKey('Employee', on_delete=models.CASCADE)


class CostCenter(BaseModel):
    """Cost center management"""

    code = models.CharField(max_length=20, unique=True)
    name = models.CharField(max_length=200)
    name_ar = models.CharField(max_length=200, blank=True)
    description = models.TextField(blank=True)
    manager = models.ForeignKey(
        'Employee',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='managed_cost_centers'
    )
    budget_amount = MoneyField(default=0)
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return f"{self.code} - {self.name}"

    class Meta:
        ordering = ['code']