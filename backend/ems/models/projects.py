"""
Projects Domain Models for EMS
Contains project management related models:
- Project: Project management and tracking
- Task: Task management within projects
- Workflow: Workflow automation and management
"""

from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
from django.core.validators import MinV<PERSON>ueValidator, MaxValueValidator
from .base import BaseModel, NamedModel, AuditableModel, MoneyField, PRIORITY_CHOICES


class Project(BaseModel):
    """
    Project management and tracking
    """
    STATUS_CHOICES = [
        ('PLANNING', 'Planning'),
        ('IN_PROGRESS', 'In Progress'),
        ('ON_HOLD', 'On Hold'),
        ('COMPLETED', 'Completed'),
        ('CANCELLED', 'Cancelled'),
    ]

    name = models.CharField(max_length=200, help_text="Project name")
    name_ar = models.CharField(max_length=200, help_text="Arabic project name")
    description = models.TextField(help_text="Project description")
    description_ar = models.TextField(blank=True, help_text="Arabic description")
    project_manager = models.ForeignKey(
        'Employee',
        on_delete=models.SET_NULL,
        null=True,
        related_name='managed_projects'
    )
    department = models.ForeignKey(
        'Department',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='projects'
    )
    client = models.CharField(max_length=200, blank=True, help_text="Client name")
    start_date = models.DateField(help_text="Project start date")
    end_date = models.DateField(help_text="Project end date")
    budget = MoneyField(default=0, help_text="Project budget")
    actual_cost = MoneyField(default=0, help_text="Actual cost incurred")
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='PLANNING'
    )
    priority = models.CharField(
        max_length=10,
        choices=PRIORITY_CHOICES,
        default='medium'
    )
    progress_percentage = models.IntegerField(
        default=0,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        help_text="Project completion percentage"
    )
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return self.name

    def get_task_count(self):
        """Get total number of tasks"""
        return self.tasks.count()

    def get_completed_task_count(self):
        """Get number of completed tasks"""
        return self.tasks.filter(status='COMPLETED').count()

    def calculate_progress(self):
        """Calculate project progress based on completed tasks"""
        total_tasks = self.get_task_count()
        if total_tasks > 0:
            completed_tasks = self.get_completed_task_count()
            self.progress_percentage = int((completed_tasks / total_tasks) * 100)
            self.save(update_fields=['progress_percentage'])
        return self.progress_percentage

    def is_overdue(self):
        """Check if project is overdue"""
        return timezone.now().date() > self.end_date and self.status != 'COMPLETED'

    def is_over_budget(self):
        """Check if project is over budget"""
        return self.actual_cost > self.budget

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['project_manager', 'status']),
            models.Index(fields=['department', 'status']),
            models.Index(fields=['status', '-created_at']),
            models.Index(fields=['start_date', 'end_date']),
        ]


class Task(BaseModel):
    """
    Task management within projects
    """
    STATUS_CHOICES = [
        ('TODO', 'To Do'),
        ('IN_PROGRESS', 'In Progress'),
        ('REVIEW', 'Under Review'),
        ('COMPLETED', 'Completed'),
        ('CANCELLED', 'Cancelled'),
    ]

    project = models.ForeignKey(
        Project,
        on_delete=models.CASCADE,
        related_name='tasks'
    )
    title = models.CharField(max_length=200, help_text="Task title")
    title_ar = models.CharField(max_length=200, blank=True, help_text="Arabic title")
    description = models.TextField(help_text="Task description")
    description_ar = models.TextField(blank=True, help_text="Arabic description")
    assigned_to = models.ForeignKey(
        'Employee',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='assigned_tasks'
    )
    created_by = models.ForeignKey(
        'Employee',
        on_delete=models.CASCADE,
        related_name='created_tasks'
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='TODO'
    )
    priority = models.CharField(
        max_length=10,
        choices=PRIORITY_CHOICES,
        default='medium'
    )
    due_date = models.DateField(help_text="Task due date")
    estimated_hours = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        default=0,
        help_text="Estimated hours to complete"
    )
    actual_hours = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        default=0,
        help_text="Actual hours spent"
    )
    completion_percentage = models.IntegerField(
        default=0,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        help_text="Task completion percentage"
    )

    def __str__(self):
        return f"{self.project.name} - {self.title}"

    def is_overdue(self):
        """Check if task is overdue"""
        return timezone.now().date() > self.due_date and self.status != 'COMPLETED'

    def mark_completed(self):
        """Mark task as completed"""
        self.status = 'COMPLETED'
        self.completion_percentage = 100
        self.save()

        # Update project progress
        self.project.calculate_progress()

    class Meta:
        ordering = ['due_date', 'priority']
        indexes = [
            models.Index(fields=['project', 'status']),
            models.Index(fields=['assigned_to', 'status']),
            models.Index(fields=['due_date', 'status']),
            models.Index(fields=['priority', 'due_date']),
        ]


class Workflow(BaseModel):
    """
    Workflow automation and management
    """
    STATUS_CHOICES = [
        ('active', 'Active'),
        ('inactive', 'Inactive'),
        ('draft', 'Draft'),
        ('archived', 'Archived'),
    ]

    CATEGORY_CHOICES = [
        ('hr', 'Human Resources'),
        ('finance', 'Finance'),
        ('sales', 'Sales'),
        ('operations', 'Operations'),
        ('it', 'Information Technology'),
        ('general', 'General'),
    ]

    name = models.CharField(max_length=200, help_text="Workflow name")
    name_ar = models.CharField(max_length=200, blank=True, help_text="Arabic name")
    description = models.TextField(help_text="Workflow description")
    description_ar = models.TextField(blank=True, help_text="Arabic description")
    category = models.CharField(
        max_length=20,
        choices=CATEGORY_CHOICES,
        default='general'
    )
    category_ar = models.CharField(max_length=100, blank=True, help_text="Arabic category")
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='draft'
    )
    priority = models.CharField(
        max_length=10,
        choices=PRIORITY_CHOICES,
        default='medium'
    )

    # Workflow configuration
    trigger_event = models.CharField(max_length=100, blank=True, help_text="Event that triggers workflow")
    conditions = models.TextField(blank=True, help_text="Workflow conditions")
    conditions_ar = models.TextField(blank=True, help_text="Arabic conditions")
    actions = models.TextField(blank=True, help_text="Workflow actions")
    actions_ar = models.TextField(blank=True, help_text="Arabic actions")

    # Automation settings
    is_automated = models.BooleanField(default=False, help_text="Is workflow automated?")
    next_run = models.DateTimeField(null=True, blank=True, help_text="Next scheduled run")
    last_run = models.DateTimeField(null=True, blank=True, help_text="Last execution time")
    run_count = models.IntegerField(default=0, help_text="Total execution count")
    success_count = models.IntegerField(default=0, help_text="Successful execution count")

    # Metadata
    created_by = models.ForeignKey(
        'Employee',
        on_delete=models.CASCADE,
        related_name='created_workflows',
        null=True,
        blank=True
    )

    def __str__(self):
        return self.name

    def execute(self):
        """Execute the workflow"""
        self.run_count += 1
        self.last_run = timezone.now()

        try:
            # Workflow execution logic would go here
            # This is a placeholder for actual workflow engine
            self.success_count += 1
            success = True
        except Exception as e:
            success = False

        self.save()
        return success

    def get_success_rate(self):
        """Calculate workflow success rate"""
        if self.run_count > 0:
            return (self.success_count / self.run_count) * 100
        return 0

    def activate(self):
        """Activate the workflow"""
        self.status = 'active'
        self.save()

    def deactivate(self):
        """Deactivate the workflow"""
        self.status = 'inactive'
        self.save()

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['category', 'status']),
            models.Index(fields=['status', 'is_automated']),
            models.Index(fields=['trigger_event']),
            models.Index(fields=['next_run']),
        ]