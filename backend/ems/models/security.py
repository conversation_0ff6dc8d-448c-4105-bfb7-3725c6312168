"""
Security Domain Models for EMS
Contains security and compliance related models
"""

from django.db import models
from django.utils import timezone
from .base import BaseModel, NamedModel, AuditableModel


class UserSecurityProfile(BaseModel):
    """User security profiles"""
    
    user = models.OneToOneField('Employee', on_delete=models.CASCADE, related_name='security_profile')
    failed_login_attempts = models.IntegerField(default=0)
    last_failed_login = models.DateTimeField(null=True, blank=True)
    account_locked = models.BooleanField(default=False)
    locked_until = models.DateTimeField(null=True, blank=True)
    password_changed_at = models.DateTimeField(null=True, blank=True)
    two_factor_enabled = models.BooleanField(default=False)


class AuditTrail(BaseModel):
    """System audit trail"""
    
    user = models.ForeignKey('Employee', on_delete=models.CASCADE, related_name='audit_entries')
    action = models.Char<PERSON>ield(max_length=100)
    object_type = models.CharField(max_length=50)
    object_id = models.CharField(max_length=50)
    changes = models.JSONField(default=dict)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)


class SecurityIncident(BaseModel):
    """Security incident tracking"""
    
    SEVERITY_CHOICES = [
        ('LOW', 'Low'),
        ('MEDIUM', 'Medium'),
        ('HIGH', 'High'),
        ('CRITICAL', 'Critical'),
    ]
    
    title = models.CharField(max_length=200)
    description = models.TextField()
    severity = models.CharField(max_length=20, choices=SEVERITY_CHOICES)
    status = models.CharField(max_length=50, default='OPEN')
    reported_by = models.ForeignKey('Employee', on_delete=models.CASCADE)
    assigned_to = models.ForeignKey(
        'Employee', 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='assigned_incidents'
    )


class ComplianceFramework(NamedModel):
    """Compliance frameworks"""
    
    version = models.CharField(max_length=50)
    effective_date = models.DateField()
    is_active = models.BooleanField(default=True)


class ComplianceControl(BaseModel):
    """Compliance controls"""
    
    framework = models.ForeignKey(ComplianceFramework, on_delete=models.CASCADE, related_name='controls')
    control_id = models.CharField(max_length=50)
    title = models.CharField(max_length=200)
    description = models.TextField()
    is_implemented = models.BooleanField(default=False)


class DataClassification(BaseModel):
    """Data classification levels"""
    
    CLASSIFICATION_LEVELS = [
        ('PUBLIC', 'Public'),
        ('INTERNAL', 'Internal'),
        ('CONFIDENTIAL', 'Confidential'),
        ('RESTRICTED', 'Restricted'),
    ]
    
    name = models.CharField(max_length=100)
    level = models.CharField(max_length=20, choices=CLASSIFICATION_LEVELS)
    description = models.TextField()
    handling_requirements = models.TextField()


class SecurityAlert(BaseModel):
    """Security alerts"""
    
    alert_type = models.CharField(max_length=50)
    message = models.TextField()
    severity = models.CharField(max_length=20)
    is_resolved = models.BooleanField(default=False)
    resolved_at = models.DateTimeField(null=True, blank=True)
