"""
EMS Infrastructure Monitoring Dashboard
Provides comprehensive monitoring for database, API, and security metrics
"""

import time
import psutil
import logging
from django.db import connection
from django.core.cache import cache
from django.conf import settings
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import user_passes_test
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAdminUser
from rest_framework.response import Response
from rest_framework import status

logger = logging.getLogger(__name__)

def is_superuser(user):
    """Check if user is superuser for monitoring access"""
    return user.is_authenticated and user.is_superuser

class SystemMonitor:
    """System monitoring utilities"""
    
    @staticmethod
    def get_database_metrics():
        """Get database performance metrics"""
        try:
            start_time = time.time()
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                db_response_time = (time.time() - start_time) * 1000
            
            # Get connection info
            db_vendor = connection.vendor
            
            # Get query count if DEBUG is enabled
            query_count = len(connection.queries) if settings.DEBUG else 0
            
            return {
                'status': 'healthy',
                'response_time_ms': round(db_response_time, 2),
                'vendor': db_vendor,
                'query_count': query_count,
                'connection_status': 'connected'
            }
        except Exception as e:
            logger.error(f"Database metrics error: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'connection_status': 'disconnected'
            }
    
    @staticmethod
    def get_system_metrics():
        """Get system performance metrics"""
        try:
            # CPU and Memory
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # Process info
            process = psutil.Process()
            process_memory = process.memory_info()
            
            return {
                'status': 'healthy',
                'cpu_percent': cpu_percent,
                'memory': {
                    'total_gb': round(memory.total / (1024**3), 2),
                    'available_gb': round(memory.available / (1024**3), 2),
                    'percent_used': memory.percent
                },
                'disk': {
                    'total_gb': round(disk.total / (1024**3), 2),
                    'free_gb': round(disk.free / (1024**3), 2),
                    'percent_used': round((disk.used / disk.total) * 100, 1)
                },
                'process': {
                    'memory_mb': round(process_memory.rss / (1024**2), 2),
                    'pid': process.pid
                }
            }
        except Exception as e:
            logger.error(f"System metrics error: {e}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    @staticmethod
    def get_api_metrics():
        """Get API performance metrics"""
        try:
            # Test API endpoints
            from django.test import Client
            client = Client()
            
            endpoints_to_test = [
                '/api/health/',
                '/api/v1/departments/',
                '/api/auth/csrf/'
            ]
            
            api_metrics = []
            total_response_time = 0
            successful_requests = 0
            
            for endpoint in endpoints_to_test:
                start_time = time.time()
                try:
                    response = client.get(endpoint)
                    response_time = (time.time() - start_time) * 1000
                    total_response_time += response_time
                    
                    if response.status_code < 500:
                        successful_requests += 1
                    
                    api_metrics.append({
                        'endpoint': endpoint,
                        'status_code': response.status_code,
                        'response_time_ms': round(response_time, 2),
                        'status': 'healthy' if response.status_code < 500 else 'error'
                    })
                except Exception as e:
                    api_metrics.append({
                        'endpoint': endpoint,
                        'status': 'error',
                        'error': str(e)
                    })
            
            avg_response_time = total_response_time / len(endpoints_to_test) if endpoints_to_test else 0
            success_rate = (successful_requests / len(endpoints_to_test)) * 100 if endpoints_to_test else 0
            
            return {
                'status': 'healthy' if success_rate >= 80 else 'degraded',
                'endpoints': api_metrics,
                'average_response_time_ms': round(avg_response_time, 2),
                'success_rate_percent': round(success_rate, 1),
                'total_endpoints_tested': len(endpoints_to_test)
            }
        except Exception as e:
            logger.error(f"API metrics error: {e}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    @staticmethod
    def get_security_metrics():
        """Get security monitoring metrics"""
        try:
            # Check security settings
            security_score = 0
            total_checks = 0
            
            security_checks = [
                ('DEBUG disabled', not settings.DEBUG),
                ('SECURE_SSL_REDIRECT', getattr(settings, 'SECURE_SSL_REDIRECT', False)),
                ('SESSION_COOKIE_SECURE', getattr(settings, 'SESSION_COOKIE_SECURE', False)),
                ('CSRF_COOKIE_SECURE', getattr(settings, 'CSRF_COOKIE_SECURE', False)),
                ('SECURE_HSTS_SECONDS', getattr(settings, 'SECURE_HSTS_SECONDS', 0) > 0),
            ]
            
            security_details = []
            for check_name, is_secure in security_checks:
                total_checks += 1
                if is_secure:
                    security_score += 1
                
                security_details.append({
                    'check': check_name,
                    'status': 'pass' if is_secure else 'fail',
                    'secure': is_secure
                })
            
            security_percentage = (security_score / total_checks) * 100 if total_checks > 0 else 0
            
            return {
                'status': 'healthy' if security_percentage >= 80 else 'warning',
                'security_score_percent': round(security_percentage, 1),
                'checks_passed': security_score,
                'total_checks': total_checks,
                'details': security_details
            }
        except Exception as e:
            logger.error(f"Security metrics error: {e}")
            return {
                'status': 'error',
                'error': str(e)
            }

@api_view(['GET'])
@permission_classes([IsAdminUser])
def monitoring_dashboard_view(request):
    """
    Comprehensive monitoring dashboard endpoint
    Requires admin permissions
    """
    try:
        monitor = SystemMonitor()
        
        # Collect all metrics
        metrics = {
            'timestamp': time.time(),
            'database': monitor.get_database_metrics(),
            'system': monitor.get_system_metrics(),
            'api': monitor.get_api_metrics(),
            'security': monitor.get_security_metrics()
        }
        
        # Calculate overall health
        component_statuses = [
            metrics['database']['status'],
            metrics['system']['status'],
            metrics['api']['status'],
            metrics['security']['status']
        ]
        
        healthy_components = sum(1 for status in component_statuses if status == 'healthy')
        total_components = len(component_statuses)
        overall_health_percent = (healthy_components / total_components) * 100
        
        if overall_health_percent >= 80:
            overall_status = 'healthy'
        elif overall_health_percent >= 60:
            overall_status = 'degraded'
        else:
            overall_status = 'critical'
        
        metrics['overall'] = {
            'status': overall_status,
            'health_percent': round(overall_health_percent, 1),
            'healthy_components': healthy_components,
            'total_components': total_components
        }
        
        return Response(metrics, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Monitoring dashboard error: {e}")
        return Response({
            'error': 'Monitoring dashboard unavailable',
            'message': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@require_http_methods(["GET"])
@user_passes_test(is_superuser)
def system_health_check(request):
    """
    Quick system health check for load balancers
    """
    try:
        # Quick database check
        start_time = time.time()
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
        db_time = (time.time() - start_time) * 1000
        
        # Quick system check
        cpu_percent = psutil.cpu_percent()
        memory_percent = psutil.virtual_memory().percent
        
        # Determine health status
        if db_time > 1000 or cpu_percent > 90 or memory_percent > 90:
            status_code = 503  # Service Unavailable
            health_status = 'unhealthy'
        else:
            status_code = 200
            health_status = 'healthy'
        
        return JsonResponse({
            'status': health_status,
            'database_response_ms': round(db_time, 2),
            'cpu_percent': cpu_percent,
            'memory_percent': memory_percent,
            'timestamp': time.time()
        }, status=status_code)
        
    except Exception as e:
        logger.error(f"Health check error: {e}")
        return JsonResponse({
            'status': 'unhealthy',
            'error': str(e)
        }, status=503)
