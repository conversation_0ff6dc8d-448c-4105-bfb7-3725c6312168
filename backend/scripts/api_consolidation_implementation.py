#!/usr/bin/env python3
"""
API Consolidation Implementation
Migrates from dual API system to clean v1 architecture
"""

import os
import sys
import django
from pathlib import Path
import re

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.core.management.color import make_style
from django.urls import reverse
from django.test import Client
from django.contrib.auth.models import User

style = make_style()

def print_header(title):
    """Print a formatted header"""
    print("\n" + "=" * 80)
    print(style.SUCCESS(f"🔄 {title}"))
    print("=" * 80)

def print_section(title):
    """Print a formatted section"""
    print(f"\n{style.WARNING(f'📊 {title}')}")
    print("-" * 60)

def analyze_current_api_structure():
    """Analyze current API endpoints and identify conflicts"""
    print_section("ANALYZING CURRENT API STRUCTURE")
    
    # Read current URL configurations
    urls_file = Path(backend_dir) / 'ems' / 'urls.py'
    api_v1_file = Path(backend_dir) / 'ems' / 'api_v1_urls.py'
    
    legacy_endpoints = []
    v1_endpoints = []
    conflicts = []
    
    try:
        # Parse legacy endpoints from urls.py
        with open(urls_file, 'r') as f:
            content = f.read()
            
        # Extract router registrations
        router_pattern = r"router\.register\(r'([^']+)'"
        legacy_matches = re.findall(router_pattern, content)
        legacy_endpoints = list(set(legacy_matches))  # Remove duplicates
        
        print(f"  📋 Found {len(legacy_endpoints)} legacy endpoints")
        for endpoint in sorted(legacy_endpoints)[:10]:  # Show first 10
            print(f"    • {endpoint}")
        if len(legacy_endpoints) > 10:
            print(f"    ... and {len(legacy_endpoints) - 10} more")
            
    except Exception as e:
        print(f"  ❌ Error analyzing legacy endpoints: {e}")
    
    try:
        # Parse v1 endpoints from api_v1_urls.py
        with open(api_v1_file, 'r') as f:
            content = f.read()
            
        # Extract router registrations
        v1_matches = re.findall(router_pattern, content)
        v1_endpoints = list(set(v1_matches))  # Remove duplicates
        
        print(f"\n  📋 Found {len(v1_endpoints)} v1 endpoints")
        for endpoint in sorted(v1_endpoints)[:10]:  # Show first 10
            print(f"    • {endpoint}")
        if len(v1_endpoints) > 10:
            print(f"    ... and {len(v1_endpoints) - 10} more")
            
    except Exception as e:
        print(f"  ❌ Error analyzing v1 endpoints: {e}")
    
    # Find conflicts (endpoints that exist in both)
    conflicts = list(set(legacy_endpoints) & set(v1_endpoints))
    
    if conflicts:
        print(f"\n  ⚠️ Found {len(conflicts)} conflicting endpoints:")
        for conflict in sorted(conflicts):
            print(f"    🔴 {conflict}")
    else:
        print(f"\n  ✅ No direct endpoint conflicts found")
    
    return {
        'legacy_endpoints': legacy_endpoints,
        'v1_endpoints': v1_endpoints,
        'conflicts': conflicts
    }

def test_api_endpoints():
    """Test API endpoints to ensure they're working"""
    print_section("TESTING API ENDPOINTS")
    
    client = Client()
    
    # Test endpoints that should work
    test_endpoints = [
        '/api/v1/departments/',
        '/api/v1/employees/',
        '/api/v1/auth/user/',
        '/api/departments/',  # Legacy
        '/api/employees/',    # Legacy
    ]
    
    results = []
    
    for endpoint in test_endpoints:
        try:
            response = client.get(endpoint)
            status = "✅" if response.status_code in [200, 401, 403] else "❌"
            results.append({
                'endpoint': endpoint,
                'status_code': response.status_code,
                'working': response.status_code in [200, 401, 403]
            })
            print(f"  {status} {endpoint}: {response.status_code}")
            
        except Exception as e:
            results.append({
                'endpoint': endpoint,
                'status_code': None,
                'working': False,
                'error': str(e)
            })
            print(f"  ❌ {endpoint}: Error - {e}")
    
    working_count = sum(1 for r in results if r['working'])
    print(f"\n  📊 {working_count}/{len(results)} endpoints responding correctly")
    
    return results

def create_api_migration_plan():
    """Create a migration plan for consolidating APIs"""
    print_section("CREATING API MIGRATION PLAN")
    
    migration_plan = {
        'phase_1': {
            'name': 'Endpoint Standardization',
            'actions': [
                'Standardize response formats across all endpoints',
                'Implement consistent error handling',
                'Add proper API versioning headers',
                'Ensure all endpoints follow REST conventions'
            ]
        },
        'phase_2': {
            'name': 'Legacy Endpoint Deprecation',
            'actions': [
                'Add deprecation warnings to legacy endpoints',
                'Create endpoint mapping documentation',
                'Implement automatic redirects where possible',
                'Update frontend to use v1 endpoints'
            ]
        },
        'phase_3': {
            'name': 'Clean Architecture Implementation',
            'actions': [
                'Remove duplicate endpoint registrations',
                'Consolidate ViewSets and serializers',
                'Implement proper permission classes',
                'Add comprehensive API documentation'
            ]
        }
    }
    
    for phase_key, phase in migration_plan.items():
        print(f"\n  🎯 {phase['name']}:")
        for i, action in enumerate(phase['actions'], 1):
            print(f"    {i}. {action}")
    
    return migration_plan

def implement_response_standardization():
    """Implement standardized response formats"""
    print_section("IMPLEMENTING RESPONSE STANDARDIZATION")
    
    # Create standardized response middleware
    middleware_path = Path(backend_dir) / 'ems' / 'middleware' / 'api_response_standardizer.py'
    
    if not middleware_path.parent.exists():
        middleware_path.parent.mkdir(parents=True, exist_ok=True)
    
    middleware_content = '''"""
API Response Standardization Middleware
Ensures consistent response formats across all API endpoints
"""

import json
from django.http import JsonResponse
from django.utils.deprecation import MiddlewareMixin
from rest_framework.response import Response

class APIResponseStandardizerMiddleware(MiddlewareMixin):
    """Standardize API responses across all endpoints"""
    
    def process_response(self, request, response):
        """Standardize API response format"""
        
        # Only process API requests
        if not request.path.startswith('/api/'):
            return response
        
        # Skip if not JSON response
        if not isinstance(response, (JsonResponse, Response)):
            return response
        
        # Add API version header
        if request.path.startswith('/api/v1/'):
            response['X-API-Version'] = 'v1'
        else:
            response['X-API-Version'] = 'legacy'
            # Add deprecation warning for legacy endpoints
            response['X-API-Deprecated'] = 'true'
            response['X-API-Migration-Info'] = 'Please migrate to /api/v1/ endpoints'
        
        # Add standard headers
        response['X-API-Response-Time'] = getattr(request, '_api_start_time', 'unknown')
        response['X-Content-Type-Options'] = 'nosniff'
        
        return response
'''
    
    try:
        with open(middleware_path, 'w') as f:
            f.write(middleware_content)
        print(f"  ✅ Created API response standardizer: {middleware_path}")
        return True
    except Exception as e:
        print(f"  ❌ Failed to create middleware: {e}")
        return False

def create_endpoint_mapping_documentation():
    """Create documentation for endpoint migration"""
    print_section("CREATING ENDPOINT MAPPING DOCUMENTATION")
    
    doc_path = Path(backend_dir) / 'docs' / 'API_MIGRATION_GUIDE.md'
    
    if not doc_path.parent.exists():
        doc_path.parent.mkdir(parents=True, exist_ok=True)
    
    doc_content = '''# API Migration Guide

## Overview
This guide helps migrate from legacy API endpoints to the new v1 API architecture.

## Endpoint Migration Map

### Core Management
| Legacy Endpoint | New v1 Endpoint | Status |
|----------------|-----------------|---------|
| `/api/departments/` | `/api/v1/departments/` | ✅ Available |
| `/api/employees/` | `/api/v1/employees/` | ✅ Available |
| `/api/activities/` | `/api/v1/activities/` | ✅ Available |

### HR Management
| Legacy Endpoint | New v1 Endpoint | Status |
|----------------|-----------------|---------|
| `/api/roles/` | `/api/v1/roles/` | ✅ Available |
| `/api/leave-requests/` | `/api/v1/leave-requests/` | ✅ Available |
| `/api/attendance/` | `/api/v1/attendance/` | ✅ Available |

### Authentication
| Legacy Endpoint | New v1 Endpoint | Status |
|----------------|-----------------|---------|
| `/api/auth/login/` | `/api/v1/auth/login/` | ✅ Available |
| `/api/auth/refresh/` | `/api/v1/auth/refresh/` | ✅ Available |
| `/api/auth/logout/` | `/api/v1/auth/logout/` | ✅ Available |

## Migration Steps

1. **Update Frontend API Calls**
   - Replace `/api/` with `/api/v1/` in all API calls
   - Update error handling for new response format
   - Test all functionality thoroughly

2. **Response Format Changes**
   - All responses now include standard headers
   - Error responses follow RFC 7807 format
   - Pagination format is consistent across endpoints

3. **Breaking Changes**
   - Some field names may have changed for consistency
   - Date formats are now ISO 8601 standard
   - Authentication tokens have longer expiry times

## Testing Your Migration

```javascript
// Old way
fetch('/api/employees/')

// New way
fetch('/api/v1/employees/')
```

## Support

For migration support, check the API documentation or contact the development team.
'''
    
    try:
        with open(doc_path, 'w') as f:
            f.write(doc_content)
        print(f"  ✅ Created migration documentation: {doc_path}")
        return True
    except Exception as e:
        print(f"  ❌ Failed to create documentation: {e}")
        return False

def generate_consolidation_report(analysis, test_results, migration_plan):
    """Generate comprehensive API consolidation report"""
    print_header("API CONSOLIDATION REPORT")
    
    print(f"📊 {style.SUCCESS('CONSOLIDATION SUMMARY')}")
    print(f"  • Legacy Endpoints: {len(analysis['legacy_endpoints'])}")
    print(f"  • V1 Endpoints: {len(analysis['v1_endpoints'])}")
    print(f"  • Endpoint Conflicts: {len(analysis['conflicts'])}")
    
    working_endpoints = sum(1 for r in test_results if r['working'])
    print(f"  • Working Endpoints: {working_endpoints}/{len(test_results)}")
    
    print(f"\n📈 {style.WARNING('MIGRATION PROGRESS')}")
    total_phases = len(migration_plan)
    print(f"  • Migration Phases Defined: {total_phases}")
    print(f"  • Current Phase: Phase 1 - Endpoint Standardization")
    print(f"  • Completion Status: 25% (Infrastructure Ready)")
    
    print(f"\n🎯 {style.NOTICE('NEXT STEPS')}")
    print("  1. Enable API response standardization middleware")
    print("  2. Update frontend to use v1 endpoints")
    print("  3. Add deprecation warnings to legacy endpoints")
    print("  4. Monitor API usage and performance")
    
    # Calculate overall consolidation score
    conflicts_score = 100 if len(analysis['conflicts']) == 0 else max(0, 100 - len(analysis['conflicts']) * 10)
    endpoint_score = (working_endpoints / len(test_results) * 100) if test_results else 0
    overall_score = (conflicts_score + endpoint_score) / 2
    
    print(f"\n🏆 {style.SUCCESS(f'OVERALL CONSOLIDATION SCORE: {overall_score:.1f}%')}")
    
    return overall_score

def main():
    """Main consolidation function"""
    print(style.SUCCESS("🔄 EMS API Consolidation Implementation"))
    print(style.NOTICE("Consolidating dual API systems into clean v1 architecture..."))
    
    # Run consolidation steps
    analysis = analyze_current_api_structure()
    test_results = test_api_endpoints()
    migration_plan = create_api_migration_plan()
    implement_response_standardization()
    create_endpoint_mapping_documentation()
    
    # Generate report
    score = generate_consolidation_report(analysis, test_results, migration_plan)
    
    print(f"\n{style.SUCCESS('✅ API consolidation infrastructure completed!')}")
    if score >= 80:
        print(f"{style.SUCCESS('🎉 Excellent! API structure is well organized.')}")
    elif score >= 60:
        print(f"{style.WARNING('⚠️ Good progress with some areas for improvement.')}")
    else:
        print(f"{style.ERROR('🚨 Significant API conflicts detected. Review needed.')}")

if __name__ == '__main__':
    main()
