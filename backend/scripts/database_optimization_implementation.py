#!/usr/bin/env python3
"""
Database Optimization Implementation
Implements database performance optimizations for the EMS system
"""

import os
import sys
import django
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.db import connection, models
from django.apps import apps
from django.core.management.color import make_style
from django.core.management import call_command
from django.db import transaction
import time

style = make_style()

def print_header(title):
    """Print a formatted header"""
    print("\n" + "=" * 80)
    print(style.SUCCESS(f"🚀 {title}"))
    print("=" * 80)

def print_section(title):
    """Print a formatted section"""
    print(f"\n{style.WARNING(f'📊 {title}')}")
    print("-" * 60)

def create_missing_indexes():
    """Create missing indexes for foreign keys and commonly queried fields"""
    print_section("CREATING MISSING INDEXES")
    
    indexes_created = []
    
    # Define critical indexes for performance
    critical_indexes = [
        # Employee-related indexes
        {
            'table': 'ems_employee',
            'columns': ['department_id'],
            'name': 'idx_employee_department'
        },
        {
            'table': 'ems_employee',
            'columns': ['user_id'],
            'name': 'idx_employee_user'
        },
        {
            'table': 'ems_employee',
            'columns': ['is_active', 'department_id'],
            'name': 'idx_employee_active_dept'
        },
        
        # Attendance indexes
        {
            'table': 'ems_attendance',
            'columns': ['employee_id', 'date'],
            'name': 'idx_attendance_employee_date'
        },
        {
            'table': 'ems_attendance',
            'columns': ['date'],
            'name': 'idx_attendance_date'
        },
        
        # KPI indexes
        {
            'table': 'ems_kpivalue',
            'columns': ['kpi_id', 'period_start'],
            'name': 'idx_kpivalue_kpi_period'
        },
        {
            'table': 'ems_kpivalue',
            'columns': ['employee_id', 'period_start'],
            'name': 'idx_kpivalue_employee_period'
        },
        
        # Financial indexes
        {
            'table': 'ems_customerinvoice',
            'columns': ['invoice_date', 'status'],
            'name': 'idx_invoice_date_status'
        },
        {
            'table': 'ems_expense',
            'columns': ['date', 'employee_id'],
            'name': 'idx_expense_date_employee'
        },
        
        # Audit trail indexes
        {
            'table': 'ems_audittrail',
            'columns': ['timestamp', 'user_id'],
            'name': 'idx_audit_timestamp_user'
        },
        {
            'table': 'ems_audittrail',
            'columns': ['action', 'timestamp'],
            'name': 'idx_audit_action_timestamp'
        },
        
        # Project and task indexes
        {
            'table': 'ems_task',
            'columns': ['project_id', 'status'],
            'name': 'idx_task_project_status'
        },
        {
            'table': 'ems_task',
            'columns': ['assigned_to_id', 'due_date'],
            'name': 'idx_task_assignee_due'
        }
    ]
    
    with connection.cursor() as cursor:
        for index in critical_indexes:
            try:
                # Check if index already exists
                if connection.vendor == 'sqlite':
                    cursor.execute("""
                        SELECT name FROM sqlite_master
                        WHERE type='index' AND name=?
                    """, [index['name']])
                    
                    if not cursor.fetchone():
                        # Create index
                        columns_str = ', '.join(index['columns'])
                        sql = f"CREATE INDEX {index['name']} ON {index['table']} ({columns_str})"
                        cursor.execute(sql)
                        indexes_created.append(index['name'])
                        print(f"  ✅ Created index: {index['name']}")
                    else:
                        print(f"  ⏭️ Index already exists: {index['name']}")
                        
            except Exception as e:
                print(f"  ❌ Failed to create index {index['name']}: {e}")
    
    print(f"\n📊 Created {len(indexes_created)} new indexes")
    return indexes_created

def optimize_database_settings():
    """Optimize database settings for better performance"""
    print_section("OPTIMIZING DATABASE SETTINGS")
    
    optimizations = []
    
    with connection.cursor() as cursor:
        try:
            if connection.vendor == 'sqlite':
                # SQLite optimizations
                optimizations_sql = [
                    ("PRAGMA journal_mode = WAL", "Enable WAL mode for better concurrency"),
                    ("PRAGMA synchronous = NORMAL", "Balance safety and performance"),
                    ("PRAGMA cache_size = 10000", "Increase cache size"),
                    ("PRAGMA temp_store = MEMORY", "Store temp tables in memory"),
                    ("PRAGMA mmap_size = 268435456", "Enable memory mapping (256MB)"),
                ]
                
                for sql, description in optimizations_sql:
                    try:
                        cursor.execute(sql)
                        optimizations.append(description)
                        print(f"  ✅ {description}")
                    except Exception as e:
                        print(f"  ❌ Failed: {description} - {e}")
                        
        except Exception as e:
            print(f"❌ Database optimization failed: {e}")
    
    return optimizations

def analyze_query_performance():
    """Analyze and optimize slow queries"""
    print_section("QUERY PERFORMANCE ANALYSIS")
    
    # Test common queries and measure performance
    test_queries = [
        {
            'name': 'Employee Count by Department',
            'query': 'SELECT department_id, COUNT(*) FROM ems_employee GROUP BY department_id',
            'expected_time': 0.1
        },
        {
            'name': 'Recent Attendance Records',
            'query': 'SELECT * FROM ems_attendance ORDER BY date DESC LIMIT 100',
            'expected_time': 0.05
        },
        {
            'name': 'Active KPI Values',
            'query': 'SELECT * FROM ems_kpivalue WHERE period_start >= date("now", "-30 days")',
            'expected_time': 0.1
        }
    ]
    
    performance_results = []
    
    with connection.cursor() as cursor:
        for test in test_queries:
            try:
                start_time = time.time()
                cursor.execute(test['query'])
                cursor.fetchall()
                execution_time = (time.time() - start_time) * 1000  # Convert to ms
                
                status = "✅" if execution_time <= test['expected_time'] * 1000 else "⚠️"
                print(f"  {status} {test['name']}: {execution_time:.2f}ms")
                
                performance_results.append({
                    'name': test['name'],
                    'time': execution_time,
                    'expected': test['expected_time'] * 1000,
                    'status': 'good' if execution_time <= test['expected_time'] * 1000 else 'slow'
                })
                
            except Exception as e:
                print(f"  ❌ {test['name']}: Failed - {e}")
                performance_results.append({
                    'name': test['name'],
                    'time': None,
                    'expected': test['expected_time'] * 1000,
                    'status': 'error',
                    'error': str(e)
                })
    
    return performance_results

def implement_query_monitoring():
    """Implement query monitoring middleware"""
    print_section("IMPLEMENTING QUERY MONITORING")
    
    middleware_path = Path(backend_dir) / 'ems' / 'middleware' / 'query_monitor.py'
    
    if not middleware_path.parent.exists():
        middleware_path.parent.mkdir(parents=True, exist_ok=True)
    
    middleware_content = '''"""
Query Monitoring Middleware for EMS
Monitors database query performance and logs slow queries
"""

import time
import logging
from django.db import connection
from django.utils.deprecation import MiddlewareMixin
from django.conf import settings

logger = logging.getLogger(__name__)

class QueryMonitoringMiddleware(MiddlewareMixin):
    """Monitor database queries for performance issues"""
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.slow_query_threshold = getattr(settings, 'SLOW_QUERY_THRESHOLD', 0.5)  # 500ms
        
    def process_request(self, request):
        """Reset query count at start of request"""
        self.start_time = time.time()
        self.start_queries = len(connection.queries) if settings.DEBUG else 0
        
    def process_response(self, request, response):
        """Log query performance after request"""
        if settings.DEBUG:
            end_time = time.time()
            total_time = end_time - self.start_time
            query_count = len(connection.queries) - self.start_queries
            
            # Log slow requests
            if total_time > self.slow_query_threshold:
                logger.warning(
                    f"Slow request: {request.path} took {total_time:.3f}s "
                    f"with {query_count} queries"
                )
                
                # Log individual slow queries
                for query in connection.queries[self.start_queries:]:
                    query_time = float(query['time'])
                    if query_time > 0.1:  # 100ms threshold for individual queries
                        logger.warning(
                            f"Slow query ({query_time:.3f}s): {query['sql'][:200]}..."
                        )
            
            # Add performance headers for debugging
            response['X-DB-Query-Count'] = str(query_count)
            response['X-DB-Query-Time'] = f"{total_time:.3f}"
            
        return response
'''
    
    try:
        with open(middleware_path, 'w') as f:
            f.write(middleware_content)
        print(f"  ✅ Created query monitoring middleware: {middleware_path}")
        
        # Add instructions for enabling the middleware
        print(f"  📝 To enable, add to MIDDLEWARE in settings.py:")
        print(f"     'ems.middleware.query_monitor.QueryMonitoringMiddleware',")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Failed to create middleware: {e}")
        return False

def create_database_health_check():
    """Create database health check endpoint"""
    print_section("CREATING DATABASE HEALTH CHECK")
    
    health_check_path = Path(backend_dir) / 'ems' / 'health_checks.py'
    
    # Check if health checks already exist
    if health_check_path.exists():
        print(f"  ✅ Health checks already exist: {health_check_path}")
        return True
    
    return False

def generate_optimization_report(indexes_created, optimizations, performance_results):
    """Generate comprehensive optimization report"""
    print_header("DATABASE OPTIMIZATION REPORT")
    
    print(f"📊 {style.SUCCESS('OPTIMIZATION SUMMARY')}")
    print(f"  • Indexes Created: {len(indexes_created)}")
    print(f"  • Database Optimizations: {len(optimizations)}")
    print(f"  • Query Tests Performed: {len(performance_results)}")
    
    print(f"\n📈 {style.WARNING('PERFORMANCE RESULTS')}")
    for result in performance_results:
        if result['status'] == 'good':
            print(f"  ✅ {result['name']}: {result['time']:.2f}ms (target: {result['expected']:.0f}ms)")
        elif result['status'] == 'slow':
            print(f"  ⚠️ {result['name']}: {result['time']:.2f}ms (target: {result['expected']:.0f}ms)")
        else:
            print(f"  ❌ {result['name']}: Error - {result.get('error', 'Unknown')}")
    
    print(f"\n🎯 {style.NOTICE('NEXT STEPS')}")
    print("  1. Monitor query performance in production")
    print("  2. Consider implementing Redis caching for frequently accessed data")
    print("  3. Review and optimize N+1 query patterns in ViewSets")
    print("  4. Set up database monitoring and alerting")
    
    # Calculate overall performance score
    good_queries = sum(1 for r in performance_results if r['status'] == 'good')
    total_queries = len(performance_results)
    score = (good_queries / total_queries * 100) if total_queries > 0 else 0
    
    print(f"\n🏆 {style.SUCCESS(f'OVERALL PERFORMANCE SCORE: {score:.1f}%')}")
    
    return score

def main():
    """Main optimization function"""
    print(style.SUCCESS("🚀 EMS Database Optimization Implementation"))
    print(style.NOTICE("Implementing database performance optimizations..."))
    
    # Run optimizations
    indexes_created = create_missing_indexes()
    optimizations = optimize_database_settings()
    performance_results = analyze_query_performance()
    implement_query_monitoring()
    create_database_health_check()
    
    # Generate report
    score = generate_optimization_report(indexes_created, optimizations, performance_results)
    
    print(f"\n{style.SUCCESS('✅ Database optimization completed!')}")
    if score >= 80:
        print(f"{style.SUCCESS('🎉 Excellent performance! Database is well optimized.')}")
    elif score >= 60:
        print(f"{style.WARNING('⚠️ Good performance with room for improvement.')}")
    else:
        print(f"{style.ERROR('🚨 Performance issues detected. Review slow queries.')}")

if __name__ == '__main__':
    main()
