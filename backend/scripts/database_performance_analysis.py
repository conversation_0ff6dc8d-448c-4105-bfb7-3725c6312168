#!/usr/bin/env python3
"""
Database Performance Analysis Script
Analyzes current database performance, identifies missing indexes, and provides optimization recommendations
"""

import os
import sys
import django
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.db import connection, models
from django.apps import apps
from django.core.management.color import make_style
import time

style = make_style()

def print_header(title):
    """Print a formatted header"""
    print("\n" + "=" * 80)
    print(style.SUCCESS(f"🔍 {title}"))
    print("=" * 80)

def print_section(title):
    """Print a formatted section"""
    print(f"\n{style.WARNING(f'📊 {title}')}")
    print("-" * 60)

def analyze_database_connection():
    """Analyze database connection and basic info"""
    print_header("DATABASE CONNECTION ANALYSIS")
    
    try:
        with connection.cursor() as cursor:
            # Test connection speed
            start_time = time.time()
            cursor.execute("SELECT 1")
            connection_time = (time.time() - start_time) * 1000
            
            # Get database info
            cursor.execute("SELECT version()")
            db_version = cursor.fetchone()[0] if cursor.rowcount > 0 else "Unknown"
            
            # Get database size (PostgreSQL specific)
            try:
                cursor.execute("""
                    SELECT pg_size_pretty(pg_database_size(current_database())) as size
                """)
                db_size = cursor.fetchone()[0]
            except:
                db_size = "Unknown"
            
            print(f"✅ Database Engine: {connection.vendor}")
            print(f"✅ Database Version: {db_version}")
            print(f"✅ Database Size: {db_size}")
            print(f"✅ Connection Time: {connection_time:.2f}ms")
            
            return True
            
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def analyze_table_sizes():
    """Analyze table sizes and row counts"""
    print_section("TABLE SIZE ANALYSIS")
    
    try:
        with connection.cursor() as cursor:
            # Get table sizes (PostgreSQL specific)
            cursor.execute("""
                SELECT 
                    schemaname,
                    tablename,
                    attname,
                    n_distinct,
                    correlation
                FROM pg_stats 
                WHERE schemaname = 'public'
                ORDER BY n_distinct DESC
                LIMIT 20
            """)
            
            print("Top 20 tables by distinct values:")
            for row in cursor.fetchall():
                schema, table, column, distinct, correlation = row
                print(f"  📋 {table}.{column}: {distinct} distinct values")
                
    except Exception as e:
        print(f"⚠️ Could not analyze table sizes: {e}")
        
        # Fallback: Count rows in major tables
        major_models = [
            'Employee', 'Attendance', 'KPIValue', 'AuditTrail', 
            'CustomerInvoice', 'JournalEntry', 'Asset'
        ]
        
        for model_name in major_models:
            try:
                model = apps.get_model('ems', model_name)
                count = model.objects.count()
                print(f"  📋 {model_name}: {count:,} records")
            except:
                print(f"  ❌ {model_name}: Model not found")

def analyze_missing_indexes():
    """Analyze missing indexes on foreign keys"""
    print_section("MISSING INDEX ANALYSIS")
    
    missing_indexes = []
    
    # Get all models
    for model in apps.get_models():
        if model._meta.app_label == 'ems':
            table_name = model._meta.db_table
            
            # Check foreign key fields
            for field in model._meta.fields:
                if isinstance(field, models.ForeignKey):
                    column_name = field.column
                    
                    # Check if index exists
                    try:
                        with connection.cursor() as cursor:
                            cursor.execute("""
                                SELECT COUNT(*) 
                                FROM pg_indexes 
                                WHERE tablename = %s 
                                AND indexdef LIKE %s
                            """, [table_name, f'%{column_name}%'])
                            
                            index_count = cursor.fetchone()[0]
                            if index_count == 0:
                                missing_indexes.append({
                                    'table': table_name,
                                    'column': column_name,
                                    'field': field.name,
                                    'model': model.__name__
                                })
                    except:
                        # Fallback for non-PostgreSQL databases
                        missing_indexes.append({
                            'table': table_name,
                            'column': column_name,
                            'field': field.name,
                            'model': model.__name__
                        })
    
    if missing_indexes:
        print("🔴 Missing indexes on foreign keys:")
        for idx in missing_indexes[:10]:  # Show top 10
            print(f"  ❌ {idx['model']}.{idx['field']} ({idx['table']}.{idx['column']})")
    else:
        print("✅ All foreign keys appear to have indexes")
    
    return missing_indexes

def analyze_slow_queries():
    """Analyze slow queries if pg_stat_statements is available"""
    print_section("SLOW QUERY ANALYSIS")
    
    try:
        with connection.cursor() as cursor:
            # Check if pg_stat_statements extension is available
            cursor.execute("""
                SELECT EXISTS(
                    SELECT 1 FROM pg_extension WHERE extname = 'pg_stat_statements'
                )
            """)
            
            has_pg_stat = cursor.fetchone()[0]
            
            if has_pg_stat:
                cursor.execute("""
                    SELECT 
                        query,
                        calls,
                        total_time,
                        mean_time,
                        rows
                    FROM pg_stat_statements 
                    WHERE mean_time > 100
                    ORDER BY mean_time DESC 
                    LIMIT 10
                """)
                
                slow_queries = cursor.fetchall()
                if slow_queries:
                    print("🐌 Top 10 slowest queries (>100ms average):")
                    for query, calls, total_time, mean_time, rows in slow_queries:
                        print(f"  ⏱️ {mean_time:.2f}ms avg ({calls} calls): {query[:80]}...")
                else:
                    print("✅ No slow queries detected")
            else:
                print("⚠️ pg_stat_statements extension not available")
                
    except Exception as e:
        print(f"⚠️ Could not analyze slow queries: {e}")

def generate_optimization_recommendations(missing_indexes):
    """Generate optimization recommendations"""
    print_header("OPTIMIZATION RECOMMENDATIONS")
    
    recommendations = []
    
    # Missing indexes
    if missing_indexes:
        recommendations.append({
            'priority': 'HIGH',
            'category': 'Indexing',
            'issue': f'{len(missing_indexes)} missing foreign key indexes',
            'action': 'Add database indexes for foreign key columns',
            'impact': 'Significant query performance improvement'
        })
    
    # Connection pooling
    recommendations.append({
        'priority': 'MEDIUM',
        'category': 'Connection Management',
        'issue': 'Basic connection configuration',
        'action': 'Implement connection pooling with pgbouncer',
        'impact': 'Better connection management under load'
    })
    
    # Query monitoring
    recommendations.append({
        'priority': 'MEDIUM',
        'category': 'Monitoring',
        'issue': 'Limited query performance monitoring',
        'action': 'Enable pg_stat_statements and query logging',
        'impact': 'Better visibility into performance issues'
    })
    
    # Caching
    recommendations.append({
        'priority': 'LOW',
        'category': 'Caching',
        'issue': 'No Redis caching configured',
        'action': 'Implement Redis for session and query caching',
        'impact': 'Reduced database load for repeated queries'
    })
    
    for i, rec in enumerate(recommendations, 1):
        priority_color = style.ERROR if rec['priority'] == 'HIGH' else style.WARNING if rec['priority'] == 'MEDIUM' else style.NOTICE
        print(f"\n{i}. {priority_color(rec['priority'])} - {rec['category']}")
        print(f"   Issue: {rec['issue']}")
        print(f"   Action: {rec['action']}")
        print(f"   Impact: {rec['impact']}")

def main():
    """Main analysis function"""
    print(style.SUCCESS("🚀 EMS Database Performance Analysis"))
    print(style.NOTICE("Analyzing database performance and optimization opportunities..."))
    
    # Run analyses
    if not analyze_database_connection():
        return
    
    analyze_table_sizes()
    missing_indexes = analyze_missing_indexes()
    analyze_slow_queries()
    generate_optimization_recommendations(missing_indexes)
    
    print(f"\n{style.SUCCESS('✅ Analysis complete!')}")
    print(f"{style.NOTICE('📋 Review recommendations above and implement high-priority items first.')}")

if __name__ == '__main__':
    main()
