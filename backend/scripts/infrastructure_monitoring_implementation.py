#!/usr/bin/env python3
"""
Infrastructure Monitoring Implementation
Implements comprehensive monitoring for database, API performance, and security metrics
"""

import os
import sys
import django
from pathlib import Path
import time
import psutil
import json

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.core.management.color import make_style
from django.conf import settings
from django.test import Client
from django.db import connection
from django.core.cache import cache

style = make_style()

def print_header(title):
    """Print a formatted header"""
    print("\n" + "=" * 80)
    print(style.SUCCESS(f"📊 {title}"))
    print("=" * 80)

def print_section(title):
    """Print a formatted section"""
    print(f"\n{style.WARNING(f'📈 {title}')}")
    print("-" * 60)

def create_monitoring_dashboard():
    """Create comprehensive monitoring dashboard"""
    print_section("CREATING MONITORING DASHBOARD")
    
    dashboard_path = Path(backend_dir) / 'ems' / 'monitoring_dashboard.py'
    
    dashboard_content = '''"""
EMS Infrastructure Monitoring Dashboard
Provides comprehensive monitoring for database, API, and security metrics
"""

import time
import psutil
import logging
from django.db import connection
from django.core.cache import cache
from django.conf import settings
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import user_passes_test
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAdminUser
from rest_framework.response import Response
from rest_framework import status

logger = logging.getLogger(__name__)

def is_superuser(user):
    """Check if user is superuser for monitoring access"""
    return user.is_authenticated and user.is_superuser

class SystemMonitor:
    """System monitoring utilities"""
    
    @staticmethod
    def get_database_metrics():
        """Get database performance metrics"""
        try:
            start_time = time.time()
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                db_response_time = (time.time() - start_time) * 1000
            
            # Get connection info
            db_vendor = connection.vendor
            
            # Get query count if DEBUG is enabled
            query_count = len(connection.queries) if settings.DEBUG else 0
            
            return {
                'status': 'healthy',
                'response_time_ms': round(db_response_time, 2),
                'vendor': db_vendor,
                'query_count': query_count,
                'connection_status': 'connected'
            }
        except Exception as e:
            logger.error(f"Database metrics error: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'connection_status': 'disconnected'
            }
    
    @staticmethod
    def get_system_metrics():
        """Get system performance metrics"""
        try:
            # CPU and Memory
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # Process info
            process = psutil.Process()
            process_memory = process.memory_info()
            
            return {
                'status': 'healthy',
                'cpu_percent': cpu_percent,
                'memory': {
                    'total_gb': round(memory.total / (1024**3), 2),
                    'available_gb': round(memory.available / (1024**3), 2),
                    'percent_used': memory.percent
                },
                'disk': {
                    'total_gb': round(disk.total / (1024**3), 2),
                    'free_gb': round(disk.free / (1024**3), 2),
                    'percent_used': round((disk.used / disk.total) * 100, 1)
                },
                'process': {
                    'memory_mb': round(process_memory.rss / (1024**2), 2),
                    'pid': process.pid
                }
            }
        except Exception as e:
            logger.error(f"System metrics error: {e}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    @staticmethod
    def get_api_metrics():
        """Get API performance metrics"""
        try:
            # Test API endpoints
            from django.test import Client
            client = Client()
            
            endpoints_to_test = [
                '/api/health/',
                '/api/v1/departments/',
                '/api/auth/csrf/'
            ]
            
            api_metrics = []
            total_response_time = 0
            successful_requests = 0
            
            for endpoint in endpoints_to_test:
                start_time = time.time()
                try:
                    response = client.get(endpoint)
                    response_time = (time.time() - start_time) * 1000
                    total_response_time += response_time
                    
                    if response.status_code < 500:
                        successful_requests += 1
                    
                    api_metrics.append({
                        'endpoint': endpoint,
                        'status_code': response.status_code,
                        'response_time_ms': round(response_time, 2),
                        'status': 'healthy' if response.status_code < 500 else 'error'
                    })
                except Exception as e:
                    api_metrics.append({
                        'endpoint': endpoint,
                        'status': 'error',
                        'error': str(e)
                    })
            
            avg_response_time = total_response_time / len(endpoints_to_test) if endpoints_to_test else 0
            success_rate = (successful_requests / len(endpoints_to_test)) * 100 if endpoints_to_test else 0
            
            return {
                'status': 'healthy' if success_rate >= 80 else 'degraded',
                'endpoints': api_metrics,
                'average_response_time_ms': round(avg_response_time, 2),
                'success_rate_percent': round(success_rate, 1),
                'total_endpoints_tested': len(endpoints_to_test)
            }
        except Exception as e:
            logger.error(f"API metrics error: {e}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    @staticmethod
    def get_security_metrics():
        """Get security monitoring metrics"""
        try:
            # Check security settings
            security_score = 0
            total_checks = 0
            
            security_checks = [
                ('DEBUG disabled', not settings.DEBUG),
                ('SECURE_SSL_REDIRECT', getattr(settings, 'SECURE_SSL_REDIRECT', False)),
                ('SESSION_COOKIE_SECURE', getattr(settings, 'SESSION_COOKIE_SECURE', False)),
                ('CSRF_COOKIE_SECURE', getattr(settings, 'CSRF_COOKIE_SECURE', False)),
                ('SECURE_HSTS_SECONDS', getattr(settings, 'SECURE_HSTS_SECONDS', 0) > 0),
            ]
            
            security_details = []
            for check_name, is_secure in security_checks:
                total_checks += 1
                if is_secure:
                    security_score += 1
                
                security_details.append({
                    'check': check_name,
                    'status': 'pass' if is_secure else 'fail',
                    'secure': is_secure
                })
            
            security_percentage = (security_score / total_checks) * 100 if total_checks > 0 else 0
            
            return {
                'status': 'healthy' if security_percentage >= 80 else 'warning',
                'security_score_percent': round(security_percentage, 1),
                'checks_passed': security_score,
                'total_checks': total_checks,
                'details': security_details
            }
        except Exception as e:
            logger.error(f"Security metrics error: {e}")
            return {
                'status': 'error',
                'error': str(e)
            }

@api_view(['GET'])
@permission_classes([IsAdminUser])
def monitoring_dashboard_view(request):
    """
    Comprehensive monitoring dashboard endpoint
    Requires admin permissions
    """
    try:
        monitor = SystemMonitor()
        
        # Collect all metrics
        metrics = {
            'timestamp': time.time(),
            'database': monitor.get_database_metrics(),
            'system': monitor.get_system_metrics(),
            'api': monitor.get_api_metrics(),
            'security': monitor.get_security_metrics()
        }
        
        # Calculate overall health
        component_statuses = [
            metrics['database']['status'],
            metrics['system']['status'],
            metrics['api']['status'],
            metrics['security']['status']
        ]
        
        healthy_components = sum(1 for status in component_statuses if status == 'healthy')
        total_components = len(component_statuses)
        overall_health_percent = (healthy_components / total_components) * 100
        
        if overall_health_percent >= 80:
            overall_status = 'healthy'
        elif overall_health_percent >= 60:
            overall_status = 'degraded'
        else:
            overall_status = 'critical'
        
        metrics['overall'] = {
            'status': overall_status,
            'health_percent': round(overall_health_percent, 1),
            'healthy_components': healthy_components,
            'total_components': total_components
        }
        
        return Response(metrics, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Monitoring dashboard error: {e}")
        return Response({
            'error': 'Monitoring dashboard unavailable',
            'message': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@require_http_methods(["GET"])
@user_passes_test(is_superuser)
def system_health_check(request):
    """
    Quick system health check for load balancers
    """
    try:
        # Quick database check
        start_time = time.time()
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
        db_time = (time.time() - start_time) * 1000
        
        # Quick system check
        cpu_percent = psutil.cpu_percent()
        memory_percent = psutil.virtual_memory().percent
        
        # Determine health status
        if db_time > 1000 or cpu_percent > 90 or memory_percent > 90:
            status_code = 503  # Service Unavailable
            health_status = 'unhealthy'
        else:
            status_code = 200
            health_status = 'healthy'
        
        return JsonResponse({
            'status': health_status,
            'database_response_ms': round(db_time, 2),
            'cpu_percent': cpu_percent,
            'memory_percent': memory_percent,
            'timestamp': time.time()
        }, status=status_code)
        
    except Exception as e:
        logger.error(f"Health check error: {e}")
        return JsonResponse({
            'status': 'unhealthy',
            'error': str(e)
        }, status=503)
'''
    
    try:
        with open(dashboard_path, 'w') as f:
            f.write(dashboard_content)
        print(f"  ✅ Created monitoring dashboard: {dashboard_path}")
        return True
    except Exception as e:
        print(f"  ❌ Failed to create monitoring dashboard: {e}")
        return False

def create_alerting_system():
    """Create alerting system for critical issues"""
    print_section("CREATING ALERTING SYSTEM")
    
    alerting_path = Path(backend_dir) / 'ems' / 'alerting.py'
    
    alerting_content = '''"""
EMS Alerting System
Monitors system health and sends alerts for critical issues
"""

import logging
import time
from django.core.mail import send_mail
from django.conf import settings
from django.core.management.base import BaseCommand
from .monitoring_dashboard import SystemMonitor

logger = logging.getLogger(__name__)

class AlertManager:
    """Manages system alerts and notifications"""
    
    def __init__(self):
        self.alert_thresholds = {
            'database_response_time_ms': 1000,
            'cpu_percent': 80,
            'memory_percent': 85,
            'disk_percent': 90,
            'api_success_rate': 95,
            'security_score': 80
        }
        
        self.alert_cooldown = 300  # 5 minutes between same alerts
        self.last_alerts = {}
    
    def check_system_health(self):
        """Check system health and trigger alerts if needed"""
        monitor = SystemMonitor()
        
        # Get metrics
        db_metrics = monitor.get_database_metrics()
        system_metrics = monitor.get_system_metrics()
        api_metrics = monitor.get_api_metrics()
        security_metrics = monitor.get_security_metrics()
        
        alerts = []
        
        # Database alerts
        if db_metrics.get('status') == 'error':
            alerts.append({
                'type': 'critical',
                'component': 'database',
                'message': f"Database connection failed: {db_metrics.get('error', 'Unknown error')}"
            })
        elif db_metrics.get('response_time_ms', 0) > self.alert_thresholds['database_response_time_ms']:
            alerts.append({
                'type': 'warning',
                'component': 'database',
                'message': f"Database response time high: {db_metrics.get('response_time_ms')}ms"
            })
        
        # System alerts
        if system_metrics.get('status') == 'healthy':
            cpu_percent = system_metrics.get('cpu_percent', 0)
            memory_percent = system_metrics.get('memory', {}).get('percent_used', 0)
            disk_percent = system_metrics.get('disk', {}).get('percent_used', 0)
            
            if cpu_percent > self.alert_thresholds['cpu_percent']:
                alerts.append({
                    'type': 'warning',
                    'component': 'system',
                    'message': f"High CPU usage: {cpu_percent}%"
                })
            
            if memory_percent > self.alert_thresholds['memory_percent']:
                alerts.append({
                    'type': 'warning',
                    'component': 'system',
                    'message': f"High memory usage: {memory_percent}%"
                })
            
            if disk_percent > self.alert_thresholds['disk_percent']:
                alerts.append({
                    'type': 'critical',
                    'component': 'system',
                    'message': f"High disk usage: {disk_percent}%"
                })
        
        # API alerts
        if api_metrics.get('status') == 'error':
            alerts.append({
                'type': 'critical',
                'component': 'api',
                'message': f"API system error: {api_metrics.get('error', 'Unknown error')}"
            })
        elif api_metrics.get('success_rate_percent', 100) < self.alert_thresholds['api_success_rate']:
            alerts.append({
                'type': 'warning',
                'component': 'api',
                'message': f"API success rate low: {api_metrics.get('success_rate_percent')}%"
            })
        
        # Security alerts
        if security_metrics.get('status') == 'warning':
            security_score = security_metrics.get('security_score_percent', 0)
            if security_score < self.alert_thresholds['security_score']:
                alerts.append({
                    'type': 'warning',
                    'component': 'security',
                    'message': f"Security score low: {security_score}%"
                })
        
        # Send alerts
        for alert in alerts:
            self.send_alert(alert)
        
        return alerts
    
    def send_alert(self, alert):
        """Send alert notification"""
        alert_key = f"{alert['component']}_{alert['type']}"
        current_time = time.time()
        
        # Check cooldown
        if alert_key in self.last_alerts:
            if current_time - self.last_alerts[alert_key] < self.alert_cooldown:
                return  # Skip due to cooldown
        
        # Log alert
        log_level = logging.CRITICAL if alert['type'] == 'critical' else logging.WARNING
        logger.log(log_level, f"ALERT [{alert['type'].upper()}] {alert['component']}: {alert['message']}")
        
        # Send email if configured
        if hasattr(settings, 'ALERT_EMAIL_RECIPIENTS') and settings.ALERT_EMAIL_RECIPIENTS:
            try:
                subject = f"EMS Alert [{alert['type'].upper()}] - {alert['component']}"
                message = f"""
EMS System Alert

Component: {alert['component']}
Type: {alert['type']}
Message: {alert['message']}
Timestamp: {time.strftime('%Y-%m-%d %H:%M:%S')}

Please check the system monitoring dashboard for more details.
"""
                
                send_mail(
                    subject,
                    message,
                    settings.DEFAULT_FROM_EMAIL,
                    settings.ALERT_EMAIL_RECIPIENTS,
                    fail_silently=False
                )
                
                logger.info(f"Alert email sent for {alert_key}")
                
            except Exception as e:
                logger.error(f"Failed to send alert email: {e}")
        
        # Update last alert time
        self.last_alerts[alert_key] = current_time

class Command(BaseCommand):
    """Management command to run health checks"""
    help = 'Run system health checks and send alerts'
    
    def handle(self, *args, **options):
        alert_manager = AlertManager()
        alerts = alert_manager.check_system_health()
        
        if alerts:
            self.stdout.write(f"Generated {len(alerts)} alerts")
            for alert in alerts:
                self.stdout.write(f"  {alert['type']}: {alert['message']}")
        else:
            self.stdout.write("No alerts generated - system healthy")
'''
    
    try:
        with open(alerting_path, 'w') as f:
            f.write(alerting_content)
        print(f"  ✅ Created alerting system: {alerting_path}")
        return True
    except Exception as e:
        print(f"  ❌ Failed to create alerting system: {e}")
        return False

def test_monitoring_system():
    """Test the monitoring system"""
    print_section("TESTING MONITORING SYSTEM")
    
    try:
        # Import and test the monitoring dashboard
        from ems.monitoring_dashboard import SystemMonitor
        
        monitor = SystemMonitor()
        
        # Test each monitoring component
        print(f"  📊 Testing database metrics...")
        db_metrics = monitor.get_database_metrics()
        db_status = "✅" if db_metrics['status'] == 'healthy' else "❌"
        print(f"    {db_status} Database: {db_metrics['status']} ({db_metrics.get('response_time_ms', 'N/A')}ms)")
        
        print(f"  📊 Testing system metrics...")
        system_metrics = monitor.get_system_metrics()
        sys_status = "✅" if system_metrics['status'] == 'healthy' else "❌"
        print(f"    {sys_status} System: {system_metrics['status']} (CPU: {system_metrics.get('cpu_percent', 'N/A')}%)")
        
        print(f"  📊 Testing API metrics...")
        api_metrics = monitor.get_api_metrics()
        api_status = "✅" if api_metrics['status'] in ['healthy', 'degraded'] else "❌"
        print(f"    {api_status} API: {api_metrics['status']} (Success: {api_metrics.get('success_rate_percent', 'N/A')}%)")
        
        print(f"  📊 Testing security metrics...")
        security_metrics = monitor.get_security_metrics()
        sec_status = "✅" if security_metrics['status'] in ['healthy', 'warning'] else "❌"
        print(f"    {sec_status} Security: {security_metrics['status']} (Score: {security_metrics.get('security_score_percent', 'N/A')}%)")
        
        # Calculate overall success
        all_metrics = [db_metrics, system_metrics, api_metrics, security_metrics]
        working_components = sum(1 for m in all_metrics if m['status'] in ['healthy', 'degraded', 'warning'])
        
        return {
            'working_components': working_components,
            'total_components': len(all_metrics),
            'success_rate': (working_components / len(all_metrics)) * 100
        }
        
    except Exception as e:
        print(f"  ❌ Monitoring system test failed: {e}")
        return {
            'working_components': 0,
            'total_components': 4,
            'success_rate': 0,
            'error': str(e)
        }

def generate_monitoring_report(dashboard_created, alerting_created, test_results):
    """Generate comprehensive monitoring implementation report"""
    print_header("INFRASTRUCTURE MONITORING REPORT")
    
    print(f"📊 {style.SUCCESS('IMPLEMENTATION SUMMARY')}")
    print(f"  • Monitoring Dashboard: {'✅ Created' if dashboard_created else '❌ Failed'}")
    print(f"  • Alerting System: {'✅ Created' if alerting_created else '❌ Failed'}")
    print(f"  • Working Components: {test_results['working_components']}/{test_results['total_components']}")
    print(f"  • System Success Rate: {test_results['success_rate']:.1f}%")
    
    if test_results.get('error'):
        print(f"  ⚠️ Test Error: {test_results['error']}")
    
    # Calculate implementation score
    implementation_score = 0
    if dashboard_created:
        implementation_score += 40
    if alerting_created:
        implementation_score += 30
    implementation_score += (test_results['success_rate'] * 0.3)
    
    print(f"\n🎯 {style.NOTICE('MONITORING CAPABILITIES')}")
    print("  • Real-time system health monitoring")
    print("  • Database performance tracking")
    print("  • API endpoint monitoring")
    print("  • Security configuration monitoring")
    print("  • Automated alerting system")
    print("  • Admin dashboard for metrics")
    
    print(f"\n🏆 {style.SUCCESS(f'OVERALL MONITORING SCORE: {implementation_score:.1f}%')}")
    
    # Recommendations
    print(f"\n🎯 {style.NOTICE('NEXT STEPS')}")
    if implementation_score >= 80:
        print("  ✅ Excellent monitoring infrastructure!")
        print("  • Set up automated health check cron jobs")
        print("  • Configure email alerts for production")
        print("  • Add custom metrics for business KPIs")
    elif implementation_score >= 60:
        print("  ⚠️ Good monitoring with areas for improvement:")
        if not dashboard_created:
            print("  • Fix monitoring dashboard creation")
        if not alerting_created:
            print("  • Fix alerting system setup")
        if test_results['success_rate'] < 80:
            print("  • Address failing monitoring components")
    else:
        print("  🚨 Critical monitoring issues:")
        print("  • Review monitoring system implementation")
        print("  • Check dependencies and permissions")
        print("  • Verify system resources")
    
    return implementation_score

def main():
    """Main monitoring implementation function"""
    print(style.SUCCESS("📊 EMS Infrastructure Monitoring Implementation"))
    print(style.NOTICE("Implementing comprehensive monitoring and alerting..."))
    
    # Run implementation steps
    dashboard_created = create_monitoring_dashboard()
    alerting_created = create_alerting_system()
    test_results = test_monitoring_system()
    
    # Generate report
    score = generate_monitoring_report(dashboard_created, alerting_created, test_results)
    
    print(f"\n{style.SUCCESS('✅ Infrastructure monitoring implementation completed!')}")
    if score >= 80:
        print(f"{style.SUCCESS('🎉 Excellent monitoring! System is well instrumented.')}")
    elif score >= 60:
        print(f"{style.WARNING('⚠️ Good monitoring with some areas for improvement.')}")
    else:
        print(f"{style.ERROR('🚨 Critical monitoring issues require attention.')}")

if __name__ == '__main__':
    main()
