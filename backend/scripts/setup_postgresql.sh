#!/bin/bash
"""
PostgreSQL Setup Script for EMS
Sets up PostgreSQL database and environment for production use
"""

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DB_NAME=${DB_NAME:-"ems_production"}
DB_USER=${DB_USER:-"ems_user"}
DB_PASSWORD=${DB_PASSWORD:-""}
DB_HOST=${DB_HOST:-"localhost"}
DB_PORT=${DB_PORT:-"5432"}

log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

check_postgresql() {
    log "Checking PostgreSQL installation..."
    
    if ! command -v psql &> /dev/null; then
        error "PostgreSQL is not installed!"
        echo "Please install PostgreSQL first:"
        echo "  macOS: brew install postgresql"
        echo "  Ubuntu: sudo apt-get install postgresql postgresql-contrib"
        echo "  CentOS: sudo yum install postgresql postgresql-server"
        exit 1
    fi
    
    # Check if PostgreSQL service is running
    if ! pg_isready -h $DB_HOST -p $DB_PORT &> /dev/null; then
        error "PostgreSQL service is not running!"
        echo "Please start PostgreSQL service:"
        echo "  macOS: brew services start postgresql"
        echo "  Ubuntu: sudo systemctl start postgresql"
        echo "  CentOS: sudo systemctl start postgresql"
        exit 1
    fi
    
    success "PostgreSQL is installed and running"
}

prompt_for_password() {
    if [ -z "$DB_PASSWORD" ]; then
        echo -n "Enter password for database user '$DB_USER': "
        read -s DB_PASSWORD
        echo
        
        if [ -z "$DB_PASSWORD" ]; then
            error "Password cannot be empty!"
            exit 1
        fi
    fi
}

create_database() {
    log "Creating PostgreSQL database and user..."
    
    # Create user and database
    PGPASSWORD="$POSTGRES_PASSWORD" psql -h $DB_HOST -p $DB_PORT -U postgres -c "
        DO \$\$
        BEGIN
            IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = '$DB_USER') THEN
                CREATE USER $DB_USER WITH PASSWORD '$DB_PASSWORD';
            END IF;
        END
        \$\$;
    " 2>/dev/null || warning "User might already exist"
    
    PGPASSWORD="$POSTGRES_PASSWORD" psql -h $DB_HOST -p $DB_PORT -U postgres -c "
        SELECT 'CREATE DATABASE $DB_NAME OWNER $DB_USER'
        WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = '$DB_NAME')\\gexec
    " 2>/dev/null || warning "Database might already exist"
    
    # Grant privileges
    PGPASSWORD="$POSTGRES_PASSWORD" psql -h $DB_HOST -p $DB_PORT -U postgres -c "
        GRANT ALL PRIVILEGES ON DATABASE $DB_NAME TO $DB_USER;
        ALTER USER $DB_USER CREATEDB;
    " 2>/dev/null || warning "Privileges might already be set"
    
    success "Database setup completed"
}

test_connection() {
    log "Testing database connection..."
    
    PGPASSWORD="$DB_PASSWORD" psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "SELECT version();" > /dev/null
    
    if [ $? -eq 0 ]; then
        success "Database connection successful"
    else
        error "Failed to connect to database"
        exit 1
    fi
}

create_env_file() {
    log "Creating environment configuration..."
    
    ENV_FILE="../.env.postgresql"
    
    cat > $ENV_FILE << EOF
# PostgreSQL Configuration for EMS
USE_POSTGRESQL=true
DB_NAME=$DB_NAME
DB_USER=$DB_USER
DB_PASSWORD=$DB_PASSWORD
DB_HOST=$DB_HOST
DB_PORT=$DB_PORT

# Connection pooling
DB_MAX_CONNS=20
DB_CONN_MAX_AGE=600

# Performance settings
DB_ENABLE_QUERY_LOGGING=true
DB_SLOW_QUERY_THRESHOLD=0.5
EOF

    success "Environment file created: $ENV_FILE"
    echo "To use PostgreSQL, run: source $ENV_FILE"
}

install_dependencies() {
    log "Installing Python PostgreSQL dependencies..."
    
    cd ..
    
    if [ -f "requirements-production.txt" ]; then
        pip install psycopg2-binary
        success "PostgreSQL dependencies installed"
    else
        warning "requirements-production.txt not found"
    fi
}

main() {
    echo "=== PostgreSQL Setup for EMS ==="
    echo
    
    # Check prerequisites
    check_postgresql
    
    # Get database password
    prompt_for_password
    
    # Prompt for postgres superuser password if needed
    if [ -z "$POSTGRES_PASSWORD" ]; then
        echo -n "Enter PostgreSQL superuser (postgres) password: "
        read -s POSTGRES_PASSWORD
        echo
    fi
    
    # Create database and user
    create_database
    
    # Test connection
    test_connection
    
    # Install dependencies
    install_dependencies
    
    # Create environment file
    create_env_file
    
    echo
    success "PostgreSQL setup completed successfully!"
    echo
    echo "Next steps:"
    echo "1. Source the environment file: source .env.postgresql"
    echo "2. Run the migration script: python scripts/migrate_to_postgresql.py"
    echo "3. Start your Django application"
    echo
}

# Check if running as source or executed
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
