#!/bin/bash
"""
Production Startup Script for EMS with PostgreSQL
Starts the Django application with PostgreSQL database configuration
"""

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# PostgreSQL Configuration
export USE_POSTGRESQL=true
export DB_NAME=ems_production
export DB_USER=ems_user
export DB_PASSWORD=ems_secure_password_2024
export DB_HOST=localhost
export DB_PORT=5432

# Connection pooling
export DB_CONN_MAX_AGE=600

# Performance settings
export DB_ENABLE_QUERY_LOGGING=true
export DB_SLOW_QUERY_THRESHOLD=0.5

# Check if virtual environment is activated
check_venv() {
    if [[ "$VIRTUAL_ENV" == "" ]]; then
        log "Activating virtual environment..."
        source ../backend_env/bin/activate
    else
        log "Virtual environment already activated: $VIRTUAL_ENV"
    fi
}

# Check PostgreSQL connection
check_postgresql() {
    log "Checking PostgreSQL connection..."
    
    if ! command -v psql &> /dev/null; then
        error "PostgreSQL client not found!"
        exit 1
    fi
    
    if ! pg_isready -h $DB_HOST -p $DB_PORT &> /dev/null; then
        error "PostgreSQL server is not running!"
        echo "Please start PostgreSQL service:"
        echo "  macOS: brew services start postgresql"
        echo "  Ubuntu: sudo systemctl start postgresql"
        exit 1
    fi
    
    # Test database connection
    if ! PGPASSWORD="$DB_PASSWORD" psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "SELECT 1;" &> /dev/null; then
        error "Cannot connect to database '$DB_NAME'"
        echo "Please ensure the database and user exist:"
        echo "  Database: $DB_NAME"
        echo "  User: $DB_USER"
        exit 1
    fi
    
    success "PostgreSQL connection verified"
}

# Run Django checks
run_checks() {
    log "Running Django system checks..."
    
    if ! python manage.py check --database default; then
        error "Django system checks failed!"
        exit 1
    fi
    
    success "Django system checks passed"
}

# Check for pending migrations
check_migrations() {
    log "Checking for pending migrations..."
    
    # Get migration status
    PENDING=$(python manage.py showmigrations --plan | grep -c "\[ \]" || true)
    
    if [ "$PENDING" -gt 0 ]; then
        warning "Found $PENDING pending migrations"
        echo "Run migrations with: python manage.py migrate"
        read -p "Do you want to run migrations now? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            log "Running migrations..."
            python manage.py migrate
            success "Migrations completed"
        else
            warning "Skipping migrations - application may not work correctly"
        fi
    else
        success "All migrations are up to date"
    fi
}

# Collect static files for production
collect_static() {
    if [ "$1" == "--production" ]; then
        log "Collecting static files..."
        python manage.py collectstatic --noinput
        success "Static files collected"
    fi
}

# Start the Django server
start_server() {
    local port=${1:-8000}
    local host=${2:-127.0.0.1}
    
    log "Starting Django development server..."
    log "Database: PostgreSQL ($DB_NAME@$DB_HOST:$DB_PORT)"
    log "Server: http://$host:$port/"
    log "Admin: http://$host:$port/admin/ (admin/admin123)"
    echo
    success "EMS is ready! Press Ctrl+C to stop."
    echo
    
    # Start the server
    python manage.py runserver $host:$port
}

# Main execution
main() {
    echo "=== EMS Production Startup (PostgreSQL) ==="
    echo
    
    # Change to backend directory
    cd "$(dirname "$0")/.."
    
    # Check virtual environment
    check_venv
    
    # Check PostgreSQL
    check_postgresql
    
    # Run Django checks
    run_checks
    
    # Check migrations
    check_migrations
    
    # Collect static files if production flag is set
    collect_static "$1"
    
    # Start server
    start_server "${@:2}"
}

# Handle script arguments
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
