#!/usr/bin/env python3
"""
Test API Consolidation
Tests the consolidated API endpoints to ensure they're working correctly
"""

import os
import sys
import django
from pathlib import Path
import requests
import time

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.core.management.color import make_style
from django.test import Client
from django.urls import reverse

style = make_style()

def print_header(title):
    """Print a formatted header"""
    print("\n" + "=" * 80)
    print(style.SUCCESS(f"🧪 {title}"))
    print("=" * 80)

def print_section(title):
    """Print a formatted section"""
    print(f"\n{style.WARNING(f'📊 {title}')}")
    print("-" * 60)

def test_api_endpoints_with_client():
    """Test API endpoints using Django test client"""
    print_section("TESTING API ENDPOINTS WITH DJANGO CLIENT")
    
    client = Client()
    
    # Test endpoints
    test_endpoints = [
        # V1 API endpoints
        {'url': '/api/v1/departments/', 'name': 'V1 Departments', 'expected': [200, 401, 403]},
        {'url': '/api/v1/employees/', 'name': 'V1 Employees', 'expected': [200, 401, 403]},
        {'url': '/api/v1/activities/', 'name': 'V1 Activities', 'expected': [200, 401, 403]},
        {'url': '/api/v1/auth/user/', 'name': 'V1 Auth User', 'expected': [200, 401, 403]},
        
        # Legacy API endpoints
        {'url': '/api/departments/', 'name': 'Legacy Departments', 'expected': [200, 401, 403]},
        {'url': '/api/employees/', 'name': 'Legacy Employees', 'expected': [200, 401, 403]},
        {'url': '/api/activities/', 'name': 'Legacy Activities', 'expected': [200, 401, 403]},
        
        # Health check
        {'url': '/api/health/', 'name': 'Health Check', 'expected': [200]},
    ]
    
    results = []
    
    for endpoint in test_endpoints:
        try:
            response = client.get(endpoint['url'])
            status_code = response.status_code
            is_working = status_code in endpoint['expected']
            
            status_icon = "✅" if is_working else "❌"
            print(f"  {status_icon} {endpoint['name']}: {status_code}")
            
            # Check for API version headers
            api_version = response.get('X-API-Version', 'none')
            if api_version != 'none':
                print(f"    📋 API Version: {api_version}")
            
            # Check for deprecation warnings
            if response.get('X-API-Deprecated'):
                print(f"    ⚠️ Deprecated: {response.get('X-API-Migration-Info', 'No info')}")
            
            results.append({
                'endpoint': endpoint['url'],
                'name': endpoint['name'],
                'status_code': status_code,
                'working': is_working,
                'api_version': api_version,
                'deprecated': bool(response.get('X-API-Deprecated'))
            })
            
        except Exception as e:
            print(f"  ❌ {endpoint['name']}: Error - {e}")
            results.append({
                'endpoint': endpoint['url'],
                'name': endpoint['name'],
                'status_code': None,
                'working': False,
                'error': str(e)
            })
    
    return results

def test_response_standardization():
    """Test API response standardization"""
    print_section("TESTING RESPONSE STANDARDIZATION")
    
    client = Client()
    
    # Test different API versions
    test_cases = [
        {'url': '/api/v1/departments/', 'expected_version': 'v1'},
        {'url': '/api/departments/', 'expected_version': 'legacy'},
    ]
    
    standardization_results = []
    
    for case in test_cases:
        try:
            response = client.get(case['url'])
            api_version = response.get('X-API-Version', 'none')
            has_security_headers = bool(response.get('X-Content-Type-Options'))
            is_deprecated = bool(response.get('X-API-Deprecated'))
            
            print(f"  📋 {case['url']}:")
            print(f"    • API Version: {api_version}")
            print(f"    • Security Headers: {'✅' if has_security_headers else '❌'}")
            print(f"    • Deprecated: {'⚠️' if is_deprecated else '✅'}")
            
            standardization_results.append({
                'url': case['url'],
                'api_version': api_version,
                'has_security_headers': has_security_headers,
                'is_deprecated': is_deprecated,
                'correct_version': api_version == case['expected_version']
            })
            
        except Exception as e:
            print(f"  ❌ {case['url']}: Error - {e}")
            standardization_results.append({
                'url': case['url'],
                'error': str(e)
            })
    
    return standardization_results

def test_endpoint_migration_mapping():
    """Test endpoint migration mapping"""
    print_section("TESTING ENDPOINT MIGRATION MAPPING")
    
    # Import the migration map
    try:
        from ems.api_v1_urls import get_migration_map
        migration_map = get_migration_map()
        
        print(f"  📋 Migration map contains {len(migration_map)} endpoint mappings:")
        for old_endpoint, new_endpoint in list(migration_map.items())[:5]:
            print(f"    • {old_endpoint} → {new_endpoint}")
        
        if len(migration_map) > 5:
            print(f"    ... and {len(migration_map) - 5} more mappings")
        
        return migration_map
        
    except Exception as e:
        print(f"  ❌ Error loading migration map: {e}")
        return {}

def generate_consolidation_test_report(endpoint_results, standardization_results, migration_map):
    """Generate comprehensive test report"""
    print_header("API CONSOLIDATION TEST REPORT")
    
    # Calculate metrics
    total_endpoints = len(endpoint_results)
    working_endpoints = sum(1 for r in endpoint_results if r.get('working', False))
    v1_endpoints = sum(1 for r in endpoint_results if r.get('api_version') == 'v1')
    deprecated_endpoints = sum(1 for r in endpoint_results if r.get('deprecated', False))
    
    standardized_responses = sum(1 for r in standardization_results if r.get('has_security_headers', False))
    total_standardization_tests = len(standardization_results)
    
    print(f"📊 {style.SUCCESS('TEST SUMMARY')}")
    print(f"  • Total Endpoints Tested: {total_endpoints}")
    print(f"  • Working Endpoints: {working_endpoints}/{total_endpoints}")
    print(f"  • V1 Endpoints: {v1_endpoints}")
    print(f"  • Deprecated Endpoints: {deprecated_endpoints}")
    print(f"  • Migration Mappings: {len(migration_map)}")
    
    print(f"\n📈 {style.WARNING('STANDARDIZATION RESULTS')}")
    print(f"  • Standardized Responses: {standardized_responses}/{total_standardization_tests}")
    
    # Calculate scores
    endpoint_score = (working_endpoints / total_endpoints * 100) if total_endpoints > 0 else 0
    standardization_score = (standardized_responses / total_standardization_tests * 100) if total_standardization_tests > 0 else 0
    migration_score = min(100, len(migration_map) * 10)  # 10 points per mapping, max 100
    
    overall_score = (endpoint_score + standardization_score + migration_score) / 3
    
    print(f"\n🎯 {style.NOTICE('SCORES')}")
    print(f"  • Endpoint Functionality: {endpoint_score:.1f}%")
    print(f"  • Response Standardization: {standardization_score:.1f}%")
    print(f"  • Migration Readiness: {migration_score:.1f}%")
    
    print(f"\n🏆 {style.SUCCESS(f'OVERALL CONSOLIDATION SCORE: {overall_score:.1f}%')}")
    
    # Recommendations
    print(f"\n🎯 {style.NOTICE('RECOMMENDATIONS')}")
    if overall_score >= 80:
        print("  ✅ API consolidation is working well!")
        print("  • Continue monitoring API usage")
        print("  • Plan legacy endpoint deprecation timeline")
    elif overall_score >= 60:
        print("  ⚠️ Good progress with some areas for improvement:")
        if endpoint_score < 80:
            print("  • Fix non-working endpoints")
        if standardization_score < 80:
            print("  • Improve response standardization")
        if migration_score < 80:
            print("  • Add more migration mappings")
    else:
        print("  🚨 Significant issues detected:")
        print("  • Review API endpoint configurations")
        print("  • Check middleware setup")
        print("  • Verify URL routing")
    
    return overall_score

def main():
    """Main test function"""
    print(style.SUCCESS("🧪 EMS API Consolidation Testing"))
    print(style.NOTICE("Testing consolidated API endpoints and standardization..."))
    
    # Run tests
    endpoint_results = test_api_endpoints_with_client()
    standardization_results = test_response_standardization()
    migration_map = test_endpoint_migration_mapping()
    
    # Generate report
    score = generate_consolidation_test_report(endpoint_results, standardization_results, migration_map)
    
    print(f"\n{style.SUCCESS('✅ API consolidation testing completed!')}")
    
    return score

if __name__ == '__main__':
    main()
