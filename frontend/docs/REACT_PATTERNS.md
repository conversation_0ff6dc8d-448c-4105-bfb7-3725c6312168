# React Patterns - No DOM Manipulation

This document outlines the React patterns that should be used instead of direct DOM manipulation.

## ✅ React Patterns (Use These)

### 1. State Management
```tsx
// ✅ Use React state
const [isVisible, setIsVisible] = useState(false)
const [inputValue, setInputValue] = useState('')

// ✅ Use refs for direct element access when needed
const inputRef = useRef<HTMLInputElement>(null)
```

### 2. Event Handling
```tsx
// ✅ Use React event handlers
const handleClick = (e: React.MouseEvent) => {
  e.preventDefault()
  setIsVisible(!isVisible)
}

// ✅ Use React synthetic events
<button onClick={handleClick}>Toggle</button>
```

### 3. Conditional Rendering
```tsx
// ✅ Use React conditional rendering
{isVisible && <div>Content</div>}
{error && <div className="error">{error}</div>}
```

### 4. Dynamic Classes
```tsx
// ✅ Use React className with template literals
<div className={`card ${isActive ? 'active' : ''} ${theme}`}>
  Content
</div>

// ✅ Use clsx or classnames library
import clsx from 'clsx'
<div className={clsx('card', { active: isActive }, theme)}>
  Content
</div>
```

### 5. Form Handling
```tsx
// ✅ Use controlled components
const [formData, setFormData] = useState({ name: '', email: '' })

const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
  setFormData(prev => ({
    ...prev,
    [e.target.name]: e.target.value
  }))
}

<input
  name="name"
  value={formData.name}
  onChange={handleChange}
/>
```

### 6. Effects and Lifecycle
```tsx
// ✅ Use useEffect for side effects
useEffect(() => {
  // Setup
  const handleResize = () => setWindowWidth(window.innerWidth)
  window.addEventListener('resize', handleResize)
  
  // Cleanup
  return () => window.removeEventListener('resize', handleResize)
}, [])
```

## ❌ DOM Manipulation (Don't Use These)

### 1. Direct DOM Queries
```tsx
// ❌ Don't use document.getElementById
const element = document.getElementById('myElement')

// ❌ Don't use document.querySelector
const element = document.querySelector('.my-class')

// ❌ Don't use document.getElementsByClassName
const elements = document.getElementsByClassName('my-class')
```

### 2. Direct Style Manipulation
```tsx
// ❌ Don't manipulate styles directly
element.style.display = 'none'
element.style.backgroundColor = 'red'

// ❌ Don't manipulate classes directly
element.classList.add('active')
element.classList.remove('hidden')
```

### 3. Direct Content Manipulation
```tsx
// ❌ Don't manipulate content directly
element.innerHTML = '<div>New content</div>'
element.textContent = 'New text'
```

### 4. Direct Event Listeners
```tsx
// ❌ Don't add event listeners directly
element.addEventListener('click', handler)
document.addEventListener('keydown', handler)
```

## 🔄 Migration Examples

### Example 1: Toggle Visibility
```tsx
// ❌ DOM Manipulation
const toggleElement = () => {
  const element = document.getElementById('content')
  element.style.display = element.style.display === 'none' ? 'block' : 'none'
}

// ✅ React Pattern
const [isVisible, setIsVisible] = useState(true)
const toggleElement = () => setIsVisible(!isVisible)

return (
  <div>
    {isVisible && <div id="content">Content</div>}
    <button onClick={toggleElement}>Toggle</button>
  </div>
)
```

### Example 2: Form Validation
```tsx
// ❌ DOM Manipulation
const validateForm = () => {
  const input = document.getElementById('email')
  const error = document.getElementById('error')
  
  if (!input.value.includes('@')) {
    error.textContent = 'Invalid email'
    error.style.display = 'block'
  }
}

// ✅ React Pattern
const [email, setEmail] = useState('')
const [error, setError] = useState('')

const validateEmail = (value: string) => {
  if (!value.includes('@')) {
    setError('Invalid email')
  } else {
    setError('')
  }
}

const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
  const value = e.target.value
  setEmail(value)
  validateEmail(value)
}

return (
  <div>
    <input
      type="email"
      value={email}
      onChange={handleEmailChange}
    />
    {error && <div className="error">{error}</div>}
  </div>
)
```

### Example 3: Dynamic Content
```tsx
// ❌ DOM Manipulation
const updateList = (items) => {
  const container = document.getElementById('list')
  container.innerHTML = ''
  
  items.forEach(item => {
    const div = document.createElement('div')
    div.textContent = item.name
    container.appendChild(div)
  })
}

// ✅ React Pattern
const [items, setItems] = useState([])

return (
  <div id="list">
    {items.map(item => (
      <div key={item.id}>{item.name}</div>
    ))}
  </div>
)
```

## 🎯 Key Principles

1. **Declarative over Imperative**: Describe what the UI should look like, not how to manipulate it
2. **State-Driven**: Use React state to drive UI changes
3. **Component Isolation**: Keep component logic within the component
4. **Refs for Exceptions**: Only use refs when you absolutely need direct DOM access
5. **Event Delegation**: Use React's synthetic event system

## 🚫 Exceptions (Rare Cases)

Only use direct DOM manipulation for:
- Third-party library integration that requires it
- Performance-critical operations (with careful consideration)
- Browser API access that React doesn't provide

Even in these cases, isolate DOM manipulation in custom hooks or utility functions.
