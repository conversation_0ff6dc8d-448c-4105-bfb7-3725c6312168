export default {
  preset: 'ts-jest',
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/src/setupTests.ts'],

  // Enhanced module resolution for better compatibility
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
    '\\.(jpg|jpeg|png|gif|svg)$': '<rootDir>/src/__mocks__/fileMock.js',
    '^react-router-dom$': '<rootDir>/node_modules/react-router-dom',
    '^react-redux$': '<rootDir>/node_modules/react-redux'
  },

  // Enhanced TypeScript configuration for current issues
  transform: {
    '^.+\\.(ts|tsx)$': ['ts-jest', {
      isolatedModules: true,
      useESM: true,
      tsconfig: {
        compilerOptions: {
          // Relaxed settings to work around TypeScript cleanup
          strict: false,
          noImplicitAny: false,
          skipLibCheck: true,
          allowJs: true,
          esModuleInterop: true,
          allowSyntheticDefaultImports: true,
          jsx: 'react-jsx'
        }
      }
    }],
    '^.+\\.(js|jsx)$': 'babel-jest',
  },
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node'],
  testMatch: [
    '<rootDir>/src/**/__tests__/**/*.(ts|tsx|js)',
    '<rootDir>/src/**/?(*.)(spec|test).(ts|tsx|js)',
  ],

  // Ignore problematic files during TypeScript cleanup
  testPathIgnorePatterns: [
    '/node_modules/',
    '/dist/',
    '/coverage/',
    // Temporarily ignore files with syntax errors
    'UserManagementFixed.tsx.broken',
    'UserManagementClean.tsx'
  ],
  collectCoverageFrom: [
    'src/**/*.(ts|tsx)',
    '!src/**/*.d.ts',
    '!src/main.tsx',
    '!src/vite-env.d.ts',
    '!src/**/*.stories.{ts,tsx}',
    '!src/**/*.config.{ts,tsx}',
    // Focus on critical security and accessibility files
    'src/pages/Login.tsx',
    'src/utils/securityHeaders.ts',
    'src/utils/accessibilityEnhancements.ts',
    'src/utils/csrfToken.ts',
    'src/hooks/useAuth.ts',
    'src/hooks/useCSRF.ts',
    'src/components/common/ErrorBoundaryWithFeedback.tsx',
    'src/components/common/LoadingStates.tsx',
    'src/components/common/EnhancedToast.tsx'
  ],

  // Coverage thresholds - ensure >90% coverage
  coverageThreshold: {
    global: {
      branches: 90,
      functions: 90,
      lines: 90,
      statements: 90
    },
    // Specific thresholds for critical security files
    'src/pages/Login.tsx': {
      branches: 95,
      functions: 95,
      lines: 95,
      statements: 95
    },
    'src/utils/securityHeaders.ts': {
      branches: 95,
      functions: 95,
      lines: 95,
      statements: 95
    }
  },

  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'text-summary', 'lcov', 'html', 'json-summary'],
  extensionsToTreatAsEsm: ['.ts', '.tsx'],
  globals: {
    'ts-jest': {
      useESM: true,
    },
  },
};
