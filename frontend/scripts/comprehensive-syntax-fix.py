#!/usr/bin/env python3
"""
Comprehensive Syntax Fix for UserManagementFixed.tsx
Fixes all malformed syntax patterns in one pass
"""

import re
import os

def fix_user_management_file():
    file_path = os.path.join(os.path.dirname(__file__), '..', 'src', 'pages', 'admin', 'UserManagementFixed.tsx')
    
    print("🔧 Fixing UserManagementFixed.tsx syntax errors...")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Define all the problematic patterns and their fixes
        fixes = [
            # Fix malformed function calls with "( as any)"
            (r'\(\s*as\s+any\s*\)', '()'),
            
            # Fix useEffect with malformed callback
            (r'useEffect\(\(\s*as\s+any\)\s*=>\s*{', 'useEffect(() => {'),
            
            # Fix useCallback with malformed callback
            (r'useCallback\(\(\s*as\s+any\)\s*=>\s*{', 'useCallback(() => {'),
            
            # Fix function parameters with "as any"
            (r'\(\s*([^)]*?)\s*as\s+any\s*\)', r'(\1)'),
            
            # Remove excessive "as any" type assertions
            (r'\s+as\s+any', ''),
            
            # Remove @ts-ignore comments
            (r'\s*//\s*@ts-ignore\s*\n', '\n'),
            
            # Fix dependency arrays with malformed syntax
            (r'}\s*//\s*@ts-ignore\s*},\s*\[([^\]]*)\]', r'  }, [\1]'),
            
            # Fix specific function calls
            (r'debugLog\(([^)]+),\s*([^)]+)\s+as\s+any\)', r'debugLog(\1, \2)'),
            (r'debugError\(([^)]+),\s*([^)]+),\s*([^)]+)\s+as\s+any\)', r'debugError(\1, \2, \3)'),
            
            # Fix object properties
            (r'}\s+as\s+any\)', '}'),
            
            # Fix setState calls
            (r'setTestMode\(true\s+as\s+any\)', 'setTestMode(true)'),
            (r'setShowModal\(false\s+as\s+any\)', 'setShowModal(false)'),
            (r'setShowModal\(true\s+as\s+any\)', 'setShowModal(true)'),
            (r'setIsInitialized\(true\s+as\s+any\)', 'setIsInitialized(true)'),
            (r'setModalMode\(\'([^\']+)\'\s+as\s+any\)', r"setModalMode('\1')"),
            
            # Fix selectItem calls
            (r'selectItem\(null\s+as\s+any\)', 'selectItem(null)'),
            (r'selectItem\(([^)]+)\s+as\s+any\)', r'selectItem(\1)'),
            
            # Fix function parameters in callbacks
            (r'const\s+(\w+)\s*=\s*useCallback\(\(([^)]*?):\s*\w+\s+as\s+any\)\s*=>', r'const \1 = useCallback((\2) =>'),
            
            # Fix double "as any as any" patterns
            (r'\(\s*([^)]+?)\s+as\s+any\s+as\s+any\s*\)', r'(\1)'),
            
            # Fix t object access
            (r'\(t\s+as\s+any\s+as\s+any\)\.(\w+)', r't.\1'),
            (r'\(t\s+as\s+any\)\.(\w+)', r't.\1'),
            
            # Fix user object access
            (r'\(user\s+as\s+any\s+as\s+any\)\.(\w+)', r'user.\1'),
            (r'\(user\s+as\s+any\)\.(\w+)', r'user.\1'),
            
            # Fix error object access
            (r'\(error\s+as\s+any\)\.(\w+)', r'error.\1'),
            
            # Clean up extra whitespace
            (r'\n\s*\n\s*\n', '\n\n'),
        ]
        
        fix_count = 0
        for pattern, replacement in fixes:
            matches = re.findall(pattern, content)
            if matches:
                content = re.sub(pattern, replacement, content)
                fix_count += len(matches)
                print(f"  ✅ Fixed pattern '{pattern[:50]}...': {len(matches)} occurrences")
        
        # Additional specific fixes
        
        # Fix the function declaration
        content = re.sub(
            r'export default function UserManagementFixed\([^)]+\):\s*\([^)]+\)\.ReactElement\s*{',
            'export default function UserManagementFixed({ language }: UserManagementProps) {',
            content
        )
        
        # Fix any remaining malformed patterns
        content = re.sub(r'\(\s*as\s+any\s*\)\s*=>', '() =>', content)
        content = re.sub(r'\s*as\s+any\s*\)', ')', content)
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ Fixed {fix_count} syntax errors in UserManagementFixed.tsx")
            print(f"📊 File size: {len(original_content)} -> {len(content)} characters")
            
            # Basic validation
            open_braces = content.count('{')
            close_braces = content.count('}')
            open_parens = content.count('(')
            close_parens = content.count(')')
            
            print(f"📋 Syntax validation:")
            print(f"  Braces: {open_braces} open, {close_braces} close (diff: {open_braces - close_braces})")
            print(f"  Parens: {open_parens} open, {close_parens} close (diff: {open_parens - close_parens})")
            
            if open_braces == close_braces and open_parens == close_parens:
                print("✅ Basic syntax validation passed")
            else:
                print("⚠️ Potential syntax issues detected")
            
            return True
        else:
            print("ℹ️ No syntax errors found to fix")
            return False
            
    except Exception as e:
        print(f"❌ Error fixing file: {e}")
        return False

def main():
    print("🚀 Comprehensive TypeScript Syntax Fixer")
    print("Fixing critical build-blocking syntax errors...\n")
    
    success = fix_user_management_file()
    
    if success:
        print("\n🎉 Syntax fixing completed!")
        print("📋 Next steps:")
        print("  1. Test TypeScript compilation: npx tsc --noEmit")
        print("  2. Test build: npm run build")
        print("  3. Fix any remaining type errors")
    else:
        print("\n⚠️ No changes made or errors encountered")
    
    return success

if __name__ == '__main__':
    main()
