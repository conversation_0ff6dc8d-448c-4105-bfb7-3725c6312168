#!/usr/bin/env node
/**
 * Fix Critical Syntax Errors <PERSON>ript
 * Fixes the most critical syntax errors that prevent TypeScript compilation
 */

const fs = require('fs');
const path = require('path');

// ANSI color codes
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m'
};

function colorize(text, color) {
    return `${colors[color]}${text}${colors.reset}`;
}

class SyntaxErrorFixer {
    constructor() {
        this.fixedFiles = [];
        this.errors = [];
    }

    fixUserManagementFile() {
        const filePath = path.join(__dirname, '..', 'src', 'pages', 'admin', 'UserManagementFixed.tsx');
        
        console.log(colorize('🔧 Fixing UserManagementFixed.tsx syntax errors...', 'blue'));
        
        try {
            let content = fs.readFileSync(filePath, 'utf8');
            const originalContent = content;
            
            // Fix malformed function calls with "( as any)" pattern
            const fixes = [
                // Fix function calls with malformed parameters
                {
                    pattern: /\(\s*as\s+any\s*\)/g,
                    replacement: '()',
                    description: 'Fixed malformed function calls'
                },
                
                // Fix useEffect with malformed callback
                {
                    pattern: /useEffect\(\(\s*as\s+any\)\s*=>\s*{/g,
                    replacement: 'useEffect(() => {',
                    description: 'Fixed useEffect callback'
                },
                
                // Fix useCallback with malformed callback
                {
                    pattern: /useCallback\(\(\s*as\s+any\)\s*=>\s*{/g,
                    replacement: 'useCallback(() => {',
                    description: 'Fixed useCallback callback'
                },
                
                // Fix function parameters with "as any"
                {
                    pattern: /\(\s*([^)]*)\s*as\s+any\s*\)/g,
                    replacement: '($1)',
                    description: 'Fixed function parameters'
                },
                
                // Remove excessive "as any" type assertions
                {
                    pattern: /\s+as\s+any/g,
                    replacement: '',
                    description: 'Removed excessive type assertions'
                },
                
                // Fix setState calls
                {
                    pattern: /setState\w*\([^)]+as\s+any\)/g,
                    replacement: (match) => match.replace(/\s+as\s+any/, ''),
                    description: 'Fixed setState calls'
                },
                
                // Remove @ts-ignore comments that are no longer needed
                {
                    pattern: /\s*\/\/\s*@ts-ignore\s*\n/g,
                    replacement: '\n',
                    description: 'Removed unnecessary @ts-ignore comments'
                }
            ];
            
            let fixCount = 0;
            for (const fix of fixes) {
                const matches = content.match(fix.pattern);
                if (matches) {
                    content = content.replace(fix.pattern, fix.replacement);
                    fixCount += matches.length;
                    console.log(`  ✅ ${fix.description}: ${matches.length} fixes`);
                }
            }
            
            // Additional specific fixes for this file
            
            // Fix the function declaration return type
            content = content.replace(
                /export default function UserManagementFixed\([^)]+\):\s*\([^)]+\)\.ReactElement\s*{/,
                'export default function UserManagementFixed({ language }: UserManagementProps) {'
            );
            
            // Fix malformed dependency arrays
            content = content.replace(
                /}\s*\/\/\s*@ts-ignore\s*},\s*\[[^\]]*\]/g,
                (match) => {
                    const deps = match.match(/\[([^\]]*)\]/);
                    return `  }, [${deps ? deps[1] : ''}])`;
                }
            );
            
            // Fix conditional statements
            content = content.replace(
                /if\s*\(\s*isDevelopmentMode\(\s*as\s+any\s*\)\s*\)/g,
                'if (isDevelopmentMode())'
            );
            
            if (content !== originalContent) {
                fs.writeFileSync(filePath, content);
                this.fixedFiles.push(filePath);
                console.log(colorize(`  ✅ Fixed ${fixCount} syntax errors in UserManagementFixed.tsx`, 'green'));
                return true;
            } else {
                console.log(colorize('  ℹ️ No syntax errors found to fix', 'yellow'));
                return false;
            }
            
        } catch (error) {
            console.error(colorize(`  ❌ Error fixing ${filePath}: ${error.message}`, 'red'));
            this.errors.push({ file: filePath, error: error.message });
            return false;
        }
    }

    fixCommonSyntaxErrors() {
        console.log(colorize('🔧 Scanning for common syntax errors...', 'blue'));
        
        const srcDir = path.join(__dirname, '..', 'src');
        const filesToCheck = [];
        
        // Recursively find all .tsx and .ts files
        const findFiles = (dir) => {
            const items = fs.readdirSync(dir);
            for (const item of items) {
                const fullPath = path.join(dir, item);
                const stat = fs.statSync(fullPath);
                
                if (stat.isDirectory() && !item.startsWith('.')) {
                    findFiles(fullPath);
                } else if (item.endsWith('.tsx') || item.endsWith('.ts')) {
                    filesToCheck.push(fullPath);
                }
            }
        };
        
        findFiles(srcDir);
        
        let totalFixes = 0;
        
        for (const filePath of filesToCheck) {
            try {
                let content = fs.readFileSync(filePath, 'utf8');
                const originalContent = content;
                
                // Common syntax error patterns
                const commonFixes = [
                    // Fix missing React import for JSX
                    {
                        pattern: /^(?!.*import.*React)(.|\n)*(<[A-Z])/m,
                        replacement: (match) => {
                            if (!match.includes('import React')) {
                                return `import React from 'react'\n${match}`;
                            }
                            return match;
                        },
                        description: 'Added missing React import'
                    },
                    
                    // Fix missing return statements in functions
                    {
                        pattern: /const\s+\w+\s*=\s*\([^)]*\)\s*:\s*void\s*=>\s*{[^}]*}(?!\s*return)/g,
                        replacement: (match) => {
                            if (!match.includes('return') && !match.includes('console.') && !match.includes('set')) {
                                return match.replace('}', '  return\n}');
                            }
                            return match;
                        },
                        description: 'Added missing return statements'
                    }
                ];
                
                let fileFixes = 0;
                for (const fix of commonFixes) {
                    const matches = content.match(fix.pattern);
                    if (matches) {
                        content = content.replace(fix.pattern, fix.replacement);
                        fileFixes += matches.length;
                    }
                }
                
                if (content !== originalContent && fileFixes > 0) {
                    fs.writeFileSync(filePath, content);
                    totalFixes += fileFixes;
                    console.log(`  ✅ Fixed ${fileFixes} issues in ${path.relative(srcDir, filePath)}`);
                }
                
            } catch (error) {
                console.error(`  ⚠️ Error processing ${filePath}: ${error.message}`);
            }
        }
        
        console.log(colorize(`  📊 Total common fixes applied: ${totalFixes}`, 'cyan'));
        return totalFixes;
    }

    generateReport() {
        console.log('\n' + '='.repeat(60));
        console.log(colorize('📋 SYNTAX ERROR FIX REPORT', 'cyan'));
        console.log('='.repeat(60));
        
        console.log(`${colorize('✅ Files fixed:', 'green')} ${this.fixedFiles.length}`);
        
        if (this.fixedFiles.length > 0) {
            console.log('\n📁 Fixed files:');
            for (const file of this.fixedFiles) {
                console.log(`  • ${path.relative(process.cwd(), file)}`);
            }
        }
        
        if (this.errors.length > 0) {
            console.log(`\n${colorize('❌ Errors encountered:', 'red')} ${this.errors.length}`);
            for (const error of this.errors) {
                console.log(`  • ${path.relative(process.cwd(), error.file)}: ${error.error}`);
            }
        }
        
        console.log(`\n${colorize('🎯 Next steps:', 'yellow')}`);
        console.log('  1. Run TypeScript check: npx tsc --noEmit');
        console.log('  2. Fix remaining type errors systematically');
        console.log('  3. Test build: npm run build');
        
        return {
            fixedFiles: this.fixedFiles.length,
            errors: this.errors.length
        };
    }

    run() {
        console.log(colorize('🚀 Critical Syntax Error Fixer', 'cyan'));
        console.log('Fixing build-blocking syntax errors...\n');
        
        // Fix the critical UserManagement file first
        this.fixUserManagementFile();
        
        // Fix common syntax errors across all files
        this.fixCommonSyntaxErrors();
        
        // Generate report
        const results = this.generateReport();
        
        console.log(`\n${colorize('✅ Syntax error fixing completed!', 'green')}`);
        
        return results;
    }
}

// Run the fixer
if (require.main === module) {
    const fixer = new SyntaxErrorFixer();
    fixer.run();
}

module.exports = SyntaxErrorFixer;
