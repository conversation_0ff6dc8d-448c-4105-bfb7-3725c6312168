#!/usr/bin/env node
/**
 * Fix UserManagement Syntax Errors
 * Systematically fixes all syntax errors in UserManagementFixed.tsx
 */

const fs = require('fs');
const path = require('path');

const filePath = path.join(__dirname, '..', 'src', 'pages', 'admin', 'UserManagementFixed.tsx');

console.log('🔧 Fixing UserManagementFixed.tsx syntax errors...');

try {
    let content = fs.readFileSync(filePath, 'utf8');
    const originalContent = content;
    
    console.log('📊 Original file size:', content.length, 'characters');
    
    // Fix all malformed function calls and parameters
    const fixes = [
        // Fix malformed useEffect callbacks
        {
            pattern: /useEffect\(\(\s*as\s+any\)\s*=>\s*{/g,
            replacement: 'useEffect(() => {',
            description: 'Fixed useEffect callbacks'
        },
        
        // Fix malformed useCallback callbacks
        {
            pattern: /useCallback\(\(\s*as\s+any\)\s*=>\s*{/g,
            replacement: 'useCallback(() => {',
            description: 'Fixed useCallback callbacks'
        },
        
        // Fix malformed function calls with "( as any)"
        {
            pattern: /\(\s*as\s+any\s*\)/g,
            replacement: '()',
            description: 'Fixed malformed function calls'
        },
        
        // Fix function parameters with "as any"
        {
            pattern: /\(\s*([^)]*)\s*as\s+any\s*\)/g,
            replacement: '($1)',
            description: 'Fixed function parameters'
        },
        
        // Remove excessive "as any" type assertions
        {
            pattern: /\s+as\s+any/g,
            replacement: '',
            description: 'Removed excessive type assertions'
        },
        
        // Remove @ts-ignore comments
        {
            pattern: /\s*\/\/\s*@ts-ignore\s*\n/g,
            replacement: '\n',
            description: 'Removed @ts-ignore comments'
        },
        
        // Fix setState calls
        {
            pattern: /setTestMode\(true\s+as\s+any\)/g,
            replacement: 'setTestMode(true)',
            description: 'Fixed setTestMode calls'
        },
        
        {
            pattern: /setShowModal\(false\s+as\s+any\)/g,
            replacement: 'setShowModal(false)',
            description: 'Fixed setShowModal calls'
        },
        
        {
            pattern: /setIsInitialized\(true\s+as\s+any\)/g,
            replacement: 'setIsInitialized(true)',
            description: 'Fixed setIsInitialized calls'
        },
        
        // Fix selectItem calls
        {
            pattern: /selectItem\(null\s+as\s+any\)/g,
            replacement: 'selectItem(null)',
            description: 'Fixed selectItem calls'
        },
        
        // Fix debugLog calls
        {
            pattern: /debugLog\(([^)]+)\s+as\s+any\)/g,
            replacement: 'debugLog($1)',
            description: 'Fixed debugLog calls'
        },
        
        // Fix object properties with "as any"
        {
            pattern: /}\s+as\s+any\)/g,
            replacement: '})',
            description: 'Fixed object properties'
        },
        
        // Fix dependency arrays with @ts-ignore
        {
            pattern: /}\s*\/\/\s*@ts-ignore\s*},\s*\[([^\]]*)\]/g,
            replacement: '  }, [$1]',
            description: 'Fixed dependency arrays'
        }
    ];
    
    let totalFixes = 0;
    
    for (const fix of fixes) {
        const matches = content.match(fix.pattern);
        if (matches) {
            content = content.replace(fix.pattern, fix.replacement);
            totalFixes += matches.length;
            console.log(`  ✅ ${fix.description}: ${matches.length} fixes`);
        }
    }
    
    // Additional specific fixes for this file
    
    // Fix the function declaration if it still has issues
    content = content.replace(
        /export default function UserManagementFixed\([^)]+\):\s*\([^)]+\)\.ReactElement\s*{/,
        'export default function UserManagementFixed({ language }: UserManagementProps) {'
    );
    
    // Fix any remaining malformed patterns
    content = content.replace(/\(\s*as\s+any\s*\)\s*=>/g, '() =>');
    content = content.replace(/\s*as\s+any\s*\)/g, ')');
    content = content.replace(/\(\s*as\s+any\s*\)/g, '()');
    
    // Clean up extra whitespace
    content = content.replace(/\n\s*\n\s*\n/g, '\n\n');
    
    if (content !== originalContent) {
        fs.writeFileSync(filePath, content);
        console.log(`✅ Fixed ${totalFixes} syntax errors in UserManagementFixed.tsx`);
        console.log('📊 New file size:', content.length, 'characters');
        console.log('📈 Size change:', content.length - originalContent.length, 'characters');
        
        // Verify the file is valid JavaScript syntax
        try {
            // Basic syntax check - try to parse as JavaScript
            const lines = content.split('\n');
            let braceCount = 0;
            let parenCount = 0;
            
            for (let i = 0; i < lines.length; i++) {
                const line = lines[i];
                braceCount += (line.match(/{/g) || []).length;
                braceCount -= (line.match(/}/g) || []).length;
                parenCount += (line.match(/\(/g) || []).length;
                parenCount -= (line.match(/\)/g) || []).length;
            }
            
            if (braceCount === 0 && parenCount === 0) {
                console.log('✅ Basic syntax validation passed');
            } else {
                console.log(`⚠️ Potential syntax issues: braces=${braceCount}, parens=${parenCount}`);
            }
            
        } catch (error) {
            console.log('⚠️ Syntax validation warning:', error.message);
        }
        
    } else {
        console.log('ℹ️ No syntax errors found to fix');
    }
    
} catch (error) {
    console.error('❌ Error fixing file:', error.message);
    process.exit(1);
}

console.log('🎉 UserManagement syntax fix completed!');
console.log('📋 Next steps:');
console.log('  1. Run TypeScript check: npx tsc --noEmit');
console.log('  2. Test the component in browser');
console.log('  3. Fix any remaining type errors');
