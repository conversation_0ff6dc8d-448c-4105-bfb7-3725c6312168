#!/usr/bin/env node
/**
 * TypeScript Error Analysis & Categorization Script
 * Analyzes TypeScript errors and creates a systematic cleanup strategy
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// ANSI color codes for better output
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function colorize(text, color) {
    return `${colors[color]}${text}${colors.reset}`;
}

function printHeader(title) {
    console.log('\n' + '='.repeat(80));
    console.log(colorize(`🔍 ${title}`, 'cyan'));
    console.log('='.repeat(80));
}

function printSection(title) {
    console.log('\n' + colorize(`📊 ${title}`, 'yellow'));
    console.log('-'.repeat(60));
}

class TypeScriptAnalyzer {
    constructor() {
        this.srcDir = path.join(__dirname, '..', 'src');
        this.errorCategories = {
            'syntax': { count: 0, files: new Set(), examples: [] },
            'imports': { count: 0, files: new Set(), examples: [] },
            'types': { count: 0, files: new Set(), examples: [] },
            'props': { count: 0, files: new Set(), examples: [] },
            'hooks': { count: 0, files: new Set(), examples: [] },
            'api': { count: 0, files: new Set(), examples: [] },
            'components': { count: 0, files: new Set(), examples: [] },
            'other': { count: 0, files: new Set(), examples: [] }
        };
        this.fileStats = {
            total: 0,
            tsx: 0,
            ts: 0,
            withErrors: 0
        };
    }

    analyzeFileStructure() {
        printSection('ANALYZING FILE STRUCTURE');
        
        const analyzeDirectory = (dir) => {
            const items = fs.readdirSync(dir);
            
            for (const item of items) {
                const fullPath = path.join(dir, item);
                const stat = fs.statSync(fullPath);
                
                if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
                    analyzeDirectory(fullPath);
                } else if (stat.isFile()) {
                    this.fileStats.total++;
                    
                    if (item.endsWith('.tsx')) {
                        this.fileStats.tsx++;
                    } else if (item.endsWith('.ts')) {
                        this.fileStats.ts++;
                    }
                }
            }
        };

        try {
            analyzeDirectory(this.srcDir);
            
            console.log(`  📁 Total files analyzed: ${this.fileStats.total}`);
            console.log(`  📄 TypeScript files (.ts): ${this.fileStats.ts}`);
            console.log(`  ⚛️ React TypeScript files (.tsx): ${this.fileStats.tsx}`);
            console.log(`  📊 TypeScript files total: ${this.fileStats.ts + this.fileStats.tsx}`);
            
        } catch (error) {
            console.log(`  ❌ Error analyzing file structure: ${error.message}`);
        }
    }

    analyzeCommonIssues() {
        printSection('ANALYZING COMMON TYPESCRIPT ISSUES');
        
        const commonPatterns = [
            {
                name: 'Missing type imports',
                pattern: /import.*from ['"]react['"]/g,
                category: 'imports',
                description: 'React imports without proper types'
            },
            {
                name: 'Untyped props',
                pattern: /function\s+\w+\s*\(\s*\{[^}]*\}\s*\)/g,
                category: 'props',
                description: 'Function components without typed props'
            },
            {
                name: 'Any types',
                pattern: /:\s*any/g,
                category: 'types',
                description: 'Explicit any types that need proper typing'
            },
            {
                name: 'Untyped useState',
                pattern: /useState\s*\(/g,
                category: 'hooks',
                description: 'useState hooks without type parameters'
            },
            {
                name: 'Untyped API calls',
                pattern: /fetch\s*\(|axios\./g,
                category: 'api',
                description: 'API calls without proper response typing'
            }
        ];

        const analyzeFile = (filePath) => {
            try {
                const content = fs.readFileSync(filePath, 'utf8');
                let hasErrors = false;

                for (const pattern of commonPatterns) {
                    const matches = content.match(pattern.pattern);
                    if (matches) {
                        hasErrors = true;
                        this.errorCategories[pattern.category].count += matches.length;
                        this.errorCategories[pattern.category].files.add(filePath);
                        
                        // Store example if we don't have many yet
                        if (this.errorCategories[pattern.category].examples.length < 3) {
                            this.errorCategories[pattern.category].examples.push({
                                file: path.relative(this.srcDir, filePath),
                                issue: pattern.name,
                                count: matches.length
                            });
                        }
                    }
                }

                if (hasErrors) {
                    this.fileStats.withErrors++;
                }

            } catch (error) {
                console.log(`    ⚠️ Error reading ${filePath}: ${error.message}`);
            }
        };

        const scanDirectory = (dir) => {
            const items = fs.readdirSync(dir);
            
            for (const item of items) {
                const fullPath = path.join(dir, item);
                const stat = fs.statSync(fullPath);
                
                if (stat.isDirectory() && !item.startsWith('.')) {
                    scanDirectory(fullPath);
                } else if (item.endsWith('.tsx') || item.endsWith('.ts')) {
                    analyzeFile(fullPath);
                }
            }
        };

        try {
            scanDirectory(this.srcDir);
            
            console.log(`  📊 Files with potential issues: ${this.fileStats.withErrors}`);
            console.log(`  📈 Issue coverage: ${((this.fileStats.withErrors / (this.fileStats.ts + this.fileStats.tsx)) * 100).toFixed(1)}%`);
            
        } catch (error) {
            console.log(`  ❌ Error during analysis: ${error.message}`);
        }
    }

    generateErrorReport() {
        printSection('ERROR CATEGORIZATION REPORT');
        
        const sortedCategories = Object.entries(this.errorCategories)
            .sort(([,a], [,b]) => b.count - a.count)
            .filter(([,data]) => data.count > 0);

        if (sortedCategories.length === 0) {
            console.log('  ✅ No common TypeScript issues detected in static analysis');
            return;
        }

        console.log('  📋 Issues by category (estimated):');
        
        for (const [category, data] of sortedCategories) {
            const percentage = ((data.count / this.getTotalIssues()) * 100).toFixed(1);
            console.log(`\n    ${colorize(category.toUpperCase(), 'magenta')}: ${data.count} issues (${percentage}%)`);
            console.log(`      📁 Files affected: ${data.files.size}`);
            
            if (data.examples.length > 0) {
                console.log(`      📝 Examples:`);
                for (const example of data.examples.slice(0, 2)) {
                    console.log(`        • ${example.file}: ${example.issue} (${example.count}x)`);
                }
            }
        }
    }

    getTotalIssues() {
        return Object.values(this.errorCategories).reduce((sum, data) => sum + data.count, 0);
    }

    generateCleanupStrategy() {
        printSection('SYSTEMATIC CLEANUP STRATEGY');
        
        const strategy = [
            {
                phase: 'Phase 1: Critical Build Blockers',
                priority: 'HIGH',
                tasks: [
                    'Fix syntax errors preventing compilation',
                    'Resolve missing import statements',
                    'Fix module resolution issues',
                    'Address critical type errors'
                ],
                estimatedTime: '2-3 days',
                impact: 'Enables builds and development workflow'
            },
            {
                phase: 'Phase 2: Component Type Safety',
                priority: 'HIGH',
                tasks: [
                    'Add proper prop interfaces for all components',
                    'Type React component state and refs',
                    'Fix hook usage with proper types',
                    'Add event handler typing'
                ],
                estimatedTime: '3-5 days',
                impact: 'Prevents runtime errors and improves DX'
            },
            {
                phase: 'Phase 3: API Integration Types',
                priority: 'MEDIUM',
                tasks: [
                    'Create interfaces for all API responses',
                    'Type API request payloads',
                    'Add error handling types',
                    'Implement proper async/await typing'
                ],
                estimatedTime: '2-3 days',
                impact: 'Ensures type safety in data flow'
            },
            {
                phase: 'Phase 4: Advanced Type Features',
                priority: 'LOW',
                tasks: [
                    'Enable strict mode gradually',
                    'Add utility types and generics',
                    'Implement discriminated unions',
                    'Add comprehensive JSDoc comments'
                ],
                estimatedTime: '3-4 days',
                impact: 'Maximizes TypeScript benefits'
            }
        ];

        for (const phase of strategy) {
            console.log(`\n  ${colorize(phase.phase, 'cyan')} (${colorize(phase.priority, phase.priority === 'HIGH' ? 'red' : phase.priority === 'MEDIUM' ? 'yellow' : 'green')})`);
            console.log(`    ⏱️ Estimated time: ${phase.estimatedTime}`);
            console.log(`    🎯 Impact: ${phase.impact}`);
            console.log(`    📋 Tasks:`);
            
            for (const task of phase.tasks) {
                console.log(`      • ${task}`);
            }
        }
    }

    generateConfigurationRecommendations() {
        printSection('TYPESCRIPT CONFIGURATION RECOMMENDATIONS');
        
        console.log('  📋 Current configuration issues:');
        console.log('    ❌ Complete error suppression mode enabled');
        console.log('    ❌ All strict checks disabled');
        console.log('    ❌ No type checking during development');
        
        console.log('\n  ✅ Recommended configuration changes:');
        console.log('    1. Enable gradual strict mode');
        console.log('    2. Add proper error reporting');
        console.log('    3. Configure path mapping correctly');
        console.log('    4. Enable incremental compilation');
        
        const recommendedConfig = {
            compilerOptions: {
                // Gradual strictness
                strict: false, // Start false, enable gradually
                noImplicitAny: true, // Enable first
                strictNullChecks: false, // Enable after fixing any types
                
                // Essential checks
                noUnusedLocals: true,
                noUnusedParameters: true,
                noImplicitReturns: true,
                
                // Better error reporting
                noErrorTruncation: true,
                skipLibCheck: true,
                
                // Development experience
                incremental: true,
                tsBuildInfoFile: './node_modules/.tmp/tsconfig.app.tsbuildinfo'
            }
        };
        
        console.log('\n  📝 Recommended tsconfig.app.json changes:');
        console.log('    • Enable noImplicitAny first');
        console.log('    • Add noUnusedLocals and noUnusedParameters');
        console.log('    • Keep skipLibCheck for faster builds');
        console.log('    • Enable incremental compilation');
    }

    generateActionPlan() {
        printHeader('TYPESCRIPT CLEANUP ACTION PLAN');
        
        const totalIssues = this.getTotalIssues();
        const estimatedEffort = this.calculateEstimatedEffort();
        
        console.log(`📊 ${colorize('ANALYSIS SUMMARY', 'bright')}`);
        console.log(`  • Total TypeScript files: ${this.fileStats.ts + this.fileStats.tsx}`);
        console.log(`  • Files with issues: ${this.fileStats.withErrors}`);
        console.log(`  • Estimated issues: ${totalIssues}+`);
        console.log(`  • Estimated cleanup time: ${estimatedEffort}`);
        
        console.log(`\n🎯 ${colorize('IMMEDIATE NEXT STEPS', 'bright')}`);
        console.log('  1. Start with Phase 1: Critical Build Blockers');
        console.log('  2. Enable noImplicitAny in tsconfig.app.json');
        console.log('  3. Fix the most critical files first');
        console.log('  4. Set up proper development workflow');
        
        console.log(`\n📈 ${colorize('SUCCESS METRICS', 'bright')}`);
        console.log('  • Target: <100 TypeScript errors');
        console.log('  • Goal: Successful npm run build');
        console.log('  • Milestone: Enable strict mode');
        console.log('  • Ultimate: Zero TypeScript errors');
        
        return {
            totalFiles: this.fileStats.ts + this.fileStats.tsx,
            filesWithIssues: this.fileStats.withErrors,
            estimatedIssues: totalIssues,
            estimatedEffort,
            categories: this.errorCategories
        };
    }

    calculateEstimatedEffort() {
        const totalFiles = this.fileStats.ts + this.fileStats.tsx;
        const issueRate = this.fileStats.withErrors / totalFiles;
        
        if (issueRate > 0.8) return '2-3 weeks';
        if (issueRate > 0.6) return '1-2 weeks';
        if (issueRate > 0.4) return '1 week';
        return '3-5 days';
    }

    run() {
        printHeader('TYPESCRIPT ERROR ANALYSIS & CATEGORIZATION');
        console.log(colorize('Analyzing TypeScript codebase for systematic cleanup strategy...', 'blue'));
        
        this.analyzeFileStructure();
        this.analyzeCommonIssues();
        this.generateErrorReport();
        this.generateCleanupStrategy();
        this.generateConfigurationRecommendations();
        
        const results = this.generateActionPlan();
        
        console.log(`\n${colorize('✅ Analysis completed!', 'green')}`);
        console.log(`${colorize('📋 Review the action plan above and proceed with Phase 1.', 'blue')}`);
        
        return results;
    }
}

// Run the analysis
if (require.main === module) {
    const analyzer = new TypeScriptAnalyzer();
    analyzer.run();
}

module.exports = TypeScriptAnalyzer;
