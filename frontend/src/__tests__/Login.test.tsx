/**
 * Comprehensive Login Component Tests
 * Tests authentication, security, accessibility, and user interactions
 */

import React from 'react'
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { Provider } from 'react-redux'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-router-dom'
import { configureStore } from '@reduxjs/toolkit'
import Login from '../pages/Login'
import authSlice from '../store/slices/authSlice'
import { ToastProvider } from '../contexts/ToastContext'

// Mock utilities
jest.mock('../utils/securityHeaders', () => ({
  checkLoginRateLimit: jest.fn(() => ({
    allowed: true,
    remaining: 4,
    resetTime: Date.now() + 900000,
    blocked: false
  })),
  logSecurityEvent: jest.fn()
}))

jest.mock('../utils/accessibilityEnhancements', () => ({
  ScreenReaderAnnouncer: {
    announce: jest.fn()
  },
  RTLSupport: {
    applyRTLStyles: jest.fn()
  },
  KEYBOARD_KEYS: {
    ENTER: 'Enter',
    ESCAPE: 'Escape'
  }
}))

jest.mock('../hooks/useCSRF', () => ({
  useCSRF: () => ({
    ensureToken: jest.fn().mockResolvedValue('mock-token')
  })
}))

// Mock navigation
const mockNavigate = jest.fn()
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
  Navigate: ({ to }: { to: string }) => <div data-testid="navigate-to">{to}</div>
}))

// Test store setup
const createTestStore = (initialState = {}): void => {
  return configureStore({
    reducer: {
      auth: authSlice
    },
    preloadedState: {
      auth: {
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
        loginAttempts: 0,
        lastLoginAttempt: null,
        csrfToken: null,
        csrfTokenExpiry: 0,
        isFetchingCsrf: false,
        ...initialState
      }
    }
  })
}

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode; store?: any }> = ({ 
  children, 
  store = createTestStore() 
}) => (
  <Provider store={store}>
    <BrowserRouter>
      <ToastProvider>
        {children}
      </ToastProvider>
    </BrowserRouter>
  </Provider>
)

describe('Login Component', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockNavigate.mockClear()
  })

  describe('Rendering and Basic Functionality', () => {
    test('renders login form with all required elements', () => {
      render(
        <TestWrapper>
          <Login language="en" />
        </TestWrapper>
      )

      expect(screen.getByRole('main')).toBeInTheDocument()
      expect(screen.getByLabelText(/email/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/password/i)).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /login/i })).toBeInTheDocument()
      expect(screen.getByRole('checkbox', { name: /remember/i })).toBeInTheDocument()
    })

    test('renders in Arabic with RTL support', () => {
      render(
        <TestWrapper>
          <Login language="ar" />
        </TestWrapper>
      )

      const mainElement = screen.getByRole('main')
      expect(mainElement).toHaveClass('rtl')
      expect(screen.getByText('تسجيل الدخول')).toBeInTheDocument()
    })

    test('applies proper ARIA labels and accessibility attributes', () => {
      render(
        <TestWrapper>
          <Login language="en" />
        </TestWrapper>
      )

      const form = screen.getByRole('form')
      expect(form).toHaveAttribute('aria-labelledby', 'login-heading')
      
      const usernameInput = screen.getByLabelText(/email/i)
      expect(usernameInput).toHaveAttribute('name', 'username')
      expect(usernameInput).toHaveAttribute('autoComplete', 'username')
      
      const passwordInput = screen.getByLabelText(/password/i)
      expect(passwordInput).toHaveAttribute('name', 'password')
      expect(passwordInput).toHaveAttribute('autoComplete', 'current-password')
    })
  })

  describe('Form Validation', () => {
    test('shows validation errors for empty fields', async () => {
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <Login language="en" />
        </TestWrapper>
      )

      const submitButton = screen.getByRole('button', { name: /login/i })
      await user.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText(/email is required/i)).toBeInTheDocument()
        expect(screen.getByText(/password is required/i)).toBeInTheDocument()
      })
    })

    test('validates email format', async () => {
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <Login language="en" />
        </TestWrapper>
      )

      const emailInput = screen.getByLabelText(/email/i)
      await user.type(emailInput, 'invalid-email')
      
      const submitButton = screen.getByRole('button', { name: /login/i })
      await user.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText(/please enter a valid email/i)).toBeInTheDocument()
      })
    })

    test('clears validation errors when user starts typing', async () => {
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <Login language="en" />
        </TestWrapper>
      )

      // Trigger validation error
      const submitButton = screen.getByRole('button', { name: /login/i })
      await user.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText(/email is required/i)).toBeInTheDocument()
      })

      // Start typing to clear error
      const emailInput = screen.getByLabelText(/email/i)
      await user.type(emailInput, '<EMAIL>')

      await waitFor(() => {
        expect(screen.queryByText(/email is required/i)).not.toBeInTheDocument()
      })
    })
  })

  describe('Security Features', () => {
    test('implements rate limiting', async () => {
      const { checkLoginRateLimit } = require('../utils/securityHeaders')
      checkLoginRateLimit.mockReturnValue({
        allowed: false,
        remaining: 0,
        resetTime: Date.now() + 900000,
        blocked: true
      })

      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <Login language="en" />
        </TestWrapper>
      )

      const emailInput = screen.getByLabelText(/email/i)
      const passwordInput = screen.getByLabelText(/password/i)
      const submitButton = screen.getByRole('button', { name: /login/i })

      await user.type(emailInput, '<EMAIL>')
      await user.type(passwordInput, 'password123')
      await user.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText(/too many login attempts/i)).toBeInTheDocument()
      })

      expect(checkLoginRateLimit).toHaveBeenCalledWith('<EMAIL>')
    })

    test('logs security events', async () => {
      const { logSecurityEvent } = require('../utils/securityHeaders')
      
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <Login language="en" />
        </TestWrapper>
      )

      const emailInput = screen.getByLabelText(/email/i)
      const passwordInput = screen.getByLabelText(/password/i)
      const submitButton = screen.getByRole('button', { name: /login/i })

      await user.type(emailInput, '<EMAIL>')
      await user.type(passwordInput, 'password123')
      await user.click(submitButton)

      // Should log security event for rate limiting check
      expect(logSecurityEvent).toHaveBeenCalled()
    })

    test('ensures CSRF token before submission', async () => {
      const { useCSRF } = require('../hooks/useCSRF')
      const mockEnsureToken = jest.fn().mockResolvedValue('mock-token')
      useCSRF.mockReturnValue({ ensureToken: mockEnsureToken })

      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <Login language="en" />
        </TestWrapper>
      )

      const emailInput = screen.getByLabelText(/email/i)
      const passwordInput = screen.getByLabelText(/password/i)
      const submitButton = screen.getByRole('button', { name: /login/i })

      await user.type(emailInput, '<EMAIL>')
      await user.type(passwordInput, 'password123')
      await user.click(submitButton)

      await waitFor(() => {
        expect(mockEnsureToken).toHaveBeenCalled()
      })
    })
  })

  describe('Accessibility Features', () => {
    test('announces page load to screen readers', () => {
      const { ScreenReaderAnnouncer } = require('../utils/accessibilityEnhancements')
      
      render(
        <TestWrapper>
          <Login language="en" />
        </TestWrapper>
      )

      expect(ScreenReaderAnnouncer.announce).toHaveBeenCalledWith('Login page loaded')
    })

    test('announces successful login', async () => {
      const { ScreenReaderAnnouncer } = require('../utils/accessibilityEnhancements')
      
      // Mock successful login
      const store = createTestStore({
        isAuthenticated: true,
        user: { id: 1, email: '<EMAIL>' }
      })

      render(
        <TestWrapper store={store}>
          <Login language="en" />
        </TestWrapper>
      )

      // Should redirect when authenticated
      expect(screen.getByTestId('navigate-to')).toHaveTextContent('/')
    })

    test('handles keyboard navigation', async () => {
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <Login language="en" />
        </TestWrapper>
      )

      const form = screen.getByRole('form')
      
      // Test Escape key clears form
      const emailInput = screen.getByLabelText(/email/i)
      await user.type(emailInput, '<EMAIL>')
      
      await user.keyboard('{Escape}')
      
      expect(emailInput).toHaveValue('')
    })

    test('shows error messages with proper ARIA attributes', async () => {
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <Login language="en" />
        </TestWrapper>
      )

      const submitButton = screen.getByRole('button', { name: /login/i })
      await user.click(submitButton)

      await waitFor(() => {
        const errorMessage = screen.getByText(/email is required/i)
        expect(errorMessage).toBeInTheDocument()
        
        // Check that error is associated with input
        const emailInput = screen.getByLabelText(/email/i)
        expect(emailInput).toHaveAttribute('aria-invalid', 'true')
      })
    })
  })

  describe('User Interactions', () => {
    test('toggles password visibility', async () => {
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <Login language="en" />
        </TestWrapper>
      )

      const passwordInput = screen.getByLabelText(/password/i)
      const toggleButton = screen.getByRole('button', { name: /show password/i })

      expect(passwordInput).toHaveAttribute('type', 'password')
      
      await user.click(toggleButton)
      expect(passwordInput).toHaveAttribute('type', 'text')
      
      await user.click(toggleButton)
      expect(passwordInput).toHaveAttribute('type', 'password')
    })

    test('handles remember me checkbox', async () => {
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <Login language="en" />
        </TestWrapper>
      )

      const rememberCheckbox = screen.getByRole('checkbox', { name: /remember/i })
      
      expect(rememberCheckbox).not.toBeChecked()
      
      await user.click(rememberCheckbox)
      expect(rememberCheckbox).toBeChecked()
    })

    test('shows forgot password form', async () => {
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <Login language="en" />
        </TestWrapper>
      )

      const forgotPasswordLink = screen.getByRole('button', { name: /forgot password/i })
      await user.click(forgotPasswordLink)

      expect(screen.getByText(/reset password/i)).toBeInTheDocument()
    })
  })

  describe('Error Handling', () => {
    test('displays authentication errors', () => {
      const store = createTestStore({
        error: 'Invalid credentials'
      })

      render(
        <TestWrapper store={store}>
          <Login language="en" />
        </TestWrapper>
      )

      const errorAlert = screen.getByRole('alert')
      expect(errorAlert).toBeInTheDocument()
      expect(errorAlert).toHaveTextContent('Invalid credentials')
    })

    test('allows dismissing error messages', async () => {
      const user = userEvent.setup()
      
      const store = createTestStore({
        error: 'Invalid credentials'
      })

      render(
        <TestWrapper store={store}>
          <Login language="en" />
        </TestWrapper>
      )

      const dismissButton = screen.getByLabelText(/dismiss error/i)
      await user.click(dismissButton)

      await waitFor(() => {
        expect(screen.queryByRole('alert')).not.toBeInTheDocument()
      })
    })
  })

  describe('Loading States', () => {
    test('disables form during loading', () => {
      const store = createTestStore({
        isLoading: true
      })

      render(
        <TestWrapper store={store}>
          <Login language="en" />
        </TestWrapper>
      )

      const emailInput = screen.getByLabelText(/email/i)
      const passwordInput = screen.getByLabelText(/password/i)
      const submitButton = screen.getByRole('button', { name: /logging in/i })

      expect(emailInput).toBeDisabled()
      expect(passwordInput).toBeDisabled()
      expect(submitButton).toBeDisabled()
    })

    test('shows loading spinner during authentication', () => {
      const store = createTestStore({
        isLoading: true
      })

      render(
        <TestWrapper store={store}>
          <Login language="en" />
        </TestWrapper>
      )

      expect(screen.getByText(/logging in/i)).toBeInTheDocument()
    })
  })
})
