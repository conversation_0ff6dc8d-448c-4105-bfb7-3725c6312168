import React from 'react';
/**
 * Security Headers and Rate Limiting Tests
 * Tests security features, rate limiting, and CSRF protection
 */

import { checkLoginRateLimit, logSecurityEvent } from '../utils/securityHeaders'

// Mock logger
jest.mock('../utils/logger', () => ({
  log: {
    warn: jest.fn(),
    info: jest.fn()
  }
}))

describe('Security Headers Utilities', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    // Clear localStorage to reset rate limiting
    localStorage.clear()
  })

  describe('Rate Limiting', () => {
    test('allows initial login attempt', () => {
      const result: any = checkLoginRateLimit('<EMAIL>')
      
      expect(result.allowed).toBe(true)
      expect(result.remaining).toBe(4) // 5 max attempts - 1 used
      expect(result.blocked).toBe(false)
      expect(result.resetTime).toBeGreaterThan(Date.now())
    })

    test('tracks multiple attempts for same user', () => {
      const email = '<EMAIL>'
      
      // First attempt
      let result = checkLoginRateLimit(email)
      expect(result.allowed).toBe(true)
      expect(result.remaining).toBe(4)
      
      // Second attempt
      result = checkLoginRateLimit(email)
      expect(result.allowed).toBe(true)
      expect(result.remaining).toBe(3)
      
      // Third attempt
      result = checkLoginRateLimit(email)
      expect(result.allowed).toBe(true)
      expect(result.remaining).toBe(2)
    })

    test('blocks user after exceeding rate limit', () => {
      const email = '<EMAIL>'
      
      // Use up all attempts
      for (let i = 0; i < 5; i++) {
        checkLoginRateLimit(email)
      }
      
      // Next attempt should be blocked
      const result: any = checkLoginRateLimit(email)
      expect(result.allowed).toBe(false)
      expect(result.remaining).toBe(0)
      expect(result.blocked).toBe(true)
    })

    test('maintains separate counters for different users', () => {
      const user1 = '<EMAIL>'
      const user2 = '<EMAIL>'
      
      // User 1 makes attempts
      checkLoginRateLimit(user1)
      checkLoginRateLimit(user1)
      
      // User 2 should start fresh
      const result = checkLoginRateLimit(user2)
      expect(result.allowed).toBe(true)
      expect(result.remaining).toBe(4)
    })

    test('resets rate limit after time window expires', () => {
      const email = '<EMAIL>'
      
      // Mock Date.now to simulate time passage
      const originalNow = Date.now
      let mockTime = originalNow()
      Date.now = jest.fn(() => mockTime)
      
      // Use up attempts
      for (let i = 0; i < 5; i++) {
        checkLoginRateLimit(email)
      }
      
      // Should be blocked
      let result: any = checkLoginRateLimit(email)
      expect(result.blocked).toBe(true)
      
      // Advance time past reset window (15 minutes)
      mockTime += 16 * 60 * 1000
      
      // Should be allowed again
      result = checkLoginRateLimit(email)
      expect(result.allowed).toBe(true)
      expect(result.remaining).toBe(4)
      
      // Restore original Date.now
      Date.now = originalNow
    })

    test('logs security events when rate limit exceeded', () => {
      const { log } = require('../utils/logger')
      const email = '<EMAIL>'
      
      // Exceed rate limit
      for (let i = 0; i < 6; i++) {
        checkLoginRateLimit(email)
      }
      
      expect(log.warn).toHaveBeenCalledWith(
        'security',
        'Rate limit exceeded',
        expect.objectContaining({
          key: `login:${email}`,
          count: 6,
          maxAttempts: 5
        })
      )
    })
  })

  describe('Security Event Logging', () => {
    test('logs security events with proper context', () => {
      const { log } = require('../utils/logger')
      
      // Mock window properties
      Object.defineProperty(window, 'location', {
        value: { href: 'https://example.com/login' },
        writable: true
      })
      
      Object.defineProperty(navigator, 'userAgent', {
        value: 'Mozilla/5.0 Test Browser',
        writable: true
      })
      
      logSecurityEvent('test_event', { userId: 123 })
      
      expect(log.warn).toHaveBeenCalledWith(
        'security',
        'Security event: test_event',
        expect.objectContaining({
          timestamp: expect.any(String),
          userAgent: 'Mozilla/5.0 Test Browser',
          url: 'https://example.com/login',
          userId: 123
        })
      )
    })

    test('includes custom details in security logs', () => {
      const { log } = require('../utils/logger')
      
      const customDetails: any = {
        attemptedAction: 'unauthorized_access',
        resourceId: 'sensitive-data',
        severity: 'high'
      }
      
      logSecurityEvent('unauthorized_access_attempt', customDetails)
      
      expect(log.warn).toHaveBeenCalledWith(
        'security',
        'Security event: unauthorized_access_attempt',
        expect.objectContaining(customDetails)
      )
    })
  })

  describe('Rate Limiter Class Integration', () => {
    test('handles concurrent rate limit checks', () => {
      const email = '<EMAIL>'
      
      // Simulate concurrent requests
      const results = []
      for (let i = 0; i < 3; i++) {
        results.push(checkLoginRateLimit(email))
      }
      
      // All should be allowed but with decreasing remaining counts
      expect(results[0].allowed).toBe(true)
      expect(results[0].remaining).toBe(4)
      
      expect(results[1].allowed).toBe(true)
      expect(results[1].remaining).toBe(3)
      
      expect(results[2].allowed).toBe(true)
      expect(results[2].remaining).toBe(2)
    })

    test('maintains consistent reset times within window', () => {
      const email: any = '<EMAIL>'
      
      const result1 = checkLoginRateLimit(email)
      
      // Small delay
      setTimeout(() => {
        const result2 = checkLoginRateLimit(email)
        
        // Reset times should be the same (within same window)
        expect(result2.resetTime).toBe(result1.resetTime)
      }, 100)
    })

    test('handles edge case of exactly max attempts', () => {
      const email = '<EMAIL>'
      
      // Make exactly 5 attempts (the limit)
      let result
      for (let i = 0; i < 5; i++) {
        result = checkLoginRateLimit(email)
      }
      
      // 5th attempt should still be allowed
      expect(result.allowed).toBe(true)
      expect(result.remaining).toBe(0)
      expect(result.blocked).toBe(false)
      
      // 6th attempt should be blocked
      result = checkLoginRateLimit(email)
      expect(result.allowed).toBe(false)
      expect(result.blocked).toBe(true)
    })
  })

  describe('Error Handling', () => {
    test('handles invalid email formats gracefully', () => {
      const invalidEmails: any = ['', null, undefined, 'not-an-email', '@domain.com']
      
      invalidEmails.forEach(email => {
        expect(() => {
          checkLoginRateLimit(email as string)
        }).not.toThrow()
      })
    })

    test('handles storage errors gracefully', () => {
      // Mock localStorage to throw error
      const originalSetItem = Storage.prototype.setItem
      Storage.prototype.setItem = jest.fn(() => {
        throw new Error('Storage quota exceeded')
      })
      
      expect(() => {
        checkLoginRateLimit('<EMAIL>')
      }).not.toThrow()
      
      // Restore original setItem
      Storage.prototype.setItem = originalSetItem
    })
  })

  describe('Performance', () => {
    test('rate limiting check completes quickly', () => {
      const start = performance.now()
      
      for (let i = 0; i < 100; i++) {
        checkLoginRateLimit(`user${i}@example.com`)
      }
      
      const end: any = performance.now()
      const duration = end - start
      
      // Should complete 100 checks in under 100ms
      expect(duration).toBeLessThan(100)
    })

    test('memory usage remains reasonable with many users', () => {
      // Create rate limit entries for many users
      for (let i = 0; i < 1000; i++) {
        checkLoginRateLimit(`user${i}@example.com`)
      }
      
      // This test mainly ensures no memory leaks or crashes
      // In a real scenario, you might check actual memory usage
      expect(true).toBe(true)
    })
  })

  describe('Security Best Practices', () => {
    test('rate limit keys are properly namespaced', () => {
      const { log } = require('../utils/logger')
      
      checkLoginRateLimit('<EMAIL>')
      // Trigger rate limit to see the key in logs
      for (let i: any = 0; i < 6; i++) {
        checkLoginRateLimit('<EMAIL>')
      }
      
      expect(log.warn).toHaveBeenCalledWith(
        'security',
        'Rate limit exceeded',
        expect.objectContaining({
          key: 'login:<EMAIL>'
        })
      )
    })

    test('does not expose sensitive information in logs', () => {
      const { log } = require('../utils/logger')
      
      logSecurityEvent('login_attempt', {
        username: '<EMAIL>',
        password: 'secret123', // This should not be logged
        sessionId: 'abc123'
      })
      
      const logCall = log.warn.mock.calls[0]
      const loggedData = JSON.stringify(logCall)
      
      // Should not contain password
      expect(loggedData).not.toContain('secret123')
      // Should contain other safe information
      expect(loggedData).toContain('<EMAIL>')
      expect(loggedData).toContain('abc123')
    })
  })
})
