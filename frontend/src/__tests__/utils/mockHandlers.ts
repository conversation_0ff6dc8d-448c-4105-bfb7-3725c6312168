import { rest } from 'msw'

const API_BASE_URL = 'http://localhost:8000/api'

export const handlers = [
  // Authentication endpoints
  rest.post(`${API_BASE_URL}/auth/login/`, (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        access: 'mock-access-token',
        refresh: 'mock-refresh-token',
        user: {
          id: 1,
          username: 'testuser',
          email: '<EMAIL>',
          first_name: 'Test',
          last_name: 'User',
          role: { id: 'employee', name: 'Employee' },
          profile: { department: 'IT' }
        }
      })
    )
  }),

  rest.post(`${API_BASE_URL}/auth/logout/`, (req, res, ctx) => {
    return res(ctx.status(200), ctx.json({ message: 'Logged out successfully' }))
  }),

  // Employee endpoints
  rest.get(`${API_BASE_URL}/v1/employees/`, (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        data: [
          {
            id: 1,
            name: '<PERSON>',
            email: '<EMAIL>',
            department_id: 1,
            position: 'Developer',
            hire_date: '2024-01-01',
            status: 'active'
          },
          {
            id: 2,
            name: 'Jane Smith',
            email: '<EMAIL>',
            department_id: 2,
            position: 'Designer',
            hire_date: '2024-01-15',
            status: 'active'
          }
        ],
        message: 'Employees retrieved successfully',
        status: 'success'
      })
    )
  }),

  rest.post(`${API_BASE_URL}/v1/employees/`, (req, res, ctx) => {
    return res(
      ctx.status(201),
      ctx.json({
        data: {
          id: 3,
          name: 'New Employee',
          email: '<EMAIL>',
          department_id: 1,
          position: 'Intern',
          hire_date: '2024-07-25',
          status: 'active'
        },
        message: 'Employee created successfully',
        status: 'success'
      })
    )
  }),

  // Department endpoints
  rest.get(`${API_BASE_URL}/v1/departments/`, (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        data: [
          { id: 1, name: 'IT', description: 'Information Technology' },
          { id: 2, name: 'HR', description: 'Human Resources' },
          { id: 3, name: 'Finance', description: 'Finance Department' }
        ],
        message: 'Departments retrieved successfully',
        status: 'success'
      })
    )
  }),

  // Error handlers for testing
  rest.get(`${API_BASE_URL}/error`, (req, res, ctx) => {
    return res(
      ctx.status(500),
      ctx.json({
        message: 'Internal server error',
        status: 'error'
      })
    )
  }),

  rest.get(`${API_BASE_URL}/unauthorized`, (req, res, ctx) => {
    return res(
      ctx.status(401),
      ctx.json({
        message: 'Unauthorized access',
        status: 'error'
      })
    )
  })
]
