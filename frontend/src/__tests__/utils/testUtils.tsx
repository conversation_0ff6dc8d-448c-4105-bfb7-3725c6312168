import React from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { Provider } from 'react-redux'
import { <PERSON><PERSON>er<PERSON>outer } from 'react-router-dom'
import { store } from '@/store'

// Enhanced render function with all necessary providers
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  return (
    <Provider store={store}>
      <BrowserRouter>
        <div data-testid="test-wrapper">
          {children}
        </div>
      </BrowserRouter>
    </Provider>
  )
}

const customRender = (
  ui: React.ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options })

// Test utilities for common patterns
export const createMockUser = (overrides = {}) => ({
  id: 1,
  username: 'testuser',
  email: '<EMAIL>',
  first_name: 'Test',
  last_name: 'User',
  role: { id: 'employee', name: 'Employee' },
  profile: { department: 'IT' },
  ...overrides
})

export const createMockEmployee = (overrides = {}) => ({
  id: 1,
  name: '<PERSON>',
  email: '<EMAIL>',
  department_id: 1,
  position: 'Developer',
  hire_date: '2024-01-01',
  status: 'active',
  ...overrides
})

export const waitForLoadingToFinish = async () => {
  const { waitForElementToBeRemoved, queryByTestId } = await import('@testing-library/react')
  const loadingElement = queryByTestId(document.body, 'loading-spinner')
  if (loadingElement) {
    await waitForElementToBeRemoved(loadingElement)
  }
}

// Mock API responses
export const mockApiResponse = (data: any, status = 200) => ({
  ok: status >= 200 && status < 300,
  status,
  json: () => Promise.resolve(data),
  text: () => Promise.resolve(JSON.stringify(data)),
  headers: new Headers(),
})

export const mockApiError = (message = 'API Error', status = 500) => ({
  ok: false,
  status,
  json: () => Promise.resolve({ message }),
  text: () => Promise.resolve(JSON.stringify({ message })),
  headers: new Headers(),
})

// Re-export everything from React Testing Library
export * from '@testing-library/react'
export { customRender as render }
