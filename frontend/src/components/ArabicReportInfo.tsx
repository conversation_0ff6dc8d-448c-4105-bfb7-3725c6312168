/**
 * Arabic Report Information Component
 * Explains PDF report generation for Arabic users
 */

import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Info, FileText, Download, CheckCircle } from 'lucide-react'

interface ArabicReportInfoProps {
  language: 'ar' | 'en'
  hrMetrics?: {
    totalEmployees: number
    activeEmployees: number
    newHires: number
    presentToday: number
    avgPerformance: number
    monthlyPayroll: number
  }
}

const translations = {
  ar: {
    reportInfo: 'معلومات التقارير',
    pdfReports: 'تقارير PDF',
    reportLanguage: 'لغة التقارير',
    reportQuality: 'جودة التقارير',
    arabicReports: 'التقارير باللغة العربية',
    reasons: [
      'نصوص عربية كاملة في التقرير',
      'أسماء الأقسام باللغة العربية',
      'المؤشرات والعناوين بالعربية',
      'تصميم احترافي مع النص العربي',
      'سهولة القراءة والفهم'
    ],
    features: [
      'بيانات شاملة ودقيقة',
      'تصميم احترافي',
      'جداول منظمة',
      'مقاييس الأداء الرئيسية',
      'تحليل الأقسام',
      'معلومات سرية ومحمية'
    ],
    note: 'ملاحظة: النظام والتقارير متوفران باللغة العربية بالكامل مع دعم النصوص العربية',
    reportContains: 'يحتوي التقرير على:',
    reportFeatures: 'مميزات التقرير:'
  },
  en: {
    reportInfo: 'Report Information',
    pdfReports: 'PDF Reports',
    reportLanguage: 'Report Language',
    reportQuality: 'Report Quality',
    arabicReports: 'Arabic Reports',
    whyEnglish: 'Why English Reports?',
    reasons: [
      'Ensures data and number display quality',
      'Compatible with all PDF systems',
      'Easy printing and sharing',
      'International standards for financial reports',
      'Accurate display of tables and charts'
    ],
    features: [
      'Comprehensive and accurate data',
      'Professional design',
      'Organized tables',
      'Key performance indicators',
      'Department analysis',
      'Confidential and protected information'
    ],
    note: 'Note: System interface in Arabic, reports in English for best display quality',
    reportContains: 'Report contains:',
    reportFeatures: 'Report features:'
  }
}

export default function ArabicReportInfo({ language, hrMetrics }: ArabicReportInfoProps): React.ReactElement {
  const t = translations[language]
  const isRTL = language === 'ar'

  // Use real metrics or fallback to default values
  const metrics = hrMetrics || {
    totalEmployees: 13,
    activeEmployees: 13,
    newHires: 8,
    presentToday: 11,
    avgPerformance: 85,
    monthlyPayroll: 71667
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Main Info Card */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-3">
            <FileText className="h-6 w-6" />
            {t.reportInfo}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Info for Arabic users */}
          <div className="bg-blue-500/10 border border-blue-400/30 rounded-lg p-4 flex items-start gap-3">
            <Info className="h-5 w-5 text-blue-400 mt-0.5 flex-shrink-0" />
            <p className="text-blue-200 text-sm">
              {t.note}
            </p>
          </div>

          {/* Arabic Reports */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold text-white">{t.arabicReports}</h3>
            <ul className="space-y-2">
              {t.reasons.map((reason, index) => (
                <li key={index} className="flex items-start gap-3 text-white/80">
                  <CheckCircle className="h-4 w-4 text-green-400 mt-0.5 flex-shrink-0" />
                  <span>{reason}</span>
                </li>
              ))}
            </ul>
          </div>
        </CardContent>
      </Card>

      {/* Report Features */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-3">
            <Download className="h-6 w-6" />
            {t.reportFeatures}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {t.features.map((feature, index) => (
              <div key={index} className="flex items-center gap-3 p-3 glass-card border-white/10">
                <CheckCircle className="h-5 w-5 text-green-400" />
                <span className="text-white/90">{feature}</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Sample Report Preview */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white">
            {language === 'ar' ? 'نموذج من التقرير' : 'Report Sample'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-white/5 p-4 rounded-lg border border-white/10">
            <div className="text-white/70 text-sm font-mono space-y-2" dir="rtl">
              <div className="text-center font-bold text-blue-400">شركة الحلول الإدارية المتقدمة</div>
              <div className="text-center">الرياض، المملكة العربية السعودية</div>
              <div className="text-center">+966 11 123 4567 | <EMAIL></div>
              <div className="text-center font-bold text-lg mt-4">تقرير الموارد البشرية</div>
              <div className="text-center">2025-01-01 - 2025-06-08</div>

              <div className="mt-4">
                <div className="font-bold text-blue-400">المؤشرات الرئيسية</div>
                <div className="grid grid-cols-2 gap-2 mt-2 text-xs">
                  <div>إجمالي الموظفين: {metrics.totalEmployees.toLocaleString()}</div>
                  <div>الموظفون النشطون: {metrics.activeEmployees.toLocaleString()}</div>
                  <div>التوظيفات الجديدة: {metrics.newHires}</div>
                  <div>الحاضرون اليوم: {metrics.presentToday.toLocaleString()}</div>
                  <div>متوسط الأداء: {metrics.avgPerformance}%</div>
                  <div>إجمالي الرواتب: {(metrics.monthlyPayroll * 12).toLocaleString()} ر.س.</div>
                </div>
              </div>

              <div className="mt-4">
                <div className="font-bold text-blue-400">تحليل الأقسام</div>
                <div className="text-xs mt-2">
                  <div>إجمالي الأقسام: {Math.ceil(metrics.totalEmployees / 4)}</div>
                  <div>متوسط الموظفين لكل قسم: {Math.ceil(metrics.totalEmployees / Math.ceil(metrics.totalEmployees / 4))}</div>
                  <div>معدل الحضور: {((metrics.presentToday / metrics.totalEmployees) * 100).toFixed(1)}%</div>
                  <div>...</div>
                </div>
              </div>

              <div className="text-center text-xs mt-4 text-red-400">
                سري - للاستخدام الداخلي فقط
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
