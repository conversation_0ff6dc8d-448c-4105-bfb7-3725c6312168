import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  MessageCircle,
  Send,
  X,
  Minimize2,
  Bot,
  User,
  ThumbsUp,
  ThumbsDown,
  ExternalLink,
  Loader2
} from 'lucide-react';

interface ChatMessage {
  id: string;
  type: 'user' | 'ai' | 'system';
  content: string;
  timestamp: Date;
  confidence?: number;
  suggestions?: Array<{
    id: number;
    title: string;
    summary: string;
  }>;
  helpful?: boolean;
}

interface ChatWidgetProps {
  language?: 'ar' | 'en';
}

const ChatWidget: React.FC<ChatWidgetProps> = ({ language = 'en' }) => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [isMinimized, setIsMinimized] = useState<boolean>(false);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputMessage, setInputMessage] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [assistantId, setAssistantId] = useState<number | null>(null);
  const [hasNewMessage, setHasNewMessage] = useState<boolean>(false);
  const messagesEndRef: any = useRef<HTMLDivElement>(null);

  // API base URL from environment
  const API_BASE_URL: any = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api';

  useEffect(() => {
    fetchAIAssistant();
    initializeChat();
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (isOpen) {
      setHasNewMessage(false);
    }
  }, [isOpen]);

  const fetchAIAssistant: any = async () => {
    try {
      const token = localStorage.getItem('access_token');

      // Check if user is authenticated
      if (token) {
        try {
          // For authenticated users, check if they're admin
          const userResponse: any = await fetch(`${API_BASE_URL}/auth/user/`, {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json',
            }
          });

          if (userResponse.ok) {
            const userData: any = await userResponse.json();
            const isAdmin: any = userData.role?.id === 'admin' || userData.role?.id === 'super_admin';

            if (isAdmin) {
              // Admin users can access full AI assistant API
              try {
                const response: any = await fetch(`${API_BASE_URL}/customer-service/ai-assistants/`, {
                  headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                  }
                });
                if (response.ok) {
                  const data: any = await response.json();
                  const chatbot: any = data.results?.find((assistant: any) => assistant.assistant_type === 'chatbot');
                  if (chatbot) {
                    setAssistantId(chatbot.id);
                    return;
                  }
                }
              } catch (adminError) {
                console.log('Admin API not available, falling back to customer chat:', adminError);
              }
            }
            // Regular authenticated users use customer chat
            setAssistantId(-1); // Special ID for customer chat
          } else {
            // Token invalid, use public chat
            setAssistantId(0);
          }
        } catch (authError) {
          console.log('Auth check failed, using public chat:', authError);
          setAssistantId(0);
        }
      } else {
        // Anonymous users use public chat
        setAssistantId(0); // Special ID for public chat
      }
    } catch (error) {
      console.error('Error fetching AI assistant:', error);
      // Fallback to public chat
      setAssistantId(0);
    }
  };

  const initializeChat: any = (): void => {
    const welcomeMessage: ChatMessage = {
      id: '1',
      type: 'ai',
      content: language === 'ar'
        ? 'مرحباً! أنا مساعد الدعم الذكي. كيف يمكنني مساعدتك اليوم؟'
        : 'Hello! I\'m your AI support assistant. How can I help you today?',
      timestamp: new Date(),
      confidence: 0.95
    };
    setMessages([welcomeMessage]);
  };

  const scrollToBottom: any = (): void => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const sendMessage: any = async () => {
    if (!inputMessage.trim() || assistantId === null) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: inputMessage,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    const currentMessage: any = inputMessage;
    setInputMessage('');
    setIsLoading(true);

    try {
      const token: any = localStorage.getItem('access_token');
      let response;

      if (assistantId === 0) {
        // Public chat (no authentication required)
        response = await fetch(`${API_BASE_URL}/customer-service/public-chat/chat/`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            message: currentMessage,
            session_id: 'public-widget-' + Date.now(),
            language: language,
            customer_name: 'Anonymous User'
          })
        });
      } else if (assistantId === -1) {
        // Customer chat (authenticated non-admin)
        response = await fetch(`${API_BASE_URL}/customer-service/customer-chat/chat/`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            message: currentMessage,
            session_id: 'customer-widget-' + Date.now(),
            language: language
          })
        });
      } else {
        // Admin chat (full AI assistant access)
        response = await fetch(`${API_BASE_URL}/customer-service/ai-assistants/${assistantId}/chat/`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            message: currentMessage,
            context: {
              language: language,
              session_id: 'admin-widget-' + Date.now(),
              source: 'chat_widget'
            }
          })
        });
      }

      const aiResponse: any = await response.json();

      const aiMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: aiResponse.response || (language === 'ar'
          ? 'أعتذر، واجهت خطأ. يرجى المحاولة مرة أخرى.'
          : 'I apologize, but I encountered an error. Please try again.'),
        timestamp: new Date(),
        confidence: aiResponse.confidence,
        suggestions: aiResponse.suggestions
      };

      setMessages(prev => [...prev, aiMessage]);

      // Show notification if widget is closed
      if (!isOpen) {
        setHasNewMessage(true);
      }

      // Handle escalation
      if (aiResponse.escalate_to_human) {
        const systemMessage: ChatMessage = {
          id: (Date.now() + 2).toString(),
          type: 'system',
          content: language === 'ar'
            ? 'تم تحويل المحادثة إلى وكيل بشري. سيتم الاتصال بك قريباً.'
            : 'This conversation has been escalated to a human agent. You will be connected shortly.',
          timestamp: new Date()
        };
        setMessages(prev => [...prev, systemMessage]);
      }

    } catch (error) {
      console.error('Error sending message:', error);
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'system',
        content: language === 'ar'
          ? 'عذراً، واجهت خطأ. يرجى المحاولة مرة أخرى أو الاتصال بالدعم.'
          : 'Sorry, I encountered an error. Please try again or contact support.',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const provideFeedback: any = async (messageId: string, helpful: boolean) => {
    setMessages(prev => prev.map(msg =>
      msg.id === messageId ? { ...msg, helpful } : msg
    ));

    try {
      const token: any = localStorage.getItem('access_token');
      await fetch(`${API_BASE_URL}/customer-service/ai-interactions/feedback/`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message_id: messageId,
          was_helpful: helpful
        })
      });
    } catch (error) {
      console.error('Error providing feedback:', error);
    }
  };

  const handleKeyPress: any = (e: React.KeyboardEvent): void => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const formatTime: any = (date: Date): string => {
    return date.toLocaleTimeString(language === 'ar' ? 'ar-SA' : 'en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Chat Widget Button (when closed)
  if (!isOpen) {
    return (
      <div className="fixed bottom-6 right-6 z-50">
        <Button
          onClick={() => setIsOpen(true)}
          data-chat-widget
          className="w-14 h-14 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 shadow-lg hover:shadow-xl transition-all duration-300 relative"
        >
          <MessageCircle className="h-6 w-6 text-white" />
          {hasNewMessage && (
            <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full animate-pulse"></div>
          )}
        </Button>
      </div>
    );
  }

  // Chat Widget (when open)
  return (
    <div className="fixed bottom-6 right-6 z-50">
      <Card className={`glass-card border-white/20 transition-all duration-300 ${
        isMinimized ? 'w-80 h-16' : 'w-96 h-[500px]'
      } flex flex-col`}>
        {/* Header */}
        <CardHeader className="border-b border-white/10 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                <Bot className="h-4 w-4 text-white" />
              </div>
              <div>
                <CardTitle className="text-white text-sm">
                  {language === 'ar' ? 'مساعد الدعم' : 'Support Assistant'}
                </CardTitle>
                <Badge className="bg-green-500/20 text-green-300 border-green-500/30 text-xs">
                  {language === 'ar' ? 'متصل' : 'Online'}
                </Badge>
              </div>
            </div>
            <div className="flex gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsMinimized(!isMinimized)}
                className="text-white/60 hover:text-white h-8 w-8 p-0"
              >
                <Minimize2 className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsOpen(false)}
                className="text-white/60 hover:text-white h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>

        {/* Messages (hidden when minimized) */}
        {!isMinimized && (
          <>
            <CardContent className="flex-1 overflow-y-auto p-4 space-y-3">
              {messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex gap-2 ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  {message.type !== 'user' && (
                    <div className={`w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 ${
                      message.type === 'ai' ? 'bg-blue-500' : 'bg-gray-500'
                    }`}>
                      {message.type === 'ai' ? (
                        <Bot className="h-3 w-3 text-white" />
                      ) : (
                        <div className="w-1.5 h-1.5 bg-white rounded-full" />
                      )}
                    </div>
                  )}

                  <div className={`max-w-[80%] ${message.type === 'user' ? 'order-first' : ''}`}>
                    <div
                      className={`p-2 rounded-lg text-sm ${
                        message.type === 'user'
                          ? 'bg-blue-500 text-white ml-auto'
                          : message.type === 'ai'
                          ? 'bg-white/10 text-white'
                          : 'bg-yellow-500/20 text-yellow-300 border border-yellow-500/30'
                      }`}
                    >
                      <p>{message.content}</p>

                      {/* Knowledge Base Suggestions */}
                      {message.suggestions && message.suggestions.length > 0 && (
                        <div className="mt-2 space-y-1">
                          <div className="text-xs text-white/80">
                            {language === 'ar' ? 'مقالات ذات صلة:' : 'Related articles:'}
                          </div>
                          {message.suggestions.slice(0, 2).map((suggestion) => (
                            <div
                              key={suggestion.id}
                              className="p-1.5 bg-white/5 rounded border border-white/10 cursor-pointer hover:bg-white/10 transition-colors"
                            >
                              <div className="flex items-start justify-between">
                                <div className="flex-1">
                                  <h4 className="text-xs font-medium text-white">{suggestion.title}</h4>
                                  <p className="text-xs text-white/60 mt-0.5 line-clamp-1">{suggestion.summary}</p>
                                </div>
                                <ExternalLink className="h-2.5 w-2.5 text-white/40 ml-1 flex-shrink-0" />
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>

                    {/* Feedback for AI messages */}
                    {message.type === 'ai' && (
                      <div className="flex items-center gap-1 mt-1">
                        <span className="text-xs text-white/60">{formatTime(message.timestamp)}</span>
                        <div className="flex gap-0.5 ml-auto">
                          <Button
                            variant="ghost"
                            size="sm"
                            className={`h-5 w-5 p-0 ${
                              message.helpful === true ? 'text-green-400' : 'text-white/40 hover:text-green-400'
                            }`}
                            onClick={() => provideFeedback(message.id, true)}
                          >
                            <ThumbsUp className="h-2.5 w-2.5" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className={`h-5 w-5 p-0 ${
                              message.helpful === false ? 'text-red-400' : 'text-white/40 hover:text-red-400'
                            }`}
                            onClick={() => provideFeedback(message.id, false)}
                          >
                            <ThumbsDown className="h-2.5 w-2.5" />
                          </Button>
                        </div>
                      </div>
                    )}

                    {/* Timestamp for user messages */}
                    {message.type === 'user' && (
                      <div className="text-xs text-white/60 mt-1 text-right">
                        {formatTime(message.timestamp)}
                      </div>
                    )}
                  </div>

                  {message.type === 'user' && (
                    <div className="w-6 h-6 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
                      <User className="h-3 w-3 text-white" />
                    </div>
                  )}
                </div>
              ))}

              {/* Loading indicator */}
              {isLoading && (
                <div className="flex gap-2 justify-start">
                  <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                    <Bot className="h-3 w-3 text-white" />
                  </div>
                  <div className="bg-white/10 text-white p-2 rounded-lg">
                    <div className="flex items-center gap-1">
                      <Loader2 className="h-3 w-3 animate-spin" />
                      <span className="text-xs">
                        {language === 'ar' ? 'جاري التفكير...' : 'Thinking...'}
                      </span>
                    </div>
                  </div>
                </div>
              )}

              <div ref={messagesEndRef} />
            </CardContent>

            {/* Input */}
            <div className="border-t border-white/10 p-3">
              <div className="flex gap-2">
                <Input
                  value={inputMessage}
                  onChange={(e: any) => setInputMessage(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder={language === 'ar' ? 'اكتب رسالتك...' : 'Type your message...'}
                  className="flex-1 bg-white/5 border-white/20 text-white placeholder:text-white/40 text-sm h-8"
                  disabled={isLoading}
                />
                <Button
                  onClick={sendMessage}
                  disabled={!inputMessage.trim() || isLoading}
                  className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 h-8 w-8 p-0"
                >
                  <Send className="h-3 w-3" />
                </Button>
              </div>
            </div>
          </>
        )}
      </Card>
    </div>
  );
};

export default ChatWidget;
