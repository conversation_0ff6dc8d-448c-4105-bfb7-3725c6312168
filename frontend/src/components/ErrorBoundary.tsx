import React, { Component, ErrorInfo, ReactNode } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { AlertTriangle, RefreshCw, Home, Bug, Copy, CheckCircle } from 'lucide-react'
import { comprehensiveErrorHandler } from '@/utils/comprehensiveErrorHandler'

interface Props {
  children: ReactNode
  fallback?: ReactNode
  language?: 'ar' | 'en'
  onError?: (error: Error, errorInfo: ErrorInfo) => void
}

interface State {
  hasError: boolean
  error?: Error
  errorInfo?: ErrorInfo
  errorId?: string
  retryCount: number
  copied: boolean
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      hasError: false,
      retryCount: 0,
      copied: false
    }
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo)

    // ERROR FIX: Use comprehensive error handler
    const processedError = comprehensiveErrorHandler.handleError(error, {
      component: 'ErrorBoundary',
      action: 'componentDidCatch',
      additionalData: errorInfo
    }, {
      language: this.props.language || 'en',
      showToast: false, // Don't show toast since we're showing the error boundary
      logToConsole: true,
      sendToService: true
    })

    this.setState({
      error,
      errorInfo,
      errorId: processedError.code
    })

    // Call custom error handler if provided
    this.props.onError?.(error, errorInfo)
  }

  // ERROR FIX: Enhanced reset with retry tracking
  handleReset = () => {
    this.setState({
      hasError: false,
      error: undefined,
      errorInfo: undefined,
      retryCount: this.state.retryCount + 1,
      copied: false
    })

    // FIXED: Announce recovery to screen readers
    const announcement = document.createElement('div')
    announcement.setAttribute('aria-live', 'polite')
    announcement.className = 'sr-only'
    announcement.textContent = 'Error resolved, application restored'
    document.body.appendChild(announcement)

    setTimeout(() => {
      document.body.removeChild(announcement)
    }, 1000)
  }

  // ERROR FIX: Copy error details to clipboard
  handleCopyError = async () => {
    const errorDetails = {
      message: this.state.error?.message,
      stack: this.state.error?.stack,
      componentStack: this.state.errorInfo?.componentStack,
      errorId: this.state.errorId,
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      retryCount: this.state.retryCount
    }

    try {
      await navigator.clipboard.writeText(JSON.stringify(errorDetails, null, 2))
      this.setState({ copied: true })
      setTimeout(() => this.setState({ copied: false }), 2000)
    } catch (err) {
      console.error('Failed to copy error details:', err)
    }
  }

  handleReload = () => {
    // FIXED: Use state reset and navigation service instead of page reload
    const announcement = document.createElement('div')
    announcement.setAttribute('aria-live', 'polite')
    announcement.className = 'sr-only'
    announcement.textContent = 'Attempting to recover from error'
    document.body.appendChild(announcement)

    // Reset the error boundary state
    this.setState({ hasError: false, error: null, errorInfo: null })

    // Emit refresh event instead of page reload
    window.dispatchEvent(new CustomEvent('app:force-refresh', {
      detail: { timestamp: Date.now(), source: 'error-boundary' }
    }))

    announcement.textContent = 'Application refreshed'
  }

  handleGoHome = () => {
    // FIXED: Use navigation service instead of window.location
    try {
      // Reset error state
      this.setState({ hasError: false, error: null, errorInfo: null })

      // Use navigation service
      window.dispatchEvent(new CustomEvent('app:navigate', {
        detail: { path: '/' }
      }))
    } catch (error) {
      console.error('Error boundary navigation failed:', error)
      // Last resort: emit navigation event
      window.dispatchEvent(new CustomEvent('app:navigate', {
        detail: { path: '/' }
      }))
    }
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback
      }

      return (
        <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center p-4">
          {/* Background Effects */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
            <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
            <div className="absolute top-40 left-40 w-80 h-80 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"></div>
          </div>

          <div className="relative z-10 w-full max-w-2xl">
            <Card className="glass-card border-white/20 shadow-2xl">
              <CardHeader className="text-center">
                <div className="mx-auto w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mb-4">
                  <AlertTriangle className="w-8 h-8 text-red-400" />
                </div>
                <CardTitle className="text-2xl font-bold text-white">
                  {this.props.language === 'en' ? 'Unexpected Error Occurred' : 'حدث خطأ غير متوقع'}
                </CardTitle>
                <CardDescription className="text-white/70">
                  {this.props.language === 'en'
                    ? 'Sorry, an error occurred in the application. Please try again.'
                    : 'عذراً، حدث خطأ في التطبيق. يرجى المحاولة مرة أخرى.'
                  }
                </CardDescription>

                {/* ERROR FIX: Error ID and retry count */}
                {this.state.errorId && (
                  <div className="mt-2 text-xs text-white/50">
                    {this.props.language === 'en' ? 'Error ID:' : 'معرف الخطأ:'} {this.state.errorId}
                    {this.state.retryCount > 0 && (
                      <span className="ml-2">
                        ({this.props.language === 'en' ? 'Retry' : 'إعادة المحاولة'} #{this.state.retryCount})
                      </span>
                    )}
                  </div>
                )}
              </CardHeader>

              <CardContent className="space-y-6">
                {/* Error Details (only in development) */}
                {import.meta.env.VITE_NODE_ENV === 'development' && this.state.error && (
                  <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-4">
                    <h4 className="text-red-400 font-medium mb-2">Error Details:</h4>
                    <pre className="text-xs text-red-300 overflow-auto max-h-32">
                      {this.state.error.message}
                    </pre>
                    {this.state.errorInfo && (
                      <details className="mt-2">
                        <summary className="text-red-400 cursor-pointer text-sm">
                          Stack Trace
                        </summary>
                        <pre className="text-xs text-red-300 mt-2 overflow-auto max-h-32">
                          {this.state.errorInfo.componentStack}
                        </pre>
                      </details>
                    )}
                  </div>
                )}

                {/* ERROR FIX: Enhanced Action Buttons */}
                <div className="space-y-3">
                  {/* Primary Actions */}
                  <div className="flex flex-col sm:flex-row gap-3">
                    <Button
                      onClick={this.handleReset}
                      className="flex-1 glass-button bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600"
                    >
                      <RefreshCw className="w-4 h-4 mr-2" />
                      {this.props.language === 'en' ? 'Try Again' : 'المحاولة مرة أخرى'}
                    </Button>

                    <Button
                      onClick={this.handleReload}
                      variant="outline"
                      className="flex-1 glass-button border-white/30 hover:bg-white/10"
                    >
                      <RefreshCw className="w-4 h-4 mr-2" />
                      {this.props.language === 'en' ? 'Reload Page' : 'إعادة تحميل الصفحة'}
                    </Button>

                    <Button
                      onClick={this.handleGoHome}
                      variant="outline"
                      className="flex-1 glass-button border-white/30 hover:bg-white/10"
                    >
                      <Home className="w-4 h-4 mr-2" />
                      {this.props.language === 'en' ? 'Go Home' : 'الصفحة الرئيسية'}
                    </Button>
                  </div>

                  {/* Secondary Actions */}
                  <div className="flex justify-center">
                    <Button
                      onClick={this.handleCopyError}
                      variant="ghost"
                      size="sm"
                      className="glass-button text-white/60 hover:text-white"
                    >
                      {this.state.copied ? (
                        <>
                          <CheckCircle className="w-4 h-4 mr-2" />
                          {this.props.language === 'en' ? 'Copied!' : 'تم النسخ!'}
                        </>
                      ) : (
                        <>
                          <Copy className="w-4 h-4 mr-2" />
                          {this.props.language === 'en' ? 'Copy Error Details' : 'نسخ تفاصيل الخطأ'}
                        </>
                      )}
                    </Button>
                  </div>
                </div>

                {/* Help Text */}
                <div className="text-center">
                  <p className="text-white/60 text-sm">
                    إذا استمر الخطأ، يرجى الاتصال بفريق الدعم الفني
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )
    }

    return (
      <div data-error-boundary="true">
        {this.props.children}
      </div>
    )
  }
}

export default ErrorBoundary

// Hook version for functional components
export const useErrorHandler = (): void => {
  const handleError = (error: Error, errorInfo?: any): void => {
    console.error('Error handled:', error, errorInfo)
    
    // You can send error to logging service here
    if (import.meta.env.VITE_NODE_ENV === 'production') {
      // Send to error tracking service (e.g., Sentry)
      // sentryService.captureException(error, errorInfo)
    }
  }

  return { handleError }
}
