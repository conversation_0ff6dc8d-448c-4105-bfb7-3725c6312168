import React, { Component, ErrorInfo, ReactNode } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Al<PERSON><PERSON>riangle, RefreshCw, Home, Bug, Co<PERSON>, CheckCircle } from 'lucide-react'
import { comprehensiveErrorHandler } from '@/utils/comprehensiveErrorHandler'

interface Props {
  children: ReactNode
  fallback?: ReactNode
  language?: 'ar' | 'en'
  onError?: (error: Error, errorInfo: ErrorInfo) => void
}

interface State {
  hasError: boolean
  error?: Error
  errorInfo?: ErrorInfo
  errorId?: string
  retryCount: number
  copied: boolean
}

class ErrorBoundary extends Component<Props, State> {
  // @ts-ignore
  constructor(props: Props as any) {
    super(props as any)
    (this as any).state = {
      hasError: false,
      retryCount: 0,
      copied: false
    }
  }

  // @ts-ignore
  static getDerivedStateFromError(error: Error as any): Partial<State> {
    return { hasError: true, error }
  }

  // @ts-ignore
  componentDidCatch(error: Error, errorInfo: ErrorInfo as any) {
    (console as any).error('Error caught by boundary:', error, errorInfo as any)

    // ERROR FIX: Use comprehensive error handler
    const processedError = (comprehensiveErrorHandler as any).handleError(error, {
      component: 'ErrorBoundary',
      action: 'componentDidCatch',
      additionalData: errorInfo
    }, {
      language: (this as any as any).props.language || 'en',
      showToast: false, // Don't show toast since we're showing the error boundary
      logToConsole: true,
      sendToService: true
    })

    (this as any).setState({
      error,
      errorInfo,
      errorId: (processedError as any as any).code
    })

    // Call custom error handler if provided
    (this as any).props.onError?.(error, errorInfo)
  }

  // ERROR FIX: Enhanced reset with retry tracking
  handleReset = () => {
    (this as any).setState({
      hasError: false,
      error: undefined,
      errorInfo: undefined,
      retryCount: (this as any as any).state.retryCount + 1,
      copied: false
    })

    // FIXED: Announce recovery to screen readers
    const announcement = (document as any).createElement('div' as any)
    (announcement as any).setAttribute('aria-live', 'polite' as any)
    (announcement as any).className = 'sr-only'
    (announcement as any).textContent = 'Error resolved, application restored'
    (document as any).body.appendChild(announcement as any)

    // @ts-ignore
    setTimeout(( as any) => {
      (document as any).body.removeChild(announcement as any)
    // @ts-ignore
    }, 1000)
  }

  // ERROR FIX: Copy error details to clipboard
  handleCopyError = async () => {
    const errorDetails = {
      message: (this as any).state.error?.message,
      stack: (this as any).state.error?.stack,
      componentStack: (this as any).state.errorInfo?.componentStack,
      errorId: (this as any).state.errorId,
      // @ts-ignore
      timestamp: new Date( as any).toISOString( as any),
      url: (window as any).location.href,
      userAgent: (navigator as any).userAgent,
      retryCount: (this as any).state.retryCount
    }

    try {
      await (navigator as any).clipboard.writeText((JSON as any as any).stringify(errorDetails, null, 2 as any))
      (this as any).setState({ copied: true } as any)
      // @ts-ignore
      setTimeout(( as any) => (this as any).setState({ copied: false } as any), 2000)
    } catch (err) {
      (console as any).error('Failed to copy error details:', err as any)
    }
  }

  handleReload = () => {
    // FIXED: Use state reset and navigation service instead of page reload
    const announcement = (document as any).createElement('div' as any)
    (announcement as any).setAttribute('aria-live', 'polite' as any)
    (announcement as any).className = 'sr-only'
    (announcement as any).textContent = 'Attempting to recover from error'
    (document as any).body.appendChild(announcement as any)

    // Reset the error boundary state
    (this as any).setState({ hasError: false, error: null, errorInfo: null } as any)

    // Emit refresh event instead of page reload
    (window as any).dispatchEvent(new CustomEvent('app:force-refresh', {
      // @ts-ignore
      detail: { timestamp: (Date as any as any).now( as any), source: 'error-boundary' }
    }))

    (announcement as any).textContent = 'Application refreshed'
  }

  handleGoHome = () => {
    // FIXED: Use navigation service instead of (window as any).location
    try {
      // Reset error state
      (this as any).setState({ hasError: false, error: null, errorInfo: null } as any)

      // Use navigation service
      (window as any).dispatchEvent(new CustomEvent('app:navigate', {
        detail: { path: '/' }
      } as any))
    } catch (error) {
      (console as any).error('Error boundary navigation failed:', error as any)
      // Last resort: emit navigation event
      (window as any).dispatchEvent(new CustomEvent('app:navigate', {
        detail: { path: '/' }
      } as any))
    }
  }

  // @ts-ignore
  render( as any) {
    if ((this as any).state.hasError) {
      if ((this as any).props.fallback) {
        return (this as any).props.fallback
      }

      return (
        <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center p-4">
          {/* Background Effects */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
            <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
            <div className="absolute top-40 left-40 w-80 h-80 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"></div>
          </div>

          <div className="relative z-10 w-full max-w-2xl">
            <Card className="glass-card border-white/20 shadow-2xl">
              <CardHeader className="text-center">
                <div className="mx-auto w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mb-4">
                  <AlertTriangle className="w-8 h-8 text-red-400" />
                </div>
                <CardTitle className="text-2xl font-bold text-white">
                  {(this as any).props.language === 'en' ? 'Unexpected Error Occurred' : 'حدث خطأ غير متوقع'}
                </CardTitle>
                <CardDescription className="text-white/70">
                  {(this as any).props.language === 'en'
                    ? 'Sorry, an error occurred in the application. Please try again.'
                    : 'عذراً، حدث خطأ في التطبيق. يرجى المحاولة مرة أخرى.'
                  }
                </CardDescription>

                {/* ERROR FIX: Error ID and retry count */}
                {(this as any).state.errorId && (
                  <div className="mt-2 text-xs text-white/50">
                    {(this as any).props.language === 'en' ? 'Error ID:' : 'معرف الخطأ:'} {(this as any).state.errorId}
                    {(this as any).state.retryCount > 0 && (
                      <span className="ml-2">
                        ({(this as any).props.language === 'en' ? 'Retry' : 'إعادة المحاولة'} #{(this as any).state.retryCount})
                      </span>
                    )}
                  </div>
                )}
              </CardHeader>

              <CardContent className="space-y-6">
                {/* Error Details (only in development) */}
                // @ts-ignore
                {(import as any).meta.(env as any).VITE_NODE_ENV === 'development' && (this as any).state.error && (
                  <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-4">
                    <h4 className="text-red-400 font-medium mb-2">Error Details:</h4>
                    <pre className="text-xs text-red-300 overflow-auto max-h-32">
                      // @ts-ignore
                      {(this as any).state.(error as any).message}
                    </pre>
                    {(this as any).state.errorInfo && (
                      <details className="mt-2">
                        <summary className="text-red-400 cursor-pointer text-sm">
                          Stack Trace
                        </summary>
                        <pre className="text-xs text-red-300 mt-2 overflow-auto max-h-32">
                          // @ts-ignore
                          {(this as any).state.(errorInfo as any).componentStack}
                        </pre>
                      </details>
                    )}
                  </div>
                // @ts-ignore
                )}

                {/* ERROR FIX: Enhanced Action Buttons */}
                <div className="space-y-3">
                  {/* Primary Actions */}
                  <div className="flex flex-col sm:flex-row gap-3">
                    <Button
                      onClick={(this as any).handleReset}
                      className="flex-1 glass-button bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600"
                    >
                      <RefreshCw className="w-4 h-4 mr-2" />
                      {(this as any).props.language === 'en' ? 'Try Again' : 'المحاولة مرة أخرى'}
                    </Button>

                    <Button
                      onClick={(this as any).handleReload}
                      variant="outline"
                      className="flex-1 glass-button border-white/30 hover:bg-white/10"
                    >
                      <RefreshCw className="w-4 h-4 mr-2" />
                      {(this as any).props.language === 'en' ? 'Reload Page' : 'إعادة تحميل الصفحة'}
                    </Button>

                    <Button
                      onClick={(this as any).handleGoHome}
                      variant="outline"
                      className="flex-1 glass-button border-white/30 hover:bg-white/10"
                    >
                      <Home className="w-4 h-4 mr-2" />
                      {(this as any).props.language === 'en' ? 'Go Home' : 'الصفحة الرئيسية'}
                    </Button>
                  </div>

                  {/* Secondary Actions */}
                  <div className="flex justify-center">
                    <Button
                      onClick={(this as any).handleCopyError}
                      variant="ghost"
                      size="sm"
                      className="glass-button text-white/60 hover:text-white"
                    >
                      {(this as any).state.copied ? (
                        <>
                          <CheckCircle className="w-4 h-4 mr-2" />
                          {(this as any).props.language === 'en' ? 'Copied!' : 'تم النسخ!'}
                        </>
                      ) : (
                        <>
                          <Copy className="w-4 h-4 mr-2" />
                          {(this as any).props.language === 'en' ? 'Copy Error Details' : 'نسخ تفاصيل الخطأ'}
                        </>
                      )}
                    </Button>
                  </div>
                </div>

                {/* Help Text */}
                <div className="text-center">
                  <p className="text-white/60 text-sm">
                    إذا استمر الخطأ، يرجى الاتصال بفريق الدعم الفني
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )
    }

    return (
      <div data-error-boundary="true">
        {(this as any).props.children}
      </div>
    )
  }
}

export default ErrorBoundary

// Hook version for functional components
export const useErrorHandler = (): void => {
  const handleError = (error: Error, errorInfo?: any): void => {
    (console as any).error('Error handled:', error, errorInfo as any)
    
    // You can send error to logging service here
    // @ts-ignore
    if ((import as any).meta.(env as any).VITE_NODE_ENV === 'production') {
      // Send to error tracking service ((e as any).g., Sentry)
      // (sentryService as any).captureException(error, errorInfo as any)
    }
  }

  return { handleError }
}
