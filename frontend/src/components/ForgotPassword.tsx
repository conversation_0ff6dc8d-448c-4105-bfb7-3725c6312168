import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Mail, ArrowLeft, CheckCircle } from 'lucide-react'
import { authService } from '../services/authService'

interface ForgotPasswordProps {
  language: 'ar' | 'en'
  onBack: () => void
}

const translations = {
  ar: {
    title: 'نسيت كلمة المرور؟',
    subtitle: 'أدخل بريدك الإلكتروني وسنرسل لك رابط إعادة تعيين كلمة المرور',
    email: 'البريد الإلكتروني',
    emailPlaceholder: 'أدخل بريدك الإلكتروني',
    sendButton: 'إرسال رابط الإعادة',
    backToLogin: 'العودة لتسجيل الدخول',
    sending: 'جاري الإرسال...',
    successTitle: 'تم إرسال الرابط!',
    successMessage: 'تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني. يرجى التحقق من صندوق الوارد.',
    emailRequired: 'البريد الإلكتروني مطلوب',
    invalidEmail: 'يرجى إدخال بريد إلكتروني صحيح',
    errorTitle: 'خطأ في الإرسال',
    tryAgain: 'حاول مرة أخرى'
  },
  en: {
    title: 'Forgot Password?',
    subtitle: 'Enter your email address and we\'ll send you a password reset link',
    email: 'Email Address',
    emailPlaceholder: 'Enter your email address',
    sendButton: 'Send Reset Link',
    backToLogin: 'Back to Login',
    sending: 'Sending...',
    successTitle: 'Reset Link Sent!',
    successMessage: 'A password reset link has been sent to your email address. Please check your inbox.',
    emailRequired: 'Email address is required',
    invalidEmail: 'Please enter a valid email address',
    errorTitle: 'Failed to Send',
    tryAgain: 'Try Again'
  }
}

export const ForgotPassword: React.FC<ForgotPasswordProps> = ({ language, onBack }) => {
  const [email, setEmail] = useState<string>('')
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [isSuccess, setIsSuccess] = useState<boolean>(false)
  const [error, setError] = useState<string>('')
  const [validationError, setValidationError] = useState<string>('')

  const t = translations[language]
  const isRTL = language === 'ar'

  const validateEmail = (email: string): string | null => {
    if (!email.trim()) {
      return t.emailRequired
    }
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return t.invalidEmail
    }
    return null
  }

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
    const value = e.target.value
    setEmail(value)
    
    // Clear validation error when user starts typing
    if (validationError) {
      setValidationError('')
    }
    // Clear general error when user makes changes
    if (error) {
      setError('')
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    e.stopPropagation()

    // Validate email
    const emailError = validateEmail(email)
    if (emailError) {
      setValidationError(emailError)
      return
    }

    setIsLoading(true)
    setError('')

    try {
      await authService.requestPasswordReset(email)
      setIsSuccess(true)
      console.log('✅ Password reset email sent successfully')
    } catch (error: any) {
      console.error('❌ Password reset failed:', error)
      setError(error?.message || 'Failed to send reset email')
    } finally {
      setIsLoading(false)
    }
  }

  if (isSuccess) {
    return (
      <Card className="w-full max-w-md mx-auto glass-card">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 w-12 h-12 bg-green-500/20 rounded-full flex items-center justify-center">
            <CheckCircle className="w-6 h-6 text-green-400" />
          </div>
          <CardTitle className="text-white text-xl">
            {t.successTitle}
          </CardTitle>
          <CardDescription className="text-white/70">
            {t.successMessage}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button
            onClick={onBack}
            className="w-full glass-button bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white font-semibold py-3"
          >
            <ArrowLeft className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
            {t.backToLogin}
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="w-full max-w-md mx-auto glass-card">
      <CardHeader className="text-center">
        <CardTitle className="text-white text-xl">
          {t.title}
        </CardTitle>
        <CardDescription className="text-white/70">
          {t.subtitle}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Error Message */}
          {error && (
            <div className="bg-red-500/20 border border-red-500/50 rounded-lg p-3">
              <p className="text-red-400 text-sm text-center">
                {error}
              </p>
            </div>
          )}

          {/* Email Input */}
          <div className="space-y-2">
            <Label htmlFor="reset-email" className="text-white">{t.email}</Label>
            <div className="relative">
              <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 h-4 w-4" />
              <Input
                id="reset-email"
                type="email"
                placeholder={t.emailPlaceholder}
                value={email}
                onChange={handleEmailChange}
                className={`pl-10 glass-input ${validationError ? 'border-red-500 focus:border-red-500' : ''}`}
                required
                disabled={isLoading}
              />
            </div>
            {validationError && (
              <p className="text-red-400 text-xs mt-1">{validationError}</p>
            )}
          </div>

          {/* Buttons */}
          <div className="space-y-3">
            <Button
              type="submit"
              className="w-full glass-button bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white font-semibold py-3"
              disabled={isLoading || !email}
            >
              {isLoading ? (
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                  {t.sending}
                </div>
              ) : (
                t.sendButton
              )}
            </Button>

            <Button
              type="button"
              variant="ghost"
              onClick={onBack}
              className="w-full text-white/70 hover:text-white hover:bg-white/10"
              disabled={isLoading}
            >
              <ArrowLeft className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
              {t.backToLogin}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}

export default ForgotPassword
