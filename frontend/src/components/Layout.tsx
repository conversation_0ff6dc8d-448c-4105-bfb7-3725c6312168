import React, { ReactNode, useState, useEffect } from 'react'
import { useLocation, Link } from 'react-router-dom'
import { useDispatch, useSelector } from 'react-redux'
import { Button } from '@/components/ui/button'
import type { AppDispatch, RootState } from '../store'
import { logoutUser } from '../store/slices/authSlice'
import SmartNotifications from './SmartNotifications'
import { PermissionGate, usePermissions } from './RoleBasedRoute'
import {
  Home,
  Users,
  Building,
  BarChart3,
  Settings,
  LogOut,
  Menu,
  X,
  Globe,
  Bell,
  Calendar,
  Clock,
  TrendingUp,
  DollarSign,
  FolderOpen,
  Briefcase,
  Package,
  Truck,
  ShoppingCart,
  MessageSquare,
  Megaphone,
  FileText,
  Video,
  ChevronDown,
  ChevronRight,
  User,
  Shield,
  Boxes,
  UserCog,
  ClipboardList,
  Archive,
  GraduationCap,
  BookOpen,
  Award,
  Target,
  AlertTriangle
} from 'lucide-react'

interface LayoutProps {
  children: ReactNode
  language: 'ar' | 'en'
  setLanguage: (lang: 'ar' | 'en') => void
}

const translations = {
  ar: {
    dashboard: 'لوحة التحكم',
    customers: 'العملاء',
    sales: 'المبيعات',
    salesOrders: 'أوامر المبيعات',
    quotations: 'عروض الأسعار',
    salesPipeline: 'مسار المبيعات',
    products: 'المنتجات',
    productCatalog: 'كتالوج المنتجات',
    employees: 'الموظفين',
    departments: 'الأقسام',
    reports: 'التقارير',
    settings: 'الإعدادات',
    logout: 'تسجيل الخروج',
    menu: 'القائمة',
    hrManagement: 'إدارة الموارد البشرية',
    leaveManagement: 'إدارة الإجازات',
    attendance: 'الحضور والانصراف',
    performance: 'تقييم الأداء',
    payroll: 'كشوف المرتبات',
    projectManagement: 'إدارة المشاريع',
    projects: 'المشاريع',
    tasks: 'المهام',
    projectReports: 'تقارير المشاريع',
    financialManagement: 'الإدارة المالية',
    budgets: 'الميزانيات',
    expenses: 'المصروفات',
    financialReports: 'التقارير المالية',
    assetManagement: 'إدارة الأصول',
    assets: 'الأصول',
    suppliers: 'الموردين',
    purchaseOrders: 'أوامر الشراء',
    communication: 'التواصل والتعاون',
    messages: 'الرسائل',
    announcements: 'الإعلانات',
    documents: 'المستندات',
    meetings: 'الاجتماعات',
    analytics: 'التحليلات المتقدمة',
    employeeServices: 'الخدمات الذاتية للموظف',
    employeeProfile: 'الملف الشخصي',
    employeeLeave: 'طلبات الإجازة',
    employeeTasks: 'المهام',
    inventory: 'إدارة المخزون',
    userManagement: 'إدارة المستخدمين',
    systemAdmin: 'إدارة النظام',
    roleManagement: 'إدارة الأدوار',
    auditLogs: 'سجلات المراجعة',
    training: 'التدريب والتطوير',
    trainingPrograms: 'برامج التدريب',
    certifications: 'الشهادات المهنية',
    performanceReviews: 'تقييمات الأداء',
    recruitment: 'التوظيف',
    jobPostings: 'الوظائف المعلنة',
    calendar: 'التقويم',
    knowledgeBase: 'قاعدة المعرفة',
    payrollManagement: 'إدارة الرواتب',
    compliance: 'الامتثال',
    riskManagement: 'إدارة المخاطر',
    quality: 'إدارة الجودة',
    businessIntelligence: 'ذكاء الأعمال',
    vendors: 'إدارة الموردين',
    myProjects: 'مشاريعي',
    assignedProjects: 'المشاريع المكلف بها',
    myTasks: 'مهامي',
    myProfile: 'ملفي الشخصي',
    personalMessages: 'رسائلي الشخصية',
    myCalendar: 'تقويمي الشخصي',
    personalSettings: 'إعداداتي الشخصية',
    userAccount: 'حسابي',
    personalSpace: 'المساحة الشخصية',
    myMessages: 'رسائلي',
    myWork: 'عملي',
    companyInfo: 'معلومات الشركة'
  },
  en: {
    dashboard: 'Dashboard',
    customers: 'Customers',
    sales: 'Sales',
    salesOrders: 'Sales Orders',
    quotations: 'Quotations',
    salesPipeline: 'Sales Pipeline',
    products: 'Products',
    productCatalog: 'Product Catalog',
    employees: 'Employees',
    departments: 'Departments',
    reports: 'Reports',
    settings: 'Settings',
    logout: 'Logout',
    menu: 'Menu',
    hrManagement: 'HR Management',
    leaveManagement: 'Leave Management',
    attendance: 'Attendance',
    performance: 'Performance',
    payroll: 'Payroll',
    projectManagement: 'Project Management',
    projects: 'Projects',
    tasks: 'Tasks',
    projectReports: 'Project Reports',
    financialManagement: 'Financial Management',
    budgets: 'Budgets',
    expenses: 'Expenses',
    financialReports: 'Financial Reports',
    assetManagement: 'Asset Management',
    assets: 'Assets',
    suppliers: 'Suppliers',
    purchaseOrders: 'Purchase Orders',
    communication: 'Communication',
    messages: 'Messages',
    announcements: 'Announcements',
    documents: 'Documents',
    meetings: 'Meetings',
    analytics: 'Advanced Analytics',
    employeeServices: 'Employee Self-Service',
    employeeProfile: 'Employee Profile',
    employeeLeave: 'Leave Requests',
    employeeTasks: 'Tasks',
    inventory: 'Inventory Management',
    userManagement: 'User Management',
    systemAdmin: 'System Administration',
    roleManagement: 'Role Management',
    auditLogs: 'Audit Logs',
    training: 'Training & Development',
    trainingPrograms: 'Training Programs',
    certifications: 'Certifications',
    performanceReviews: 'Performance Reviews',
    recruitment: 'Recruitment',
    jobPostings: 'Job Postings',
    calendar: 'Calendar',
    knowledgeBase: 'Knowledge Base',
    payrollManagement: 'Payroll Management',
    compliance: 'Compliance',
    riskManagement: 'Risk Management',
    quality: 'Quality Management',
    businessIntelligence: 'Business Intelligence',
    vendors: 'Vendor Management',
    myProjects: 'My Projects',
    assignedProjects: 'Assigned Projects',
    myTasks: 'My Tasks',
    myProfile: 'My Profile',
    personalMessages: 'Personal Messages',
    myCalendar: 'My Calendar',
    personalSettings: 'Personal Settings',
    userAccount: 'My Account',
    personalSpace: 'Personal Space',
    myMessages: 'My Messages',
    myWork: 'My Work',
    companyInfo: 'Company Info'
  }
}

export default function Layout({ children, language, setLanguage }: LayoutProps) {
  const dispatch = useDispatch<AppDispatch>()
  const { user } = useSelector((state: RootState) => state.auth)
  const { unreadCount } = useSelector((state: RootState) => state.notifications)
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [desktopSidebarOpen, setDesktopSidebarOpen] = useState(true)
  const [expandedMenus, setExpandedMenus] = useState<string[]>([])
  const [notificationsOpen, setNotificationsOpen] = useState(false)
  const location = useLocation()
  const t = translations[language]
  const { hasPermission, hasMinRoleLevel } = usePermissions()

  const toggleMenu = (menuId: string) => {
    setExpandedMenus(prev =>
      prev.includes(menuId)
        ? prev.filter(id => id !== menuId)
        : [...prev, menuId]
    )
  }

  // Role-based navigation filtering with proper prefixes
  const getNavigationForRole = (userRole: string) => {
    // Get the appropriate prefix based on user role
    const getPrefix = (role: string) => {
      switch (role) {
        case 'super_admin': return '/admin'
        case 'hr_manager': return '/hr'
        case 'finance_manager': return '/finance'
        case 'sales_manager': return '/sales'
        case 'department_manager': return '/department'
        case 'employee': return '/employee'
        default: return '/employee'
      }
    }

    const prefix = getPrefix(userRole)

    // Create role-specific navigation
    const createNavigation = () => {
      switch (userRole) {
        case 'super_admin':
          return [
            { name: t.dashboard, icon: Home, href: '/' },
            { name: t.employees, icon: Users, href: '/admin/employees' },
            { name: t.departments, icon: Building, href: '/admin/departments' },
            {
              name: t.hrManagement,
              icon: Users,
              id: 'hr',
              children: [
                { name: t.leaveManagement, icon: Calendar, href: '/admin/hr/leave' },
                { name: t.attendance, icon: Clock, href: '/admin/hr/attendance' },
                { name: t.performance, icon: TrendingUp, href: '/admin/hr/performance' },
                { name: t.payroll, icon: DollarSign, href: '/admin/hr/payroll' },
              ]
            },
            {
              name: t.sales,
              icon: ShoppingCart,
              id: 'sales',
              children: [
                { name: t.salesOrders, icon: ShoppingCart, href: '/admin/sales/orders' },
                { name: t.quotations, icon: FileText, href: '/admin/sales/quotations' },
                { name: t.salesPipeline, icon: TrendingUp, href: '/admin/sales/pipeline' },
              ]
            },
            // Company-wide communication (Super Admin has full access)
            {
              name: t.communication,
              icon: MessageSquare,
              id: 'communication',
              children: [
                { name: t.messages, icon: MessageSquare, href: '/admin/communication/messages' },
                { name: t.announcements, icon: Megaphone, href: '/admin/communication/announcements' },
                { name: t.documents, icon: FileText, href: '/admin/communication/documents' },
                { name: t.meetings, icon: Video, href: '/admin/communication/meetings' },
              ]
            },
            { name: t.reports, icon: BarChart3, href: '/admin/reports' },
            { name: t.settings, icon: Settings, href: '/admin/settings' },
            // Personal user features
            {
              name: t.personalSpace,
              icon: User,
              id: 'personal',
              children: [
                { name: t.myProfile, icon: User, href: '/admin/profile' },
                { name: t.myMessages, icon: MessageSquare, href: '/admin/messages' },
                { name: t.myCalendar, icon: Calendar, href: '/admin/calendar' },
              ]
            },
          ]

        case 'hr_manager':
          return [
            { name: t.dashboard, icon: Home, href: '/hr/dashboard' },
            { name: t.employees, icon: Users, href: '/hr/employees' },
            { name: t.departments, icon: Building, href: '/hr/departments' },
            {
              name: t.hrManagement,
              icon: Users,
              id: 'hr',
              children: [
                { name: t.leaveManagement, icon: Calendar, href: '/hr/leave' },
                { name: t.attendance, icon: Clock, href: '/hr/attendance' },
                { name: t.performance, icon: TrendingUp, href: '/hr/performance' },
                { name: t.payroll, icon: DollarSign, href: '/hr/payroll' },
              ]
            },
            {
              name: t.communication,
              icon: MessageSquare,
              id: 'communication',
              children: [
                { name: t.messages, icon: MessageSquare, href: '/hr/communication/messages' },
                { name: t.announcements, icon: Megaphone, href: '/hr/communication/announcements' },
                { name: t.documents, icon: FileText, href: '/hr/communication/documents' },
                { name: t.meetings, icon: Video, href: '/hr/communication/meetings' },
              ]
            },
            { name: t.reports, icon: BarChart3, href: '/hr/reports' },
            // Personal user features
            {
              name: t.personalSpace,
              icon: User,
              id: 'personal',
              children: [
                { name: t.myProfile, icon: User, href: '/hr/profile' },
                { name: t.myMessages, icon: MessageSquare, href: '/hr/messages' },
                { name: t.myCalendar, icon: Calendar, href: '/hr/calendar' },
              ]
            },
          ]

        case 'sales_manager':
          return [
            { name: t.dashboard, icon: Home, href: '/sales/dashboard' },
            { name: t.customers, icon: Users, href: '/sales/customers' },
            {
              name: t.sales,
              icon: ShoppingCart,
              id: 'sales',
              children: [
                { name: t.salesOrders, icon: ShoppingCart, href: '/sales/orders' },
                { name: t.quotations, icon: FileText, href: '/sales/quotations' },
                { name: t.salesPipeline, icon: TrendingUp, href: '/sales/pipeline' },
              ]
            },
            { name: t.products, icon: Package, href: '/sales/products' },
            { name: t.analytics, icon: BarChart3, href: '/sales/analytics' },
            { name: t.reports, icon: BarChart3, href: '/sales/reports' },
            // Personal Features for Sales Manager
            {
              name: t.personalSpace,
              icon: User,
              id: 'personal',
              children: [
                { name: t.myProfile, icon: User, href: '/sales/profile' },
                { name: t.myMessages, icon: MessageSquare, href: '/sales/messages' },
                { name: t.myCalendar, icon: Calendar, href: '/sales/calendar' },
              ]
            },
            {
              name: t.communication,
              icon: MessageSquare,
              id: 'communication',
              children: [
                { name: t.announcements, icon: Megaphone, href: '/sales/communication/announcements' },
              ]
            },
          ]

        case 'finance_manager':
          return [
            { name: t.dashboard, icon: Home, href: '/finance/dashboard' },
            {
              name: t.financialManagement,
              icon: DollarSign,
              id: 'finance',
              children: [
                { name: t.budgets, icon: DollarSign, href: '/finance/budgets' },
                { name: t.expenses, icon: TrendingUp, href: '/finance/expenses' },
                { name: t.financialReports, icon: BarChart3, href: '/finance/reports' },
              ]
            },
            {
              name: t.assetManagement,
              icon: Package,
              id: 'assets',
              children: [
                { name: t.assets, icon: Package, href: '/finance/assets' },
                { name: t.suppliers, icon: Truck, href: '/finance/suppliers' },
                { name: t.purchaseOrders, icon: ShoppingCart, href: '/finance/purchase-orders' },
              ]
            },
            { name: t.inventory, icon: Boxes, href: '/finance/inventory' },
            // Personal Features for Finance Manager
            {
              name: t.personalSpace,
              icon: User,
              id: 'personal',
              children: [
                { name: t.myProfile, icon: User, href: '/finance/profile' },
                { name: t.myMessages, icon: MessageSquare, href: '/finance/messages' },
                { name: t.myCalendar, icon: Calendar, href: '/finance/calendar' },
              ]
            },
            {
              name: t.communication,
              icon: MessageSquare,
              id: 'communication',
              children: [
                { name: t.announcements, icon: Megaphone, href: '/finance/communication/announcements' },
              ]
            },
          ]

        case 'department_manager':
          return [
            { name: t.dashboard, icon: Home, href: '/department/dashboard' },
            {
              name: t.projectManagement,
              icon: Briefcase,
              id: 'projects',
              children: [
                { name: t.projects, icon: FolderOpen, href: '/department/projects' },
                { name: t.tasks, icon: BarChart3, href: '/department/tasks' },
                { name: t.projectReports, icon: BarChart3, href: '/department/reports' },
              ]
            },
            { name: t.departments, icon: Building, href: '/department/departments' },
            // Personal Features for Department Manager
            {
              name: t.personalSpace,
              icon: User,
              id: 'personal',
              children: [
                { name: t.myProfile, icon: User, href: '/department/profile' },
                { name: t.myMessages, icon: MessageSquare, href: '/department/messages' },
                { name: t.myCalendar, icon: Calendar, href: '/department/calendar' },
              ]
            },
            {
              name: t.communication,
              icon: MessageSquare,
              id: 'communication',
              children: [
                { name: t.announcements, icon: Megaphone, href: '/department/communication/announcements' },
              ]
            },
          ]

        case 'employee':
          return [
            { name: t.dashboard, icon: Home, href: '/employee/dashboard' },
            // Personal user features
            {
              name: t.personalSpace,
              icon: User,
              id: 'personal',
              children: [
                { name: t.myProfile, icon: User, href: '/employee/profile' },
                { name: t.myMessages, icon: MessageSquare, href: '/employee/messages' },
                { name: t.myCalendar, icon: Calendar, href: '/employee/calendar' },
              ]
            },
            // Work-related features
            {
              name: t.myWork,
              icon: Briefcase,
              id: 'work',
              children: [
                { name: t.employeeLeave, icon: Calendar, href: '/employee/leave' },
                { name: t.myTasks, icon: ClipboardList, href: '/employee/tasks' },
                { name: t.assignedProjects, icon: FolderOpen, href: '/employee/projects' },
              ]
            },
            // Company information - read-only
            {
              name: t.companyInfo,
              icon: Building,
              id: 'company',
              children: [
                { name: t.announcements, icon: Megaphone, href: '/employee/communication/announcements' },
                { name: t.documents, icon: FileText, href: '/employee/communication/documents' },
              ]
            },
          ]

        default:
          return [
            { name: t.dashboard, icon: Home, href: '/employee/dashboard' },
          ]
      }
    }

    return createNavigation()
  }

  const navigation = getNavigationForRole(user?.role?.id || 'employee')

  const isCurrentPath = (href: string) => {
    return location.pathname === href || (href === '/dashboard' && location.pathname === '/') || (href === '/' && location.pathname === '/')
  }

  const isChildActive = (children: any[]) => {
    return children.some(child => isCurrentPath(child.href))
  }

  // Helper function to check if user can access a route
  const canAccessRoute = (href: string) => {
    if (!user) return false

    // Super admin can access everything
    if (user.role.id === 'super_admin') return true

    // Check if the route matches the user's role prefix
    const rolePrefix = {
      'hr_manager': '/hr',
      'finance_manager': '/finance',
      'sales_manager': '/sales',
      'department_manager': '/department',
      'employee': '/employee'
    }[user.role.id]

    // Allow access to root dashboard and role-specific routes
    return href === '/' || (rolePrefix && href.startsWith(rolePrefix))
  }

  const toggleLanguage = () => {
    setLanguage(language === 'ar' ? 'en' : 'ar')
  }

  const handleLogout = () => {
    dispatch(logoutUser())
  }

  const handleLinkClick = () => {
    // Close mobile sidebar when a link is clicked
    setSidebarOpen(false)
  }

  // Handle keyboard events for accessibility
  const handleKeyDown = (event: KeyboardEvent) => {
    if (event.key === 'Escape' && sidebarOpen) {
      setSidebarOpen(false)
    }
  }

  // Add keyboard event listener
  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown)
    return () => {
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [sidebarOpen])

  const renderNavigationItem = (item: any, index: number) => {
    if (item.children) {
      // Filter children to only show accessible routes
      const accessibleChildren = item.children.filter((child: any) => canAccessRoute(child.href))

      // Don't show parent if no children are accessible
      if (accessibleChildren.length === 0) return null

      const isExpanded = expandedMenus.includes(item.id)
      const hasActiveChild = isChildActive(accessibleChildren)

      return (
        <li key={index}>
          <button
            onClick={() => toggleMenu(item.id)}
            className={`group flex w-full items-center gap-x-3 rounded-xl p-3 text-sm font-semibold leading-6 transition-all duration-300 ${
              hasActiveChild
                ? 'glass border-white/30 text-white shadow-lg'
                : 'text-white/80 hover:text-white hover:bg-white/15'
            }`}
          >
            <item.icon className="h-5 w-5 shrink-0" />
            <span className="flex-1 text-left">{item.name}</span>
            {isExpanded ? (
              <ChevronDown className="h-4 w-4 transition-transform duration-200" />
            ) : (
              <ChevronRight className="h-4 w-4 transition-transform duration-200" />
            )}
          </button>
          {isExpanded && (
            <ul className="mt-2 space-y-1 pl-4">
              {accessibleChildren.map((child: any, childIndex: number) => (
                <li key={childIndex}>
                  <Link
                    to={child.href}
                    onClick={handleLinkClick}
                    className={`group flex gap-x-3 rounded-lg p-2.5 text-sm font-medium leading-6 transition-all duration-300 ${
                      isCurrentPath(child.href)
                        ? 'glass border-white/30 text-white shadow-lg'
                        : 'text-white/70 hover:text-white hover:bg-white/15'
                    }`}
                  >
                    <child.icon className="h-4 w-4 shrink-0" />
                    {child.name}
                  </Link>
                </li>
              ))}
            </ul>
          )}
        </li>
      )
    }

    // Check if user can access this single route
    if (!canAccessRoute(item.href)) return null

    return (
      <li key={index}>
        <Link
          to={item.href}
          onClick={handleLinkClick}
          className={`group flex gap-x-3 rounded-xl p-3 text-sm font-semibold leading-6 transition-all duration-300 ${
            isCurrentPath(item.href)
              ? 'glass border-white/30 text-white shadow-lg'
              : 'text-white/80 hover:text-white hover:bg-white/15'
          }`}
        >
          <item.icon className="h-5 w-5 shrink-0" />
          {item.name}
        </Link>
      </li>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Background Effects - Same as Login */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
        <div className="absolute top-40 left-40 w-80 h-80 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"></div>
      </div>

      {/* Mobile sidebar */}
      <div className={`fixed inset-0 z-50 lg:hidden transition-all duration-300 ${sidebarOpen ? 'opacity-100 visible' : 'opacity-0 invisible'}`}>
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm transition-opacity duration-300" onClick={() => setSidebarOpen(false)} />
        <div className={`fixed top-0 ${language === 'ar' ? 'right-0' : 'left-0'} z-50 w-64 sm:w-72 h-full modern-card shadow-2xl transform transition-transform duration-300 ease-in-out ${
          sidebarOpen
            ? 'translate-x-0'
            : language === 'ar'
              ? 'translate-x-full'
              : '-translate-x-full'
        }`}>
          <div className="flex items-center justify-between p-6 border-b border-white/10">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center glow floating">
                <span className="text-white text-lg font-bold">ن</span>
              </div>
              <h2 className="text-xl font-bold text-white">نمو</h2>
            </div>
            <Button variant="ghost" size="sm" onClick={() => setSidebarOpen(false)} className="glass text-white border-white/30 hover:bg-white/20 p-2">
              <X className="h-5 w-5" />
            </Button>
          </div>
          <nav className="mt-6 px-6 space-y-2">
            <ul role="list" className="space-y-2">
              {navigation.map((item, index) => renderNavigationItem(item, index)).filter(Boolean)}
            </ul>
          </nav>
        </div>
      </div>

      {/* Desktop sidebar */}
      <div className={`hidden lg:fixed lg:inset-y-0 ${language === 'ar' ? 'lg:right-0' : 'lg:left-0'} lg:z-50 lg:w-64 xl:w-72 lg:flex lg:flex-col transition-transform duration-300 ease-in-out ${
        desktopSidebarOpen
          ? 'translate-x-0'
          : language === 'ar'
            ? 'translate-x-full'
            : '-translate-x-full'
      }`}>
        <div className="flex grow flex-col gap-y-6 overflow-y-auto modern-card shadow-2xl">
          <div className="flex h-20 shrink-0 items-center justify-between px-6 border-b border-white/10">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center glow floating shadow-lg">
                <span className="text-white text-xl font-bold">ن</span>
              </div>
              <div>
                <h1 className="text-xl font-bold text-white">نمو</h1>
                <p className="text-xs text-white/60">Enterprise Management</p>
              </div>
            </div>
            {/* Desktop close button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setDesktopSidebarOpen(false)}
              className="glass text-white border-white/30 hover:bg-white/20 p-2"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>
          <nav className="flex flex-1 flex-col px-6">
            <ul role="list" className="flex flex-1 flex-col gap-y-8">
              <li>
                <ul role="list" className="space-y-2">
                  {navigation.map((item, index) => renderNavigationItem(item, index)).filter(Boolean)}
                </ul>
              </li>
              <li className="mt-auto">
                <div className="space-y-3 pb-6">
                  <Button
                    variant="ghost"
                    onClick={toggleLanguage}
                    className="w-full justify-start gap-3 glass text-white border-white/30 hover:bg-white/20 rounded-xl p-3"
                  >
                    <Globe className="h-5 w-5" />
                    {language === 'ar' ? 'English' : 'العربية'}
                  </Button>
                  <Button
                    variant="ghost"
                    onClick={handleLogout}
                    className="w-full justify-start gap-3 glass text-red-300 border-red-300/30 hover:bg-red-500/20 rounded-xl p-3"
                  >
                    <LogOut className="h-5 w-5" />
                    {t.logout}
                  </Button>
                </div>
              </li>
            </ul>
          </nav>
        </div>
      </div>

      {/* Main content */}
      <div className={`relative z-10 transition-all duration-300 ease-in-out ${
        desktopSidebarOpen
          ? language === 'ar' ? 'lg:pr-64 xl:pr-72' : 'lg:pl-64 xl:pl-72'
          : 'lg:pl-0 lg:pr-0'
      }`}>
        {/* Top bar */}
        <div className="sticky top-0 z-40 flex h-16 sm:h-20 shrink-0 items-center gap-x-2 sm:gap-x-4 border-b border-white/10 modern-card px-2 sm:px-4 shadow-2xl lg:gap-x-6 lg:px-8">
          {/* Mobile menu button */}
          <Button
            variant="ghost"
            size="sm"
            className="lg:hidden glass text-white border-white/30 hover:bg-white/20 rounded-lg p-2 sm:p-3"
            onClick={() => setSidebarOpen(true)}
          >
            <Menu className="h-5 w-5 sm:h-6 sm:w-6" />
            <span className="sr-only">{t.menu}</span>
          </Button>

          {/* Desktop sidebar toggle */}
          <Button
            variant="ghost"
            size="sm"
            className="hidden lg:flex glass text-white border-white/30 hover:bg-white/20 rounded-lg p-2 sm:p-3 transition-all duration-300"
            onClick={() => setDesktopSidebarOpen(!desktopSidebarOpen)}
            title={desktopSidebarOpen ? "Hide Sidebar" : "Show Sidebar"}
          >
            {desktopSidebarOpen ? (
              <X className="h-5 w-5 sm:h-6 sm:w-6" />
            ) : (
              <Menu className="h-5 w-5 sm:h-6 sm:w-6" />
            )}
            <span className="sr-only">Toggle Sidebar</span>
          </Button>

          <div className="h-6 w-px bg-white/20 lg:hidden" />

          <div className="flex flex-1 gap-x-2 sm:gap-x-4 self-stretch lg:gap-x-6">
            <div className="flex items-center gap-x-2 sm:gap-x-4 ml-auto lg:gap-x-6">
              {/* Notification Bell */}
              <Button
                variant="ghost"
                size="sm"
                className="relative glass text-white border-white/30 hover:bg-white/20 rounded-lg p-2 sm:p-3"
                onClick={() => setNotificationsOpen(!notificationsOpen)}
              >
                <Bell className="h-4 w-4 sm:h-5 sm:w-5" />
                {/* Notification Badge */}
                {unreadCount > 0 && (
                  <div className="absolute -top-1 -right-1 min-w-[1rem] sm:min-w-[1.25rem] h-4 sm:h-5 bg-gradient-to-r from-red-500 to-red-600 rounded-full flex items-center justify-center animate-pulse shadow-lg">
                    <span className="text-white text-xs font-bold">
                      {unreadCount > 99 ? '99+' : unreadCount}
                    </span>
                  </div>
                )}
              </Button>

              {/* User Profile & Actions */}
              <div className="hidden sm:flex items-center gap-2 lg:gap-3">
                <div className="text-right hidden md:block">
                  <p className="text-white text-sm font-medium">{user?.name}</p>
                  <p className="text-white/60 text-xs">{user?.role.nameAr}</p>
                </div>
                <div className="w-8 h-8 sm:w-10 sm:h-10 rounded-lg sm:rounded-xl bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-white font-bold shadow-lg glow">
                  {user?.avatar || user?.name?.charAt(0)}
                </div>
              </div>

              {/* Language Toggle */}
              <Button
                variant="ghost"
                size="sm"
                className="glass text-white border-white/30 hover:bg-white/20 rounded-lg p-2 sm:p-3"
                onClick={toggleLanguage}
              >
                <Globe className="h-4 w-4 sm:h-5 sm:w-5 sm:mr-2" />
                <span className="hidden sm:inline">
                  {language === 'ar' ? 'English' : 'العربية'}
                </span>
              </Button>

              {/* Logout Button */}
              <Button
                variant="ghost"
                size="sm"
                className="glass text-red-300 border-red-300/30 hover:bg-red-500/20 rounded-lg p-2 sm:p-3"
                onClick={handleLogout}
              >
                <LogOut className="h-4 w-4 sm:h-5 sm:w-5" />
              </Button>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="py-4 sm:py-6 lg:py-8 px-2 sm:px-4 lg:px-8 transition-all duration-300">
          <div className="max-w-7xl mx-auto">
            {children}
          </div>
        </main>
      </div>

      {/* Smart Notifications */}
      <SmartNotifications
        language={language}
        isOpen={notificationsOpen}
        onClose={() => setNotificationsOpen(false)}
      />
    </div>
  )
}
