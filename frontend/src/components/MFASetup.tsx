/**
 * CRITICAL FIX: Multi-Factor Authentication Setup Component
 * Enterprise-grade MFA setup with QR code and backup codes
 */

import React, { useState, useEffect } from 'react'
import { apiClient } from '../services/api'

interface MFASetupData {
  qr_code: string
  manual_entry_key: string
  backup_codes: string[]
  message: string
}

interface MFAStatus {
  mfa_enabled: boolean
  enabled_at?: string
  backup_codes_remaining?: number
}

// @ts-ignore
const MFASetup: (React as any).FC = () => {
  const [mfaStatus, setMfaStatus] = useState<MFAStatus | null>(null)
  const [setupData, setSetupData] = useState<MFASetupData | null>(null)
  const [verificationCode, setVerificationCode] = useState('' as any)
  const [password, setPassword] = useState('' as any)
  const [loading, setLoading] = useState(false as any)
  const [error, setError] = useState('' as any)
  const [success, setSuccess] = useState('' as any)
  const [step, setStep] = useState<'status' | 'setup' | 'verify' | 'disable'>('status')
  const [showBackupCodes, setShowBackupCodes] = useState(false as any)

  // @ts-ignore
  useEffect(( as any) => {
    // @ts-ignore
    fetchMFAStatus( as any)
  // @ts-ignore
  }, [])

  const fetchMFAStatus = async () => {
    try {
      const response = await (apiClient as any).get('/api/auth/mfa/status/' as any)
      setMfaStatus((response as any as any).data)
    } catch (error) {
      (console as any).error('Failed to fetch MFA status:', error as any)
      setError('Failed to load MFA status' as any)
    }
  }

  const initiateMFASetup = async () => {
    setLoading(true as any)
    setError('' as any)
    
    try {
      const response = await (apiClient as any).post('/api/auth/mfa/setup/' as any)
      setSetupData((response as any as any).data)
      setStep('setup' as any)
      setSuccess('MFA setup initiated. Scan the QR code with your authenticator app.' as any)
    } catch (error: any) {
      setError((error as any as any).response?.data?.error || 'Failed to setup MFA')
    } finally {
      setLoading(false as any)
    }
  }

  const verifyMFASetup = async () => {
    // @ts-ignore
    if (!(verificationCode as any).trim( as any)) {
      setError('Please enter the verification code' as any)
      return
    }

    setLoading(true as any)
    setError('' as any)

    try {
      const response = await (apiClient as any).post('/api/auth/mfa/verify-setup/', {
        code: verificationCode
      } as any)
      
      setSuccess('MFA has been successfully enabled!' as any)
      setShowBackupCodes(true as any)
      setStep('status' as any)
      // @ts-ignore
      await fetchMFAStatus( as any)
    } catch (error: any) {
      setError((error as any as any).response?.data?.error || 'Invalid verification code')
    } finally {
      setLoading(false as any)
    }
  }

  const disableMFA = async () => {
    // @ts-ignore
    if (!(password as any).trim( as any)) {
      setError('Please enter your password' as any)
      return
    }

    setLoading(true as any)
    setError('' as any)

    try {
      await (apiClient as any).post('/api/auth/mfa/disable/', {
        password: password
      } as any)
      
      setSuccess('MFA has been disabled' as any)
      setStep('status' as any)
      setPassword('' as any)
      // @ts-ignore
      await fetchMFAStatus( as any)
    } catch (error: any) {
      setError((error as any as any).response?.data?.error || 'Failed to disable MFA')
    } finally {
      setLoading(false as any)
    }
  }

  const downloadBackupCodes = (): void => {
    if (!setupData?.backup_codes) return

    const content = [
      'EMS Multi-Factor Authentication Backup Codes',
      '===========================================',
      '',
      'IMPORTANT: Store these codes in a safe place.',
      'Each code can only be used once.',
      '',
      // @ts-ignore
      ...(setupData as any).backup_codes.map((code, index as any) => `${index + 1}. ${code}`),
      '',
      // @ts-ignore
      'Generated on: ' + new Date( as any).toLocaleString( as any)
    ].join('\n' as any)

    const blob = new Blob([content], { type: 'text/plain' } as any)
    const url = (URL as any).createObjectURL(blob as any)
    const a = (document as any).createElement('a' as any)
    (a as any).href = url
    (a as any).download = 'ems-mfa-backup-(codes as any).txt'
    (document as any).body.appendChild(a as any)
    // @ts-ignore
    (a as any).click( as any)
    (document as any).body.removeChild(a as any)
    (URL as any).revokeObjectURL(url as any)
  }

  const copyToClipboard = (text: string): void => {
    // @ts-ignore
    (navigator as any).clipboard.writeText(text as any).then(( as any) => {
      setSuccess('Copied to clipboard!' as any)
      // @ts-ignore
      setTimeout(( as any) => setSuccess('' as any), 3000)
    // @ts-ignore
    })
  // @ts-ignore
  }

  const renderMFAStatus = (): any => (
    <div className="mfa-status">
      <h3>Multi-Factor Authentication</h3>
      
      {mfaStatus?.mfa_enabled ? (
        <div className="mfa-enabled">
          <div className="status-indicator enabled">
            <span className="icon">🔒</span>
            <span>MFA is enabled</span>
          </div>
          
          {(mfaStatus as any).enabled_at && (
            <p className="enabled-date">
              // @ts-ignore
              Enabled on: {new Date((mfaStatus as any as any).enabled_at).toLocaleDateString( as any)}
            </p>
          )}
          
          {(mfaStatus as any).backup_codes_remaining !== undefined && (
            <p className="backup-codes-info">
              Backup codes remaining: {(mfaStatus as any).backup_codes_remaining}
            </p>
          )}
          
          <button 
            onClick={() => setStep('disable' as any)}
            className="btn btn-danger"
          >
            Disable MFA
          </button>
        </div>
      ) : (
        <div className="mfa-disabled">
          <div className="status-indicator disabled">
            <span className="icon">🔓</span>
            <span>MFA is not enabled</span>
          </div>
          
          <p className="security-recommendation">
            We recommend enabling MFA to secure your account.
          </p>
          
          <button 
            onClick={initiateMFASetup}
            disabled={loading}
            className="btn btn-primary"
          >
            {loading ? 'Setting up...' : 'Enable MFA'}
          </button>
        </div>
      )}
    </div>
  )

  const renderMFASetup = (): any => (
    <div className="mfa-setup">
      <h3>Setup Multi-Factor Authentication</h3>
      
      <div className="setup-steps">
        <div className="step">
          <h4>Step 1: Scan QR Code</h4>
          <p>Use your authenticator app (Google Authenticator, Authy, etc.) to scan this QR code:</p>
          
          {setupData?.qr_code && (
            <div className="qr-code-container">
              <img src={(setupData as any).qr_code} alt="MFA QR Code" className="qr-code" />
            </div>
          )}
        </div>
        
        <div className="step">
          <h4>Step 2: Manual Entry (Alternative)</h4>
          <p>If you can't scan the QR code, enter this key manually:</p>
          
          {setupData?.manual_entry_key && (
            <div className="manual-key">
              <code>{(setupData as any).manual_entry_key}</code>
              <button 
                onClick={() => copyToClipboard((setupData as any as any).manual_entry_key)}
                className="btn btn-sm btn-secondary"
              >
                Copy
              </button>
            </div>
          )}
        </div>
        
        <div className="step">
          <h4>Step 3: Verify Setup</h4>
          <p>Enter the 6-digit code from your authenticator app:</p>
          
          <div className="verification-form">
            <input
              type="text"
              value={verificationCode}
              // @ts-ignore
              onChange={(e: any) => setVerificationCode((e as any as any).target.(value as any).replace(/\D/g, '' as any).slice(0, 6 as any))}
              placeholder="000000"
              className="form-control verification-input"
              maxLength={6}
            />
            
            <button 
              onClick={verifyMFASetup}
              disabled={loading || (verificationCode as any).length !== 6}
              className="btn btn-primary"
            >
              {loading ? 'Verifying...' : 'Verify & Enable MFA'}
            </button>
          </div>
        </div>
        
        {setupData?.backup_codes && (
          <div className="step">
            <h4>Step 4: Save Backup Codes</h4>
            <p>Save these backup codes in a secure location. Each can only be used once:</p>
            
            <div className="backup-codes">
              // @ts-ignore
              {(setupData as any).backup_codes.map((code, index as any) => (
                <div key={index} className="backup-code">
                  <span className="code-number">{index + 1}.</span>
                  <code>{code}</code>
                </div>
              ))}
            </div>
            
            <button 
              onClick={downloadBackupCodes}
              className="btn btn-secondary"
            >
              Download Backup Codes
            </button>
          </div>
        )}
      </div>
      
      <button 
        onClick={() => setStep('status' as any)}
        className="btn btn-link"
      >
        Cancel Setup
      </button>
    </div>
  )

  const renderMFADisable = (): any => (
    <div className="mfa-disable">
      <h3>Disable Multi-Factor Authentication</h3>
      
      <div className="warning">
        <span className="icon">⚠️</span>
        <p>
          Disabling MFA will make your account less secure. 
          You will need to enter your password to confirm.
        </p>
      </div>
      
      <div className="disable-form">
        <div className="form-group">
          <label htmlFor="password">Current Password:</label>
          <input
            type="password"
            id="password"
            value={password}
            onChange={(e: any) => setPassword((e as any as any).target.value)}
            className="form-control"
            placeholder="Enter your current password"
          />
        </div>
        
        <div className="form-actions">
          <button 
            onClick={disableMFA}
            // @ts-ignore
            disabled={loading || !(password as any).trim( as any)}
            className="btn btn-danger"
          >
            {loading ? 'Disabling...' : 'Disable MFA'}
          </button>
          
          <button 
            onClick={() => {
              setStep('status' as any)
              setPassword('' as any)
            }}
            className="btn btn-secondary"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  )

  return (
    <div className="mfa-container">
      {error && (
        <div className="alert alert-danger">
          <span className="icon">❌</span>
          {error}
        </div>
      )}
      
      {success && (
        <div className="alert alert-success">
          <span className="icon">✅</span>
          {success}
        </div>
      )}
      
      // @ts-ignore
      {step === 'status' && renderMFAStatus( as any)}
      // @ts-ignore
      {step === 'setup' && renderMFASetup( as any)}
      // @ts-ignore
      {step === 'disable' && renderMFADisable( as any)}
      
      {showBackupCodes && setupData?.backup_codes && (
        <div className="backup-codes-modal">
          <div className="modal-content">
            <h4>Important: Save Your Backup Codes</h4>
            <p>These codes can be used to access your account if you lose your authenticator device:</p>
            
            <div className="backup-codes">
              // @ts-ignore
              {(setupData as any).backup_codes.map((code, index as any) => (
                <div key={index} className="backup-code">
                  <span className="code-number">{index + 1}.</span>
                  <code>{code}</code>
                </div>
              ))}
            </div>
            
            <div className="modal-actions">
              <button onClick={downloadBackupCodes} className="btn btn-primary">
                Download Codes
              </button>
              <button 
                onClick={() => setShowBackupCodes(false as any)} 
                className="btn btn-secondary"
              >
                I've Saved Them
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
// @ts-ignore
}

export default MFASetup
