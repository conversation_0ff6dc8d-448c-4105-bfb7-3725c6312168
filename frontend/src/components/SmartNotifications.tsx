import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  Bell,
  X,
  Check,
  AlertTriangle,
  Info,
  CheckCircle,
  Clock,
  User,
  DollarSign,
  Calendar,
  FileText,
  MessageSquare,
  Settings,
  Filter,
  MoreVertical,
  Archive,
  Star,
  Trash2
} from 'lucide-react'

interface SmartNotificationsProps {
  language: 'ar' | 'en'
  isOpen: boolean
  onClose: () => void
}

interface Notification {
  id: number
  type: 'info' | 'warning' | 'success' | 'error' | 'urgent'
  category: 'hr' | 'finance' | 'project' | 'system' | 'communication'
  title: string
  message: string
  timestamp: string
  isRead: boolean
  isStarred: boolean
  actionRequired: boolean
  priority: 'low' | 'medium' | 'high' | 'urgent'
  relatedUser?: string
  relatedEntity?: string
  actions?: Array<{
    label: string
    action: string
    variant: 'primary' | 'secondary' | 'danger'
  }>
}

const translations = {
  ar: {
    notifications: 'الإشعارات',
    markAllRead: 'تمييز الكل كمقروء',
    clearAll: 'مسح الكل',
    filter: 'تصفية',
    settings: 'الإعدادات',
    all: 'الكل',
    unread: 'غير مقروء',
    starred: 'مميز',
    urgent: 'عاجل',
    actionRequired: 'يتطلب إجراء',
    markAsRead: 'تمييز كمقروء',
    markAsUnread: 'تمييز كغير مقروء',
    star: 'تمييز',
    unstar: 'إلغاء التمييز',
    archive: 'أرشفة',
    delete: 'حذف',
    approve: 'موافقة',
    reject: 'رفض',
    view: 'عرض',
    reply: 'رد',
    now: 'الآن',
    minutesAgo: 'دقائق مضت',
    hoursAgo: 'ساعات مضت',
    daysAgo: 'أيام مضت',
    // Categories
    hr: 'الموارد البشرية',
    finance: 'المالية',
    project: 'المشاريع',
    system: 'النظام',
    communication: 'التواصل'
  },
  en: {
    notifications: 'Notifications',
    markAllRead: 'Mark All Read',
    clearAll: 'Clear All',
    filter: 'Filter',
    settings: 'Settings',
    all: 'All',
    unread: 'Unread',
    starred: 'Starred',
    urgent: 'Urgent',
    actionRequired: 'Action Required',
    markAsRead: 'Mark as Read',
    markAsUnread: 'Mark as Unread',
    star: 'Star',
    unstar: 'Unstar',
    archive: 'Archive',
    delete: 'Delete',
    approve: 'Approve',
    reject: 'Reject',
    view: 'View',
    reply: 'Reply',
    now: 'Now',
    minutesAgo: 'minutes ago',
    hoursAgo: 'hours ago',
    daysAgo: 'days ago',
    // Categories
    hr: 'HR',
    finance: 'Finance',
    project: 'Projects',
    system: 'System',
    communication: 'Communication'
  }
}

export default function SmartNotifications({ language, isOpen, onClose }: SmartNotificationsProps) {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [filter, setFilter] = useState('all')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const t = translations[language]
  const isRTL = language === 'ar'

  // Mock notifications data
  useEffect(() => {
    const mockNotifications: Notification[] = [
      {
        id: 1,
        type: 'urgent',
        category: 'hr',
        title: 'طلب إجازة عاجل',
        message: 'أحمد محمد قدم طلب إجازة عاجلة تحتاج موافقة فورية',
        timestamp: '5 دقائق مضت',
        isRead: false,
        isStarred: true,
        actionRequired: true,
        priority: 'urgent',
        relatedUser: 'أحمد محمد',
        relatedEntity: 'طلب إجازة #1234',
        actions: [
          { label: t.approve, action: 'approve', variant: 'primary' },
          { label: t.reject, action: 'reject', variant: 'danger' },
          { label: t.view, action: 'view', variant: 'secondary' }
        ]
      },
      {
        id: 2,
        type: 'warning',
        category: 'finance',
        title: 'تجاوز الميزانية',
        message: 'مشروع تطوير التطبيق تجاوز 90% من الميزانية المخصصة',
        timestamp: '15 دقيقة مضت',
        isRead: false,
        isStarred: false,
        actionRequired: true,
        priority: 'high',
        relatedEntity: 'مشروع تطوير التطبيق',
        actions: [
          { label: t.view, action: 'view', variant: 'primary' },
          { label: 'تعديل الميزانية', action: 'adjust', variant: 'secondary' }
        ]
      },
      {
        id: 3,
        type: 'success',
        category: 'project',
        title: 'اكتمال المهمة',
        message: 'فاطمة علي أكملت مهمة "تصميم واجهة المستخدم" بنجاح',
        timestamp: '30 دقيقة مضت',
        isRead: true,
        isStarred: false,
        actionRequired: false,
        priority: 'medium',
        relatedUser: 'فاطمة علي',
        relatedEntity: 'تصميم واجهة المستخدم'
      },
      {
        id: 4,
        type: 'info',
        category: 'system',
        title: 'تحديث النظام',
        message: 'تم تحديث النظام إلى الإصدار 2.1.0 بنجاح مع ميزات جديدة',
        timestamp: '1 ساعة مضت',
        isRead: true,
        isStarred: false,
        actionRequired: false,
        priority: 'low',
        relatedEntity: 'تحديث النظام v2.1.0'
      },
      {
        id: 5,
        type: 'info',
        category: 'communication',
        title: 'رسالة جديدة',
        message: 'محمد حسن أرسل رسالة جديدة في محادثة فريق التطوير',
        timestamp: '2 ساعة مضت',
        isRead: false,
        isStarred: false,
        actionRequired: false,
        priority: 'medium',
        relatedUser: 'محمد حسن',
        actions: [
          { label: t.reply, action: 'reply', variant: 'primary' },
          { label: t.view, action: 'view', variant: 'secondary' }
        ]
      }
    ]
    setNotifications(mockNotifications)
  }, [t])

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'urgent':
        return <AlertTriangle className="h-5 w-5 text-red-400" />
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-400" />
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-400" />
      case 'error':
        return <X className="h-5 w-5 text-red-400" />
      default:
        return <Info className="h-5 w-5 text-blue-400" />
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'hr':
        return <User className="h-4 w-4" />
      case 'finance':
        return <DollarSign className="h-4 w-4" />
      case 'project':
        return <FileText className="h-4 w-4" />
      case 'communication':
        return <MessageSquare className="h-4 w-4" />
      default:
        return <Settings className="h-4 w-4" />
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'urgent':
        return 'from-red-500 to-red-600'
      case 'warning':
        return 'from-yellow-500 to-yellow-600'
      case 'success':
        return 'from-green-500 to-green-600'
      case 'error':
        return 'from-red-500 to-red-600'
      default:
        return 'from-blue-500 to-blue-600'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'border-l-red-500'
      case 'high':
        return 'border-l-orange-500'
      case 'medium':
        return 'border-l-blue-500'
      default:
        return 'border-l-gray-500'
    }
  }

  const filteredNotifications = notifications.filter(notification => {
    if (filter === 'unread' && notification.isRead) return false
    if (filter === 'starred' && !notification.isStarred) return false
    if (filter === 'urgent' && notification.priority !== 'urgent') return false
    if (filter === 'actionRequired' && !notification.actionRequired) return false
    if (selectedCategory !== 'all' && notification.category !== selectedCategory) return false
    return true
  })

  const markAsRead = (id: number) => {
    setNotifications(prev => 
      prev.map(notif => 
        notif.id === id ? { ...notif, isRead: true } : notif
      )
    )
  }

  const toggleStar = (id: number) => {
    setNotifications(prev => 
      prev.map(notif => 
        notif.id === id ? { ...notif, isStarred: !notif.isStarred } : notif
      )
    )
  }

  const markAllAsRead = () => {
    setNotifications(prev => 
      prev.map(notif => ({ ...notif, isRead: true }))
    )
  }

  const unreadCount = notifications.filter(n => !n.isRead).length

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex items-start justify-end p-4">
      <div className="w-full max-w-md">
        <Card className="glass-card border-white/20 shadow-2xl">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Bell className="h-5 w-5 text-white" />
                <CardTitle className="text-white text-lg">{t.notifications}</CardTitle>
                {unreadCount > 0 && (
                  <div className="bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                    {unreadCount}
                  </div>
                )}
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={onClose}
                className="glass-button"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            
            {/* Action Buttons */}
            <div className="flex gap-2 mt-3">
              <Button
                variant="outline"
                size="sm"
                onClick={markAllAsRead}
                className="glass-button text-xs"
              >
                <Check className="h-3 w-3 mr-1" />
                {t.markAllRead}
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="glass-button text-xs"
              >
                <Filter className="h-3 w-3 mr-1" />
                {t.filter}
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="glass-button text-xs"
              >
                <Settings className="h-3 w-3 mr-1" />
                {t.settings}
              </Button>
            </div>

            {/* Filter Tabs */}
            <div className="flex space-x-1 bg-white/10 p-1 rounded-lg mt-3">
              {['all', 'unread', 'starred', 'urgent'].map((filterType) => (
                <button
                  key={filterType}
                  onClick={() => setFilter(filterType)}
                  className={`flex-1 py-1 px-2 rounded-md text-xs font-medium transition-all ${
                    filter === filterType
                      ? 'bg-white/20 text-white shadow-lg'
                      : 'text-white/70 hover:text-white hover:bg-white/10'
                  }`}
                >
                  {t[filterType as keyof typeof t]}
                </button>
              ))}
            </div>
          </CardHeader>

          <CardContent className="p-0 max-h-96 overflow-y-auto">
            <div className="space-y-1">
              {filteredNotifications.length === 0 ? (
                <div className="text-center py-8">
                  <Bell className="h-12 w-12 text-white/40 mx-auto mb-3" />
                  <p className="text-white/60">لا توجد إشعارات</p>
                </div>
              ) : (
                filteredNotifications.map((notification) => (
                  <div
                    key={notification.id}
                    className={`p-4 mx-3 rounded-xl border-l-4 ${getPriorityColor(notification.priority)} ${
                      notification.isRead ? 'bg-white/5' : 'glass-card border-white/20'
                    } hover:bg-white/10 transition-all cursor-pointer`}
                    onClick={() => !notification.isRead && markAsRead(notification.id)}
                  >
                    <div className="flex items-start gap-3">
                      <div className={`p-2 rounded-lg bg-gradient-to-r ${getTypeColor(notification.type)} shadow-lg`}>
                        {getTypeIcon(notification.type)}
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between mb-1">
                          <h4 className={`font-medium ${notification.isRead ? 'text-white/80' : 'text-white'}`}>
                            {notification.title}
                          </h4>
                          <div className="flex items-center gap-1">
                            <button
                              onClick={(e) => {
                                e.stopPropagation()
                                toggleStar(notification.id)
                              }}
                              className="text-white/60 hover:text-yellow-400 transition-colors"
                            >
                              <Star className={`h-3 w-3 ${notification.isStarred ? 'fill-yellow-400 text-yellow-400' : ''}`} />
                            </button>
                            <button className="text-white/60 hover:text-white transition-colors">
                              <MoreVertical className="h-3 w-3" />
                            </button>
                          </div>
                        </div>
                        
                        <p className={`text-sm mb-2 ${notification.isRead ? 'text-white/60' : 'text-white/80'}`}>
                          {notification.message}
                        </p>
                        
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2 text-xs text-white/60">
                            {getCategoryIcon(notification.category)}
                            <span>{t[notification.category as keyof typeof t]}</span>
                            <Clock className="h-3 w-3" />
                            <span>{notification.timestamp}</span>
                          </div>
                          
                          {notification.actionRequired && (
                            <div className="bg-orange-500/20 text-orange-400 text-xs px-2 py-1 rounded-full">
                              {t.actionRequired}
                            </div>
                          )}
                        </div>
                        
                        {notification.actions && (
                          <div className="flex gap-2 mt-3">
                            {notification.actions.map((action, index) => (
                              <Button
                                key={index}
                                size="sm"
                                variant={action.variant === 'primary' ? 'default' : 'outline'}
                                className={`text-xs ${
                                  action.variant === 'danger' 
                                    ? 'glass-button text-red-400 hover:bg-red-500/20' 
                                    : 'glass-button'
                                }`}
                              >
                                {action.label}
                              </Button>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
