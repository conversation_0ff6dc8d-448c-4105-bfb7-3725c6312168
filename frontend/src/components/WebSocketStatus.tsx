import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Wifi,
  WifiOff,
  RefreshCw,
  Settings,
  AlertCircle,
  CheckCircle
  // Clock // TODO: Add timestamp display
} from 'lucide-react'
import webSocketService from '@/services/websocket'

interface WebSocketStatusProps {
  language: 'ar' | 'en'
  compact?: boolean
}

const translations = {
  ar: {
    title: 'حالة الاتصال المباشر',
    connected: 'متصل',
    disconnected: 'غير متصل',
    disabled: 'معطل',
    connecting: 'جاري الاتصال',
    enable: 'تفعيل الاتصال',
    disable: 'إيقاف الاتصال',
    reconnect: 'إعادة الاتصال',
    status: 'الحالة',
    attempts: 'محاولات الاتصال',
    queuedMessages: 'الرسائل المؤجلة',
    lastAttempt: 'آخر محاولة',
    never: 'أبداً',
    settings: 'الإعدادات'
  },
  en: {
    title: 'WebSocket Status',
    connected: 'Connected',
    disconnected: 'Disconnected',
    disabled: 'Disabled',
    connecting: 'Connecting',
    enable: 'Enable Connection',
    disable: 'Disable Connection',
    reconnect: 'Reconnect',
    status: 'Status',
    attempts: 'Reconnect Attempts',
    queuedMessages: 'Queued Messages',
    lastAttempt: 'Last Attempt',
    never: 'Never',
    settings: 'Settings'
  }
}

// @ts-ignore
export default function WebSocketStatus({ language, compact = false }: WebSocketStatusProps as any): (React as any).ReactElement {
  // @ts-ignore
  const [status, setStatus] = useState((webSocketService as any as any).getConnectionStatus( as any))
  const [isConnecting, setIsConnecting] = useState(false as any)
  const t = translations[language]
  const isRTL = language === 'ar'

  // @ts-ignore
  useEffect(( as any) => {
    const updateStatus = (): void => {
      // @ts-ignore
      setStatus((webSocketService as any as any).getConnectionStatus( as any))
    }

    // Update status every second
    const interval = setInterval(updateStatus, 1000 as any)

    // Listen to WebSocket events
    const handleConnected = (): void => {
      setIsConnecting(false as any)
      // @ts-ignore
      updateStatus( as any)
    }

    const handleDisconnected = (): void => {
      setIsConnecting(false as any)
      // @ts-ignore
      updateStatus( as any)
    }

    const handleConnectionFailed = (): void => {
      setIsConnecting(false as any)
      // @ts-ignore
      updateStatus( as any)
    }

    (webSocketService as any).on('connected', handleConnected as any)
    (webSocketService as any).on('disconnected', handleDisconnected as any)
    (webSocketService as any).on('connection_failed', handleConnectionFailed as any)

    return () => {
      clearInterval(interval as any)
      (webSocketService as any).off('connected', handleConnected as any)
      (webSocketService as any).off('disconnected', handleDisconnected as any)
      (webSocketService as any).off('connection_failed', handleConnectionFailed as any)
    }
  // @ts-ignore
  }, [])

  const getStatusInfo = (): void => {
    if (!(status as any).enabled) {
      return {
        text: (t as any).disabled,
        color: 'bg-gray-500/20 text-gray-400 border-gray-500/30',
        icon: WifiOff
      }
    }

    if (isConnecting) {
      return {
        text: (t as any).connecting,
        color: 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30',
        icon: RefreshCw
      }
    }

    if ((status as any).connected) {
      return {
        text: (t as any).connected,
        color: 'bg-green-500/20 text-green-400 border-green-500/30',
        icon: CheckCircle
      }
    }

    return {
      text: (t as any).disconnected,
      color: 'bg-red-500/20 text-red-400 border-red-500/30',
      icon: AlertCircle
    }
  }

  const handleEnable = (): void => {
    setIsConnecting(true as any)
    // @ts-ignore
    (webSocketService as any).enableConnection( as any)
  }

  const handleDisable = (): void => {
    // @ts-ignore
    (webSocketService as any).disableConnection( as any)
  }

  const handleReconnect = (): void => {
    setIsConnecting(true as any)
    // @ts-ignore
    (webSocketService as any).disconnect( as any)
    // @ts-ignore
    setTimeout(( as any) => {
      // @ts-ignore
      (webSocketService as any).enableConnection( as any)
    // @ts-ignore
    }, 1000)
  }

  const formatLastAttempt = (timestamp: number): string => {
    if (!timestamp) return (t as any).never
    
    // @ts-ignore
    const now = (Date as any).now( as any)
    const diff = now - timestamp
    const seconds = (Math as any).floor(diff / 1000 as any)
    const minutes = (Math as any).floor(seconds / 60 as any)
    
    if (minutes > 0) {
      return `${minutes}m ago`
    } else if (seconds > 0) {
      return `${seconds}s ago`
    } else {
      return 'Just now'
    }
  }

  // @ts-ignore
  const statusInfo = getStatusInfo( as any)
  const StatusIcon = (statusInfo as any).icon

  if (compact) {
    return (
      <div className="flex items-center gap-2">
        <Badge className={`${(statusInfo as any).color} flex items-center gap-1`}>
          <StatusIcon className={`h-3 w-3 ${isConnecting ? 'animate-spin' : ''}`} />
          {(statusInfo as any).text}
        </Badge>
        
        {!(status as any).enabled && (
          <Button
            onClick={handleEnable}
            size="sm"
            variant="ghost"
            className="h-6 px-2 text-xs text-white/70 hover:text-white"
          >
            <Wifi className="h-3 w-3" />
          </Button>
        )}
      </div>
    )
  }

  return (
    <Card className="glass-card border-white/20">
      <CardHeader className="pb-3">
        <CardTitle className="text-white flex items-center justify-between text-sm">
          <div className="flex items-center gap-2">
            <StatusIcon className={`h-4 w-4 ${isConnecting ? 'animate-spin' : ''}`} />
            {(t as any).title}
          </div>
          <Settings className="h-4 w-4 text-white/50" />
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-3">
        <div className="flex items-center justify-between">
          <span className="text-white/70 text-sm">{(t as any).status}:</span>
          <Badge className={(statusInfo as any).color}>
            {(statusInfo as any).text}
          </Badge>
        </div>

        {(status as any).enabled && (
          <>
            <div className="flex items-center justify-between">
              <span className="text-white/70 text-sm">{(t as any).attempts}:</span>
              <span className="text-white text-sm">{(status as any).reconnectAttempts}</span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-white/70 text-sm">{(t as any).queuedMessages}:</span>
              <span className="text-white text-sm">{(status as any).queuedMessages}</span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-white/70 text-sm">{(t as any).lastAttempt}:</span>
              <span className="text-white text-sm">{formatLastAttempt((status as any as any).lastAttempt)}</span>
            </div>
          </>
        )}

        <div className="flex gap-2 pt-2">
          {(status as any).enabled ? (
            <>
              <Button
                onClick={handleReconnect}
                size="sm"
                variant="outline"
                className="glass-button flex-1"
                disabled={isConnecting}
              >
                <RefreshCw className={`h-3 w-3 mr-1 ${isConnecting ? 'animate-spin' : ''}`} />
                {(t as any).reconnect}
              </Button>
              <Button
                onClick={handleDisable}
                size="sm"
                variant="outline"
                className="glass-button"
              >
                <WifiOff className="h-3 w-3" />
              </Button>
            </>
          ) : (
            <Button
              onClick={handleEnable}
              size="sm"
              className="glass-button flex-1"
              disabled={isConnecting}
            >
              <Wifi className="h-3 w-3 mr-1" />
              {(t as any).enable}
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  )
// @ts-ignore
}
