import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Wifi,
  WifiOff,
  RefreshCw,
  Settings,
  AlertCircle,
  CheckCircle
  // Clock // TODO: Add timestamp display
} from 'lucide-react'
import webSocketService from '@/services/websocket'

interface WebSocketStatusProps {
  language: 'ar' | 'en'
  compact?: boolean
}

const translations = {
  ar: {
    title: 'حالة الاتصال المباشر',
    connected: 'متصل',
    disconnected: 'غير متصل',
    disabled: 'معطل',
    connecting: 'جاري الاتصال',
    enable: 'تفعيل الاتصال',
    disable: 'إيقاف الاتصال',
    reconnect: 'إعادة الاتصال',
    status: 'الحالة',
    attempts: 'محاولات الاتصال',
    queuedMessages: 'الرسائل المؤجلة',
    lastAttempt: 'آخر محاولة',
    never: 'أبداً',
    settings: 'الإعدادات'
  },
  en: {
    title: 'WebSocket Status',
    connected: 'Connected',
    disconnected: 'Disconnected',
    disabled: 'Disabled',
    connecting: 'Connecting',
    enable: 'Enable Connection',
    disable: 'Disable Connection',
    reconnect: 'Reconnect',
    status: 'Status',
    attempts: 'Reconnect Attempts',
    queuedMessages: 'Queued Messages',
    lastAttempt: 'Last Attempt',
    never: 'Never',
    settings: 'Settings'
  }
}

export default function WebSocketStatus({ language, compact = false }: WebSocketStatusProps): React.ReactElement {
  const [status, setStatus] = useState(webSocketService.getConnectionStatus())
  const [isConnecting, setIsConnecting] = useState(false)
  const t = translations[language]
  const isRTL = language === 'ar'

  useEffect(() => {
    const updateStatus = (): void => {
      setStatus(webSocketService.getConnectionStatus())
    }

    // Update status every second
    const interval = setInterval(updateStatus, 1000)

    // Listen to WebSocket events
    const handleConnected = (): void => {
      setIsConnecting(false)
      updateStatus()
    }

    const handleDisconnected = (): void => {
      setIsConnecting(false)
      updateStatus()
    }

    const handleConnectionFailed = (): void => {
      setIsConnecting(false)
      updateStatus()
    }

    webSocketService.on('connected', handleConnected)
    webSocketService.on('disconnected', handleDisconnected)
    webSocketService.on('connection_failed', handleConnectionFailed)

    return () => {
      clearInterval(interval)
      webSocketService.off('connected', handleConnected)
      webSocketService.off('disconnected', handleDisconnected)
      webSocketService.off('connection_failed', handleConnectionFailed)
    }
  }, [])

  const getStatusInfo = (): void => {
    if (!status.enabled) {
      return {
        text: t.disabled,
        color: 'bg-gray-500/20 text-gray-400 border-gray-500/30',
        icon: WifiOff
      }
    }

    if (isConnecting) {
      return {
        text: t.connecting,
        color: 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30',
        icon: RefreshCw
      }
    }

    if (status.connected) {
      return {
        text: t.connected,
        color: 'bg-green-500/20 text-green-400 border-green-500/30',
        icon: CheckCircle
      }
    }

    return {
      text: t.disconnected,
      color: 'bg-red-500/20 text-red-400 border-red-500/30',
      icon: AlertCircle
    }
  }

  const handleEnable = (): void => {
    setIsConnecting(true)
    webSocketService.enableConnection()
  }

  const handleDisable = (): void => {
    webSocketService.disableConnection()
  }

  const handleReconnect = (): void => {
    setIsConnecting(true)
    webSocketService.disconnect()
    setTimeout(() => {
      webSocketService.enableConnection()
    }, 1000)
  }

  const formatLastAttempt = (timestamp: number): string => {
    if (!timestamp) return t.never
    
    const now = Date.now()
    const diff = now - timestamp
    const seconds = Math.floor(diff / 1000)
    const minutes = Math.floor(seconds / 60)
    
    if (minutes > 0) {
      return `${minutes}m ago`
    } else if (seconds > 0) {
      return `${seconds}s ago`
    } else {
      return 'Just now'
    }
  }

  const statusInfo = getStatusInfo()
  const StatusIcon = statusInfo.icon

  if (compact) {
    return (
      <div className="flex items-center gap-2">
        <Badge className={`${statusInfo.color} flex items-center gap-1`}>
          <StatusIcon className={`h-3 w-3 ${isConnecting ? 'animate-spin' : ''}`} />
          {statusInfo.text}
        </Badge>
        
        {!status.enabled && (
          <Button
            onClick={handleEnable}
            size="sm"
            variant="ghost"
            className="h-6 px-2 text-xs text-white/70 hover:text-white"
          >
            <Wifi className="h-3 w-3" />
          </Button>
        )}
      </div>
    )
  }

  return (
    <Card className="glass-card border-white/20">
      <CardHeader className="pb-3">
        <CardTitle className="text-white flex items-center justify-between text-sm">
          <div className="flex items-center gap-2">
            <StatusIcon className={`h-4 w-4 ${isConnecting ? 'animate-spin' : ''}`} />
            {t.title}
          </div>
          <Settings className="h-4 w-4 text-white/50" />
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-3">
        <div className="flex items-center justify-between">
          <span className="text-white/70 text-sm">{t.status}:</span>
          <Badge className={statusInfo.color}>
            {statusInfo.text}
          </Badge>
        </div>

        {status.enabled && (
          <>
            <div className="flex items-center justify-between">
              <span className="text-white/70 text-sm">{t.attempts}:</span>
              <span className="text-white text-sm">{status.reconnectAttempts}</span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-white/70 text-sm">{t.queuedMessages}:</span>
              <span className="text-white text-sm">{status.queuedMessages}</span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-white/70 text-sm">{t.lastAttempt}:</span>
              <span className="text-white text-sm">{formatLastAttempt(status.lastAttempt)}</span>
            </div>
          </>
        )}

        <div className="flex gap-2 pt-2">
          {status.enabled ? (
            <>
              <Button
                onClick={handleReconnect}
                size="sm"
                variant="outline"
                className="glass-button flex-1"
                disabled={isConnecting}
              >
                <RefreshCw className={`h-3 w-3 mr-1 ${isConnecting ? 'animate-spin' : ''}`} />
                {t.reconnect}
              </Button>
              <Button
                onClick={handleDisable}
                size="sm"
                variant="outline"
                className="glass-button"
              >
                <WifiOff className="h-3 w-3" />
              </Button>
            </>
          ) : (
            <Button
              onClick={handleEnable}
              size="sm"
              className="glass-button flex-1"
              disabled={isConnecting}
            >
              <Wifi className="h-3 w-3 mr-1" />
              {t.enable}
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
