import React from 'react';
import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area
} from 'recharts'
import {
  TrendingUp,
  TrendingDown,
  Users,
  DollarSign,
  Package,
  Calendar,
  Target,
  Activity,
  BarChart3,
  PieChart as PieChartIcon,
  Download,
  Filter,
  RefreshCw
} from 'lucide-react'

interface AdvancedAnalyticsProps {
  // TODO: Define proper prop types
  [key: string]: any;
}

interface AnalyticsProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    analytics: 'التحليلات المتقدمة',
    overview: 'نظرة عامة',
    revenue: 'الإيرادات',
    employees: 'الموظفين',
    projects: 'المشاريع',
    inventory: 'المخزون',
    salesTrend: 'اتجاه المبيعات',
    employeePerformance: 'أداء الموظفين',
    projectStatus: 'حالة المشاريع',
    inventoryTurnover: 'دوران المخزون',
    monthlyRevenue: 'الإيرادات الشهرية',
    quarterlyGrowth: 'النمو الفصلي',
    topProducts: 'أفضل المنتجات',
    departmentStats: 'إحصائيات الأقسام',
    timeRange: 'الفترة الزمنية',
    lastWeek: 'الأسبوع الماضي',
    lastMonth: 'الشهر الماضي',
    lastQuarter: 'الربع الماضي',
    lastYear: 'السنة الماضية',
    export: 'تصدير',
    refresh: 'تحديث',
    filter: 'تصفية',
    totalSales: 'إجمالي المبيعات',
    activeProjects: 'المشاريع النشطة',
    totalEmployees: 'إجمالي الموظفين',
    lowStockItems: 'أصناف منخفضة المخزون',
    growth: 'نمو',
    decline: 'انخفاض'
  },
  en: {
    analytics: 'Advanced Analytics',
    overview: 'Overview',
    revenue: 'Revenue',
    employees: 'Employees',
    projects: 'Projects',
    inventory: 'Inventory',
    salesTrend: 'Sales Trend',
    employeePerformance: 'Employee Performance',
    projectStatus: 'Project Status',
    inventoryTurnover: 'Inventory Turnover',
    monthlyRevenue: 'Monthly Revenue',
    quarterlyGrowth: 'Quarterly Growth',
    topProducts: 'Top Products',
    departmentStats: 'Department Statistics',
    timeRange: 'Time Range',
    lastWeek: 'Last Week',
    lastMonth: 'Last Month',
    lastQuarter: 'Last Quarter',
    lastYear: 'Last Year',
    export: 'Export',
    refresh: 'Refresh',
    filter: 'Filter',
    totalSales: 'Total Sales',
    activeProjects: 'Active Projects',
    totalEmployees: 'Total Employees',
    lowStockItems: 'Low Stock Items',
    growth: 'Growth',
    decline: 'Decline'
  }
}

export default function AdvancedAnalytics({ language }: AnalyticsProps): React.ReactElement {
  const [timeRange, setTimeRange] = useState('lastMonth')
  const [loading, setLoading] = useState(false)
  const t = translations[language]
  const isRTL = language === 'ar'

  // Mock analytics data
  const kpiData = {
    totalSales: { value: 2450000, change: 12.5, trend: 'up' },
    activeProjects: { value: 24, change: -2.3, trend: 'down' },
    totalEmployees: { value: 156, change: 8.7, trend: 'up' },
    lowStockItems: { value: 8, change: -15.2, trend: 'down' }
  }

  const salesTrendData = [
    { month: 'Jan', sales: 180000, target: 200000 },
    { month: 'Feb', sales: 220000, target: 210000 },
    { month: 'Mar', sales: 195000, target: 220000 },
    { month: 'Apr', sales: 280000, target: 240000 },
    { month: 'May', sales: 310000, target: 260000 },
    { month: 'Jun', sales: 290000, target: 280000 }
  ]

  const projectStatusData = [
    { name: 'Completed', value: 45, color: '#10B981' },
    { name: 'In Progress', value: 30, color: '#3B82F6' },
    { name: 'Planning', value: 15, color: '#F59E0B' },
    { name: 'On Hold', value: 10, color: '#EF4444' }
  ]

  const departmentData = [
    { department: 'Sales', employees: 25, performance: 92 },
    { department: 'Engineering', employees: 45, performance: 88 },
    { department: 'Marketing', employees: 18, performance: 85 },
    { department: 'HR', employees: 12, performance: 90 },
    { department: 'Finance', employees: 15, performance: 94 }
  ]

  const inventoryData = [
    { category: 'Electronics', stock: 450, turnover: 8.2 },
    { category: 'Furniture', stock: 120, turnover: 3.5 },
    { category: 'Supplies', stock: 890, turnover: 12.1 },
    { category: 'Equipment', stock: 75, turnover: 2.8 }
  ]

  const handleRefresh = (): void => {
    setLoading(true)
    setTimeout(() => setLoading(false), 1000)
  }

  const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat(language === 'ar' ? 'ar-SA' : 'en-US', {
      style: 'currency',
      currency: 'SAR'
    }).format(value)
  }

  const formatPercentage = (value: number): string => {
    return `${value > 0 ? '+' : ''}${value.toFixed(1)}%`
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">{t.analytics}</h1>
          <p className="text-white/70">
            {language === 'ar' ? 'تحليلات شاملة لأداء الأعمال' : 'Comprehensive business performance analytics'}
          </p>
        </div>
        <div className="flex gap-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="glass text-white border-white/30 w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="glass-card border-white/20">
              <SelectItem value="lastWeek">{t.lastWeek}</SelectItem>
              <SelectItem value="lastMonth">{t.lastMonth}</SelectItem>
              <SelectItem value="lastQuarter">{t.lastQuarter}</SelectItem>
              <SelectItem value="lastYear">{t.lastYear}</SelectItem>
            </SelectContent>
          </Select>
          <Button 
            variant="outline" 
            className="glass-button"
            onClick={handleRefresh}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            {t.refresh}
          </Button>
          <Button variant="outline" className="glass-button">
            <Download className="h-4 w-4 mr-2" />
            {t.export}
          </Button>
        </div>
      </div>

      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.totalSales}</p>
                <p className="text-2xl font-bold text-white">{formatCurrency(kpiData.totalSales.value)}</p>
                <div className="flex items-center gap-1 mt-1">
                  {kpiData.totalSales.trend === 'up' ? (
                    <TrendingUp className="h-4 w-4 text-green-400" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-red-400" />
                  )}
                  <span className={`text-sm ${kpiData.totalSales.trend === 'up' ? 'text-green-400' : 'text-red-400'}`}>
                    {formatPercentage(kpiData.totalSales.change)}
                  </span>
                </div>
              </div>
              <DollarSign className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.activeProjects}</p>
                <p className="text-2xl font-bold text-white">{kpiData.activeProjects.value}</p>
                <div className="flex items-center gap-1 mt-1">
                  {kpiData.activeProjects.trend === 'up' ? (
                    <TrendingUp className="h-4 w-4 text-green-400" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-red-400" />
                  )}
                  <span className={`text-sm ${kpiData.activeProjects.trend === 'up' ? 'text-green-400' : 'text-red-400'}`}>
                    {formatPercentage(kpiData.activeProjects.change)}
                  </span>
                </div>
              </div>
              <Target className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.totalEmployees}</p>
                <p className="text-2xl font-bold text-white">{kpiData.totalEmployees.value}</p>
                <div className="flex items-center gap-1 mt-1">
                  {kpiData.totalEmployees.trend === 'up' ? (
                    <TrendingUp className="h-4 w-4 text-green-400" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-red-400" />
                  )}
                  <span className={`text-sm ${kpiData.totalEmployees.trend === 'up' ? 'text-green-400' : 'text-red-400'}`}>
                    {formatPercentage(kpiData.totalEmployees.change)}
                  </span>
                </div>
              </div>
              <Users className="h-8 w-8 text-purple-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.lowStockItems}</p>
                <p className="text-2xl font-bold text-white">{kpiData.lowStockItems.value}</p>
                <div className="flex items-center gap-1 mt-1">
                  {kpiData.lowStockItems.trend === 'up' ? (
                    <TrendingUp className="h-4 w-4 text-green-400" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-red-400" />
                  )}
                  <span className={`text-sm ${kpiData.lowStockItems.trend === 'up' ? 'text-green-400' : 'text-red-400'}`}>
                    {formatPercentage(kpiData.lowStockItems.change)}
                  </span>
                </div>
              </div>
              <Package className="h-8 w-8 text-orange-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Sales Trend Chart */}
        <Card className="glass-card border-white/20">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              {t.salesTrend}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={salesTrendData}>
                <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                <XAxis dataKey="month" stroke="rgba(255,255,255,0.7)" />
                <YAxis stroke="rgba(255,255,255,0.7)" />
                <Tooltip 
                  contentStyle={{ 
                    backgroundColor: 'rgba(0,0,0,0.8)', 
                    border: '1px solid rgba(255,255,255,0.2)',
                    borderRadius: '8px'
                  }}
                />
                <Area type="monotone" dataKey="sales" stroke="#3B82F6" fill="rgba(59,130,246,0.3)" />
                <Area type="monotone" dataKey="target" stroke="#10B981" fill="rgba(16,185,129,0.2)" />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Project Status Chart */}
        <Card className="glass-card border-white/20">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <PieChartIcon className="h-5 w-5" />
              {t.projectStatus}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={projectStatusData}
                  cx="50%"
                  cy="50%"
                  outerRadius={100}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                >
                  {projectStatusData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
