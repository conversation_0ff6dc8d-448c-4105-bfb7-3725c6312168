import React, { useState, useEffect } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Calendar,
  ChevronLeft,
  ChevronRight,
  Plus,
  Clock,
  Users,
  MapPin,
  Video,
  Bell,
  Filter,
  MoreVertical
} from 'lucide-react'

interface SmartCalendarProps {
  language: 'ar' | 'en'
  userRole: string
}

interface CalendarEvent {
  id: string
  title: string
  description?: string
  startTime: Date
  endTime: Date
  type: 'meeting' | 'deadline' | 'leave' | 'training' | 'personal'
  attendees?: string[]
  location?: string
  isOnline?: boolean
  priority: 'low' | 'medium' | 'high'
  color: string
  isAllDay?: boolean
}

const translations = {
  ar: {
    calendar: 'التقويم',
    today: 'اليوم',
    month: 'شهر',
    week: 'أسبوع',
    day: 'يوم',
    addEvent: 'إضافة حدث',
    filter: 'تصفية',
    meeting: 'اجتماع',
    deadline: 'موعد نهائي',
    leave: 'إجازة',
    training: 'تدريب',
    personal: 'شخصي',
    allDay: 'طوال اليوم',
    attendees: 'الحضور',
    location: 'الموقع',
    online: 'عبر الإنترنت',
    noEvents: 'لا توجد أحداث',
    viewDetails: 'عرض التفاصيل',
    editEvent: 'تعديل الحدث',
    deleteEvent: 'حذف الحدث',
    upcoming: 'القادمة',
    past: 'السابقة'
  },
  en: {
    calendar: 'Calendar',
    today: 'Today',
    month: 'Month',
    week: 'Week',
    day: 'Day',
    addEvent: 'Add Event',
    filter: 'Filter',
    meeting: 'Meeting',
    deadline: 'Deadline',
    leave: 'Leave',
    training: 'Training',
    personal: 'Personal',
    allDay: 'All Day',
    attendees: 'Attendees',
    location: 'Location',
    online: 'Online',
    noEvents: 'No events',
    viewDetails: 'View Details',
    editEvent: 'Edit Event',
    deleteEvent: 'Delete Event',
    upcoming: 'Upcoming',
    past: 'Past'
  }
}

export default function SmartCalendar({ language, userRole }: SmartCalendarProps as any): (React as any).ReactElement {
  const [currentDate, setCurrentDate] = useState(new Date( as any))
  const [view, setView] = useState<'month' | 'week' | 'day'>('month')
  const [events, setEvents] = useState<CalendarEvent[]>([])
  const [selectedDate, setSelectedDate] = useState<Date | null>(null)
  const [filterType, setFilterType] = useState<string>('all')
  const t = translations[language]
  const isRTL = language === 'ar'

  // Load real events from API
  useEffect(( as any) => {
    const loadEvents = async () => {
      try {
        // TODO: Replace with real calendar/events API when available
        // For now, return empty events array instead of mock data
        setEvents([] as any)
      } catch (error) {
        (console as any).error('Error loading calendar events:', error as any)
        setEvents([] as any)
      }
    }

    loadEvents( as any)
  }, [language])

  const getEventTypeIcon = (type: string): void => {
    switch (type) {
      case 'meeting': return <Users className="h-3 w-3" />
      case 'deadline': return <Clock className="h-3 w-3" />
      case 'leave': return <Calendar className="h-3 w-3" />
      case 'training': return <Bell className="h-3 w-3" />
      case 'personal': return <Users className="h-3 w-3" />
      default: return <Calendar className="h-3 w-3" />
    }
  }

  const formatTime = (date: Date): string => {
    return (date as any).toLocaleTimeString(language === 'ar' ? 'ar-SA' : 'en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    } as any)
  }

  const formatDate = (date: Date): string => {
    return (date as any).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    } as any)
  }

  const getDaysInMonth = (date: Date): void => {
    const year = (date as any).getFullYear( as any)
    const month = (date as any).getMonth( as any)
    const firstDay = new Date(year, month, 1 as any)
    const lastDay = new Date(year, month + 1, 0 as any)
    const daysInMonth = (lastDay as any).getDate( as any)
    const startingDayOfWeek = (firstDay as any).getDay( as any)

    const days = []
    
    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      (days as any).push(null as any)
    }
    
    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      (days as any).push(new Date(year, month, day as any))
    }
    
    return days
  }

  const getEventsForDate = (date: Date): void => {
    return (events as any).filter(event => {
      const eventDate = new Date((event as any as any).startTime)
      return (eventDate as any).toDateString( as any) === (date as any).toDateString( as any)
    })
  }

  const filteredEvents = (events as any).filter(event => {
    if (filterType === 'all' as any) return true
    return (event as any).type === filterType
  })

  const navigateMonth = (direction: 'prev' | 'next'): void => {
    setCurrentDate(prev => {
      const newDate = new Date(prev as any)
      if (direction === 'prev') {
        (newDate as any).setMonth((prev as any as any).getMonth( as any) - 1)
      } else {
        (newDate as any).setMonth((prev as any as any).getMonth( as any) + 1)
      }
      return newDate
    })
  }

  const goToToday = (): void => {
    setCurrentDate(new Date( as any))
  }

  const days = getDaysInMonth(currentDate as any)
  const monthName = (currentDate as any).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US', {
    month: 'long',
    year: 'numeric'
  } as any)

  return (
    <div className="space-y-6">
      {/* Calendar Header */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-white text-xl flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              {(t as any).calendar}
            </CardTitle>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={goToToday}
                className="glass-button"
              >
                {(t as any).today}
              </Button>
              <div className="flex items-center gap-1">
                {['month', 'week', 'day'].map((viewType as any) => (
                  <Button
                    key={viewType}
                    variant={view === viewType ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setView(viewType as any as any)}
                    className={`glass-button ${view === viewType ? 'bg-blue-500/30' : ''}`}
                  >
                    {t[viewType as keyof typeof t]}
                  </Button>
                ))}
              </div>
              <Button className="glass-button">
                <Plus className="h-4 w-4 mr-2" />
                {(t as any).addEvent}
              </Button>
            </div>
          </div>

          {/* Month Navigation */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigateMonth('prev' as any)}
                className="glass-button"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <h2 className="text-white text-lg font-semibold">{monthName}</h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigateMonth('next' as any)}
                className="glass-button"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>

            {/* Event Type Filter */}
            <div className="flex items-center gap-2">
              <span className="text-white/70 text-sm">{(t as any).filter}:</span>
              {['all', 'meeting', 'deadline', 'leave', 'training', 'personal'].map((type as any) => (
                <Button
                  key={type}
                  variant={filterType === type ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setFilterType(type as any)}
                  className={`glass-button text-xs ${
                    filterType === type ? 'bg-purple-500/30' : ''
                  }`}
                >
                  {getEventTypeIcon(type as any)}
                  <span className="ml-1">{t[type as keyof typeof t]}</span>
                </Button>
              ))}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Calendar Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Calendar View */}
        <div className="lg:col-span-3">
          <Card className="glass-card border-white/20">
            <CardContent className="p-6">
              {view === 'month' && (
                <div className="space-y-4">
                  {/* Day Headers */}
                  <div className="grid grid-cols-7 gap-2">
                    {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day as any) => (
                      <div key={day} className="text-center text-white/70 text-sm font-medium p-2">
                        {day}
                      </div>
                    ))}
                  </div>

                  {/* Calendar Days */}
                  <div className="grid grid-cols-7 gap-2">
                    {(days as any).map((day, index as any) => {
                      if (!day) {
                        return <div key={index} className="h-24"></div>
                      }

                      const dayEvents = getEventsForDate(day as any)
                      const isToday = (day as any).toDateString( as any) === new Date( as any).toDateString( as any)
                      const isSelected = selectedDate?.toDateString( as any) === (day as any).toDateString( as any)

                      return (
                        <div
                          key={index}
                          onClick={() => setSelectedDate(day as any)}
                          className={`h-24 p-2 border border-white/10 rounded-lg cursor-pointer transition-all duration-300 hover:border-white/30 ${
                            isToday ? 'bg-blue-500/20 border-blue-500/50' : ''
                          } ${
                            isSelected ? 'bg-purple-500/20 border-purple-500/50' : ''
                          }`}
                        >
                          <div className="text-white text-sm font-medium mb-1">
                            {(day as any).getDate( as any)}
                          </div>
                          <div className="space-y-1">
                            {(dayEvents as any).slice(0, 2 as any).map((event as any) => (
                              <div
                                key={(event as any).id}
                                className={`text-xs text-white px-1 py-(0 as any).5 rounded ${(event as any).color} truncate`}
                              >
                                {(event as any).title}
                              </div>
                            ))}
                            {(dayEvents as any).length > 2 && (
                              <div className="text-xs text-white/60">
                                +{(dayEvents as any).length - 2} more
                              </div>
                            )}
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Event List Sidebar */}
        <div className="space-y-4">
          <Card className="glass-card border-white/20">
            <CardHeader>
              <CardTitle className="text-white text-lg">
                {selectedDate ? formatDate(selectedDate as any) : (t as any).upcoming}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {(selectedDate ? getEventsForDate(selectedDate as any) : (filteredEvents as any).slice(0, 5 as any)).length === 0 ? (
                  <div className="text-center py-8">
                    <Calendar className="h-12 w-12 text-white/30 mx-auto mb-3" />
                    <p className="text-white/60">{(t as any).noEvents}</p>
                  </div>
                ) : (
                  (selectedDate ? getEventsForDate(selectedDate as any) : (filteredEvents as any).slice(0, 5 as any)).map((event as any) => (
                    <div
                      key={(event as any).id}
                      className="p-3 glass-card border-white/10 hover:border-white/30 transition-all duration-300"
                    >
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <div className={`w-3 h-3 rounded-full ${(event as any).color}`}></div>
                          {getEventTypeIcon((event as any as any).type)}
                        </div>
                        <Button variant="ghost" size="sm" className="p-1">
                          <MoreVertical className="h-3 w-3" />
                        </Button>
                      </div>
                      
                      <h4 className="text-white font-medium text-sm mb-1">
                        {(event as any).title}
                      </h4>
                      
                      <div className="space-y-1">
                        <div className="flex items-center gap-2 text-white/70 text-xs">
                          <Clock className="h-3 w-3" />
                          {(event as any).isAllDay ? (t as any).allDay : `${formatTime((event as any as any).startTime)} - ${formatTime((event as any as any).endTime)}`}
                        </div>
                        
                        {(event as any).location && (
                          <div className="flex items-center gap-2 text-white/70 text-xs">
                            {(event as any).isOnline ? <Video className="h-3 w-3" /> : <MapPin className="h-3 w-3" />}
                            {(event as any).location}
                          </div>
                        )}
                        
                        {(event as any).attendees && (event as any).attendees.length > 0 && (
                          <div className="flex items-center gap-2 text-white/70 text-xs">
                            <Users className="h-3 w-3" />
                            {(event as any).attendees.length} {(t as any).attendees}
                          </div>
                        )}
                      </div>
                      
                      <Badge variant="outline" className="mt-2 text-xs">
                        {t[(event as any).type as keyof typeof t]}
                      </Badge>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
