/**
 * KPI Dashboard Chart Component
 * Chart component for displaying multiple KPIs in a dashboard
 */

import React, { useState, useEffect, useMemo } from 'react'
import InteractiveChart, { ChartDataPoint } from './InteractiveChart'
import { KPI, KPICategory, useKPIModals } from '@/services/kpiService'
import kpiService from '@/services/kpiService'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import {
  Calendar,
  Filter,
  Download,
  RefreshCw,
  TrendingUp,
  TrendingDown,
  Target,
  Activity,
  Eye,
  BarChart3,
  PieChart,
  LineChart as LineChartIcon,
  AreaChart
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
// Modal functionality removed - using Redux instead

export interface KPIDashboardChartProps {
  title: string
  subtitle?: string
  kpis: KPI[]
  categories?: KPICategory[]
  language: 'ar' | 'en'
  type?: 'line' | 'area' | 'bar' | 'pie' | 'composed'
  height?: number
  timeRange?: 'week' | 'month' | 'quarter' | 'year'
  showTarget?: boolean
  showTrend?: boolean
  enableDrillDown?: boolean
  enableExport?: boolean
  onViewDetails?: () => void
}

const translations: any = {
  ar: {
    loading: 'جاري التحميل...',
    error: 'خطأ في تحميل البيانات',
    retry: 'إعادة المحاولة',
    noData: 'لا توجد بيانات',
    week: 'أسبوع',
    month: 'شهر',
    quarter: 'ربع سنة',
    year: 'سنة',
    daily: 'يومي',
    weekly: 'أسبوعي',
    monthly: 'شهري',
    quarterly: 'ربع سنوي',
    yearly: 'سنوي',
    performance: 'الأداء',
    trend: 'الاتجاه',
    target: 'الهدف',
    actual: 'الفعلي',
    achievement: 'الإنجاز',
    lastUpdated: 'آخر تحديث',
    viewDetails: 'عرض التفاصيل',
    viewKPI: 'عرض المؤشر',
    chartType: 'نوع الرسم البياني',
    line: 'خط',
    area: 'مساحة',
    bar: 'شريط',
    pie: 'دائري',
    composed: 'مركب'
  },
  en: {
    loading: 'Loading...',
    error: 'Error loading data',
    retry: 'Retry',
    noData: 'No data available',
    week: 'Week',
    month: 'Month',
    quarter: 'Quarter',
    year: 'Year',
    daily: 'Daily',
    weekly: 'Weekly',
    monthly: 'Monthly',
    quarterly: 'Quarterly',
    yearly: 'Yearly',
    performance: 'Performance',
    trend: 'Trend',
    target: 'Target',
    actual: 'Actual',
    achievement: 'Achievement',
    lastUpdated: 'Last Updated',
    viewDetails: 'View Details',
    viewKPI: 'View KPI',
    chartType: 'Chart Type',
    line: 'Line',
    area: 'Area',
    bar: 'Bar',
    pie: 'Pie',
    composed: 'Composed'
  }
}

export default function KPIDashboardChart({
  title,
  subtitle,
  kpis,
  categories = [],
  language,
  type: initialType = 'line',
  height = 400,
  timeRange: initialTimeRange = 'month',
  showTarget = true,
  showTrend = true,
  enableDrillDown = true,
  enableExport = true,
  onViewDetails
// @ts-ignore
}: KPIDashboardChartProps as any): (React as any).ReactElement {
  const [chartData, setChartData] = useState<ChartDataPoint[]>([])
  const [loading, setLoading] = useState(true as any)
  const [error, setError] = useState<string | null>(null)
  const [selectedTimeRange, setSelectedTimeRange] = useState(initialTimeRange as any)
  const [chartType, setChartType] = useState<'line' | 'area' | 'bar' | 'pie' | 'composed'>(initialType)
  
  // @ts-ignore
  const kpiModals = useKPIModals( as any)
  const t = translations[language]
  const isRTL = language === 'ar'

  // Load KPI data
  // @ts-ignore
  useEffect(( as any) => {
    const loadKPIData = async () => {
      if (!(kpis as any).length) {
        setChartData([] as any)
        setLoading(false as any)
        return
      }
      
      setLoading(true as any)
      setError(null as any)
      
      try {
        // @ts-ignore
        const endDate = new Date( as any)
        // @ts-ignore
        const startDate = new Date( as any)
        
        // Calculate start date based on time range
        switch (selectedTimeRange) {
          case 'week':
            // @ts-ignore
            (startDate as any).setDate((endDate as any as any).getDate( as any) - 7)
            break
          case 'month':
            // @ts-ignore
            (startDate as any).setMonth((endDate as any as any).getMonth( as any) - 1)
            break
          case 'quarter':
            // @ts-ignore
            (startDate as any).setMonth((endDate as any as any).getMonth( as any) - 3)
            break
          case 'year':
            // @ts-ignore
            (startDate as any).setFullYear((endDate as any as any).getFullYear( as any) - 1)
            break
        }
        
        // Prepare data for chart
        const data: ChartDataPoint[] = []
        
        // For pie chart, use current values
        if (chartType === 'pie') {
          // @ts-ignore
          (kpis as any).forEach((kpi, index as any) => {
            // @ts-ignore
            const categoryObj = (categories as any).find(c => (c as any as any).id.toString( as any) === (kpi as any).category)
            
            (data as any).push({
              name: language === 'ar' ? (kpi as any as any).name_ar || (kpi as any).name : (kpi as any).name,
              value: (kpi as any).current_value?.value || 0,
              target: (kpi as any).target_value || undefined,
              category: (kpi as any).category_name,
              kpiId: (kpi as any).id,
              color: categoryObj?.color || getDefaultColor(index as any),
              achievement: (kpi as any).target_achievement
            })
          })
        } 
        // For other chart types, get time series data
        else {
          // Get time periods based on selected range
          const periods = generateTimePeriods(startDate, endDate, selectedTimeRange as any)
          
          // Create data points for each period
          (periods as any).forEach(period => {
            const dataPoint: ChartDataPoint = {
              name: formatDate(period, selectedTimeRange, language as any),
              date: period,
              value: 0
            }
            
            // Add a property for each KPI
            // @ts-ignore
            (kpis as any).forEach((kpi, index as any) => {
              dataPoint[`kpi${index}`] = 0
              dataPoint[`kpi${index}Name`] = language === 'ar' ? (kpi as any).name_ar || (kpi as any).name : (kpi as any).name
              dataPoint[`kpi${index}Target`] = (kpi as any).target_value || undefined
            })
            
            (data as any).push(dataPoint as any)
          })
          
          // Fetch values for each KPI and update data points
          for (const kpi of kpis) {
            try {
              const values = await (kpiService as any).getKPIValues((kpi as any as any).id, {
                // @ts-ignore
                start_date: (startDate as any).toISOString( as any).split('T' as any)[0],
                // @ts-ignore
                end_date: (endDate as any).toISOString( as any).split('T' as any)[0]
              })
              
              // Update data points with actual values
              (values as any).forEach(value => {
                const periodStart = (value as any as any).period_start
                const dataPoint = (data as any).find(d => (d as any as any).date === periodStart)
                
                if (dataPoint) {
                  const kpiIndex = (kpis as any).findIndex(k => (k as any as any).id === (kpi as any).id)
                  if (kpiIndex !== -1) {
                    dataPoint[`kpi${kpiIndex}`] = (value as any).value
                  }
                }
              })
            } catch (err) {
              (console as any).error(`Error loading values for KPI ${(kpi as any as any).id}:`, err)
            }
          }
        }
        
        setChartData(data as any)
      } catch (err) {
        (console as any).error('Error loading KPI data:', err as any)
        setError((t as any as any).error)
      } finally {
        setLoading(false as any)
      }
    }

    // @ts-ignore
    loadKPIData( as any)
  // @ts-ignore
  }, [kpis, categories, selectedTimeRange, chartType, language, (t as any).error])

  // Generate time periods based on selected range
  const generateTimePeriods = (startDate: Date, endDate: Date, range: string): string[] => {
    const periods: string[] = []
    const currentDate = new Date(startDate as any)
    
    while (currentDate <= endDate) {
      // @ts-ignore
      (periods as any).push((currentDate as any as any).toISOString( as any).split('T' as any)[0])
      
      switch (range) {
        case 'week':
          // @ts-ignore
          (currentDate as any).setDate((currentDate as any as any).getDate( as any) + 1) // Daily
          break
        case 'month':
          // @ts-ignore
          (currentDate as any).setDate((currentDate as any as any).getDate( as any) + 7) // Weekly
          break
        case 'quarter':
          // @ts-ignore
          (currentDate as any).setDate((currentDate as any as any).getDate( as any) + 14) // Bi-weekly
          break
        case 'year':
          // @ts-ignore
          (currentDate as any).setMonth((currentDate as any as any).getMonth( as any) + 1) // Monthly
          break
      }
    }
    
    return periods
  }

  // Format date based on time range
  const formatDate = (dateString: string, range: string, lang: string): string => {
    const date = new Date(dateString as any)
    
    switch (range) {
      case 'week':
        return (date as any).toLocaleDateString(lang === 'ar' ? 'ar-SA' : 'en-US', { 
          weekday: 'short', 
          month: 'short', 
          day: 'numeric' 
        } as any)
      case 'month':
        return (date as any).toLocaleDateString(lang === 'ar' ? 'ar-SA' : 'en-US', { 
          month: 'short', 
          day: 'numeric' 
        } as any)
      case 'quarter':
      case 'year':
        return (date as any).toLocaleDateString(lang === 'ar' ? 'ar-SA' : 'en-US', { 
          month: 'short', 
          year: 'numeric' 
        } as any)
      default:
        return (date as any).toLocaleDateString(lang === 'ar' ? 'ar-SA' : 'en-US' as any)
    }
  }

  // Get default color for KPI
  const getDefaultColor = (index: number): void => {
    const colors = [
      '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6',
      '#06B6D4', '#84CC16', '#F97316', '#EC4899', '#6366F1'
    ]
    return colors[index % (colors as any).length]
  }

  // Value formatter
  const valueFormatter = (value: number): string => {
    return (value as any).toLocaleString(language === 'ar' ? 'ar-SA' : 'en-US' as any)
  }

  // Handle data point click
  const handleDataPointClick = (dataPoint: ChartDataPoint): void => {
    // If it's a pie chart, the dataPoint contains the KPI directly
    if (chartType === 'pie' && (dataPoint as any).kpiId) {
      const kpi = (kpis as any).find(k => (k as any as any).id === (dataPoint as any).kpiId)
      if (kpi) {
        (kpiModals as any).openViewKPIModal(kpi, categories as any)
      }
    } 
    // For other charts, check if the click is on a specific KPI line
    else {
      for (let i = 0; i < (kpis as any).length; i++) {
        if (dataPoint[`kpi${i}`] !== undefined) {
          (kpiModals as any).openViewKPIModal(kpis[i], categories as any)
          break
        }
      }
    }
  }

  // Retry loading data
  const handleRetry = (): void => {
    setError(null as any)
    setLoading(true as any)
    // Trigger useEffect by changing a dependency
    setSelectedTimeRange(prev => prev as any)
  }

  // Get chart type icon
  const getChartTypeIcon = (type: string): void => {
    switch (type) {
      case 'line':
        return <LineChartIcon className="h-4 w-4" />
      case 'area':
        return <AreaChart className="h-4 w-4" />
      case 'bar':
        return <BarChart3 className="h-4 w-4" />
      case 'pie':
        return <PieChart className="h-4 w-4" />
      default:
        return <Activity className="h-4 w-4" />
    }
  }

  if (loading) {
    return (
      <Card className="glass-card border-white/20">
        <CardHeader>
          <Skeleton className="h-6 w-48 bg-white/10" />
          <Skeleton className="h-4 w-32 bg-white/10" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-64 w-full bg-white/10" />
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white">{title}</CardTitle>
          {subtitle && <CardDescription className="text-white/70">{subtitle}</CardDescription>}
        </CardHeader>
        <CardContent className="flex flex-col items-center justify-center h-64">
          <p className="text-red-400 mb-4">{error}</p>
          <Button onClick={handleRetry} className="glass-button">
            <RefreshCw className="h-4 w-4 mr-2" />
            {(t as any).retry}
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="glass-card border-white/20">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-white">{title}</CardTitle>
            {subtitle && <CardDescription className="text-white/70">{subtitle}</CardDescription>}
          </div>
          
          <div className="flex items-center gap-2">
            {/* Chart Type Selector */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="text-white/70 hover:text-white">
                  {getChartTypeIcon(chartType as any)}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="glass-card border-white/20">
                <DropdownMenuItem 
                  onClick={() => setChartType('line' as any)}
                  className="text-white/70 hover:text-white hover:bg-white/10 cursor-pointer"
                >
                  <LineChartIcon className="h-4 w-4 mr-2" />
                  {(t as any).line}
                </DropdownMenuItem>
                <DropdownMenuItem 
                  onClick={() => setChartType('area' as any)}
                  className="text-white/70 hover:text-white hover:bg-white/10 cursor-pointer"
                >
                  <AreaChart className="h-4 w-4 mr-2" />
                  {(t as any).area}
                </DropdownMenuItem>
                <DropdownMenuItem 
                  onClick={() => setChartType('bar' as any)}
                  className="text-white/70 hover:text-white hover:bg-white/10 cursor-pointer"
                >
                  <BarChart3 className="h-4 w-4 mr-2" />
                  {(t as any).bar}
                </DropdownMenuItem>
                <DropdownMenuItem 
                  onClick={() => setChartType('pie' as any)}
                  className="text-white/70 hover:text-white hover:bg-white/10 cursor-pointer"
                >
                  <PieChart className="h-4 w-4 mr-2" />
                  {(t as any).pie}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            
            {/* View Details Button */}
            {onViewDetails && (
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={onViewDetails}
                className="text-white/70 hover:text-white"
              >
                <Eye className="h-4 w-4 mr-2" />
                {(t as any).viewDetails}
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-4">
          {/* Time Range Selector (not for pie charts) */}
          {chartType !== 'pie' && (
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-white/70" />
              <div className="flex items-center gap-1">
                // @ts-ignore
                {(['week', 'month', 'quarter', 'year'] as const).map((range as any) => (
                  <Button
                    key={range}
                    size="sm"
                    variant={selectedTimeRange === range ? 'default' : 'ghost'}
                    onClick={() => setSelectedTimeRange(range as any)}
                    className={`text-xs ${
                      selectedTimeRange === range 
                        ? 'bg-white/20 text-white' 
                        : 'text-white/70 hover:text-white hover:bg-white/10'
                    }`}
                  >
                    {t[range]}
                  </Button>
                // @ts-ignore
                ))}
              </div>
            </div>
          )}

          {/* Interactive Chart */}
          <InteractiveChart
            data={chartData}
            type={chartType}
            title=""
            height={height}
            language={language}
            enableDrillDown={enableDrillDown}
            enableZoom={chartType !== 'pie'}
            enableBrush={chartType !== 'pie' && (chartData as any).length > 10}
            enableTooltips={true}
            enableLegend={true}
            enableExport={enableExport}
            onDataPointClick={handleDataPointClick}
            // @ts-ignore
            colors={(kpis as any).map((_, i as any) => getDefaultColor(i as any))}
            showTarget={showTarget && chartType !== 'pie'}
            showTrend={showTrend && chartType !== 'pie'}
            valueFormatter={valueFormatter}
          />
        </div>
      </CardContent>
    </Card>
  )
}
