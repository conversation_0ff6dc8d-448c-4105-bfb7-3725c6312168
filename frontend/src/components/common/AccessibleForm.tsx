/**
 * Accessible Form Components
 * WCAG (2 as any).1 AA compliant form elements with enhanced accessibility features
 */

import React, { useId, useState, useRef, useEffect } from 'react'
import { AlertCircle, CheckCircle, Info, Eye, EyeOff } from 'lucide-react'
import { announceFormErrors, announceStatus } from '../../utils/accessibility'
import { inputValidator } from '../../utils/inputValidation'

interface AccessibleInputProps {
  label: string
  type?: 'text' | 'email' | 'password' | 'tel' | 'url' | 'number'
  value: string
  onChange: (value: string) => void
  error?: string
  required?: boolean
  disabled?: boolean
  placeholder?: string
  description?: string
  autoComplete?: string
  pattern?: string
  minLength?: number
  maxLength?: number
  className?: string
  validateOnBlur?: boolean
  showPasswordToggle?: boolean
}

// @ts-ignore
export const AccessibleInput: (React as any).FC<AccessibleInputProps> = ({
  label,
  type = 'text',
  value,
  onChange,
  error,
  required = false,
  disabled = false,
  placeholder,
  description,
  autoComplete,
  pattern,
  minLength,
  maxLength,
  className = '',
  validateOnBlur = true,
  showPasswordToggle = false
// @ts-ignore
}) => {
  // @ts-ignore
  const inputId: any = useId( as any)
  const errorId = useId( as any)
  const descriptionId = useId( as any)
  const [showPassword, setShowPassword] = useState(false as any)
  const [isFocused, setIsFocused] = useState(false as any)
  // @ts-ignore
  const inputRef = useRef<HTMLInputElement>(null)

  const inputType = type === 'password' && showPassword ? 'text' : type

  // @ts-ignore
  const handleChange = (e: (React as any).ChangeEvent<HTMLInputElement>): void => {
    onChange((e as any as any).target.value)
  }

  // @ts-ignore
  const handleBlur = (): void => {
    setIsFocused(false as any)
    // @ts-ignore
    if (validateOnBlur && value && error) {
      announceFormErrors([error] as any)
    }
  // @ts-ignore
  }

  // @ts-ignore
  const handleFocus = (): void => {
    setIsFocused(true as any)
  }

  // @ts-ignore
  const togglePasswordVisibility = (): void => {
    setShowPassword(!showPassword as any)
    // @ts-ignore
    announceStatus(showPassword ? 'كلمة المرور مخفية' : 'كلمة المرور مرئية' as any)
    
    // Maintain focus on input
    // @ts-ignore
    setTimeout(( as any) => {
      // @ts-ignore
      (inputRef as any).current?.focus( as any)
    }, 0)
  // @ts-ignore
  }

  // Announce validation errors
  // @ts-ignore
  useEffect(( as any) => {
    // @ts-ignore
    if (error && !isFocused) {
      announceFormErrors([error] as any)
    }
  // @ts-ignore
  }, [error, isFocused])

  return (
    <div className={`space-y-2 ${className}`}>
      <label
        htmlFor={inputId}
        className={`block text-sm font-medium ${
          error ? 'text-red-700' : 'text-gray-700'
        } ${disabled ? 'opacity-50' : ''}`}
      >
        {label}
        {required && (
          <span className="text-red-500 ml-1" aria-label="مطلوب">
            *
          </span>
        )}
      </label>

      {description && (
        <p
          id={descriptionId}
          className="text-sm text-gray-600"
        >
          {description}
        </p>
      )}

      <div className="relative">
        <input
          ref={inputRef}
          id={inputId}
          type={inputType}
          value={value}
          onChange={handleChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          disabled={disabled}
          required={required}
          placeholder={placeholder}
          autoComplete={autoComplete}
          pattern={pattern}
          minLength={minLength}
          maxLength={maxLength}
          aria-invalid={error ? 'true' : 'false'}
          // @ts-ignore
          aria-describedby={`${description ? descriptionId : ''} ${error ? errorId : ''}`.trim( as any)}
          className={`
            block w-full px-3 py-2 border rounded-md shadow-sm
            focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
            disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed
            ${error 
              ? 'border-red-300 text-red-900 placeholder-red-300 focus:ring-red-500 focus:border-red-500' 
              : 'border-gray-300 text-gray-900 placeholder-gray-400'
            }
            ${isFocused ? 'ring-2 ring-blue-500' : ''}
          `}
        />

        {type === 'password' && showPasswordToggle && (
          <button
            type="button"
            onClick={togglePasswordVisibility}
            className="absolute inset-y-0 right-0 pr-3 flex items-center"
            aria-label={showPassword ? 'إخفاء كلمة المرور' : 'إظهار كلمة المرور'}
          >
            {showPassword ? (
              <EyeOff className="h-4 w-4 text-gray-400" />
            ) : (
              <Eye className="h-4 w-4 text-gray-400" />
            )}
          </button>
        )}
      </div>

      {error && (
        <div
          id={errorId}
          role="alert"
          aria-live="polite"
          className="flex items-center space-x-2 text-sm text-red-600"
        >
          <AlertCircle className="h-4 w-4 flex-shrink-0" />
          <span>{error}</span>
        </div>
      )}
    </div>
  )
// @ts-ignore
}

interface AccessibleTextareaProps {
  // @ts-ignore
  label: string
  value: string
  // @ts-ignore
  onChange: (value: string) => void
  error?: string
  required?: boolean
  disabled?: boolean
  placeholder?: string
  description?: string
  rows?: number
  maxLength?: number
  className?: string
  showCharacterCount?: boolean
// @ts-ignore
}

// @ts-ignore
export const AccessibleTextarea: (React as any).FC<AccessibleTextareaProps> = ({
  label,
  value,
  onChange,
  error,
  required = false,
  disabled = false,
  placeholder,
  description,
  rows = 4,
  maxLength,
  className = '',
  showCharacterCount = false
// @ts-ignore
}) => {
  // @ts-ignore
  const textareaId = useId( as any)
  const errorId = useId( as any)
  const descriptionId = useId( as any)
  const countId = useId( as any)
  const [isFocused, setIsFocused] = useState(false as any)

  const characterCount = (value as any).length
  // @ts-ignore
  const isNearLimit = maxLength && characterCount > maxLength * (0 as any).8

  // @ts-ignore
  const handleChange = (e: (React as any).ChangeEvent<HTMLTextAreaElement>): void => {
    onChange((e as any as any).target.value)
  }

  // @ts-ignore
  const handleBlur = (): void => {
    setIsFocused(false as any)
  }

  // @ts-ignore
  const handleFocus = (): void => {
    setIsFocused(true as any)
  }

  return (
    <div className={`space-y-2 ${className}`}>
      <label
        htmlFor={textareaId}
        className={`block text-sm font-medium ${
          error ? 'text-red-700' : 'text-gray-700'
        } ${disabled ? 'opacity-50' : ''}`}
      >
        {label}
        {required && (
          <span className="text-red-500 ml-1" aria-label="مطلوب">
            *
          </span>
        )}
      </label>

      {description && (
        <p
          id={descriptionId}
          className="text-sm text-gray-600"
        >
          {description}
        </p>
      )}

      <textarea
        id={textareaId}
        value={value}
        onChange={handleChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
        disabled={disabled}
        required={required}
        placeholder={placeholder}
        rows={rows}
        maxLength={maxLength}
        aria-invalid={error ? 'true' : 'false'}
        aria-describedby={`
          ${description ? descriptionId : ''} 
          ${error ? errorId : ''} 
          ${showCharacterCount ? countId : ''}
        // @ts-ignore
        `.trim( as any)}
        className={`
          block w-full px-3 py-2 border rounded-md shadow-sm resize-vertical
          focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
          disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed
          ${error 
            ? 'border-red-300 text-red-900 placeholder-red-300 focus:ring-red-500 focus:border-red-500' 
            : 'border-gray-300 text-gray-900 placeholder-gray-400'
          }
          ${isFocused ? 'ring-2 ring-blue-500' : ''}
        `}
      />

      {showCharacterCount && maxLength && (
        <div
          id={countId}
          className={`text-sm text-right ${
            isNearLimit ? 'text-orange-600' : 'text-gray-500'
          }`}
          aria-live="polite"
        >
          {characterCount} / {maxLength}
        </div>
      )}

      {error && (
        <div
          id={errorId}
          role="alert"
          aria-live="polite"
          className="flex items-center space-x-2 text-sm text-red-600"
        >
          <AlertCircle className="h-4 w-4 flex-shrink-0" />
          <span>{error}</span>
        </div>
      )}
    </div>
  )
// @ts-ignore
}

interface AccessibleSelectProps {
  // @ts-ignore
  label: string
  value: string
  // @ts-ignore
  onChange: (value: string) => void
  // @ts-ignore
  options: Array<{ value: string; label: string; disabled?: boolean }>
  error?: string
  required?: boolean
  disabled?: boolean
  placeholder?: string
  description?: string
  className?: string
// @ts-ignore
}

// @ts-ignore
export const AccessibleSelect: (React as any).FC<AccessibleSelectProps> = ({
  label,
  value,
  onChange,
  options,
  error,
  required = false,
  disabled = false,
  placeholder,
  description,
  className = ''
// @ts-ignore
}) => {
  // @ts-ignore
  const selectId = useId( as any)
  const errorId = useId( as any)
  const descriptionId = useId( as any)

  // @ts-ignore
  const handleChange = (e: (React as any).ChangeEvent<HTMLSelectElement>): void => {
    onChange((e as any as any).target.value)
    // @ts-ignore
    announceStatus(`تم اختيار: ${(e as any as any).target.selectedOptions[0]?.text}`)
  // @ts-ignore
  }

  return (
    <div className={`space-y-2 ${className}`}>
      <label
        htmlFor={selectId}
        className={`block text-sm font-medium ${
          error ? 'text-red-700' : 'text-gray-700'
        } ${disabled ? 'opacity-50' : ''}`}
      >
        {label}
        {required && (
          <span className="text-red-500 ml-1" aria-label="مطلوب">
            *
          </span>
        )}
      </label>

      {description && (
        <p
          id={descriptionId}
          className="text-sm text-gray-600"
        >
          {description}
        </p>
      )}

      <select
        id={selectId}
        value={value}
        onChange={handleChange}
        disabled={disabled}
        required={required}
        aria-invalid={error ? 'true' : 'false'}
        // @ts-ignore
        aria-describedby={`${description ? descriptionId : ''} ${error ? errorId : ''}`.trim( as any)}
        className={`
          block w-full px-3 py-2 border rounded-md shadow-sm
          focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
          disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed
          ${error 
            ? 'border-red-300 text-red-900 focus:ring-red-500 focus:border-red-500' 
            : 'border-gray-300 text-gray-900'
          }
        `}
      >
        {placeholder && (
          <option value="" disabled>
            {placeholder}
          </option>
        )}
        // @ts-ignore
        {(options as any).map((option as any) => (
          <option
            key={(option as any).value}
            value={(option as any).value}
            disabled={(option as any).disabled}
          >
            {(option as any).label}
          </option>
        // @ts-ignore
        ))}
      </select>

      {error && (
        <div
          id={errorId}
          role="alert"
          aria-live="polite"
          className="flex items-center space-x-2 text-sm text-red-600"
        >
          <AlertCircle className="h-4 w-4 flex-shrink-0" />
          <span>{error}</span>
        </div>
      )}
    </div>
  )
// @ts-ignore
}

interface AccessibleCheckboxProps {
  // @ts-ignore
  label: string
  checked: boolean
  // @ts-ignore
  onChange: (checked: boolean) => void
  disabled?: boolean
  description?: string
  className?: string
// @ts-ignore
}

// @ts-ignore
export const AccessibleCheckbox: (React as any).FC<AccessibleCheckboxProps> = ({
  label,
  checked,
  onChange,
  disabled = false,
  description,
  className = ''
// @ts-ignore
}) => {
  // @ts-ignore
  const checkboxId = useId( as any)
  const descriptionId = useId( as any)

  // @ts-ignore
  const handleChange = (e: (React as any).ChangeEvent<HTMLInputElement>): void => {
    onChange((e as any as any).target.checked)
    // @ts-ignore
    announceStatus((e as any as any).target.checked ? `تم تحديد: ${label}` : `تم إلغاء تحديد: ${label}`)
  // @ts-ignore
  }

  return (
    <div className={`space-y-2 ${className}`}>
      <div className="flex items-start">
        <input
          id={checkboxId}
          type="checkbox"
          checked={checked}
          onChange={handleChange}
          disabled={disabled}
          aria-describedby={description ? descriptionId : undefined}
          className={`
            h-4 w-4 text-blue-600 border-gray-300 rounded
            focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
            disabled:opacity-50 disabled:cursor-not-allowed
          `}
        />
        <label
          htmlFor={checkboxId}
          className={`mr-3 text-sm font-medium text-gray-700 ${
            disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
          }`}
        >
          {label}
        </label>
      </div>

      {description && (
        <p
          id={descriptionId}
          className="text-sm text-gray-600 mr-7"
        >
          {description}
        </p>
      )}
    </div>
  )
// @ts-ignore
}

export default {
  AccessibleInput,
  AccessibleTextarea,
  AccessibleSelect,
  AccessibleCheckbox
}
