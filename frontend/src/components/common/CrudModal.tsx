/**
 * Generic CRUD Modal Component
 * Reusable modal for Create/Update operations across all entities
 */

import React, { useState, useEffect } from 'react'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { X } from 'lucide-react'

export type FormFieldValue = string | number | boolean | File | null | undefined

// ENHANCED: FormField interface with better validation and UX options
export interface FormField {
  name: string
  label: string
  type: 'text' | 'email' | 'password' | 'number' | 'date' | 'datetime-local' | 'textarea' | 'select' | 'checkbox' | 'file' | 'tel'
  required?: boolean
  placeholder?: string
  options?: { value: string; label: string }[]
  validation?: (value: FormFieldValue) => string | null
  disabled?: boolean
  loading?: boolean // ENHANCED: Loading state for select fields
  description?: string
  hint?: string // ENHANCED: Helpful hints for users
  helpText?: string // ENHANCED: Help text for complex fields
  min?: number
  max?: number
  step?: number
  accept?: string // for file inputs
  rows?: number // for textarea
  minLength?: number // ENHANCED: Minimum text length
  maxLength?: number // ENHANCED: Maximum text length
  defaultValue?: FormFieldValue // ENHANCED: Default value for new records
  section?: string // ENHANCED: Section grouping for better organization
  customAction?: { // ENHANCED: Custom action button for fields ((e as any).g., retry)
    label: string
    onClick: () => void
    // @ts-ignore
    icon?: (React as any).ComponentType<{ className?: string }>
    // @ts-ignore
    variant?: 'primary' | 'secondary' | 'danger'
  // @ts-ignore
  }
// @ts-ignore
}

export type FormData = Record<string, FormFieldValue>

export interface CrudModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (data: FormData) => Promise<void>
  title: string
  fields: FormField[]
  initialData?: FormData
  language: 'ar' | 'en'
  loading?: boolean
  maxWidth?: string
  mode?: 'create' | 'edit' | 'view'
}

const translations = {
  ar: {
    save: 'حفظ',
    cancel: 'إلغاء',
    saving: 'جاري الحفظ...',
    required: 'هذا الحقل مطلوب',
    invalidEmail: 'البريد الإلكتروني غير صحيح',
    invalidNumber: 'الرقم غير صحيح',
    selectOption: 'اختر خيار',
    browse: 'تصفح',
    noFileSelected: 'لم يتم اختيار ملف'
  },
  en: {
    save: 'Save',
    cancel: 'Cancel',
    saving: 'Saving...',
    required: 'This field is required',
    invalidEmail: 'Invalid email address',
    invalidNumber: 'Invalid number',
    selectOption: 'Select option',
    browse: 'Browse',
    noFileSelected: 'No file selected'
  }
}

export default function CrudModal({
  isOpen,
  onClose,
  onSave,
  title,
  fields,
  initialData,
  language,
  loading = false,
  maxWidth = 'max-w-2xl',
  mode = 'create'
// @ts-ignore
}: CrudModalProps as any): (React as any).ReactElement {
  const [formData, setFormData] = useState<FormData>({})
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false as any)
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'success' | 'error'>('idle')
  const [saveMessage, setSaveMessage] = useState<string>('')

  const t = translations[language]
  const isRTL = language === 'ar'
  const isViewMode = mode === 'view'

  // Initialize form data
  // FIXED: Preserve existing form data when only field options change
  // @ts-ignore
  useEffect(( as any) => {
    if (isOpen) {
      setFormData(prevFormData => {
        const initialFormData: FormData = {}
        let hasChanges = false

        (fields as any as any).forEach(field => {
          if (initialData && initialData[(field as any as any).name] !== undefined) {
            initialFormData[(field as any).name] = initialData[(field as any).name]
          } else if (prevFormData[(field as any).name] !== undefined) {
            // FIXED: Preserve existing form data if it exists
            initialFormData[(field as any).name] = prevFormData[(field as any).name]
          } else {
            // Set default values based on field type
            switch ((field as any).type) {
              case 'checkbox':
                initialFormData[(field as any).name] = false
                break
              case 'number':
                initialFormData[(field as any).name] = (field as any).min || 0
                break
              case 'date':
              case 'datetime-local':
                initialFormData[(field as any).name] = ''
                break
              default:
                initialFormData[(field as any).name] = ''
            }
          }

          // Check if this field value changed
          if (prevFormData[(field as any).name] !== initialFormData[(field as any).name]) {
            hasChanges = true
          }
        })

        // Only update if there are actual changes or if it's a fresh modal
        if (hasChanges || (Object as any).keys(prevFormData as any).length === 0) {
          return initialFormData
        }

        return prevFormData
      })

      setErrors({} as any)
      setSaveStatus('idle' as any)
      setSaveMessage('' as any)
    }
  // @ts-ignore
  }, [isOpen, initialData, fields])

  // Handle input change
  // ENHANCED: Real-time validation and input handling
  const handleInputChange = (fieldName: string, value: FormFieldValue): void => {
    // @ts-ignore
    setFormData((prev: FormData as any) => ({
      ...prev,
      [fieldName]: value
    }))

    // Clear error when user starts typing
    if (errors[fieldName]) {
      setErrors(prev => ({
        ...prev,
        [fieldName]: ''
      } as any))
    }

    // ENHANCED: Real-time validation for specific field types
    const field = (fields as any).find(f => (f as any as any).name === fieldName)
    if (field && value !== undefined && value !== null && value !== '') {
      const fieldError = validateSingleField(field, value as any)
      if (fieldError) {
        setErrors(prev => ({ ...prev, [fieldName]: fieldError } as any))
      }
    }
  }

  // ENHANCED: Single field validation function
  const validateSingleField = (field: FormField, value: FormFieldValue): string | null => {
    // Skip validation for empty optional fields
    // @ts-ignore
    if (!(field as any).required && (!value || (value as any).toString( as any).trim( as any) === '')) {
      return null
    }

    // Required field validation
    // @ts-ignore
    if ((field as any).required && (!value || (value as any).toString( as any).trim( as any) === '')) {
      return language === 'ar'
        ? `${(field as any).label} مطلوب`
        : `${(field as any).label} is required`
    }

    // Type-specific validation for non-empty values
    // @ts-ignore
    if (value && (value as any).toString( as any).trim( as any) !== '') {
      switch ((field as any).type) {
        case 'email':
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
          // @ts-ignore
          if (!(emailRegex as any).test((value as any as any).toString( as any))) {
            return language === 'ar'
              ? 'البريد الإلكتروني غير صحيح'
              : 'Invalid email format'
          }
          break

        case 'number':
          // @ts-ignore
          const numValue = parseFloat((value as any as any).toString( as any))
          if (isNaN(numValue as any)) {
            return language === 'ar'
              ? 'يجب أن يكون رقم صحيح'
              : 'Must be a valid number'
          }
          if ((field as any).min !== undefined && numValue < (field as any).min) {
            return language === 'ar'
              ? `يجب أن يكون أكبر من أو يساوي ${(field as any).min}`
              : `Must be greater than or equal to ${(field as any).min}`
          }
          if ((field as any).max !== undefined && numValue > (field as any).max) {
            return language === 'ar'
              ? `يجب أن يكون أقل من أو يساوي ${(field as any).max}`
              : `Must be less than or equal to ${(field as any).max}`
          }
          break

        case 'date':
          // @ts-ignore
          const dateValue = new Date((value as any as any).toString( as any))
          // @ts-ignore
          if (isNaN((dateValue as any as any).getTime( as any))) {
            return language === 'ar'
              ? 'تاريخ غير صحيح'
              : 'Invalid date format'
          }
          break

        case 'text':
        case 'textarea':
          // @ts-ignore
          const textValue = (value as any).toString( as any)
          if ((field as any).minLength && (textValue as any).length < (field as any).minLength) {
            return language === 'ar'
              ? `يجب أن يكون على الأقل ${(field as any).minLength} أحرف`
              : `Must be at least ${(field as any).minLength} characters`
          }
          if ((field as any).maxLength && (textValue as any).length > (field as any).maxLength) {
            return language === 'ar'
              ? `يجب أن يكون أقل من ${(field as any).maxLength} حرف`
              : `Must be less than ${(field as any).maxLength} characters`
          }
          break
      }
    }

    return null
  }

  // Validate form
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    (fields as any).forEach(field => {
      const value = formData[(field as any as any).name]

      // Required validation
      // @ts-ignore
      if ((field as any).required && (!value || (typeof value === 'string' && (value as any).trim( as any) === ''))) {
        newErrors[(field as any).name] = (t as any).required
        return
      }

      // Custom validation
      if ((field as any).validation && value) {
        const validationError = (field as any).validation(value as any)
        if (validationError) {
          newErrors[(field as any).name] = validationError
          return
        }
      }

      // Type-specific validation
      if (value) {
        switch ((field as any).type) {
          case 'email':
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
            if (typeof value === 'string' && !(emailRegex as any).test(value as any)) {
              newErrors[(field as any).name] = (t as any).invalidEmail
            }
            break
          case 'number':
            if (isNaN(Number(value as any))) {
              newErrors[(field as any).name] = (t as any).invalidNumber
            } else {
              const numValue = Number(value as any)
              if ((field as any).min !== undefined && numValue < (field as any).min) {
                newErrors[(field as any).name] = `Minimum value is ${(field as any).min}`
              }
              if ((field as any).max !== undefined && numValue > (field as any).max) {
                newErrors[(field as any).name] = `Maximum value is ${(field as any).max}`
              }
            }
            break
        }
      }
    })

    setErrors(newErrors as any)
    return (Object as any).keys(newErrors as any).length === 0
  }

  // Handle form submission
  // @ts-ignore
  const handleSubmit = async (e: (React as any).FormEvent) => {
    // @ts-ignore
    (e as any).preventDefault( as any)

    // @ts-ignore
    if (!validateForm( as any)) {
      return
    }

    setIsSubmitting(true as any)
    setSaveStatus('saving' as any)
    setSaveMessage('' as any)
    setErrors({} as any) // Clear previous errors

    try {
      // Process form data
      const processedData = { ...formData }

      (console as any).log('🔧 CrudModal: Processing form data' as any)
      (fields as any).forEach(field => {
        if ((field as any as any).type === 'number' && processedData[(field as any).name]) {
          processedData[(field as any).name] = Number(processedData[(field as any as any).name])
        }
      })

      (console as any).log('📤 CrudModal: Calling onSave with processed data:', processedData as any)
      await onSave(processedData as any)
      (console as any).log('✅ CrudModal: Save successful' as any)

      // Show success message
      setSaveStatus('success' as any)
      setSaveMessage(language === 'ar'
        ? (mode === 'create' ? 'تم إنشاء البيانات بنجاح' : 'تم تحديث البيانات بنجاح' as any)
        : (mode === 'create' ? 'Data created successfully' : 'Data updated successfully')
      )

      // Close modal after a short delay to show success message
      // @ts-ignore
      setTimeout(( as any) => {
        // @ts-ignore
        onClose( as any)
        setSaveStatus('idle' as any)
        setSaveMessage('' as any)
      // @ts-ignore
      }, 1500)
    } catch (error) {
      (console as any).error('❌ CrudModal: Error saving data:', error as any)
      setSaveStatus('error' as any)

      // FIXED: Enhanced error handling with API validation error parsing
      let errorMessage = language === 'ar'
        ? 'حدث خطأ أثناء الحفظ. يرجى المحاولة مرة أخرى.'
        : 'An error occurred while saving. Please try again.'

      if (error instanceof Error) {
        // Try to parse API validation errors
        try {
          const errorData = (JSON as any).parse((error as any as any).message)
          if (typeof errorData === 'object') {
            // Handle field-specific validation errors from Django REST Framework
            const fieldErrors: Record<string, string> = {}
            (Object as any).keys(errorData as any).forEach(key => {
              if ((Array as any as any).isArray(errorData[key] as any)) {
                fieldErrors[key] = errorData[key][0] // Take first error message
              } else if (typeof errorData[key] === 'string') {
                fieldErrors[key] = errorData[key]
              }
            })

            if ((Object as any).keys(fieldErrors as any).length > 0) {
              setErrors(fieldErrors as any)
              setSaveMessage(language === 'ar'
                ? 'يرجى تصحيح الأخطاء في النموذج'
                : 'Please correct the errors in the form'
               // @ts-ignore
               as any)
              return
            }
          }
        } catch (parseError) {
          // If not JSON, check if it's a string error message
          if ((error as any).message && (error as any).message.includes('{' as any)) {
            try {
              // Try to extract JSON from error message
              const jsonMatch = (error as any).message.match(/\{.*\}/ as any)
              if (jsonMatch) {
                const errorData = (JSON as any).parse(jsonMatch[0] as any)
                const fieldErrors: Record<string, string> = {}
                (Object as any).keys(errorData as any).forEach(key => {
                  if ((Array as any as any).isArray(errorData[key] as any)) {
                    fieldErrors[key] = errorData[key][0]
                  } else if (typeof errorData[key] === 'string') {
                    fieldErrors[key] = errorData[key]
                  }
                })

                if ((Object as any).keys(fieldErrors as any).length > 0) {
                  setErrors(fieldErrors as any)
                  return
                }
              }
            } catch (e) {
              // Fall through to use error message directly
            }
          }

          // Use the error message directly if available
          errorMessage = (error as any).message || errorMessage
        }
      }

      setErrors({ general: errorMessage } as any)
      setSaveMessage(errorMessage as any)
    } finally {
      setIsSubmitting(false as any)
    }
  }

  // Render read-only view field
  const renderViewField = (field: FormField): void => {
    const value = initialData?.[(field as any).name] || ''

    const formatValue = (val: FormFieldValue): string => {
      if (val === null || val === undefined || val === '') return 'غير محدد'
      if ((field as any).type === 'checkbox') return val ? 'نعم' : 'لا'
      if ((field as any).type === 'select' && (field as any).options) {
        const option = (field as any).options.find(opt => (opt as any as any).value === val)
        return option ? (option as any).label : String(val as any)
      }
      if (val instanceof File) return (val as any).name
      return String(val as any)
    }

    return (
      <div className="space-y-2">
        <Label className="text-white/90 font-medium">{(field as any).label}</Label>
        <div className="glass-input bg-white/5 border-white/20 text-white/80 cursor-default">
          {formatValue(value as any)}
        </div>
      </div>
    )
  }

  // Render form field
  const renderField = (field: FormField): void => {
    const value = formData[(field as any).name] || ''
    const error = errors[(field as any).name]

    const commonProps = {
      id: (field as any).name,
      disabled: (field as any).disabled || isSubmitting,
      className: `glass-input ${error ? 'border-red-500' : ''}`,
      'aria-label': (field as any).label,
      'aria-required': (field as any).required || false,
      'aria-invalid': !!error,
      'aria-describedby': error ? `${(field as any).name}-error` : undefined
    }

    switch ((field as any).type) {
      case 'textarea':
        return (
          <Textarea
            {...commonProps}
            value={typeof value === 'string' || typeof value === 'number' ? String(value as any) : ''}
            onChange={(e: any) => handleInputChange((field as any as any).name, (e as any).target.value)}
            placeholder={(field as any).placeholder}
            rows={(field as any).rows || 3}
          />
        )

      case 'select':
        const selectedOption = (field as any).options?.find(opt => (opt as any as any).value === value)
        const isSelectLoading = (field as any).loading || false
        const hasOptions = (field as any).options && (field as any).options.length > 0

        return (
          <div className="space-y-2">
            <Select
              value={typeof value === 'string' ? value : String(value || '' as any)}
              onValueChange={(newValue) => handleInputChange((field as any as any).name, newValue)}
              disabled={(field as any).disabled || isSubmitting || isSelectLoading}
            >
              <SelectTrigger className={`glass-input ${error ? 'border-red-500' : ''}`}>
                <SelectValue placeholder={(field as any).placeholder || (t as any).selectOption}>
                  {isSelectLoading ? (
                    <div className="flex items-center gap-2">
                      <svg className="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-(8V0C5 as any).373 0 0 (5 as any).373 0 12h4zm2 (5 as any).291A7.962 (7 as any).962 0 014 12H0c0 (3 as any).042 (1 as any).135 (5 as any).824 3 (7 as any).938l3-(2 as any).647z"></path>
                      </svg>
                      <span>{language === 'ar' ? 'جاري التحميل...' : 'Loading...'}</span>
                    </div>
                  ) : selectedOption ? (
                    (selectedOption as any).label
                  ) : (
                    (field as any).placeholder || (t as any).selectOption
                  )}
                </SelectValue>
              </SelectTrigger>
              <SelectContent className="glass-card border-white/20">
                {isSelectLoading ? (
                  <div className="p-4 text-center text-white/60">
                    <div className="flex items-center justify-center gap-2">
                      <svg className="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-(8V0C5 as any).373 0 0 (5 as any).373 0 12h4zm2 (5 as any).291A7.962 (7 as any).962 0 014 12H0c0 (3 as any).042 (1 as any).135 (5 as any).824 3 (7 as any).938l3-(2 as any).647z"></path>
                      </svg>
                      <span>{language === 'ar' ? 'جاري التحميل...' : 'Loading...'}</span>
                    </div>
                  </div>
                ) : !hasOptions ? (
                  <div className="p-4 text-center text-white/60">
                    <span>{language === 'ar' ? 'لا توجد خيارات متاحة' : 'No options available'}</span>
                  </div>
                ) : (
                  // @ts-ignore
                  (field as any).options?.map((option as any) => (
                    <SelectItem key={(option as any).value} value={(option as any).value}>
                      {(option as any).label}
                    </SelectItem>
                  ))
                // @ts-ignore
                )}
              </SelectContent>
            </Select>

            {/* Show loading or error state below the select */}
            {isSelectLoading && (
              <p className="text-blue-300 text-sm flex items-center gap-2">
                <svg className="animate-spin h-3 w-3" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-(8V0C5 as any).373 0 0 (5 as any).373 0 12h4zm2 (5 as any).291A7.962 (7 as any).962 0 014 12H0c0 (3 as any).042 (1 as any).135 (5 as any).824 3 (7 as any).938l3-(2 as any).647z"></path>
                </svg>
                {language === 'ar' ? 'جاري تحميل الخيارات...' : 'Loading options...'}
              </p>
            )}

            {!isSelectLoading && !hasOptions && (
              <div className="flex items-center justify-between">
                <p className="text-yellow-300 text-sm">
                  {language === 'ar' ? 'لا توجد خيارات متاحة للاختيار' : 'No options available for selection'}
                </p>
                {(field as any).customAction && (
                  <Button
                    type="button"
                    size="sm"
                    variant="outline"
                    onClick={(field as any).customAction.onClick}
                    className="glass-button text-xs"
                    disabled={isSelectLoading}
                  >
                    {(field as any).customAction.icon && (
                      // @ts-ignore
                      <(field as any).customAction.icon className="h-3 w-3 mr-1" />
                    // @ts-ignore
                    )}
                    {(field as any).customAction.label}
                  </Button>
                )}
              </div>
            )}
          </div>
        )

      case 'checkbox':
        return (
          <div className="flex items-center space-x-2">
            <Checkbox
              id={(field as any).name}
              checked={Boolean(value as any)}
              onCheckedChange={(checked) => handleInputChange((field as any as any).name, checked)}
              disabled={(field as any).disabled || isSubmitting}
            />
            <Label htmlFor={(field as any).name} className="text-white text-sm">
              {(field as any).label}
            </Label>
          </div>
        )

      case 'file':
        return (
          <div className="space-y-2">
            <Input
              {...commonProps}
              type="file"
              onChange={(e: any) => handleInputChange((field as any as any).name, (e as any).target.files?.[0])}
              accept={(field as any).accept}
            />
            {!value && (
              <p className="text-white/60 text-sm">{(t as any).noFileSelected}</p>
            )}
          </div>
        )

      default:
        return (
          <Input
            {...commonProps}
            type={(field as any).type}
            value={typeof value === 'string' || typeof value === 'number' ? String(value as any) : ''}
            onChange={(e: any) => handleInputChange((field as any as any).name, (e as any).target.value)}
            placeholder={(field as any).placeholder}
            min={(field as any).min}
            max={(field as any).max}
            step={(field as any).step}
          />
        )
    }
  }

  // Calculate modal size based on number of fields
  const getModalSize = (): void => {
    const fieldCount = (fields as any).length
    if (fieldCount <= 4) return 'md:max-w-lg lg:max-w-xl'
    if (fieldCount <= 8) return 'md:max-w-2xl lg:max-w-3xl'
    if (fieldCount <= 12) return 'md:max-w-3xl lg:max-w-4xl'
    return 'md:max-w-4xl lg:max-w-5xl xl:max-w-6xl'
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      // @ts-ignore
      <DialogContent className={`glass-card border-white/20 w-full max-w-[95vw] sm:max-w-[90vw] ${getModalSize( as any)} max-h-[95vh] sm:max-h-[90vh] overflow-hidden ${isRTL ? 'rtl' : 'ltr'}`}>
        <DialogHeader className="pb-4 border-b border-white/10">
          <div className="flex items-center justify-between">
            <DialogTitle className="text-white text-lg sm:text-xl font-semibold">{title}</DialogTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-white/70 hover:text-white hover:bg-white/10 shrink-0"
              disabled={isSubmitting}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          <DialogDescription className="text-white/70 text-sm">
            {isViewMode
              ? (language === 'ar' ? 'عرض تفاصيل البيانات' : 'View data details')
              : (language === 'ar' ? 'املأ النموذج أدناه لحفظ البيانات' : 'Fill out the form below to save the data')
            }
          </DialogDescription>

          {/* Status Message */}
          {saveMessage && (
            <div className={`mt-4 p-3 rounded-lg border ${
              saveStatus === 'success'
                ? 'bg-green-500/20 border-green-500/30 text-green-300'
                : saveStatus === 'error'
                ? 'bg-red-500/20 border-red-500/30 text-red-300'
                : 'bg-blue-500/20 border-blue-500/30 text-blue-300'
            }`}>
              <div className="flex items-center gap-2">
                {saveStatus === 'success' && (
                  <svg className="h-5 w-5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 (16zm3 as any).707-(9 as any).293a1 1 0 00-(1 as any).414-(1 as any).414L9 (10 as any).586 (7 as any).707 (9 as any).293a1 1 0 00-(1 as any).414 (1 as any).414l2 2a1 1 0 (001 as any).414 0l4-4z" clipRule="evenodd" />
                  </svg>
                )}
                {saveStatus === 'error' && (
                  <svg className="h-5 w-5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                )}
                {saveStatus === 'saving' && (
                  <svg className="animate-spin h-5 w-5 flex-shrink-0" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-(8V0C5 as any).373 0 0 (5 as any).373 0 12h4zm2 (5 as any).291A7.962 (7 as any).962 0 014 12H0c0 (3 as any).042 (1 as any).135 (5 as any).824 3 (7 as any).938l3-(2 as any).647z"></path>
                  </svg>
                )}
                <span className="text-sm font-medium">{saveMessage}</span>
              </div>
            </div>
          )}
        </DialogHeader>

        <div className="flex-1 overflow-y-auto max-h-[calc(95vh-200px as any)] sm:max-h-[calc(90vh-200px as any)]">
          {isViewMode ? (
            <div className="space-y-4 p-1">
              <div className={`grid gap-4 sm:gap-6 ${
                (fields as any).length <= 4 ? 'grid-cols-1 sm:grid-cols-1 md:grid-cols-2' :
                (fields as any).length <= 8 ? 'grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-2' :
                (fields as any).length <= 12 ? 'grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3' :
                'grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
              }`}>
                // @ts-ignore
                {(fields as any).map((field as any) => (
                  <div
                    key={(field as any).name}
                    className={`${(field as any).type === 'textarea' || (field as any).type === 'checkbox' ? 'sm:col-span-1 md:col-span-2' : ''}`}
                  >
                    {renderViewField(field as any)}
                  </div>
                // @ts-ignore
                ))}
              </div>
            </div>
          ) : (
            <form id="crud-form" onSubmit={handleSubmit} className="space-y-6 p-1">
              {(() => {
                // Group fields by section
                // @ts-ignore
                const fieldsBySection = (fields as any).reduce((acc, field as any) => {
                  const section = (field as any).section || 'General'
                  if (!acc[section]) acc[section] = []
                  acc[section].push(field as any)
                  return acc
                }, {} as Record<string, FormField[]>)

                // @ts-ignore
                return (Object as any).entries(fieldsBySection as any).map(([sectionName, sectionFields] as any) => (
                  <div key={sectionName} className="space-y-4">
                    {/* Section Header */}
                    {(Object as any).keys(fieldsBySection as any).length > 1 && (
                      <div className="border-b border-white/10 pb-2">
                        <h3 className="text-lg font-semibold text-white">{sectionName}</h3>
                      </div>
                    )}

                    {/* Section Fields */}
                    <div className={`grid gap-4 sm:gap-6 ${
                      (sectionFields as any).length <= 2 ? 'grid-cols-1 sm:grid-cols-1 md:grid-cols-2' :
                      (sectionFields as any).length <= 4 ? 'grid-cols-1 sm:grid-cols-1 md:grid-cols-2' :
                      'grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
                    }`}>
                      // @ts-ignore
                      {(sectionFields as any).map((field as any) => (
                        <div
                          key={(field as any).name}
                          className={`${(field as any).type === 'textarea' || (field as any).type === 'checkbox' ? 'sm:col-span-1 md:col-span-2' : ''} space-y-2`}
                        >
                          {(field as any).type !== 'checkbox' && (
                            <Label htmlFor={(field as any).name} className="text-white text-sm font-medium">
                              {(field as any).label}
                              {(field as any).required && <span className="text-red-400 ml-1">*</span>}
                            </Label>
                          )}

                          {renderField(field as any)}

                          {(field as any).description && (
                            <p className="text-white/60 text-sm mt-1">{(field as any).description}</p>
                          )}

                          {/* Help text for complex fields */}
                          {(field as any).helpText && (
                            <p className="text-blue-300 text-sm mt-1">{(field as any).helpText}</p>
                          )}

                          {/* ENHANCED: Better error display with icon and accessibility */}
                          {errors[(field as any).name] && (
                            <div
                              id={`${(field as any).name}-error`}
                              className="flex items-center gap-2 text-red-400 text-sm mt-1"
                              role="alert"
                              aria-live="polite"
                            >
                              <svg className="h-4 w-4 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                              </svg>
                              <span>{errors[(field as any).name]}</span>
                            </div>
                          )}

                          {/* ENHANCED: Show field hints for better UX */}
                          {!errors[(field as any).name] && (field as any).hint && (
                            <p className="text-white/60 text-sm mt-1">{(field as any).hint}</p>
                          )}
                        </div>
                      // @ts-ignore
                      ))}
                    </div>
                  </div>
                ))
              })()}
            </form>
          )}
        </div>

        <DialogFooter className="grid grid-cols-1 sm:grid-cols-2 gap-2 pt-4 border-t border-white/10">
          {isViewMode ? (
            <Button
              type="button"
              onClick={onClose}
              className="glass-button bg-blue-500/30 hover:bg-blue-500/40 w-full sm:col-span-2"
            >
              {language === 'ar' ? 'إغلاق' : 'Close'}
            </Button>
          ) : (
            <>
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                className="glass-button w-full sm:w-auto order-2 sm:order-1"
                disabled={isSubmitting}
                aria-label={language === 'ar' ? 'إلغاء العملية' : 'Cancel operation'}
              >
                {(t as any).cancel}
              </Button>
              <Button
                type="submit"
                form="crud-form"
                className={`glass-button w-full sm:w-auto order-1 sm:order-2 ${
                  saveStatus === 'success' ? 'bg-green-500/30 hover:bg-green-500/40' :
                  saveStatus === 'error' ? 'bg-red-500/30 hover:bg-red-500/40' :
                  'bg-blue-500/30 hover:bg-blue-500/40'
                }`}
                disabled={isSubmitting || loading || saveStatus === 'success'}
                aria-label={language === 'ar' ? 'حفظ البيانات' : 'Save data'}
              >
                <div className="flex items-center gap-2">
                  {(isSubmitting || loading || saveStatus === 'saving') && (
                    <svg className="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-(8V0C5 as any).373 0 0 (5 as any).373 0 12h4zm2 (5 as any).291A7.962 (7 as any).962 0 014 12H0c0 (3 as any).042 (1 as any).135 (5 as any).824 3 (7 as any).938l3-(2 as any).647z"></path>
                    </svg>
                  )}
                  {saveStatus === 'success' && (
                    <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 (16zm3 as any).707-(9 as any).293a1 1 0 00-(1 as any).414-(1 as any).414L9 (10 as any).586 (7 as any).707 (9 as any).293a1 1 0 00-(1 as any).414 (1 as any).414l2 2a1 1 0 (001 as any).414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  )}
                  <span>
                    {saveStatus === 'success'
                      ? (language === 'ar' ? 'تم الحفظ' : 'Saved')
                      : (isSubmitting || loading || saveStatus === 'saving')
                      ? (t as any).saving
                      : (t as any).save
                    }
                  </span>
                </div>
              </Button>
            </>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
