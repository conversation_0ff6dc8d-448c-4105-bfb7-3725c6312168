/**
 * Generic CRUD Table Component
 * Reusable table with sorting, filtering, and actions for all entities
 */

import React, { useState, useMemo, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Search,
  Filter,
  SortAsc,
  SortDesc,
  MoreVertical,
  Eye,
  Edit,
  Trash2,
  X,
  Plus,
  RefreshCw,
  Download,
  Upload,
  ChevronLeft,
  ChevronRight,
  ChevronDown,
  FileText,
  FileSpreadsheet,
  FileImage
} from 'lucide-react'
import { UnifiedExportButton } from './UnifiedExportButton'
import { LoadingOverlay, EnhancedSpinner } from '@/components/ui/enhanced-loading'
import { useUXFeedback } from '@/utils/uxFeedbackManager'
// Note: Mobile optimization utility removed - using CSS media queries instead
import { MobileTable, TouchButton, CollapsibleSection } from '@/components/ui/mobile-responsive'

export interface TableColumn<T = Record<string, unknown>> {
  key: string
  label: string
  sortable?: boolean
  filterable?: boolean
  // @ts-ignore
  render?: (item: T) => (React as any).ReactNode
  // @ts-ignore
  width?: string
  // @ts-ignore
  align?: 'left' | 'center' | 'right'
// @ts-ignore
}

export interface TableAction<T = Record<string, unknown>> {
  label: string
  // @ts-ignore
  icon: (React as any).ComponentType<{ className?: string }>
  onClick: (item: T) => void
  // @ts-ignore
  variant?: 'default' | 'destructive' | 'outline' | 'ghost'
  // @ts-ignore
  className?: string
  // @ts-ignore
  show?: (item: T) => boolean
// @ts-ignore
}

export interface FilterOption {
  key: string
  label: string
  options: { value: string; label: string }[]
}

export interface CrudTableProps<T = Record<string, unknown>> {
  title: string
  data: T[]
  columns: TableColumn<T>[]
  actions?: TableAction<T>[]
  filters?: FilterOption[]
  loading?: boolean
  searchPlaceholder?: string
  language: 'ar' | 'en'

  // CRUD operations
  onCreate?: () => void
  onRefresh?: () => void
  onExport?: (format?: 'csv' | 'excel' | 'pdf') => void
  onImport?: (file: File) => void

  // UI Control props for role-based restrictions
  showCreateButton?: boolean
  showExportButton?: boolean
  showImportButton?: boolean
  createButtonText?: string

  // Pagination
  currentPage?: number
  totalPages?: number
  pageSize?: number
  total?: number
  onPageChange?: (page: number) => void
  onPageSizeChange?: (pageSize: number) => void

  // Search and filter
  searchQuery?: string
  onSearchChange?: (query: string) => void
  activeFilters?: Record<string, string>
  onFilterChange?: (filters: Record<string, string>) => void

  // Sorting
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  onSortChange?: (sortBy: string, sortOrder: 'asc' | 'desc') => void

  // Selection
  selectable?: boolean
  selectedItems?: T[]
  onSelectionChange?: (items: T[]) => void

  // Bulk actions
  bulkActions?: TableAction[]
}

const translations = {
  ar: {
    search: 'بحث...',
    filter: 'تصفية',
    create: 'إنشاء جديد',
    refresh: 'تحديث',
    export: 'تصدير',
    import: 'استيراد',
    actions: 'الإجراءات',
    noData: 'لا توجد بيانات',
    loading: 'جاري التحميل...',
    page: 'صفحة',
    of: 'من',
    items: 'عنصر',
    selected: 'محدد',
    selectAll: 'تحديد الكل',
    clearSelection: 'إلغاء التحديد',
    bulkActions: 'إجراءات مجمعة',
    previous: 'السابق',
    next: 'التالي'
  },
  en: {
    search: 'Search...',
    filter: 'Filter',
    create: 'Create New',
    refresh: 'Refresh',
    export: 'Export',
    import: 'Import',
    actions: 'Actions',
    noData: 'No data available',
    loading: 'Loading...',
    page: 'Page',
    of: 'of',
    items: 'items',
    selected: 'selected',
    selectAll: 'Select All',
    clearSelection: 'Clear Selection',
    bulkActions: 'Bulk Actions',
    previous: 'Previous',
    next: 'Next'
  }
}

export default function CrudTable<T = Record<string, unknown>>({
  title,
  data,
  columns,
  actions = [],
  filters = [],
  loading = false,
  searchPlaceholder,
  language,
  onCreate,
  onRefresh,
  onExport,
  onImport,
  currentPage = 1,
  totalPages = 1,
  pageSize = 20,
  total = 0,
  onPageChange,
  onPageSizeChange,
  searchQuery = '',
  onSearchChange,
  activeFilters = {},
  onFilterChange,
  sortBy,
  sortOrder,
  onSortChange,
  selectable = false,
  selectedItems = [],
  onSelectionChange,
  bulkActions = [],
  showCreateButton = true,
  showExportButton = true,
  showImportButton = true,
  createButtonText
}: CrudTableProps<T>) {
  const [showFilters, setShowFilters] = useState(false as any)
  const t = translations[language]
  const isRTL = language === 'ar'

  // UX FIX: Enhanced feedback system
  const uxFeedback = useUXFeedback(language as any)

  // MOBILE FIX: Mobile optimization using CSS media queries
  const [isMobile, setIsMobile] = useState(false as any)
  const [isTablet, setIsTablet] = useState(false as any)

  // @ts-ignore
  useEffect(( as any) => {
    const checkDevice = (): void => {
      const width = (window as any).innerWidth
      setIsMobile(width < 768 as any)
      setIsTablet(width >= 768 && width < 1024 as any)
    }

    // @ts-ignore
    checkDevice( as any)
    (window as any).addEventListener('resize', checkDevice as any)
    return () => (window as any).removeEventListener('resize', checkDevice as any)
  // @ts-ignore
  }, [])

  // FIXED: Memoize expensive computations
  // @ts-ignore
  const memoizedData = useMemo(( as any) => data, [data])
  // @ts-ignore
  const memoizedColumns = useMemo(( as any) => columns, [columns])
  // @ts-ignore
  const memoizedActions = useMemo(( as any) => actions, [actions])

  // Handle sort
  const handleSort = (columnKey: string): void => {
    if (!onSortChange) return

    const newSortOrder = sortBy === columnKey && sortOrder === 'asc' ? 'desc' : 'asc'
    onSortChange(columnKey, newSortOrder as any)
  }

  // Handle selection
  const handleSelectAll = (): void => {
    if (!onSelectionChange) return

    if ((selectedItems as any).length === (memoizedData as any).length) {
      onSelectionChange([] as any)
    } else {
      onSelectionChange(memoizedData as any)
    }
  }

  const handleSelectItem = (item: T): void => {
    if (!onSelectionChange) return

    const isSelected = (selectedItems as any).some(selected => (selected as Record<string, unknown> as any).id === (item as Record<string, unknown>).id)
    if (isSelected) {
      onSelectionChange((selectedItems as any as any).filter(selected => (selected as Record<string, unknown> as any).id !== (item as Record<string, unknown>).id))
    } else {
      onSelectionChange([...selectedItems, item] as any)
    }
  }

  // Handle file import
  // @ts-ignore
  const handleFileImport = (event: (React as any).ChangeEvent<HTMLInputElement>): void => {
    // @ts-ignore
    const file = (event as any).target.files?.[0]
    if (file && onImport) {
      onImport(file as any)
    }
    // Reset input
    (event as any).target.value = ''
  // @ts-ignore
  }

  return (
    <Card className="glass-card border-white/20">

      <CardHeader>
        {/* MOBILE FIX: Enhanced mobile-responsive header */}
        <div className={cn(
          "flex flex-col gap-4",
          isMobile ? "space-y-3" : "sm:flex-row sm:items-center sm:justify-between sm:gap-4"
         // @ts-ignore
         as any)}>
          <CardTitle className="text-white text-xl">{title}</CardTitle>

          {/* MOBILE FIX: Mobile-optimized header actions */}
          <div className={cn(
            "flex items-center gap-2",
            isMobile ? "flex-col space-y-2 w-full" : "flex-wrap"
           // @ts-ignore
           as any)}>
            {onCreate && showCreateButton && (
              <TouchButton
                onClick={onCreate}
                variant="primary"
                size={isMobile ? "lg" : "md"}
                className={cn("glass-button", isMobile ? "w-full" : "" as any)}
                aria-label={createButtonText || `${(t as any).create} ${title || 'item'}`}
              >
                <Plus className="h-4 w-4 mr-2" aria-hidden="true" />
                {createButtonText || (t as any).create}
              </TouchButton>
            )}

            {onRefresh && (
              <Button
                onClick={() => {
                  // UX FIX: Enhanced refresh feedback
                  const loadingId = (uxFeedback as any).loading(
                    language === 'ar' ? 'جاري تحديث البيانات...' : 'Refreshing data...',
                    { expectedDuration: 3000 }
                   // @ts-ignore
                   as any)

                  try {
                    // @ts-ignore
                    onRefresh( as any)
                    // Note: The parent component should call (uxFeedback as any).dismissLoading when done
                  } catch (error) {
                    (uxFeedback as any).dismissLoading(loadingId, 'error',
                      language === 'ar' ? 'فشل في تحديث البيانات' : 'Failed to refresh data'
                     // @ts-ignore
                     as any)
                  }
                }}
                variant="outline"
                className="glass-button"
                disabled={loading}
                aria-label={loading ? 'Refreshing...' : (t as any).refresh}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} aria-hidden="true" />
                {(t as any).refresh}
              </Button>
            )}

            {/* Enhanced Export Button with Custom Handler */}
            {showExportButton && onExport && (
              <div className="relative">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" className="glass-button">
                      <Download className="h-4 w-4 mr-2" />
                      {(t as any).export}
                      <ChevronDown className="h-4 w-4 ml-2" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-48">
                    <DropdownMenuItem onClick={() => onExport('csv' as any)}>
                      <FileText className="h-4 w-4 mr-2" />
                      {language === 'ar' ? 'تصدير CSV' : 'Export CSV'}
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => onExport('excel' as any)}>
                      <FileSpreadsheet className="h-4 w-4 mr-2" />
                      {language === 'ar' ? 'تصدير إكسل' : 'Export Excel'}
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => onExport('pdf' as any)}>
                      <FileImage className="h-4 w-4 mr-2" />
                      {language === 'ar' ? 'تصدير PDF' : 'Export PDF'}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            )}

            {/* Fallback to Unified Export Button if no custom handler */}
            {showExportButton && !onExport && (
              <UnifiedExportButton
                // @ts-ignore
                dataType={(title || 'data').toLowerCase( as any).replace(/\s+/g, '-' as any)}
                data={data}
                className="glass-button"
                variant="outline"
                showAdvanced={true}
                language={language}
              />
            )}

            {onImport && showImportButton && (
              <div>
                <input
                  type="file"
                  id="file-import"
                  className="hidden"
                  accept=".csv,.xlsx,.xls"
                  onChange={handleFileImport}
                />
                <Button
                  // @ts-ignore
                  onClick={() => (document as any).getElementById('file-import' as any)?.click( as any)}
                  variant="outline"
                  className="glass-button"
                  aria-label={`${(t as any).import} ${title || 'data'} file`}
                >
                  <Upload className="h-4 w-4 mr-2" aria-hidden="true" />
                  {(t as any).import}
                </Button>
              </div>
            )}
          </div>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          {onSearchChange && (
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/60" />
                <Input
                  placeholder={searchPlaceholder || (t as any).search}
                  value={searchQuery}
                  onChange={(e: any) => onSearchChange((e as any as any).target.value)}
                  className="glass-input pl-10 pr-10"
                  aria-label={searchPlaceholder || (t as any).search}
                  role="searchbox"
                />
                {/* FIXED: Add clear button */}
                {searchQuery && (
                  <button
                    onClick={() => onSearchChange('' as any)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/60 hover:text-white/80 transition-colors"
                    aria-label="Clear search"
                  >
                    <X className="h-4 w-4" />
                  </button>
                )}
              </div>
            </div>
          )}

          {/* Filter Toggle */}
          {(filters as any).length > 0 && (
            <Button
              onClick={() => setShowFilters(!showFilters as any)}
              variant="outline"
              className="glass-button"
              aria-label={showFilters ? `Hide ${(t as any).filter}` : `Show ${(t as any).filter}`}
              aria-expanded={showFilters}
              aria-controls="filter-panel"
            >
              <Filter className="h-4 w-4 mr-2" aria-hidden="true" />
              {(t as any).filter}
            </Button>
          )}
        </div>

        {/* Filters */}
        {showFilters && (filters as any).length > 0 && (
          <div
            id="filter-panel"
            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 p-4 bg-white/5 rounded-lg"
            role="region"
            aria-label={`${(t as any).filter} ${title || 'data'}`}
          >
            // @ts-ignore
            {(filters as any).map((filter as any) => (
              <div key={(filter as any).key}>
                <label className="text-white text-sm mb-2 block">{(filter as any).label}</label>
                <Select
                  value={activeFilters[(filter as any).key] || 'all'}
                  onValueChange={(value) => {
                    const newValue = value === 'all' ? '' : value
                    onFilterChange?.({ ...activeFilters, [(filter as any).key]: newValue })
                  }}
                >
                  <SelectTrigger className="glass-input">
                    <SelectValue placeholder={`Select ${(filter as any).label}`} />
                  </SelectTrigger>
                  <SelectContent className="glass-card border-white/20">
                    <SelectItem value="all">All</SelectItem>
                    // @ts-ignore
                    {(filter as any).options.map((option as any) => (
                      <SelectItem key={(option as any).value} value={(option as any).value}>
                        {(option as any).label}
                      </SelectItem>
                    // @ts-ignore
                    ))}
                  </SelectContent>
                </Select>
              </div>
            // @ts-ignore
            ))}
          </div>
        )}

        {/* Selection and Bulk Actions */}
        {selectable && (selectedItems as any).length > 0 && (
          <div className="flex items-center justify-between p-3 bg-blue-500/20 rounded-lg">
            <span className="text-white text-sm">
              {(selectedItems as any).length} {(t as any).selected}
            </span>
            <div className="flex items-center gap-2">
              // @ts-ignore
              {(bulkActions as any).map((action, actionIndex as any) => {
                const IconComponent = (action as any).icon
                return (
                  <Button
                    key={actionIndex}
                    onClick={() => (action as any).onClick(selectedItems as unknown as Record<string, unknown> as any)}
                    variant={(action as any).variant || 'outline'}
                    size="sm"
                    className="glass-button"
                  >
                    <IconComponent className="h-4 w-4 mr-2" />
                    {(action as any).label}
                  </Button>
                )
              })}
              <Button
                onClick={() => onSelectionChange?.([])}
                variant="outline"
                size="sm"
                className="glass-button"
              >
                {(t as any).clearSelection}
              </Button>
            </div>
          </div>
        )}
      </CardHeader>

      <CardContent>
        {/* UX FIX: Enhanced loading overlay with better feedback */}
        <LoadingOverlay
          isLoading={loading}
          text={(t as any).loading}
          showNetworkStatus={true}
          preserveHeight={true}
          minHeight={400}
          language={language}
        >
          {loading ? (
            // Enhanced skeleton loading state to prevent CLS
            <div className="space-y-4">
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow className="border-white/20">
                      // @ts-ignore
                      {(columns as any).map((column, index as any) => (
                        <TableHead key={index} className="text-white/80">
                          <div className="h-4 bg-white/10 rounded animate-pulse"></div>
                        </TableHead>
                      ))}
                      {(actions as any).length > 0 && (
                        <TableHead className="text-white/80 w-32">
                          <div className="h-4 bg-white/10 rounded animate-pulse"></div>
                        </TableHead>
                      )}
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    // @ts-ignore
                    {(Array as any).from({ length: (Math as any as any).min(pageSize, 5 as any) }).map((_, index as any) => (
                      <TableRow key={index} className="border-white/10">
                        // @ts-ignore
                        {(columns as any).map((_, colIndex as any) => (
                          <TableCell key={colIndex}>
                            <div className="h-4 bg-white/5 rounded animate-pulse"></div>
                          </TableCell>
                        ))}
                        {(actions as any).length > 0 && (
                          <TableCell>
                            <div className="flex gap-1">
                              // @ts-ignore
                              {(actions as any).slice(0, 3 as any).map((_, actionIndex as any) => (
                                <div key={actionIndex} className="h-8 w-8 bg-white/5 rounded animate-pulse"></div>
                            ))}
                          </div>
                        </TableCell>
                      )}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        ) : (data as any).length === 0 ? (
          <div className="text-center py-8">
            <p className="text-white/60">{(t as any).noData}</p>
          </div>
        ) : (
          <>
            {/* MOBILE FIX: Responsive Table */}
            {isMobile ? (
              // Mobile card layout
              <div className="space-y-4">
                // @ts-ignore
                {(data as any).map((item, index as any) => (
                  <div key={(item as Record<string, unknown>).id as string || index}
                       className="glass-card border-white/20 p-4 space-y-3">
                    {/* Mobile card content */}
                    // @ts-ignore
                    {(columns as any).map((column as any) => (
                      <div key={(column as any).key} className="flex justify-between items-start">
                        <span className="text-white/70 text-sm font-medium min-w-0 flex-1">
                          {(column as any).label}
                        </span>
                        <span className="text-white text-sm text-right ml-2 flex-1">
                          {(column as any).render ? ((column as any).render(item as any) || '-') :
                            (typeof (item as Record<string, unknown>)[(column as any).key] === 'object' && (item as Record<string, unknown>)[(column as any).key] !== null)
                              ? (JSON as any).stringify((item as Record<string, unknown> as any)[(column as any).key])
                              : String((item as Record<string, unknown> as any)[(column as any).key] || '-')
                          }
                        </span>
                      </div>
                    // @ts-ignore
                    ))}

                    {/* Mobile actions */}
                    {(actions as any).length > 0 && (
                      <div className="flex gap-2 pt-2 border-t border-white/10">
                        // @ts-ignore
                        {(actions as any).slice(0, 2 as any).map((action, actionIndex as any) => {
                          if ((action as any).show && !(action as any).show(item as any)) return null
                          const Icon = (action as any).icon
                          return (
                            <TouchButton
                              key={actionIndex}
                              variant="ghost"
                              size="sm"
                              onClick={() => (action as any).onClick(item as any)}
                              className="flex-1 text-white hover:bg-white/10"
                            >
                              <Icon className="h-4 w-4 mr-1" />
                              {(action as any).label}
                            </TouchButton>
                          )
                        })}
                        {(actions as any).length > 2 && (
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <TouchButton variant="ghost" size="sm" className="text-white hover:bg-white/10">
                                <MoreVertical className="h-4 w-4" />
                              </TouchButton>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent className="glass-card border-white/20">
                              // @ts-ignore
                              {(actions as any).slice(2 as any).map((action, actionIndex as any) => {
                                if ((action as any).show && !(action as any).show(item as any)) return null
                                const Icon = (action as any).icon
                                return (
                                  <DropdownMenuItem
                                    key={actionIndex + 2}
                                    onClick={() => (action as any).onClick(item as any)}
                                    className="text-white hover:bg-white/10"
                                  >
                                    <Icon className="h-4 w-4 mr-2" />
                                    {(action as any).label}
                                  </DropdownMenuItem>
                                )
                              })}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        )}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              // Desktop table layout
              <div className="overflow-x-auto">
                <Table role="table" aria-label={`${title || 'Data'} table`}>
                  <TableHeader>
                    <TableRow className="border-white/20">
                    {selectable && (
                      <TableHead className="w-12">
                        <input
                          type="checkbox"
                          checked={(selectedItems as any).length === (data as any).length && (data as any).length > 0}
                          onChange={handleSelectAll}
                          className="rounded border-white/20"
                          aria-label={`Select all ${title || 'items'}`}
                        />
                      </TableHead>
                    )}
                    // @ts-ignore
                    {(columns as any).map((column as any) => (
                      <TableHead
                        key={(column as any).key}
                        className={`text-white ${(column as any).align === 'center' ? 'text-center' : (column as any).align === 'right' ? 'text-right' : 'text-left'}`}
                        style={{ width: (column as any).width }}
                      >
                        {(column as any).sortable ? (
                          <Button
                            variant="ghost"
                            onClick={() => handleSort((column as any as any).key)}
                            className="text-white hover:text-white hover:bg-white/10 p-0 h-auto font-medium"
                            aria-label={`Sort by ${(column as any).label} ${
                              sortBy === (column as any).key
                                ? (sortOrder === 'asc' ? 'descending' : 'ascending')
                                : 'ascending'
                            }`}
                            aria-sort={
                              sortBy === (column as any).key
                                ? (sortOrder === 'asc' ? 'ascending' : 'descending')
                                : 'none'
                            }
                          >
                            {(column as any).label}
                            {sortBy === (column as any).key && (
                              sortOrder === 'asc' ? (
                                <SortAsc className="h-4 w-4 ml-1" aria-hidden="true" />
                              ) : (
                                <SortDesc className="h-4 w-4 ml-1" aria-hidden="true" />
                              )
                            )}
                          </Button>
                        ) : (
                          (column as any).label
                        )}
                      </TableHead>
                    // @ts-ignore
                    ))}
                    {(actions as any).length > 0 && (
                      <TableHead className="text-white text-center w-24">{(t as any).actions}</TableHead>
                    )}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  // @ts-ignore
                  {(data as any).map((item, index as any) => (
                    <TableRow key={(item as Record<string, unknown>).id as string || index} className="border-white/10 hover:bg-white/5">
                      {selectable && (
                        <TableCell>
                          <input
                            type="checkbox"
                            checked={(selectedItems as any).some(selected => (selected as Record<string, unknown> as any).id === (item as Record<string, unknown>).id)}
                            onChange={() => handleSelectItem(item as any)}
                            className="rounded border-white/20"
                          />
                        </TableCell>
                      )}
                      // @ts-ignore
                      {(columns as any).map((column as any) => (
                        <TableCell
                          key={(column as any).key}
                          className={`text-white/90 ${(column as any).align === 'center' ? 'text-center' : (column as any).align === 'right' ? 'text-right' : 'text-left'}`}
                        >
                          {(column as any).render ? ((column as any).render(item as any) || '-') :
                            (typeof (item as Record<string, unknown>)[(column as any).key] === 'object' && (item as Record<string, unknown>)[(column as any).key] !== null)
                              ? (JSON as any).stringify((item as Record<string, unknown> as any)[(column as any).key])
                              : String((item as Record<string, unknown> as any)[(column as any).key] || '-')
                          }
                        </TableCell>
                      // @ts-ignore
                      ))}
                      {(actions as any).length > 0 && (
                        <TableCell className="text-center">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="text-white hover:bg-white/10"
                                aria-label={`Actions for ${
                                  (item as any).name ||
                                  (item as any).title ||
                                  (item as any).firstName ||
                                  'item'
                                }`}
                                aria-haspopup="menu"
                                aria-expanded="false"
                              >
                                <MoreVertical className="h-4 w-4" aria-hidden="true" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent className="glass-card border-white/20">
                              // @ts-ignore
                              {(actions as any).map((action, actionIndex as any) => {
                                if ((action as any).show && !(action as any).show(item as any)) return null

                                const IconComponent = (action as any).icon

                                return (
                                  <DropdownMenuItem
                                    key={actionIndex}
                                    onClick={() => (action as any).onClick(item as any)}
                                    className={`text-white ${(action as any).className || ''} ${(action as any).variant === 'destructive' ? 'text-red-400 hover:text-red-300 hover:bg-red-500/10' : 'hover:bg-white/10'}`}
                                  >
                                    <IconComponent className="h-4 w-4 mr-2" />
                                    {(action as any).label}
                                  </DropdownMenuItem>
                                )
                              })}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      )}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
            )}
            {/* End of responsive table */}

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between mt-4">
                <div className="text-white/60 text-sm">
                  {(t as any).page} {currentPage} {(t as any).of} {totalPages} ({total} {(t as any).items})
                </div>

                <div className="flex items-center gap-2" role="navigation" aria-label="Pagination">
                  <Button
                    onClick={() => onPageChange?.(currentPage - 1)}
                    disabled={currentPage === 1}
                    variant="outline"
                    size="sm"
                    className="glass-button"
                    aria-label={`${(t as any).previous} page (${currentPage - 1})`}
                  >
                    <ChevronLeft className="h-4 w-4" aria-hidden="true" />
                    {(t as any).previous}
                  </Button>

                  <Button
                    onClick={() => onPageChange?.(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    variant="outline"
                    size="sm"
                    className="glass-button"
                    aria-label={`${(t as any).next} page (${currentPage + 1})`}
                  >
                    {(t as any).next}
                    <ChevronRight className="h-4 w-4" aria-hidden="true" />
                  </Button>
                </div>
              </div>
            )}
          </>
        )}
        </LoadingOverlay>
      </CardContent>
    </Card>
  )
// @ts-ignore
}
