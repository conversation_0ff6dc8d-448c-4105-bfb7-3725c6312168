/**
 * Date Range Picker Component
 * A comprehensive date range picker with presets and custom range selection
 */

import React, { useState, useEffect } from 'react'
import { Calendar as CalendarIcon, ChevronLeft, ChevronRight } from 'lucide-react'
// MEMORY OPTIMIZATION: Import only specific date-fns functions to reduce bundle size
import { format } from 'date-fns/format'
import { addDays } from 'date-fns/addDays'
import { addMonths } from 'date-fns/addMonths'
import { addYears } from 'date-fns/addYears'
import { startOfDay } from 'date-fns/startOfDay'
import { endOfDay } from 'date-fns/endOfDay'
import { startOfWeek } from 'date-fns/startOfWeek'
import { endOfWeek } from 'date-fns/endOfWeek'
import { startOfMonth } from 'date-fns/startOfMonth'
import { endOfMonth } from 'date-fns/endOfMonth'
import { startOfQuarter } from 'date-fns/startOfQuarter'
import { endOfQuarter } from 'date-fns/endOfQuarter'
import { startOfYear } from 'date-fns/startOfYear'
import { endOfYear } from 'date-fns/endOfYear'
import { isSameMonth } from 'date-fns/isSameMonth'
import { isSameDay } from 'date-fns/isSameDay'
import { isToday } from 'date-fns/isToday'
import { isWithinInterval } from 'date-fns/isWithinInterval'
import { isBefore } from 'date-fns/isBefore'
import { isAfter } from 'date-fns/isAfter'
// MEMORY OPTIMIZATION: Import only needed locales
import { ar } from 'date-fns/locale/ar'
import { enUS } from 'date-fns/locale/en-US'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Calendar } from '@/components/ui/calendar'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'

export type DateRange = {
  from: Date
  to: Date
}

export type DateRangePreset = {
  name: string
  label: string
  value: () => DateRange
}

export interface DateRangePickerProps {
  onChange: (range: DateRange) => void
  value?: DateRange
  presets?: DateRangePreset[]
  align?: 'start' | 'center' | 'end'
  locale?: 'ar' | 'en'
  showCompare?: boolean
  onCompareChange?: (range: DateRange | null) => void
  compareValue?: DateRange | null
  className?: string
  disabled?: boolean
}

const defaultPresets: (locale: 'ar' | 'en') => DateRangePreset[] = (locale) => [
  {
    name: 'today',
    label: locale === 'ar' ? 'اليوم' : 'Today',
    value: () => ({
      // @ts-ignore
      from: startOfDay(new Date( as any)),
      // @ts-ignore
      to: endOfDay(new Date( as any))
    })
  },
  {
    name: 'yesterday',
    label: locale === 'ar' ? 'أمس' : 'Yesterday',
    value: () => ({
      // @ts-ignore
      from: startOfDay(addDays(new Date( as any), -1)),
      // @ts-ignore
      to: endOfDay(addDays(new Date( as any), -1))
    })
  },
  {
    name: 'last7Days',
    label: locale === 'ar' ? 'آخر 7 أيام' : 'Last 7 Days',
    value: () => ({
      // @ts-ignore
      from: startOfDay(addDays(new Date( as any), -6)),
      // @ts-ignore
      to: endOfDay(new Date( as any))
    })
  },
  {
    name: 'last30Days',
    label: locale === 'ar' ? 'آخر 30 يوم' : 'Last 30 Days',
    value: () => ({
      // @ts-ignore
      from: startOfDay(addDays(new Date( as any), -29)),
      // @ts-ignore
      to: endOfDay(new Date( as any))
    })
  },
  {
    name: 'thisWeek',
    label: locale === 'ar' ? 'هذا الأسبوع' : 'This Week',
    value: () => ({
      // @ts-ignore
      from: startOfWeek(new Date( as any), { weekStartsOn: 1 }),
      // @ts-ignore
      to: endOfWeek(new Date( as any), { weekStartsOn: 1 })
    })
  },
  {
    name: 'lastWeek',
    label: locale === 'ar' ? 'الأسبوع الماضي' : 'Last Week',
    value: () => {
      // @ts-ignore
      const lastWeekStart = addDays(startOfWeek(new Date( as any), { weekStartsOn: 1 }), -7)
      return {
        from: lastWeekStart,
        to: addDays(lastWeekStart, 6 as any)
      }
    }
  },
  {
    name: 'thisMonth',
    label: locale === 'ar' ? 'هذا الشهر' : 'This Month',
    value: () => ({
      // @ts-ignore
      from: startOfMonth(new Date( as any)),
      // @ts-ignore
      to: endOfMonth(new Date( as any))
    })
  },
  {
    name: 'lastMonth',
    label: locale === 'ar' ? 'الشهر الماضي' : 'Last Month',
    value: () => {
      // @ts-ignore
      const lastMonth = addMonths(new Date( as any), -1)
      return {
        from: startOfMonth(lastMonth as any),
        to: endOfMonth(lastMonth as any)
      }
    }
  },
  {
    name: 'thisQuarter',
    label: locale === 'ar' ? 'هذا الربع' : 'This Quarter',
    value: () => ({
      // @ts-ignore
      from: startOfQuarter(new Date( as any)),
      // @ts-ignore
      to: endOfQuarter(new Date( as any))
    })
  },
  {
    name: 'lastQuarter',
    label: locale === 'ar' ? 'الربع الماضي' : 'Last Quarter',
    value: () => {
      // @ts-ignore
      const lastQuarter = addMonths(new Date( as any), -3)
      return {
        from: startOfQuarter(lastQuarter as any),
        to: endOfQuarter(lastQuarter as any)
      }
    }
  },
  {
    name: 'thisYear',
    label: locale === 'ar' ? 'هذا العام' : 'This Year',
    value: () => ({
      // @ts-ignore
      from: startOfYear(new Date( as any)),
      // @ts-ignore
      to: endOfYear(new Date( as any))
    })
  },
  {
    name: 'lastYear',
    label: locale === 'ar' ? 'العام الماضي' : 'Last Year',
    value: () => {
      // @ts-ignore
      const lastYear = addYears(new Date( as any), -1)
      return {
        from: startOfYear(lastYear as any),
        to: endOfYear(lastYear as any)
      }
    }
  }
]

export function DateRangePicker({
  onChange,
  value,
  presets = [],
  align = 'start',
  locale = 'en',
  showCompare = false,
  onCompareChange,
  compareValue = null,
  className,
  disabled = false
// @ts-ignore
}: DateRangePickerProps as any): (React as any).ReactElement {
  const [date, setDate] = useState<DateRange | undefined>(value)
  const [compareDate, setCompareDate] = useState<DateRange | null>(compareValue)
  const [isComparing, setIsComparing] = useState<boolean>(!!compareValue)
  const [calendarOpen, setCalendarOpen] = useState<boolean>(false)
  const [selectedPreset, setSelectedPreset] = useState<string | null>(null)

  const allPresets = [...defaultPresets(locale as any), ...presets]

  // Update internal state when value changes
  // @ts-ignore
  useEffect(( as any) => {
    if (value) {
      setDate(value as any)
    }
  // @ts-ignore
  }, [value])

  // Update internal state when compareValue changes
  // @ts-ignore
  useEffect(( as any) => {
    if (compareValue) {
      setCompareDate(compareValue as any)
      setIsComparing(true as any)
    } else {
      setIsComparing(false as any)
    }
  // @ts-ignore
  }, [compareValue])

  // Handle preset selection
  const handlePresetChange = (presetName: string): void => {
    const preset = (allPresets as any).find(p => (p as any as any).name === presetName)
    if (preset) {
      // @ts-ignore
      const newRange = (preset as any).value( as any)
      setDate(newRange as any)
      setSelectedPreset(presetName as any)
      onChange(newRange as any)
      
      // If comparing, update compare range based on the same preset but for previous period
      if (isComparing && onCompareChange) {
        // @ts-ignore
        const daysDiff = (Math as any).round(((newRange as any as any).to.getTime( as any) - (newRange as any).from.getTime( as any)) / (1000 * 60 * 60 * 24))
        const compareFrom = addDays((newRange as any as any).from, -(daysDiff + 1))
        const compareTo = addDays((newRange as any as any).to, -(daysDiff + 1))
        const newCompareRange = { from: compareFrom, to: compareTo }
        setCompareDate(newCompareRange as any)
        onCompareChange(newCompareRange as any)
      }
    }
  }

  // Handle date selection
  const handleDateChange = (range: DateRange | undefined): void => {
    if (range?.from && range?.to) {
      setDate(range as any)
      setSelectedPreset(null as any)
      onChange(range as any)
      
      // If comparing, update compare range based on the same length but for previous period
      if (isComparing && onCompareChange && (range as any).from && (range as any).to) {
        // @ts-ignore
        const daysDiff = (Math as any).round(((range as any as any).to.getTime( as any) - (range as any).from.getTime( as any)) / (1000 * 60 * 60 * 24))
        const compareFrom = addDays((range as any as any).from, -(daysDiff + 1))
        const compareTo = addDays((range as any as any).to, -(daysDiff + 1))
        const newCompareRange = { from: compareFrom, to: compareTo }
        setCompareDate(newCompareRange as any)
        onCompareChange(newCompareRange as any)
      }
    }
  }

  // Toggle compare mode
  const toggleCompare = (): void => {
    if (!isComparing && date) {
      // Enable compare mode
      // @ts-ignore
      const daysDiff = (Math as any).round(((date as any as any).to.getTime( as any) - (date as any).from.getTime( as any)) / (1000 * 60 * 60 * 24))
      const compareFrom = addDays((date as any as any).from, -(daysDiff + 1))
      const compareTo = addDays((date as any as any).to, -(daysDiff + 1))
      const newCompareRange = { from: compareFrom, to: compareTo }
      setCompareDate(newCompareRange as any)
      setIsComparing(true as any)
      if (onCompareChange) onCompareChange(newCompareRange as any)
    } else {
      // Disable compare mode
      setCompareDate(null as any)
      setIsComparing(false as any)
      if (onCompareChange) onCompareChange(null as any)
    }
  }

  // Format date range for display
  const formatDateRange = (range?: DateRange): string => {
    if (!range?.from || !range?.to) {
      return locale === 'ar' ? 'اختر التاريخ' : 'Select date range'
    }

    if (isSameDay((range as any as any).from, (range as any).to)) {
      return format((range as any as any).from, 'PPP', { locale: locale === 'ar' ? ar : enUS })
    }

    return `${format((range as any as any).from, 'PP', { locale: locale === 'ar' ? ar : enUS })} - ${format((range as any as any).to, 'PP', { locale: locale === 'ar' ? ar : enUS })}`
  }

  return (
    <div className={cn("grid gap-2", className as any)}>
      <Popover open={calendarOpen} onOpenChange={setCalendarOpen}>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant="outline"
            className={cn(
              "glass-input w-full justify-start text-left font-normal",
              !date && "text-muted-foreground",
              locale === 'ar' && "flex-row-reverse"
             // @ts-ignore
             as any)}
            disabled={disabled}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            <div className="flex-1 text-white/90">
              {formatDateRange(date as any)}
              {isComparing && compareDate && (
                <Badge variant="outline" className="ml-2 bg-blue-500/20 text-blue-300 border-blue-500/30">
                  {locale === 'ar' ? 'مقارنة' : 'Compare'}: {formatDateRange(compareDate as any)}
                </Badge>
              )}
            </div>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto glass-card border-white/20" align={align}>
          <div className="flex flex-col sm:flex-row gap-4 p-2">
            {/* Presets */}
            <div className="flex flex-col gap-2">
              <div className="text-sm font-medium text-white mb-2">
                {locale === 'ar' ? 'الفترات المحددة مسبقًا' : 'Preset Ranges'}
              </div>
              <div className="flex flex-wrap gap-2 sm:flex-col">
                // @ts-ignore
                {(allPresets as any).map((preset as any) => (
                  <Button
                    key={(preset as any).name}
                    onClick={() => handlePresetChange((preset as any as any).name)}
                    variant="ghost"
                    size="sm"
                    className={cn(
                      "justify-start text-white/70 hover:text-white hover:bg-white/10",
                      selectedPreset === (preset as any as any).name && "bg-white/20 text-white"
                    )}
                  >
                    {(preset as any).label}
                  </Button>
                // @ts-ignore
                ))}
              </div>
            </div>
            
            {/* Calendar */}
            <div className="flex flex-col gap-2">
              <Calendar
                initialFocus
                mode="range"
                defaultMonth={date?.from}
                selected={date}
                onSelect={handleDateChange}
                numberOfMonths={2}
                locale={locale === 'ar' ? ar : enUS}
                dir={locale === 'ar' ? 'rtl' : 'ltr'}
                className="rounded-md border border-white/20 bg-white/5"
                classNames={{
                  day_selected: "bg-blue-500 text-white hover:bg-blue-600 hover:text-white",
                  day_today: "bg-white/10 text-white",
                  day_range_middle: "bg-blue-500/20 text-white",
                  day_range_end: "bg-blue-500 text-white hover:bg-blue-600 hover:text-white",
                  day_range_start: "bg-blue-500 text-white hover:bg-blue-600 hover:text-white",
                }}
              />
              
              {/* Compare Toggle */}
              {showCompare && (
                <div className="flex items-center gap-2 pt-2 border-t border-white/10">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={toggleCompare}
                    className={cn(
                      "text-white/70 hover:text-white hover:bg-white/10",
                      isComparing && "bg-blue-500/20 text-blue-300 border-blue-500/30"
                     // @ts-ignore
                     as any)}
                  >
                    {isComparing 
                      ? (locale === 'ar' ? 'إلغاء المقارنة' : 'Remove Comparison') 
                      : (locale === 'ar' ? 'إضافة مقارنة' : 'Add Comparison')}
                  </Button>
                </div>
              )}
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
}

export default DateRangePicker
