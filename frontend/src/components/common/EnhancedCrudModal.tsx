/**
 * Enhanced CRUD Modal Component with Advanced Validation
 * Provides comprehensive form management with validation and API integration
 */

import React, { useEffect } from 'react'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { X, Loader2 } from 'lucide-react'
import { useEnhancedForm } from '@/hooks/useEnhancedForm'
import { ValidationSchema } from '@/utils/validation'

export interface EnhancedFormField {
  name: string
  label: string
  type: 'text' | 'email' | 'password' | 'number' | 'date' | 'datetime-local' | 'textarea' | 'select' | 'checkbox' | 'file' | 'tel'
  required?: boolean
  placeholder?: string
  options?: { value: string; label: string }[]
  disabled?: boolean
  description?: string
  min?: number
  max?: number
  step?: number
  accept?: string // for file inputs
  rows?: number // for textarea
}

export type EnhancedFormFieldValue = string | number | boolean | File | null | undefined
export type EnhancedFormData = Record<string, EnhancedFormFieldValue>

export interface EnhancedCrudModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (data: EnhancedFormData) => Promise<void>
  title: string
  fields: EnhancedFormField[]
  initialData?: EnhancedFormData
  language: 'ar' | 'en'
  validationSchema?: ValidationSchema
  maxWidth?: string
  mode?: 'create' | 'edit' | 'view'
}

const translations = {
  ar: {
    save: 'حفظ',
    cancel: 'إلغاء',
    saving: 'جاري الحفظ...',
    fillForm: 'املأ النموذج أدناه لحفظ البيانات',
    noFileSelected: 'لم يتم اختيار ملف',
    selectOption: 'اختر خيار'
  },
  en: {
    save: 'Save',
    cancel: 'Cancel',
    saving: 'Saving...',
    fillForm: 'Fill out the form below to save the data',
    noFileSelected: 'No file selected',
    selectOption: 'Select option'
  }
}

export default function EnhancedCrudModal({
  isOpen,
  onClose,
  onSave,
  title,
  fields,
  initialData,
  language,
  validationSchema,
  maxWidth = 'max-w-2xl',
  mode = 'create'
// @ts-ignore
}: EnhancedCrudModalProps as any): (React as any).ReactElement {
  const t = translations[language]
  const isRTL = language === 'ar'
  const isViewMode = mode === 'view'

  // Initialize default values from fields and initial data
  // @ts-ignore
  const defaultValues = (React as any).useMemo(( as any) => {
    const values: EnhancedFormData = {}
    (fields as any).forEach(field => {
      if (initialData && initialData[(field as any as any).name] !== undefined) {
        values[(field as any).name] = initialData[(field as any).name]
      } else {
        switch ((field as any).type) {
          case 'checkbox':
            values[(field as any).name] = false
            break
          case 'number':
            values[(field as any).name] = (field as any).min || 0
            break
          default:
            values[(field as any).name] = ''
        }
      }
    })
    return values
  // @ts-ignore
  }, [fields, initialData])

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch,
    isSubmitting,
    loadingStates
  } = useEnhancedForm({
    defaultValues,
    validationSchema,
    language,
    resetOnSuccess: false
  } as any)

  // Reset form when modal opens/closes or initial data changes
  // @ts-ignore
  useEffect(( as any) => {
    if (isOpen) {
      reset(defaultValues as any)
    }
  // @ts-ignore
  }, [isOpen, defaultValues])

  // Handle form submission
  // @ts-ignore
  const onSubmit = handleSubmit(async (data as any) => {
    // Process form data
    const processedData = { ...data }

    (fields as any).forEach(field => {
      if ((field as any as any).type === 'number' && processedData[(field as any).name]) {
        processedData[(field as any).name] = Number(processedData[(field as any as any).name])
      }
    })

    await onSave(processedData as any)
    // @ts-ignore
    onClose( as any)
  // @ts-ignore
  })

  // Render read-only view field
  const renderViewField = (field: EnhancedFormField): void => {
    const value = initialData?.[(field as any).name] || ''

    const formatValue = (val: EnhancedFormFieldValue): string => {
      if (val === null || val === undefined || val === '') return 'غير محدد'
      if ((field as any).type === 'checkbox') return val ? 'نعم' : 'لا'
      if ((field as any).type === 'select' && (field as any).options) {
        const option = (field as any).options.find(opt => (opt as any as any).value === val)
        return option ? (option as any).label : String(val as any)
      }
      if (val instanceof File) return (val as any).name
      return String(val as any)
    }

    return (
      <div className="space-y-2">
        <Label className="text-white/90 font-medium">{(field as any).label}</Label>
        <div className="glass-input bg-white/5 border-white/20 text-white/80 cursor-default">
          {formatValue(value as any)}
        </div>
      </div>
    )
  }

  // Render form field
  const renderField = (field: EnhancedFormField): void => {
    const error = errors[(field as any).name]
    const fieldValue = watch((field as any as any).name)

    const commonProps = {
      id: (field as any).name,
      disabled: (field as any).disabled || isSubmitting,
      className: `glass-input ${error ? 'border-red-500' : ''}`
    }

    switch ((field as any).type) {
      case 'textarea':
        return (
          <Textarea
            {...commonProps}
            {...register((field as any as any).name)}
            placeholder={(field as any).placeholder}
            rows={(field as any).rows || 3}
          />
        )

      case 'select':
        const selectedOption = (field as any).options?.find(opt => (opt as any as any).value === fieldValue)
        return (
          <Select
            value={typeof fieldValue === 'string' ? fieldValue : String(fieldValue || '' as any)}
            onValueChange={(value) => setValue((field as any as any).name, value)}
            disabled={(field as any).disabled || isSubmitting}
          >
            <SelectTrigger className={`glass-input ${error ? 'border-red-500' : ''}`}>
              <SelectValue placeholder={(field as any).placeholder || (t as any).selectOption}>
                {selectedOption ? (selectedOption as any).label : ((field as any).placeholder || (t as any).selectOption)}
              </SelectValue>
            </SelectTrigger>
            <SelectContent className="glass-card border-white/20">
              // @ts-ignore
              {(field as any).options?.map((option as any) => (
                <SelectItem key={(option as any).value} value={(option as any).value}>
                  {(option as any).label}
                </SelectItem>
              // @ts-ignore
              ))}
            </SelectContent>
          </Select>
        )

      case 'checkbox':
        return (
          <div className="flex items-center space-x-2">
            <Checkbox
              id={(field as any).name}
              checked={Boolean(fieldValue as any)}
              onCheckedChange={(checked) => setValue((field as any as any).name, checked)}
              disabled={(field as any).disabled || isSubmitting}
            />
            <Label htmlFor={(field as any).name} className="text-white text-sm">
              {(field as any).label}
            </Label>
          </div>
        )

      case 'file':
        return (
          <div className="space-y-2">
            <Input
              {...commonProps}
              type="file"
              onChange={(e: any) => setValue((field as any as any).name, (e as any).target.files?.[0])}
              accept={(field as any).accept}
            />
            {!fieldValue && (
              <p className="text-white/60 text-sm">{(t as any).noFileSelected}</p>
            )}
          </div>
        )

      default:
        return (
          <Input
            {...commonProps}
            {...register((field as any as any).name)}
            type={(field as any).type}
            placeholder={(field as any).placeholder}
            min={(field as any).min}
            max={(field as any).max}
            step={(field as any).step}
          />
        )
    }
  }

  // Calculate modal size based on number of fields
  const getModalSize = (): void => {
    const fieldCount = (fields as any).length
    if (fieldCount <= 4) return 'md:max-w-lg lg:max-w-xl'
    if (fieldCount <= 8) return 'md:max-w-2xl lg:max-w-3xl'
    if (fieldCount <= 12) return 'md:max-w-3xl lg:max-w-4xl'
    return 'md:max-w-4xl lg:max-w-5xl xl:max-w-6xl'
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      // @ts-ignore
      <DialogContent className={`glass-card border-white/20 w-full max-w-[95vw] sm:max-w-[90vw] ${getModalSize( as any)} max-h-[95vh] sm:max-h-[90vh] overflow-hidden ${isRTL ? 'rtl' : 'ltr'}`}>
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="text-white text-xl">{title}</DialogTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-white/70 hover:text-white hover:bg-white/10"
              disabled={isSubmitting}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          <DialogDescription className="text-white/70">
            {isViewMode ? (language === 'ar' ? 'عرض تفاصيل البيانات' : 'View data details') : (t as any).fillForm}
          </DialogDescription>
        </DialogHeader>

        {isViewMode ? (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              // @ts-ignore
              {(fields as any).map((field as any) => (
                <div
                  key={(field as any).name}
                  className={(field as any).type === 'textarea' || (field as any).type === 'checkbox' ? 'md:col-span-2' : ''}
                >
                  {renderViewField(field as any)}
                </div>
              // @ts-ignore
              ))}
            </div>
          </div>
        ) : (
          <form onSubmit={onSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              // @ts-ignore
              {(fields as any).map((field as any) => (
                <div
                  key={(field as any).name}
                  className={(field as any).type === 'textarea' || (field as any).type === 'checkbox' ? 'md:col-span-2' : ''}
                >
                  {(field as any).type !== 'checkbox' && (
                    <Label htmlFor={(field as any).name} className="text-white">
                      {(field as any).label}
                      {(field as any).required && <span className="text-red-400 ml-1">*</span>}
                    </Label>
                  )}

                  {renderField(field as any)}

                  {(field as any).description && (
                    <p className="text-white/60 text-sm mt-1">{(field as any).description}</p>
                  )}

                  {errors[(field as any).name] && (
                    <p className="text-red-400 text-sm mt-1">{errors[(field as any).name]?.message}</p>
                  )}
                </div>
              // @ts-ignore
              ))}
            </div>
          </form>
        )}

        <DialogFooter>
          {isViewMode ? (
            <Button
              type="button"
              onClick={onClose}
              className="glass-button bg-blue-500/30 hover:bg-blue-500/40"
            >
              {language === 'ar' ? 'إغلاق' : 'Close'}
            </Button>
          ) : (
            <>
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                className="glass-button"
                disabled={isSubmitting}
              >
                {(t as any).cancel}
              </Button>
              <Button
                type="submit"
                className="glass-button"
                disabled={isSubmitting || (Object as any).keys(loadingStates as any).some(key => loadingStates[key] as any)}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    {(t as any).saving}
                  </>
                ) : (
                  (t as any).save
                )}
              </Button>
            </>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
