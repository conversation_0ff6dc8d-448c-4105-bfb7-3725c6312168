/**
 * Enhanced CRUD Modal Component with Advanced Validation
 * Provides comprehensive form management with validation and API integration
 */

import React, { useEffect } from 'react'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { X, Loader2 } from 'lucide-react'
import { useEnhancedForm } from '@/hooks/useEnhancedForm'
import { ValidationSchema } from '@/utils/validation'

export interface EnhancedFormField {
  name: string
  label: string
  type: 'text' | 'email' | 'password' | 'number' | 'date' | 'datetime-local' | 'textarea' | 'select' | 'checkbox' | 'file' | 'tel'
  required?: boolean
  placeholder?: string
  options?: { value: string; label: string }[]
  disabled?: boolean
  description?: string
  min?: number
  max?: number
  step?: number
  accept?: string // for file inputs
  rows?: number // for textarea
}

export type EnhancedFormFieldValue = string | number | boolean | File | null | undefined
export type EnhancedFormData = Record<string, EnhancedFormFieldValue>

export interface EnhancedCrudModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (data: EnhancedFormData) => Promise<void>
  title: string
  fields: EnhancedFormField[]
  initialData?: EnhancedFormData
  language: 'ar' | 'en'
  validationSchema?: ValidationSchema
  maxWidth?: string
  mode?: 'create' | 'edit' | 'view'
}

const translations = {
  ar: {
    save: 'حفظ',
    cancel: 'إلغاء',
    saving: 'جاري الحفظ...',
    fillForm: 'املأ النموذج أدناه لحفظ البيانات',
    noFileSelected: 'لم يتم اختيار ملف',
    selectOption: 'اختر خيار'
  },
  en: {
    save: 'Save',
    cancel: 'Cancel',
    saving: 'Saving...',
    fillForm: 'Fill out the form below to save the data',
    noFileSelected: 'No file selected',
    selectOption: 'Select option'
  }
}

export default function EnhancedCrudModal({
  isOpen,
  onClose,
  onSave,
  title,
  fields,
  initialData,
  language,
  validationSchema,
  maxWidth = 'max-w-2xl',
  mode = 'create'
}: EnhancedCrudModalProps): React.ReactElement {
  const t = translations[language]
  const isRTL = language === 'ar'
  const isViewMode = mode === 'view'

  // Initialize default values from fields and initial data
  const defaultValues = React.useMemo(() => {
    const values: EnhancedFormData = {}
    fields.forEach(field => {
      if (initialData && initialData[field.name] !== undefined) {
        values[field.name] = initialData[field.name]
      } else {
        switch (field.type) {
          case 'checkbox':
            values[field.name] = false
            break
          case 'number':
            values[field.name] = field.min || 0
            break
          default:
            values[field.name] = ''
        }
      }
    })
    return values
  }, [fields, initialData])

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch,
    isSubmitting,
    loadingStates
  } = useEnhancedForm({
    defaultValues,
    validationSchema,
    language,
    resetOnSuccess: false
  })

  // Reset form when modal opens/closes or initial data changes
  useEffect(() => {
    if (isOpen) {
      reset(defaultValues)
    }
  }, [isOpen, defaultValues])

  // Handle form submission
  const onSubmit = handleSubmit(async (data) => {
    // Process form data
    const processedData = { ...data }

    fields.forEach(field => {
      if (field.type === 'number' && processedData[field.name]) {
        processedData[field.name] = Number(processedData[field.name])
      }
    })

    await onSave(processedData)
    onClose()
  })

  // Render read-only view field
  const renderViewField = (field: EnhancedFormField): void => {
    const value = initialData?.[field.name] || ''

    const formatValue = (val: EnhancedFormFieldValue): string => {
      if (val === null || val === undefined || val === '') return 'غير محدد'
      if (field.type === 'checkbox') return val ? 'نعم' : 'لا'
      if (field.type === 'select' && field.options) {
        const option = field.options.find(opt => opt.value === val)
        return option ? option.label : String(val)
      }
      if (val instanceof File) return val.name
      return String(val)
    }

    return (
      <div className="space-y-2">
        <Label className="text-white/90 font-medium">{field.label}</Label>
        <div className="glass-input bg-white/5 border-white/20 text-white/80 cursor-default">
          {formatValue(value)}
        </div>
      </div>
    )
  }

  // Render form field
  const renderField = (field: EnhancedFormField): void => {
    const error = errors[field.name]
    const fieldValue = watch(field.name)

    const commonProps = {
      id: field.name,
      disabled: field.disabled || isSubmitting,
      className: `glass-input ${error ? 'border-red-500' : ''}`
    }

    switch (field.type) {
      case 'textarea':
        return (
          <Textarea
            {...commonProps}
            {...register(field.name)}
            placeholder={field.placeholder}
            rows={field.rows || 3}
          />
        )

      case 'select':
        const selectedOption = field.options?.find(opt => opt.value === fieldValue)
        return (
          <Select
            value={typeof fieldValue === 'string' ? fieldValue : String(fieldValue || '')}
            onValueChange={(value) => setValue(field.name, value)}
            disabled={field.disabled || isSubmitting}
          >
            <SelectTrigger className={`glass-input ${error ? 'border-red-500' : ''}`}>
              <SelectValue placeholder={field.placeholder || t.selectOption}>
                {selectedOption ? selectedOption.label : (field.placeholder || t.selectOption)}
              </SelectValue>
            </SelectTrigger>
            <SelectContent className="glass-card border-white/20">
              {field.options?.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )

      case 'checkbox':
        return (
          <div className="flex items-center space-x-2">
            <Checkbox
              id={field.name}
              checked={Boolean(fieldValue)}
              onCheckedChange={(checked) => setValue(field.name, checked)}
              disabled={field.disabled || isSubmitting}
            />
            <Label htmlFor={field.name} className="text-white text-sm">
              {field.label}
            </Label>
          </div>
        )

      case 'file':
        return (
          <div className="space-y-2">
            <Input
              {...commonProps}
              type="file"
              onChange={(e) => setValue(field.name, e.target.files?.[0])}
              accept={field.accept}
            />
            {!fieldValue && (
              <p className="text-white/60 text-sm">{t.noFileSelected}</p>
            )}
          </div>
        )

      default:
        return (
          <Input
            {...commonProps}
            {...register(field.name)}
            type={field.type}
            placeholder={field.placeholder}
            min={field.min}
            max={field.max}
            step={field.step}
          />
        )
    }
  }

  // Calculate modal size based on number of fields
  const getModalSize = (): void => {
    const fieldCount = fields.length
    if (fieldCount <= 4) return 'md:max-w-lg lg:max-w-xl'
    if (fieldCount <= 8) return 'md:max-w-2xl lg:max-w-3xl'
    if (fieldCount <= 12) return 'md:max-w-3xl lg:max-w-4xl'
    return 'md:max-w-4xl lg:max-w-5xl xl:max-w-6xl'
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className={`glass-card border-white/20 w-full max-w-[95vw] sm:max-w-[90vw] ${getModalSize()} max-h-[95vh] sm:max-h-[90vh] overflow-hidden ${isRTL ? 'rtl' : 'ltr'}`}>
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="text-white text-xl">{title}</DialogTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-white/70 hover:text-white hover:bg-white/10"
              disabled={isSubmitting}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          <DialogDescription className="text-white/70">
            {isViewMode ? (language === 'ar' ? 'عرض تفاصيل البيانات' : 'View data details') : t.fillForm}
          </DialogDescription>
        </DialogHeader>

        {isViewMode ? (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {fields.map((field) => (
                <div
                  key={field.name}
                  className={field.type === 'textarea' || field.type === 'checkbox' ? 'md:col-span-2' : ''}
                >
                  {renderViewField(field)}
                </div>
              ))}
            </div>
          </div>
        ) : (
          <form onSubmit={onSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {fields.map((field) => (
                <div
                  key={field.name}
                  className={field.type === 'textarea' || field.type === 'checkbox' ? 'md:col-span-2' : ''}
                >
                  {field.type !== 'checkbox' && (
                    <Label htmlFor={field.name} className="text-white">
                      {field.label}
                      {field.required && <span className="text-red-400 ml-1">*</span>}
                    </Label>
                  )}

                  {renderField(field)}

                  {field.description && (
                    <p className="text-white/60 text-sm mt-1">{field.description}</p>
                  )}

                  {errors[field.name] && (
                    <p className="text-red-400 text-sm mt-1">{errors[field.name]?.message}</p>
                  )}
                </div>
              ))}
            </div>
          </form>
        )}

        <DialogFooter>
          {isViewMode ? (
            <Button
              type="button"
              onClick={onClose}
              className="glass-button bg-blue-500/30 hover:bg-blue-500/40"
            >
              {language === 'ar' ? 'إغلاق' : 'Close'}
            </Button>
          ) : (
            <>
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                className="glass-button"
                disabled={isSubmitting}
              >
                {t.cancel}
              </Button>
              <Button
                type="submit"
                className="glass-button"
                disabled={isSubmitting || Object.keys(loadingStates).some(key => loadingStates[key])}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    {t.saving}
                  </>
                ) : (
                  t.save
                )}
              </Button>
            </>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
