/**
 * Enhanced Toast Notification System
 * Provides comprehensive user feedback with accessibility support
 */

import React, { useState, useEffect } from 'react'
import { X, CheckCircle, AlertCircle, AlertTriangle, Info, Loader2 } from 'lucide-react'
import { Button } from '@/components/ui/button'

export type ToastType = 'success' | 'error' | 'warning' | 'info' | 'loading'

export interface ToastProps {
  id: string
  type: ToastType
  title: string
  message?: string
  duration?: number
  persistent?: boolean
  action?: {
    label: string
    onClick: () => void
  }
  onClose?: () => void
  language?: 'ar' | 'en'
}

interface ToastContainerProps {
  toasts: ToastProps[]
  onRemove: (id: string) => void
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center'
  language?: 'ar' | 'en'
}

const translations = {
  ar: {
    close: 'إغلاق',
    success: 'نجح',
    error: 'خطأ',
    warning: 'تحذير',
    info: 'معلومات',
    loading: 'جاري التحميل'
  },
  en: {
    close: 'Close',
    success: 'Success',
    error: 'Error',
    warning: 'Warning',
    info: 'Info',
    loading: 'Loading'
  }
}

const toastIcons = {
  success: CheckCircle,
  error: AlertCircle,
  warning: AlertTriangle,
  info: Info,
  loading: Loader2
}

const toastStyles = {
  success: {
    container: 'bg-green-500/20 border-green-500/50 text-green-100',
    icon: 'text-green-400',
    progress: 'bg-green-400'
  },
  error: {
    container: 'bg-red-500/20 border-red-500/50 text-red-100',
    icon: 'text-red-400',
    progress: 'bg-red-400'
  },
  warning: {
    container: 'bg-yellow-500/20 border-yellow-500/50 text-yellow-100',
    icon: 'text-yellow-400',
    progress: 'bg-yellow-400'
  },
  info: {
    container: 'bg-blue-500/20 border-blue-500/50 text-blue-100',
    icon: 'text-blue-400',
    progress: 'bg-blue-400'
  },
  loading: {
    container: 'bg-purple-500/20 border-purple-500/50 text-purple-100',
    icon: 'text-purple-400',
    progress: 'bg-purple-400'
  }
}

const positionClasses = {
  'top-right': 'top-4 right-4',
  'top-left': 'top-4 left-4',
  'bottom-right': 'bottom-4 right-4',
  'bottom-left': 'bottom-4 left-4',
  'top-center': 'top-4 left-1/2 transform -translate-x-1/2',
  'bottom-center': 'bottom-4 left-1/2 transform -translate-x-1/2'
}

export const Toast: React.FC<ToastProps> = ({
  id,
  type,
  title,
  message,
  duration = 5000,
  persistent = false,
  action,
  onClose,
  language = 'en'
}) => {
  const [isVisible, setIsVisible] = useState(true)
  const [progress, setProgress] = useState(100)
  const t = translations[language]
  const isRTL = language === 'ar'

  const Icon = toastIcons[type]
  const styles = toastStyles[type]

  useEffect(() => {
    if (persistent || type === 'loading') return

    const timer = setTimeout(() => {
      handleClose()
    }, duration)

    // Progress bar animation
    const progressTimer = setInterval(() => {
      setProgress(prev => {
        const newProgress = prev - (100 / (duration / 100))
        return newProgress <= 0 ? 0 : newProgress
      })
    }, 100)

    return () => {
      clearTimeout(timer)
      clearInterval(progressTimer)
    }
  }, [duration, persistent, type])

  const handleClose = (): void => {
    setIsVisible(false)
    setTimeout(() => {
      onClose?.()
    }, 300) // Wait for animation to complete
  }

  if (!isVisible) return null

  return (
    <div
      className={`
        relative overflow-hidden rounded-lg border backdrop-blur-lg p-4 shadow-lg
        transition-all duration-300 ease-in-out transform
        ${isVisible ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'}
        ${styles.container}
        ${isRTL ? 'rtl' : 'ltr'}
        max-w-sm w-full
      `}
      role="alert"
      aria-live={type === 'error' ? 'assertive' : 'polite'}
      aria-atomic="true"
    >
      {/* Progress bar */}
      {!persistent && type !== 'loading' && (
        <div className="absolute bottom-0 left-0 h-1 bg-white/20 w-full">
          <div
            className={`h-full transition-all duration-100 ease-linear ${styles.progress}`}
            style={{ width: `${progress}%` }}
          />
        </div>
      )}

      <div className="flex items-start space-x-3">
        {/* Icon */}
        <div className={`flex-shrink-0 ${styles.icon}`}>
          <Icon 
            className={`h-5 w-5 ${type === 'loading' ? 'animate-spin' : ''}`}
            aria-hidden="true"
          />
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h4 className="font-medium text-sm">{title}</h4>
              {message && (
                <p className="mt-1 text-sm opacity-90">{message}</p>
              )}
            </div>

            {/* Close button */}
            <button
              onClick={handleClose}
              className="flex-shrink-0 ml-2 p-1 rounded-md hover:bg-white/10 transition-colors"
              aria-label={t.close}
            >
              <X className="h-4 w-4" />
            </button>
          </div>

          {/* Action button */}
          {action && (
            <div className="mt-3">
              <Button
                size="sm"
                variant="outline"
                onClick={action.onClick}
                className="text-xs border-current text-current hover:bg-white/10"
              >
                {action.label}
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export const ToastContainer: React.FC<ToastContainerProps> = ({
  toasts,
  onRemove,
  position = 'top-right',
  language = 'en'
}) => {
  if (toasts.length === 0) return null

  return (
    <div
      className={`fixed z-50 pointer-events-none ${positionClasses[position]}`}
      aria-live="polite"
      aria-label="Notifications"
    >
      <div className="space-y-3 pointer-events-auto">
        {toasts.map((toast) => (
          <Toast
            key={toast.id}
            {...toast}
            language={language}
            onClose={() => onRemove(toast.id)}
          />
        ))}
      </div>
    </div>
  )
}

// Toast manager hook
export const useToastManager = (): void => {
  const [toasts, setToasts] = useState<ToastProps[]>([])

  const addToast = (toast: Omit<ToastProps, 'id'>): void => {
    const id = Math.random().toString(36).substr(2, 9)
    setToasts(prev => [...prev, { ...toast, id }])
    return id
  }

  const removeToast = (id: string): void => {
    setToasts(prev => prev.filter(toast => toast.id !== id))
  }

  const clearAllToasts = (): void => {
    setToasts([])
  }

  const updateToast = (id: string, updates: Partial<ToastProps>): void => {
    setToasts(prev => prev.map(toast => 
      toast.id === id ? { ...toast, ...updates } : toast
    ))
  }

  // Convenience methods
  const showSuccess = (title: string, message?: string, options?: Partial<ToastProps>): void => {
    return addToast({ type: 'success', title, message, ...options })
  }

  const showError = (title: string, message?: string, options?: Partial<ToastProps>): void => {
    return addToast({ type: 'error', title, message, persistent: true, ...options })
  }

  const showWarning = (title: string, message?: string, options?: Partial<ToastProps>): void => {
    return addToast({ type: 'warning', title, message, ...options })
  }

  const showInfo = (title: string, message?: string, options?: Partial<ToastProps>): void => {
    return addToast({ type: 'info', title, message, ...options })
  }

  const showLoading = (title: string, message?: string, options?: Partial<ToastProps>): void => {
    return addToast({ type: 'loading', title, message, persistent: true, ...options })
  }

  return {
    toasts,
    addToast,
    removeToast,
    clearAllToasts,
    updateToast,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showLoading
  }
}

export default Toast
