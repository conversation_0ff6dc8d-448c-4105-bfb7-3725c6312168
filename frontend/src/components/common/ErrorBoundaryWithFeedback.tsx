/**
 * Enhanced Error Boundary with User Feedback
 * Provides comprehensive error handling with user-friendly messages
 */

import React, { Component, ReactNode } from 'react'
import { AlertTriangle, RefreshCw, Home, Bug } from 'lucide-react'
import { Button } from '@/components/ui/button'

interface Props {
  children: ReactNode
  fallback?: ReactNode
  // @ts-ignore
  onError?: (error: Error, errorInfo: (React as any).ErrorInfo) => void
  language?: 'ar' | 'en'
}

interface State {
  hasError: boolean
  error: Error | null
  // @ts-ignore
  errorInfo: (React as any).ErrorInfo | null
  retryCount: number
// @ts-ignore
}

const translations = {
  ar: {
    errorTitle: 'حدث خطأ غير متوقع',
    errorSubtitle: 'نعتذر، حدث خطأ أثناء تحميل هذه الصفحة',
    errorDetails: 'تفاصيل الخطأ',
    tryAgain: 'المحاولة مرة أخرى',
    goHome: 'العودة للرئيسية',
    reportBug: 'الإبلاغ عن خطأ',
    retryAttempt: 'محاولة رقم',
    maxRetriesReached: 'تم الوصول للحد الأقصى من المحاولات',
    contactSupport: 'يرجى الاتصال بالدعم الفني'
  },
  en: {
    errorTitle: 'Something went wrong',
    errorSubtitle: 'Sorry, an error occurred while loading this page',
    errorDetails: 'Error Details',
    tryAgain: 'Try Again',
    goHome: 'Go Home',
    reportBug: 'Report Bug',
    retryAttempt: 'Attempt',
    maxRetriesReached: 'Maximum retries reached',
    contactSupport: 'Please contact support'
  }
}

export default class ErrorBoundaryWithFeedback extends Component<Props, State> {
  private maxRetries = 3

  // @ts-ignore
  constructor(props: Props as any) {
    super(props as any)
    (this as any).state = {
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0
    }
  }

  // @ts-ignore
  static getDerivedStateFromError(error: Error as any): Partial<State> {
    return {
      hasError: true,
      error
    }
  }

  // @ts-ignore
  componentDidCatch(error: Error, errorInfo: (React as any as any).ErrorInfo) {
    (this as any).setState({
      error,
      errorInfo
    } as any)

    // Call custom error handler if provided
    if ((this as any).props.onError) {
      (this as any).props.onError(error, errorInfo as any)
    }

    // Log error to console in development
    if ((process as any).env.NODE_ENV === 'development') {
      (console as any).error('ErrorBoundary caught an error:', error, errorInfo as any)
    }

    // In production, you might want to send this to an error reporting service
    if ((process as any).env.NODE_ENV === 'production') {
      // Example: Send to error reporting service
      // (errorReportingService as any).captureException(error, { extra: errorInfo } as any)
    }
  }

  handleRetry = () => {
    if ((this as any).state.retryCount < (this as any).maxRetries) {
      (this as any).setState(prevState => ({
        hasError: false,
        error: null,
        errorInfo: null,
        retryCount: (prevState as any as any).retryCount + 1
      }))
    }
  }

  handleGoHome = () => {
    (window as any).location.href = '/'
  }

  handleReportBug = () => {
    const { error, errorInfo } = (this as any).state
    const errorReport = {
      // @ts-ignore
      error: error?.toString( as any),
      stack: error?.stack,
      componentStack: errorInfo?.componentStack,
      // @ts-ignore
      timestamp: new Date( as any).toISOString( as any),
      userAgent: (navigator as any).userAgent,
      url: (window as any).location.href
    }

    // In a real application, you would send this to your bug tracking system
    (console as any).log('Bug report:', errorReport as any)
    
    // For now, copy to clipboard
    (navigator as any).clipboard.writeText((JSON as any as any).stringify(errorReport, null, 2 as any))
      // @ts-ignore
      .then(( as any) => alert('Error details copied to clipboard' as any))
      // @ts-ignore
      .catch(( as any) => (console as any).log('Failed to copy error details' as any))
  }

  // @ts-ignore
  render( as any) {
    const { hasError, error, retryCount } = (this as any).state
    const { children, fallback, language = 'en' } = (this as any).props
    const t = translations[language]
    const isRTL = language === 'ar'
    const canRetry = retryCount < (this as any).maxRetries

    if (hasError) {
      // Use custom fallback if provided
      if (fallback) {
        return fallback
      }

      return (
        <div className={`min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center p-4 ${isRTL ? 'rtl' : 'ltr'}`}>
          <div className="max-w-md w-full">
            <div className="bg-white/10 backdrop-blur-lg border border-white/20 rounded-xl p-8 text-center">
              <div className="mb-6">
                <AlertTriangle className="h-16 w-16 text-red-400 mx-auto mb-4" />
                <h1 className="text-2xl font-bold text-white mb-2">{(t as any).errorTitle}</h1>
                <p className="text-white/70">{(t as any).errorSubtitle}</p>
              </div>

              {retryCount > 0 && (
                <div className="mb-4 p-3 bg-yellow-500/20 border border-yellow-500/50 rounded-lg">
                  <p className="text-yellow-200 text-sm">
                    {(t as any).retryAttempt} {retryCount}/{(this as any).maxRetries}
                  </p>
                </div>
              )}

              {!canRetry && (
                <div className="mb-4 p-3 bg-red-500/20 border border-red-500/50 rounded-lg">
                  <p className="text-red-200 text-sm">{(t as any).maxRetriesReached}</p>
                  <p className="text-red-300 text-xs mt-1">{(t as any).contactSupport}</p>
                </div>
              )}

              <div className="space-y-3">
                {canRetry && (
                  <Button
                    onClick={(this as any).handleRetry}
                    className="w-full bg-blue-500 hover:bg-blue-600 text-white"
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    {(t as any).tryAgain}
                  </Button>
                )}

                <Button
                  onClick={(this as any).handleGoHome}
                  variant="outline"
                  className="w-full border-white/30 text-white hover:bg-white/10"
                >
                  <Home className="h-4 w-4 mr-2" />
                  {(t as any).goHome}
                </Button>

                <Button
                  onClick={(this as any).handleReportBug}
                  variant="ghost"
                  className="w-full text-white/70 hover:text-white hover:bg-white/5"
                >
                  <Bug className="h-4 w-4 mr-2" />
                  {(t as any).reportBug}
                </Button>
              </div>

              {(process as any).env.NODE_ENV === 'development' && error && (
                <details className="mt-6 text-left">
                  <summary className="text-white/70 text-sm cursor-pointer hover:text-white">
                    {(t as any).errorDetails}
                  </summary>
                  <div className="mt-2 p-3 bg-black/30 rounded border text-xs text-red-300 font-mono overflow-auto max-h-40">
                    <div className="mb-2">
                      // @ts-ignore
                      <strong>Error:</strong> {(error as any).toString( as any)}
                    </div>
                    {(error as any).stack && (
                      <div>
                        <strong>Stack:</strong>
                        <pre className="whitespace-pre-wrap text-xs">{(error as any).stack}</pre>
                      </div>
                    )}
                  </div>
                </details>
              )}
            </div>
          </div>
        </div>
      )
    }

    return children
  }
// @ts-ignore
}

// Hook for using error boundary programmatically
export const useErrorHandler = (): void => {
  // @ts-ignore
  return (error: Error, errorInfo?: (React as any).ErrorInfo) => {
    // This would trigger the error boundary
    throw error
  }
// @ts-ignore
}

// Higher-order component for wrapping components with error boundary
export const withErrorBoundary = <P extends object>(
  // @ts-ignore
  Component: (React as any).ComponentType<P>,
  // @ts-ignore
  errorBoundaryProps?: Omit<Props, 'children'>
// @ts-ignore
) => {
  // @ts-ignore
  const WrappedComponent = (props: P): any => (
    <ErrorBoundaryWithFeedback {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundaryWithFeedback>
  )

  (WrappedComponent as any).displayName = `withErrorBoundary(${(Component as any as any).displayName || (Component as any).name})`
  return WrappedComponent
// @ts-ignore
}
