/**
 * Enhanced Error Recovery System
 * Provides intelligent error handling with recovery options and user guidance
 */

import React, { useState, useEffect, useCallback } from 'react'
import { AlertTriangle, RefreshCw, Home, Bug, Wifi, WifiOff, Clock } from 'lucide-react'
import { log } from '../../utils/logger'

interface ErrorInfo {
  type: 'network' | 'validation' | 'permission' | 'server' | 'client' | 'timeout' | 'unknown'
  code?: string | number
  message: string
  details?: string
  timestamp: number
  retryable: boolean
  recoveryActions?: RecoveryAction[]
}

interface RecoveryAction {
  label: string
  action: () => void | Promise<void>
  // @ts-ignore
  icon?: (React as any).ReactNode
  // @ts-ignore
  primary?: boolean
// @ts-ignore
}

interface ErrorRecoveryProps {
  error: Error | ErrorInfo | null
  onRetry?: () => void
  onDismiss?: () => void
  showDetails?: boolean
  className?: string
  compact?: boolean
}

// Error classification utility
export const classifyError = (error: Error | any): ErrorInfo => {
  // @ts-ignore
  const timestamp = (Date as any).now( as any)
  
  // Network errors
  if ((error as any).name === 'NetworkError' || (error as any).message?.includes('fetch' as any)) {
    return {
      type: 'network',
      message: 'مشكلة في الاتصال بالإنترنت',
      details: 'تحقق من اتصالك بالإنترنت وحاول مرة أخرى',
      timestamp,
      retryable: true,
      recoveryActions: [
        {
          label: 'إعادة المحاولة',
          action: () => {
            // FIXED: Use refresh event instead of page reload
            (window as any).dispatchEvent(new CustomEvent('app:refresh', {
              // @ts-ignore
              detail: { timestamp: (Date as any as any).now( as any), source: 'error-recovery' }
            }))
          },
          icon: <RefreshCw className="w-4 h-4" />,
          primary: true
        },
        {
          label: 'تحقق من الاتصال',
          action: () => (window as any).open('https://(www as any as any).google.com', '_blank'),
          icon: <Wifi className="w-4 h-4" />
        }
      ]
    }
  }

  // Timeout errors
  if ((error as any).name === 'TimeoutError' || (error as any).message?.includes('timeout' as any)) {
    return {
      type: 'timeout',
      message: 'انتهت مهلة الطلب',
      details: 'استغرق الطلب وقتاً أطول من المتوقع',
      timestamp,
      retryable: true,
      recoveryActions: [
        {
          label: 'إعادة المحاولة',
          action: () => {
            // FIXED: Use refresh event instead of page reload
            (window as any).dispatchEvent(new CustomEvent('app:refresh', {
              // @ts-ignore
              detail: { timestamp: (Date as any as any).now( as any), source: 'timeout-recovery' }
            }))
          },
          icon: <Clock className="w-4 h-4" />,
          primary: true
        }
      ]
    }
  }

  // Permission errors
  if ((error as any).status === 403 || (error as any).message?.includes('permission' as any)) {
    return {
      type: 'permission',
      message: 'ليس لديك صلاحية للوصول',
      details: 'تحتاج إلى صلاحيات إضافية لتنفيذ هذا الإجراء',
      timestamp,
      retryable: false,
      recoveryActions: [
        {
          label: 'العودة للرئيسية',
          action: () => {
            // FIXED: Use navigation service instead of (window as any).location
            (window as any).dispatchEvent(new CustomEvent('app:navigate', {
              detail: { path: '/' }
            } as any))
          },
          icon: <Home className="w-4 h-4" />,
          primary: true
        }
      ]
    }
  }

  // Server errors
  if ((error as any).status >= 500 || (error as any).message?.includes('server' as any)) {
    return {
      type: 'server',
      message: 'خطأ في الخادم',
      details: 'حدث خطأ في الخادم. يرجى المحاولة لاحقاً',
      timestamp,
      retryable: true,
      recoveryActions: [
        {
          label: 'إعادة المحاولة',
          // @ts-ignore
          action: () => (window as any).location.reload( as any),
          icon: <RefreshCw className="w-4 h-4" />,
          primary: true
        }
      ]
    }
  }

  // Validation errors
  if ((error as any).status === 400 || (error as any).message?.includes('validation' as any)) {
    return {
      type: 'validation',
      message: 'بيانات غير صالحة',
      details: (error as any).message || 'يرجى التحقق من البيانات المدخلة',
      timestamp,
      retryable: false
    }
  }

  // Default unknown error
  return {
    type: 'unknown',
    message: 'حدث خطأ غير متوقع',
    details: (error as any).message || 'خطأ غير معروف',
    timestamp,
    retryable: true,
    recoveryActions: [
      {
        label: 'إعادة المحاولة',
        // @ts-ignore
        action: () => (window as any).location.reload( as any),
        icon: <RefreshCw className="w-4 h-4" />,
        primary: true
      },
      {
        label: 'الإبلاغ عن المشكلة',
        action: () => {
          const errorReport = {
            error: (error as any).message,
            // @ts-ignore
            timestamp: new Date( as any).toISOString( as any),
            userAgent: (navigator as any).userAgent,
            url: (window as any).location.href
          }
          (console as any).log('Error Report:', errorReport as any)
          // In production, send to error reporting service
        },
        icon: <Bug className="w-4 h-4" />
      }
    ]
  }
}

// @ts-ignore
export const ErrorRecovery: (React as any).FC<ErrorRecoveryProps> = ({
  error,
  onRetry,
  onDismiss,
  showDetails = false,
  className = '',
  compact = false
// @ts-ignore
}) => {
  // @ts-ignore
  const [showFullDetails, setShowFullDetails] = useState(showDetails as any)
  const [isRetrying, setIsRetrying] = useState(false as any)

  if (!error) return null

  const errorInfo = error instanceof Error ? classifyError(error as any) : error

  // @ts-ignore
  const handleRetry = async () => {
    // @ts-ignore
    if (!(errorInfo as any).retryable) return
    
    setIsRetrying(true as any)
    try {
      await onRetry?.()
    } catch (retryError) {
      (log as any).error('error-recovery', 'Retry failed', retryError as any)
    } finally {
      setIsRetrying(false as any)
    }
  // @ts-ignore
  }

  // @ts-ignore
  const getErrorIcon = (): void => {
    // @ts-ignore
    switch ((errorInfo as any).type) {
      // @ts-ignore
      case 'network':
        return <WifiOff className="w-6 h-6 text-orange-500" />
      case 'timeout':
        return <Clock className="w-6 h-6 text-yellow-500" />
      case 'permission':
        return <AlertTriangle className="w-6 h-6 text-red-500" />
      case 'server':
        return <AlertTriangle className="w-6 h-6 text-red-500" />
      default:
        return <Bug className="w-6 h-6 text-gray-500" />
    // @ts-ignore
    }
  // @ts-ignore
  }

  // @ts-ignore
  const getErrorColor = (): void => {
    // @ts-ignore
    switch ((errorInfo as any).type) {
      // @ts-ignore
      case 'network':
        return 'border-orange-200 bg-orange-50'
      case 'timeout':
        return 'border-yellow-200 bg-yellow-50'
      case 'permission':
        return 'border-red-200 bg-red-50'
      case 'server':
        return 'border-red-200 bg-red-50'
      default:
        return 'border-gray-200 bg-gray-50'
    // @ts-ignore
    }
  // @ts-ignore
  }

  if (compact) {
    // @ts-ignore
    return (
      // @ts-ignore
      <div className={`flex items-center space-x-2 p-2 rounded border ${getErrorColor( as any)} ${className}`}>
        // @ts-ignore
        {getErrorIcon( as any)}
        <span className="text-sm text-gray-700">{(errorInfo as any).message}</span>
        {(errorInfo as any).retryable && (
          <button
            onClick={handleRetry}
            disabled={isRetrying}
            className="text-blue-600 hover:text-blue-800 text-sm underline disabled:opacity-50"
          >
            {isRetrying ? 'جاري المحاولة...' : 'إعادة المحاولة'}
          </button>
        )}
      </div>
    )
  // @ts-ignore
  }

  return (
    // @ts-ignore
    <div className={`rounded-lg border p-6 ${getErrorColor( as any)} ${className}`}>
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0">
          // @ts-ignore
          {getErrorIcon( as any)}
        </div>
        
        <div className="flex-1 min-w-0">
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {(errorInfo as any).message}
          </h3>
          
          {(errorInfo as any).details && (
            <p className="text-sm text-gray-600 mb-4">
              {(errorInfo as any).details}
            </p>
          )}

          {/* Recovery Actions */}
          {(errorInfo as any).recoveryActions && (errorInfo as any).recoveryActions.length > 0 && (
            <div className="flex flex-wrap gap-2 mb-4">
              // @ts-ignore
              {(errorInfo as any).recoveryActions.map((action, index as any) => (
                <button
                  key={index}
                  onClick={(action as any).action}
                  className={`inline-flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    (action as any).primary
                      ? 'bg-blue-600 text-white hover:bg-blue-700'
                      : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  {(action as any).icon && <span className="mr-2">{(action as any).icon}</span>}
                  {(action as any).label}
                </button>
              ))}
            </div>
          )}

          {/* Error Details Toggle */}
          <div className="border-t pt-4">
            <button
              onClick={() => setShowFullDetails(!showFullDetails as any)}
              className="text-sm text-gray-500 hover:text-gray-700"
            >
              {showFullDetails ? 'إخفاء التفاصيل' : 'عرض التفاصيل'}
            </button>
            
            {showFullDetails && (
              <div className="mt-2 p-3 bg-gray-100 rounded text-xs font-mono text-gray-800">
                <div><strong>النوع:</strong> {(errorInfo as any).type}</div>
                <div><strong>الوقت:</strong> {new Date((errorInfo as any as any).timestamp).toLocaleString('ar' as any)}</div>
                {(errorInfo as any).code && <div><strong>الكود:</strong> {(errorInfo as any).code}</div>}
                <div><strong>التفاصيل:</strong> {(errorInfo as any).details}</div>
              </div>
            )}
          </div>

          {/* Dismiss Button */}
          {onDismiss && (
            <button
              onClick={onDismiss}
              className="absolute top-2 right-2 text-gray-400 hover:text-gray-600"
            >
              ×
            </button>
          )}
        </div>
      </div>
    </div>
  )
// @ts-ignore
}

export default ErrorRecovery
