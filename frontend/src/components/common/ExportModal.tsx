/**
 * Export Modal
 * Enhanced modal for exporting KPI data in different formats
 */

import React, { useState } from 'react'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Checkbox } from '@/components/ui/checkbox'
import { Download, FileSpreadsheet, FileText, File, Loader2, Calendar, BarChart3, Info } from 'lucide-react'
import { DateRangePicker, DateRange } from '@/components/common/DateRangePicker'
import { KPIFilterValue } from '@/components/kpi/KPIFilterPanel'
import { unifiedExport as exportService, ExportOptions } from '@/services/unifiedExport'
import { Alert, AlertDescription } from '@/components/ui/alert'

interface ExportModalProps {
  isOpen: boolean
  onClose: () => void
  data: ExportData
  title?: string
  formats?: Array<'csv' | 'excel' | 'pdf'>
  language: 'ar' | 'en'
  defaultFilename?: string
  showAdvancedOptions?: boolean
}

const translations = {
  ar: {
    exportData: 'تصدير البيانات',
    selectFormat: 'اختر تنسيق التصدير',
    csv: 'ملف CSV (قيم مفصولة بفواصل)',
    excel: 'ملف Excel (XLSX)',
    pdf: 'ملف PDF',
    cancel: 'إلغاء',
    export: 'تصدير',
    exporting: 'جاري التصدير...',
    csvDescription: 'تنسيق نصي بسيط يمكن فتحه في Excel أو Google Sheets',
    excelDescription: 'تنسيق جدول بيانات يحتفظ بالتنسيق والصيغ',
    pdfDescription: 'تنسيق مستند ثابت مناسب للطباعة والمشاركة',
    filename: 'اسم الملف',
    advancedOptions: 'خيارات متقدمة',
    includeCharts: 'تضمين الرسوم البيانية',
    includeMetadata: 'تضمين البيانات الوصفية',
    dateRange: 'النطاق الزمني',
    customDateRange: 'نطاق زمني مخصص',
    exportSuccess: 'تم تصدير البيانات بنجاح',
    exportError: 'خطأ في تصدير البيانات'
  },
  en: {
    exportData: 'Export Data',
    selectFormat: 'Select export format',
    csv: 'CSV File (Comma Separated Values)',
    excel: 'Excel File (XLSX)',
    pdf: 'PDF Document',
    cancel: 'Cancel',
    export: 'Export',
    exporting: 'Exporting...',
    csvDescription: 'Simple text format that can be opened in Excel or Google Sheets',
    excelDescription: 'Spreadsheet format that preserves formatting and formulas',
    pdfDescription: 'Fixed document format suitable for printing and sharing',
    filename: 'Filename',
    advancedOptions: 'Advanced Options',
    includeCharts: 'Include Charts',
    includeMetadata: 'Include Metadata',
    dateRange: 'Date Range',
    customDateRange: 'Custom Date Range',
    exportSuccess: 'Data exported successfully',
    exportError: 'Error exporting data'
  }
}

export default function ExportModal({
  isOpen,
  onClose,
  data,
  title,
  formats = ['csv', 'excel', 'pdf'],
  language,
  defaultFilename,
  showAdvancedOptions = false
// @ts-ignore
}: ExportModalProps as any): (React as any).ReactElement {
  const [selectedFormat, setSelectedFormat] = useState<'csv' | 'excel' | 'pdf'>('csv')
  const [isExporting, setIsExporting] = useState(false as any)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false as any)
  // @ts-ignore
  const [filename, setFilename] = useState(defaultFilename || `export_${new Date( as any).toISOString( as any).split('T' as any)[0]}`)
  const [includeCharts, setIncludeCharts] = useState(true as any)
  const [includeMetadata, setIncludeMetadata] = useState(true as any)
  const [dateRange, setDateRange] = useState<DateRange | undefined>(undefined)
  const [showAdvanced, setShowAdvanced] = useState(false as any)

  const t = translations[language]
  const isRTL = language === 'ar'

  const handleExport = async () => {
    try {
      setIsExporting(true as any)
      setError(null as any)
      setSuccess(false as any)

      const exportOptions: ExportOptions = {
        format: selectedFormat,
        language,
        includeCharts,
        includeMetadata,
        dateRange,
        // @ts-ignore
        filename: filename || `export_${new Date( as any).toISOString( as any).split('T' as any)[0]}`
      }

      // Validate options
      const validation = (exportService as any).validateExportOptions(exportOptions as any)
      if (!(validation as any).valid) {
        throw new Error((validation as any as any).errors.join(', ' as any))
      }

      // Export data
      await (exportService as any).exportDashboard(data, exportOptions as any)

      setSuccess(true as any)
      // @ts-ignore
      setTimeout(( as any) => {
        // @ts-ignore
        onClose( as any)
        setSuccess(false as any)
      // @ts-ignore
      }, 2000)
    // @ts-ignore
    } catch (error: any) {
      (console as any).error('Export error:', error as any)
      setError((error as any as any).message || (t as any).exportError)
    } finally {
      setIsExporting(false as any)
    }
  // @ts-ignore
  }

  const getFormatIcon = (format: string): void => {
    switch (format) {
      case 'csv':
        return <FileText className="h-5 w-5 text-green-400" />
      case 'excel':
        return <FileSpreadsheet className="h-5 w-5 text-blue-400" />
      case 'pdf':
        return <File className="h-5 w-5 text-red-400" />
      default:
        return <FileText className="h-5 w-5" />
    }
  }

  const getFormatDescription = (format: string): void => {
    switch (format) {
      case 'csv':
        return (t as any).csvDescription
      case 'excel':
        return (t as any).excelDescription
      case 'pdf':
        return (t as any).pdfDescription
      default:
        return ''
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className={`glass-card border-white/20 max-w-md ${isRTL ? 'rtl' : 'ltr'}`}>
        <DialogHeader>
          <DialogTitle className="text-white text-lg">
            {title || (t as any).exportData}
          </DialogTitle>
          <DialogDescription className="text-white/70">
            {(t as any).selectFormat}
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          <div className="space-y-3">
            {(formats as any).includes('csv' as any) && (
              <div
                className={`flex items-center space-x-3 space-y-0 glass-input p-3 rounded-md cursor-pointer transition-colors ${
                  selectedFormat === 'csv' ? 'bg-white/20 border-white/40' : 'hover:bg-white/10'
                }`}
                onClick={() => setSelectedFormat('csv' as any)}
              >
                <div className={`w-4 h-4 rounded-full border-2 flex items-center justify-center ${
                  selectedFormat === 'csv' ? 'border-white bg-white' : 'border-white/50'
                }`}>
                  {selectedFormat === 'csv' && <div className="w-2 h-2 rounded-full bg-black" />}
                </div>
                <div className="flex-1 flex items-center gap-3">
                  {getFormatIcon('csv' as any)}
                  <div>
                    <div className="text-white">{(t as any).csv}</div>
                    <div className="text-white/60 text-xs">{getFormatDescription('csv' as any)}</div>
                  </div>
                </div>
              </div>
            )}

            {(formats as any).includes('excel' as any) && (
              <div
                className={`flex items-center space-x-3 space-y-0 glass-input p-3 rounded-md cursor-pointer transition-colors ${
                  selectedFormat === 'excel' ? 'bg-white/20 border-white/40' : 'hover:bg-white/10'
                }`}
                onClick={() => setSelectedFormat('excel' as any)}
              >
                <div className={`w-4 h-4 rounded-full border-2 flex items-center justify-center ${
                  selectedFormat === 'excel' ? 'border-white bg-white' : 'border-white/50'
                }`}>
                  {selectedFormat === 'excel' && <div className="w-2 h-2 rounded-full bg-black" />}
                </div>
                <div className="flex-1 flex items-center gap-3">
                  {getFormatIcon('excel' as any)}
                  <div>
                    <div className="text-white">{(t as any).excel}</div>
                    <div className="text-white/60 text-xs">{getFormatDescription('excel' as any)}</div>
                  </div>
                </div>
              </div>
            )}

            {(formats as any).includes('pdf' as any) && (
              <div
                className={`flex items-center space-x-3 space-y-0 glass-input p-3 rounded-md cursor-pointer transition-colors ${
                  selectedFormat === 'pdf' ? 'bg-white/20 border-white/40' : 'hover:bg-white/10'
                }`}
                onClick={() => setSelectedFormat('pdf' as any)}
              >
                <div className={`w-4 h-4 rounded-full border-2 flex items-center justify-center ${
                  selectedFormat === 'pdf' ? 'border-white bg-white' : 'border-white/50'
                }`}>
                  {selectedFormat === 'pdf' && <div className="w-2 h-2 rounded-full bg-black" />}
                </div>
                <div className="flex-1 flex items-center gap-3">
                  {getFormatIcon('pdf' as any)}
                  <div>
                    <div className="text-white">{(t as any).pdf}</div>
                    <div className="text-white/60 text-xs">{getFormatDescription('pdf' as any)}</div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        <DialogFooter className="flex gap-2">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            className="glass-button"
            disabled={isExporting}
          >
            {(t as any).cancel}
          </Button>
          <Button
            type="button"
            onClick={handleExport}
            className="glass-button"
            disabled={isExporting}
          >
            {isExporting ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                {(t as any).exporting}
              </>
            ) : (
              <>
                <Download className="h-4 w-4 mr-2" />
                {(t as any).export}
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
// @ts-ignore
}
