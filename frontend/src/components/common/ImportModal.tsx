/**
 * Import Modal
 * Reusable modal for importing data from files
 */

import React, { useState, useRef } from 'react'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Upload, FileSpreadsheet, FileText, AlertCircle, CheckCircle, Loader2 } from 'lucide-react'
import { cn } from '@/lib/utils'

interface ImportModalProps {
  isOpen: boolean
  onClose: () => void
  onImport: (file: File) => Promise<{ success: number; errors: any[] }>
  title?: string
  acceptedFormats?: string[]
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    importData: 'استيراد البيانات',
    dragDrop: 'اسحب وأفلت الملف هنا أو',
    browse: 'تصفح',
    selectFile: 'حدد ملف',
    fileSelected: 'تم تحديد الملف',
    importing: 'جاري الاستيراد...',
    import: 'استيراد',
    cancel: 'إلغاء',
    importSuccess: 'تم الاستيراد بنجاح',
    importError: 'حدث خطأ أثناء الاستيراد',
    recordsImported: 'تم استيراد السجلات',
    withErrors: 'مع الأخطاء',
    acceptedFormats: 'التنسيقات المقبولة',
    maxFileSize: 'الحد الأقصى لحجم الملف: 10 ميجابايت'
  },
  en: {
    importData: 'Import Data',
    dragDrop: 'Drag and drop file here or',
    browse: 'browse',
    selectFile: 'Select file',
    fileSelected: 'File selected',
    importing: 'Importing...',
    import: 'Import',
    cancel: 'Cancel',
    importSuccess: 'Import successful',
    importError: 'Import failed',
    recordsImported: 'records imported',
    withErrors: 'with errors',
    acceptedFormats: 'Accepted formats',
    maxFileSize: 'Max file size: 10MB'
  }
}

export default function ImportModal({
  isOpen,
  onClose,
  onImport,
  title,
  acceptedFormats = ['.csv', '.xlsx'],
  language
}: ImportModalProps): React.ReactElement {
  const [file, setFile] = useState<File | null>(null)
  const [isDragging, setIsDragging] = useState(false)
  const [isImporting, setIsImporting] = useState(false)
  const [progress, setProgress] = useState(0)
  const [result, setResult] = useState<{ success: number; errors: any[] } | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)
  
  const t = translations[language]
  const isRTL = language === 'ar'

  const handleDragOver = (e: React.DragEvent): void => {
    e.preventDefault()
    setIsDragging(true)
  }

  const handleDragLeave = (): void => {
    setIsDragging(false)
  }

  const handleDrop = (e: React.DragEvent): void => {
    e.preventDefault()
    setIsDragging(false)
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const droppedFile = e.dataTransfer.files[0]
      setFile(droppedFile)
    }
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
    if (e.target.files && e.target.files.length > 0) {
      setFile(e.target.files[0])
    }
  }

  const handleImport = async () => {
    if (!file) return
    
    try {
      setIsImporting(true)
      setProgress(0)
      
      // Simulate progress
      const progressInterval = setInterval(() => {
        setProgress(prev => {
          const newProgress = prev + Math.random() * 10
          return newProgress >= 90 ? 90 : newProgress
        })
      }, 200)
      
      const importResult = await onImport(file)
      
      clearInterval(progressInterval)
      setProgress(100)
      setResult(importResult)
      
      // Auto-close after successful import with no errors
      if (importResult.errors.length === 0) {
        setTimeout(() => {
          onClose()
        }, 2000)
      }
    } catch (error) {
      console.error('Import error:', error)
      setResult({ success: 0, errors: [error] })
    } finally {
      setIsImporting(false)
    }
  }

  const resetImport = (): void => {
    setFile(null)
    setProgress(0)
    setResult(null)
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className={`glass-card border-white/20 max-w-md ${isRTL ? 'rtl' : 'ltr'}`}>
        <DialogHeader>
          <DialogTitle className="text-white text-lg">
            {title || t.importData}
          </DialogTitle>
          <DialogDescription className="text-white/70">
            {t.acceptedFormats}: {acceptedFormats.join(', ')} • {t.maxFileSize}
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          {!isImporting && !result && (
            <div
              className={cn(
                "border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors",
                isDragging ? "border-primary bg-primary/10" : "border-white/20 hover:border-white/40",
                "glass-input"
              )}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              onClick={() => fileInputRef.current?.click()}
            >
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileChange}
                accept={acceptedFormats.join(',')}
                className="hidden"
              />
              
              {file ? (
                <div className="flex flex-col items-center gap-2">
                  <FileSpreadsheet className="h-10 w-10 text-primary" />
                  <div className="text-white font-medium">{t.fileSelected}</div>
                  <div className="text-white/70 text-sm truncate max-w-full">{file.name}</div>
                  <div className="text-white/50 text-xs">{(file.size / 1024 / 1024).toFixed(2)} MB</div>
                </div>
              ) : (
                <div className="flex flex-col items-center gap-2">
                  <Upload className="h-10 w-10 text-white/50" />
                  <div className="text-white/70">
                    {t.dragDrop} <span className="text-primary font-medium">{t.browse}</span>
                  </div>
                </div>
              )}
            </div>
          )}

          {isImporting && (
            <div className="space-y-4">
              <div className="flex items-center justify-center">
                <Loader2 className="h-10 w-10 text-primary animate-spin" />
              </div>
              <div className="text-center text-white font-medium">{t.importing}</div>
              <Progress value={progress} className="h-2 w-full" />
            </div>
          )}

          {result && (
            <div className="space-y-4">
              <div className="flex items-center justify-center">
                {result.errors.length === 0 ? (
                  <CheckCircle className="h-10 w-10 text-green-500" />
                ) : (
                  <AlertCircle className="h-10 w-10 text-amber-500" />
                )}
              </div>
              
              <div className="text-center">
                <div className="text-white font-medium">
                  {result.errors.length === 0 ? t.importSuccess : t.importError}
                </div>
                <div className="text-white/70 text-sm mt-1">
                  {result.success} {t.recordsImported}
                  {result.errors.length > 0 && (
                    <span> ({result.errors.length} {t.withErrors})</span>
                  )}
                </div>
              </div>
              
              {result.errors.length > 0 && (
                <div className="bg-red-500/10 border border-red-500/20 rounded p-2 text-xs text-white/80 max-h-32 overflow-y-auto">
                  {result.errors.map((error, index) => (
                    <div key={index} className="mb-1 last:mb-0">
                      {typeof error === 'string' ? error : JSON.stringify(error)}
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>

        <DialogFooter className="flex gap-2">
          <Button
            type="button"
            variant="outline"
            onClick={result ? resetImport : onClose}
            className="glass-button"
            disabled={isImporting}
          >
            {result ? t.selectFile : t.cancel}
          </Button>
          
          {!result && (
            <Button
              type="button"
              onClick={handleImport}
              className="glass-button"
              disabled={!file || isImporting}
            >
              {isImporting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  {t.importing}
                </>
              ) : (
                <>
                  <Upload className="h-4 w-4 mr-2" />
                  {t.import}
                </>
              )}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
