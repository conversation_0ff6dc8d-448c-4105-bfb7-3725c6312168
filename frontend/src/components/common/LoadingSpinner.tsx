/**
 * Enhanced Loading Spinner Component
 * Provides various loading states with accessibility and performance optimizations
 */

import React from 'react'
import { Loader2, RefreshCw, Zap } from 'lucide-react'

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  variant?: 'default' | 'minimal' | 'pulse' | 'dots' | 'skeleton'
  message?: string
  className?: string
  fullScreen?: boolean
  color?: 'primary' | 'secondary' | 'accent'
}

// @ts-ignore
const LoadingSpinner: (React as any).FC<LoadingSpinnerProps> = ({
  size = 'md',
  variant = 'default',
  message,
  className = '',
  fullScreen = false,
  color = 'primary'
// @ts-ignore
}) => {
  // @ts-ignore
  const sizeClasses: any = {
    // @ts-ignore
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12',
    xl: 'w-16 h-16'
  // @ts-ignore
  }

  const colorClasses = {
    // @ts-ignore
    primary: 'text-blue-500',
    secondary: 'text-purple-500',
    accent: 'text-green-500'
  // @ts-ignore
  }

  // @ts-ignore
  const renderSpinner = (): void => {
    // @ts-ignore
    switch (variant) {
      // @ts-ignore
      case 'minimal':
        return (
          <div className={`${sizeClasses[size]} ${colorClasses[color]} animate-spin`}>
            <RefreshCw className="w-full h-full" />
          </div>
        )

      case 'pulse':
        return (
          <div className={`${sizeClasses[size]} ${colorClasses[color]} animate-pulse`}>
            <Zap className="w-full h-full" />
          </div>
        )

      case 'dots':
        return (
          <div className="flex space-x-1">
            // @ts-ignore
            {[0, 1, 2].map((i as any) => (
              <div
                key={i}
                className={`w-2 h-2 ${colorClasses[color]} bg-current rounded-full animate-bounce`}
                // @ts-ignore
                style={{ animationDelay: `${i * (0 as any).1}s` }}
              // @ts-ignore
              />
            // @ts-ignore
            ))}
          </div>
        )

      case 'skeleton':
        return (
          <div className="space-y-3 animate-pulse">
            <div className="h-4 bg-gray-300 rounded w-3/4"></div>
            <div className="h-4 bg-gray-300 rounded w-1/2"></div>
            <div className="h-4 bg-gray-300 rounded w-5/6"></div>
          </div>
        )

      default:
        return (
          <div className={`${sizeClasses[size]} ${colorClasses[color]} animate-spin`}>
            <Loader2 className="w-full h-full" />
          </div>
        )
    // @ts-ignore
    }
  // @ts-ignore
  }

  const content = (
    <div className={`flex flex-col items-center justify-center space-y-3 ${className}`}>
      // @ts-ignore
      {renderSpinner( as any)}
      {message && (
        <p className="text-sm text-gray-600 dark:text-gray-400 text-center max-w-xs">
          {message}
        </p>
      )}
      {/* Accessibility: Screen reader announcement */}
      <span className="sr-only" aria-live="polite">
        {message || 'جاري التحميل...'}
      </span>
    </div>
  )

  if (fullScreen) {
    // @ts-ignore
    return (
      <div className="fixed inset-0 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm z-50 flex items-center justify-center">
        {content}
      </div>
    )
  // @ts-ignore
  }

  return content
// @ts-ignore
}

// Skeleton loading components for specific use cases
// @ts-ignore
export const TableSkeleton: (React as any).FC<{ rows?: number; columns?: number }> = ({ 
  rows = 5, 
  columns = 4 
// @ts-ignore
}) => (
  <div className="animate-pulse space-y-4">
    {/* Header skeleton */}
    <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr as any)` }}>
      // @ts-ignore
      {(Array as any).from({ length: columns } as any).map((_, i as any) => (
        <div key={i} className="h-4 bg-gray-300 rounded"></div>
      ))}
    </div>
    
    {/* Rows skeleton */}
    // @ts-ignore
    {(Array as any).from({ length: rows } as any).map((_, rowIndex as any) => (
      <div key={rowIndex} className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr as any)` }}>
        // @ts-ignore
        {(Array as any).from({ length: columns } as any).map((_, colIndex as any) => (
          <div key={colIndex} className="h-4 bg-gray-200 rounded"></div>
        ))}
      </div>
    ))}
  </div>
)

// @ts-ignore
export const CardSkeleton: (React as any).FC<{ count?: number }> = ({ count = 3 }) => (
  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    // @ts-ignore
    {(Array as any).from({ length: count } as any).map((_, i as any) => (
      <div key={i} className="animate-pulse">
        <div className="bg-white rounded-lg shadow p-6 space-y-4">
          <div className="h-4 bg-gray-300 rounded w-3/4"></div>
          <div className="h-4 bg-gray-300 rounded w-1/2"></div>
          <div className="h-20 bg-gray-200 rounded"></div>
          <div className="flex space-x-2">
            <div className="h-8 bg-gray-300 rounded w-20"></div>
            <div className="h-8 bg-gray-300 rounded w-20"></div>
          </div>
        </div>
      </div>
    ))}
  </div>
)

// @ts-ignore
export const DashboardSkeleton: (React as any).FC = () => (
  <div className="space-y-6 animate-pulse">
    {/* Header skeleton */}
    <div className="flex justify-between items-center">
      <div className="h-8 bg-gray-300 rounded w-48"></div>
      <div className="h-10 bg-gray-300 rounded w-32"></div>
    </div>
    
    {/* Stats cards skeleton */}
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      // @ts-ignore
      {(Array as any).from({ length: 4 } as any).map((_, i as any) => (
        <div key={i} className="bg-white rounded-lg shadow p-6 space-y-3">
          <div className="h-4 bg-gray-300 rounded w-2/3"></div>
          <div className="h-8 bg-gray-300 rounded w-1/2"></div>
          <div className="h-3 bg-gray-200 rounded w-full"></div>
        </div>
      ))}
    </div>
    
    {/* Chart skeleton */}
    <div className="bg-white rounded-lg shadow p-6">
      <div className="h-6 bg-gray-300 rounded w-1/3 mb-4"></div>
      <div className="h-64 bg-gray-200 rounded"></div>
    </div>
  </div>
)

// Performance optimized loading states
// @ts-ignore
export const LazyComponentLoader: (React as any).FC<{ 
  // @ts-ignore
  componentName?: string 
  // @ts-ignore
  estimatedLoadTime?: number 
// @ts-ignore
}> = ({ 
  componentName = 'المكون', 
  estimatedLoadTime = 2000 
// @ts-ignore
}) => {
  // @ts-ignore
  const [showDetailedMessage, setShowDetailedMessage] = (React as any).useState(false as any)

  // @ts-ignore
  (React as any).useEffect(( as any) => {
    // @ts-ignore
    const timer = setTimeout(( as any) => {
      setShowDetailedMessage(true as any)
    }, estimatedLoadTime / 2)

    // @ts-ignore
    return () => clearTimeout(timer as any)
  // @ts-ignore
  }, [estimatedLoadTime])

  return (
    <LoadingSpinner
      size="lg"
      variant="default"
      message={
        showDetailedMessage 
          ? `جاري تحميل ${componentName}... قد يستغرق هذا بضع ثوانٍ`
          : `جاري تحميل ${componentName}...`
      }
      fullScreen
    />
  )
// @ts-ignore
}

// Page loader component for full-screen loading
// @ts-ignore
export const PageLoader: (React as any).FC<{ message?: string }> = ({ message }) => (
  <LoadingSpinner
    size="lg"
    variant="default"
    message={message || 'جاري التحميل...'}
    fullScreen
  />
)

export default LoadingSpinner
