/**
 * ENHANCED: Advanced Loading State Manager with Progressive Enhancement
 * Provides consistent loading states, skeleton screens, and progressive enhancement
 */

import React, { useEffect, useState, useRef, createContext, useContext, useReducer, useCallback } from 'react'
import { Loader2, AlertCircle, RefreshCw } from 'lucide-react'
import { log } from '../../utils/logger'

interface LoadingStateManagerProps {
  loading: boolean
  children: React.ReactNode
  minHeight?: number
  preserveLayout?: boolean
  showSpinner?: boolean
  delayMs?: number
  className?: string
  ariaLabel?: string
}

interface SkeletonProps {
  width?: string | number
  height?: string | number
  className?: string
  rounded?: boolean
}

// FIXED: Skeleton component for better loading UX
export const Skeleton: React.FC<SkeletonProps> = ({ 
  width = '100%', 
  height = '1rem', 
  className = '', 
  rounded = false 
}) => {
  return (
    <div
      className={`animate-pulse bg-white/10 ${rounded ? 'rounded-full' : 'rounded'} ${className}`}
      style={{ width, height }}
      aria-hidden="true"
    />
  )
}

// FIXED: Table skeleton for consistent loading states
export const TableSkeleton: React.FC<{ rows?: number; columns?: number }> = ({ 
  rows = 5, 
  columns = 4 
}) => {
  return (
    <div className="space-y-3" aria-hidden="true">
      {/* Header skeleton */}
      <div className="flex space-x-4 p-4 bg-white/5 rounded-lg">
        {Array.from({ length: columns }).map((_, i) => (
          <Skeleton key={i} height="1.5rem" className="flex-1" />
        ))}
      </div>
      
      {/* Row skeletons */}
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div key={rowIndex} className="flex space-x-4 p-4 border-b border-white/10">
          {Array.from({ length: columns }).map((_, colIndex) => (
            <Skeleton key={colIndex} height="1rem" className="flex-1" />
          ))}
        </div>
      ))}
    </div>
  )
}

// FIXED: Modal skeleton for consistent modal loading
export const ModalSkeleton: React.FC = () => {
  return (
    <div className="space-y-6 p-6" aria-hidden="true">
      <Skeleton height="2rem" width="60%" />
      
      <div className="space-y-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <div key={i} className="space-y-2">
            <Skeleton height="1rem" width="30%" />
            <Skeleton height="2.5rem" />
          </div>
        ))}
      </div>
      
      <div className="flex justify-end space-x-2">
        <Skeleton height="2.5rem" width="5rem" />
        <Skeleton height="2.5rem" width="5rem" />
      </div>
    </div>
  )
}

// FIXED: Main loading state manager
export const LoadingStateManager: React.FC<LoadingStateManagerProps> = ({
  loading,
  children,
  minHeight = 200,
  preserveLayout = true,
  showSpinner = true,
  delayMs = 200,
  className = '',
  ariaLabel = 'Loading content'
}) => {
  const [showLoading, setShowLoading] = useState(false)
  const [dimensions, setDimensions] = useState<{ width: number; height: number } | null>(null)
  const contentRef = useRef<HTMLDivElement>(null)
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)

  // FIXED: Measure content dimensions when not loading
  useEffect(() => {
    if (!loading && contentRef.current && !dimensions) {
      const rect = contentRef.current.getBoundingClientRect()
      setDimensions({
        width: rect.width,
        height: Math.max(rect.height, minHeight)
      })
    }
  }, [loading, minHeight, dimensions])

  // FIXED: Delayed loading state to prevent flashing
  useEffect(() => {
    if (loading) {
      timeoutRef.current = setTimeout(() => {
        setShowLoading(true)
      }, delayMs)
    } else {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
      setShowLoading(false)
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [loading, delayMs])

  if (loading && showLoading) {
    const containerStyle = preserveLayout && dimensions ? {
      width: dimensions.width,
      height: dimensions.height,
      minHeight
    } : {
      minHeight
    }

    return (
      <div
        className={`flex items-center justify-center ${className}`}
        style={containerStyle}
        role="status"
        aria-label={ariaLabel}
        aria-live="polite"
      >
        {showSpinner && (
          <div className="flex flex-col items-center space-y-4">
            <Loader2 className="h-8 w-8 animate-spin text-white/60" aria-hidden="true" />
            <span className="text-white/60 text-sm">{ariaLabel}</span>
          </div>
        )}
      </div>
    )
  }

  return (
    <div ref={contentRef} className={className}>
      {children}
    </div>
  )
}

// FIXED: Hook for managing loading states with accessibility
export const useLoadingState = (initialLoading = false): void => {
  const [loading, setLoading] = useState(initialLoading)
  const [error, setError] = useState<string | null>(null)
  
  const startLoading = (): void => {
    setLoading(true)
    setError(null)
  }
  
  const stopLoading = (): void => {
    setLoading(false)
  }
  
  const setLoadingError = (errorMessage: string): void => {
    setLoading(false)
    setError(errorMessage)
  }
  
  return {
    loading,
    error,
    startLoading,
    stopLoading,
    setLoadingError,
    setLoading
  }
}

// FIXED: Error boundary for loading states
interface LoadingErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ReactNode
  onError?: (error: Error) => void
}

export class LoadingErrorBoundary extends React.Component<
  LoadingErrorBoundaryProps,
  { hasError: boolean; error?: Error }
> {
  constructor(props: LoadingErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Loading Error Boundary caught an error:', error, errorInfo)
    this.props.onError?.(error)
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <div 
          className="flex items-center justify-center min-h-[200px] text-white/60"
          role="alert"
          aria-live="assertive"
        >
          <div className="text-center space-y-2">
            <p>Something went wrong while loading.</p>
            <button
              onClick={() => this.setState({ hasError: false, error: undefined })}
              className="text-blue-400 hover:text-blue-300 underline"
            >
              Try again
            </button>
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

// ENHANCEMENT: Progressive Loading States
export const ProgressiveLoader: React.FC<{
  stages: Array<{ name: string; duration: number; message: string }>
  onComplete?: () => void
  className?: string
}> = ({ stages, onComplete, className = '' }) => {
  const [currentStage, setCurrentStage] = useState(0)
  const [progress, setProgress] = useState(0)

  useEffect(() => {
    if (currentStage >= stages.length) {
      onComplete?.()
      return
    }

    const stage = stages[currentStage]
    const startTime = Date.now()

    const interval = setInterval(() => {
      const elapsed = Date.now() - startTime
      const stageProgress = Math.min((elapsed / stage.duration) * 100, 100)
      setProgress(stageProgress)

      if (stageProgress >= 100) {
        clearInterval(interval)
        setCurrentStage(prev => prev + 1)
        setProgress(0)
      }
    }, 50)

    return () => clearInterval(interval)
  }, [currentStage, stages, onComplete])

  if (currentStage >= stages.length) {
    return null
  }

  const stage = stages[currentStage]

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="text-center">
        <Loader2 className="w-8 h-8 animate-spin mx-auto text-blue-500 mb-2" />
        <p className="text-sm text-gray-600">{stage.message}</p>
      </div>

      <div className="w-full bg-gray-200 rounded-full h-2">
        <div
          className="bg-blue-500 h-2 rounded-full transition-all duration-100"
          style={{ width: `${progress}%` }}
        />
      </div>

      <div className="text-xs text-gray-500 text-center">
        المرحلة {currentStage + 1} من {stages.length}
      </div>
    </div>
  )
}

// ENHANCEMENT: Smart Loading with Retry Logic
export const SmartLoader: React.FC<{
  loadingFunction: () => Promise<any>
  retryAttempts?: number
  retryDelay?: number
  onSuccess?: (data: unknown) => void
  onError?: (error: Error) => void
  children: (state: {
    loading: boolean
    error: Error | null
    retry: () => void
    attempt: number
  }) => React.ReactNode
}> = ({
  loadingFunction,
  retryAttempts = 3,
  retryDelay = 1000,
  onSuccess,
  onError,
  children
}) => {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const [attempt, setAttempt] = useState(0)

  const executeLoad = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      const result = await loadingFunction()
      setLoading(false)
      onSuccess?.(result)
    } catch (err) {
      const error = err as Error
      setLoading(false)
      setError(error)
      onError?.(error)

      // Auto-retry logic
      if (attempt < retryAttempts) {
        setTimeout(() => {
          setAttempt(prev => prev + 1)
          executeLoad()
        }, retryDelay * Math.pow(2, attempt)) // Exponential backoff
      }
    }
  }, [loadingFunction, attempt, retryAttempts, retryDelay, onSuccess, onError])

  const retry = useCallback(() => {
    setAttempt(0)
    executeLoad()
  }, [executeLoad])

  useEffect(() => {
    executeLoad()
  }, []) // Only run on mount

  return <>{children({ loading, error, retry, attempt })}</>
}

export default LoadingStateManager
