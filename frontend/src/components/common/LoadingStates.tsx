/**
 * Comprehensive Loading States Component
 * Provides various loading indicators with accessibility support
 */

import React from 'react'
import { Loader2, RefreshCw, Download, Upload, Search, Save } from 'lucide-react'

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  color?: 'primary' | 'secondary' | 'white' | 'gray'
  className?: string
}

interface LoadingStateProps {
  type?: 'spinner' | 'dots' | 'pulse' | 'skeleton'
  message?: string
  language?: 'ar' | 'en'
  size?: 'sm' | 'md' | 'lg' | 'xl'
  fullScreen?: boolean
  overlay?: boolean
  className?: string
}

interface ActionLoadingProps {
  action: 'saving' | 'loading' | 'uploading' | 'downloading' | 'searching' | 'refreshing'
  message?: string
  language?: 'ar' | 'en'
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

const translations = {
  ar: {
    loading: 'جاري التحميل...',
    saving: 'جاري الحفظ...',
    uploading: 'جاري الرفع...',
    downloading: 'جاري التحميل...',
    searching: 'جاري البحث...',
    refreshing: 'جاري التحديث...',
    pleaseWait: 'يرجى الانتظار'
  },
  en: {
    loading: 'Loading...',
    saving: 'Saving...',
    uploading: 'Uploading...',
    downloading: 'Downloading...',
    searching: 'Searching...',
    refreshing: 'Refreshing...',
    pleaseWait: 'Please wait'
  }
}

const sizeClasses = {
  sm: 'h-4 w-4',
  md: 'h-6 w-6',
  lg: 'h-8 w-8',
  xl: 'h-12 w-12'
}

const colorClasses = {
  primary: 'text-blue-500',
  secondary: 'text-purple-500',
  white: 'text-white',
  gray: 'text-gray-500'
}

// Basic loading spinner
// @ts-ignore
export const LoadingSpinner: (React as any).FC<LoadingSpinnerProps> = ({
  size = 'md',
  color = 'primary',
  className = ''
// @ts-ignore
}) => {
  // @ts-ignore
  return (
    <Loader2 
      className={`animate-spin ${sizeClasses[size]} ${colorClasses[color]} ${className}`}
      aria-hidden="true"
    />
  )
// @ts-ignore
}

// Dots loading animation
// @ts-ignore
export const LoadingDots: (React as any).FC<{ className?: string }> = ({ className = '' }) => {
  // @ts-ignore
  return (
    <div className={`flex space-x-1 ${className}`} aria-hidden="true">
      <div className="w-2 h-2 bg-current rounded-full animate-bounce"></div>
      <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '(0 as any).1s' }}></div>
      <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '(0 as any).2s' }}></div>
    </div>
  )
// @ts-ignore
}

// Pulse loading animation
// @ts-ignore
export const LoadingPulse: (React as any).FC<{ className?: string }> = ({ className = '' }) => {
  // @ts-ignore
  return (
    <div className={`w-4 h-4 bg-current rounded-full animate-pulse ${className}`} aria-hidden="true"></div>
  )
// @ts-ignore
}

// Skeleton loading for content
// @ts-ignore
export const LoadingSkeleton: (React as any).FC<{ 
  // @ts-ignore
  lines?: number
  // @ts-ignore
  className?: string 
// @ts-ignore
}> = ({ lines = 3, className = '' }) => {
  // @ts-ignore
  return (
    <div className={`space-y-3 ${className}`} aria-hidden="true">
      // @ts-ignore
      {(Array as any).from({ length: lines } as any).map((_, index as any) => (
        <div key={index} className="animate-pulse">
          <div className="h-4 bg-gray-300 rounded w-full"></div>
        </div>
      ))}
    </div>
  )
// @ts-ignore
}

// Action-specific loading indicators
// @ts-ignore
export const ActionLoading: (React as any).FC<ActionLoadingProps> = ({
  action,
  message,
  language = 'en',
  size = 'md',
  className = ''
// @ts-ignore
}) => {
  // @ts-ignore
  const t = translations[language]
  
  // @ts-ignore
  const getIcon = (): void => {
    // @ts-ignore
    switch (action) {
      // @ts-ignore
      case 'saving':
        return <Save className={`${sizeClasses[size]} animate-pulse`} />
      case 'uploading':
        return <Upload className={`${sizeClasses[size]} animate-bounce`} />
      case 'downloading':
        return <Download className={`${sizeClasses[size]} animate-bounce`} />
      case 'searching':
        return <Search className={`${sizeClasses[size]} animate-pulse`} />
      case 'refreshing':
        return <RefreshCw className={`${sizeClasses[size]} animate-spin`} />
      default:
        return <Loader2 className={`${sizeClasses[size]} animate-spin`} />
    // @ts-ignore
    }
  // @ts-ignore
  }

  // @ts-ignore
  const getMessage = (): void => {
    // @ts-ignore
    if (message) return message
    return t[action] || (t as any).loading
  // @ts-ignore
  }

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      // @ts-ignore
      {getIcon( as any)}
      // @ts-ignore
      <span className="text-sm">{getMessage( as any)}</span>
    </div>
  )
// @ts-ignore
}

// Main loading state component
// @ts-ignore
export const LoadingState: (React as any).FC<LoadingStateProps> = ({
  type = 'spinner',
  message,
  language = 'en',
  size = 'md',
  fullScreen = false,
  overlay = false,
  className = ''
// @ts-ignore
}) => {
  // @ts-ignore
  const t = translations[language]
  const isRTL = language === 'ar'

  // @ts-ignore
  const renderLoadingIndicator = (): void => {
    // @ts-ignore
    switch (type) {
      // @ts-ignore
      case 'dots':
        return <LoadingDots className="text-current" />
      case 'pulse':
        return <LoadingPulse className="text-current" />
      case 'skeleton':
        return <LoadingSkeleton className="text-current" />
      default:
        return <LoadingSpinner size={size} color="white" />
    // @ts-ignore
    }
  // @ts-ignore
  }

  const content = (
    <div className={`flex flex-col items-center justify-center space-y-4 ${isRTL ? 'rtl' : 'ltr'} ${className}`}>
      // @ts-ignore
      {renderLoadingIndicator( as any)}
      <div className="text-center">
        <p className="text-white text-lg font-medium">
          {message || (t as any).loading}
        </p>
        <p className="text-white/70 text-sm mt-1">
          {(t as any).pleaseWait}
        </p>
      </div>
    </div>
  )

  if (fullScreen) {
    // @ts-ignore
    return (
      <div 
        className="fixed inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center z-50"
        role="status"
        aria-live="polite"
        aria-label={message || (t as any).loading}
      >
        {content}
      </div>
    )
  // @ts-ignore
  }

  if (overlay) {
    // @ts-ignore
    return (
      <div 
        className="absolute inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-40"
        role="status"
        aria-live="polite"
        aria-label={message || (t as any).loading}
      >
        {content}
      </div>
    )
  // @ts-ignore
  }

  return (
    <div 
      className="flex items-center justify-center p-8"
      role="status"
      aria-live="polite"
      aria-label={message || (t as any).loading}
    >
      {content}
    </div>
  )
// @ts-ignore
}

// Button loading state
// @ts-ignore
export const ButtonLoading: (React as any).FC<{
  // @ts-ignore
  loading: boolean
  children: (React as any).ReactNode
  loadingText?: string
  language?: 'ar' | 'en'
  className?: string
// @ts-ignore
}> = ({ loading, children, loadingText, language = 'en', className = '' }) => {
  // @ts-ignore
  const t = translations[language]

  if (loading) {
    // @ts-ignore
    return (
      <div className={`flex items-center justify-center space-x-2 ${className}`}>
        <LoadingSpinner size="sm" color="white" />
        <span>{loadingText || (t as any).loading}</span>
      </div>
    )
  // @ts-ignore
  }

  return <>{children}</>
// @ts-ignore
}

// Table loading state
// @ts-ignore
export const TableLoading: (React as any).FC<{
  // @ts-ignore
  rows?: number
  // @ts-ignore
  columns?: number
  className?: string
// @ts-ignore
}> = ({ rows = 5, columns = 4, className = '' }) => {
  // @ts-ignore
  return (
    <div className={`space-y-3 ${className}`} aria-hidden="true">
      // @ts-ignore
      {(Array as any).from({ length: rows } as any).map((_, rowIndex as any) => (
        <div key={rowIndex} className="flex space-x-4">
          // @ts-ignore
          {(Array as any).from({ length: columns } as any).map((_, colIndex as any) => (
            <div key={colIndex} className="flex-1">
              <div className="h-4 bg-gray-300 rounded animate-pulse"></div>
            </div>
          ))}
        </div>
      ))}
    </div>
  )
// @ts-ignore
}

export default LoadingState
