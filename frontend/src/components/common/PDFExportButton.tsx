/**
 * PDF Export Button Component
 * Reusable component for PDF generation across all EMS pages
 */

import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger,
  DropdownMenuSeparator 
} from '@/components/ui/dropdown-menu'
import { FileText, Download, Loader2, Languages } from 'lucide-react'
import { toast } from 'react-hot-toast'

interface PDFExportButtonProps {
  templateType: string
  language: 'ar' | 'en'
  data?: any
  filename?: string
  className?: string
  variant?: 'default' | 'outline' | 'ghost'
  size?: 'sm' | 'default' | 'lg'
  showLanguageOptions?: boolean
  onExportStart?: () => void
  onExportComplete?: () => void
  onExportError?: (error: Error) => void
}

const translations = {
  ar: {
    exportPDF: 'تصدير PDF',
    exportInArabic: 'تصدير باللغة العربية',
    exportInEnglish: 'تصدير باللغة الإنجليزية',
    exporting: 'جاري التصدير...',
    exportSuccess: 'تم تصدير التقرير بنجاح',
    exportError: 'فشل في تصدير التقرير',
    generating: 'جاري إنشاء التقرير...'
  },
  en: {
    exportPDF: 'Export PDF',
    exportInArabic: 'Export in Arabic',
    exportInEnglish: 'Export in English',
    exporting: 'Exporting...',
    exportSuccess: 'Report exported successfully',
    exportError: 'Failed to export report',
    generating: 'Generating report...'
  }
}

export default function PDFExportButton({
  templateType,
  language,
  data,
  filename,
  className = '',
  variant = 'outline',
  size = 'default',
  showLanguageOptions = true,
  onExportStart,
  onExportComplete,
  onExportError
}: PDFExportButtonProps): React.ReactElement {
  const [isExporting, setIsExporting] = useState(false)
  const t = translations[language]

  const generatePDF = async (exportLanguage: 'ar' | 'en') => {
    if (isExporting) return

    setIsExporting(true)
    onExportStart?.()

    const loadingToast = toast.loading(t.generating)

    try {
      // Determine the correct template type with language suffix
      const templateWithLang = exportLanguage === 'ar' && !templateType.endsWith('_ar') 
        ? `${templateType}_ar` 
        : templateType.endsWith('_ar') && exportLanguage === 'en'
        ? templateType.replace('_ar', '')
        : templateType

      const response = await fetch(`http://localhost:8000/api/pdf/generate/${templateWithLang}/?language=${exportLanguage}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token') || ''}`,
        }
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      // Get PDF blob from response
      const pdfBlob = await response.blob()

      // Generate filename
      const defaultFilename = `${templateType}-${exportLanguage}-${new Date().toISOString().split('T')[0]}.pdf`
      const downloadFilename = filename || defaultFilename

      // Download the PDF
      const url = window.URL.createObjectURL(pdfBlob)
      const link = document.createElement('a')
      link.href = url
      link.download = downloadFilename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      toast.success(t.exportSuccess, { id: loadingToast })
      onExportComplete?.()

    } catch (error) {
      console.error('PDF export error:', error)
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      toast.error(t.exportError, { id: loadingToast })
      onExportError?.(error instanceof Error ? error : new Error(errorMessage))
    } finally {
      setIsExporting(false)
    }
  }

  const handleSingleLanguageExport = (): void => {
    generatePDF(language)
  }

  if (!showLanguageOptions) {
    return (
      <Button
        onClick={handleSingleLanguageExport}
        disabled={isExporting}
        variant={variant}
        size={size}
        className={`${className} ${isExporting ? 'opacity-50 cursor-not-allowed' : ''}`}
      >
        {isExporting ? (
          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
        ) : (
          <FileText className="h-4 w-4 mr-2" />
        )}
        {isExporting ? t.exporting : t.exportPDF}
      </Button>
    )
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          disabled={isExporting}
          variant={variant}
          size={size}
          className={`${className} ${isExporting ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          {isExporting ? (
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          ) : (
            <FileText className="h-4 w-4 mr-2" />
          )}
          {isExporting ? t.exporting : t.exportPDF}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        <DropdownMenuItem
          onClick={() => generatePDF('ar')}
          disabled={isExporting}
          className="flex items-center gap-2"
        >
          <Languages className="h-4 w-4" />
          {t.exportInArabic}
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => generatePDF('en')}
          disabled={isExporting}
          className="flex items-center gap-2"
        >
          <Languages className="h-4 w-4" />
          {t.exportInEnglish}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

// Template type mapping for common page types
export const getTemplateTypeFromPage = (pageName: string): string => {
  const templateMap: Record<string, string> = {
    'employees': 'employee_report',
    'attendance': 'attendance_report',
    'leave': 'hr_report',
    'payroll': 'hr_report',
    'projects': 'project_report',
    'tasks': 'task_report',
    'budgets': 'financial_report',
    'expenses': 'financial_report',
    'sales': 'sales_report',
    'customers': 'customer_report',
    'assets': 'asset_report',
    'inventory': 'asset_report',
    'suppliers': 'asset_report',
    'kpis': 'kpi_report',
    'performance': 'kpi_report',
    'departments': 'hr_report',
    'quotations': 'sales_report',
    'orders': 'sales_report',
    'invoices': 'sales_report',
    'support': 'support_ticket_report',
    'tickets': 'support_ticket_report',
    'feedback': 'customer_feedback_report',
    'analytics': 'analytics_report'
  }

  return templateMap[pageName.toLowerCase()] || 'hr_report'
}

// Utility function for quick PDF export
export const exportPageToPDF = async (
  pageName: string, 
  language: 'ar' | 'en', 
  filename?: string
): Promise<void> => {
  const templateType = getTemplateTypeFromPage(pageName)
  const templateWithLang = language === 'ar' ? `${templateType}_ar` : templateType
  
  const response = await fetch(`http://localhost:8000/api/pdf/generate/${templateWithLang}/?language=${language}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${localStorage.getItem('auth_token') || ''}`,
    }
  })

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`)
  }

  const pdfBlob = await response.blob()
  const downloadFilename = filename || `${pageName}-${language}-${new Date().toISOString().split('T')[0]}.pdf`

  const url = window.URL.createObjectURL(pdfBlob)
  const link = document.createElement('a')
  link.href = url
  link.download = downloadFilename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  window.URL.revokeObjectURL(url)
}
