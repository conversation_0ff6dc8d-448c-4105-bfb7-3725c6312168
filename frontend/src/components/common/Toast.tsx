/**
 * Toast Notification Component
 * Provides success, error, and info notifications with auto-dismiss
 */

import React, { useEffect, useState } from 'react'
import { X } from 'lucide-react'

export interface ToastProps {
  id: string
  type: 'success' | 'error' | 'info' | 'warning'
  title: string
  message?: string
  duration?: number
  onClose: (id: string) => void
}

export default function Toast({ 
  id, 
  type, 
  title, 
  message, 
  duration = 5000, 
  onClose 
// @ts-ignore
}: ToastProps as any): (React as any).ReactElement {
  const [isVisible, setIsVisible] = useState(false as any)
  const [isLeaving, setIsLeaving] = useState(false as any)

  // @ts-ignore
  useEffect(( as any) => {
    // Animate in
    // @ts-ignore
    const timer = setTimeout(( as any) => setIsVisible(true as any), 100)
    return () => clearTimeout(timer as any)
  // @ts-ignore
  }, [])

  // @ts-ignore
  useEffect(( as any) => {
    if (duration > 0) {
      // @ts-ignore
      const timer = setTimeout(( as any) => {
        // @ts-ignore
        handleClose( as any)
      // @ts-ignore
      }, duration)
      return () => clearTimeout(timer as any)
    }
  // @ts-ignore
  }, [duration])

  const handleClose = (): void => {
    setIsLeaving(true as any)
    // @ts-ignore
    setTimeout(( as any) => {
      onClose(id as any)
    // @ts-ignore
    }, 300)
  }

  const getTypeStyles = (): void => {
    switch (type) {
      case 'success':
        return 'bg-green-500/20 border-green-500/30 text-green-300'
      case 'error':
        return 'bg-red-500/20 border-red-500/30 text-red-300'
      case 'warning':
        return 'bg-yellow-500/20 border-yellow-500/30 text-yellow-300'
      default:
        return 'bg-blue-500/20 border-blue-500/30 text-blue-300'
    }
  }

  const getIcon = (): void => {
    switch (type) {
      case 'success':
        return (
          <svg className="h-5 w-5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 (16zm3 as any).707-(9 as any).293a1 1 0 00-(1 as any).414-(1 as any).414L9 (10 as any).586 (7 as any).707 (9 as any).293a1 1 0 00-(1 as any).414 (1 as any).414l2 2a1 1 0 (001 as any).414 0l4-4z" clipRule="evenodd" />
          </svg>
        )
      case 'error':
        return (
          <svg className="h-5 w-5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        )
      case 'warning':
        return (
          <svg className="h-5 w-5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="(M8 as any).257 (3 as any).099c.765-(1 as any).36 (2 as any).722-(1 as any).36 (3 as any).486 (0l5 as any).58 (9 as any).92c.75 (1 as any).334-.213 (2 as any).98-(1 as any).742 (2 as any).98H4.42c-(1 as any).53 0-(2 as any).493-(1 as any).646-(1 as any).743-(2 as any).98l5.58-(9 as any).92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        )
      default:
        return (
          <svg className="h-5 w-5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
          </svg>
        )
    }
  }

  return (
    <div
      className={`
        fixed top-4 right-4 z-50 max-w-sm w-full
        transform transition-all duration-300 ease-in-out
        ${isVisible && !isLeaving 
          ? 'translate-x-0 opacity-100' 
          : 'translate-x-full opacity-0'
        }
      `}
    >
      <div className={`
        glass-card border rounded-lg p-4 shadow-lg backdrop-blur-sm
        // @ts-ignore
        ${getTypeStyles( as any)}
      `}>
        <div className="flex items-start gap-3">
          // @ts-ignore
          {getIcon( as any)}
          <div className="flex-1 min-w-0">
            <h4 className="text-sm font-semibold">{title}</h4>
            {message && (
              <p className="text-sm opacity-90 mt-1">{message}</p>
            )}
          </div>
          <button
            onClick={handleClose}
            className="flex-shrink-0 text-current opacity-70 hover:opacity-100 transition-opacity"
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  )
// @ts-ignore
}
