import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from '@/components/ui/dropdown-menu'
import { Download, FileText, Mail, Shield, Settings } from 'lucide-react'
import { unifiedExport, ExportOptions } from '@/services/unifiedExport'
import { EmailPDFModal } from './EmailPDFModal'

interface UnifiedExportButtonProps {
  dataType: string
  data?: any[]
  className?: string
  variant?: 'default' | 'outline' | 'ghost'
  size?: 'sm' | 'default' | 'lg'
  showAdvanced?: boolean
  language?: 'ar' | 'en'
}

export const UnifiedExportButton: React.FC<UnifiedExportButtonProps> = ({
  dataType,
  data,
  className,
  variant = 'outline',
  size = 'default',
  showAdvanced = true,
  language = 'en'
}) => {
  const [isLoading, setIsLoading] = useState(false)
  const [loadingType, setLoadingType] = useState<string>('')
  const [showEmailModal, setShowEmailModal] = useState(false)
  const [lastPdfId, setLastPdfId] = useState<string>('')

  const handleExport = async (format: 'csv' | 'excel' | 'pdf', options?: Partial<ExportOptions>) => {
    setIsLoading(true)
    setLoadingType(format)

    try {
      // For PDF exports, we'll try the actual export and handle errors gracefully
      // No need for separate health check

      const exportOptions: ExportOptions = {
        format,
        language,
        data,
        ...options
      }

      const result = await unifiedExport.export(dataType, exportOptions)

      if (result.success) {
        console.log('Export successful:', result.message)

        // Store PDF ID for potential email sending
        if (format === 'pdf' && result.downloadUrl) {
          setLastPdfId(result.downloadUrl)
        }
      } else {
        console.error('Export failed:', result.error || result.message)
        alert(language === 'ar' ? 'فشل التصدير' : 'Export Failed')
      }
    } catch (error) {
      console.error('Export error:', error)
      if (error instanceof Error && error.message.includes('Failed to fetch')) {
        alert(language === 'ar'
          ? 'خطأ في الاتصال بالخادم. يرجى التأكد من تشغيل الخادم على المنفذ 8000'
          : 'Server connection error. Please ensure the server is running on port 8000')
      } else {
        alert(language === 'ar' ? 'خطأ في التصدير' : 'Export Error')
      }
    } finally {
      setIsLoading(false)
      setLoadingType('')
    }
  }

  const handleQuickExport = async (format: 'csv' | 'excel' | 'pdf') => {
    await handleExport(format)
  }

  const handleEmailExport = (): void => {
    // First generate PDF, then show email modal
    handleExport('pdf').then(() => {
      setShowEmailModal(true)
    })
  }

  const handleSignedExport = async () => {
    // This would show a certificate selection modal first
    // For now, we'll use a default certificate if available
    await handleExport('pdf', {
      signatureOptions: {
        enabled: true,
        certificateId: 'default', // This should come from user selection
        reason: 'Document approval'
      }
    })
  }

  const getLoadingText = (): void => {
    if (!isLoading) return ''

    const texts = {
      csv: language === 'ar' ? 'تصدير CSV...' : 'Exporting CSV...',
      excel: language === 'ar' ? 'تصدير Excel...' : 'Exporting Excel...',
      pdf: language === 'ar' ? 'إنشاء PDF...' : 'Generating PDF...'
    }

    return texts[loadingType as keyof typeof texts] || (language === 'ar' ? 'جاري التصدير...' : 'Exporting...')
  }

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant={variant}
            size={size}
            className={className}
            disabled={isLoading}
          >
            <Download className="h-4 w-4 mr-2" />
            {isLoading ? getLoadingText() : (language === 'ar' ? 'تصدير' : 'Export')}
          </Button>
        </DropdownMenuTrigger>

        <DropdownMenuContent
          align={language === 'ar' ? 'start' : 'end'}
          side="bottom"
          sideOffset={4}
          avoidCollisions={true}
          className="w-56 glass-dropdown z-50"
        >
          {/* Quick Export Options */}
          <DropdownMenuItem
            onClick={() => handleQuickExport('pdf')}
            disabled={isLoading}
          >
            <FileText className="h-4 w-4 mr-2" />
            {language === 'ar' ? 'تصدير PDF' : 'Export as PDF'}
          </DropdownMenuItem>

          <DropdownMenuItem
            onClick={() => handleQuickExport('excel')}
            disabled={isLoading}
          >
            <FileText className="h-4 w-4 mr-2" />
            {language === 'ar' ? 'تصدير Excel' : 'Export as Excel'}
          </DropdownMenuItem>

          <DropdownMenuItem
            onClick={() => handleQuickExport('csv')}
            disabled={isLoading}
          >
            <FileText className="h-4 w-4 mr-2" />
            {language === 'ar' ? 'تصدير CSV' : 'Export as CSV'}
          </DropdownMenuItem>

          {showAdvanced && (
            <>
              <DropdownMenuSeparator />

              {/* Advanced Options */}
              <DropdownMenuItem
                onClick={handleEmailExport}
                disabled={isLoading}
              >
                <Mail className="h-4 w-4 mr-2" />
                {language === 'ar' ? 'إرسال عبر البريد' : 'Send via Email'}
              </DropdownMenuItem>

              <DropdownMenuItem
                onClick={handleSignedExport}
                disabled={isLoading}
              >
                <Shield className="h-4 w-4 mr-2" />
                {language === 'ar' ? 'PDF موقع رقمياً' : 'Digitally Signed PDF'}
              </DropdownMenuItem>
            </>
          )}
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Email Modal */}
      <EmailPDFModal
        isOpen={showEmailModal}
        onClose={() => setShowEmailModal(false)}
        pdfHistoryId={lastPdfId}
        pdfType={dataType}
      />
    </>
  )
}

// Quick export hooks for common use cases
export const useQuickExport = (dataType: string, language: 'ar' | 'en' = 'en'): void => {
  return {
    exportPDF: () => unifiedExport.quickPDF(dataType, language),
    exportCSV: () => unifiedExport.quickCSV(dataType),
    exportExcel: () => unifiedExport.quickExcel(dataType),
    emailPDF: (recipients: string[], subject?: string) =>
      unifiedExport.emailPDF(dataType, recipients, language, subject),
    signedPDF: (certificateId: string) =>
      unifiedExport.signedPDF(dataType, certificateId, language)
  }
}

// Export button variants for specific use cases
export const QuickPDFButton: React.FC<{
  dataType: string;
  className?: string;
  language?: 'ar' | 'en';
}> = ({ dataType, className, language = 'en' }) => {
  const [isLoading, setIsLoading] = useState(false)

  const handleClick = async () => {
    setIsLoading(true)
    try {
      await unifiedExport.quickPDF(dataType, language)
      console.log('PDF generated successfully')
    } catch (error) {
      console.error('PDF generation failed:', error)
      alert(language === 'ar' ? 'فشل في إنشاء PDF' : 'PDF generation failed')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Button onClick={handleClick} disabled={isLoading} className={className}>
      <FileText className="h-4 w-4 mr-2" />
      {isLoading
        ? (language === 'ar' ? 'جاري الإنشاء...' : 'Generating...')
        : (language === 'ar' ? 'تقرير PDF' : 'PDF Report')
      }
    </Button>
  )
}

export const EmailPDFButton: React.FC<{
  dataType: string
  recipients: string[]
  subject?: string
  className?: string
  language?: 'ar' | 'en'
}> = ({ dataType, recipients, subject, className, language = 'en' }) => {
  const [isLoading, setIsLoading] = useState(false)

  const handleClick = async () => {
    setIsLoading(true)
    try {
      await unifiedExport.emailPDF(dataType, recipients, language, subject)
      console.log('Email sent successfully')
    } catch (error) {
      console.error('Email sending failed:', error)
      alert(language === 'ar' ? 'فشل في إرسال البريد' : 'Email sending failed')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Button onClick={handleClick} disabled={isLoading} className={className}>
      <Mail className="h-4 w-4 mr-2" />
      {isLoading
        ? (language === 'ar' ? 'جاري الإرسال...' : 'Sending...')
        : (language === 'ar' ? 'إرسال تقرير' : 'Email Report')
      }
    </Button>
  )
}
