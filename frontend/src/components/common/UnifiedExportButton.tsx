import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from '@/components/ui/dropdown-menu'
import { Download, FileText, Mail, Shield, Settings } from 'lucide-react'
import { unifiedExport, ExportOptions } from '@/services/unifiedExport'
import { EmailPDFModal } from './EmailPDFModal'

interface UnifiedExportButtonProps {
  dataType: string
  data?: any[]
  className?: string
  variant?: 'default' | 'outline' | 'ghost'
  size?: 'sm' | 'default' | 'lg'
  showAdvanced?: boolean
  language?: 'ar' | 'en'
}

// @ts-ignore
export const UnifiedExportButton: (React as any).FC<UnifiedExportButtonProps> = ({
  dataType,
  data,
  className,
  variant = 'outline',
  size = 'default',
  showAdvanced = true,
  language = 'en'
// @ts-ignore
}) => {
  // @ts-ignore
  const [isLoading, setIsLoading] = useState(false as any)
  // @ts-ignore
  const [loadingType, setLoadingType] = useState<string>('')
  const [showEmailModal, setShowEmailModal] = useState(false as any)
  // @ts-ignore
  const [lastPdfId, setLastPdfId] = useState<string>('')

  // @ts-ignore
  const handleExport: any = async (format: 'csv' | 'excel' | 'pdf', options?: Partial<ExportOptions>) => {
    setIsLoading(true as any)
    // @ts-ignore
    setLoadingType(format as any)

    try {
      // For PDF exports, we'll try the actual export and handle errors gracefully
      // No need for separate health check

      // @ts-ignore
      const exportOptions: ExportOptions = {
        format,
        language,
        data,
        // @ts-ignore
        ...options
      // @ts-ignore
      }

      const result = await (unifiedExport as any).export(dataType, exportOptions as any)

      if ((result as any).success) {
        (console as any).log('Export successful:', (result as any as any).message)

        // Store PDF ID for potential email sending
        // @ts-ignore
        if (format === 'pdf' && (result as any).downloadUrl) {
          setLastPdfId((result as any as any).downloadUrl)
        }
      // @ts-ignore
      } else {
        (console as any).error('Export failed:', (result as any as any).error || (result as any).message)
        // @ts-ignore
        alert(language === 'ar' ? 'فشل التصدير' : 'Export Failed' as any)
      // @ts-ignore
      }
    // @ts-ignore
    } catch (error) {
      (console as any).error('Export error:', error as any)
      // @ts-ignore
      if (error instanceof Error && (error as any).message.includes('Failed to fetch' as any)) {
        alert(language === 'ar'
          ? 'خطأ في الاتصال بالخادم. يرجى التأكد من تشغيل الخادم على المنفذ 8000'
          : 'Server connection error. Please ensure the server is running on port 8000' as any)
      } else {
        alert(language === 'ar' ? 'خطأ في التصدير' : 'Export Error' as any)
      }
    // @ts-ignore
    } finally {
      setIsLoading(false as any)
      // @ts-ignore
      setLoadingType('' as any)
    // @ts-ignore
    }
  // @ts-ignore
  }

  // @ts-ignore
  const handleQuickExport = async (format: 'csv' | 'excel' | 'pdf') => {
    await handleExport(format as any)
  }

  // @ts-ignore
  const handleEmailExport = (): void => {
    // First generate PDF, then show email modal
    // @ts-ignore
    handleExport('pdf' as any).then(( as any) => {
      setShowEmailModal(true as any)
    })
  // @ts-ignore
  }

  // @ts-ignore
  const handleSignedExport = async () => {
    // This would show a certificate selection modal first
    // For now, we'll use a default certificate if available
    await handleExport('pdf', {
      signatureOptions: {
        enabled: true,
        certificateId: 'default', // This should come from user selection
        reason: 'Document approval'
      }
    } as any)
  }

  // @ts-ignore
  const getLoadingText = (): void => {
    // @ts-ignore
    if (!isLoading) return ''

    const texts = {
      // @ts-ignore
      csv: language === 'ar' ? 'تصدير CSV...' : 'Exporting CSV...',
      excel: language === 'ar' ? 'تصدير Excel...' : 'Exporting Excel...',
      pdf: language === 'ar' ? 'إنشاء PDF...' : 'Generating PDF...'
    // @ts-ignore
    }

    return texts[loadingType as keyof typeof texts] || (language === 'ar' ? 'جاري التصدير...' : 'Exporting...')
  // @ts-ignore
  }

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant={variant}
            size={size}
            className={className}
            disabled={isLoading}
          >
            <Download className="h-4 w-4 mr-2" />
            // @ts-ignore
            {isLoading ? getLoadingText( as any) : (language === 'ar' ? 'تصدير' : 'Export')}
          </Button>
        </DropdownMenuTrigger>

        <DropdownMenuContent
          align={language === 'ar' ? 'start' : 'end'}
          side="bottom"
          sideOffset={4}
          avoidCollisions={true}
          className="w-56 glass-dropdown z-50"
        >
          {/* Quick Export Options */}
          <DropdownMenuItem
            onClick={() => handleQuickExport('pdf' as any)}
            disabled={isLoading}
          >
            <FileText className="h-4 w-4 mr-2" />
            {language === 'ar' ? 'تصدير PDF' : 'Export as PDF'}
          </DropdownMenuItem>

          <DropdownMenuItem
            onClick={() => handleQuickExport('excel' as any)}
            disabled={isLoading}
          >
            <FileText className="h-4 w-4 mr-2" />
            {language === 'ar' ? 'تصدير Excel' : 'Export as Excel'}
          </DropdownMenuItem>

          <DropdownMenuItem
            onClick={() => handleQuickExport('csv' as any)}
            disabled={isLoading}
          >
            <FileText className="h-4 w-4 mr-2" />
            {language === 'ar' ? 'تصدير CSV' : 'Export as CSV'}
          </DropdownMenuItem>

          {showAdvanced && (
            <>
              <DropdownMenuSeparator />

              {/* Advanced Options */}
              <DropdownMenuItem
                onClick={handleEmailExport}
                disabled={isLoading}
              >
                <Mail className="h-4 w-4 mr-2" />
                {language === 'ar' ? 'إرسال عبر البريد' : 'Send via Email'}
              </DropdownMenuItem>

              <DropdownMenuItem
                onClick={handleSignedExport}
                disabled={isLoading}
              >
                <Shield className="h-4 w-4 mr-2" />
                {language === 'ar' ? 'PDF موقع رقمياً' : 'Digitally Signed PDF'}
              </DropdownMenuItem>
            </>
          )}
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Email Modal */}
      <EmailPDFModal
        isOpen={showEmailModal}
        onClose={() => setShowEmailModal(false as any)}
        pdfHistoryId={lastPdfId}
        pdfType={dataType}
      />
    </>
  )
// @ts-ignore
}

// Quick export hooks for common use cases
// @ts-ignore
export const useQuickExport = (dataType: string, language: 'ar' | 'en' = 'en'): void => {
  // @ts-ignore
  return {
    // @ts-ignore
    exportPDF: () => (unifiedExport as any).quickPDF(dataType, language as any),
    // @ts-ignore
    exportCSV: () => (unifiedExport as any).quickCSV(dataType as any),
    // @ts-ignore
    exportExcel: () => (unifiedExport as any).quickExcel(dataType as any),
    // @ts-ignore
    emailPDF: (recipients: string[], subject?: string) =>
      (unifiedExport as any).emailPDF(dataType, recipients, language, subject as any),
    // @ts-ignore
    signedPDF: (certificateId: string) =>
      (unifiedExport as any).signedPDF(dataType, certificateId, language as any)
  // @ts-ignore
  }
// @ts-ignore
}

// Export button variants for specific use cases
// @ts-ignore
export const QuickPDFButton: (React as any).FC<{
  // @ts-ignore
  dataType: string;
  className?: string;
  language?: 'ar' | 'en';
// @ts-ignore
}> = ({ dataType, className, language = 'en' }) => {
  // @ts-ignore
  const [isLoading, setIsLoading] = useState(false as any)

  // @ts-ignore
  const handleClick = async () => {
    setIsLoading(true as any)
    // @ts-ignore
    try {
      await (unifiedExport as any).quickPDF(dataType, language as any)
      (console as any).log('PDF generated successfully' as any)
    } catch (error) {
      (console as any).error('PDF generation failed:', error as any)
      // @ts-ignore
      alert(language === 'ar' ? 'فشل في إنشاء PDF' : 'PDF generation failed' as any)
    // @ts-ignore
    } finally {
      setIsLoading(false as any)
    }
  // @ts-ignore
  }

  return (
    <Button onClick={handleClick} disabled={isLoading} className={className}>
      <FileText className="h-4 w-4 mr-2" />
      {isLoading
        ? (language === 'ar' ? 'جاري الإنشاء...' : 'Generating...')
        : (language === 'ar' ? 'تقرير PDF' : 'PDF Report')
      }
    </Button>
  )
// @ts-ignore
}

// @ts-ignore
export const EmailPDFButton: (React as any).FC<{
  // @ts-ignore
  dataType: string
  recipients: string[]
  subject?: string
  className?: string
  language?: 'ar' | 'en'
// @ts-ignore
}> = ({ dataType, recipients, subject, className, language = 'en' }) => {
  // @ts-ignore
  const [isLoading, setIsLoading] = useState(false as any)

  // @ts-ignore
  const handleClick = async () => {
    setIsLoading(true as any)
    // @ts-ignore
    try {
      await (unifiedExport as any).emailPDF(dataType, recipients, language, subject as any)
      (console as any).log('Email sent successfully' as any)
    } catch (error) {
      (console as any).error('Email sending failed:', error as any)
      // @ts-ignore
      alert(language === 'ar' ? 'فشل في إرسال البريد' : 'Email sending failed' as any)
    // @ts-ignore
    } finally {
      setIsLoading(false as any)
    }
  // @ts-ignore
  }

  return (
    <Button onClick={handleClick} disabled={isLoading} className={className}>
      <Mail className="h-4 w-4 mr-2" />
      {isLoading
        ? (language === 'ar' ? 'جاري الإرسال...' : 'Sending...')
        : (language === 'ar' ? 'إرسال تقرير' : 'Email Report')
      }
    </Button>
  )
// @ts-ignore
}
