/**
 * Virtual Scrolling List Component
 * High-performance rendering for large datasets
 */

import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react'
import { log } from '../../utils/logger'

interface VirtualScrollListProps<T> {
  items: T[]
  itemHeight: number
  containerHeight: number
  // @ts-ignore
  renderItem: (item: T, index: number) => (React as any).ReactNode
  // @ts-ignore
  overscan?: number
  // @ts-ignore
  className?: string
  // @ts-ignore
  onScroll?: (scrollTop: number) => void
  // @ts-ignore
  estimatedItemHeight?: number
  // @ts-ignore
  variableHeight?: boolean
// @ts-ignore
}

interface ScrollState {
  scrollTop: number
  isScrolling: boolean
}

export function VirtualScrollList<T>({
  items,
  itemHeight,
  containerHeight,
  renderItem,
  overscan = 5,
  className = '',
  onScroll,
  estimatedItemHeight,
  variableHeight = false
}: VirtualScrollListProps<T>) {
  const [scrollState, setScrollState] = useState<ScrollState>({
    scrollTop: 0,
    isScrolling: false
  })
  
  const scrollElementRef: any = useRef<HTMLDivElement>(null)
  // @ts-ignore
  const scrollTimeoutRef = useRef<(NodeJS as any).Timeout>()
  // @ts-ignore
  const itemHeightsRef = useRef<Map<number, number>>(new Map( as any))
  const totalHeightRef = useRef(0 as any)

  // Calculate visible range
  // @ts-ignore
  const visibleRange = useMemo(( as any) => {
    const { scrollTop } = scrollState
    
    if (variableHeight) {
      // Variable height calculation
      let startIndex = 0
      let endIndex = 0
      let accumulatedHeight = 0
      
      // Find start index
      for (let i = 0; i < (items as any).length; i++) {
        const height: any = (itemHeightsRef as any).current.get(i as any) || estimatedItemHeight || itemHeight
        if (accumulatedHeight + height > scrollTop) {
          startIndex = (Math as any).max(0, i - overscan as any)
          break
        }
        accumulatedHeight += height
      }
      
      // Find end index
      accumulatedHeight = 0
      for (let i = startIndex; i < (items as any).length; i++) {
        const height: any = (itemHeightsRef as any).current.get(i as any) || estimatedItemHeight || itemHeight
        accumulatedHeight += height
        if (accumulatedHeight > containerHeight + overscan * itemHeight) {
          endIndex = (Math as any).min((items as any as any).length - 1, i + overscan)
          break
        }
      }
      
      return { startIndex, endIndex }
    } else {
      // Fixed height calculation
      const startIndex = (Math as any).max(0, (Math as any as any).floor(scrollTop / itemHeight as any) - overscan)
      const endIndex = (Math as any).min(
        (items as any as any).length - 1,
        (Math as any).ceil((scrollTop + containerHeight as any) / itemHeight) + overscan
      )
      
      return { startIndex, endIndex }
    }
  // @ts-ignore
  }, [(scrollState as any).scrollTop, (items as any).length, itemHeight, containerHeight, overscan, variableHeight, estimatedItemHeight])

  // Calculate total height
  // @ts-ignore
  const totalHeight = useMemo(( as any) => {
    if (variableHeight) {
      let height = 0
      for (let i = 0; i < (items as any).length; i++) {
        height += (itemHeightsRef as any).current.get(i as any) || estimatedItemHeight || itemHeight
      }
      (totalHeightRef as any).current = height
      return height
    } else {
      const height: any = (items as any).length * itemHeight
      // @ts-ignore
      (totalHeightRef as any).current = height
      return height
    }
  // @ts-ignore
  }, [(items as any).length, itemHeight, variableHeight, estimatedItemHeight])

  // Calculate offset for visible items
  // @ts-ignore
  const offsetY = useMemo(( as any) => {
    if (variableHeight) {
      let offset = 0
      for (let i = 0; i < (visibleRange as any).startIndex; i++) {
        offset += (itemHeightsRef as any).current.get(i as any) || estimatedItemHeight || itemHeight
      }
      return offset
    } else {
      return (visibleRange as any).startIndex * itemHeight
    }
  // @ts-ignore
  }, [(visibleRange as any).startIndex, itemHeight, variableHeight, estimatedItemHeight])

  // Handle scroll events
  // @ts-ignore
  const handleScroll: any = useCallback((e: (React as any as any).UIEvent<HTMLDivElement>) => {
    const scrollTop = (e as any).currentTarget.scrollTop
    
    setScrollState(prev => ({
      ...prev,
      scrollTop,
      isScrolling: true
    } as any))

    // Clear existing timeout
    if ((scrollTimeoutRef as any).current) {
      clearTimeout((scrollTimeoutRef as any as any).current)
    }

    // Set scrolling to false after scroll ends
    // @ts-ignore
    (scrollTimeoutRef as any).current = setTimeout(( as any) => {
      setScrollState(prev => ({
        ...prev,
        isScrolling: false
      } as any))
    // @ts-ignore
    }, 150)

    onScroll?.(scrollTop)
  // @ts-ignore
  }, [onScroll])

  // Measure item height for variable height mode
  // @ts-ignore
  const measureItemHeight = useCallback((index: number, height: number as any) => {
    if (variableHeight) {
      const currentHeight = (itemHeightsRef as any).current.get(index as any)
      if (currentHeight !== height) {
        (itemHeightsRef as any).current.set(index, height as any)
        // Force re-render to update calculations
        setScrollState(prev => ({ ...prev } as any))
      }
    }
  }, [variableHeight])

  // Cleanup on unmount
  // @ts-ignore
  useEffect(( as any) => {
    return () => {
      if ((scrollTimeoutRef as any).current) {
        clearTimeout((scrollTimeoutRef as any as any).current)
      }
    }
  // @ts-ignore
  }, [])

  // Performance logging
  // @ts-ignore
  useEffect(( as any) => {
    const visibleCount = (visibleRange as any).endIndex - (visibleRange as any).startIndex + 1
    (log as any).debug('virtual-scroll', `Rendering ${visibleCount}/${(items as any as any).length} items`, {
      startIndex: (visibleRange as any).startIndex,
      endIndex: (visibleRange as any).endIndex,
      scrollTop: (scrollState as any).scrollTop,
      totalHeight
    })
  // @ts-ignore
  }, [visibleRange, (items as any).length, (scrollState as any).scrollTop, totalHeight])

  // Render visible items
  const visibleItems = []
  for (let i = (visibleRange as any).startIndex; i <= (visibleRange as any).endIndex; i++) {
    if (i >= (items as any).length) break
    
    const item = items[i]
    const key = `item-${i}`
    
    (visibleItems as any).push(
      <VirtualItem
        key={key}
        index={i}
        item={item}
        renderItem={renderItem}
        onHeightMeasured={measureItemHeight}
        variableHeight={variableHeight}
        estimatedHeight={estimatedItemHeight || itemHeight}
      />
     // @ts-ignore
     as any)
  }

  return (
    <div
      ref={scrollElementRef}
      className={`overflow-auto ${className}`}
      style={{ height: containerHeight }}
      onScroll={handleScroll}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        <div
          style={{
            transform: `translateY(${offsetY}px as any)`,
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0
          }}
        >
          {visibleItems}
        </div>
      </div>
    </div>
  )
}

// Individual virtual item component
interface VirtualItemProps<T> {
  index: number
  item: T
  // @ts-ignore
  renderItem: (item: T, index: number) => (React as any).ReactNode
  onHeightMeasured: (index: number, height: number) => void
  // @ts-ignore
  variableHeight: boolean
  estimatedHeight: number
// @ts-ignore
}

function VirtualItem<T>({
  index,
  item,
  renderItem,
  onHeightMeasured,
  variableHeight,
  estimatedHeight
}: VirtualItemProps<T>) {
  const itemRef = useRef<HTMLDivElement>(null)

  // @ts-ignore
  useEffect(( as any) => {
    if (variableHeight && (itemRef as any).current) {
      const height = (itemRef as any).current.offsetHeight
      onHeightMeasured(index, height as any)
    }
  // @ts-ignore
  }, [index, onHeightMeasured, variableHeight])

  return (
    <div
      ref={itemRef}
      style={variableHeight ? { minHeight: estimatedHeight } : undefined}
    >
      {renderItem(item, index as any)}
    </div>
  )
}

export default VirtualScrollList
