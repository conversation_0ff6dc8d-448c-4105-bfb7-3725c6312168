import React, { useState, useEffect } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  Activity,
  TrendingUp,
  TrendingDown,
  Users,
  DollarSign,
  Clock,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Bell,
  // Calendar, // TODO: Add calendar integration
  BarChart3,
  type LucideIcon
} from 'lucide-react'
import { useDashboardSync } from '../../hooks/useDashboardSync'

interface RealTimeDashboardProps {
  language: 'ar' | 'en'
  userRole: string
}

interface RealTimeMetric {
  id: string
  title: string
  value: string | number
  change: number
  trend: 'up' | 'down' | 'stable'
  icon: LucideIcon
  color: string
  lastUpdated: Date
}

const translations = {
  ar: {
    realTimeMetrics: 'المؤشرات المباشرة',
    lastUpdated: 'آخر تحديث',
    autoRefresh: 'تحديث تلقائي',
    refreshNow: 'تحديث الآن',
    activeUsers: 'المستخدمون النشطون',
    systemLoad: 'حمولة النظام',
    pendingTasks: 'المهام المعلقة',
    todayRevenue: 'إيرادات اليوم',
    systemAlerts: 'تنبيهات النظام',
    completedToday: 'مكتمل اليوم',
    onlineEmployees: 'الموظفون المتصلون',
    criticalIssues: 'مشاكل حرجة'
  },
  en: {
    realTimeMetrics: 'Real-Time Metrics',
    lastUpdated: 'Last Updated',
    autoRefresh: 'Auto Refresh',
    refreshNow: 'Refresh Now',
    activeUsers: 'Active Users',
    systemLoad: 'System Load',
    pendingTasks: 'Pending Tasks',
    todayRevenue: 'Today Revenue',
    systemAlerts: 'System Alerts',
    completedToday: 'Completed Today',
    onlineEmployees: 'Online Employees',
    criticalIssues: 'Critical Issues'
  }
}

// @ts-ignore
export default function RealTimeDashboard({ language, userRole }: RealTimeDashboardProps as any): (React as any).ReactElement {
  const [metrics, setMetrics] = useState<RealTimeMetric[]>([])
  const [autoRefresh, setAutoRefresh] = useState(true as any)
  // @ts-ignore
  const [lastUpdate, setLastUpdate] = useState(new Date( as any))
  const [isRefreshing, setIsRefreshing] = useState(false as any)
  const t = translations[language]
  const isRTL = language === 'ar'

  // FIXED: Use dashboard sync hook for data invalidation
  // @ts-ignore
  const { refreshDashboardMetrics } = useDashboardSync( as any)

  // Initialize metrics based on user role with real API data
  // @ts-ignore
  useEffect(( as any) => {
    const loadRealTimeMetrics = async () => {
      try {
        // Import dashboard API
        const { dashboardAPI } = await import('../../services/api' as any)
        // @ts-ignore
        const dashboardStats = await (dashboardAPI as any).getStats( as any)

        const baseMetrics: RealTimeMetric[] = [
          {
            id: 'activeUsers',
            title: (t as any).activeUsers,
            value: (dashboardStats as any).total_employees || 0,
            change: (dashboardStats as any).employee_growth_rate || 0, // Use real growth rate from API
            trend: ((dashboardStats as any).employee_growth_rate || 0) > 0 ? 'up' : ((dashboardStats as any).employee_growth_rate || 0) < 0 ? 'down' : 'stable',
            icon: Users,
            color: 'from-blue-500 to-blue-600',
            // @ts-ignore
            lastUpdated: new Date( as any)
          },
          {
            id: 'systemLoad',
            title: (t as any).systemLoad,
            value: (dashboardStats as any).system_health?.cpu_usage ? `${(Math as any).round((dashboardStats as any as any).system_health.cpu_usage)}%` : '0%',
            change: (dashboardStats as any).system_health?.cpu_change || 0, // Use real CPU change from API
            trend: ((dashboardStats as any).system_health?.cpu_change || 0) > 0 ? 'up' : ((dashboardStats as any).system_health?.cpu_change || 0) < 0 ? 'down' : 'stable',
            icon: Activity,
            color: 'from-green-500 to-green-600',
            // @ts-ignore
            lastUpdated: new Date( as any)
          },
          {
            id: 'pendingTasks',
            title: (t as any).pendingTasks,
            value: (dashboardStats as any).pending_tasks || 0,
            change: 0, // TODO: Calculate real change from historical data
            trend: 'stable',
            icon: Clock,
            color: 'from-orange-500 to-orange-600',
            // @ts-ignore
            lastUpdated: new Date( as any)
          },
          {
            id: 'todayRevenue',
            title: (t as any).todayRevenue,
            // @ts-ignore
            value: (dashboardStats as any).monthly_expenses ? `$${(dashboardStats as any).monthly_expenses.toLocaleString( as any)}` : '$0',
            change: 0, // TODO: Calculate real change from historical data
            trend: 'stable',
            icon: DollarSign,
            color: 'from-purple-500 to-purple-600',
            // @ts-ignore
            lastUpdated: new Date( as any)
          }
        ]

        // Add role-specific metrics
        if (userRole === 'super_admin') {
          (baseMetrics as any).push(
            {
              id: 'systemAlerts',
              title: (t as any as any).systemAlerts,
              value: (dashboardStats as any).system_alerts_count || 0, // Use real system alerts from API
              change: (dashboardStats as any).alerts_change || 0,
              trend: ((dashboardStats as any).alerts_change || 0) > 0 ? 'up' : ((dashboardStats as any).alerts_change || 0) < 0 ? 'down' : 'stable',
              icon: AlertTriangle,
              color: 'from-red-500 to-red-600',
              // @ts-ignore
              lastUpdated: new Date( as any)
            },
            {
              id: 'completedToday',
              title: (t as any).completedToday,
              value: (dashboardStats as any).active_projects || 0,
              change: (dashboardStats as any).projects_completion_rate || 0, // Use real completion rate from API
              trend: ((dashboardStats as any).projects_completion_rate || 0) > 0 ? 'up' : ((dashboardStats as any).projects_completion_rate || 0) < 0 ? 'down' : 'stable',
              icon: CheckCircle,
              color: 'from-emerald-500 to-emerald-600',
              // @ts-ignore
              lastUpdated: new Date( as any)
            }
          )
        }

        setMetrics(baseMetrics as any)
      } catch (error) {
        (console as any).error('Error loading real-time metrics:', error as any)

        // Fallback to basic metrics if API fails
        const fallbackMetrics: RealTimeMetric[] = [
          {
            id: 'activeUsers',
            title: (t as any).activeUsers,
            value: 0,
            change: 0,
            trend: 'stable',
            icon: Users,
            color: 'from-blue-500 to-blue-600',
            // @ts-ignore
            lastUpdated: new Date( as any)
          }
        ]
        setMetrics(fallbackMetrics as any)
      }
    }

    // @ts-ignore
    loadRealTimeMetrics( as any)
  // @ts-ignore
  }, [userRole, t])

  // Auto-refresh functionality
  // @ts-ignore
  useEffect(( as any) => {
    if (!autoRefresh) return

    // @ts-ignore
    const interval = setInterval(( as any) => {
      // @ts-ignore
      updateMetrics( as any)
    // @ts-ignore
    }, 30000) // Update every 30 seconds

    return () => clearInterval(interval as any)
  // @ts-ignore
  }, [autoRefresh])

  // Update metrics with real API data
  const updateMetrics = async () => {
    setIsRefreshing(true as any)

    try {
      // Fetch fresh data from API
      const { dashboardAPI } = await import('../../services/api' as any)
      // @ts-ignore
      const dashboardStats = await (dashboardAPI as any).getStats( as any)

      // Create stable timestamp to prevent cascading re-renders
      // @ts-ignore
      const updateTimestamp = new Date( as any)

      setMetrics(prev => (prev as any as any).map(metric => {
        let newValue = (metric as any as any).value
        let newChange = (metric as any).change

        // Update with real data based on metric type
        switch ((metric as any).id) {
          case 'activeUsers':
            newValue = (dashboardStats as any).total_employees || 0
            break
          case 'pendingTasks':
            newValue = (dashboardStats as any).pending_tasks || 0
            break
          case 'completedToday':
            newValue = (dashboardStats as any).active_projects || 0
            break
          // For metrics without direct API equivalents, keep current value
          default:
            break
        }

        return {
          ...metric,
          value: newValue,
          change: newChange,
          lastUpdated: updateTimestamp
        }
      }))

      setLastUpdate(updateTimestamp as any)
    } catch (error) {
      (console as any).error('Error updating metrics:', error as any)
    } finally {
      setIsRefreshing(false as any)
    }
  }

  const getTrendIcon = (trend: string): void => {
    switch (trend) {
      case 'up': return <TrendingUp className="h-4 w-4 text-green-400" />
      case 'down': return <TrendingDown className="h-4 w-4 text-red-400" />
      default: return <BarChart3 className="h-4 w-4 text-gray-400" />
    }
  }

  const getTrendColor = (trend: string): void => {
    switch (trend) {
      case 'up': return 'text-green-400'
      case 'down': return 'text-red-400'
      default: return 'text-gray-400'
    }
  }

  return (
    <Card className="glass-card border-white/20">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-white text-xl flex items-center gap-2">
            <Activity className="h-5 w-5" />
            {(t as any).realTimeMetrics}
          </CardTitle>
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setAutoRefresh(!autoRefresh as any)}
                className={`glass-button text-xs ${autoRefresh ? 'bg-green-500/20' : 'bg-gray-500/20'}`}
              >
                <Bell className="h-3 w-3 mr-1" />
                {(t as any).autoRefresh}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={updateMetrics}
                disabled={isRefreshing}
                className="glass-button text-xs"
              >
                <RefreshCw className={`h-3 w-3 mr-1 ${isRefreshing ? 'animate-spin' : ''}`} />
                {(t as any).refreshNow}
              </Button>
            </div>
          </div>
        </div>
        <p className="text-white/60 text-sm">
          {(t as any).lastUpdated}: {(lastUpdate as any).toLocaleTimeString(language === 'ar' ? 'ar-SA' : 'en-US' as any)}
        </p>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          // @ts-ignore
          {(metrics as any).map((metric as any) => (
            <div
              key={(metric as any).id}
              className="p-4 glass-card border-white/10 hover:border-white/30 transition-all duration-300 group"
            >
              <div className="flex items-center justify-between mb-3">
                <div className={`p-2 rounded-lg bg-gradient-to-r ${(metric as any).color} group-hover:scale-110 transition-transform duration-300`}>
                  // @ts-ignore
                  <(metric as any).icon className="h-4 w-4 text-white" />
                </div>
                <div className="flex items-center gap-1">
                  {getTrendIcon((metric as any as any).trend)}
                  <span className={`text-xs font-medium ${getTrendColor((metric as any as any).trend)}`}>
                    {(metric as any).change > 0 ? '+' : ''}{(metric as any).change.toFixed(1 as any)}%
                  </span>
                </div>
              </div>
              <div>
                <p className="text-white/70 text-xs mb-1">{(metric as any).title}</p>
                <p className="text-white text-lg font-bold">{(metric as any).value}</p>
                <p className="text-white/50 text-xs">
                  {(metric as any).lastUpdated.toLocaleTimeString(language === 'ar' ? 'ar-SA' : 'en-US' as any)}
                </p>
              </div>
            </div>
          // @ts-ignore
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
