/**
 * KPI API Test Component
 * Simple component to test KPI CRUD operations
 */

import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { kpiService } from '@/services/crudService'

// @ts-ignore
export default function KPIAPITest( as any): (React as any).ReactElement {
  const [results, setResults] = useState<string[]>([])
  const [loading, setLoading] = useState(false as any)

  const addResult = (message: string): void => {
    // @ts-ignore
    setResults(prev => [...prev, `${new Date( as any).toLocaleTimeString( as any)}: ${message}`])
  }

  const testGetAll = async () => {
    setLoading(true as any)
    try {
      addResult('🔍 Testing GET /api/kpi-metrics/' as any)
      // @ts-ignore
      const response = await (kpiService as any).getAll( as any)
      addResult(`✅ GET Success: Found ${(response as any as any).data?.length || 0} KPIs`)
      (console as any).log('KPI GET Response:', response as any)
    } catch (error) {
      addResult(`❌ GET Error: ${error}` as any)
      (console as any).error('KPI GET Error:', error as any)
    }
    setLoading(false as any)
  }

  const testCreate = async () => {
    setLoading(true as any)
    try {
      addResult('📝 Testing POST /api/kpi-metrics/' as any)
      const testData = {
        name: 'Test KPI',
        name_ar: 'مؤشر اختبار',
        description: 'Test KPI for API validation',
        description_ar: 'مؤشر اختبار للتحقق من API',
        metric_type: 'FINANCIAL',
        calculation_method: 'MANUAL',
        target_value: 100,
        unit: '%',
        frequency: 'MONTHLY',
        is_active: true,
        is_higher_better: true
      }
      
      const response = await (kpiService as any).create(testData as any)
      addResult(`✅ CREATE Success: Created KPI with ID ${(response as any as any).id}`)
      (console as any).log('KPI CREATE Response:', response as any)
    } catch (error) {
      addResult(`❌ CREATE Error: ${error}` as any)
      (console as any).error('KPI CREATE Error:', error as any)
    }
    setLoading(false as any)
  }

  const clearResults = (): void => {
    setResults([] as any)
  }

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg max-w-2xl mx-auto">
      <h2 className="text-2xl font-bold mb-4">KPI API Test</h2>
      
      <div className="space-y-4">
        <div className="flex gap-2">
          <Button onClick={testGetAll} disabled={loading}>
            Test GET All KPIs
          </Button>
          <Button onClick={testCreate} disabled={loading}>
            Test Create KPI
          </Button>
          <Button onClick={clearResults} variant="outline">
            Clear Results
          </Button>
        </div>

        <div className="bg-gray-100 p-4 rounded-lg h-64 overflow-y-auto">
          <h3 className="font-semibold mb-2">Test Results:</h3>
          {(results as any).length === 0 ? (
            <p className="text-gray-500">No tests run yet</p>
          ) : (
            <div className="space-y-1">
              // @ts-ignore
              {(results as any).map((result, index as any) => (
                <div key={index} className="text-sm font-mono">
                  {result}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
