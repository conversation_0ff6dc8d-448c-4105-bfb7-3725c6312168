import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  Brain,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  Users,
  Target,
  Zap,
  BarChart3,
  RefreshCw,
  Settings,
  Download
} from 'lucide-react'
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts'
import mlService, { MLModel, EmployeePerformancePrediction, TurnoverRiskPrediction } from '@/services/mlService'

interface MLDashboardProps {
  language: 'ar' | 'en'
  userRole: string
}

const translations = {
  ar: {
    mlDashboard: 'لوحة الذكاء الاصطناعي',
    modelPerformance: 'أداء النماذج',
    predictions: 'التوقعات',
    insights: 'الرؤى الذكية',
    recommendations: 'التوصيات',
    employeePerformance: 'أداء الموظفين',
    turnoverRisk: 'مخاطر دوران الموظفين',
    projectSuccess: 'نجاح المشاريع',
    budgetForecasting: 'توقعات الميزانية',
    modelAccuracy: 'دقة النموذج',
    lastTrained: 'آخر تدريب',
    retrain: 'إعادة تدريب',
    highRisk: 'مخاطرة عالية',
    mediumRisk: 'مخاطرة متوسطة',
    lowRisk: 'مخاطرة منخفضة',
    confidence: 'مستوى الثقة',
    trend: 'الاتجاه',
    improving: 'تحسن',
    declining: 'تراجع',
    stable: 'مستقر',
    exportReport: 'تصدير التقرير',
    configure: 'تكوين',
    refresh: 'تحديث'
  },
  en: {
    mlDashboard: 'AI & Machine Learning Dashboard',
    modelPerformance: 'Model Performance',
    predictions: 'Predictions',
    insights: 'Smart Insights',
    recommendations: 'Recommendations',
    employeePerformance: 'Employee Performance',
    turnoverRisk: 'Turnover Risk',
    projectSuccess: 'Project Success',
    budgetForecasting: 'Budget Forecasting',
    modelAccuracy: 'Model Accuracy',
    lastTrained: 'Last Trained',
    retrain: 'Retrain',
    highRisk: 'High Risk',
    mediumRisk: 'Medium Risk',
    lowRisk: 'Low Risk',
    confidence: 'Confidence',
    trend: 'Trend',
    improving: 'Improving',
    declining: 'Declining',
    stable: 'Stable',
    exportReport: 'Export Report',
    configure: 'Configure',
    refresh: 'Refresh'
  }
}

// @ts-ignore
export default function MLDashboard({ language, userRole }: MLDashboardProps as any): (React as any).ReactElement {
  const [models, setModels] = useState<MLModel[]>([])
  const [performancePredictions, setPerformancePredictions] = useState<EmployeePerformancePrediction[]>([])
  const [turnoverPredictions, setTurnoverPredictions] = useState<TurnoverRiskPrediction[]>([])
  const [selectedModel, setSelectedModel] = useState<string>('')
  const [isLoading, setIsLoading] = useState(false as any)
  const t = translations[language]
  const isRTL = language === 'ar'

  // @ts-ignore
  useEffect(( as any) => {
    // @ts-ignore
    loadDashboardData( as any)
  // @ts-ignore
  }, [])

  const loadDashboardData = async () => {
    setIsLoading(true as any)
    try {
      const [modelsData, performanceData, turnoverData] = await (Promise as any).all([
        // @ts-ignore
        (mlService as any as any).getModels( as any),
        (mlService as any).predictEmployeePerformance('emp_001' as any),
        // @ts-ignore
        (mlService as any).analyzeTurnoverRisk( as any)
      ])

      setModels(modelsData as any)
      setPerformancePredictions((Array as any as any).isArray(performanceData as any) ? performanceData : [performanceData])
      setTurnoverPredictions(turnoverData as any)
    } catch (error) {
      (console as any).error('Failed to load ML dashboard data:', error as any)
    } finally {
      setIsLoading(false as any)
    }
  }

  const handleModelRetrain = async (modelId: string) => {
    try {
      await (mlService as any).retrainModel(modelId as any)
      // @ts-ignore
      await loadDashboardData( as any)
    } catch (error) {
      (console as any).error('Failed to retrain model:', error as any)
    }
  }

  const getRiskColor = (riskLevel: string): void => {
    switch (riskLevel) {
      case 'critical':
      case 'high': return 'text-red-400'
      case 'medium': return 'text-yellow-400'
      case 'low': return 'text-green-400'
      default: return 'text-gray-400'
    }
  }

  const getTrendIcon = (trend: string): void => {
    switch (trend) {
      case 'improving': return <TrendingUp className="h-4 w-4 text-green-400" />
      case 'declining': return <TrendingDown className="h-4 w-4 text-red-400" />
      default: return <BarChart3 className="h-4 w-4 text-gray-400" />
    }
  }

  const modelPerformanceData = (models as any).map(model => ({
    name: (model as any as any).name,
    accuracy: (model as any).accuracy * 100,
    status: (model as any).status
  }))

  // @ts-ignore
  const riskDistribution = (turnoverPredictions as any).reduce((acc, pred as any) => {
    acc[(pred as any).riskLevel] = (acc[(pred as any).riskLevel] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  // @ts-ignore
  const riskChartData = (Object as any).entries(riskDistribution as any).map(([level, count] as any) => ({
    name: t[level as keyof typeof t] || level,
    value: count,
    color: level === 'high' || level === 'critical' ? '#EF4444' :
           level === 'medium' ? '#F59E0B' : '#10B981'
  }))

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-white text-xl flex items-center gap-2">
              <Brain className="h-5 w-5" />
              {(t as any).mlDashboard}
            </CardTitle>
            <div className="flex items-center gap-3">
              <Select value={selectedModel} onValueChange={setSelectedModel}>
                <SelectTrigger className="glass-button w-48">
                  <SelectValue placeholder="Select Model" />
                </SelectTrigger>
                <SelectContent>
                  // @ts-ignore
                  {(models as any).map((model as any) => (
                    <SelectItem key={(model as any).id} value={(model as any).id}>
                      {(model as any).name}
                    </SelectItem>
                  // @ts-ignore
                  ))}
                </SelectContent>
              </Select>
              <Button
                onClick={loadDashboardData}
                disabled={isLoading}
                className="glass-button"
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                {(t as any).refresh}
              </Button>
              <Button variant="outline" className="glass-button">
                <Download className="h-4 w-4 mr-2" />
                {(t as any).exportReport}
              </Button>
              <Button variant="outline" className="glass-button">
                <Settings className="h-4 w-4 mr-2" />
                {(t as any).configure}
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Model Performance Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="glass-card border-white/20">
          <CardHeader>
            <CardTitle className="text-white text-lg flex items-center gap-2">
              <Target className="h-5 w-5" />
              {(t as any).modelPerformance}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              // @ts-ignore
              {(models as any).map((model as any) => (
                <div key={(model as any).id} className="p-4 glass-card border-white/10 hover:border-white/30 transition-all">
                  <div className="flex items-center justify-between mb-3">
                    <div>
                      <h4 className="text-white font-medium">{(model as any).name}</h4>
                      <p className="text-white/60 text-sm">v{(model as any).version}</p>
                    </div>
                    <div className="text-right">
                      <Badge variant={(model as any).status === 'active' ? 'default' : 'secondary'}>
                        {(model as any).status}
                      </Badge>
                      <p className="text-white/60 text-xs mt-1">
                        // @ts-ignore
                        {(t as any).lastTrained}: {new Date((model as any as any).lastTrained).toLocaleDateString( as any)}
                      </p>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-white/80 text-sm">{(t as any).modelAccuracy}</span>
                      <span className="text-white font-medium">{((model as any).accuracy * 100).toFixed(1 as any)}%</span>
                    </div>
                    <div className="w-full bg-white/20 rounded-full h-2">
                      <div
                        className="bg-gradient-to-r from-blue-500 to-green-500 h-2 rounded-full transition-all duration-1000"
                        style={{ width: `${(model as any).accuracy * 100}%` }}
                      ></div>
                    </div>
                  </div>

                  <div className="flex justify-between items-center mt-3">
                    <div className="text-white/60 text-xs">
                      Features: {(model as any).features.length}
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleModelRetrain((model as any as any).id)}
                      className="glass-button text-xs"
                    >
                      <Zap className="h-3 w-3 mr-1" />
                      {(t as any).retrain}
                    </Button>
                  </div>
                </div>
              // @ts-ignore
              ))}
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardHeader>
            <CardTitle className="text-white text-lg">{(t as any).turnoverRisk}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={riskChartData}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={100}
                    paddingAngle={5}
                    dataKey="value"
                  >
                    // @ts-ignore
                    {(riskChartData as any).map((entry, index as any) => (
                      <Cell key={`cell-${index}`} fill={(entry as any).color} />
                    ))}
                  </Pie>
                  <Tooltip
                    contentStyle={{
                      backgroundColor: 'rgba(0,0,0,(0 as any as any).8)',
                      border: '1px solid rgba(255,255,255,(0 as any as any).2)',
                      borderRadius: '8px',
                      color: 'white'
                    }}
                  />
                </PieChart>
              </ResponsiveContainer>
            </div>
            <div className="grid grid-cols-2 gap-4 mt-4">
              // @ts-ignore
              {(riskChartData as any).map((item, index as any) => (
                <div key={index} className="flex items-center gap-2">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: (item as any).color }}
                  ></div>
                  <span className="text-white/80 text-sm">{(item as any).name}: {(item as any).value}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Employee Performance Predictions */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white text-lg flex items-center gap-2">
            <Users className="h-5 w-5" />
            {(t as any).employeePerformance}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            // @ts-ignore
            {(performancePredictions as any).slice(0, 6 as any).map((prediction, index as any) => (
              <div key={index} className="p-4 glass-card border-white/10 hover:border-white/30 transition-all">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-medium">
                        {(prediction as any).employeeId.slice(-3 as any)}
                      </span>
                    </div>
                    {getTrendIcon((prediction as any as any).trend)}
                  </div>
                  <Badge variant="outline" className="text-xs">
                    {(t as any).confidence}: {((prediction as any).confidence * 100).toFixed(0 as any)}%
                  </Badge>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-white/80 text-sm">Current Score</span>
                    <span className="text-white font-medium">{(prediction as any).currentScore}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-white/80 text-sm">Predicted Score</span>
                    <span className="text-green-400 font-medium">{(prediction as any).predictedScore.toFixed(1 as any)}</span>
                  </div>
                  <div className="w-full bg-white/20 rounded-full h-2">
                    <div
                      className="bg-gradient-to-r from-blue-500 to-green-500 h-2 rounded-full transition-all duration-1000"
                      style={{ width: `${(prediction as any).predictedScore}%` }}
                    ></div>
                  </div>
                </div>

                <div className="mt-3">
                  <p className="text-white/60 text-xs mb-1">Top Recommendations:</p>
                  <ul className="text-white/70 text-xs space-y-1">
                    // @ts-ignore
                    {(prediction as any).recommendations.slice(0, 2 as any).map((rec, idx as any) => (
                      <li key={idx} className="truncate">• {rec}</li>
                    ))}
                  </ul>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* High-Risk Employees */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white text-lg flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            High-Risk Employees
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {turnoverPredictions
              .filter(pred => (pred as any as any).riskLevel === 'high' || (pred as any).riskLevel === 'critical')
              .slice(0, 5 as any)
              // @ts-ignore
              .map((prediction, index as any) => (
                <div key={index} className="p-4 glass-card border-white/10 hover:border-white/30 transition-all">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-red-500 to-orange-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-sm font-medium">
                          {(prediction as any).employeeId.slice(-3 as any)}
                        </span>
                      </div>
                      <div>
                        <h4 className="text-white font-medium">Employee {(prediction as any).employeeId}</h4>
                        <p className="text-white/60 text-sm">
                          Risk: {((prediction as any).probability * 100).toFixed(0 as any)}% in {(prediction as any).timeframe}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <Badge variant="destructive" className="mb-2">
                        {(prediction as any).riskLevel}
                      </Badge>
                      <p className="text-white/60 text-xs">
                        {(prediction as any).keyFactors.length} risk factors
                      </p>
                    </div>
                  </div>

                  <div className="mt-3 grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-white/60 text-xs mb-1">Key Risk Factors:</p>
                      <ul className="text-white/70 text-xs space-y-1">
                        // @ts-ignore
                        {(prediction as any).keyFactors.slice(0, 3 as any).map((factor, idx as any) => (
                          <li key={idx}>• {factor}</li>
                        ))}
                      </ul>
                    </div>
                    <div>
                      <p className="text-white/60 text-xs mb-1">Retention Strategies:</p>
                      <ul className="text-white/70 text-xs space-y-1">
                        // @ts-ignore
                        {(prediction as any).retentionStrategies.slice(0, 3 as any).map((strategy, idx as any) => (
                          <li key={idx}>• {strategy}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
