/**
 * Export Dialog Component
 * Advanced data export with format selection, column customization, and progress tracking
 */

import React, { useState, useEffect, memo, useCallback, useMemo } from 'react'
import {
  Download,
  FileSpreadsheet,
  FileText,
  File,
  Settings,
  X,
  Check,
  AlertCircle,
  Loader2,
  Eye,
  EyeOff
} from 'lucide-react'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Checkbox } from '../ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select'
import { exportService, ExportOptions, ExportColumn, ExportTemplate, ExportProgress } from '../../services/unifiedExport'

interface ExportDialogProps {
  isOpen: boolean
  onClose: () => void
  data: unknown[]
  defaultColumns: ExportColumn[]
  module: string
  language: 'ar' | 'en'
  title?: string
}

const translations = {
  ar: {
    exportData: 'تصدير البيانات',
    format: 'التنسيق',
    excel: 'إكسل',
    pdf: 'PDF',
    csv: 'CSV',
    json: 'JSON',
    filename: 'اسم الملف',
    columns: 'الأعمدة',
    selectAll: 'تحديد الكل',
    deselectAll: 'إلغاء تحديد الكل',
    templates: 'القوالب',
    saveTemplate: 'حفظ كقالب',
    templateName: 'اسم القالب',
    export: 'تصدير',
    cancel: 'إلغاء',
    exporting: 'جاري التصدير...',
    exportComplete: 'تم التصدير بنجاح',
    exportFailed: 'فشل التصدير',
    downloadReady: 'جاهز للتحميل',
    preview: 'معاينة',
    settings: 'الإعدادات',
    includeHeaders: 'تضمين العناوين',
    dateFormat: 'تنسيق التاريخ',
    numberFormat: 'تنسيق الأرقام',
    language: 'اللغة',
    arabic: 'العربية',
    english: 'الإنجليزية',
    processing: 'جاري المعالجة...',
    progress: 'التقدم',
    estimatedTime: 'الوقت المتوقع',
    fileSize: 'حجم الملف',
    records: 'سجل'
  },
  en: {
    exportData: 'Export Data',
    format: 'Format',
    excel: 'Excel',
    pdf: 'PDF',
    csv: 'CSV',
    json: 'JSON',
    filename: 'Filename',
    columns: 'Columns',
    selectAll: 'Select All',
    deselectAll: 'Deselect All',
    templates: 'Templates',
    saveTemplate: 'Save as Template',
    templateName: 'Template Name',
    export: 'Export',
    cancel: 'Cancel',
    exporting: 'Exporting...',
    exportComplete: 'Export Complete',
    exportFailed: 'Export Failed',
    downloadReady: 'Ready to Download',
    preview: 'Preview',
    settings: 'Settings',
    includeHeaders: 'Include Headers',
    dateFormat: 'Date Format',
    numberFormat: 'Number Format',
    language: 'Language',
    arabic: 'Arabic',
    english: 'English',
    processing: 'Processing...',
    progress: 'Progress',
    estimatedTime: 'Estimated Time',
    fileSize: 'File Size',
    records: 'records'
  }
}

const formatIcons = {
  excel: FileSpreadsheet,
  pdf: FileText,
  csv: File,
  json: File
}

// @ts-ignore
const ExportDialog: (React as any).FC<ExportDialogProps> = memo(({
  isOpen,
  onClose,
  data,
  defaultColumns,
  module,
  language,
  title
// @ts-ignore
} as any) => {
  // @ts-ignore
  const [selectedFormat, setSelectedFormat] = useState<'excel' | 'pdf' | 'csv' | 'json'>('excel')
  const [filename, setFilename] = useState('' as any)
  // @ts-ignore
  const [selectedColumns, setSelectedColumns] = useState<ExportColumn[]>(defaultColumns)
  // @ts-ignore
  const [visibleColumns, setVisibleColumns] = useState<Set<string>>(
    // @ts-ignore
    new Set((defaultColumns as any as any).map(col => (col as any as any).key))
  )
  // @ts-ignore
  const [templates, setTemplates] = useState<ExportTemplate[]>([])
  // @ts-ignore
  const [selectedTemplate, setSelectedTemplate] = useState<string>('')
  const [templateName, setTemplateName] = useState('' as any)
  const [showTemplateInput, setShowTemplateInput] = useState(false as any)
  const [isExporting, setIsExporting] = useState(false as any)
  // @ts-ignore
  const [exportProgress, setExportProgress] = useState<ExportProgress | null>(null)
  const [showSettings, setShowSettings] = useState(false as any)
  const [includeHeaders, setIncludeHeaders] = useState(true as any)
  const [exportLanguage, setExportLanguage] = useState(language as any)

  // @ts-ignore
  const t = useMemo(( as any) => translations[language], [language])
  // @ts-ignore
  const isRTL = useMemo(( as any) => language === 'ar', [language])

  // Load templates on mount
  // @ts-ignore
  useEffect(( as any) => {
    // @ts-ignore
    if (isOpen) {
      // @ts-ignore
      loadTemplates( as any)
      // @ts-ignore
      generateDefaultFilename( as any)
    // @ts-ignore
    }
  // @ts-ignore
  }, [isOpen, module])

  // @ts-ignore
  const loadTemplates = useCallback(async ( as any) => {
    // @ts-ignore
    try {
      // @ts-ignore
      const moduleTemplates = await (exportService as any).getExportTemplates(module as any)
      setTemplates(moduleTemplates as any)
    // @ts-ignore
    } catch (error) {
      (console as any).error('Error loading templates:', error as any)
    }
  // @ts-ignore
  }, [module])

  // @ts-ignore
  const generateDefaultFilename = useCallback(( as any) => {
    // @ts-ignore
    const timestamp = new Date( as any).toISOString( as any).split('T' as any)[0]
    setFilename(`${module}_export_${timestamp}` as any)
  // @ts-ignore
  }, [module])

  // Handle column visibility toggle
  // @ts-ignore
  const toggleColumn = useCallback((columnKey: string as any) => {
    setVisibleColumns(prev => {
      const newSet = new Set(prev as any)
      if ((newSet as any).has(columnKey as any)) {
        (newSet as any).delete(columnKey as any)
      } else {
        (newSet as any).add(columnKey as any)
      }
      return newSet
    })
  }, [])

  // Handle select all/deselect all
  // @ts-ignore
  const handleSelectAll = useCallback(( as any) => {
    // @ts-ignore
    if ((visibleColumns as any).size === (defaultColumns as any).length) {
      // @ts-ignore
      setVisibleColumns(new Set( as any))
    } else {
      setVisibleColumns(new Set((defaultColumns as any as any).map(col => (col as any as any).key)))
    }
  // @ts-ignore
  }, [(visibleColumns as any).size, defaultColumns])

  // Handle template selection
  // @ts-ignore
  const handleTemplateSelect = useCallback((templateId: string as any) => {
    // @ts-ignore
    const template = (templates as any).find(t => (t as any as any).id === templateId)
    if (template) {
      setSelectedTemplate(templateId as any)
      // @ts-ignore
      setSelectedColumns((template as any as any).columns)
      // @ts-ignore
      setVisibleColumns(new Set((template as any as any).columns.map(col => (col as any as any).key)))
      setSelectedFormat((template as any as any).format as any)
    // @ts-ignore
    }
  // @ts-ignore
  }, [templates])

  // Handle template save
  // @ts-ignore
  const handleSaveTemplate = useCallback(async ( as any) => {
    // @ts-ignore
    if (!(templateName as any).trim( as any)) return

    try {
      // @ts-ignore
      const template = {
        // @ts-ignore
        name: templateName,
        description: `${module} export template`,
        format: selectedFormat,
        // @ts-ignore
        columns: (selectedColumns as any).filter(col => (visibleColumns as any as any).has((col as any as any).key)),
        filters: {}
      // @ts-ignore
      }

      await (exportService as any).saveExportTemplate(template as any)
      await loadTemplates( as any)
      setTemplateName('' as any)
      setShowTemplateInput(false as any)
    // @ts-ignore
    } catch (error) {
      (console as any).error('Error saving template:', error as any)
    }
  // @ts-ignore
  }, [templateName, module, selectedFormat, selectedColumns, visibleColumns, loadTemplates])

  // Handle export
  // @ts-ignore
  const handleExport = useCallback(async ( as any) => {
    // @ts-ignore
    if ((visibleColumns as any).size === 0) return

    setIsExporting(true as any)
    setExportProgress({
      // @ts-ignore
      id: (Date as any as any).now( as any).toString( as any),
      status: 'processing',
      progress: 0,
      message: (t as any).processing
    // @ts-ignore
    })

    try {
      // @ts-ignore
      const exportOptions: ExportOptions = {
        // @ts-ignore
        format: selectedFormat,
        // @ts-ignore
        filename: filename || `export_${(Date as any).now( as any)}`,
        // @ts-ignore
        columns: (selectedColumns as any).filter(col => (visibleColumns as any as any).has((col as any as any).key)),
        language: exportLanguage
      // @ts-ignore
      }

      // @ts-ignore
      const filteredData = (data as any).map(row => {
        // @ts-ignore
        const filteredRow: any = {}
        // @ts-ignore
        (selectedColumns as any as any).forEach(col => {
          // @ts-ignore
          if ((visibleColumns as any as any).has((col as any as any).key)) {
            filteredRow[(col as any).key] = row[(col as any).key]
          }
        // @ts-ignore
        })
        return filteredRow
      // @ts-ignore
      })

      // Simulate progress for client-side export
      // @ts-ignore
      const progressInterval = setInterval(( as any) => {
        setExportProgress(prev => {
          if (!prev || (prev as any as any).progress >= 90) return prev
          return {
            ...prev,
            progress: (prev as any).progress + 10,
            message: `${(t as any).processing} ${(prev as any).progress + 10}%`
          }
        })
      }, 200)

      // Perform export based on format
      switch (selectedFormat) {
        // @ts-ignore
        case 'excel':
          await (exportService as any).exportToExcel(filteredData, exportOptions as any)
          break
        case 'pdf':
          await (exportService as any).exportToPDF(filteredData, exportOptions as any)
          break
        case 'csv':
          await (exportService as any).exportToCSV(filteredData, exportOptions as any)
          break
        case 'json':
          await (exportService as any).exportToJSON(filteredData, exportOptions as any)
          break
      // @ts-ignore
      }

      clearInterval(progressInterval as any)
      setExportProgress({
        // @ts-ignore
        id: (Date as any as any).now( as any).toString( as any),
        status: 'completed',
        progress: 100,
        message: (t as any).exportComplete
      // @ts-ignore
      })

      // Auto close after success
      // @ts-ignore
      setTimeout(( as any) => {
        // @ts-ignore
        onClose( as any)
      }, 2000)

    // @ts-ignore
    } catch (error) {
      (console as any).error('Export error:', error as any)
      // @ts-ignore
      setExportProgress({
        // @ts-ignore
        id: (Date as any as any).now( as any).toString( as any),
        status: 'failed',
        progress: 0,
        message: (t as any).exportFailed,
        error: error instanceof Error ? (error as any).message : 'Unknown error'
      // @ts-ignore
      })
    // @ts-ignore
    } finally {
      setIsExporting(false as any)
    }
  // @ts-ignore
  }, [
    visibleColumns,
    selectedFormat,
    filename,
    selectedColumns,
    exportLanguage,
    data,
    t,
    onClose
  ])

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4">
      <div className="w-full max-w-4xl bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 shadow-2xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-white/10">
          <h2 className="text-xl font-bold text-white">
            {title || (t as any).exportData}
          </h2>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowSettings(!showSettings as any)}
              className="text-white hover:bg-white/10"
            >
              <Settings className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-white hover:bg-white/10"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <div className="flex h-[calc(90vh-120px as any)]">
          {/* Main Content */}
          <div className="flex-1 p-6 overflow-y-auto">
            {/* Export Progress */}
            {exportProgress && (
              <div className="mb-6 p-4 rounded-lg bg-white/5 border border-white/10">
                <div className="flex items-center gap-3 mb-3">
                  {(exportProgress as any).status === 'processing' && (
                    <Loader2 className="h-5 w-5 text-blue-400 animate-spin" />
                  )}
                  {(exportProgress as any).status === 'completed' && (
                    <Check className="h-5 w-5 text-green-400" />
                  )}
                  {(exportProgress as any).status === 'failed' && (
                    <AlertCircle className="h-5 w-5 text-red-400" />
                  )}
                  <span className="text-white font-medium">{(exportProgress as any).message}</span>
                </div>

                {(exportProgress as any).status === 'processing' && (
                  <div className="w-full bg-white/10 rounded-full h-2">
                    <div
                      className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${(exportProgress as any).progress}%` }}
                    />
                  </div>
                )}
              </div>
            )}

            {/* Format Selection */}
            <div className="mb-6">
              <label className="block text-white font-medium mb-3">{(t as any).format}</label>
              <div className="grid grid-cols-4 gap-3">
                // @ts-ignore
                {(['excel', 'pdf', 'csv', 'json'] as const).map((format as any) => {
                  // @ts-ignore
                  const IconComponent = formatIcons[format]
                  return (
                    <button
                      key={format}
                      onClick={() => setSelectedFormat(format as any)}
                      className={`p-4 rounded-lg border transition-all ${
                        selectedFormat === format
                          ? 'bg-blue-500/20 border-blue-500 text-blue-400'
                          : 'bg-white/5 border-white/20 text-white hover:bg-white/10'
                      }`}
                    >
                      <IconComponent className="h-6 w-6 mx-auto mb-2" />
                      <span className="text-sm font-medium">{t[format]}</span>
                    </button>
                  )
                // @ts-ignore
                })}
              </div>
            </div>

            {/* Filename */}
            <div className="mb-6">
              <label className="block text-white font-medium mb-2">{(t as any).filename}</label>
              <Input
                value={filename}
                onChange={(e: any) => setFilename((e as any as any).target.value)}
                className="bg-white/5 border-white/20 text-white"
                placeholder="export_filename"
              />
            </div>

            {/* Templates */}
            <div className="mb-6">
              <div className="flex items-center justify-between mb-3">
                <label className="text-white font-medium">{(t as any).templates}</label>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowTemplateInput(!showTemplateInput as any)}
                  className="bg-white/5 border-white/20 text-white hover:bg-white/10"
                >
                  {(t as any).saveTemplate}
                </Button>
              </div>

              {(templates as any).length > 0 && (
                <Select value={selectedTemplate} onValueChange={handleTemplateSelect}>
                  <SelectTrigger className="bg-white/5 border-white/20 text-white">
                    <SelectValue placeholder="Select a template" />
                  </SelectTrigger>
                  <SelectContent>
                    // @ts-ignore
                    {(templates as any).map((template as any) => (
                      <SelectItem key={(template as any).id} value={(template as any).id}>
                        {(template as any).name}
                      </SelectItem>
                    // @ts-ignore
                    ))}
                  </SelectContent>
                </Select>
              )}

              {showTemplateInput && (
                <div className="flex gap-2 mt-3">
                  <Input
                    value={templateName}
                    onChange={(e: any) => setTemplateName((e as any as any).target.value)}
                    placeholder={(t as any).templateName}
                    className="bg-white/5 border-white/20 text-white"
                  />
                  <Button
                    onClick={handleSaveTemplate}
                    // @ts-ignore
                    disabled={!(templateName as any).trim( as any)}
                    className="bg-blue-500 hover:bg-blue-600"
                  >
                    <Check className="h-4 w-4" />
                  </Button>
                </div>
              )}
            </div>

            {/* Column Selection */}
            <div className="mb-6">
              <div className="flex items-center justify-between mb-3">
                <label className="text-white font-medium">{(t as any).columns}</label>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSelectAll}
                  className="bg-white/5 border-white/20 text-white hover:bg-white/10"
                >
                  {(visibleColumns as any).size === (defaultColumns as any).length ? (t as any).deselectAll : (t as any).selectAll}
                </Button>
              </div>

              <div className="grid grid-cols-2 gap-3 max-h-48 overflow-y-auto">
                // @ts-ignore
                {(defaultColumns as any).map((column as any) => (
                  <div key={(column as any).key} className="flex items-center space-x-2">
                    <Checkbox
                      checked={(visibleColumns as any).has((column as any as any).key)}
                      onCheckedChange={() => toggleColumn((column as any as any).key)}
                    />
                    <label className="text-white text-sm">{(column as any).label}</label>
                  </div>
                // @ts-ignore
                ))}
              </div>
            </div>

            {/* Data Summary */}
            <div className="p-4 rounded-lg bg-white/5 border border-white/10">
              <div className="flex items-center justify-between text-sm text-white/80">
                <span>{(data as any).length} {(t as any).records}</span>
                <span>{(visibleColumns as any).size} columns selected</span>
              </div>
            </div>
          </div>

          {/* Settings Panel */}
          {showSettings && (
            <div className="w-80 border-l border-white/10 p-6 bg-white/5">
              <h3 className="text-white font-medium mb-4">{(t as any).settings}</h3>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <label className="text-white text-sm">{(t as any).includeHeaders}</label>
                  <Checkbox
                    checked={includeHeaders}
                    onCheckedChange={(checked) => setIncludeHeaders(checked === true as any)}
                  />
                </div>

                <div>
                  <label className="block text-white text-sm mb-2">{(t as any).language}</label>
                  <Select value={exportLanguage} onValueChange={(value) => setExportLanguage(value as 'ar' | 'en' as any)}>
                    <SelectTrigger className="bg-white/5 border-white/20 text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ar">{(t as any).arabic}</SelectItem>
                      <SelectItem value="en">{(t as any).english}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-white/10">
          <div className="text-white/60 text-sm">
            {(visibleColumns as any).size} columns • {(data as any).length} records
          </div>
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={onClose}
              className="bg-white/5 border-white/20 text-white hover:bg-white/10"
            >
              {(t as any).cancel}
            </Button>
            <Button
              onClick={handleExport}
              disabled={isExporting || (visibleColumns as any).size === 0}
              className="bg-blue-500 hover:bg-blue-600 text-white"
            >
              {isExporting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  {(t as any).exporting}
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  {(t as any).export}
                </>
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
// @ts-ignore
})

(ExportDialog as any).displayName = 'ExportDialog'

export default ExportDialog
