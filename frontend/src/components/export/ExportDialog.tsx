/**
 * Export Dialog Component
 * Advanced data export with format selection, column customization, and progress tracking
 */

import React, { useState, useEffect, memo, useCallback, useMemo } from 'react'
import {
  Download,
  FileSpreadsheet,
  FileText,
  File,
  Settings,
  X,
  Check,
  AlertCircle,
  Loader2,
  Eye,
  EyeOff
} from 'lucide-react'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Checkbox } from '../ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select'
import { exportService, ExportOptions, ExportColumn, ExportTemplate, ExportProgress } from '../../services/unifiedExport'

interface ExportDialogProps {
  isOpen: boolean
  onClose: () => void
  data: unknown[]
  defaultColumns: ExportColumn[]
  module: string
  language: 'ar' | 'en'
  title?: string
}

const translations = {
  ar: {
    exportData: 'تصدير البيانات',
    format: 'التنسيق',
    excel: 'إكسل',
    pdf: 'PDF',
    csv: 'CSV',
    json: 'JSON',
    filename: 'اسم الملف',
    columns: 'الأعمدة',
    selectAll: 'تحديد الكل',
    deselectAll: 'إلغاء تحديد الكل',
    templates: 'القوالب',
    saveTemplate: 'حفظ كقالب',
    templateName: 'اسم القالب',
    export: 'تصدير',
    cancel: 'إلغاء',
    exporting: 'جاري التصدير...',
    exportComplete: 'تم التصدير بنجاح',
    exportFailed: 'فشل التصدير',
    downloadReady: 'جاهز للتحميل',
    preview: 'معاينة',
    settings: 'الإعدادات',
    includeHeaders: 'تضمين العناوين',
    dateFormat: 'تنسيق التاريخ',
    numberFormat: 'تنسيق الأرقام',
    language: 'اللغة',
    arabic: 'العربية',
    english: 'الإنجليزية',
    processing: 'جاري المعالجة...',
    progress: 'التقدم',
    estimatedTime: 'الوقت المتوقع',
    fileSize: 'حجم الملف',
    records: 'سجل'
  },
  en: {
    exportData: 'Export Data',
    format: 'Format',
    excel: 'Excel',
    pdf: 'PDF',
    csv: 'CSV',
    json: 'JSON',
    filename: 'Filename',
    columns: 'Columns',
    selectAll: 'Select All',
    deselectAll: 'Deselect All',
    templates: 'Templates',
    saveTemplate: 'Save as Template',
    templateName: 'Template Name',
    export: 'Export',
    cancel: 'Cancel',
    exporting: 'Exporting...',
    exportComplete: 'Export Complete',
    exportFailed: 'Export Failed',
    downloadReady: 'Ready to Download',
    preview: 'Preview',
    settings: 'Settings',
    includeHeaders: 'Include Headers',
    dateFormat: 'Date Format',
    numberFormat: 'Number Format',
    language: 'Language',
    arabic: 'Arabic',
    english: 'English',
    processing: 'Processing...',
    progress: 'Progress',
    estimatedTime: 'Estimated Time',
    fileSize: 'File Size',
    records: 'records'
  }
}

const formatIcons = {
  excel: FileSpreadsheet,
  pdf: FileText,
  csv: File,
  json: File
}

const ExportDialog: React.FC<ExportDialogProps> = memo(({
  isOpen,
  onClose,
  data,
  defaultColumns,
  module,
  language,
  title
}) => {
  const [selectedFormat, setSelectedFormat] = useState<'excel' | 'pdf' | 'csv' | 'json'>('excel')
  const [filename, setFilename] = useState('')
  const [selectedColumns, setSelectedColumns] = useState<ExportColumn[]>(defaultColumns)
  const [visibleColumns, setVisibleColumns] = useState<Set<string>>(
    new Set(defaultColumns.map(col => col.key))
  )
  const [templates, setTemplates] = useState<ExportTemplate[]>([])
  const [selectedTemplate, setSelectedTemplate] = useState<string>('')
  const [templateName, setTemplateName] = useState('')
  const [showTemplateInput, setShowTemplateInput] = useState(false)
  const [isExporting, setIsExporting] = useState(false)
  const [exportProgress, setExportProgress] = useState<ExportProgress | null>(null)
  const [showSettings, setShowSettings] = useState(false)
  const [includeHeaders, setIncludeHeaders] = useState(true)
  const [exportLanguage, setExportLanguage] = useState(language)

  const t = useMemo(() => translations[language], [language])
  const isRTL = useMemo(() => language === 'ar', [language])

  // Load templates on mount
  useEffect(() => {
    if (isOpen) {
      loadTemplates()
      generateDefaultFilename()
    }
  }, [isOpen, module])

  const loadTemplates = useCallback(async () => {
    try {
      const moduleTemplates = await exportService.getExportTemplates(module)
      setTemplates(moduleTemplates)
    } catch (error) {
      console.error('Error loading templates:', error)
    }
  }, [module])

  const generateDefaultFilename = useCallback(() => {
    const timestamp = new Date().toISOString().split('T')[0]
    setFilename(`${module}_export_${timestamp}`)
  }, [module])

  // Handle column visibility toggle
  const toggleColumn = useCallback((columnKey: string) => {
    setVisibleColumns(prev => {
      const newSet = new Set(prev)
      if (newSet.has(columnKey)) {
        newSet.delete(columnKey)
      } else {
        newSet.add(columnKey)
      }
      return newSet
    })
  }, [])

  // Handle select all/deselect all
  const handleSelectAll = useCallback(() => {
    if (visibleColumns.size === defaultColumns.length) {
      setVisibleColumns(new Set())
    } else {
      setVisibleColumns(new Set(defaultColumns.map(col => col.key)))
    }
  }, [visibleColumns.size, defaultColumns])

  // Handle template selection
  const handleTemplateSelect = useCallback((templateId: string) => {
    const template = templates.find(t => t.id === templateId)
    if (template) {
      setSelectedTemplate(templateId)
      setSelectedColumns(template.columns)
      setVisibleColumns(new Set(template.columns.map(col => col.key)))
      setSelectedFormat(template.format as any)
    }
  }, [templates])

  // Handle template save
  const handleSaveTemplate = useCallback(async () => {
    if (!templateName.trim()) return

    try {
      const template = {
        name: templateName,
        description: `${module} export template`,
        format: selectedFormat,
        columns: selectedColumns.filter(col => visibleColumns.has(col.key)),
        filters: {}
      }

      await exportService.saveExportTemplate(template)
      await loadTemplates()
      setTemplateName('')
      setShowTemplateInput(false)
    } catch (error) {
      console.error('Error saving template:', error)
    }
  }, [templateName, module, selectedFormat, selectedColumns, visibleColumns, loadTemplates])

  // Handle export
  const handleExport = useCallback(async () => {
    if (visibleColumns.size === 0) return

    setIsExporting(true)
    setExportProgress({
      id: Date.now().toString(),
      status: 'processing',
      progress: 0,
      message: t.processing
    })

    try {
      const exportOptions: ExportOptions = {
        format: selectedFormat,
        filename: filename || `export_${Date.now()}`,
        columns: selectedColumns.filter(col => visibleColumns.has(col.key)),
        language: exportLanguage
      }

      const filteredData = data.map(row => {
        const filteredRow: any = {}
        selectedColumns.forEach(col => {
          if (visibleColumns.has(col.key)) {
            filteredRow[col.key] = row[col.key]
          }
        })
        return filteredRow
      })

      // Simulate progress for client-side export
      const progressInterval = setInterval(() => {
        setExportProgress(prev => {
          if (!prev || prev.progress >= 90) return prev
          return {
            ...prev,
            progress: prev.progress + 10,
            message: `${t.processing} ${prev.progress + 10}%`
          }
        })
      }, 200)

      // Perform export based on format
      switch (selectedFormat) {
        case 'excel':
          await exportService.exportToExcel(filteredData, exportOptions)
          break
        case 'pdf':
          await exportService.exportToPDF(filteredData, exportOptions)
          break
        case 'csv':
          await exportService.exportToCSV(filteredData, exportOptions)
          break
        case 'json':
          await exportService.exportToJSON(filteredData, exportOptions)
          break
      }

      clearInterval(progressInterval)
      setExportProgress({
        id: Date.now().toString(),
        status: 'completed',
        progress: 100,
        message: t.exportComplete
      })

      // Auto close after success
      setTimeout(() => {
        onClose()
      }, 2000)

    } catch (error) {
      console.error('Export error:', error)
      setExportProgress({
        id: Date.now().toString(),
        status: 'failed',
        progress: 0,
        message: t.exportFailed,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    } finally {
      setIsExporting(false)
    }
  }, [
    visibleColumns,
    selectedFormat,
    filename,
    selectedColumns,
    exportLanguage,
    data,
    t,
    onClose
  ])

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4">
      <div className="w-full max-w-4xl bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 shadow-2xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-white/10">
          <h2 className="text-xl font-bold text-white">
            {title || t.exportData}
          </h2>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowSettings(!showSettings)}
              className="text-white hover:bg-white/10"
            >
              <Settings className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-white hover:bg-white/10"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <div className="flex h-[calc(90vh-120px)]">
          {/* Main Content */}
          <div className="flex-1 p-6 overflow-y-auto">
            {/* Export Progress */}
            {exportProgress && (
              <div className="mb-6 p-4 rounded-lg bg-white/5 border border-white/10">
                <div className="flex items-center gap-3 mb-3">
                  {exportProgress.status === 'processing' && (
                    <Loader2 className="h-5 w-5 text-blue-400 animate-spin" />
                  )}
                  {exportProgress.status === 'completed' && (
                    <Check className="h-5 w-5 text-green-400" />
                  )}
                  {exportProgress.status === 'failed' && (
                    <AlertCircle className="h-5 w-5 text-red-400" />
                  )}
                  <span className="text-white font-medium">{exportProgress.message}</span>
                </div>

                {exportProgress.status === 'processing' && (
                  <div className="w-full bg-white/10 rounded-full h-2">
                    <div
                      className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${exportProgress.progress}%` }}
                    />
                  </div>
                )}
              </div>
            )}

            {/* Format Selection */}
            <div className="mb-6">
              <label className="block text-white font-medium mb-3">{t.format}</label>
              <div className="grid grid-cols-4 gap-3">
                {(['excel', 'pdf', 'csv', 'json'] as const).map((format) => {
                  const IconComponent = formatIcons[format]
                  return (
                    <button
                      key={format}
                      onClick={() => setSelectedFormat(format)}
                      className={`p-4 rounded-lg border transition-all ${
                        selectedFormat === format
                          ? 'bg-blue-500/20 border-blue-500 text-blue-400'
                          : 'bg-white/5 border-white/20 text-white hover:bg-white/10'
                      }`}
                    >
                      <IconComponent className="h-6 w-6 mx-auto mb-2" />
                      <span className="text-sm font-medium">{t[format]}</span>
                    </button>
                  )
                })}
              </div>
            </div>

            {/* Filename */}
            <div className="mb-6">
              <label className="block text-white font-medium mb-2">{t.filename}</label>
              <Input
                value={filename}
                onChange={(e) => setFilename(e.target.value)}
                className="bg-white/5 border-white/20 text-white"
                placeholder="export_filename"
              />
            </div>

            {/* Templates */}
            <div className="mb-6">
              <div className="flex items-center justify-between mb-3">
                <label className="text-white font-medium">{t.templates}</label>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowTemplateInput(!showTemplateInput)}
                  className="bg-white/5 border-white/20 text-white hover:bg-white/10"
                >
                  {t.saveTemplate}
                </Button>
              </div>

              {templates.length > 0 && (
                <Select value={selectedTemplate} onValueChange={handleTemplateSelect}>
                  <SelectTrigger className="bg-white/5 border-white/20 text-white">
                    <SelectValue placeholder="Select a template" />
                  </SelectTrigger>
                  <SelectContent>
                    {templates.map((template) => (
                      <SelectItem key={template.id} value={template.id}>
                        {template.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}

              {showTemplateInput && (
                <div className="flex gap-2 mt-3">
                  <Input
                    value={templateName}
                    onChange={(e) => setTemplateName(e.target.value)}
                    placeholder={t.templateName}
                    className="bg-white/5 border-white/20 text-white"
                  />
                  <Button
                    onClick={handleSaveTemplate}
                    disabled={!templateName.trim()}
                    className="bg-blue-500 hover:bg-blue-600"
                  >
                    <Check className="h-4 w-4" />
                  </Button>
                </div>
              )}
            </div>

            {/* Column Selection */}
            <div className="mb-6">
              <div className="flex items-center justify-between mb-3">
                <label className="text-white font-medium">{t.columns}</label>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSelectAll}
                  className="bg-white/5 border-white/20 text-white hover:bg-white/10"
                >
                  {visibleColumns.size === defaultColumns.length ? t.deselectAll : t.selectAll}
                </Button>
              </div>

              <div className="grid grid-cols-2 gap-3 max-h-48 overflow-y-auto">
                {defaultColumns.map((column) => (
                  <div key={column.key} className="flex items-center space-x-2">
                    <Checkbox
                      checked={visibleColumns.has(column.key)}
                      onCheckedChange={() => toggleColumn(column.key)}
                    />
                    <label className="text-white text-sm">{column.label}</label>
                  </div>
                ))}
              </div>
            </div>

            {/* Data Summary */}
            <div className="p-4 rounded-lg bg-white/5 border border-white/10">
              <div className="flex items-center justify-between text-sm text-white/80">
                <span>{data.length} {t.records}</span>
                <span>{visibleColumns.size} columns selected</span>
              </div>
            </div>
          </div>

          {/* Settings Panel */}
          {showSettings && (
            <div className="w-80 border-l border-white/10 p-6 bg-white/5">
              <h3 className="text-white font-medium mb-4">{t.settings}</h3>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <label className="text-white text-sm">{t.includeHeaders}</label>
                  <Checkbox
                    checked={includeHeaders}
                    onCheckedChange={(checked) => setIncludeHeaders(checked === true)}
                  />
                </div>

                <div>
                  <label className="block text-white text-sm mb-2">{t.language}</label>
                  <Select value={exportLanguage} onValueChange={(value) => setExportLanguage(value as 'ar' | 'en')}>
                    <SelectTrigger className="bg-white/5 border-white/20 text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ar">{t.arabic}</SelectItem>
                      <SelectItem value="en">{t.english}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-white/10">
          <div className="text-white/60 text-sm">
            {visibleColumns.size} columns • {data.length} records
          </div>
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={onClose}
              className="bg-white/5 border-white/20 text-white hover:bg-white/10"
            >
              {t.cancel}
            </Button>
            <Button
              onClick={handleExport}
              disabled={isExporting || visibleColumns.size === 0}
              className="bg-blue-500 hover:bg-blue-600 text-white"
            >
              {isExporting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  {t.exporting}
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  {t.export}
                </>
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
})

ExportDialog.displayName = 'ExportDialog'

export default ExportDialog
