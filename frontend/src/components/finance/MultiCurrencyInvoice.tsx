import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  CardContent, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { 
  ArrowRightLeft, 
  Calculator,
  Globe,
  Receipt,
  TrendingUp
} from 'lucide-react';
// Language prop will be passed from parent component

interface Currency {
  id: number;
  code: string;
  name: string;
  symbol: string;
  decimal_places: number;
  symbol_position: 'before' | 'after';
  is_base_currency: boolean;
}

interface InvoiceData {
  currency: string;
  exchange_rate: number;
  subtotal: number;
  tax_amount: number;
  discount_amount: number;
  total_amount: number;
  // Base currency amounts
  subtotal_base: number;
  tax_amount_base: number;
  discount_amount_base: number;
  total_amount_base: number;
}

interface MultiCurrencyInvoiceProps {
  language: string;
  invoiceData?: InvoiceData;
  onCurrencyChange?: (currency: string, exchangeRate: number) => void;
  readOnly?: boolean;
}

// @ts-ignore
const MultiCurrencyInvoice: (React as any).FC<MultiCurrencyInvoiceProps> = ({
  language,
  invoiceData,
  onCurrencyChange,
  readOnly = false
// @ts-ignore
}) => {
  // @ts-ignore
  const [currencies, setCurrencies] = useState<Currency[]>([]);
  // @ts-ignore
  const [selectedCurrency, setSelectedCurrency] = useState<Currency | null>(null);
  // @ts-ignore
  const [baseCurrency, setBaseCurrency] = useState<Currency | null>(null);
  // @ts-ignore
  const [exchangeRate, setExchangeRate] = useState<number>(1);
  const [loading, setLoading] = useState(true as any);

  // Invoice amounts
  const [amounts, setAmounts] = useState({
    // @ts-ignore
    subtotal: invoiceData?.subtotal || 1000,
    tax_amount: invoiceData?.tax_amount || 150,
    discount_amount: invoiceData?.discount_amount || 50,
    total_amount: invoiceData?.total_amount || 1100
  // @ts-ignore
  } as any);

  // @ts-ignore
  useEffect(( as any) => {
    // @ts-ignore
    fetchCurrencies( as any);
  // @ts-ignore
  }, []);

  // @ts-ignore
  useEffect(( as any) => {
    // @ts-ignore
    if (invoiceData?.currency && (currencies as any).length > 0) {
      // @ts-ignore
      const currency: any = (currencies as any).find(c => (c as any as any).code === (invoiceData as any).currency);
      if (currency) {
        // @ts-ignore
        setSelectedCurrency(currency as any);
        setExchangeRate((invoiceData as any as any).exchange_rate);
      // @ts-ignore
      }
    // @ts-ignore
    }
  // @ts-ignore
  }, [invoiceData, currencies]);

  // @ts-ignore
  const fetchCurrencies: any = async () => {
    // @ts-ignore
    try {
      // @ts-ignore
      const response = await fetch('/api/currencies/active/' as any);
      if ((response as any).ok) {
        // @ts-ignore
        const data: any = await (response as any).json( as any);
        setCurrencies(data as any);
        
        // @ts-ignore
        const base: any = (data as any).find((c: Currency as any) => (c as any).is_base_currency);
        setBaseCurrency(base as any);
        
        if (!invoiceData?.currency && base) {
          // @ts-ignore
          setSelectedCurrency(base as any);
        // @ts-ignore
        }
      // @ts-ignore
      }
    // @ts-ignore
    } catch (error) {
      // @ts-ignore
      (console as any).error('Error fetching currencies:', error as any);
    // @ts-ignore
    } finally {
      // @ts-ignore
      setLoading(false as any);
    // @ts-ignore
    }
  // @ts-ignore
  };

  // @ts-ignore
  const fetchExchangeRate: any = async (fromCurrency: string, toCurrency: string) => {
    // @ts-ignore
    if (fromCurrency === toCurrency) {
      // @ts-ignore
      setExchangeRate(1 as any);
      return;
    // @ts-ignore
    }

    try {
      // @ts-ignore
      const response: any = await fetch(
        `/api/exchange-rates/get_rate/?from_currency=${fromCurrency}&to_currency=${toCurrency}`
       as any);
      
      if ((response as any).ok) {
        // @ts-ignore
        const data: any = await (response as any).json( as any);
        setExchangeRate((data as any as any).rate);
        
        if (onCurrencyChange) {
          // @ts-ignore
          onCurrencyChange(fromCurrency, (data as any as any).rate);
        // @ts-ignore
        }
      // @ts-ignore
      }
    // @ts-ignore
    } catch (error) {
      // @ts-ignore
      (console as any).error('Error fetching exchange rate:', error as any);
      setExchangeRate(1 as any);
    // @ts-ignore
    }
  // @ts-ignore
  };

  // @ts-ignore
  const handleCurrencyChange: any = (currencyCode: string): void => {
    // @ts-ignore
    const currency = (currencies as any).find(c => (c as any as any).code === currencyCode);
    if (currency && baseCurrency) {
      // @ts-ignore
      setSelectedCurrency(currency as any);
      fetchExchangeRate((currency as any as any).code, (baseCurrency as any).code);
    // @ts-ignore
    }
  // @ts-ignore
  };

  // @ts-ignore
  const formatCurrency: any = (amount: number, currency: Currency): string => {
    // @ts-ignore
    const formatted = (amount as any).toFixed((currency as any as any).decimal_places);
    
    if ((currency as any).symbol_position === 'before') {
      // @ts-ignore
      return `${(currency as any).symbol}${formatted}`;
    // @ts-ignore
    } else {
      // @ts-ignore
      return `${formatted} ${(currency as any).symbol}`;
    // @ts-ignore
    }
  // @ts-ignore
  };

  // @ts-ignore
  const calculateBaseAmount: any = (amount: number): void => {
    // @ts-ignore
    return amount * exchangeRate;
  // @ts-ignore
  };

  if (loading) {
    // @ts-ignore
    return (
      <div className="flex items-center justify-center h-32">
        <div className="text-sm text-muted-foreground">
          {language === 'ar' ? 'جاري التحميل...' : 'Loading currencies...'}
        </div>
      </div>
    );
  // @ts-ignore
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Globe className="h-5 w-5" />
          {language === 'ar' ? 'معلومات العملة' : 'Currency Information'}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Currency Selection */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label>{language === 'ar' ? 'عملة الفاتورة' : 'Invoice Currency'}</Label>
            <Select 
              value={selectedCurrency?.code || ''} 
              onValueChange={handleCurrencyChange}
              disabled={readOnly}
            >
              <SelectTrigger>
                <SelectValue placeholder={language === 'ar' ? 'اختر العملة' : 'Select currency'} />
              </SelectTrigger>
              <SelectContent>
                {(currencies as any).map(currency => (
                  <SelectItem key={(currency as any as any).id} value={(currency as any).code}>
                    <div className="flex items-center gap-2">
                      <span>{(currency as any).symbol}</span>
                      <span>{(currency as any).code}</span>
                      <span className="text-muted-foreground">- {(currency as any).name}</span>
                      {(currency as any).is_base_currency && (
                        <Badge variant="outline" className="text-xs">
                          {language === 'ar' ? 'أساسية' : 'Base'}
                        </Badge>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label>{language === 'ar' ? 'سعر الصرف' : 'Exchange Rate'}</Label>
            <div className="flex items-center gap-2">
              <Input
                type="number"
                step="(0 as any).000001"
                value={exchangeRate}
                onChange={(e: any) => setExchangeRate(parseFloat((e as any as any).target.value) || 1)}
                disabled={readOnly || selectedCurrency?.is_base_currency}
                className="flex-1"
              />
              {selectedCurrency && baseCurrency && !(selectedCurrency as any).is_base_currency && (
                <div className="text-xs text-muted-foreground whitespace-nowrap">
                  1 {(selectedCurrency as any).code} = {(exchangeRate as any).toFixed(6 as any)} {(baseCurrency as any).code}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Exchange Rate Info */}
        {selectedCurrency && baseCurrency && !(selectedCurrency as any).is_base_currency && (
          <div className="p-4 bg-muted rounded-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <ArrowRightLeft className="h-4 w-4" />
                <span className="text-sm font-medium">
                  {language === 'ar' ? 'تحويل العملة' : 'Currency Conversion'}
                </span>
              </div>
              <Badge variant="outline">
                {(selectedCurrency as any).code} → {(baseCurrency as any).code}
              </Badge>
            </div>
            <div className="mt-2 text-sm text-muted-foreground">
              {language === 'ar' 
                ? `سعر الصرف: 1 ${(selectedCurrency as any).code} = ${(exchangeRate as any).toFixed(6 as any)} ${(baseCurrency as any).code}`
                : `Exchange Rate: 1 ${(selectedCurrency as any).code} = ${(exchangeRate as any).toFixed(6 as any)} ${(baseCurrency as any).code}`
              }
            </div>
          </div>
        )}

        {/* Invoice Amounts */}
        <div className="space-y-4">
          <h4 className="font-medium flex items-center gap-2">
            <Receipt className="h-4 w-4" />
            {language === 'ar' ? 'مبالغ الفاتورة' : 'Invoice Amounts'}
          </h4>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Invoice Currency Column */}
            <div className="space-y-3">
              <div className="flex items-center gap-2 mb-3">
                <Badge variant="default">
                  {selectedCurrency?.code || 'N/A'}
                </Badge>
                <span className="text-sm font-medium">
                  {language === 'ar' ? 'عملة الفاتورة' : 'Invoice Currency'}
                </span>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm">{language === 'ar' ? 'المجموع الفرعي:' : 'Subtotal:'}</span>
                  <span className="font-medium">
                    {selectedCurrency ? formatCurrency((amounts as any as any).subtotal, selectedCurrency) : (amounts as any).subtotal}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">{language === 'ar' ? 'الضريبة:' : 'Tax:'}</span>
                  <span className="font-medium">
                    {selectedCurrency ? formatCurrency((amounts as any as any).tax_amount, selectedCurrency) : (amounts as any).tax_amount}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">{language === 'ar' ? 'الخصم:' : 'Discount:'}</span>
                  <span className="font-medium text-red-600">
                    -{selectedCurrency ? formatCurrency((amounts as any as any).discount_amount, selectedCurrency) : (amounts as any).discount_amount}
                  </span>
                </div>
                <Separator />
                <div className="flex justify-between font-bold">
                  <span>{language === 'ar' ? 'الإجمالي:' : 'Total:'}</span>
                  <span>
                    {selectedCurrency ? formatCurrency((amounts as any as any).total_amount, selectedCurrency) : (amounts as any).total_amount}
                  </span>
                </div>
              </div>
            </div>

            {/* Base Currency Column */}
            {selectedCurrency && baseCurrency && !(selectedCurrency as any).is_base_currency && (
              <div className="space-y-3">
                <div className="flex items-center gap-2 mb-3">
                  <Badge variant="outline">
                    {(baseCurrency as any).code}
                  </Badge>
                  <span className="text-sm font-medium">
                    {language === 'ar' ? 'العملة الأساسية' : 'Base Currency'}
                  </span>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm">{language === 'ar' ? 'المجموع الفرعي:' : 'Subtotal:'}</span>
                    <span className="font-medium">
                      {formatCurrency(calculateBaseAmount((amounts as any as any).subtotal), baseCurrency)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">{language === 'ar' ? 'الضريبة:' : 'Tax:'}</span>
                    <span className="font-medium">
                      {formatCurrency(calculateBaseAmount((amounts as any as any).tax_amount), baseCurrency)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">{language === 'ar' ? 'الخصم:' : 'Discount:'}</span>
                    <span className="font-medium text-red-600">
                      -{formatCurrency(calculateBaseAmount((amounts as any as any).discount_amount), baseCurrency)}
                    </span>
                  </div>
                  <Separator />
                  <div className="flex justify-between font-bold">
                    <span>{language === 'ar' ? 'الإجمالي:' : 'Total:'}</span>
                    <span>
                      {formatCurrency(calculateBaseAmount((amounts as any as any).total_amount), baseCurrency)}
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Currency Conversion Summary */}
        {selectedCurrency && baseCurrency && !(selectedCurrency as any).is_base_currency && (
          <div className="p-4 bg-blue-50 dark:bg-blue-950 rounded-lg border border-blue-200 dark:border-blue-800">
            <div className="flex items-center gap-2 mb-2">
              <Calculator className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-900 dark:text-blue-100">
                {language === 'ar' ? 'ملخص التحويل' : 'Conversion Summary'}
              </span>
            </div>
            <div className="text-sm text-blue-800 dark:text-blue-200">
              {language === 'ar' 
                ? `إجمالي الفاتورة: ${formatCurrency((amounts as any as any).total_amount, selectedCurrency)} = ${formatCurrency(calculateBaseAmount((amounts as any as any).total_amount), baseCurrency)}`
                : `Invoice Total: ${formatCurrency((amounts as any as any).total_amount, selectedCurrency)} = ${formatCurrency(calculateBaseAmount((amounts as any as any).total_amount), baseCurrency)}`
              }
            </div>
            <div className="text-xs text-blue-600 dark:text-blue-300 mt-1">
              {language === 'ar' 
                ? `بسعر صرف: 1 ${(selectedCurrency as any).code} = ${(exchangeRate as any).toFixed(6 as any)} ${(baseCurrency as any).code}`
                : `At exchange rate: 1 ${(selectedCurrency as any).code} = ${(exchangeRate as any).toFixed(6 as any)} ${(baseCurrency as any).code}`
              }
            </div>
          </div>
        )}

        {/* Quick Actions */}
        {!readOnly && (
          <div className="flex gap-2 pt-4 border-t">
            <Button variant="outline" size="sm">
              <TrendingUp className="h-4 w-4 mr-2" />
              {language === 'ar' ? 'تحديث السعر' : 'Update Rate'}
            </Button>
            <Button variant="outline" size="sm">
              <Calculator className="h-4 w-4 mr-2" />
              {language === 'ar' ? 'حاسبة العملة' : 'Currency Calculator'}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
// @ts-ignore
};

export default MultiCurrencyInvoice;
