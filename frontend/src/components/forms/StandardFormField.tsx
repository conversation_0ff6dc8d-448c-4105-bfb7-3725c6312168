/**
 * VALIDATION FIX: Standardized Form Field Component
 * Provides consistent form field rendering with validation and error handling
 */

import React from 'react'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { AlertCircle, CheckCircle } from 'lucide-react'

export interface FormFieldOption {
  value: string | number
  label: string
  labelAr?: string
}

export interface StandardFormFieldProps {
  name: string
  label: string
  labelAr?: string
  type?: 'text' | 'email' | 'number' | 'select' | 'textarea' | 'date' | 'tel' | 'password'
  value: string | number
  onChange: (value: string | number) => void
  onBlur?: () => void
  error?: string
  required?: boolean
  placeholder?: string
  placeholderAr?: string
  options?: FormFieldOption[]
  disabled?: boolean
  loading?: boolean
  language?: 'ar' | 'en'
  className?: string
  rows?: number
  min?: number
  max?: number
  step?: number
  pattern?: string
  autoComplete?: string
  showValidationIcon?: boolean
}

export function StandardFormField({
  name,
  label,
  labelAr,
  type = 'text',
  value,
  onChange,
  onBlur,
  error,
  required = false,
  placeholder,
  placeholderAr,
  options = [],
  disabled = false,
  loading = false,
  language = 'en',
  className = '',
  rows = 3,
  min,
  max,
  step,
  pattern,
  autoComplete,
  showValidationIcon = true
}: StandardFormFieldProps): React.ReactElement {
  const isRTL = language === 'ar'
  const displayLabel = language === 'ar' && labelAr ? labelAr : label
  const displayPlaceholder = language === 'ar' && placeholderAr ? placeholderAr : placeholder
  
  // Determine validation state
  const hasError = !!error
  const hasValue = value !== '' && value !== null && value !== undefined
  const isValid = hasValue && !hasError

  // Base input classes
  const baseInputClasses = `
    glass-input transition-all duration-200
    ${hasError ? 'border-red-500 focus:border-red-500 focus:ring-red-500/20' : ''}
    ${isValid && showValidationIcon ? 'border-green-500 focus:border-green-500 focus:ring-green-500/20' : ''}
    ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
    ${isRTL ? 'text-right' : 'text-left'}
    ${className}
  `.trim()

  // Handle input change
  const handleChange = (newValue: string | number): void => {
    if (type === 'number') {
      const numValue = newValue === '' ? '' : Number(newValue)
      onChange(numValue)
    } else {
      onChange(newValue)
    }
  }

  // Render validation icon
  const renderValidationIcon = (): void => {
    if (!showValidationIcon) return null
    
    if (hasError) {
      return (
        <AlertCircle className="absolute top-1/2 transform -translate-y-1/2 right-3 h-4 w-4 text-red-500" />
      )
    }
    
    if (isValid) {
      return (
        <CheckCircle className="absolute top-1/2 transform -translate-y-1/2 right-3 h-4 w-4 text-green-500" />
      )
    }
    
    return null
  }

  // Render input based on type
  const renderInput = (): void => {
    switch (type) {
      case 'select':
        return (
          <div className="relative">
            <Select
              value={String(value)}
              onValueChange={handleChange}
              disabled={disabled || loading}
            >
              <SelectTrigger className={baseInputClasses}>
                <SelectValue placeholder={displayPlaceholder} />
              </SelectTrigger>
              <SelectContent className="glass-dropdown">
                {options.map((option) => (
                  <SelectItem key={option.value} value={String(option.value)}>
                    {language === 'ar' && option.labelAr ? option.labelAr : option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {renderValidationIcon()}
          </div>
        )

      case 'textarea':
        return (
          <div className="relative">
            <Textarea
              id={name}
              name={name}
              value={String(value)}
              onChange={(e) => handleChange(e.target.value)}
              onBlur={onBlur}
              placeholder={displayPlaceholder}
              disabled={disabled || loading}
              rows={rows}
              className={baseInputClasses}
              dir={isRTL ? 'rtl' : 'ltr'}
            />
            {renderValidationIcon()}
          </div>
        )

      default:
        return (
          <div className="relative">
            <Input
              id={name}
              name={name}
              type={type}
              value={String(value)}
              onChange={(e) => handleChange(e.target.value)}
              onBlur={onBlur}
              placeholder={displayPlaceholder}
              disabled={disabled || loading}
              className={baseInputClasses}
              min={min}
              max={max}
              step={step}
              pattern={pattern}
              autoComplete={autoComplete}
              dir={isRTL ? 'rtl' : 'ltr'}
            />
            {renderValidationIcon()}
          </div>
        )
    }
  }

  return (
    <div className={`space-y-2 ${isRTL ? 'text-right' : 'text-left'}`}>
      {/* Label */}
      <Label 
        htmlFor={name} 
        className={`text-white font-medium ${required ? 'after:content-["*"] after:text-red-400 after:ml-1' : ''}`}
      >
        {displayLabel}
      </Label>

      {/* Input */}
      {renderInput()}

      {/* Error Message */}
      {hasError && (
        <div className="flex items-center gap-2 text-red-400 text-sm">
          <AlertCircle className="h-4 w-4 flex-shrink-0" />
          <span>{error}</span>
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <div className="text-blue-400 text-sm">
          {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
        </div>
      )}
    </div>
  )
}

// VALIDATION FIX: Form section component for better organization
export interface FormSectionProps {
  title: string
  titleAr?: string
  language?: 'ar' | 'en'
  children: React.ReactNode
  className?: string
}

export function FormSection({
  title,
  titleAr,
  language = 'en',
  children,
  className = ''
}: FormSectionProps): React.ReactElement {
  const displayTitle = language === 'ar' && titleAr ? titleAr : title
  const isRTL = language === 'ar'

  return (
    <div className={`space-y-4 ${className}`}>
      <h3 className={`text-lg font-semibold text-white border-b border-white/20 pb-2 ${isRTL ? 'text-right' : 'text-left'}`}>
        {displayTitle}
      </h3>
      <div className="space-y-4">
        {children}
      </div>
    </div>
  )
}

// VALIDATION FIX: Form grid component for responsive layouts
export interface FormGridProps {
  children: React.ReactNode
  columns?: 1 | 2 | 3 | 4
  className?: string
}

export function FormGrid({
  children,
  columns = 2,
  className = ''
}: FormGridProps): React.ReactElement {
  const gridClasses = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'
  }

  return (
    <div className={`grid ${gridClasses[columns]} gap-4 ${className}`}>
      {children}
    </div>
  )
}

export default StandardFormField
