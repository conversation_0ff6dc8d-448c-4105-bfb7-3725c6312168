import React from 'react';
import { <PERSON> } from 'react-router-dom'
import { But<PERSON> } from '../ui/button'
import { ArrowLeft, Globe, Menu, X, Play } from 'lucide-react'

interface HowItWorksHeaderProps {
  language: 'ar' | 'en'
  setLanguage: (lang: 'ar' | 'en') => void
  mobileMenuOpen: boolean
  setMobileMenuOpen: (open: boolean) => void
}

const translations = {
  ar: {
    appName: 'نمو',
    backToHome: 'العودة للرئيسية',
    howItWorks: 'كيف يعمل النظام',
    subtitle: 'اكتشف كيف يعمل نظام إدارة المؤسسات الشامل',
    description: 'تعرف على جميع ميزات النظام وكيفية استخدامها لتحسين إدارة مؤسستك',
    watchDemo: 'شاهد العرض التوضيحي',
    getStarted: 'ابدأ الآن'
  },
  en: {
    appName: 'Numu',
    backToHome: 'Back to Home',
    howItWorks: 'How It Works',
    subtitle: 'Discover how our comprehensive Enterprise Management System works',
    description: 'Learn about all system features and how to use them to improve your organization management',
    watchDemo: 'Watch Demo',
    getStarted: 'Get Started'
  }
}

export default function HowItWorksHeader({
  language,
  setLanguage,
  mobileMenuOpen,
  setMobileMenuOpen
}: HowItWorksHeaderProps): React.ReactElement {
  const t = translations[language]
  const isRTL = language === 'ar'

  return (
    <>
      {/* Header */}
      <header className="relative z-50 glass-card border-b border-white/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            {/* Logo and Back Button */}
            <div className="flex items-center gap-4">
              <Link
                to="/"
                className="flex items-center gap-2 text-white/80 hover:text-white transition-colors"
              >
                <ArrowLeft className="h-5 w-5" />
                <span className="hidden sm:inline">{t.backToHome}</span>
              </Link>
              <div className="h-6 w-px bg-white/20 hidden sm:block" />
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center glow floating shadow-lg">
                  <span className="text-white text-xl font-bold">ن</span>
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-white">{t.appName}</h1>
                  <p className="text-xs text-white/60">{language === 'ar' ? 'كيف يعمل النظام' : 'How It Works'}</p>
                </div>
              </div>
            </div>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setLanguage(language === 'ar' ? 'en' : 'ar')}
                className="text-white/80 hover:text-white hover:bg-white/15 rounded-xl p-3 backdrop-blur-10"
              >
                <Globe className="h-4 w-4 mr-2" />
                {language === 'ar' ? 'English' : 'العربية'}
              </Button>

              <Button variant="outline" className="glass-button">
                <Play className="h-4 w-4 mr-2" />
                {t.watchDemo}
              </Button>

              <Link to="/login">
                <Button className="glass-button bg-gradient-to-r from-blue-500 to-purple-500">
                  {t.getStarted}
                </Button>
              </Link>
            </div>

            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              size="sm"
              className="md:hidden text-white/80 hover:text-white hover:bg-white/15 rounded-xl p-3"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              {mobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </Button>
          </div>
        </div>

        {/* Mobile Menu */}
        {mobileMenuOpen && (
          <div className="md:hidden glass-card border-t border-white/10">
            <div className="px-4 py-6 space-y-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setLanguage(language === 'ar' ? 'en' : 'ar')}
                className="w-full text-white/80 hover:text-white hover:bg-white/15 rounded-xl p-3 justify-start"
              >
                <Globe className="h-4 w-4 mr-2" />
                {language === 'ar' ? 'English' : 'العربية'}
              </Button>

              <Button variant="outline" className="w-full glass-button justify-start">
                <Play className="h-4 w-4 mr-2" />
                {t.watchDemo}
              </Button>

              <Link to="/login" className="block">
                <Button className="w-full glass-button bg-gradient-to-r from-blue-500 to-purple-500">
                  {t.getStarted}
                </Button>
              </Link>
            </div>
          </div>
        )}
      </header>

      {/* Hero Section */}
      <section className="relative z-10 pt-32 pb-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            {/* Badge with Animation */}
            <div className="inline-flex items-center gap-2 glass-card px-6 py-3 rounded-full border border-white/20 mb-8 animate-fade-in-up animation-delay-200">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-white/80 text-sm font-medium">
                {language === 'ar' ? 'دليل شامل - جميع الميزات' : 'Complete Guide - All Features'}
              </span>
            </div>

            <h1 className="text-6xl md:text-8xl font-bold text-white mb-8 leading-tight animate-fade-in-up animation-delay-400">
              <span className="inline-block animate-text-gradient bg-gradient-to-r from-white via-blue-200 to-white bg-clip-text text-transparent bg-300% animate-gradient-x">
                {t.howItWorks}
              </span>
            </h1>
            <p className="text-2xl md:text-3xl text-white/90 mb-10 max-w-5xl mx-auto leading-relaxed font-light animate-fade-in-up animation-delay-600">
              {t.subtitle}
            </p>
            <p className="text-xl text-white/70 mb-16 max-w-4xl mx-auto leading-relaxed animate-fade-in-up animation-delay-800">
              {t.description}
            </p>
          </div>
        </div>
      </section>
    </>
  )
}
