import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import {
  Globe,
  Zap,
  Database,
  Mail,
  MessageSquare,
  Calendar,
  FileText,
  DollarSign,
  ShoppingCart,
  Truck,
  BarChart3,
  Cloud,
  Shield,
  Smartphone,
  Webhook,
  Settings,
  Link,
  CheckCircle,
  Clock,
  AlertTriangle
} from 'lucide-react'

interface IntegrationCapabilitiesProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    title: 'قدرات التكامل',
    subtitle: 'اتصال سلس مع جميع أدواتك المفضلة',
    description: 'نمو يتكامل مع أكثر من 100 تطبيق وخدمة لضمان تدفق البيانات بسلاسة',

    // Categories
    communication: 'التواصل',
    productivity: 'الإنتاجية',
    finance: 'المالية',
    marketing: 'التسويق',
    development: 'التطوير',
    storage: 'التخزين',
    analytics: 'التحليلات',
    security: 'الأمان',

    // Integration Types
    apiIntegration: 'تكامل API',
    webhooks: 'Webhooks',
    sso: 'تسجيل دخول موحد',
    dataSync: 'مزامنة البيانات',
    realTime: 'الوقت الفعلي',
    scheduled: 'مجدول',

    // Status
    available: 'متاح',
    comingSoon: 'قريباً',
    beta: 'تجريبي',

    // Features
    features: 'الميزات',
    bidirectionalSync: 'مزامنة ثنائية الاتجاه',
    realTimeUpdates: 'تحديثات فورية',
    automaticBackup: 'نسخ احتياطي تلقائي',
    customMapping: 'تخصيص الحقول',
    errorHandling: 'معالجة الأخطاء',
    auditTrail: 'سجل التدقيق'
  },
  en: {
    title: 'Integration Capabilities',
    subtitle: 'Seamless connection with all your favorite tools',
    description: 'Numu integrates with 100+ applications and services to ensure smooth data flow',

    // Categories
    communication: 'Communication',
    productivity: 'Productivity',
    finance: 'Finance',
    marketing: 'Marketing',
    development: 'Development',
    storage: 'Storage',
    analytics: 'Analytics',
    security: 'Security',

    // Integration Types
    apiIntegration: 'API Integration',
    webhooks: 'Webhooks',
    sso: 'Single Sign-On',
    dataSync: 'Data Sync',
    realTime: 'Real-time',
    scheduled: 'Scheduled',

    // Status
    available: 'Available',
    comingSoon: 'Coming Soon',
    beta: 'Beta',

    // Features
    features: 'Features',
    bidirectionalSync: 'Bidirectional sync',
    realTimeUpdates: 'Real-time updates',
    automaticBackup: 'Automatic backup',
    customMapping: 'Custom field mapping',
    errorHandling: 'Error handling',
    auditTrail: 'Audit trail'
  }
}

// @ts-ignore
export default function IntegrationCapabilities({ language }: IntegrationCapabilitiesProps as any): (React as any).ReactElement {
  const t = translations[language]
  const isRTL = language === 'ar'

  const integrationCategories = {
    communication: {
      title: (t as any).communication,
      icon: MessageSquare,
      color: 'from-blue-500 to-cyan-500',
      integrations: [
        {
          name: 'Microsoft Teams',
          icon: MessageSquare,
          status: 'available',
          type: 'api',
          description: language === 'ar' ? 'تكامل مع Microsoft Teams للرسائل والاجتماعات' : 'Integration with Microsoft Teams for messaging and meetings'
        },
        {
          name: 'Slack',
          icon: MessageSquare,
          status: 'available',
          type: 'webhook',
          description: language === 'ar' ? 'إشعارات وتحديثات عبر Slack' : 'Notifications and updates via Slack'
        },
        {
          name: 'WhatsApp Business',
          icon: Smartphone,
          status: 'beta',
          type: 'api',
          description: language === 'ar' ? 'رسائل العملاء عبر WhatsApp' : 'Customer messaging via WhatsApp'
        },
        {
          name: 'Email Services',
          icon: Mail,
          status: 'available',
          type: 'smtp',
          description: language === 'ar' ? 'تكامل مع خدمات البريد الإلكتروني' : 'Integration with email services'
        }
      ]
    },
    productivity: {
      title: (t as any).productivity,
      icon: Calendar,
      color: 'from-green-500 to-emerald-500',
      integrations: [
        {
          name: 'Google Workspace',
          icon: Calendar,
          status: 'available',
          type: 'oauth',
          description: language === 'ar' ? 'مزامنة التقويم والمستندات' : 'Calendar and document sync'
        },
        {
          name: 'Microsoft 365',
          icon: FileText,
          status: 'available',
          type: 'oauth',
          description: language === 'ar' ? 'تكامل شامل مع Office 365' : 'Full Office 365 integration'
        },
        {
          name: 'Trello',
          icon: BarChart3,
          status: 'available',
          type: 'api',
          description: language === 'ar' ? 'إدارة المهام والمشاريع' : 'Task and project management'
        },
        {
          name: 'Asana',
          icon: BarChart3,
          status: 'comingSoon',
          type: 'api',
          description: language === 'ar' ? 'تتبع المشاريع والمهام' : 'Project and task tracking'
        }
      ]
    },
    finance: {
      title: (t as any).finance,
      icon: DollarSign,
      color: 'from-yellow-500 to-orange-500',
      integrations: [
        {
          name: 'QuickBooks',
          icon: DollarSign,
          status: 'available',
          type: 'api',
          description: language === 'ar' ? 'مزامنة البيانات المحاسبية' : 'Accounting data sync'
        },
        {
          name: 'Xero',
          icon: BarChart3,
          status: 'available',
          type: 'api',
          description: language === 'ar' ? 'إدارة مالية متقدمة' : 'Advanced financial management'
        },
        {
          name: 'PayPal',
          icon: DollarSign,
          status: 'available',
          type: 'api',
          description: language === 'ar' ? 'معالجة المدفوعات' : 'Payment processing'
        },
        {
          name: 'Stripe',
          icon: DollarSign,
          status: 'available',
          type: 'api',
          description: language === 'ar' ? 'بوابة دفع متقدمة' : 'Advanced payment gateway'
        }
      ]
    },
    storage: {
      title: (t as any).storage,
      icon: Cloud,
      color: 'from-purple-500 to-pink-500',
      integrations: [
        {
          name: 'Google Drive',
          icon: Cloud,
          status: 'available',
          type: 'oauth',
          description: language === 'ar' ? 'تخزين ومشاركة الملفات' : 'File storage and sharing'
        },
        {
          name: 'Dropbox',
          icon: Cloud,
          status: 'available',
          type: 'oauth',
          description: language === 'ar' ? 'مزامنة الملفات السحابية' : 'Cloud file synchronization'
        },
        {
          name: 'OneDrive',
          icon: Cloud,
          status: 'available',
          type: 'oauth',
          description: language === 'ar' ? 'تكامل مع Microsoft OneDrive' : 'Microsoft OneDrive integration'
        },
        {
          name: 'AWS S3',
          icon: Database,
          status: 'available',
          type: 'api',
          description: language === 'ar' ? 'تخزين سحابي متقدم' : 'Advanced cloud storage'
        }
      ]
    },
    analytics: {
      title: (t as any).analytics,
      icon: BarChart3,
      color: 'from-indigo-500 to-purple-500',
      integrations: [
        {
          name: 'Google Analytics',
          icon: BarChart3,
          status: 'available',
          type: 'api',
          description: language === 'ar' ? 'تحليلات الويب المتقدمة' : 'Advanced web analytics'
        },
        {
          name: 'Power BI',
          icon: BarChart3,
          status: 'available',
          type: 'api',
          description: language === 'ar' ? 'تصور البيانات والتقارير' : 'Data visualization and reports'
        },
        {
          name: 'Tableau',
          icon: BarChart3,
          status: 'comingSoon',
          type: 'api',
          description: language === 'ar' ? 'تحليلات بيانات متقدمة' : 'Advanced data analytics'
        }
      ]
    },
    security: {
      title: (t as any).security,
      icon: Shield,
      color: 'from-red-500 to-rose-500',
      integrations: [
        {
          name: 'Active Directory',
          icon: Shield,
          status: 'available',
          type: 'ldap',
          description: language === 'ar' ? 'إدارة الهوية والوصول' : 'Identity and access management'
        },
        {
          name: 'Okta',
          icon: Shield,
          status: 'available',
          type: 'saml',
          description: language === 'ar' ? 'تسجيل دخول موحد آمن' : 'Secure single sign-on'
        },
        {
          name: 'Auth0',
          icon: Shield,
          status: 'available',
          type: 'oauth',
          description: language === 'ar' ? 'إدارة المصادقة' : 'Authentication management'
        }
      ]
    }
  }

  const getStatusBadge = (status: string): void => {
    switch (status) {
      case 'available':
        return <Badge className="bg-green-500/20 text-green-400 border-green-500/30">{(t as any).available}</Badge>
      case 'comingSoon':
        return <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30">{(t as any).comingSoon}</Badge>
      case 'beta':
        return <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/30">{(t as any).beta}</Badge>
      default:
        return null
    }
  }

  const integrationFeatures = [
    {
      title: (t as any).bidirectionalSync,
      description: language === 'ar' ? 'مزامنة البيانات في الاتجاهين' : 'Two-way data synchronization',
      icon: Zap
    },
    {
      title: (t as any).realTimeUpdates,
      description: language === 'ar' ? 'تحديثات فورية للبيانات' : 'Instant data updates',
      icon: Clock
    },
    {
      title: (t as any).automaticBackup,
      description: language === 'ar' ? 'نسخ احتياطي تلقائي للبيانات' : 'Automatic data backup',
      icon: Shield
    },
    {
      title: (t as any).customMapping,
      description: language === 'ar' ? 'تخصيص ربط الحقول' : 'Custom field mapping',
      icon: Settings
    },
    {
      title: (t as any).errorHandling,
      description: language === 'ar' ? 'معالجة ذكية للأخطاء' : 'Smart error handling',
      icon: AlertTriangle
    },
    {
      title: (t as any).auditTrail,
      description: language === 'ar' ? 'سجل كامل للتغييرات' : 'Complete change log',
      icon: FileText
    }
  ]

  return (
    <div className={`space-y-8 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="text-center">
        <h2 className="text-4xl font-bold text-white mb-4">{(t as any).title}</h2>
        <p className="text-xl text-white/80 mb-2">{(t as any).subtitle}</p>
        <p className="text-white/70 max-w-3xl mx-auto">{(t as any).description}</p>
      </div>

      {/* Integration Features */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        // @ts-ignore
        {(integrationFeatures as any).map((feature, index as any) => {
          const Icon = (feature as any).icon
          return (
            <Card key={index} className="glass-card border-white/20 hover:border-white/40 transition-all duration-300">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <Icon className="h-5 w-5 text-white/80" />
                  <div>
                    <h4 className="text-white font-semibold text-sm">{(feature as any).title}</h4>
                    <p className="text-white/70 text-xs">{(feature as any).description}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Integration Categories */}
      <div className="space-y-8">
        // @ts-ignore
        {(Object as any).entries(integrationCategories as any).map(([categoryKey, category] as any) => {
          const CategoryIcon = (category as any).icon
          return (
            <div key={categoryKey}>
              <div className="flex items-center gap-3 mb-4">
                <div className={`w-8 h-8 bg-gradient-to-r ${(category as any).color} rounded-lg flex items-center justify-center`}>
                  <CategoryIcon className="h-4 w-4 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-white">{(category as any).title}</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                // @ts-ignore
                {(category as any).integrations.map((integration, index as any) => {
                  const Icon = (integration as any).icon
                  return (
                    <Card key={index} className="glass-card border-white/20 hover:border-white/40 transition-all duration-300 group">
                      <CardContent className="p-4">
                        <div className="flex items-center gap-3 mb-2">
                          <Icon className="h-6 w-6 text-white/80 group-hover:scale-110 transition-transform duration-300" />
                          <div className="flex-1">
                            <h4 className="text-white font-semibold text-sm">{(integration as any).name}</h4>
                            {getStatusBadge((integration as any as any).status)}
                          </div>
                        </div>
                        <p className="text-white/70 text-xs">{(integration as any).description}</p>
                        <div className="flex items-center gap-1 mt-2">
                          <Link className="h-3 w-3 text-white/50" />
                          <span className="text-white/50 text-xs uppercase">{(integration as any).type}</span>
                        </div>
                      </CardContent>
                    </Card>
                  )
                })}
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}
