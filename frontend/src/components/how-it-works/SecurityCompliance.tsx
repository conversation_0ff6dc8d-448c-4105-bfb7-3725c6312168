import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import {
  Shield,
  Lock,
  Eye,
  FileText,
  Database,
  Globe,
  Users,
  AlertTriangle,
  CheckCircle,
  Key,
  Fingerprint,
  Server,
  Cloud,
  Zap,
  Settings,
  Bell,
  Search,
  Clock,
  Award,
  TrendingUp
} from 'lucide-react'

interface SecurityComplianceProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    title: 'الأمان والامتثال',
    subtitle: 'حماية متقدمة وامتثال للمعايير الدولية',
    description: 'نمو يوفر أعلى مستويات الأمان وحماية البيانات مع الامتثال للمعايير الدولية',

    // Security Categories
    dataProtection: 'حماية البيانات',
    accessControl: 'التحكم في الوصول',
    compliance: 'الامتثال التنظيمي',
    monitoring: 'المراقبة والتدقيق',

    // Security Features
    encryption: 'التشفير المتقدم',
    backups: 'النسخ الاحتياطية',
    firewall: 'جدار الحماية',
    antivirus: 'مكافحة الفيروسات',

    twoFactor: 'المصادقة الثنائية',
    sso: 'تسجيل الدخول الموحد',
    roleBasedAccess: 'التحكم القائم على الأدوار',
    sessionManagement: 'إدارة الجلسات',

    gdpr: 'GDPR - اللائحة الأوروبية',
    iso27001: 'ISO 27001',
    sox: 'SOX - ساربانيس أوكسلي',
    hipaa: 'HIPAA - قانون الصحة الأمريكي',

    auditLogs: 'سجلات التدقيق',
    realTimeMonitoring: 'المراقبة الفورية',
    threatDetection: 'كشف التهديدات',
    incidentResponse: 'الاستجابة للحوادث',

    // Certifications
    certifications: 'الشهادات والمعايير',
    securityLevel: 'مستوى الأمان',
    complianceStatus: 'حالة الامتثال',

    // Benefits
    benefits: 'الفوائد',
    dataIntegrity: 'سلامة البيانات',
    riskReduction: 'تقليل المخاطر',
    regulatoryCompliance: 'الامتثال التنظيمي',
    businessContinuity: 'استمرارية الأعمال',
    customerTrust: 'ثقة العملاء',
    costEfficiency: 'كفاءة التكلفة'
  },
  en: {
    title: 'Security & Compliance',
    subtitle: 'Advanced protection and international standards compliance',
    description: 'Numu provides the highest levels of security and data protection with compliance to international standards',

    // Security Categories
    dataProtection: 'Data Protection',
    accessControl: 'Access Control',
    compliance: 'Regulatory Compliance',
    monitoring: 'Monitoring & Auditing',

    // Security Features
    encryption: 'Advanced Encryption',
    backups: 'Automated Backups',
    firewall: 'Firewall Protection',
    antivirus: 'Antivirus Protection',

    twoFactor: 'Two-Factor Authentication',
    sso: 'Single Sign-On',
    roleBasedAccess: 'Role-Based Access Control',
    sessionManagement: 'Session Management',

    gdpr: 'GDPR - European Regulation',
    iso27001: 'ISO 27001',
    sox: 'SOX - Sarbanes-Oxley',
    hipaa: 'HIPAA - Health Insurance Act',

    auditLogs: 'Audit Logs',
    realTimeMonitoring: 'Real-Time Monitoring',
    threatDetection: 'Threat Detection',
    incidentResponse: 'Incident Response',

    // Certifications
    certifications: 'Certifications & Standards',
    securityLevel: 'Security Level',
    complianceStatus: 'Compliance Status',

    // Benefits
    benefits: 'Benefits',
    dataIntegrity: 'Data Integrity',
    riskReduction: 'Risk Reduction',
    regulatoryCompliance: 'Regulatory Compliance',
    businessContinuity: 'Business Continuity',
    customerTrust: 'Customer Trust',
    costEfficiency: 'Cost Efficiency'
  }
}

export default function SecurityCompliance({ language }: SecurityComplianceProps): React.ReactElement {
  const t = translations[language]
  const isRTL = language === 'ar'

  const securityCategories = {
    dataProtection: {
      title: t.dataProtection,
      icon: Database,
      color: 'from-blue-500 to-cyan-500',
      features: [
        {
          name: t.encryption,
          icon: Lock,
          description: language === 'ar'
            ? 'تشفير AES-256 لجميع البيانات المخزنة والمنقولة'
            : 'AES-256 encryption for all stored and transmitted data',
          level: 'Enterprise',
          status: 'active'
        },
        {
          name: t.backups,
          icon: Cloud,
          description: language === 'ar'
            ? 'نسخ احتياطية تلقائية كل 6 ساعات مع استرداد فوري'
            : 'Automated backups every 6 hours with instant recovery',
          level: 'Enterprise',
          status: 'active'
        },
        {
          name: t.firewall,
          icon: Shield,
          description: language === 'ar'
            ? 'جدار حماية متقدم مع كشف التسلل'
            : 'Advanced firewall with intrusion detection',
          level: 'Enterprise',
          status: 'active'
        }
      ]
    },
    accessControl: {
      title: t.accessControl,
      icon: Key,
      color: 'from-green-500 to-emerald-500',
      features: [
        {
          name: t.twoFactor,
          icon: Fingerprint,
          description: language === 'ar'
            ? 'مصادقة ثنائية العامل مع دعم التطبيقات والرسائل'
            : 'Two-factor authentication with app and SMS support',
          level: 'Standard',
          status: 'active'
        },
        {
          name: t.sso,
          icon: Users,
          description: language === 'ar'
            ? 'تسجيل دخول موحد مع Active Directory و LDAP'
            : 'Single sign-on with Active Directory and LDAP',
          level: 'Enterprise',
          status: 'active'
        },
        {
          name: t.roleBasedAccess,
          icon: Settings,
          description: language === 'ar'
            ? 'تحكم دقيق في الصلاحيات حسب الأدوار'
            : 'Granular role-based permission control',
          level: 'Standard',
          status: 'active'
        }
      ]
    },
    compliance: {
      title: t.compliance,
      icon: Award,
      color: 'from-purple-500 to-pink-500',
      features: [
        {
          name: t.gdpr,
          icon: Globe,
          description: language === 'ar'
            ? 'امتثال كامل للائحة حماية البيانات الأوروبية'
            : 'Full compliance with European Data Protection Regulation',
          level: 'Certified',
          status: 'certified'
        },
        {
          name: t.iso27001,
          icon: Award,
          description: language === 'ar'
            ? 'معتمد وفقاً لمعيار أمان المعلومات الدولي'
            : 'Certified according to international information security standard',
          level: 'Certified',
          status: 'certified'
        },
        {
          name: t.sox,
          icon: FileText,
          description: language === 'ar'
            ? 'امتثال لقانون ساربانيس أوكسلي للشركات المدرجة'
            : 'Sarbanes-Oxley compliance for public companies',
          level: 'Enterprise',
          status: 'active'
        }
      ]
    },
    monitoring: {
      title: t.monitoring,
      icon: Eye,
      color: 'from-red-500 to-orange-500',
      features: [
        {
          name: t.auditLogs,
          icon: FileText,
          description: language === 'ar'
            ? 'سجل شامل لجميع العمليات والتغييرات'
            : 'Comprehensive log of all operations and changes',
          level: 'Standard',
          status: 'active'
        },
        {
          name: t.realTimeMonitoring,
          icon: Clock,
          description: language === 'ar'
            ? 'مراقبة فورية للأنشطة المشبوهة'
            : 'Real-time monitoring of suspicious activities',
          level: 'Enterprise',
          status: 'active'
        },
        {
          name: t.threatDetection,
          icon: AlertTriangle,
          description: language === 'ar'
            ? 'كشف التهديدات باستخدام الذكاء الاصطناعي'
            : 'AI-powered threat detection and prevention',
          level: 'Enterprise',
          status: 'active'
        }
      ]
    }
  }

  const certifications = [
    {
      name: 'ISO 27001',
      description: language === 'ar' ? 'إدارة أمان المعلومات' : 'Information Security Management',
      icon: Award,
      status: 'certified',
      validUntil: '2025'
    },
    {
      name: 'SOC 2 Type II',
      description: language === 'ar' ? 'ضوابط الأمان والتوفر' : 'Security and Availability Controls',
      icon: Shield,
      status: 'certified',
      validUntil: '2024'
    },
    {
      name: 'GDPR',
      description: language === 'ar' ? 'حماية البيانات الأوروبية' : 'European Data Protection',
      icon: Globe,
      status: 'compliant',
      validUntil: 'Ongoing'
    },
    {
      name: 'HIPAA',
      description: language === 'ar' ? 'حماية المعلومات الصحية' : 'Health Information Protection',
      icon: FileText,
      status: 'compliant',
      validUntil: 'Ongoing'
    }
  ]

  const securityMetrics = [
    {
      title: language === 'ar' ? 'وقت التشغيل' : 'Uptime',
      value: '99.9%',
      description: language === 'ar' ? 'ضمان التوفر' : 'Availability guarantee',
      icon: Clock
    },
    {
      title: language === 'ar' ? 'وقت الاستجابة' : 'Response Time',
      value: '<2s',
      description: language === 'ar' ? 'متوسط وقت الاستجابة' : 'Average response time',
      icon: Zap
    },
    {
      title: language === 'ar' ? 'الحوادث الأمنية' : 'Security Incidents',
      value: '0',
      description: language === 'ar' ? 'في آخر 12 شهر' : 'In last 12 months',
      icon: Shield
    },
    {
      title: language === 'ar' ? 'استرداد البيانات' : 'Data Recovery',
      value: '<1h',
      description: language === 'ar' ? 'وقت الاسترداد المضمون' : 'Guaranteed recovery time',
      icon: Database
    }
  ]

  const getStatusBadge = (status: string): void => {
    switch (status) {
      case 'certified':
        return <Badge className="bg-green-500/20 text-green-400 border-green-500/30">{language === 'ar' ? 'معتمد' : 'Certified'}</Badge>
      case 'compliant':
        return <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/30">{language === 'ar' ? 'متوافق' : 'Compliant'}</Badge>
      case 'active':
        return <Badge className="bg-green-500/20 text-green-400 border-green-500/30">{language === 'ar' ? 'نشط' : 'Active'}</Badge>
      default:
        return null
    }
  }

  const getLevelBadge = (level: string): void => {
    switch (level) {
      case 'Enterprise':
        return <Badge className="bg-purple-500/20 text-purple-400 border-purple-500/30">Enterprise</Badge>
      case 'Standard':
        return <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/30">Standard</Badge>
      case 'Certified':
        return <Badge className="bg-gold-500/20 text-yellow-400 border-yellow-500/30">Certified</Badge>
      default:
        return null
    }
  }

  return (
    <div className={`space-y-8 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="text-center">
        <h2 className="text-4xl font-bold text-white mb-4">{t.title}</h2>
        <p className="text-xl text-white/80 mb-2">{t.subtitle}</p>
        <p className="text-white/70 max-w-3xl mx-auto">{t.description}</p>
      </div>

      {/* Security Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {securityMetrics.map((metric, index) => {
          const Icon = metric.icon
          return (
            <Card key={index} className="glass-card border-white/20 hover:border-white/40 transition-all duration-300 text-center">
              <CardContent className="p-6">
                <Icon className="h-8 w-8 text-white/80 mx-auto mb-3" />
                <div className="text-3xl font-bold text-white mb-2">{metric.value}</div>
                <h4 className="text-white font-semibold mb-1">{metric.title}</h4>
                <p className="text-white/70 text-sm">{metric.description}</p>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Security Categories */}
      <div className="space-y-8">
        {Object.entries(securityCategories).map(([categoryKey, category]) => {
          const CategoryIcon = category.icon
          return (
            <div key={categoryKey}>
              <div className="flex items-center gap-3 mb-6">
                <div className={`w-10 h-10 bg-gradient-to-r ${category.color} rounded-lg flex items-center justify-center`}>
                  <CategoryIcon className="h-5 w-5 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-white">{category.title}</h3>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {category.features.map((feature, index) => {
                  const Icon = feature.icon
                  return (
                    <Card key={index} className="glass-card border-white/20 hover:border-white/40 transition-all duration-300 group">
                      <CardHeader>
                        <div className="flex items-center gap-3">
                          <div className={`w-10 h-10 bg-gradient-to-r ${category.color} rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                            <Icon className="h-5 w-5 text-white" />
                          </div>
                          <div className="flex-1">
                            <CardTitle className="text-white text-lg">{feature.name}</CardTitle>
                            <div className="flex gap-2 mt-1">
                              {getLevelBadge(feature.level)}
                              {getStatusBadge(feature.status)}
                            </div>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <p className="text-white/70 text-sm">{feature.description}</p>
                      </CardContent>
                    </Card>
                  )
                })}
              </div>
            </div>
          )
        })}
      </div>

      {/* Certifications */}
      <div>
        <h3 className="text-2xl font-bold text-white text-center mb-6">{t.certifications}</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {certifications.map((cert, index) => {
            const Icon = cert.icon
            return (
              <Card key={index} className="glass-card border-white/20 hover:border-white/40 transition-all duration-300 text-center">
                <CardContent className="p-6">
                  <Icon className="h-12 w-12 text-white/80 mx-auto mb-4" />
                  <h4 className="text-white font-semibold text-lg mb-2">{cert.name}</h4>
                  <p className="text-white/70 text-sm mb-3">{cert.description}</p>
                  {getStatusBadge(cert.status)}
                  <p className="text-white/50 text-xs mt-2">
                    {language === 'ar' ? 'صالح حتى' : 'Valid until'}: {cert.validUntil}
                  </p>
                </CardContent>
              </Card>
            )
          })}
        </div>
      </div>

      {/* Security Process */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white text-center">
            {language === 'ar' ? 'عملية الأمان المتكاملة' : 'Integrated Security Process'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            {[
              {
                step: 1,
                title: language === 'ar' ? 'الوقاية' : 'Prevention',
                description: language === 'ar' ? 'حماية استباقية' : 'Proactive protection',
                icon: Shield
              },
              {
                step: 2,
                title: language === 'ar' ? 'الكشف' : 'Detection',
                description: language === 'ar' ? 'كشف التهديدات' : 'Threat detection',
                icon: Eye
              },
              {
                step: 3,
                title: language === 'ar' ? 'الاستجابة' : 'Response',
                description: language === 'ar' ? 'استجابة فورية' : 'Immediate response',
                icon: Bell
              },
              {
                step: 4,
                title: language === 'ar' ? 'الاسترداد' : 'Recovery',
                description: language === 'ar' ? 'استرداد سريع' : 'Quick recovery',
                icon: Database
              },
              {
                step: 5,
                title: language === 'ar' ? 'التحسين' : 'Improvement',
                description: language === 'ar' ? 'تحسين مستمر' : 'Continuous improvement',
                icon: TrendingUp
              }
            ].map((step, index) => {
              const Icon = step.icon
              return (
                <div key={index} className="text-center">
                  <div className="w-12 h-12 bg-gradient-to-r from-red-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-3">
                    <Icon className="h-5 w-5 text-white" />
                  </div>
                  <h4 className="text-white font-semibold mb-2">{step.title}</h4>
                  <p className="text-white/70 text-sm">{step.description}</p>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
