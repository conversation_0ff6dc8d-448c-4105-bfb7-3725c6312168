import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import {
  Crown,
  Shield,
  Users,
  DollarSign,
  Building,
  ShoppingCart,
  User,
  Settings,
  BarChart3,
  FileText,
  Calendar,
  MessageSquare,
  Lock,
  Eye,
  Edit,
  Trash2,
  Plus
} from 'lucide-react'

interface UserRolesProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    title: 'أدوار المستخدمين',
    subtitle: 'نظام أدوار مرن مع صلاحيات محددة',
    description: 'كل دور له صلاحيات محددة وواجهة مخصصة لضمان الأمان والكفاءة',

    // Roles
    superAdmin: 'المدير العام',
    admin: 'المدير',
    hrManager: 'مدير الموارد البشرية',
    financeManager: 'مدير المالية',
    departmentManager: 'مدير القسم',
    salesManager: 'مدير المبيعات',
    employee: 'موظف',

    // Permissions
    permissions: 'الصلاحيات',
    fullAccess: 'وصول كامل',
    limitedAccess: 'وصول محدود',
    readOnly: 'قراءة فقط',
    noAccess: 'لا يوجد وصول',

    // Actions
    create: 'إنشاء',
    read: 'قراءة',
    update: 'تحديث',
    delete: 'حذف',

    // Features
    dashboardAccess: 'الوصول للوحة التحكم',
    userManagement: 'إدارة المستخدمين',
    systemSettings: 'إعدادات النظام',
    reports: 'التقارير',
    hrFeatures: 'ميزات الموارد البشرية',
    financeFeatures: 'الميزات المالية',
    projectManagement: 'إدارة المشاريع',
    salesFeatures: 'ميزات المبيعات',
    personalProfile: 'الملف الشخصي'
  },
  en: {
    title: 'User Roles',
    subtitle: 'Flexible Role System with Defined Permissions',
    description: 'Each role has specific permissions and customized interface to ensure security and efficiency',

    // Roles
    superAdmin: 'Super Admin',
    admin: 'Admin',
    hrManager: 'HR Manager',
    financeManager: 'Finance Manager',
    departmentManager: 'Department Manager',
    salesManager: 'Sales Manager',
    employee: 'Employee',

    // Permissions
    permissions: 'Permissions',
    fullAccess: 'Full Access',
    limitedAccess: 'Limited Access',
    readOnly: 'Read Only',
    noAccess: 'No Access',

    // Actions
    create: 'Create',
    read: 'Read',
    update: 'Update',
    delete: 'Delete',

    // Features
    dashboardAccess: 'Dashboard Access',
    userManagement: 'User Management',
    systemSettings: 'System Settings',
    reports: 'Reports',
    hrFeatures: 'HR Features',
    financeFeatures: 'Finance Features',
    projectManagement: 'Project Management',
    salesFeatures: 'Sales Features',
    personalProfile: 'Personal Profile'
  }
}

export default function UserRoles({ language }: UserRolesProps): React.ReactElement {
  const t = translations[language]
  const isRTL = language === 'ar'

  const roles = [
    {
      id: 'super_admin',
      name: t.superAdmin,
      icon: Crown,
      color: 'from-yellow-500 to-orange-500',
      level: 1,
      description: language === 'ar'
        ? 'وصول كامل لجميع ميزات النظام وإدارة المؤسسة'
        : 'Full access to all system features and organization management',
      permissions: {
        [t.dashboardAccess]: 'full',
        [t.userManagement]: 'full',
        [t.systemSettings]: 'full',
        [t.reports]: 'full',
        [t.hrFeatures]: 'full',
        [t.financeFeatures]: 'full',
        [t.projectManagement]: 'full',
        [t.salesFeatures]: 'full'
      },
      features: [
        language === 'ar' ? 'إدارة جميع المستخدمين' : 'Manage all users',
        language === 'ar' ? 'إعدادات النظام المتقدمة' : 'Advanced system settings',
        language === 'ar' ? 'تقارير شاملة' : 'Comprehensive reports',
        language === 'ar' ? 'إدارة الأمان' : 'Security management'
      ]
    },
    {
      id: 'admin',
      name: t.admin,
      icon: Shield,
      color: 'from-blue-500 to-purple-500',
      level: 2,
      description: language === 'ar'
        ? 'إدارة العمليات اليومية والموظفين'
        : 'Manage daily operations and employees',
      permissions: {
        [t.dashboardAccess]: 'full',
        [t.userManagement]: 'limited',
        [t.systemSettings]: 'limited',
        [t.reports]: 'full',
        [t.hrFeatures]: 'full',
        [t.financeFeatures]: 'limited',
        [t.projectManagement]: 'full',
        [t.salesFeatures]: 'full'
      },
      features: [
        language === 'ar' ? 'إدارة الموظفين' : 'Employee management',
        language === 'ar' ? 'إدارة الأقسام' : 'Department management',
        language === 'ar' ? 'تقارير العمليات' : 'Operations reports',
        language === 'ar' ? 'إدارة المشاريع' : 'Project management'
      ]
    },
    {
      id: 'hr_manager',
      name: t.hrManager,
      icon: Users,
      color: 'from-green-500 to-emerald-500',
      level: 3,
      description: language === 'ar'
        ? 'إدارة الموارد البشرية والموظفين'
        : 'Human resources and employee management',
      permissions: {
        [t.dashboardAccess]: 'limited',
        [t.userManagement]: 'limited',
        [t.systemSettings]: 'none',
        [t.reports]: 'limited',
        [t.hrFeatures]: 'full',
        [t.financeFeatures]: 'none',
        [t.projectManagement]: 'read',
        [t.salesFeatures]: 'none'
      },
      features: [
        language === 'ar' ? 'إدارة الإجازات' : 'Leave management',
        language === 'ar' ? 'إدارة الحضور' : 'Attendance management',
        language === 'ar' ? 'تقييم الأداء' : 'Performance evaluation',
        language === 'ar' ? 'إدارة الرواتب' : 'Payroll management'
      ]
    },
    {
      id: 'finance_manager',
      name: t.financeManager,
      icon: DollarSign,
      color: 'from-yellow-500 to-green-500',
      level: 3,
      description: language === 'ar'
        ? 'إدارة الشؤون المالية والميزانيات'
        : 'Financial affairs and budget management',
      permissions: {
        [t.dashboardAccess]: 'limited',
        [t.userManagement]: 'none',
        [t.systemSettings]: 'none',
        [t.reports]: 'limited',
        [t.hrFeatures]: 'read',
        [t.financeFeatures]: 'full',
        [t.projectManagement]: 'read',
        [t.salesFeatures]: 'read'
      },
      features: [
        language === 'ar' ? 'إدارة الميزانيات' : 'Budget management',
        language === 'ar' ? 'تتبع المصروفات' : 'Expense tracking',
        language === 'ar' ? 'التقارير المالية' : 'Financial reports',
        language === 'ar' ? 'إدارة الأصول' : 'Asset management'
      ]
    },
    {
      id: 'department_manager',
      name: t.departmentManager,
      icon: Building,
      color: 'from-purple-500 to-pink-500',
      level: 4,
      description: language === 'ar'
        ? 'إدارة قسم محدد ومشاريعه'
        : 'Manage specific department and its projects',
      permissions: {
        [t.dashboardAccess]: 'limited',
        [t.userManagement]: 'none',
        [t.systemSettings]: 'none',
        [t.reports]: 'limited',
        [t.hrFeatures]: 'read',
        [t.financeFeatures]: 'none',
        [t.projectManagement]: 'limited',
        [t.salesFeatures]: 'none'
      },
      features: [
        language === 'ar' ? 'إدارة فريق القسم' : 'Department team management',
        language === 'ar' ? 'مشاريع القسم' : 'Department projects',
        language === 'ar' ? 'تقارير القسم' : 'Department reports',
        language === 'ar' ? 'جدولة المهام' : 'Task scheduling'
      ]
    },
    {
      id: 'sales_manager',
      name: t.salesManager,
      icon: ShoppingCart,
      color: 'from-red-500 to-rose-500',
      level: 4,
      description: language === 'ar'
        ? 'إدارة المبيعات والعملاء'
        : 'Sales and customer management',
      permissions: {
        [t.dashboardAccess]: 'limited',
        [t.userManagement]: 'none',
        [t.systemSettings]: 'none',
        [t.reports]: 'limited',
        [t.hrFeatures]: 'none',
        [t.financeFeatures]: 'read',
        [t.projectManagement]: 'none',
        [t.salesFeatures]: 'full'
      },
      features: [
        language === 'ar' ? 'إدارة العملاء' : 'Customer management',
        language === 'ar' ? 'خط أنابيب المبيعات' : 'Sales pipeline',
        language === 'ar' ? 'إدارة العروض' : 'Quote management',
        language === 'ar' ? 'تقارير المبيعات' : 'Sales reports'
      ]
    },
    {
      id: 'employee',
      name: t.employee,
      icon: User,
      color: 'from-gray-500 to-slate-500',
      level: 5,
      description: language === 'ar'
        ? 'وصول للميزات الأساسية والملف الشخصي'
        : 'Access to basic features and personal profile',
      permissions: {
        [t.dashboardAccess]: 'read',
        [t.userManagement]: 'none',
        [t.systemSettings]: 'none',
        [t.reports]: 'none',
        [t.hrFeatures]: 'read',
        [t.financeFeatures]: 'none',
        [t.projectManagement]: 'read',
        [t.salesFeatures]: 'none'
      },
      features: [
        language === 'ar' ? 'الملف الشخصي' : 'Personal profile',
        language === 'ar' ? 'طلبات الإجازة' : 'Leave requests',
        language === 'ar' ? 'المهام المعينة' : 'Assigned tasks',
        language === 'ar' ? 'الرسائل الشخصية' : 'Personal messages'
      ]
    }
  ]

  const getPermissionBadge = (permission: string): void => {
    switch (permission) {
      case 'full':
        return <Badge className="bg-green-500/20 text-green-400 border-green-500/30">{t.fullAccess}</Badge>
      case 'limited':
        return <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30">{t.limitedAccess}</Badge>
      case 'read':
        return <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/30">{t.readOnly}</Badge>
      case 'none':
        return <Badge className="bg-red-500/20 text-red-400 border-red-500/30">{t.noAccess}</Badge>
      default:
        return null
    }
  }

  return (
    <div className={`space-y-10 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="text-center animate-fade-in-up">
        <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-white via-white to-white/80 bg-clip-text text-transparent mb-6">{t.title}</h2>
        <p className="text-xl text-white/90 mb-4">{t.subtitle}</p>
        <p className="text-white/70 max-w-3xl mx-auto text-lg">{t.description}</p>
      </div>

      {/* Roles Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 animate-fade-in-up animation-delay-200">
        {roles.map((role) => {
          const Icon = role.icon
          return (
            <Card key={role.id} className="glass-card border-white/20 hover:border-white/40 transition-all duration-300 group hover:scale-105">
              <CardHeader>
                <div className="flex items-center gap-4">
                  <div className={`w-14 h-14 bg-gradient-to-r ${role.color} rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg`}>
                    <Icon className="h-7 w-7 text-white" />
                  </div>
                  <div>
                    <CardTitle className="text-white text-xl font-semibold">{role.name}</CardTitle>
                    <p className="text-white/80 text-sm leading-relaxed">{role.description}</p>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Features */}
                <div>
                  <h4 className="text-white font-semibold mb-3 text-lg">{language === 'ar' ? 'الميزات الرئيسية' : 'Key Features'}</h4>
                  <div className="space-y-2">
                    {role.features.map((feature, index) => (
                      <div key={index} className="flex items-start gap-3">
                        <div className="w-2 h-2 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full mt-2 flex-shrink-0" />
                        <span className="text-white/80 text-sm leading-relaxed">{feature}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Permissions */}
                <div>
                  <h4 className="text-white font-semibold mb-3 text-lg">{t.permissions}</h4>
                  <div className="space-y-3">
                    {Object.entries(role.permissions).map(([feature, permission]) => (
                      <div key={feature} className="flex items-center justify-between p-2 rounded-lg bg-white/5 border border-white/10">
                        <span className="text-white/80 text-sm font-medium">{feature}</span>
                        {getPermissionBadge(permission)}
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>
    </div>
  )
}
