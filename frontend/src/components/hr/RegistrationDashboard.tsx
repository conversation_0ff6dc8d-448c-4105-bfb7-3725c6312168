import React, { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Users, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  TrendingUp,
  Calendar,
  Building2,
  Mail,
  Eye
} from 'lucide-react'
import { apiClient } from '@/services/api'

interface RegistrationOverview {
  period: {
    start_date: string
    end_date: string
    days: number
  }
  counts: {
    total_registrations: number
    pending_approvals: number
    approved_pending_activation: number
    completed_activations: number
    rejected_activations: number
  }
  rates: {
    completion_rate: number
    rejection_rate: number
    approval_rate: number
  }
  processing_times: {
    avg_approval_time_hours: number | null
    avg_activation_time_hours: number | null
  }
}

interface PendingApproval {
  activation_id: number
  employee_id: string
  employee_name: string
  employee_name_ar: string
  email: string
  position: string
  position_ar: string
  department: string | null
  department_ar: string | null
  created_by: string | null
  created_at: string
  waiting_time_hours: number
  waiting_time_days: number
  is_urgent: boolean
}

interface RegistrationAnalytics {
  overview: RegistrationOverview
  pending_approvals: PendingApproval[]
  trends: any
  department_breakdown: any[]
  generated_at: string
}

interface RegistrationDashboardProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    registrationDashboard: 'لوحة تحكم التسجيل',
    overview: 'نظرة عامة',
    pendingApprovals: 'الموافقات المعلقة',
    totalRegistrations: 'إجمالي التسجيلات',
    pendingApproval: 'في انتظار الموافقة',
    completedActivations: 'التفعيلات المكتملة',
    rejectedApplications: 'الطلبات المرفوضة',
    completionRate: 'معدل الإكمال',
    approvalRate: 'معدل الموافقة',
    avgApprovalTime: 'متوسط وقت الموافقة',
    avgActivationTime: 'متوسط وقت التفعيل',
    hours: 'ساعة',
    days: 'أيام',
    urgent: 'عاجل',
    approve: 'موافقة',
    viewDetails: 'عرض التفاصيل',
    waitingTime: 'وقت الانتظار',
    createdBy: 'أنشئ بواسطة',
    department: 'القسم',
    position: 'المنصب',
    email: 'البريد الإلكتروني',
    employeeId: 'رقم الموظف',
    loading: 'جاري التحميل...',
    error: 'حدث خطأ في تحميل البيانات',
    refresh: 'تحديث',
    noData: 'لا توجد بيانات',
    approveSuccess: 'تم الموافقة على الموظف بنجاح',
    approveError: 'فشل في الموافقة على الموظف'
  },
  en: {
    registrationDashboard: 'Registration Dashboard',
    overview: 'Overview',
    pendingApprovals: 'Pending Approvals',
    totalRegistrations: 'Total Registrations',
    pendingApproval: 'Pending Approval',
    completedActivations: 'Completed Activations',
    rejectedApplications: 'Rejected Applications',
    completionRate: 'Completion Rate',
    approvalRate: 'Approval Rate',
    avgApprovalTime: 'Avg Approval Time',
    avgActivationTime: 'Avg Activation Time',
    hours: 'hours',
    days: 'days',
    urgent: 'Urgent',
    approve: 'Approve',
    viewDetails: 'View Details',
    waitingTime: 'Waiting Time',
    createdBy: 'Created By',
    department: 'Department',
    position: 'Position',
    email: 'Email',
    employeeId: 'Employee ID',
    loading: 'Loading...',
    error: 'Error loading data',
    refresh: 'Refresh',
    noData: 'No data available',
    approveSuccess: 'Employee approved successfully',
    approveError: 'Failed to approve employee'
  }
}

// @ts-ignore
export default function RegistrationDashboard({ language }: RegistrationDashboardProps as any): (React as any).ReactElement {
  const [analytics, setAnalytics] = useState<RegistrationAnalytics | null>(null)
  const [loading, setLoading] = useState(true as any)
  const [error, setError] = useState<string | null>(null)
  // @ts-ignore
  const [approvingIds, setApprovingIds] = useState<Set<number>>(new Set( as any))

  const t = translations[language]
  const isRTL = language === 'ar'

  // @ts-ignore
  useEffect(( as any) => {
    // @ts-ignore
    loadAnalytics( as any)
  // @ts-ignore
  }, [])

  const loadAnalytics = async () => {
    try {
      setLoading(true as any)
      setError(null as any)
      const response = await (apiClient as any).get('/auth/registration-analytics/' as any)
      setAnalytics((response as any as any).data)
    } catch (error) {
      (console as any).error('Error loading registration analytics:', error as any)
      setError((t as any as any).error)
    } finally {
      setLoading(false as any)
    }
  }

  const handleApprove = async (employeeId: number) => {
    try {
      setApprovingIds(prev => new Set(prev as any).add(employeeId as any))
      
      await (apiClient as any).post(`/employees/${employeeId}/approve/` as any)
      
      // Show success message
      alert((t as any as any).approveSuccess)
      
      // Reload analytics to update the dashboard
      // @ts-ignore
      await loadAnalytics( as any)
    } catch (error) {
      (console as any).error('Error approving employee:', error as any)
      alert((t as any as any).approveError)
    } finally {
      setApprovingIds(prev => {
        const newSet = new Set(prev as any)
        (newSet as any).delete(employeeId as any)
        return newSet
      })
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-white">{(t as any).loading}</div>
      </div>
    )
  }

  if (error || !analytics) {
    return (
      <div className="flex flex-col items-center justify-center h-64 space-y-4">
        <div className="text-red-400">{error || (t as any).error}</div>
        <Button onClick={loadAnalytics} variant="outline">
          {(t as any).refresh}
        </Button>
      </div>
    )
  }

  const { overview, pending_approvals } = analytics

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-white">{(t as any).registrationDashboard}</h1>
        <Button onClick={loadAnalytics} variant="outline" size="sm">
          {(t as any).refresh}
        </Button>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="glass-card border-white/20">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-white">
              {(t as any).totalRegistrations}
            </CardTitle>
            <Users className="h-4 w-4 text-blue-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">
              {(overview as any).counts.total_registrations}
            </div>
            <p className="text-xs text-white/70">
              {(t as any).completionRate}: {(overview as any).rates.completion_rate}%
            </p>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-white">
              {(t as any).pendingApproval}
            </CardTitle>
            <Clock className="h-4 w-4 text-yellow-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">
              {(overview as any).counts.pending_approvals}
            </div>
            <p className="text-xs text-white/70">
              {(t as any).approvalRate}: {(overview as any).rates.approval_rate}%
            </p>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-white">
              {(t as any).completedActivations}
            </CardTitle>
            <CheckCircle className="h-4 w-4 text-green-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">
              {(overview as any).counts.completed_activations}
            </div>
            <p className="text-xs text-white/70">
              {(overview as any).processing_times.avg_activation_time_hours 
                ? `${(overview as any).processing_times.avg_activation_time_hours} ${(t as any).hours}`
                : (t as any).noData
              }
            </p>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-white">
              {(t as any).rejectedApplications}
            </CardTitle>
            <XCircle className="h-4 w-4 text-red-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">
              {(overview as any).counts.rejected_activations}
            </div>
            <p className="text-xs text-white/70">
              {(overview as any).rates.rejection_rate}%
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Pending Approvals */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-yellow-400" />
            {(t as any).pendingApprovals}
          </CardTitle>
          <CardDescription className="text-white/70">
            {(pending_approvals as any).length} employees waiting for approval
          </CardDescription>
        </CardHeader>
        <CardContent>
          {(pending_approvals as any).length === 0 ? (
            <div className="text-center py-8 text-white/70">
              {(t as any).noData}
            </div>
          ) : (
            <div className="space-y-4">
              // @ts-ignore
              {(pending_approvals as any).map((approval as any) => (
                <div
                  key={(approval as any).activation_id}
                  className="flex items-center justify-between p-4 bg-white/5 rounded-lg border border-white/10"
                >
                  <div className="flex-1 space-y-2">
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-white">
                        {language === 'ar' ? (approval as any).employee_name_ar : (approval as any).employee_name}
                      </span>
                      <Badge variant="outline" className="text-xs">
                        {(approval as any).employee_id}
                      </Badge>
                      {(approval as any).is_urgent && (
                        <Badge variant="destructive" className="text-xs">
                          {(t as any).urgent}
                        </Badge>
                      )}
                    </div>
                    <div className="text-sm text-white/70 space-y-1">
                      <div className="flex items-center gap-2">
                        <Mail className="h-3 w-3" />
                        {(approval as any).email}
                      </div>
                      <div className="flex items-center gap-2">
                        <Building2 className="h-3 w-3" />
                        {language === 'ar' ? (approval as any).position_ar : (approval as any).position}
                        {(approval as any).department && (
                          <span>
                            - {language === 'ar' ? (approval as any).department_ar : (approval as any).department}
                          </span>
                        )}
                      </div>
                      <div className="flex items-center gap-2">
                        <Clock className="h-3 w-3" />
                        {(t as any).waitingTime}: {(approval as any).waiting_time_days} {(t as any).days}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleApprove((approval as any as any).activation_id)}
                      disabled={(approvingIds as any).has((approval as any as any).activation_id)}
                    >
                      {(approvingIds as any).has((approval as any as any).activation_id) ? (t as any).loading : (t as any).approve}
                    </Button>
                  </div>
                </div>
              // @ts-ignore
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
