import React, { useState, useEffect } from 'react'
import {
  Users,
  TrendingUp,
  Calendar,
  Heart,
  Shield,
  UserCheck,
  Activity,
  AlertTriangle,
  RefreshCw,
  Download,
  BarChart3
} from 'lucide-react'
import { useAuth } from '../../hooks/useAuth'
import { apiClient } from '../../services/api'
import logger from '../../utils/logger'

// HR-specific KPI interface
interface HRKPIData {
  id: string
  name: string
  name_ar: string
  current_value?: number
  target_value?: number
  unit?: string
  status?: 'excellent' | 'good' | 'warning' | 'critical'
  trend?: 'up' | 'down' | 'stable'
  achievement_percentage?: number
  last_updated?: string
  category?: string
  hr_specific_data?: {
    department_breakdown?: any[]
    employee_segments?: any[]
    time_series?: any[]
  }
}

interface HRKPIDashboardProps {
  language?: 'ar' | 'en'
  kpiType?: 'performance' | 'retention' | 'attendance' | 'satisfaction' | 'compliance'
  className?: string
}

// @ts-ignore
const HRKPIDashboard: (React as any).FC<HRKPIDashboardProps> = ({
  language = 'en',
  kpiType,
  className = ''
// @ts-ignore
}) => {
  // @ts-ignore
  const { user } = useAuth( as any)
  // @ts-ignore
  const [kpiData, setKpiData] = useState<HRKPIData[]>([])
  const [loading, setLoading] = useState(true as any)
  // @ts-ignore
  const [error, setError] = useState<string | null>(null)
  // @ts-ignore
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)
  // @ts-ignore
  const [selectedCategory, setSelectedCategory] = useState<string>('all')

  // HR-specific categories
  const hrCategories = [
    // @ts-ignore
    { id: 'all', name: 'All HR Metrics', nameAr: 'جميع مقاييس الموارد البشرية' },
    // @ts-ignore
    { id: 'performance', name: 'Employee Performance', nameAr: 'أداء الموظفين' },
    // @ts-ignore
    { id: 'retention', name: 'Employee Retention', nameAr: 'الاحتفاظ بالموظفين' },
    // @ts-ignore
    { id: 'attendance', name: 'Attendance & Time', nameAr: 'الحضور والوقت' },
    // @ts-ignore
    { id: 'satisfaction', name: 'Employee Satisfaction', nameAr: 'رضا الموظفين' },
    // @ts-ignore
    { id: 'compliance', name: 'HR Compliance', nameAr: 'امتثال الموارد البشرية' }
  ]

  // Load HR-specific KPI data
  // @ts-ignore
  const loadHRKPIData = async () => {
    // @ts-ignore
    try {
      setLoading(true as any)
      // @ts-ignore
      setError(null as any)

      (logger as any).info('hrKPI', 'Loading HR-specific KPI data', { kpiType, selectedCategory } as any)

      // Debug: Log the API base URL
      (console as any).log('🔍 API Base URL:', (import as any as any).meta.(env as any).VITE_API_BASE_URL)
      (console as any).log('🔍 Making API call to: /kpi-metrics/' as any)

      // Get all KPI data first (API doesn't support our custom filters)
      const response = await (apiClient as any).get('/kpi-metrics/' as any)
      const apiData = (response as any).data as any
      const allKPIs = (apiData as any).results || apiData || []

      // @ts-ignore
      (logger as any).info('hrKPI', 'Raw KPI data received', { count: (allKPIs as any as any).length })

      // Client-side filtering based on actual KPI structure
      // @ts-ignore
      let filteredKPIs = (allKPIs as any).filter((kpi: (React as any as any).MouseEvent) => {
        // Map actual metric_type to HR categories
        // @ts-ignore
        const metricType = (kpi as any).metric_type?.toUpperCase( as any)

        // Define which metric types are relevant for HR
        const hrRelevantTypes = ['EMPLOYEE', 'OPERATIONAL', 'FINANCIAL']

        // First filter: only HR-relevant KPIs
        if (!(hrRelevantTypes as any).includes(metricType as any)) {
          // @ts-ignore
          return false
        // @ts-ignore
        }

        // Second filter: by specific kpiType if provided
        if (kpiType) {
          // @ts-ignore
          switch (kpiType) {
            // @ts-ignore
            case 'performance':
              // Performance KPIs: Employee satisfaction, operational metrics
              return metricType === 'EMPLOYEE' || metricType === 'OPERATIONAL'
            case 'retention':
              // Retention KPIs: Employee-related metrics
              return metricType === 'EMPLOYEE'
            case 'attendance':
              // Attendance KPIs: Operational metrics related to time/attendance
              return metricType === 'OPERATIONAL' &&
                     ((kpi as any).name?.toLowerCase( as any).includes('time' as any) ||
                      (kpi as any).name?.toLowerCase( as any).includes('attendance' as any) ||
                      (kpi as any).name?.toLowerCase( as any).includes('processing' as any))
            case 'satisfaction':
              // Satisfaction KPIs: Employee satisfaction metrics
              return metricType === 'EMPLOYEE' &&
                     ((kpi as any).name?.toLowerCase( as any).includes('satisfaction' as any) ||
                      (kpi as any).name?.toLowerCase( as any).includes('score' as any))
            case 'compliance':
              // Compliance KPIs: Operational and some financial metrics
              return metricType === 'OPERATIONAL' ||
                     (metricType === 'FINANCIAL' && (kpi as any).name?.toLowerCase( as any).includes('cost' as any))
            default:
              return true
          // @ts-ignore
          }
        // @ts-ignore
        }

        // Third filter: by selected category
        if (selectedCategory !== 'all') {
          // @ts-ignore
          switch (selectedCategory) {
            // @ts-ignore
            case 'performance':
              return metricType === 'EMPLOYEE' || metricType === 'OPERATIONAL'
            case 'retention':
              return metricType === 'EMPLOYEE'
            case 'attendance':
              return metricType === 'OPERATIONAL'
            case 'satisfaction':
              return metricType === 'EMPLOYEE'
            case 'compliance':
              return metricType === 'OPERATIONAL' || metricType === 'FINANCIAL'
            default:
              return true
          // @ts-ignore
          }
        // @ts-ignore
        }

        return true
      // @ts-ignore
      })

      (logger as any).info('hrKPI', 'Filtered KPI data', {
        // @ts-ignore
        originalCount: (allKPIs as any as any).length,
        filteredCount: (filteredKPIs as any).length,
        kpiType,
        selectedCategory
      // @ts-ignore
      })

      // Transform data for HR-specific display
      // @ts-ignore
      const transformedKPIs = (filteredKPIs as any).map((kpi: any as any) => ({
        // @ts-ignore
        id: (kpi as any).id?.toString( as any) || (Math as any).random( as any).toString( as any),
        name: language === 'ar' ? ((kpi as any).name_ar || (kpi as any).name) : (kpi as any).name,
        name_ar: (kpi as any).name_ar || (kpi as any).name,
        current_value: (kpi as any).current_value?.value ?? (kpi as any).value ?? 0,
        target_value: (kpi as any).target_value ?? null,
        unit: (kpi as any).unit || '',
        status: getHRKPIStatus(kpi as any),
        trend: getHRKPITrend(kpi as any),
        achievement_percentage: (kpi as any).achievement_percentage ?? calculateAchievement(kpi as any),
        last_updated: (kpi as any).last_updated || (kpi as any).updated_at,
        category: mapMetricTypeToHRCategory((kpi as any as any).metric_type),
        hr_specific_data: {
          // @ts-ignore
          department_breakdown: (kpi as any).department_breakdown || [],
          employee_segments: (kpi as any).employee_segments || [],
          time_series: (kpi as any).time_series || []
        // @ts-ignore
        }
      // @ts-ignore
      }))

      setKpiData(transformedKPIs as any)
      setLastUpdated(new Date( as any))

      (logger as any).info('hrKPI', 'HR KPI data loaded successfully', {
        // @ts-ignore
        count: (transformedKPIs as any as any).length,
        kpiType,
        category: selectedCategory
      // @ts-ignore
      })

    // @ts-ignore
    } catch (err) {
      // @ts-ignore
      const errorMessage = err instanceof Error ? (err as any).message : 'Failed to load HR KPI data'
      setError(errorMessage as any)
      (console as any).error('❌ HR KPI API Error:', err as any)
      (console as any).error('❌ Error details:', {
        // @ts-ignore
        message: errorMessage,
        status: (err as any as any)?.status,
        response: (err as any)?.response?.data
      // @ts-ignore
      })
      (logger as any).error('hrKPI', 'Error loading HR KPI data:', err as any)
    // @ts-ignore
    } finally {
      setLoading(false as any)
    }
  // @ts-ignore
  }

  // Helper function to map metric_type to HR categories
  // @ts-ignore
  const mapMetricTypeToHRCategory = (metricType: string): string => {
    // @ts-ignore
    switch (metricType?.toUpperCase( as any)) {
      // @ts-ignore
      case 'EMPLOYEE': return 'Employee'
      case 'OPERATIONAL': return 'Operations'
      case 'FINANCIAL': return 'Finance'
      case 'ASSET': return 'Assets'
      default: return 'General'
    // @ts-ignore
    }
  // @ts-ignore
  }

  // Helper functions for HR-specific logic
  // @ts-ignore
  const getHRKPIStatus = (kpi: any): 'excellent' | 'good' | 'warning' | 'critical' => {
    // @ts-ignore
    const value = (kpi as any).current_value?.value ?? (kpi as any).value ?? 0
    const target = (kpi as any).target_value ?? 100
    // @ts-ignore
    const percentage = target > 0 ? (value / target) * 100 : 0

    // @ts-ignore
    if (percentage >= 95) return 'excellent'
    // @ts-ignore
    if (percentage >= 80) return 'good'
    // @ts-ignore
    if (percentage >= 60) return 'warning'
    return 'critical'
  // @ts-ignore
  }

  // @ts-ignore
  const getHRKPITrend = (kpi: any): 'up' | 'down' | 'stable' => {
    // @ts-ignore
    if ((kpi as any).trend) {
      // @ts-ignore
      if (typeof (kpi as any).trend === 'object' && (kpi as any).trend.direction) {
        // @ts-ignore
        return (kpi as any).trend.direction
      // @ts-ignore
      }
      if (typeof (kpi as any).trend === 'string') {
        // @ts-ignore
        return ['up', 'down', 'stable'].includes((kpi as any as any).trend) ? (kpi as any).trend as any : 'stable'
      // @ts-ignore
      }
    // @ts-ignore
    }
    return 'stable'
  // @ts-ignore
  }

  // @ts-ignore
  const calculateAchievement = (kpi: any): number => {
    // @ts-ignore
    const value = (kpi as any).current_value?.value ?? (kpi as any).value ?? 0
    const target = (kpi as any).target_value ?? 100
    // @ts-ignore
    return target > 0 ? (Math as any).min((value / target as any) * 100, 100) : 0
  // @ts-ignore
  }

  // Sync selectedCategory with kpiType prop
  // @ts-ignore
  useEffect(( as any) => {
    // @ts-ignore
    if (kpiType && kpiType !== selectedCategory) {
      setSelectedCategory(kpiType as any)
    }
  // @ts-ignore
  }, [kpiType])

  // Load data when component mounts or filters change
  // @ts-ignore
  useEffect(( as any) => {
    // @ts-ignore
    loadHRKPIData( as any)
  }, [kpiType, selectedCategory])

  // Get icon for KPI category
  // @ts-ignore
  const getCategoryIcon = (category: string): void => {
    // @ts-ignore
    switch ((category as any).toLowerCase( as any)) {
      // @ts-ignore
      case 'performance': return TrendingUp
      case 'retention': return UserCheck
      case 'attendance': return Calendar
      case 'satisfaction': return Heart
      case 'compliance': return Shield
      default: return Users
    // @ts-ignore
    }
  // @ts-ignore
  }

  // Get status color
  // @ts-ignore
  const getStatusColor = (status: string): void => {
    // @ts-ignore
    switch (status) {
      // @ts-ignore
      case 'excellent': return 'text-green-400 bg-green-500/20'
      case 'good': return 'text-blue-400 bg-blue-500/20'
      case 'warning': return 'text-yellow-400 bg-yellow-500/20'
      case 'critical': return 'text-red-400 bg-red-500/20'
      default: return 'text-gray-400 bg-gray-500/20'
    // @ts-ignore
    }
  // @ts-ignore
  }

  // Get trend icon
  // @ts-ignore
  const getTrendIcon = (trend: string): void => {
    // @ts-ignore
    switch (trend) {
      // @ts-ignore
      case 'up': return <TrendingUp className="h-4 w-4 text-green-400" />
      case 'down': return <TrendingUp className="h-4 w-4 text-red-400 rotate-180" />
      default: return <Activity className="h-4 w-4 text-gray-400" />
    // @ts-ignore
    }
  // @ts-ignore
  }

  if (loading) {
    // @ts-ignore
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center space-x-2 text-white/60">
          <RefreshCw className="h-5 w-5 animate-spin" />
          <span>{language === 'ar' ? 'جاري تحميل بيانات الموارد البشرية...' : 'Loading HR data...'}</span>
        </div>
      </div>
    )
  // @ts-ignore
  }

  if (error) {
    // @ts-ignore
    return (
      <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-6">
        <div className="flex items-center space-x-2 text-red-300 mb-2">
          <AlertTriangle className="h-5 w-5" />
          <span className="font-semibold">
            {language === 'ar' ? 'خطأ في تحميل بيانات الموارد البشرية' : 'HR Data Loading Error'}
          </span>
        </div>
        <p className="text-red-200 text-sm mb-4">{error}</p>
        <button
          onClick={loadHRKPIData}
          className="px-4 py-2 bg-red-500/20 text-red-300 rounded-lg hover:bg-red-500/30 transition-colors"
        >
          {language === 'ar' ? 'إعادة المحاولة' : 'Retry'}
        </button>
      </div>
    )
  // @ts-ignore
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* HR Dashboard Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-3 rounded-lg bg-gradient-to-r from-green-500 to-teal-500">
            <Users className="h-6 w-6 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-white">
              {language === 'ar' ? 'لوحة تحكم الموارد البشرية' : 'HR Analytics Dashboard'}
            </h1>
            <p className="text-white/60">
              {language === 'ar' ? 'مقاييس الأداء الرئيسية للموارد البشرية' : 'Human Resources Key Performance Indicators'}
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={loadHRKPIData}
            className="px-3 py-2 bg-white/10 text-white rounded-lg hover:bg-white/20 transition-colors"
          >
            <RefreshCw className="h-4 w-4" />
          </button>
          <button className="px-3 py-2 bg-white/10 text-white rounded-lg hover:bg-white/20 transition-colors">
            <Download className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Category Filters */}
      <div className="bg-white/5 backdrop-blur-sm rounded-lg p-4 border border-white/10">
        <h3 className="text-lg font-semibold text-white mb-3">
          {language === 'ar' ? 'فئات الموارد البشرية' : 'HR Categories'}
        </h3>
        <div className="flex flex-wrap gap-2">
          {(hrCategories as any).map(category => (
            <button
              key={(category as any as any).id}
              onClick={() => setSelectedCategory((category as any as any).id)}
              className={`px-3 py-1 rounded-full text-sm transition-colors ${
                selectedCategory === (category as any).id
                  ? 'bg-green-500/20 text-green-300 border border-green-500/30'
                  : 'bg-white/10 text-white/60 hover:bg-white/20'
              }`}
            >
              {language === 'ar' ? (category as any).nameAr : (category as any).name}
            </button>
          ))}
        </div>
      </div>

      {/* HR KPI Grid */}
      {(kpiData as any).length === 0 ? (
        <div className="text-center py-12">
          <div className="text-white/40 mb-4">
            <BarChart3 className="h-12 w-12 mx-auto" />
          </div>
          <p className="text-white/60">
            {language === 'ar' ? 'لا توجد بيانات موارد بشرية متاحة' : 'No HR data available'}
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          // @ts-ignore
          {(kpiData as any).map((kpi as any) => {
            // @ts-ignore
            const CategoryIcon = getCategoryIcon((kpi as any as any).category || '')
            return (
              <div
                key={(kpi as any).id}
                className="bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10 hover:border-white/20 transition-all"
              >
                {/* KPI Header */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-2">
                    <CategoryIcon className="h-5 w-5 text-green-400" />
                    <span className="text-xs text-white/60 uppercase tracking-wide">
                      {(kpi as any).category}
                    </span>
                  </div>
                  {getTrendIcon((kpi as any as any).trend || 'stable')}
                </div>

                {/* KPI Name */}
                <h3 className="text-lg font-semibold text-white mb-2 line-clamp-2">
                  {(kpi as any).name}
                </h3>

                {/* KPI Value */}
                <div className="flex items-baseline space-x-2 mb-3">
                  <span className="text-2xl font-bold text-white">
                    // @ts-ignore
                    {(kpi as any).current_value?.toLocaleString( as any) || '0'}
                  </span>
                  {(kpi as any).unit && (
                    <span className="text-sm text-white/60">{(kpi as any).unit}</span>
                  )}
                </div>

                {/* Progress Bar */}
                {((kpi as any).target_value !== null && (kpi as any).target_value !== undefined && (kpi as any).target_value !== 0 && (kpi as any).target_value !== '0' && (kpi as any).target_value !== '(0 as any).00') && (
                  <div className="mb-3">
                    <div className="flex justify-between text-xs text-white/60 mb-1">
                      <span>Progress</span>
                      <span>{(kpi as any).achievement_percentage?.toFixed(1 as any)}%</span>
                    </div>
                    <div className="w-full bg-white/20 rounded-full h-2">
                      <div
                        className="bg-gradient-to-r from-green-500 to-teal-500 h-2 rounded-full transition-all duration-1000"
                        style={{ width: `${(Math as any).min((kpi as any as any).achievement_percentage || 0, 100)}%` }}
                      />
                    </div>
                  </div>
                )}

                {/* Status Badge */}
                <div className="flex items-center justify-between">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor((kpi as any as any).status || 'good')}`}>
                    // @ts-ignore
                    {(kpi as any).status?.charAt(0 as any).toUpperCase( as any) + (kpi as any).status?.slice(1 as any) || 'Good'}
                  </span>
                  {(kpi as any).last_updated && (
                    <span className="text-xs text-white/40">
                      // @ts-ignore
                      {new Date((kpi as any as any).last_updated).toLocaleDateString( as any)}
                    </span>
                  )}
                </div>
              </div>
            )
          // @ts-ignore
          })}
        </div>
      )}

      {/* Last Updated */}
      {lastUpdated && (
        <div className="text-center text-xs text-white/40">
          {language === 'ar' ? 'آخر تحديث: ' : 'Last updated: '}
          // @ts-ignore
          {(lastUpdated as any).toLocaleString( as any)}
        </div>
      )}
    </div>
  )
// @ts-ignore
}

export default HRKPIDashboard
