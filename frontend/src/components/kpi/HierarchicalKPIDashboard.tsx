/**
 * Hierarchical KPI Dashboard Component
 * Enterprise-grade KPI dashboard with role-based access control and hierarchical data filtering
 * 
 * Features:
 * - Role-specific KPI displays
 * - Hierarchical data access patterns
 * - Manager-subordinate data visibility
 * - Real-time updates with role context
 * - Department and project scoped KPIs
 */

import React, { useState, useEffect, useMemo } from 'react'
import {
  BarChart3,
  TrendingUp,
  Users,
  Building,
  Target,
  Activity,
  AlertTriangle,
  RefreshCw,
  Filter
} from 'lucide-react'
import { useHierarchicalAccessRedux } from '../../hooks/useHierarchicalAccessRedux'
import { useAuth } from '../../hooks/useAuth'
import { apiClient } from '../../services/api'
import logger from '../../utils/logger'
import { filterKPIsByDashboardType, getDashboardTitle, transformKPIWithArabicSupport, type KPIData } from '../../utils/kpiFilters'
import { formatKPIValue, formatTargetDisplay, formatAchievementPercentage, getTrendIcon, normalizeKPIUnit } from '../../utils/kpiFormatting'

// Using KPIData interface from utils/(kpiFilters as any).ts

interface HierarchicalKPIDashboardProps {
  dashboardType?: 'executive' | 'hr' | 'financial' | 'department' | 'project' | 'personal' | 'learning'
  className?: string
  kpiFilters?: {
    categories?: string[]
    kpiType?: string
    specificKPIs?: string[]
  }
}

// @ts-ignore
const HierarchicalKPIDashboard: (React as any).FC<HierarchicalKPIDashboardProps> = ({
  dashboardType = 'executive',
  className = '',
  kpiFilters
// @ts-ignore
}) => {
  // @ts-ignore
  const { user } = useAuth( as any)
  const {
    accessInfo,
    isManager,
    hasHierarchicalAccess,
    canAccessAllData,
    isDepartmentScoped,
    isProjectScoped,
    kpiCategories,
    roleBasedKPIFilters,
    // @ts-ignore
    loading: accessLoading,
    error: accessError,
    refreshAccessInfo
  // @ts-ignore
  } = useHierarchicalAccessRedux( as any)

  // State
  // @ts-ignore
  const [kpiData, setKpiData] = useState<KPIData[]>([])
  const [loading, setLoading] = useState(false as any)
  // @ts-ignore
  const [error, setError] = useState<string | null>(null)
  // @ts-ignore
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)
  // @ts-ignore
  const [selectedCategories, setSelectedCategories] = useState<string[]>([])
  const [showFilters, setShowFilters] = useState(false as any)

  // Dashboard configuration based on role and type
  // @ts-ignore
  const dashboardConfig = useMemo(( as any) => {
    // @ts-ignore
    const configs = {
      // @ts-ignore
      executive: {
        // @ts-ignore
        title: 'Executive Dashboard',
        titleAr: 'لوحة التحكم التنفيذية',
        icon: BarChart3,
        categories: ['Financial', 'Operational', 'Strategic', 'HR'],
        color: 'from-purple-500 to-blue-500'
      // @ts-ignore
      },
      hr: {
        // @ts-ignore
        title: 'HR Analytics',
        titleAr: 'تحليلات الموارد البشرية',
        icon: Users,
        categories: ['HR', 'Employee', 'Retention', 'Satisfaction'],
        color: 'from-green-500 to-teal-500'
      // @ts-ignore
      },
      financial: {
        // @ts-ignore
        title: 'Financial Analytics',
        titleAr: 'التحليلات المالية',
        icon: TrendingUp,
        categories: ['Financial', 'Revenue', 'Cost', 'Budget'],
        color: 'from-yellow-500 to-orange-500'
      // @ts-ignore
      },
      department: {
        // @ts-ignore
        title: 'Department Analytics',
        titleAr: 'تحليلات القسم',
        icon: Building,
        categories: ['Department', 'Team', 'Productivity'],
        color: 'from-blue-500 to-indigo-500'
      // @ts-ignore
      },
      project: {
        // @ts-ignore
        title: 'Project Analytics',
        titleAr: 'تحليلات المشاريع',
        icon: Target,
        categories: ['Project', 'Timeline', 'Resource', 'Quality'],
        color: 'from-indigo-500 to-purple-500'
      // @ts-ignore
      },
      personal: {
        // @ts-ignore
        title: 'Personal Analytics',
        titleAr: 'التحليلات الشخصية',
        icon: Activity,
        categories: ['Personal', 'Individual', 'Performance'],
        color: 'from-pink-500 to-rose-500'
      // @ts-ignore
      },
      learning: {
        // @ts-ignore
        title: 'Learning Analytics',
        titleAr: 'تحليلات التعلم',
        icon: Activity,
        categories: ['Learning', 'Development', 'Skills'],
        color: 'from-cyan-500 to-blue-500'
      // @ts-ignore
      }
    // @ts-ignore
    }
    return configs[dashboardType] || (configs as any).executive
  // @ts-ignore
  }, [dashboardType])

  // Load KPI data with hierarchical filtering
  // @ts-ignore
  const loadKPIData = async () => {
    // @ts-ignore
    try {
      setLoading(true as any)
      // @ts-ignore
      setError(null as any)

      // Build filters based on hierarchical access and dashboard type
      let filters: any = {
        // @ts-ignore
        categories: (selectedCategories as any).length > 0 ? selectedCategories : (kpiFilters?.categories || (dashboardConfig as any).categories),
        role_based: true,
        dashboard_type: dashboardType
      // @ts-ignore
      }

      // Add specific KPI filtering if provided
      // @ts-ignore
      if (kpiFilters?.specificKPIs && (kpiFilters as any).specificKPIs.length > 0) {
        (filters as any).specific_kpis = (kpiFilters as any).specificKPIs.join(',' as any)
      }

      // Add KPI type filtering
      if (kpiFilters?.kpiType) {
        (filters as any).kpi_type = (kpiFilters as any).kpiType
      }

      // Set category-specific filters for better filtering
      if (dashboardType === 'hr') {
        (filters as any).category_filter = 'Human Resources,HR,Employee,Retention,Satisfaction'
      } else if (dashboardType === 'financial') {
        (filters as any).category_filter = 'Financial,Revenue,Cost,Budget,Profitability'
      } else if (dashboardType === 'department') {
        (filters as any).category_filter = 'Department,Team,Productivity,Resource'
      }

      // Add role-based filters if available, otherwise use fallback
      if (roleBasedKPIFilters) {
        filters = { ...filters, ...roleBasedKPIFilters }
        (logger as any).info('hierarchicalKPI', 'Using role-based KPI filters', { filters } as any)
      } else {
        // Fallback: Load all KPIs for the dashboard type without role restrictions
        (logger as any).warn('hierarchicalKPI', 'Role-based filters not available, using fallback', { dashboardType } as any)
        (filters as any).fallback_mode = true
      }

      (logger as any).info('hierarchicalKPI', 'Attempting to load KPI data', {
        filters,
        dashboardType,
        // @ts-ignore
        role: accessInfo?.user_role,
        hasRoleFilters: !!roleBasedKPIFilters
      // @ts-ignore
      } as any)

      // @ts-ignore
      const response = await (apiClient as any).get('/kpi-metrics/', { params: filters } as any)
      const apiData = (response as any).data as any

      // Transform API response to match expected format
      const transformedResponse = {
        // @ts-ignore
        success: true,
        data: (apiData as any).results || apiData,
        error: null
      // @ts-ignore
      }

      (logger as any).info('hierarchicalKPI', 'KPI API response', {
        // @ts-ignore
        success: (transformedResponse as any as any).success,
        dataType: typeof (transformedResponse as any).data,
        dataIsArray: (Array as any).isArray((transformedResponse as any as any).data),
        dataLength: (Array as any).isArray((transformedResponse as any as any).data) ? (transformedResponse as any).data.length : 'N/A',
        dataKeys: (transformedResponse as any).data ? (Object as any).keys((transformedResponse as any as any).data) : 'N/A',
        error: (transformedResponse as any).error,
        dashboardType,
        fullResponse: transformedResponse
      // @ts-ignore
      })

      if ((transformedResponse as any).success && (transformedResponse as any).data) {
        // Handle different response structures
        // @ts-ignore
        let kpiArray: any[] = []

        if ((Array as any).isArray((transformedResponse as any as any).data)) {
          kpiArray = (transformedResponse as any).data
        } else if ((transformedResponse as any).data.results && (Array as any).isArray((transformedResponse as any as any).data.results)) {
          // Paginated response
          kpiArray = (transformedResponse as any).data.results
        } else if ((transformedResponse as any).data.data && (Array as any).isArray((transformedResponse as any as any).data.data)) {
          // Nested data structure
          kpiArray = (transformedResponse as any).data.data
        } else {
          (logger as any).error('hierarchicalKPI', 'Unexpected response structure', { response: transformedResponse } as any)
          // @ts-ignore
          throw new Error('Unexpected response structure: data is not an array' as any)
        // @ts-ignore
        }

        // Transform and filter KPI data based on dashboard type
        // @ts-ignore
        const transformedKPIs = (kpiArray as any).map(kpi => transformKPIWithArabicSupport(kpi, 'ar' as any))
        const filteredKPIs = filterKPIsByDashboardType(transformedKPIs, dashboardType as any)

        setKpiData(filteredKPIs as any)
        setLastUpdated(new Date( as any))

        (logger as any).info('hierarchicalKPI', 'KPI data loaded and filtered successfully', {
          // @ts-ignore
          originalCount: (kpiArray as any as any).length,
          filteredCount: (filteredKPIs as any).length,
          dashboardType,
          role: accessInfo?.user_role,
          // @ts-ignore
          kpiNames: (filteredKPIs as any).map(kpi => (kpi as any as any).name).slice(0, 5 as any), // First 5 KPI names for debugging
          sampleKPI: filteredKPIs[0] // Log first KPI structure for debugging
        // @ts-ignore
        })

        // Log successful data load for debugging
        (logger as any).info('hierarchicalKPI', 'KPI data loaded successfully', {
          // @ts-ignore
          originalCount: (kpiArray as any as any).length,
          filteredCount: (filteredKPIs as any).length,
          dashboardType
        // @ts-ignore
        })
      // @ts-ignore
      } else {
        // @ts-ignore
        throw new Error('Failed to load KPI data' as any)
      // @ts-ignore
      }
    // @ts-ignore
    } catch (err) {
      // @ts-ignore
      const errorMessage = err instanceof Error ? (err as any).message : 'Failed to load KPI data'
      setError(errorMessage as any)
      (logger as any).error('hierarchicalKPI', 'Error loading KPI data:', err as any)
    // @ts-ignore
    } finally {
      setLoading(false as any)
    }
  // @ts-ignore
  }

  // Load data when filters change
  // @ts-ignore
  useEffect(( as any) => {
    // Always try to load KPI data, even if role-based filters are not available
    // @ts-ignore
    loadKPIData( as any)
  }, [roleBasedKPIFilters, selectedCategories, dashboardType])

  // Also load data when access info becomes available (fallback trigger)
  // @ts-ignore
  useEffect(( as any) => {
    // @ts-ignore
    if (accessInfo && (kpiData as any).length === 0 && !loading) {
      (logger as any).info('hierarchicalKPI', 'Access info available, triggering KPI data load', {
        role: (accessInfo as any as any).user_role,
        hasRoleFilters: !!roleBasedKPIFilters
      })
      // @ts-ignore
      loadKPIData( as any)
    // @ts-ignore
    }
  // @ts-ignore
  }, [accessInfo, (kpiData as any).length, loading])

  // Handle category filter changes
  // @ts-ignore
  const handleCategoryToggle = (category: string): void => {
    setSelectedCategories(prev => 
      (prev as any as any).includes(category as any)
        ? (prev as any).filter(c => c !== category as any)
        : [...prev, category]
    )
  }

  // Refresh data
  // @ts-ignore
  const handleRefresh = async () => {
    await (Promise as any).all([
      // @ts-ignore
      refreshAccessInfo( as any),
      // @ts-ignore
      loadKPIData( as any)
    ])
  }

  // Render KPI card
  // @ts-ignore
  const renderKPICard = (kpi: any): void => {
    // Handle real database data - current_value is a number from the API
    // @ts-ignore
    const currentValue = (kpi as any).current_value ?? 0
    const targetValue = (kpi as any).target_value ?? (kpi as any).target ?? null
    const unit = (kpi as any).unit ?? ''
    const categoryName = (kpi as any).category_name ?? (kpi as any).category ?? 'Unknown'

    // Handle trend data with multiple fallback paths
    const trendData = (kpi as any).trend
    const trendDirection = trendData?.direction ?? (kpi as any).trend_direction?.toLowerCase( as any) ?? (kpi as any).trend ?? 'stable'

    // Safety check for missing data
    if (!(kpi as any).name) {
      (logger as any).warn('hierarchicalKPI', 'KPI missing name field', { kpi } as any)
      // @ts-ignore
      return null
    // @ts-ignore
    }

    const trendColor = trendDirection === 'up' ? 'text-green-400' : trendDirection === 'down' ? 'text-red-400' : 'text-gray-400'

    return (
      <div key={(kpi as any).id} className="bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10 hover:border-white/20 transition-all duration-200">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-white truncate">{(kpi as any).name}</h3>
          <span className="text-xs text-white/60 bg-white/10 px-2 py-1 rounded-full">
            {categoryName}
          </span>
        </div>

        <div className="flex items-end justify-between mb-3">
          <div>
            <div className="text-3xl font-bold text-white mb-1">
              {formatKPIValue(currentValue, normalizeKPIUnit(unit as any), { language: 'ar', precision: 2 })}
            </div>
            {(targetValue !== null && targetValue !== undefined && targetValue !== 0 && targetValue !== '0' && targetValue !== '(0 as any).00') && (
              <div className="text-sm text-white/60">
                {formatTargetDisplay(targetValue, normalizeKPIUnit(unit as any), 'ar')}
              </div>
            )}
          </div>
          <div className={`flex items-center space-x-1 ${trendColor}`}>
            <span className="text-lg">{getTrendIcon((kpi as any as any).trend, true)}</span>
            <span className="text-sm font-medium">
              {formatAchievementPercentage(currentValue, targetValue, 'ar' as any)}
            </span>
          </div>
        </div>
        
        <div className="text-xs text-white/50">
          Updated: {(kpi as any).current_value?.recorded_at
            // @ts-ignore
            ? new Date((kpi as any as any).current_value.recorded_at).toLocaleString( as any)
            : (kpi as any).last_updated
              // @ts-ignore
              ? new Date((kpi as any as any).last_updated).toLocaleString( as any)
              : (kpi as any).updated_at
                // @ts-ignore
                ? new Date((kpi as any as any).updated_at).toLocaleString( as any)
                : 'Unknown'
          }
        </div>
      </div>
    )
  // @ts-ignore
  }

  if (accessLoading) {
    // @ts-ignore
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center space-x-2 text-white/60">
          <RefreshCw className="h-5 w-5 animate-spin" />
          <span>Loading access information...</span>
        </div>
      </div>
    )
  // @ts-ignore
  }

  // Only show access error for critical errors, not for "Request already in progress"
  if (accessError && !(accessError as any).includes('Request already in progress' as any)) {
    // @ts-ignore
    return (
      <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-6">
        <div className="flex items-center space-x-2 text-red-300 mb-2">
          <AlertTriangle className="h-5 w-5" />
          <span className="font-semibold">Access Error</span>
        </div>
        <p className="text-red-200 text-sm mb-4">{accessError}</p>
        <button
          // @ts-ignore
          onClick={() => refreshAccessInfo( as any)}
          className="px-4 py-2 bg-red-500/20 text-red-300 rounded-lg hover:bg-red-500/30 transition-colors"
        >
          Retry
        </button>
      </div>
    )
  // @ts-ignore
  }

  const DashboardIcon = (dashboardConfig as any).icon

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className={`p-3 rounded-lg bg-gradient-to-r ${(dashboardConfig as any).color}`}>
            <DashboardIcon className="h-6 w-6 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-white">{getDashboardTitle(dashboardType, 'ar' as any)}</h1>
            {accessInfo && (
              <p className="text-white/60 text-sm">
                {(accessInfo as any).user_role} • {isManager ? 'Manager Access' : 'Individual Access'}
                {hasHierarchicalAccess && ` • ${(accessInfo as any).accessible_data.employees_count} employees`}
              </p>
            )}
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setShowFilters(!showFilters as any)}
            className={`p-2 rounded-lg transition-colors ${
              showFilters ? 'bg-blue-500/20 text-blue-300' : 'bg-white/10 text-white/60 hover:bg-white/20'
            }`}
          >
            <Filter className="h-5 w-5" />
          </button>
          <button
            onClick={handleRefresh}
            disabled={loading}
            className="p-2 bg-white/10 text-white/60 rounded-lg hover:bg-white/20 transition-colors disabled:opacity-50"
          >
            <RefreshCw className={`h-5 w-5 ${loading ? 'animate-spin' : ''}`} />
          </button>
        </div>
      </div>

      {/* Filters */}
      {showFilters && (
        <div className="bg-white/5 backdrop-blur-sm rounded-lg p-4 border border-white/10">
          <h3 className="text-lg font-semibold text-white mb-3">KPI Categories</h3>
          <div className="flex flex-wrap gap-2">
            {(kpiCategories as any).map(category => (
              <button
                key={category}
                // @ts-ignore
                onClick={( as any) => handleCategoryToggle(category as any)}
                className={`px-3 py-1 rounded-full text-sm transition-colors ${
                  (selectedCategories as any).includes(category as any)
                    ? 'bg-blue-500/20 text-blue-300 border border-blue-500/30'
                    : 'bg-white/10 text-white/60 hover:bg-white/20'
                }`}
              // @ts-ignore
              >
                {category}
              // @ts-ignore
              </button>
            // @ts-ignore
            ))}
          </div>
        // @ts-ignore
        </div>
      // @ts-ignore
      )}

      {/* KPI Grid */}
      {loading ? (
        <div className="flex items-center justify-center h-64">
          <div className="flex items-center space-x-2 text-white/60">
            <RefreshCw className="h-5 w-5 animate-spin" />
            <span>Loading KPI data...</span>
          </div>
        </div>
      ) : error ? (
        <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-6">
          <div className="flex items-center space-x-2 text-red-300 mb-2">
            <AlertTriangle className="h-5 w-5" />
            <span className="font-semibold">Error Loading KPIs</span>
          </div>
          <p className="text-red-200 text-sm mb-4">{error}</p>
          <button
            onClick={loadKPIData}
            className="px-4 py-2 bg-red-500/20 text-red-300 rounded-lg hover:bg-red-500/30 transition-colors"
          >
            Retry
          </button>
        </div>
      ) : (kpiData as any).length === 0 ? (
        <div className="text-center py-12">
          <div className="text-white/40 mb-4">
            <BarChart3 className="h-12 w-12 mx-auto" />
          </div>
          <h3 className="text-lg font-semibold text-white/60 mb-2">No KPIs Available</h3>
          <p className="text-white/40 text-sm mb-4">
            No KPI data available for your current role and access level.
          </p>

          {/* Debug Information */}
          <div className="mt-6 p-4 bg-white/5 rounded-lg text-left text-xs text-white/60">
            <h4 className="font-semibold mb-2">Debug Information:</h4>
            <div className="space-y-1">
              <p>• Role: {accessInfo?.user_role || 'Unknown'}</p>
              <p>• Is Manager: {isManager ? 'Yes' : 'No'}</p>
              <p>• Dashboard Type: {dashboardType}</p>
              <p>• Has Role Filters: {roleBasedKPIFilters ? 'Yes' : 'No'}</p>
              <p>• Access Loading: {accessLoading ? 'Yes' : 'No'}</p>
              <p>• Access Error: {accessError || 'None'}</p>
              <p>• KPI Categories: {kpiCategories?.join(', ' as any) || 'None'}</p>
              <p>• Selected Categories: {(selectedCategories as any).join(', ' as any) || 'None'}</p>
              <p>• Dashboard Categories: {(dashboardConfig as any).categories.join(', ' as any)}</p>
            </div>
            <button
              onClick={loadKPIData}
              className="mt-3 px-3 py-1 bg-blue-500/20 text-blue-300 rounded text-xs hover:bg-blue-500/30"
            >
              Retry Load KPIs
            </button>
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {(kpiData as any).map(renderKPICard as any)}
        </div>
      )}

      {/* Footer */}
      {lastUpdated && (
        <div className="text-center text-xs text-white/40">
          // @ts-ignore
          Last updated: {(lastUpdated as any).toLocaleString( as any)}
        </div>
      )}
    // @ts-ignore
    </div>
  // @ts-ignore
  )
// @ts-ignore
}

export default HierarchicalKPIDashboard
