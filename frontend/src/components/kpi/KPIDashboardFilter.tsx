/**
 * KPI Dashboard Filter Component
 * Combines date range picker and filter panel for KPI dashboards
 */

import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { DateRangePicker, DateRange } from '@/components/common/DateRangePicker'
import { KPIFilterPanel, KPIFilterValue } from '@/components/kpi/KPIFilterPanel'
import { KPICategory } from '@/services/kpiService'
import { Filter, X, Calendar, RefreshCw, Download, ChevronDown, ChevronUp } from 'lucide-react'

export interface KPIDashboardFilterProps {
  categories: KPICategory[]
  onFilterChange: (filters: KPIFilterValue) => void
  onDateRangeChange: (range: DateRange) => void
  onCompareChange?: (range: DateRange | null) => void
  initialFilters?: KPIFilterValue
  initialDateRange?: DateRange
  initialCompareRange?: DateRange | null
  language: 'ar' | 'en'
  onReset?: () => void
  onExport?: (format: 'csv' | 'excel' | 'pdf') => void
  loading?: boolean
}

const translations = {
  ar: {
    filters: 'الفلاتر',
    dateRange: 'النطاق الزمني',
    showFilters: 'إظهار الفلاتر',
    hideFilters: 'إخفاء الفلاتر',
    reset: 'إعادة تعيين',
    export: 'تصدير',
    activeFilters: 'الفلاتر النشطة',
    clearAll: 'مسح الكل',
    csv: 'CSV',
    excel: 'Excel',
    pdf: 'PDF'
  },
  en: {
    filters: 'Filters',
    dateRange: 'Date Range',
    showFilters: 'Show Filters',
    hideFilters: 'Hide Filters',
    reset: 'Reset',
    export: 'Export',
    activeFilters: 'Active Filters',
    clearAll: 'Clear All',
    csv: 'CSV',
    excel: 'Excel',
    pdf: 'PDF'
  }
}

export default function KPIDashboardFilter({
  categories,
  onFilterChange,
  onDateRangeChange,
  onCompareChange,
  initialFilters = {},
  initialDateRange,
  initialCompareRange = null,
  language,
  onReset,
  onExport,
  loading = false
}: KPIDashboardFilterProps): React.ReactElement {
  const [filters, setFilters] = useState<KPIFilterValue>(initialFilters)
  const [dateRange, setDateRange] = useState<DateRange | undefined>(initialDateRange)
  const [compareRange, setCompareRange] = useState<DateRange | null>(initialCompareRange)
  const [showFilters, setShowFilters] = useState(false)
  const [activeFilterCount, setActiveFilterCount] = useState(0)
  
  const t = translations[language]
  const isRTL = language === 'ar'

  // Handle filter change
  const handleFilterChange = (newFilters: KPIFilterValue): void => {
    setFilters(newFilters)
    
    // Count active filters
    let count = 0
    if (newFilters.categories?.length) count++
    if (newFilters.status) count++
    if (newFilters.frequency) count++
    if (newFilters.measurementType) count++
    if (newFilters.trendDirection) count++
    if (newFilters.targetAchievement && 
        (newFilters.targetAchievement[0] > 0 || newFilters.targetAchievement[1] < 200)) count++
    if (newFilters.search) count++
    setActiveFilterCount(count)
    
    // Pass filters to parent
    onFilterChange(newFilters)
  }

  // Handle date range change
  const handleDateRangeChange = (range: DateRange): void => {
    setDateRange(range)
    onDateRangeChange(range)
  }

  // Handle compare range change
  const handleCompareChange = (range: DateRange | null): void => {
    setCompareRange(range)
    if (onCompareChange) {
      onCompareChange(range)
    }
  }

  // Handle reset
  const handleReset = (): void => {
    setFilters({})
    setDateRange(undefined)
    setCompareRange(null)
    setActiveFilterCount(0)
    
    if (onReset) {
      onReset()
    }
  }

  // Handle export
  const handleExport = (format: 'csv' | 'excel' | 'pdf'): void => {
    if (onExport) {
      onExport(format)
    }
  }

  // Toggle filters visibility
  const toggleFilters = (): void => {
    setShowFilters(!showFilters)
  }

  return (
    <Card className="glass-card border-white/20">
      <CardHeader className="pb-2">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <CardTitle className="text-white text-lg flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            {t.dateRange}
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleFilters}
              className="text-white/70 hover:text-white hover:bg-white/10"
            >
              <Filter className="h-4 w-4 mr-2" />
              {showFilters ? t.hideFilters : t.showFilters}
              {showFilters ? <ChevronUp className="h-4 w-4 ml-1" /> : <ChevronDown className="h-4 w-4 ml-1" />}
              {activeFilterCount > 0 && (
                <Badge className="ml-2 bg-blue-500/30 text-blue-200 border-blue-500/50">
                  {activeFilterCount}
                </Badge>
              )}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleReset}
              disabled={loading}
              className="text-white/70 hover:text-white hover:bg-white/10"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              {t.reset}
            </Button>
            {onExport && (
              <div className="relative group">
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-white/70 hover:text-white hover:bg-white/10"
                >
                  <Download className="h-4 w-4 mr-2" />
                  {t.export}
                </Button>
                <div className="absolute right-0 mt-2 w-32 bg-white/10 backdrop-blur-md border border-white/20 rounded-md shadow-lg overflow-hidden z-50 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                  <div className="py-1">
                    <button
                      onClick={() => handleExport('csv')}
                      className="block w-full px-4 py-2 text-sm text-white hover:bg-white/10 text-left"
                    >
                      {t.csv}
                    </button>
                    <button
                      onClick={() => handleExport('excel')}
                      className="block w-full px-4 py-2 text-sm text-white hover:bg-white/10 text-left"
                    >
                      {t.excel}
                    </button>
                    <button
                      onClick={() => handleExport('pdf')}
                      className="block w-full px-4 py-2 text-sm text-white hover:bg-white/10 text-left"
                    >
                      {t.pdf}
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Date Range Picker */}
        <DateRangePicker
          value={dateRange}
          onChange={handleDateRangeChange}
          locale={language}
          showCompare={true}
          onCompareChange={handleCompareChange}
          compareValue={compareRange}
          className="w-full"
        />
        
        {/* Filters Panel (collapsible) */}
        {showFilters && (
          <KPIFilterPanel
            categories={categories}
            onFilterChange={handleFilterChange}
            initialFilters={filters}
            language={language}
            onReset={handleReset}
            loading={loading}
          />
        )}
      </CardContent>
    </Card>
  )
}
