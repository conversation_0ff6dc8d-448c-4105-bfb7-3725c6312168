/**
 * KPI Filter Panel Component
 * Advanced filtering panel for KPI data with multiple filter types
 */

import React, { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion'
import { Badge } from '@/components/ui/badge'
import { Slider } from '@/components/ui/slider'
import { DateRangePicker, DateRange } from '@/components/common/DateRangePicker'
import { KPICategory } from '@/services/kpiService'
import { Filter, X, Check, RefreshCw, Calendar, Target, TrendingUp, TrendingDown, Activity } from 'lucide-react'

export interface KPIFilterValue {
  categories?: string[]
  status?: string
  frequency?: string
  measurementType?: string
  trendDirection?: string
  targetAchievement?: [number, number] // Min and max percentage
  dateRange?: DateRange
  search?: string
}

export interface KPIFilterPanelProps {
  categories: KPICategory[]
  onFilterChange: (filters: KPIFilterValue) => void
  initialFilters?: KPIFilterValue
  language: 'ar' | 'en'
  onReset?: () => void
  loading?: boolean
}

const translations = {
  ar: {
    filters: 'الفلاتر',
    categories: 'الفئات',
    status: 'الحالة',
    frequency: 'التكرار',
    measurementType: 'نوع القياس',
    trendDirection: 'اتجاه الاتجاه',
    targetAchievement: 'تحقيق الهدف',
    dateRange: 'النطاق الزمني',
    search: 'بحث',
    active: 'نشط',
    inactive: 'غير نشط',
    archived: 'مؤرشف',
    daily: 'يومي',
    weekly: 'أسبوعي',
    monthly: 'شهري',
    quarterly: 'ربع سنوي',
    yearly: 'سنوي',
    number: 'رقم',
    percentage: 'نسبة مئوية',
    currency: 'عملة',
    up: 'صعود',
    down: 'هبوط',
    stable: 'مستقر',
    any: 'الكل',
    selectAll: 'اختر الكل',
    clearAll: 'مسح الكل',
    apply: 'تطبيق',
    reset: 'إعادة تعيين',
    activeFilters: 'الفلاتر النشطة',
    noFilters: 'لا توجد فلاتر نشطة',
    min: 'الحد الأدنى',
    max: 'الحد الأقصى'
  },
  en: {
    filters: 'Filters',
    categories: 'Categories',
    status: 'Status',
    frequency: 'Frequency',
    measurementType: 'Measurement Type',
    trendDirection: 'Trend Direction',
    targetAchievement: 'Target Achievement',
    dateRange: 'Date Range',
    search: 'Search',
    active: 'Active',
    inactive: 'Inactive',
    archived: 'Archived',
    daily: 'Daily',
    weekly: 'Weekly',
    monthly: 'Monthly',
    quarterly: 'Quarterly',
    yearly: 'Yearly',
    number: 'Number',
    percentage: 'Percentage',
    currency: 'Currency',
    up: 'Up',
    down: 'Down',
    stable: 'Stable',
    any: 'Any',
    selectAll: 'Select All',
    clearAll: 'Clear All',
    apply: 'Apply',
    reset: 'Reset',
    activeFilters: 'Active Filters',
    noFilters: 'No active filters',
    min: 'Min',
    max: 'Max'
  }
}

export function KPIFilterPanel({
  categories,
  onFilterChange,
  initialFilters = {},
  language,
  onReset,
  loading = false
// @ts-ignore
}: KPIFilterPanelProps as any): (React as any).ReactElement {
  const [filters, setFilters] = useState<KPIFilterValue>(initialFilters)
  const [selectedCategories, setSelectedCategories] = useState<string[]>((initialFilters as any).categories || [])
  const [targetRange, setTargetRange] = useState<[number, number]>((initialFilters as any).targetAchievement || [0, 200])
  const [activeFilterCount, setActiveFilterCount] = useState<number>(0)
  
  const t = translations[language]
  const isRTL = language === 'ar'

  // Initialize filters from props
  // @ts-ignore
  useEffect(( as any) => {
    setFilters(initialFilters as any)
    setSelectedCategories((initialFilters as any as any).categories || [])
    setTargetRange((initialFilters as any as any).targetAchievement || [0, 200])
  // @ts-ignore
  }, [initialFilters])

  // Count active filters
  // @ts-ignore
  useEffect(( as any) => {
    let count = 0
    if ((filters as any).categories?.length) count++
    if ((filters as any).status) count++
    if ((filters as any).frequency) count++
    if ((filters as any).measurementType) count++
    if ((filters as any).trendDirection) count++
    if ((filters as any).targetAchievement && 
        ((filters as any).targetAchievement[0] > 0 || (filters as any).targetAchievement[1] < 200)) count++
    if ((filters as any).dateRange) count++
    if ((filters as any).search) count++
    setActiveFilterCount(count as any)
  // @ts-ignore
  }, [filters])

  // Handle category selection
  const handleCategoryChange = (categoryId: string, checked: boolean): void => {
    let newCategories: string[]
    
    if (checked) {
      newCategories = [...selectedCategories, categoryId]
    } else {
      newCategories = (selectedCategories as any).filter(id => id !== categoryId as any)
    }
    
    setSelectedCategories(newCategories as any)
    
    const newFilters = {
      ...filters,
      categories: (newCategories as any).length > 0 ? newCategories : undefined
    }
    
    setFilters(newFilters as any)
    onFilterChange(newFilters as any)
  }

  // Handle select all categories
  const handleSelectAllCategories = (): void => {
    const allCategoryIds = (categories as any).map(cat => (cat as any as any).id)
    setSelectedCategories(allCategoryIds as any)
    
    const newFilters = {
      ...filters,
      categories: allCategoryIds
    }
    
    setFilters(newFilters as any)
    onFilterChange(newFilters as any)
  }

  // Handle clear all categories
  const handleClearAllCategories = (): void => {
    setSelectedCategories([] as any)
    
    const newFilters = {
      ...filters,
      categories: undefined
    }
    
    setFilters(newFilters as any)
    onFilterChange(newFilters as any)
  }

  // Handle select change
  const handleSelectChange = (key: keyof KPIFilterValue, value: string): void => {
    const newFilters = {
      ...filters,
      [key]: value === 'any' ? undefined : value
    }
    
    setFilters(newFilters as any)
    onFilterChange(newFilters as any)
  }

  // Handle target achievement range change
  const handleTargetRangeChange = (value: [number, number]): void => {
    setTargetRange(value as any)
    
    const newFilters = {
      ...filters,
      targetAchievement: value[0] === 0 && value[1] === 200 ? undefined : value
    }
    
    setFilters(newFilters as any)
    onFilterChange(newFilters as any)
  }

  // Handle date range change
  const handleDateRangeChange = (range: DateRange): void => {
    const newFilters = {
      ...filters,
      dateRange: range
    }
    
    setFilters(newFilters as any)
    onFilterChange(newFilters as any)
  }

  // Handle search change
  const handleSearchChange = (value: string): void => {
    const newFilters = {
      ...filters,
      search: value || undefined
    }
    
    setFilters(newFilters as any)
    onFilterChange(newFilters as any)
  }

  // Handle reset
  const handleReset = (): void => {
    setFilters({} as any)
    setSelectedCategories([] as any)
    setTargetRange([0, 200] as any)
    onFilterChange({} as any)
    // @ts-ignore
    if (onReset) onReset( as any)
  }

  return (
    <div className={`glass-card border-white/20 p-4 rounded-lg space-y-4 ${isRTL ? 'rtl' : 'ltr'}`}>
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-white flex items-center gap-2">
          <Filter className="h-5 w-5" />
          {(t as any).filters}
          {activeFilterCount > 0 && (
            <Badge className="bg-blue-500/30 text-blue-200 border-blue-500/50">
              {activeFilterCount}
            </Badge>
          )}
        </h3>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleReset}
          disabled={loading}
          className="text-white/70 hover:text-white hover:bg-white/10"
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          {(t as any).reset}
        </Button>
      </div>

      <div className="space-y-4">
        {/* Search */}
        <div className="space-y-2">
          <Label htmlFor="search" className="text-white">{(t as any).search}</Label>
          <Input
            id="search"
            value={(filters as any).search || ''}
            onChange={(e: any) => handleSearchChange((e as any as any).target.value)}
            placeholder={language === 'ar' ? 'البحث في المؤشرات...' : 'Search KPIs...'}
            className="glass-input"
          />
        </div>

        {/* Date Range */}
        <div className="space-y-2">
          <Label className="text-white">{(t as any).dateRange}</Label>
          <DateRangePicker
            value={(filters as any).dateRange}
            onChange={handleDateRangeChange}
            locale={language}
            className="w-full"
          />
        </div>

        <Accordion type="multiple" defaultValue={['categories', 'status']} className="space-y-2">
          {/* Categories */}
          <AccordionItem value="categories" className="border-white/10">
            <AccordionTrigger className="text-white hover:no-underline py-2">
              {(t as any).categories}
            </AccordionTrigger>
            <AccordionContent>
              <div className="space-y-2">
                <div className="flex justify-between mb-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleSelectAllCategories}
                    className="text-xs text-white/70 hover:text-white hover:bg-white/10"
                  >
                    {(t as any).selectAll}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleClearAllCategories}
                    className="text-xs text-white/70 hover:text-white hover:bg-white/10"
                  >
                    {(t as any).clearAll}
                  </Button>
                </div>
                <div className="space-y-2 max-h-40 overflow-y-auto pr-2">
                  // @ts-ignore
                  {(categories as any).map((category as any) => (
                    <div key={(category as any).id} className="flex items-center space-x-2">
                      <Checkbox
                        id={`category-${(category as any).id}`}
                        checked={(selectedCategories as any).includes((category as any as any).id)}
                        onCheckedChange={(checked) => handleCategoryChange((category as any as any).id, !!checked)}
                      />
                      <Label
                        htmlFor={`category-${(category as any).id}`}
                        className="text-white cursor-pointer flex items-center"
                      >
                        <div
                          className="w-3 h-3 rounded-full mr-2"
                          style={{ backgroundColor: (category as any).color }}
                        />
                        {language === 'ar' ? (category as any).name_ar : (category as any).name}
                      </Label>
                    </div>
                  // @ts-ignore
                  ))}
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>

          {/* Status */}
          <AccordionItem value="status" className="border-white/10">
            <AccordionTrigger className="text-white hover:no-underline py-2">
              {(t as any).status}
            </AccordionTrigger>
            <AccordionContent>
              <Select
                value={(filters as any).status || 'any'}
                onValueChange={(value) => handleSelectChange('status', value as any)}
              >
                <SelectTrigger className="glass-input">
                  <SelectValue placeholder={(t as any).any} />
                </SelectTrigger>
                <SelectContent className="glass-card border-white/20">
                  <SelectItem value="any">{(t as any).any}</SelectItem>
                  <SelectItem value="active">{(t as any).active}</SelectItem>
                  <SelectItem value="inactive">{(t as any).inactive}</SelectItem>
                  <SelectItem value="archived">{(t as any).archived}</SelectItem>
                </SelectContent>
              </Select>
            </AccordionContent>
          </AccordionItem>

          {/* Frequency */}
          <AccordionItem value="frequency" className="border-white/10">
            <AccordionTrigger className="text-white hover:no-underline py-2">
              {(t as any).frequency}
            </AccordionTrigger>
            <AccordionContent>
              <Select
                value={(filters as any).frequency || 'any'}
                onValueChange={(value) => handleSelectChange('frequency', value as any)}
              >
                <SelectTrigger className="glass-input">
                  <SelectValue placeholder={(t as any).any} />
                </SelectTrigger>
                <SelectContent className="glass-card border-white/20">
                  <SelectItem value="any">{(t as any).any}</SelectItem>
                  <SelectItem value="daily">{(t as any).daily}</SelectItem>
                  <SelectItem value="weekly">{(t as any).weekly}</SelectItem>
                  <SelectItem value="monthly">{(t as any).monthly}</SelectItem>
                  <SelectItem value="quarterly">{(t as any).quarterly}</SelectItem>
                  <SelectItem value="yearly">{(t as any).yearly}</SelectItem>
                </SelectContent>
              </Select>
            </AccordionContent>
          </AccordionItem>

          {/* Measurement Type */}
          <AccordionItem value="measurementType" className="border-white/10">
            <AccordionTrigger className="text-white hover:no-underline py-2">
              {(t as any).measurementType}
            </AccordionTrigger>
            <AccordionContent>
              <Select
                value={(filters as any).measurementType || 'any'}
                onValueChange={(value) => handleSelectChange('measurementType', value as any)}
              >
                <SelectTrigger className="glass-input">
                  <SelectValue placeholder={(t as any).any} />
                </SelectTrigger>
                <SelectContent className="glass-card border-white/20">
                  <SelectItem value="any">{(t as any).any}</SelectItem>
                  <SelectItem value="number">{(t as any).number}</SelectItem>
                  <SelectItem value="percentage">{(t as any).percentage}</SelectItem>
                  <SelectItem value="currency">{(t as any).currency}</SelectItem>
                </SelectContent>
              </Select>
            </AccordionContent>
          </AccordionItem>

          {/* Trend Direction */}
          <AccordionItem value="trendDirection" className="border-white/10">
            <AccordionTrigger className="text-white hover:no-underline py-2">
              {(t as any).trendDirection}
            </AccordionTrigger>
            <AccordionContent>
              <Select
                value={(filters as any).trendDirection || 'any'}
                onValueChange={(value) => handleSelectChange('trendDirection', value as any)}
              >
                <SelectTrigger className="glass-input">
                  <SelectValue placeholder={(t as any).any} />
                </SelectTrigger>
                <SelectContent className="glass-card border-white/20">
                  <SelectItem value="any">{(t as any).any}</SelectItem>
                  <SelectItem value="up" className="flex items-center">
                    <TrendingUp className="h-4 w-4 mr-2 text-green-400" />
                    {(t as any).up}
                  </SelectItem>
                  <SelectItem value="down" className="flex items-center">
                    <TrendingDown className="h-4 w-4 mr-2 text-red-400" />
                    {(t as any).down}
                  </SelectItem>
                  <SelectItem value="stable" className="flex items-center">
                    <Activity className="h-4 w-4 mr-2 text-blue-400" />
                    {(t as any).stable}
                  </SelectItem>
                </SelectContent>
              </Select>
            </AccordionContent>
          </AccordionItem>

          {/* Target Achievement */}
          <AccordionItem value="targetAchievement" className="border-white/10">
            <AccordionTrigger className="text-white hover:no-underline py-2">
              {(t as any).targetAchievement}
            </AccordionTrigger>
            <AccordionContent>
              <div className="space-y-4">
                <Slider
                  value={targetRange}
                  min={0}
                  max={200}
                  step={5}
                  onValueChange={handleTargetRangeChange}
                  className="my-6"
                />
                <div className="flex justify-between text-sm text-white/70">
                  <div>
                    {(t as any).min}: {targetRange[0]}%
                  </div>
                  <div>
                    {(t as any).max}: {targetRange[1]}%
                  </div>
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>
    </div>
  )
}

export default KPIFilterPanel
