import React from 'react';
import { Building2, Mail, Phone, MapPin, ExternalLink } from 'lucide-react'

interface FooterProps {
  language: 'ar' | 'en'
}

const footerTranslations = {
  ar: {
    companyName: 'نمو',
    companyTagline: 'منصة إدارة المؤسسات الشاملة المدعومة بالذكاء الاصطناعي',
    companyDescription: 'نمو هو نظام ERP متكامل يجمع بين إدارة الموارد البشرية، CRM، إدارة المشاريع، مؤشرات الأداء الرئيسية، والذكاء الاصطناعي في منصة واحدة.',
    quickLinks: 'روابط سريعة',
    about: 'حول نمو',
    features: 'المميزات',
    support: 'الدعم',
    contact: 'اتصل بنا',
    privacy: 'سياسة الخصوصية',
    terms: 'شروط الاستخدام',
    solutions: 'الحلول',
    erpSolution: 'نظام ERP',
    crmSolution: 'نظام CRM',
    hrSolution: 'إدارة الموارد البشرية',
    projectManagement: 'إدارة المشاريع',
    contactInfo: 'معلومات الاتصال',
    address: 'الرياض، المملكة العربية السعودية',
    email: '<EMAIL>',
    phone: '+966 11 123 4567',
    followUs: 'تابعنا',
    allRightsReserved: 'جميع الحقوق محفوظة',
    poweredBy: 'مدعوم بالذكاء الاصطناعي'
  },
  en: {
    companyName: 'Numu',
    companyTagline: 'AI-Powered Comprehensive Enterprise Management Platform',
    companyDescription: 'Numu is an integrated ERP system that combines HR, CRM, Project Management, KPI Management, and AI in one platform.',
    quickLinks: 'Quick Links',
    about: 'About Numu',
    features: 'Features',
    support: 'Support',
    contact: 'Contact Us',
    privacy: 'Privacy Policy',
    terms: 'Terms of Service',
    solutions: 'Solutions',
    erpSolution: 'ERP System',
    crmSolution: 'CRM System',
    hrSolution: 'Human Resources',
    projectManagement: 'Project Management',
    contactInfo: 'Contact Information',
    address: 'Riyadh, Saudi Arabia',
    email: '<EMAIL>',
    phone: '+966 11 123 4567',
    followUs: 'Follow Us',
    allRightsReserved: 'All Rights Reserved',
    poweredBy: 'Powered by AI'
  }
}

export default function Footer({ language }: FooterProps): React.ReactElement {
  const t = footerTranslations[language]
  const isRTL = language === 'ar'
  const currentYear = new Date().getFullYear()

  return (
    <footer className="relative z-10 bg-black/40 border-t border-white/10" dir={isRTL ? 'rtl' : 'ltr'}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className={`grid grid-cols-1 md:grid-cols-4 gap-8 ${isRTL ? 'text-right' : 'text-left'}`}>

          {/* Company Info */}
          <div className="md:col-span-2">
            <div className={`flex items-center gap-3 mb-6 ${isRTL ? 'flex-row-reverse' : 'flex-row'}`}>
              <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center glow">
                {isRTL ? (
                  <span className="text-white text-xl font-bold">ن</span>
                ) : (
                  <Building2 className="h-6 w-6 text-white" />
                )}
              </div>
              <div>
                <h3 className={`text-2xl font-bold text-white ${isRTL ? 'font-arabic' : ''}`}>
                  {t.companyName}
                </h3>
                <p className={`text-white/60 text-sm ${isRTL ? 'font-arabic' : ''}`}>
                  {t.companyTagline}
                </p>
              </div>
            </div>
            <p className={`text-white/70 mb-6 max-w-md leading-relaxed ${isRTL ? 'font-arabic' : ''}`}>
              {t.companyDescription}
            </p>

            {/* Contact Info */}
            <div className="space-y-3">
              <div className={`flex items-center gap-3 text-white/70 ${isRTL ? 'flex-row-reverse' : 'flex-row'}`}>
                <MapPin className="h-4 w-4 flex-shrink-0" />
                <span className={`text-sm ${isRTL ? 'font-arabic' : ''}`}>{t.address}</span>
              </div>
              <div className={`flex items-center gap-3 text-white/70 ${isRTL ? 'flex-row-reverse' : 'flex-row'}`}>
                <Mail className="h-4 w-4 flex-shrink-0" />
                <span className="text-sm">{t.email}</span>
              </div>
              <div className={`flex items-center gap-3 text-white/70 ${isRTL ? 'flex-row-reverse' : 'flex-row'}`}>
                <Phone className="h-4 w-4 flex-shrink-0" />
                <span className="text-sm">{t.phone}</span>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className={`text-lg font-semibold text-white mb-4 ${isRTL ? 'font-arabic' : ''}`}>
              {t.quickLinks}
            </h4>
            <ul className="space-y-3">
              <li>
                <a href="#" className={`text-white/70 hover:text-white transition-colors text-sm ${isRTL ? 'font-arabic' : ''}`}>
                  {t.about}
                </a>
              </li>
              <li>
                <a href="#" className={`text-white/70 hover:text-white transition-colors text-sm ${isRTL ? 'font-arabic' : ''}`}>
                  {t.features}
                </a>
              </li>
              <li>
                <a href="#" className={`text-white/70 hover:text-white transition-colors text-sm ${isRTL ? 'font-arabic' : ''}`}>
                  {t.support}
                </a>
              </li>
              <li>
                <a href="#" className={`text-white/70 hover:text-white transition-colors text-sm ${isRTL ? 'font-arabic' : ''}`}>
                  {t.contact}
                </a>
              </li>
            </ul>
          </div>

          {/* Solutions & Social */}
          <div>
            <h4 className={`text-lg font-semibold text-white mb-4 ${isRTL ? 'font-arabic' : ''}`}>
              {t.solutions}
            </h4>
            <ul className="space-y-3 mb-6">
              <li>
                <a href="#" className={`text-white/70 hover:text-white transition-colors text-sm ${isRTL ? 'font-arabic' : ''}`}>
                  {t.erpSolution}
                </a>
              </li>
              <li>
                <a href="#" className={`text-white/70 hover:text-white transition-colors text-sm ${isRTL ? 'font-arabic' : ''}`}>
                  {t.crmSolution}
                </a>
              </li>
              <li>
                <a href="#" className={`text-white/70 hover:text-white transition-colors text-sm ${isRTL ? 'font-arabic' : ''}`}>
                  {t.hrSolution}
                </a>
              </li>
              <li>
                <a href="#" className={`text-white/70 hover:text-white transition-colors text-sm ${isRTL ? 'font-arabic' : ''}`}>
                  {t.projectManagement}
                </a>
              </li>
            </ul>

            {/* Social Media */}
            <div>
              <h5 className={`text-sm font-medium text-white mb-3 ${isRTL ? 'font-arabic' : ''}`}>
                {t.followUs}
              </h5>
              <div className={`flex gap-3 ${isRTL ? 'flex-row-reverse justify-end' : 'flex-row'}`}>
                <a href="#" className="w-10 h-10 glass-card border-white/20 rounded-lg flex items-center justify-center hover:border-white/40 transition-colors" title="Facebook">
                  <ExternalLink className="h-5 w-5 text-white/70" />
                </a>
                <a href="#" className="w-10 h-10 glass-card border-white/20 rounded-lg flex items-center justify-center hover:border-white/40 transition-colors" title="Twitter">
                  <ExternalLink className="h-5 w-5 text-white/70" />
                </a>
                <a href="#" className="w-10 h-10 glass-card border-white/20 rounded-lg flex items-center justify-center hover:border-white/40 transition-colors" title="LinkedIn">
                  <ExternalLink className="h-5 w-5 text-white/70" />
                </a>
                <a href="#" className="w-10 h-10 glass-card border-white/20 rounded-lg flex items-center justify-center hover:border-white/40 transition-colors" title="Instagram">
                  <ExternalLink className="h-5 w-5 text-white/70" />
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Copyright */}
        <div className="border-t border-white/10 mt-12 pt-8">
          <div className={`flex flex-col md:flex-row justify-between items-center gap-4 ${isRTL ? 'md:flex-row-reverse' : ''}`}>
            <p className={`text-white/60 text-sm ${isRTL ? 'font-arabic' : ''}`}>
              © {currentYear} {t.companyName}. {t.allRightsReserved}
            </p>
            <div className={`flex items-center gap-4 ${isRTL ? 'flex-row-reverse' : 'flex-row'}`}>
              <a href="#" className={`text-white/60 hover:text-white transition-colors text-sm ${isRTL ? 'font-arabic' : ''}`}>
                {t.privacy}
              </a>
              <a href="#" className={`text-white/60 hover:text-white transition-colors text-sm ${isRTL ? 'font-arabic' : ''}`}>
                {t.terms}
              </a>
              <span className={`text-white/40 text-sm ${isRTL ? 'font-arabic' : ''}`}>
                {t.poweredBy}
              </span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
