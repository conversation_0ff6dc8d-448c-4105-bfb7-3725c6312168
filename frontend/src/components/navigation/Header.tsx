/**
 * Header Component
 * Top navigation bar with user menu, notifications, and controls
 */

import React, { useState, memo, useCallback, useMemo } from 'react'
import { useDispatch } from 'react-redux'
import { useLocation, useNavigate } from 'react-router-dom'
import { useNavigation } from '../../hooks/useNavigationService' // FIXED: Add navigation service
import { Button } from '@/components/ui/button'
import {
  Menu,
  X,
  Globe,
  Bell,
  LogOut,
  User,
  Settings,
  ChevronDown,
  Wifi,
  WifiOff,
  Building2,
  ChevronRight,
  ChevronLeft
} from 'lucide-react'
import type { AppDispatch } from '../../store'
import { logoutUser } from '../../store/slices/authSlice'
import AdvancedGlobalSearch from '../search/AdvancedGlobalSearch'
import NotificationBell from '../notifications/NotificationBell'
import useAdvancedFeatures from '../../hooks/useAdvancedFeatures'
import { navigationTranslations } from './translations'

interface HeaderProps {
  sidebarOpen: boolean
  onToggleSidebar: () => void
  language: 'ar' | 'en'
  onLanguageChange: (lang: 'ar' | 'en') => void
  user: any
  unreadCount: number
}

// Enhanced translations for header
const headerTranslations = {
  ar: {
    companyName: 'نمو',
    companySubtitle: 'نظام إدارة المؤسسات',
    toggleMenu: 'تبديل القائمة',
    closeMenu: 'إغلاق القائمة',
    openMenu: 'فتح القائمة',
    switchToEnglish: 'تغيير إلى الإنجليزية',
    switchToArabic: 'تغيير إلى العربية',
    notifications: 'الإشعارات',
    profile: 'الملف الشخصي',
    settings: 'الإعدادات',
    logout: 'تسجيل الخروج',
    search: 'البحث',
    connected: 'متصل',
    disconnected: 'غير متصل',
    dashboard: 'لوحة التحكم',
    home: 'الرئيسية'
  },
  en: {
    companyName: 'Numu',
    companySubtitle: 'Enterprise Management System',
    toggleMenu: 'Toggle Menu',
    closeMenu: 'Close Menu',
    openMenu: 'Open Menu',
    switchToEnglish: 'Switch to English',
    switchToArabic: 'Switch to Arabic',
    notifications: 'Notifications',
    profile: 'Profile',
    settings: 'Settings',
    logout: 'Logout',
    search: 'Search',
    connected: 'Connected',
    disconnected: 'Disconnected',
    dashboard: 'Dashboard',
    home: 'Home'
  }
}

const Header: React.FC<HeaderProps> = memo(({
  sidebarOpen,
  onToggleSidebar,
  language,
  onLanguageChange,
  user,
  unreadCount // Reserved for future notification count display
}) => {
  const dispatch = useDispatch<AppDispatch>()
  const navigate = useNavigate()
  const location = useLocation()
  const [userMenuOpen, setUserMenuOpen] = useState<boolean>(false)

  // Advanced features hook
  const { notifications } = useAdvancedFeatures({
    enableSearch: true,
    enableNotifications: true,
    enableExport: false
  })

  // Get translations
  const t = headerTranslations[language]
  const navT = navigationTranslations[language]

  const navigation = useNavigation() // FIXED: Use navigation service

  const handleLogout = useCallback(async () => {
    try {
      await dispatch(logoutUser()).unwrap()
      // FIXED: Use navigation service instead of window.location
      navigation.navigateToLogin()
    } catch (error) {
      console.error('Logout failed:', error)
      // Navigate to login anyway using navigation service
      navigation.navigateToLogin()
    }
  }, [dispatch, navigation])

  const getUserDisplayName = useMemo(() => {
    if (user?.first_name && user?.last_name) {
      return `${user.first_name} ${user.last_name}`
    }
    return user?.username || user?.email || (language === 'ar' ? 'مستخدم' : 'User')
  }, [user?.first_name, user?.last_name, user?.username, user?.email, language])

  const getUserRole = useMemo(() => {
    // FIXED: Use role name_ar from backend if available, otherwise use translation mapping
    if (language === 'ar' && user?.role?.name_ar) {
      return user.role.name_ar
    }

    const role = user?.role?.name || user?.user_type || user?.role || 'user'

    // FIXED: Map backend role names (uppercase) to frontend role names (lowercase with underscores)
    const roleMapping = {
      'SUPERADMIN': 'super_admin',
      'ADMIN': 'admin',
      'HR_MANAGER': 'hr_manager',
      'FINANCE_MANAGER': 'finance_manager',
      'DEPARTMENT_MANAGER': 'department_manager',
      'SALES_MANAGER': 'sales_manager',
      'EMPLOYEE': 'employee',
      'USER': 'user'
    }

    const mappedRole = roleMapping[role as keyof typeof roleMapping] || role.toLowerCase()

    // Translate role names
    const roleTranslations = {
      ar: {
        super_admin: 'مدير النظام الرئيسي',
        admin: 'مدير النظام',
        hr_manager: 'مدير الموارد البشرية',
        finance_manager: 'مدير المالية',
        sales_manager: 'مدير المبيعات',
        department_manager: 'مدير القسم',
        employee: 'موظف',
        user: 'مستخدم'
      },
      en: {
        super_admin: 'Super Administrator',
        admin: 'Administrator',
        hr_manager: 'HR Manager',
        finance_manager: 'Finance Manager',
        sales_manager: 'Sales Manager',
        department_manager: 'Department Manager',
        employee: 'Employee',
        user: 'User'
      }
    }
    const translations = roleTranslations[language] as Record<string, string>
    return translations[mappedRole] || mappedRole
  }, [user?.role, user?.user_type, language])

  // Get current page title based on route
  const getCurrentPageTitle = useMemo(() => {
    const path = location.pathname
    if (path === '/' || path === '/dashboard') return navT.dashboard
    if (path.includes('/customers')) return navT.customers
    if (path.includes('/sales')) return navT.sales
    if (path.includes('/employees')) return navT.employees
    if (path.includes('/departments')) return navT.departments
    if (path.includes('/reports')) return navT.reports
    if (path.includes('/settings')) return navT.settings
    if (path.includes('/kpi')) return navT.kpiManagement
    if (path.includes('/projects')) return navT.projects
    if (path.includes('/hr')) return navT.hrManagement
    if (path.includes('/finance')) return navT.financialManagement
    return t.dashboard
  }, [location.pathname, navT, t])

  // Generate breadcrumb
  const getBreadcrumb = useMemo(() => {
    const pathSegments = location.pathname.split('/').filter(Boolean)
    const breadcrumb = [{ name: t.home, path: '/' }]

    if (pathSegments.length > 0) {
      breadcrumb.push({ name: getCurrentPageTitle, path: location.pathname })
    }

    return breadcrumb
  }, [location.pathname, getCurrentPageTitle, t])

  return (
    <header className="sticky top-0 z-30 glass-nav border-b border-white/10">
      <div className="flex items-center justify-between h-16 px-4 lg:px-6">
        {/* Left side - Company branding and navigation */}
        <div className={`flex items-center gap-4 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>
          {/* Sidebar toggle button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={(e: any) => {
              // CRITICAL FIX: Prevent event bubbling to avoid double-toggle
              e.stopPropagation()
              console.log('Header toggle button clicked, current state:', sidebarOpen)
              onToggleSidebar()
            }}
            className="glass-button text-white hover:bg-white/10 transition-colors"
            title={sidebarOpen ? t.closeMenu : t.openMenu}
          >
            {sidebarOpen ? (
              <X className="h-5 w-5" />
            ) : (
              <Menu className="h-5 w-5" />
            )}
          </Button>

         

          {/* Enhanced Breadcrumb Navigation with RTL Support */}
          <div className={`hidden lg:flex items-center gap-2 ${language === 'ar' ? 'mr-4' : 'ml-4'}`}>
            {getBreadcrumb.map((item, index) => (
              <div key={index} className={`flex items-center gap-2 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>
                {index > 0 && (
                  language === 'ar' ? (
                    <ChevronLeft className="h-4 w-4 text-white/50" />
                  ) : (
                    <ChevronRight className="h-4 w-4 text-white/50" />
                  )
                )}
                <span className={`text-sm header-breadcrumb ${index === getBreadcrumb.length - 1 ? 'active' : ''} ${language === 'ar' ? 'font-arabic' : ''}`}>
                  {item.name}
                </span>
              </div>
            ))}
          </div>

          {/* Current Page Title for Mobile */}
          <div className="lg:hidden">
            <h2 className="text-sm font-medium text-white">{getCurrentPageTitle}</h2>
          </div>
        </div>

        {/* Right side - User controls */}
        <div className={`flex items-center gap-3 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>
          {/* Advanced Global Search */}
          <div className="hidden md:block">
            <AdvancedGlobalSearch language={language} />
          </div>
          {/* Mobile Search Button */}
          <div className="block md:hidden">
            <Button
              variant="ghost"
              size="sm"
              className="glass-button text-white hover:bg-white/10 transition-colors"
              title={t.search}
              aria-label={language === 'ar' ? 'فتح البحث' : 'Open search'}
            >
              <span className="text-xs font-medium">
                {language === 'ar' ? 'بحث' : 'Search'}
              </span>
            </Button>
          </div>

          {/* Connection Status */}
          <div className="flex items-center" title={notifications.connectionStatus === 'connected' ? t.connected : t.disconnected}>
            {notifications.connectionStatus === 'connected' ? (
              <Wifi className="h-4 w-4 text-green-400" />
            ) : (
              <WifiOff className="h-4 w-4 text-red-400" />
            )}
          </div>

          {/* Enhanced Language Toggle with RTL Support */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onLanguageChange(language === 'ar' ? 'en' : 'ar')}
            className="glass-button text-white hover:bg-white/15 transition-all duration-300 language-toggle"
            title={language === 'ar' ? t.switchToEnglish : t.switchToArabic}
            aria-label={language === 'ar' ? 'تغيير اللغة إلى الإنجليزية' : 'Switch language to Arabic'}
          >
            <div className={`flex items-center gap-2 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>
              <div className="relative">
                <Globe className="h-4 w-4 flex-shrink-0" />
                {/* Language indicator dot */}
                <div className={`absolute -top-1 -right-1 w-2 h-2 rounded-full ${
                  language === 'ar' ? 'bg-green-400' : 'bg-blue-400'
                } animate-pulse`} />
              </div>
              <span className={`text-sm font-medium hidden sm:inline whitespace-nowrap ${language === 'ar' ? 'font-arabic' : ''}`}>
                {language === 'ar' ? 'English' : 'العربية'}
              </span>
              <span className={`text-xs font-bold sm:hidden ${language === 'ar' ? 'font-arabic' : ''}`}>
                {language === 'ar' ? 'EN' : 'ع'}
              </span>
              {/* RTL indicator */}
              {language === 'ar' && (
                <ChevronRight className="h-3 w-3 opacity-60 rtl-indicator" />
              )}
              {language === 'en' && (
                <ChevronLeft className="h-3 w-3 opacity-60 rtl-indicator" />
              )}
            </div>
          </Button>

          {/* Enhanced Notifications - TEMPORARILY DISABLED TO PREVENT THROTTLING */}
          <div className="text-white">
            {/* <NotificationBell language={language} /> */}
            <Button
              variant="ghost"
              size="sm"
              className="glass-button relative"
            >
              <Bell className="h-5 w-5" />
              <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                0
              </span>
            </Button>
          </div>

          {/* Enhanced User Menu */}
          <div className="relative">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setUserMenuOpen(!userMenuOpen)}
              className="glass-button text-white hover:bg-white/15 transition-all duration-300"
              aria-label={language === 'ar' ? 'فتح قائمة المستخدم' : 'Open user menu'}
              aria-expanded={userMenuOpen}
            >
              <div className={`flex items-center gap-3 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>
                <div className="w-9 h-9 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center shadow-lg">
                  <User className="h-5 w-5 text-white" />
                </div>
                {/* Desktop: Full user info */}
                <div className={`hidden md:block ${language === 'ar' ? 'text-right' : 'text-left'}`}>
                  <div className={`text-sm font-medium text-white ${language === 'ar' ? 'font-arabic' : ''}`}>
                    {getUserDisplayName}
                  </div>
                  <div className={`text-xs text-white/70 ${language === 'ar' ? 'font-arabic' : ''}`}>
                    {getUserRole}
                  </div>
                </div>
                {/* Mobile: Abbreviated user info */}
                <div className={`block md:hidden ${language === 'ar' ? 'text-right' : 'text-left'}`}>
                  <div className={`text-xs font-medium text-white ${language === 'ar' ? 'font-arabic' : ''}`}>
                    {getUserDisplayName.split(' ')[0]}
                  </div>
                  <div className={`text-xs text-white/70 ${language === 'ar' ? 'font-arabic' : ''}`}>
                    {getUserRole.split(' ')[0]}
                  </div>
                </div>
                <ChevronDown className={`h-4 w-4 transition-transform duration-200 ${userMenuOpen ? 'rotate-180' : ''}`} />
              </div>
            </Button>

            {/* Enhanced User Dropdown Menu */}
            {userMenuOpen && (
              <div className={`absolute top-full mt-3 w-56 glass-card border border-white/20 shadow-2xl z-50 animate-fade-in-up ${
                language === 'ar' ? 'left-0' : 'right-0'
              }`}>
                {/* User Info Header */}
                <div className={`px-4 py-3 border-b border-white/10 ${language === 'ar' ? 'text-right' : 'text-left'}`}>
                  <div className={`text-sm font-medium text-white ${language === 'ar' ? 'font-arabic' : ''}`}>
                    {getUserDisplayName}
                  </div>
                  <div className={`text-xs text-white/70 ${language === 'ar' ? 'font-arabic' : ''}`}>
                    {getUserRole}
                  </div>
                  <div className="text-xs text-white/50">{user?.email}</div>
                </div>

                {/* Menu Items */}
                <div className="py-2">
                  <button className={`w-full px-4 py-3 text-sm text-white hover:bg-white/10 transition-all duration-200 flex items-center gap-3 ${
                    language === 'ar' ? 'text-right flex-row-reverse' : 'text-left flex-row'
                  }`}>
                    <User className="h-4 w-4" />
                    <span className={language === 'ar' ? 'font-arabic' : ''}>{t.profile}</span>
                  </button>
                  <button className={`w-full px-4 py-3 text-sm text-white hover:bg-white/10 transition-all duration-200 flex items-center gap-3 ${
                    language === 'ar' ? 'text-right flex-row-reverse' : 'text-left flex-row'
                  }`}>
                    <Settings className="h-4 w-4" />
                    <span className={language === 'ar' ? 'font-arabic' : ''}>{t.settings}</span>
                  </button>

                  <hr className="my-2 border-white/20" />

                  <button
                    onClick={handleLogout}
                    className={`w-full px-4 py-3 text-sm text-red-400 hover:bg-red-500/10 hover:text-red-300 transition-all duration-200 flex items-center gap-3 ${
                      language === 'ar' ? 'text-right flex-row-reverse' : 'text-left flex-row'
                    }`}
                  >
                    <LogOut className="h-4 w-4" />
                    <span className={language === 'ar' ? 'font-arabic' : ''}>{t.logout}</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Close dropdowns when clicking outside */}
      {userMenuOpen && (
        <div
          className="fixed inset-0 z-20"
          onClick={() => setUserMenuOpen(false)}
        />
      )}


    </header>
  )
})

Header.displayName = 'Header'

export default Header
