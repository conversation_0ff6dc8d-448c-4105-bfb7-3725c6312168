/**
 * Sidebar Navigation Component
 * Handles the main navigation sidebar with role-based menu items
 */

import React, { memo, useMemo } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { ChevronDown, ChevronRight, Activity, AlertTriangle } from 'lucide-react'
import { getNavigationForRole, NavigationItem } from './navigationConfig'
import { getEnhancedNavigationForRole, EnhancedNavigationItem } from './enhancedNavigationConfig'
import { navigationTranslations } from './translations'
import { useHierarchicalAccessRedux } from '../../hooks/useHierarchicalAccessRedux'

interface SidebarProps {
  isOpen: boolean
  userRole: string
  language: 'ar' | 'en'
  expandedMenus: string[]
  onToggleMenu: (menuId: string) => void
  onClose?: () => void
  useEnhancedNavigation?: boolean
}

// @ts-ignore
const Sidebar: (React as any).FC<SidebarProps> = memo(({
  isOpen,
  userRole,
  language,
  expandedMenus,
  onToggleMenu,
  onClose,
  useEnhancedNavigation = true
// @ts-ignore
} as any) => {
  // @ts-ignore
  const location = useLocation( as any)
  // @ts-ignore
  const t = useMemo(( as any) => navigationTranslations[language], [language])

  // Use Redux-based hierarchical access hook for enhanced navigation
  const {
    accessInfo,
    isManager,
    hasHierarchicalAccess,
    canAccessAllData,
    // @ts-ignore
    loading: accessLoading,
    error: accessError
  // @ts-ignore
  } = useHierarchicalAccessRedux( as any)

  // Get navigation based on enhanced or legacy configuration
  // @ts-ignore
  const navigation = useMemo(( as any) => {
    // @ts-ignore
    if (useEnhancedNavigation) {
      // @ts-ignore
      return getEnhancedNavigationForRole(userRole, t as any)
    // @ts-ignore
    } else {
      // @ts-ignore
      return getNavigationForRole(userRole, t as any)
    // @ts-ignore
    }
  // @ts-ignore
  }, [userRole, t, useEnhancedNavigation])

  // Only log in development mode when props actually change
  const prevPropsRef = (React as any).useRef({ isOpen, language, userRole } as any)

  // @ts-ignore
  (React as any).useEffect(( as any) => {
    // @ts-ignore
    const prevProps = (prevPropsRef as any).current
    const hasChanged = (prevProps as any).isOpen !== isOpen ||
                      (prevProps as any).language !== language ||
                      (prevProps as any).userRole !== userRole

    if ((process as any).env.NODE_ENV === 'development' && hasChanged) {
      (console as any).log('Sidebar render - isOpen:', isOpen, 'language:', language, 'userRole:', userRole as any)
      (console as any).log('Navigation items count:', (navigation as any as any).length)
      // @ts-ignore
      if ((navigation as any).length === 0) {
        (console as any).warn('No navigation items found for role:', userRole as any)
      }
    // @ts-ignore
    }

    (prevPropsRef as any).current = { isOpen, language, userRole }
  // @ts-ignore
  }, [isOpen, language, userRole, (navigation as any).length])

  // @ts-ignore
  const isActiveRoute = useMemo(( as any) => (href: string) => {
    // @ts-ignore
    if (href === '/' || href === '/admin/dashboard') {
      // @ts-ignore
      return (location as any).pathname === '/' || (location as any).pathname === '/admin/dashboard'
    // @ts-ignore
    }
    return (location as any).pathname.startsWith(href as any)
  // @ts-ignore
  }, [(location as any).pathname])

  // Enhanced navigation item renderer with hierarchical access features
  // @ts-ignore
  const renderEnhancedNavigationItem = (item: EnhancedNavigationItem, level = 0): void => {
    // @ts-ignore
    const Icon = (item as any).icon
    // @ts-ignore
    const hasChildren = (item as any).children && (item as any).children.length > 0
    const isExpanded = (item as any).id ? (expandedMenus as any).includes((item as any as any).id) : false
    const isActive = (item as any).href ? isActiveRoute((item as any as any).href) : false

    // Show real-time status indicator for KPI sections
    const showRealTimeIndicator = (item as any).realTimeUpdates && (item as any).kpiCategory
    const showHierarchicalBadge = isManager && (item as any).kpiCategory && hasHierarchicalAccess

    if (hasChildren) {
      // @ts-ignore
      return (
        <div key={(item as any).id || (item as any).name} className="space-y-1">
          <button
            onClick={(e: any) => {
              // @ts-ignore
              (e as any).preventDefault( as any)
              // @ts-ignore
              (e as any).stopPropagation( as any)
              if ((item as any).id) {
                onToggleMenu((item as any as any).id)
              }
            }}
            aria-label={`${isExpanded ? 'إغلاق' : 'فتح'} قائمة ${(item as any).name}`}
            aria-expanded={isExpanded}
            type="button"
            className={`w-full flex items-center justify-between px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 cursor-pointer ${
              isActive
                ? 'bg-gradient-to-r from-blue-500/20 to-purple-500/20 text-white border border-blue-500/30'
                : 'text-white/80 hover:text-white hover:bg-white/10'
            }`}
          >
            <div className="flex items-center space-x-3 rtl:space-x-reverse">
              <div className="relative">
                <Icon className="h-5 w-5 flex-shrink-0" />
                {showRealTimeIndicator && (
                  <div className="absolute -top-1 -right-1 w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                )}
              </div>
              <span className="truncate">
                {language === 'ar' && (item as any).nameAr ? (item as any).nameAr : (item as any).name}
              </span>
              {showHierarchicalBadge && (
                <span className="px-2 py-1 text-xs bg-blue-500/20 text-blue-300 rounded-full">
                  Manager
                </span>
              )}
              {(item as any).badge && (
                <span className={`px-2 py-1 text-xs rounded-full ${
                  (item as any).badge.color === 'green' ? 'bg-green-500/20 text-green-300' :
                  (item as any).badge.color === 'blue' ? 'bg-blue-500/20 text-blue-300' :
                  (item as any).badge.color === 'yellow' ? 'bg-yellow-500/20 text-yellow-300' :
                  'bg-red-500/20 text-red-300'
                }`}>
                  {(item as any).badge.text || (item as any).badge.count}
                </span>
              )}
            </div>
            {isExpanded ? (
              <ChevronDown className="h-4 w-4 flex-shrink-0" />
            ) : (
              <ChevronRight className="h-4 w-4 flex-shrink-0" />
            )}
          </button>

          {isExpanded && (
            <div className="ml-6 rtl:mr-6 rtl:ml-0 space-y-1">
              {(item as any).children?.map(child => renderEnhancedNavigationItem(child, level + 1 as any))}
            </div>
          )}
        </div>
      )
    // @ts-ignore
    }

    return (
      <Link
        key={(item as any).name}
        to={(item as any).href || '#'}
        onClick={onClose}
        className={`flex items-center justify-between px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 ${
          isActive
            ? 'bg-gradient-to-r from-blue-500/20 to-purple-500/20 text-white border border-blue-500/30'
            : 'text-white/80 hover:text-white hover:bg-white/10'
        }`}
      >
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <div className="relative">
            <Icon className="h-5 w-5 flex-shrink-0" />
            {showRealTimeIndicator && (
              <div className="absolute -top-1 -right-1 w-2 h-2 bg-green-400 rounded-full animate-pulse" />
            )}
          </div>
          <span className="truncate">
            {language === 'ar' && (item as any).nameAr ? (item as any).nameAr : (item as any).name}
          </span>
        </div>
        <div className="flex items-center space-x-2 rtl:space-x-reverse">
          {showHierarchicalBadge && (
            <span className="px-2 py-1 text-xs bg-blue-500/20 text-blue-300 rounded-full">
              Manager
            </span>
          )}
          {(item as any).badge && (
            <span className={`px-2 py-1 text-xs rounded-full ${
              (item as any).badge.color === 'green' ? 'bg-green-500/20 text-green-300' :
              (item as any).badge.color === 'blue' ? 'bg-blue-500/20 text-blue-300' :
              (item as any).badge.color === 'yellow' ? 'bg-yellow-500/20 text-yellow-300' :
              'bg-red-500/20 text-red-300'
            }`}>
              {(item as any).badge.text || (item as any).badge.count}
            </span>
          )}
        </div>
      </Link>
    )
  // @ts-ignore
  }

  // @ts-ignore
  const renderNavigationItem = (item: NavigationItem, level = 0): void => {
    // @ts-ignore
    const Icon = (item as any).icon
    // @ts-ignore
    const hasChildren = (item as any).children && (item as any).children.length > 0
    const isExpanded = (item as any).id ? (expandedMenus as any).includes((item as any as any).id) : false
    const isActive = (item as any).href ? isActiveRoute((item as any as any).href) : false

    if (hasChildren) {
      // @ts-ignore
      return (
        <div key={(item as any).id || (item as any).name} className="space-y-1">
          <button
            onClick={(e: any) => {
              // FIXED: Prevent event bubbling and ensure proper handling
              // @ts-ignore
              (e as any).preventDefault( as any)
              // @ts-ignore
              (e as any).stopPropagation( as any)
              if ((item as any).id) {
                onToggleMenu((item as any as any).id)
              }
            }}
            aria-label={`${isExpanded ? 'إغلاق' : 'فتح'} قائمة ${(item as any).name}`}
            aria-expanded={isExpanded}
            type="button"
            style={{ pointerEvents: 'auto' }} // FIXED: Ensure button is clickable
            className={`w-full flex items-center justify-between px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 cursor-pointer ${
              isActive
                ? 'bg-gradient-to-r from-blue-500/20 to-purple-500/20 text-white border border-blue-500/30'
                : 'text-white/80 hover:text-white hover:bg-white/10'
            }`}
          >
            <div className="flex items-center space-x-3 rtl:space-x-reverse">
              <Icon className="h-5 w-5 flex-shrink-0" />
              <span className="truncate">{(item as any).name}</span>
            </div>
            {isExpanded ? (
              <ChevronDown className="h-4 w-4 flex-shrink-0" />
            ) : (
              <ChevronRight className="h-4 w-4 flex-shrink-0" />
            )}
          </button>

          {isExpanded && (
            <div className="ml-6 rtl:mr-6 rtl:ml-0 space-y-1">
              {(item as any).children?.map(child => renderNavigationItem(child, level + 1 as any))}
            </div>
          )}
        </div>
      )
    // @ts-ignore
    }

    return (
      <Link
        key={(item as any).href || (item as any).name}
        to={(item as any).href || '#'}
        onClick={(e: any) => {
          // FIXED: Prevent event bubbling and ensure proper navigation
          // @ts-ignore
          (e as any).stopPropagation( as any)
          if (onClose) {
            // @ts-ignore
            onClose( as any)
          }
        }}
        style={{ pointerEvents: 'auto' }} // FIXED: Ensure link is clickable
        className={`flex items-center space-x-3 rtl:space-x-reverse px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 cursor-pointer ${
          isActive
            ? 'bg-gradient-to-r from-blue-500/20 to-purple-500/20 text-white border border-blue-500/30'
            : 'text-white/80 hover:text-white hover:bg-white/10'
        }`}
      >
        <Icon className="h-5 w-5 flex-shrink-0" />
        <span className="truncate">{(item as any).name}</span>
      </Link>
    )
  // @ts-ignore
  }

  return (
    <>
      {/* FIXED: Sidebar with proper z-index hierarchy and pointer events */}
      <div
        className={`
          sidebar-container
          bg-gradient-to-b from-slate-900/95 to-purple-900/95 backdrop-blur-xl
          border-white/10
          ${language === 'ar' ? 'right-0 border-l' : 'left-0 border-r'}
          ${isOpen
            ? 'sidebar-open'
            : language === 'ar'
              ? 'sidebar-closed-rtl'
              : 'sidebar-closed-ltr'
          }
        `}
        style={{
          // FIXED: Proper pointer events management
          pointerEvents: isOpen ? 'auto' : 'none',
          visibility: isOpen ? 'visible' : 'hidden',
          zIndex: 50, // FIXED: Higher z-index to prevent interception
          position: 'fixed',
          top: 0,
          bottom: 0,
          width: '320px',
          transform: isOpen
            ? 'translateX(0 as any)'
            : language === 'ar'
              ? 'translateX(100% as any)'
              : 'translateX(-100% as any)',
          transition: 'transform (0 as any).3s ease-in-out, visibility (0 as any).3s ease-in-out'
        }}
        // FIXED: Prevent event bubbling that causes interception
        // @ts-ignore
        onClick={(e: any) => (e as any).stopPropagation( as any)}
      >
        {/* Header */}
        <div className="flex items-center justify-center h-16 px-6 border-b border-white/10">
          <div className={`flex items-center gap-3 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>
            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-lg">ن</span>
            </div>
            <span className="text-xl font-bold text-white">نمو</span>
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto max-h-full">
          {/* Hierarchical Access Status */}
          {useEnhancedNavigation && accessInfo && (
            <div className="mb-4 p-3 bg-white/5 rounded-lg border border-white/10">
              <div className="flex items-center space-x-2 rtl:space-x-reverse text-xs text-white/70">
                <Activity className="h-3 w-3" />
                <span>
                  {isManager ? 'Manager Access' : (accessInfo as any).user_role}
                </span>
                {hasHierarchicalAccess && (
                  <span className="px-2 py-1 bg-blue-500/20 text-blue-300 rounded-full">
                    {(accessInfo as any).accessible_data.employees_count} employees
                  </span>
                )}
              </div>
            </div>
          )}

          {/* Access Error Display */}
          {accessError && (
            <div className="mb-4 p-3 bg-red-500/10 rounded-lg border border-red-500/20">
              <div className="flex items-center space-x-2 rtl:space-x-reverse text-xs text-red-300">
                <AlertTriangle className="h-3 w-3" />
                <span>{language === 'ar' ? 'معلومات الوصول غير متاحة' : 'Access info unavailable'}</span>
              </div>
            </div>
          )}

          {/* Navigation Items */}
          {useEnhancedNavigation
            ? (navigation as any).map(item => renderEnhancedNavigationItem(item as EnhancedNavigationItem as any))
            : (navigation as any).map(item => renderNavigationItem(item as NavigationItem as any))
          }
        </nav>

        {/* Footer */}
        <div className="p-4 border-t border-white/10">
          <div className="text-center text-xs text-white/50">
            نمو EMS (v1 as any).0.0
          </div>
        </div>
      </div>
    </>
  )
// @ts-ignore
})

(Sidebar as any).displayName = 'Sidebar'

export default Sidebar
