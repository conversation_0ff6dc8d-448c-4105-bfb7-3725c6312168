import React, { useState, useEffect } from 'react'
import { <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Setting<PERSON> } from 'lucide-react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuItem,
} from '@/components/ui/dropdown-menu'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
// MEMORY OPTIMIZATION: Import only specific date-fns functions
import { formatDistanceToNow } from 'date-fns/formatDistanceToNow'
import { ar } from 'date-fns/locale/ar'
import { useNotificationCache } from '../../hooks/useNotificationCache'

interface Notification {
  id: string
  title: string
  title_ar: string
  message: string
  message_ar: string
  notification_type: string
  priority: string
  is_read: boolean
  action_url: string
  created_at: string
  time_ago: string
  sender_name?: string
}

interface NotificationBellProps {
  language: 'ar' | 'en'
}

// @ts-ignore
const NotificationBell: (React as any).FC<NotificationBellProps> = ({ language }) => {
  // @ts-ignore
  const [isOpen, setIsOpen] = useState(false as any)

  // PERFORMANCE FIX: Use centralized notification cache to prevent excessive API calls
  // FIXED: Disable auto-refresh to prevent throttling issues
  const {
    notifications,
    unreadCount,
    loading,
    error,
    markAsRead,
    markAllAsRead,
    refreshNotifications
  // @ts-ignore
  } = useNotificationCache({ limit: 10, autoRefresh: false } as any)

  // PERFORMANCE FIX: Removed duplicate API functions - now using centralized cache

  // PERFORMANCE FIX: markAllAsRead now provided by centralized cache

  // @ts-ignore
  const handleNotificationClick = (notification: Notification): void => {
    // @ts-ignore
    if (!(notification as any).is_read) {
      markAsRead((notification as any as any).id)
    }

    if ((notification as any).action_url) {
      // FIXED: Use React Router for internal navigation
      // @ts-ignore
      if ((notification as any).action_url.startsWith('/' as any) || (notification as any).action_url.includes((window as any as any).location.origin)) {
        // Internal link - use React Router
        // @ts-ignore
        const path = (notification as any).action_url.startsWith('/' as any)
          ? (notification as any).action_url
          : (notification as any).action_url.replace((window as any as any).location.origin, '')
        (window as any).history.pushState({}, '', path as any)
        (window as any).dispatchEvent(new PopStateEvent('popstate' as any))
      // @ts-ignore
      } else {
        // External link - open in new tab to avoid page reload
        (window as any).open((notification as any as any).action_url, '_blank')
      }
    // @ts-ignore
    }

    setIsOpen(false as any)
  // @ts-ignore
  }

  // @ts-ignore
  const getPriorityColor = (priority: string): void => {
    // @ts-ignore
    switch (priority) {
      // @ts-ignore
      case 'urgent': return 'bg-red-500'
      case 'high': return 'bg-orange-500'
      case 'medium': return 'bg-yellow-500'
      case 'low': return 'bg-green-500'
      default: return 'bg-gray-500'
    // @ts-ignore
    }
  // @ts-ignore
  }

  // @ts-ignore
  const getTypeIcon = (type: string): void => {
    // @ts-ignore
    switch (type) {
      // @ts-ignore
      case 'employee_update': return '👤'
      case 'project_update': return '📋'
      case 'leave_request': return '🏖️'
      case 'task_assigned': return '✅'
      case 'system_alert': return '⚠️'
      case 'success': return '✅'
      case 'warning': return '⚠️'
      case 'error': return '❌'
      default: return '📢'
    // @ts-ignore
    }
  // @ts-ignore
  }

  // @ts-ignore
  const formatTime = (dateString: string): string => {
    // @ts-ignore
    try {
      // @ts-ignore
      const date = new Date(dateString as any)
      return formatDistanceToNow(date, { 
        // @ts-ignore
        addSuffix: true,
        locale: language === 'ar' ? ar : undefined
      // @ts-ignore
      } as any)
    // @ts-ignore
    } catch {
      // @ts-ignore
      return dateString
    // @ts-ignore
    }
  // @ts-ignore
  }

  // PERFORMANCE FIX: Removed useEffect - centralized cache handles all data fetching and polling

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="relative">
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <Badge 
              variant="destructive" 
              className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs"
            >
              {unreadCount > 99 ? '99+' : unreadCount}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent 
        align={language === 'ar' ? 'start' : 'end'} 
        className="w-80 max-h-96"
        side="bottom"
      >
        <div className="flex items-center justify-between p-4">
          <h3 className="font-semibold">
            {language === 'ar' ? 'الإشعارات' : 'Notifications'}
          </h3>
          {unreadCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={markAllAsRead}
              disabled={loading}
              className="text-xs"
            >
              <CheckCheck className="h-4 w-4 mr-1" />
              {language === 'ar' ? 'قراءة الكل' : 'Mark all read'}
            </Button>
          )}
        </div>
        
        <DropdownMenuSeparator />
        
        <ScrollArea className="max-h-80">
          {(notifications as any).length === 0 ? (
            <div className="p-4 text-center text-gray-500">
              {language === 'ar' ? 'لا توجد إشعارات' : 'No notifications'}
            </div>
          ) : (
            // @ts-ignore
            (notifications as any).map((notification as any) => (
              <DropdownMenuItem
                key={(notification as any).id}
                className={`p-4 cursor-pointer hover:bg-gray-50 ${
                  !(notification as any).is_read ? 'bg-blue-50 border-l-4 border-blue-500' : ''
                }`}
                onClick={() => handleNotificationClick(notification as any)}
              >
                <div className="flex items-start space-x-3 w-full">
                  <div className="flex-shrink-0">
                    <div className="text-lg">
                      {getTypeIcon((notification as any as any).notification_type)}
                    </div>
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {language === 'ar' && (notification as any).title_ar 
                          ? (notification as any).title_ar 
                          : (notification as any).title}
                      </p>
                      <div className={`w-2 h-2 rounded-full ${getPriorityColor((notification as any as any).priority)}`} />
                    </div>
                    
                    <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                      {language === 'ar' && (notification as any).message_ar 
                        ? (notification as any).message_ar 
                        : (notification as any).message}
                    </p>
                    
                    <div className="flex items-center justify-between mt-2">
                      <p className="text-xs text-gray-400">
                        {formatTime((notification as any as any).created_at)}
                      </p>
                      {(notification as any).sender_name && (
                        <p className="text-xs text-gray-400">
                          {(notification as any).sender_name}
                        </p>
                      )}
                    </div>
                  </div>
                  
                  {!(notification as any).is_read && (
                    <div className="flex-shrink-0">
                      <div className="w-2 h-2 bg-blue-500 rounded-full" />
                    </div>
                  )}
                </div>
              </DropdownMenuItem>
            ))
          // @ts-ignore
          )}
        </ScrollArea>
        
        <DropdownMenuSeparator />
        
        <DropdownMenuItem className="p-2">
          <Button variant="ghost" className="w-full justify-center text-sm">
            <Settings className="h-4 w-4 mr-2" />
            {language === 'ar' ? 'إعدادات الإشعارات' : 'Notification Settings'}
          </Button>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
// @ts-ignore
}

export default NotificationBell
