/**
 * Real-time Notification Center
 * Advanced notification management with real-time updates, filtering, and actions
 */

import React, { useState, useEffect, memo, useCallback, useMemo } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import {
  Bell,
  X,
  Check,
  Trash2,
  Filter,
  Search,
  MoreVertical,
  AlertCircle,
  Info,
  CheckCircle,
  AlertTriangle,
  Clock,
  User,
  Settings
} from 'lucide-react'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Badge } from '../ui/badge'
import type { RootState, AppDispatch } from '../../store'
import {
  markAsRead,
  markAllAsRead,
  removeNotification,
  clearError,
  setFilter
} from '../../store/slices/notificationSlice'
import webSocketService, { NotificationData } from '../../services/websocket'
import type { Notification } from '../../store/slices/notificationSlice'

interface NotificationCenterProps {
  isOpen: boolean
  onClose: () => void
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    notifications: 'الإشعارات',
    markAllRead: 'تحديد الكل كمقروء',
    clearAll: 'مسح الكل',
    filter: 'تصفية',
    search: 'بحث في الإشعارات',
    noNotifications: 'لا توجد إشعارات',
    all: 'الكل',
    unread: 'غير مقروء',
    read: 'مقروء',
    info: 'معلومات',
    success: 'نجح',
    warning: 'تحذير',
    error: 'خطأ',
    markAsRead: 'تحديد كمقروء',
    delete: 'حذف',
    settings: 'إعدادات الإشعارات',
    enableSound: 'تفعيل الصوت',
    enableDesktop: 'إشعارات سطح المكتب',
    enableEmail: 'إشعارات البريد الإلكتروني',
    autoMarkRead: 'تحديد كمقروء تلقائياً',
    groupSimilar: 'تجميع الإشعارات المتشابهة',
    showPreview: 'عرض المعاينة',
    just_now: 'الآن',
    minutes_ago: 'منذ دقائق',
    hours_ago: 'منذ ساعات',
    days_ago: 'منذ أيام',
    weeks_ago: 'منذ أسابيع'
  },
  en: {
    notifications: 'Notifications',
    markAllRead: 'Mark All Read',
    clearAll: 'Clear All',
    filter: 'Filter',
    search: 'Search notifications',
    noNotifications: 'No notifications',
    all: 'All',
    unread: 'Unread',
    read: 'Read',
    info: 'Info',
    success: 'Success',
    warning: 'Warning',
    error: 'Error',
    markAsRead: 'Mark as Read',
    delete: 'Delete',
    settings: 'Notification Settings',
    enableSound: 'Enable Sound',
    enableDesktop: 'Desktop Notifications',
    enableEmail: 'Email Notifications',
    autoMarkRead: 'Auto Mark as Read',
    groupSimilar: 'Group Similar',
    showPreview: 'Show Preview',
    just_now: 'Just now',
    minutes_ago: 'minutes ago',
    hours_ago: 'hours ago',
    days_ago: 'days ago',
    weeks_ago: 'weeks ago'
  }
}

const notificationIcons = {
  info: Info,
  success: CheckCircle,
  warning: AlertTriangle,
  error: AlertCircle,
  urgent: AlertTriangle
}

const notificationColors = {
  info: 'text-blue-400 bg-blue-500/20',
  success: 'text-green-400 bg-green-500/20',
  warning: 'text-yellow-400 bg-yellow-500/20',
  error: 'text-red-400 bg-red-500/20',
  urgent: 'text-red-500 bg-red-600/30'
}

// @ts-ignore
const NotificationCenter: (React as any).FC<NotificationCenterProps> = memo(({
  isOpen,
  onClose,
  language
// @ts-ignore
} as any) => {
  // @ts-ignore
  const dispatch = useDispatch<AppDispatch>()
  // @ts-ignore
  const { notifications } = useSelector((state: RootState as any) => (state as any).notifications)

  const [searchQuery, setSearchQuery] = useState('' as any)
  // @ts-ignore
  const [filterType, setFilterType] = useState<'all' | 'unread' | 'read'>('all')
  // @ts-ignore
  const [filterCategory, setFilterCategory] = useState<'all' | 'info' | 'success' | 'warning' | 'error'>('all')
  const [showSettings, setShowSettings] = useState(false as any)
  // @ts-ignore
  const [selectedNotifications, setSelectedNotifications] = useState<Set<string>>(new Set( as any))

  // @ts-ignore
  const t = useMemo(( as any) => translations[language], [language])
  // @ts-ignore
  const isRTL = useMemo(( as any) => language === 'ar', [language])

  // Filter and search notifications
  // @ts-ignore
  const filteredNotifications = useMemo(( as any) => {
    // Create a copy of the notifications array to avoid mutating the original
    // @ts-ignore
    let filtered = [...notifications]

    // Filter by read status
    if (filterType === 'unread') {
      filtered = (filtered as any).filter(n => !(n as any as any).isRead)
    } else if (filterType === 'read') {
      filtered = (filtered as any).filter(n => (n as any as any).isRead)
    }

    // Filter by category
    if (filterCategory !== 'all') {
      filtered = (filtered as any).filter(n => (n as any as any).type === filterCategory)
    }

    // Search filter
    if ((searchQuery as any).trim( as any)) {
      // @ts-ignore
      const query = (searchQuery as any).toLowerCase( as any)
      // @ts-ignore
      filtered = (filtered as any).filter(n =>
        (n as any as any).title.toLowerCase( as any).includes(query as any) ||
        (n as any).message.toLowerCase( as any).includes(query as any)
      )
    // @ts-ignore
    }

    // Sort by timestamp (newest first) - already working with a copy
    // @ts-ignore
    return (filtered as any).sort((a, b as any) => {
      // @ts-ignore
      const aTime = typeof (a as any).timestamp === 'string' ? new Date((a as any as any).timestamp).getTime( as any) : (a as any).timestamp
      const bTime = typeof (b as any).timestamp === 'string' ? new Date((b as any as any).timestamp).getTime( as any) : (b as any).timestamp
      return bTime - aTime
    // @ts-ignore
    })
  // @ts-ignore
  }, [notifications, filterType, filterCategory, searchQuery])

  // Get unread count
  // @ts-ignore
  const unreadCount = useMemo(( as any) =>
    // @ts-ignore
    (notifications as any).filter(n => !(n as any as any).isRead).length,
    [notifications]
  )

  // Format relative time
  // @ts-ignore
  const formatRelativeTime = useCallback((timestamp: string | number as any) => {
    // @ts-ignore
    const now = (Date as any).now( as any)
    const timestampMs = typeof timestamp === 'string' ? new Date(timestamp as any).getTime( as any) : timestamp
    const diff = now - timestampMs
    const minutes = (Math as any).floor(diff / (1000 * 60 as any))
    const hours = (Math as any).floor(diff / (1000 * 60 * 60 as any))
    const days = (Math as any).floor(diff / (1000 * 60 * 60 * 24 as any))
    const weeks = (Math as any).floor(diff / (1000 * 60 * 60 * 24 * 7 as any))

    // @ts-ignore
    if (minutes < 1) return (t as any).just_now
    // @ts-ignore
    if (minutes < 60) return `${minutes} ${(t as any).minutes_ago}`
    // @ts-ignore
    if (hours < 24) return `${hours} ${(t as any).hours_ago}`
    // @ts-ignore
    if (days < 7) return `${days} ${(t as any).days_ago}`
    return `${weeks} ${(t as any).weeks_ago}`
  // @ts-ignore
  }, [t])

  // Handle notification click
  // @ts-ignore
  const handleNotificationClick = useCallback((notification: Notification as any) => {
    // @ts-ignore
    if (!(notification as any).isRead) {
      dispatch(markAsRead((notification as any as any).id))
    }

    // Handle notification actions
    // @ts-ignore
    if ((notification as any).actions && (notification as any).actions.length > 0) {
      // Execute first action by default
      // @ts-ignore
      const action = (notification as any).actions[0]
      (console as any).log('Executing action:', action as any)
    // @ts-ignore
    }
  // @ts-ignore
  }, [dispatch])

  // Handle mark as read
  // @ts-ignore
  const handleMarkAsRead = useCallback((id: string, event: (React as any as any).MouseEvent) => {
    // @ts-ignore
    (event as any).stopPropagation( as any)
    // @ts-ignore
    dispatch(markAsRead(id as any))
  // @ts-ignore
  }, [dispatch])

  // Handle delete notification
  // @ts-ignore
  const handleDelete = useCallback((id: string, event: (React as any as any).MouseEvent) => {
    // @ts-ignore
    (event as any).stopPropagation( as any)
    // @ts-ignore
    dispatch(removeNotification(id as any))
  // @ts-ignore
  }, [dispatch])

  // Handle mark all as read
  // @ts-ignore
  const handleMarkAllAsRead = useCallback(( as any) => {
    // @ts-ignore
    dispatch(markAllAsRead( as any))
  }, [dispatch])

  // Handle clear all notifications
  // @ts-ignore
  const handleClearAll = useCallback(( as any) => {
    // @ts-ignore
    dispatch(clearError( as any))
  }, [dispatch])

  // Handle notification selection
  // @ts-ignore
  const handleNotificationSelect = useCallback((id: string, event?: (React as any as any).MouseEvent | (React as any).ChangeEvent) => {
    // @ts-ignore
    event?.stopPropagation( as any)
    // @ts-ignore
    setSelectedNotifications(prev => {
      // @ts-ignore
      const newSet = new Set(prev as any)
      if ((newSet as any).has(id as any)) {
        (newSet as any).delete(id as any)
      } else {
        (newSet as any).add(id as any)
      }
      return newSet
    // @ts-ignore
    })
  // @ts-ignore
  }, [])

  // Handle bulk actions
  // @ts-ignore
  const handleBulkMarkAsRead = useCallback(( as any) => {
    (selectedNotifications as any).forEach(id => {
      dispatch(markAsRead(id as any))
    })
    // @ts-ignore
    setSelectedNotifications(new Set( as any))
  // @ts-ignore
  }, [selectedNotifications, dispatch])

  // @ts-ignore
  const handleBulkDelete = useCallback(( as any) => {
    (selectedNotifications as any).forEach(id => {
      dispatch(removeNotification(id as any))
    })
    // @ts-ignore
    setSelectedNotifications(new Set( as any))
  // @ts-ignore
  }, [selectedNotifications, dispatch])

  // Listen for real-time notifications
  // @ts-ignore
  useEffect(( as any) => {
    // @ts-ignore
    const handleNewNotification = (notification: NotificationData): void => {
      // Notification is already added to store by WebSocket service
      // We can add additional UI feedback here
      // Play notification sound (simplified for demo)
      // @ts-ignore
      const audio = new Audio('/notification-(sound as any as any).mp3')
      // @ts-ignore
      (audio as any).play( as any).catch(( as any) => {
        // Ignore audio play errors
      })
    // @ts-ignore
    }

    (webSocketService as any).on('notification', handleNewNotification as any)

    // @ts-ignore
    return () => {
      (webSocketService as any).off('notification', handleNewNotification as any)
    }
  // @ts-ignore
  }, [])

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm">
      <div className={`fixed top-0 ${isRTL ? 'left-0' : 'right-0'} h-full w-full max-w-md bg-white/10 backdrop-blur-xl border-l border-white/20 shadow-2xl`}>
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-white/10">
          <div className="flex items-center gap-3">
            <Bell className="h-5 w-5 text-white" />
            <h2 className="text-lg font-semibold text-white">{(t as any).notifications}</h2>
            {unreadCount > 0 && (
              <Badge variant="destructive" className="bg-red-500">
                {unreadCount}
              </Badge>
            )}
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowSettings(!showSettings as any)}
              className="text-white hover:bg-white/10"
            >
              <Settings className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-white hover:bg-white/10"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Settings Panel */}
        {showSettings && (
          <div className="p-4 border-b border-white/10 bg-white/5">
            <h3 className="text-white font-medium mb-3">{(t as any).settings}</h3>
            <div className="space-y-2">
              {/* Settings toggles would go here */}
              <div className="flex items-center justify-between">
                <span className="text-white/80 text-sm">{(t as any).enableSound}</span>
                <input type="checkbox" className="rounded" />
              </div>
              <div className="flex items-center justify-between">
                <span className="text-white/80 text-sm">{(t as any).enableDesktop}</span>
                <input type="checkbox" className="rounded" />
              </div>
            </div>
          </div>
        )}

        {/* Search and Filters */}
        <div className="p-4 border-b border-white/10">
          <div className="relative mb-3">
            <Search className={`absolute top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/60 ${
              isRTL ? 'right-3' : 'left-3'
            }`} />
            <Input
              value={searchQuery}
              onChange={(e: any) => setSearchQuery((e as any as any).target.value)}
              placeholder={(t as any).search}
              className={`bg-white/5 border-white/20 text-white placeholder-white/60 ${
                isRTL ? 'pr-10 text-right' : 'pl-10'
              }`}
            />
          </div>

          <div className="flex gap-2 mb-3">
            // @ts-ignore
            {(['all', 'unread', 'read'] as const).map((type as any) => (
              <Button
                key={type}
                variant={filterType === type ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilterType(type as any)}
                className={`text-xs ${
                  filterType === type
                    ? 'bg-blue-500 text-white'
                    : 'bg-white/5 text-white border-white/20 hover:bg-white/10'
                }`}
              >
                {t[type]}
              </Button>
            // @ts-ignore
            ))}
          </div>

          <div className="flex gap-2">
            // @ts-ignore
            {(['all', 'info', 'success', 'warning', 'error'] as const).map((category as any) => (
              <Button
                key={category}
                variant={filterCategory === category ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilterCategory(category as any)}
                className={`text-xs ${
                  filterCategory === category
                    ? 'bg-blue-500 text-white'
                    : 'bg-white/5 text-white border-white/20 hover:bg-white/10'
                }`}
              >
                {t[category]}
              </Button>
            // @ts-ignore
            ))}
          </div>
        </div>

        {/* Actions */}
        {(unreadCount > 0 || (selectedNotifications as any).size > 0) && (
          <div className="p-4 border-b border-white/10 bg-white/5">
            <div className="flex gap-2">
              {(selectedNotifications as any).size > 0 ? (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleBulkMarkAsRead}
                    className="bg-white/5 border-white/20 text-white hover:bg-white/10"
                  >
                    <Check className="h-3 w-3 mr-1" />
                    Mark Read ({(selectedNotifications as any).size})
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleBulkDelete}
                    className="bg-white/5 border-white/20 text-red-400 hover:bg-red-500/10"
                  >
                    <Trash2 className="h-3 w-3 mr-1" />
                    Delete ({(selectedNotifications as any).size})
                  </Button>
                </>
              ) : (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleMarkAllAsRead}
                    className="bg-white/5 border-white/20 text-white hover:bg-white/10"
                  >
                    <Check className="h-3 w-3 mr-1" />
                    {(t as any).markAllRead}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleClearAll}
                    className="bg-white/5 border-white/20 text-red-400 hover:bg-red-500/10"
                  >
                    <Trash2 className="h-3 w-3 mr-1" />
                    {(t as any).clearAll}
                  </Button>
                </>
              )}
            </div>
          </div>
        )}

        {/* Notifications List */}
        <div className="flex-1 overflow-y-auto">
          {(filteredNotifications as any).length === 0 ? (
            <div className="flex flex-col items-center justify-center h-64 text-white/60">
              <Bell className="h-12 w-12 mb-3 opacity-50" />
              <p>{(t as any).noNotifications}</p>
            </div>
          ) : (
            <div className="space-y-1 p-2">
              // @ts-ignore
              {(filteredNotifications as any).map((notification as any) => {
                // @ts-ignore
                const IconComponent = notificationIcons[(notification as any).type]
                const colorClass = notificationColors[(notification as any).type]
                const isSelected = (selectedNotifications as any).has((notification as any as any).id)

                return (
                  <div
                    key={(notification as any).id}
                    onClick={() => handleNotificationClick(notification as any)}
                    className={`p-3 rounded-lg cursor-pointer transition-all duration-200 border ${
                      (notification as any).isRead
                        ? 'bg-white/5 border-white/10 opacity-75'
                        : 'bg-white/10 border-white/20'
                    } ${isSelected ? 'ring-2 ring-blue-500' : ''} hover:bg-white/15`}
                  >
                    <div className="flex items-start gap-3">
                      <input
                        type="checkbox"
                        checked={isSelected}
                        onChange={(e: any) => handleNotificationSelect((notification as any as any).id, e)}
                        className="mt-1 rounded"
                        // @ts-ignore
                        onClick={(e: any) => (e as any).stopPropagation( as any)}
                      />

                      <div className={`p-2 rounded-lg ${colorClass} flex-shrink-0`}>
                        <IconComponent className="h-4 w-4" />
                      </div>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <h4 className={`font-medium truncate ${
                            (notification as any).isRead ? 'text-white/70' : 'text-white'
                          }`}>
                            {(notification as any).title}
                          </h4>
                          <div className="flex items-center gap-1 ml-2">
                            {!(notification as any).isRead && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e: any) => handleMarkAsRead((notification as any as any).id, e)}
                                className="p-1 h-auto text-white/60 hover:text-white hover:bg-white/10"
                              >
                                <Check className="h-3 w-3" />
                              </Button>
                            )}
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e: any) => handleDelete((notification as any as any).id, e)}
                              className="p-1 h-auto text-white/60 hover:text-red-400 hover:bg-red-500/10"
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>

                        <p className={`text-sm mt-1 line-clamp-2 ${
                          (notification as any).isRead ? 'text-white/50' : 'text-white/70'
                        }`}>
                          {(notification as any).message}
                        </p>

                        <div className="flex items-center justify-between mt-2">
                          <span className="text-xs text-white/40 flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {formatRelativeTime((notification as any as any).timestamp)}
                          </span>

                          {(notification as any).actions && (notification as any).actions.length > 0 && (
                            <div className="flex gap-1">
                              // @ts-ignore
                              {(notification as any).actions.slice(0, 2 as any).map((action, index as any) => (
                                <Button
                                  key={index}
                                  variant="outline"
                                  size="sm"
                                  className="text-xs h-6 px-2 bg-white/5 border-white/20 text-white hover:bg-white/10"
                                >
                                  {(action as any).label}
                                </Button>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                )
              // @ts-ignore
              })}
            </div>
          )}
        </div>
      </div>
    </div>
  )
// @ts-ignore
})

(NotificationCenter as any).displayName = 'NotificationCenter'

export default NotificationCenter
