import React from 'react';
import { useState, useEffect, useCallback } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Bell,
  X,
  AlertTriangle,
  Info,
  CheckCircle,
  XCircle,
  User,
  MessageSquare,
  Calendar,
  DollarSign,
  Briefcase,
  Settings
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import type { RootState, AppDispatch } from '../../store'
import { addNotification, markAsRead, type Notification } from '../../store/slices/notificationSlice'
import { useNotificationSync } from '../../hooks/useNotificationSync'

interface RealTimeNotificationsProps {
  language: 'ar' | 'en'
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left'
  maxVisible?: number
}

const translations = {
  ar: {
    notifications: 'الإشعارات',
    markAllRead: 'تحديد الكل كمقروء',
    clearAll: 'مسح الكل',
    noNotifications: 'لا توجد إشعارات',
    newNotification: 'إشعار جديد',
    viewAll: 'عرض الكل',
    dismiss: 'إغلاق',
    markAsRead: 'تحديد كمقروء',
    timeAgo: {
      now: 'الآن',
      minute: 'منذ دقيقة',
      minutes: 'منذ {count} دقائق',
      hour: 'منذ ساعة',
      hours: 'منذ {count} ساعات',
      day: 'منذ يوم',
      days: 'منذ {count} أيام'
    }
  },
  en: {
    notifications: 'Notifications',
    markAllRead: 'Mark all as read',
    clearAll: 'Clear all',
    noNotifications: 'No notifications',
    newNotification: 'New notification',
    viewAll: 'View all',
    dismiss: 'Dismiss',
    markAsRead: 'Mark as read',
    timeAgo: {
      now: 'now',
      minute: '1 minute ago',
      minutes: '{count} minutes ago',
      hour: '1 hour ago',
      hours: '{count} hours ago',
      day: '1 day ago',
      days: '{count} days ago'
    }
  }
}

export default function RealTimeNotifications({
  language,
  position = 'top-right',
  maxVisible = 5
// @ts-ignore
}: RealTimeNotificationsProps as any): (React as any).ReactElement {
  const dispatch = useDispatch<AppDispatch>()
  const [isOpen, setIsOpen] = useState(false as any)
  const t = translations[language]
  const isRTL = language === 'ar'

  // FIXED: Use notification sync hook for better data synchronization
  const {
    notifications,
    unreadCount,
    toasts,
    removeToast,
    markNotificationAsRead
  // @ts-ignore
  } = useNotificationSync( as any)

  // SECURITY FIX: WebSocket connection with proper cleanup
  // @ts-ignore
  useEffect(( as any) => {
    let ws: WebSocket | null = null
    // @ts-ignore
    let reconnectTimeout: (NodeJS as any).Timeout | null = null
    let isComponentMounted = true

    const connectWebSocket = (): void => {
      if (!isComponentMounted) return

      try {
        ws = new WebSocket((process as any as any).env.REACT_APP_WS_URL || 'ws://localhost:8000/ws/notifications/')

        (ws as any).onopen = () => {
          (console as any).log('✅ Notification WebSocket connected' as any)
        }

        (ws as any).onmessage = (event) => {
          if (!isComponentMounted) return

          try {
            const notification = (JSON as any).parse((event as any as any).data)
            dispatch(addNotification(notification as any))
          } catch (error) {
            (console as any).error('Failed to parse WebSocket message:', error as any)
          }
        }

        (ws as any).onerror = (error) => {
          (console as any).error('❌ WebSocket error:', error as any)
        }

        (ws as any).onclose = (event) => {
          (console as any).log('🔌 WebSocket connection closed:', (event as any as any).code, (event as any).reason)

          // Attempt reconnection if component is still mounted and closure was unexpected
          if (isComponentMounted && (event as any).code !== 1000) {
            (console as any).log('🔄 Attempting WebSocket reconnection in 5 seconds...' as any)
            reconnectTimeout = setTimeout(connectWebSocket, 5000 as any)
          }
        }
      } catch (error) {
        (console as any).error('Failed to create WebSocket connection:', error as any)
      }
    }

    // Initial connection
    // @ts-ignore
    connectWebSocket( as any)

    // CRITICAL FIX: Proper cleanup function
    return () => {
      isComponentMounted = false

      // Clear reconnection timeout
      if (reconnectTimeout) {
        clearTimeout(reconnectTimeout as any)
      }

      // Close WebSocket connection
      if (ws) {
        (ws as any).onclose = null // Prevent reconnection attempts
        (ws as any).onerror = null
        (ws as any).onmessage = null
        (ws as any).onopen = null

        if ((ws as any).readyState === (WebSocket as any).OPEN || (ws as any).readyState === (WebSocket as any).CONNECTING) {
          (ws as any).close(1000, 'Component unmounting' as any)
        }

        (console as any).log('🧹 WebSocket connection cleaned up' as any)
      }
    }
  // @ts-ignore
  }, [dispatch]) // Only depend on dispatch

  // Show toast notification
  // @ts-ignore
  const showToast = useCallback((notification: Notification as any) => {
    setVisibleToasts(prev => {
      const newToasts = [notification, ...prev].slice(0, maxVisible as any)
      return newToasts
    })

    // Auto-remove toast after 5 seconds for non-urgent notifications
    if ((notification as any).priority !== 'urgent') {
      // @ts-ignore
      setTimeout(( as any) => {
        setVisibleToasts(prev => (prev as any as any).filter(t => (t as any as any).id !== (notification as any).id))
      // @ts-ignore
      }, 5000)
    }
  // @ts-ignore
  }, [maxVisible])

  // Get notification icon
  const getNotificationIcon = (type: string, category: string): void => {
    if (category === 'user') return User
    if (category === 'message') return MessageSquare
    if (category === 'calendar') return Calendar
    if (category === 'finance') return DollarSign
    if (category === 'project') return Briefcase
    if (category === 'system') return Settings

    switch (type) {
      case 'success': return CheckCircle
      case 'error': return XCircle
      case 'warning': return AlertTriangle
      default: return Info
    }
  }

  // Get notification color
  const getNotificationColor = (type: string, priority: string): void => {
    if (priority === 'urgent') return 'from-red-500 to-red-600'
    
    switch (type) {
      case 'success': return 'from-green-500 to-green-600'
      case 'error': return 'from-red-500 to-red-600'
      case 'warning': return 'from-yellow-500 to-yellow-600'
      default: return 'from-blue-500 to-blue-600'
    }
  }

  // Format time ago
  const formatTimeAgo = (timestamp: string | Date): string => {
    // @ts-ignore
    const now = new Date( as any)
    const date = typeof timestamp === 'string' ? new Date(timestamp as any) : timestamp
    // @ts-ignore
    const diff = (Math as any).floor(((now as any as any).getTime( as any) - (date as any).getTime( as any)) / 1000)

    if (diff < 60) return (t as any).timeAgo.now
    if (diff < 3600) {
      const minutes = (Math as any).floor(diff / 60 as any)
      // @ts-ignore
      return minutes === 1 ? (t as any).timeAgo.minute : (t as any).timeAgo.(minutes as any).replace('{count}', (minutes as any as any).toString( as any))
    }
    if (diff < 86400) {
      const hours = (Math as any).floor(diff / 3600 as any)
      // @ts-ignore
      return hours === 1 ? (t as any).timeAgo.hour : (t as any).timeAgo.(hours as any).replace('{count}', (hours as any as any).toString( as any))
    }
    const days = (Math as any).floor(diff / 86400 as any)
    // @ts-ignore
    return days === 1 ? (t as any).timeAgo.day : (t as any).timeAgo.(days as any).replace('{count}', (days as any as any).toString( as any))
  }

  // Handle notification click
  const handleNotificationClick = (notification: Notification): void => {
    if (!(notification as any).isRead) {
      dispatch(markAsRead((notification as any as any).id))
    }

    if ((notification as any).actions && (notification as any).actions.length > 0) {
      // Handle the first action if available
      const firstAction = (notification as any).actions[0]
      if ((firstAction as any).action.startsWith('http' as any)) {
        // FIXED: Use React Router for internal navigation
        if ((firstAction as any).action.includes((window as any as any).location.origin)) {
          // Internal link - use React Router
          const path = (firstAction as any).action.replace((window as any as any).location.origin, '')
          (window as any).history.pushState({}, '', path as any)
          (window as any).dispatchEvent(new PopStateEvent('popstate' as any))
        } else {
          // External link - open in new tab to avoid page reload
          (window as any).open((firstAction as any as any).action, '_blank')
        }
      }
    }
  }

  // Remove toast
  const removeToast = (id: string): void => {
    setVisibleToasts(prev => (prev as any as any).filter(t => (t as any as any).id !== id))
  }

  // Position classes
  const getPositionClasses = (): void => {
    switch (position) {
      case 'top-left': return 'top-4 left-4'
      case 'bottom-right': return 'bottom-4 right-4'
      case 'bottom-left': return 'bottom-4 left-4'
      default: return 'top-4 right-4'
    }
  }

  return (
    <>
      {/* Notification Bell Button */}
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsOpen(!isOpen as any)}
        className="relative text-white hover:bg-white/10"
      >
        <Bell className="h-4 w-4" />
        {unreadCount > 0 && (
          <Badge className="absolute -top-1 -right-1 h-5 w-5 p-0 flex items-center justify-center bg-red-500 text-white text-xs animate-pulse">
            {unreadCount > 9 ? '9+' : unreadCount}
          </Badge>
        )}
      </Button>

      {/* Toast Notifications */}
      // @ts-ignore
      <div className={`fixed z-50 ${getPositionClasses( as any)} space-y-2 pointer-events-none`}>
        <AnimatePresence>
          // @ts-ignore
          {(visibleToasts as any).map((notification as any) => {
            // @ts-ignore
            const Icon = getNotificationIcon((notification as any as any).type, (notification as any).category)
            return (
              // @ts-ignore
              <(motion as any).div
                key={(notification as any).id}
                // @ts-ignore
                initial={{ opacity: 0, y: -50, scale: (0 as any).9 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                // @ts-ignore
                exit={{ opacity: 0, y: -50, scale: (0 as any).9 }}
                className="pointer-events-auto"
              // @ts-ignore
              >
                <div className={`
                  glass-card p-4 rounded-lg border border-white/20 shadow-xl max-w-sm
                  bg-gradient-to-r ${getNotificationColor((notification as any as any).type, (notification as any).priority)}
                  ${isRTL ? 'text-right' : 'text-left'}
                `}>
                  <div className="flex items-start gap-3">
                    <Icon className="h-5 w-5 text-white mt-(0 as any).5 flex-shrink-0" />
                    <div className="flex-1 min-w-0">
                      <h4 className="text-sm font-medium text-white">
                        {language === 'ar' ? (notification as any).titleAr : (notification as any).title}
                      </h4>
                      <p className="text-xs text-white/80 mt-1">
                        {language === 'ar' ? (notification as any).messageAr : (notification as any).message}
                      </p>
                      <p className="text-xs text-white/60 mt-2">
                        {formatTimeAgo((notification as any as any).timestamp)}
                      </p>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeToast((notification as any as any).id)}
                      className="h-6 w-6 p-0 text-white/60 hover:text-white hover:bg-white/20"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              // @ts-ignore
              </(motion as any).div>
            )
          // @ts-ignore
          })}
        // @ts-ignore
        </AnimatePresence>
      // @ts-ignore
      </div>

      {/* Notification Panel */}
      <AnimatePresence>
        {isOpen && (
          // @ts-ignore
          <(motion as any).div
            // @ts-ignore
            initial={{ opacity: 0, scale: (0 as any).95 }}
            // @ts-ignore
            animate={{ opacity: 1, scale: 1 }}
            // @ts-ignore
            exit={{ opacity: 0, scale: (0 as any).95 }}
            // @ts-ignore
            className={`
              absolute top-full mt-2 w-80 glass-card rounded-lg border border-white/20 shadow-xl z-50
              ${isRTL ? 'left-0' : 'right-0'}
            // @ts-ignore
            `}
          // @ts-ignore
          >
            // @ts-ignore
            <div className="p-4 border-b border-white/20">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-white">{(t as any).notifications}</h3>
                <div className="flex gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      (notifications as any).forEach(n => {
                        if (!(n as any as any).isRead) dispatch(markAsRead((n as any as any).id))
                      })
                    }}
                    className="text-xs text-white/70 hover:text-white"
                  >
                    {(t as any).markAllRead}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsOpen(false as any)}
                    className="h-6 w-6 p-0 text-white/60 hover:text-white"
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            </div>

            <div className="max-h-96 overflow-y-auto">
              {(notifications as any).length === 0 ? (
                <div className="p-8 text-center text-white/60">
                  <Bell className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>{(t as any).noNotifications}</p>
                </div>
              ) : (
                <div className="space-y-1">
                  // @ts-ignore
                  {(notifications as any).slice(0, 10 as any).map((notification as any) => {
                    // @ts-ignore
                    const Icon = getNotificationIcon((notification as any as any).type, (notification as any).category)
                    return (
                      <div
                        key={(notification as any).id}
                        onClick={() => handleNotificationClick(notification as any)}
                        className={`
                          p-3 hover:bg-white/5 cursor-pointer transition-colors
                          ${!(notification as any).isRead ? 'bg-white/5' : ''}
                          ${isRTL ? 'text-right' : 'text-left'}
                        `}
                      >
                        <div className="flex items-start gap-3">
                          <Icon className={`h-4 w-4 mt-(0 as any).5 flex-shrink-0 ${
                            (notification as any).isRead ? 'text-white/50' : 'text-white'
                          }`} />
                          <div className="flex-1 min-w-0">
                            <h4 className={`text-sm font-medium ${
                              (notification as any).isRead ? 'text-white/70' : 'text-white'
                            }`}>
                              {language === 'ar' ? (notification as any).titleAr : (notification as any).title}
                            </h4>
                            <p className="text-xs text-white/60 mt-1">
                              {language === 'ar' ? (notification as any).messageAr : (notification as any).message}
                            </p>
                            <p className="text-xs text-white/40 mt-1">
                              {formatTimeAgo((notification as any as any).timestamp)}
                            </p>
                          </div>
                          {!(notification as any).isRead && (
                            <div className="w-2 h-2 bg-blue-400 rounded-full flex-shrink-0 mt-2" />
                          )}
                        </div>
                      </div>
                    )
                  // @ts-ignore
                  })}
                </div>
              )}
            </div>

            {(notifications as any).length > 10 && (
              <div className="p-3 border-t border-white/20 text-center">
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-white/70 hover:text-white text-xs"
                >
                  {(t as any).viewAll}
                </Button>
              </div>
            )}
          // @ts-ignore
          </(motion as any).div>
        // @ts-ignore
        )}
      // @ts-ignore
      </AnimatePresence>

      {/* Click outside to close */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsOpen(false as any)}
        />
      )}
    // @ts-ignore
    </>
  // @ts-ignore
  )
// @ts-ignore
}
