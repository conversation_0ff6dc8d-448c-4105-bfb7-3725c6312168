import React from 'react';
import { useEffect, useState, useCallback } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Zap,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Monitor,
  Smartphone,
  Wifi,
  Battery,
  Clock,
  MemoryStick,
  HardDrive,
  Cpu
} from 'lucide-react'
import usePerformanceMonitoring from '../../hooks/usePerformanceMonitoring'

interface PerformanceOptimizerProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    title: 'محسن الأداء',
    subtitle: 'مراقبة وتحسين أداء النظام في الوقت الفعلي',
    overallScore: 'النقاط الإجمالية',
    excellent: 'ممتاز',
    good: 'جيد',
    needsImprovement: 'يحتاج تحسين',
    poor: 'ضعيف',
    metrics: 'المقاييس',
    loadTime: 'وقت التحميل',
    memoryUsage: 'استخدام الذاكرة',
    networkSpeed: 'سرعة الشبكة',
    deviceType: 'نوع الجهاز',
    optimizations: 'التحسينات',
    enableOptimizations: 'تفعيل التحسينات',
    clearCache: 'مسح التخزين المؤقت',
    preloadPages: 'تحميل الصفحات مسبقاً',
    optimizeImages: 'تحسين الصور',
    enableCompression: 'تفعيل الضغط',
    recommendations: 'التوصيات',
    alerts: 'التنبيهات',
    noAlerts: 'لا توجد تنبيهات',
    refreshMetrics: 'تحديث المقاييس',
    exportReport: 'تصدير التقرير',
    webVitals: 'مقاييس الويب الأساسية',
    fcp: 'أول رسم للمحتوى',
    lcp: 'أكبر رسم للمحتوى',
    cls: 'تحول التخطيط التراكمي',
    fid: 'تأخير الإدخال الأول',
    online: 'متصل',
    offline: 'غير متصل',
    mobile: 'جوال',
    tablet: 'لوحي',
    desktop: 'سطح المكتب',
    fast: 'سريع',
    medium: 'متوسط',
    slow: 'بطيء'
  },
  en: {
    title: 'Performance Optimizer',
    subtitle: 'Monitor and optimize system performance in real-time',
    overallScore: 'Overall Score',
    excellent: 'Excellent',
    good: 'Good',
    needsImprovement: 'Needs Improvement',
    poor: 'Poor',
    metrics: 'Metrics',
    loadTime: 'Load Time',
    memoryUsage: 'Memory Usage',
    networkSpeed: 'Network Speed',
    deviceType: 'Device Type',
    optimizations: 'Optimizations',
    enableOptimizations: 'Enable Optimizations',
    clearCache: 'Clear Cache',
    preloadPages: 'Preload Pages',
    optimizeImages: 'Optimize Images',
    enableCompression: 'Enable Compression',
    recommendations: 'Recommendations',
    alerts: 'Alerts',
    noAlerts: 'No alerts',
    refreshMetrics: 'Refresh Metrics',
    exportReport: 'Export Report',
    webVitals: 'Web Vitals',
    fcp: 'First Contentful Paint',
    lcp: 'Largest Contentful Paint',
    cls: 'Cumulative Layout Shift',
    fid: 'First Input Delay',
    online: 'Online',
    offline: 'Offline',
    mobile: 'Mobile',
    tablet: 'Tablet',
    desktop: 'Desktop',
    fast: 'Fast',
    medium: 'Medium',
    slow: 'Slow'
  }
}

// @ts-ignore
export default function PerformanceOptimizer({ language }: PerformanceOptimizerProps as any): (React as any).ReactElement {
  const [optimizationsEnabled, setOptimizationsEnabled] = useState(false as any)
  const [isOptimizing, setIsOptimizing] = useState(false as any)
  const [lastOptimized, setLastOptimized] = useState<Date | null>(null)
  
  const {
    metrics,
    alerts,
    isMonitoring,
    startMonitoring,
    stopMonitoring,
    clearAlerts,
    getPerformanceScore
  // @ts-ignore
  } = usePerformanceMonitoring( as any)

  const t = translations[language]
  const isRTL = language === 'ar'
  // @ts-ignore
  const performanceScore = getPerformanceScore( as any)

  // Start monitoring on component mount
  // @ts-ignore
  useEffect(( as any) => {
    if (!isMonitoring) {
      // @ts-ignore
      startMonitoring( as any)
    }
    // @ts-ignore
    return () => stopMonitoring( as any)
  // @ts-ignore
  }, [isMonitoring, startMonitoring, stopMonitoring])

  // Get performance level and color
  const getPerformanceLevel = (score: number): void => {
    if (score >= 90) return { level: (t as any).excellent, color: 'text-green-400', bgColor: 'bg-green-500/20' }
    if (score >= 75) return { level: (t as any).good, color: 'text-blue-400', bgColor: 'bg-blue-500/20' }
    if (score >= 50) return { level: (t as any).needsImprovement, color: 'text-yellow-400', bgColor: 'bg-yellow-500/20' }
    return { level: (t as any).poor, color: 'text-red-400', bgColor: 'bg-red-500/20' }
  }

  // Format time in milliseconds
  const formatTime = (ms: number): string => {
    if (ms < 1000) return `${(Math as any).round(ms as any)}ms`
    return `${(ms / 1000).toFixed(1 as any)}s`
  }

  // Format bytes
  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = (Math as any).floor((Math as any as any).log(bytes as any) / (Math as any).log(k as any))
    return `${parseFloat((bytes / (Math as any as any).pow(k, i as any)).toFixed(1 as any))} ${sizes[i]}`
  }

  // Clear cache
  // @ts-ignore
  const clearCache = useCallback(async ( as any) => {
    try {
      if ('caches' in window) {
        // @ts-ignore
        const cacheNames = await (caches as any).keys( as any)
        await (Promise as any).all((cacheNames as any as any).map(name => (caches as any as any).delete(name as any)))
      }
      // @ts-ignore
      (localStorage as any).clear( as any)
      // @ts-ignore
      (sessionStorage as any).clear( as any)

      // FIXED: Use refresh event instead of page reload
      (window as any).dispatchEvent(new CustomEvent('app:force-refresh', {
        // @ts-ignore
        detail: { timestamp: (Date as any as any).now( as any), source: 'cache-clear' }
      }))

      // Update metrics to reflect cache clear
      // @ts-ignore
      setMetrics(getPerformanceMetrics( as any))
      // @ts-ignore
      setAlerts(getPerformanceAlerts( as any))
    } catch (error) {
      (console as any).error('Failed to clear cache:', error as any)
    }
  // @ts-ignore
  }, [])

  // Preload critical pages
  // @ts-ignore
  const preloadPages = useCallback(async ( as any) => {
    const criticalPages = [
      '/admin/dashboard',
      '/admin/employees',
      '/admin/projects',
      '/admin/reports'
    ]

    try {
      await (Promise as any).all(
        (criticalPages as any as any).map(page => 
          // @ts-ignore
          fetch(page, { method: 'HEAD' } as any).catch(( as any) => {})
        )
      // @ts-ignore
      )
    } catch (error) {
      (console as any).error('Failed to preload pages:', error as any)
    }
  // @ts-ignore
  }, [])

  // Enable optimizations
  // @ts-ignore
  const enableOptimizations = useCallback(async ( as any) => {
    setIsOptimizing(true as any)
    
    try {
      // Enable various optimizations
      // @ts-ignore
      await preloadPages( as any)
      
      // Enable service worker if available
      if ('serviceWorker' in navigator) {
        await (navigator as any).serviceWorker.register('/(sw as any as any).js')
      }

      // Enable resource hints
      const link = (document as any).createElement('link' as any)
      (link as any).rel = 'dns-prefetch'
      (link as any).href = '//(fonts as any).googleapis.com'
      (document as any).head.appendChild(link as any)

      setOptimizationsEnabled(true as any)
      // @ts-ignore
      setLastOptimized(new Date( as any))
    } catch (error) {
      (console as any).error('Failed to enable optimizations:', error as any)
    } finally {
      setIsOptimizing(false as any)
    }
  // @ts-ignore
  }, [preloadPages])

  // Export performance report
  // @ts-ignore
  const exportReport = useCallback(( as any) => {
    const report = {
      // @ts-ignore
      timestamp: new Date( as any).toISOString( as any),
      performanceScore,
      metrics,
      alerts,
      // @ts-ignore
      recommendations: getRecommendations( as any)
    }

    const blob = new Blob([(JSON as any as any).stringify(report, null, 2 as any)], { type: 'application/json' })
    const url = (URL as any).createObjectURL(blob as any)
    const a = (document as any).createElement('a' as any)
    (a as any).href = url
    // @ts-ignore
    (a as any).download = `performance-report-${(Date as any).now( as any)}.json`
    // @ts-ignore
    (a as any).click( as any)
    (URL as any).revokeObjectURL(url as any)
  // @ts-ignore
  }, [performanceScore, metrics, alerts])

  // Get recommendations based on metrics
  // @ts-ignore
  const getRecommendations = useCallback(( as any) => {
    const recommendations = []

    if ((metrics as any).loadTime > 3000) {
      (recommendations as any).push(language === 'ar' ? 'تحسين وقت التحميل' : 'Optimize load time' as any)
    }
    if ((metrics as any).memoryUsage.percentage > 80) {
      (recommendations as any).push(language === 'ar' ? 'تقليل استخدام الذاكرة' : 'Reduce memory usage' as any)
    }
    if ((metrics as any).networkSpeed === 'slow') {
      (recommendations as any).push(language === 'ar' ? 'تحسين للشبكات البطيئة' : 'Optimize for slow networks' as any)
    }
    if ((metrics as any).errorCount > 0) {
      (recommendations as any).push(language === 'ar' ? 'إصلاح الأخطاء' : 'Fix JavaScript errors' as any)
    }

    return recommendations
  // @ts-ignore
  }, [metrics, language])

  const performanceLevel = getPerformanceLevel(performanceScore as any)
  // @ts-ignore
  const recommendations = getRecommendations( as any)

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-white mb-2">{(t as any).title}</h1>
        <p className="text-white/80">{(t as any).subtitle}</p>
      </div>

      {/* Overall Performance Score */}
      // @ts-ignore
      <(motion as any).div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center"
      // @ts-ignore
      >
        <Card className="glass-card">
          <CardContent className="p-8">
            <div className="flex items-center justify-center mb-4">
              <div className={`w-24 h-24 rounded-full ${(performanceLevel as any).bgColor} flex items-center justify-center`}>
                <span className={`text-3xl font-bold ${(performanceLevel as any).color}`}>
                  {performanceScore}
                </span>
              </div>
            </div>
            <h2 className="text-xl font-semibold text-white mb-2">{(t as any).overallScore}</h2>
            <Badge className={`${(performanceLevel as any).color} bg-white/10`}>
              {(performanceLevel as any).level}
            </Badge>
          </CardContent>
        </Card>
      // @ts-ignore
      </(motion as any).div>

      {/* Metrics Grid */}
      // @ts-ignore
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Load Time */}
        // @ts-ignore
        <Card className="glass-card">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Clock className="h-8 w-8 text-blue-400" />
              <div>
                <p className="text-white/70 text-sm">{(t as any).loadTime}</p>
                <p className="text-white font-semibold">{formatTime((metrics as any as any).loadTime)}</p>
              </div>
            </div>
          </CardContent>
        // @ts-ignore
        </Card>

        {/* Memory Usage */}
        // @ts-ignore
        <Card className="glass-card">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <MemoryStick className="h-8 w-8 text-green-400" />
              <div>
                <p className="text-white/70 text-sm">{(t as any).memoryUsage}</p>
                <p className="text-white font-semibold">
                  // @ts-ignore
                  {(metrics as any).memoryUsage.(percentage as any).toFixed(1 as any)}%
                </p>
              </div>
            </div>
          </CardContent>
        // @ts-ignore
        </Card>

        {/* Network Speed */}
        // @ts-ignore
        <Card className="glass-card">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Wifi className="h-8 w-8 text-yellow-400" />
              <div>
                <p className="text-white/70 text-sm">{(t as any).networkSpeed}</p>
                <p className="text-white font-semibold">{t[(metrics as any).networkSpeed]}</p>
              </div>
            </div>
          </CardContent>
        // @ts-ignore
        </Card>

        {/* Device Type */}
        // @ts-ignore
        <Card className="glass-card">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              {(metrics as any).deviceType === 'mobile' && <Smartphone className="h-8 w-8 text-purple-400" />}
              {(metrics as any).deviceType === 'tablet' && <Monitor className="h-8 w-8 text-purple-400" />}
              {(metrics as any).deviceType === 'desktop' && <Monitor className="h-8 w-8 text-purple-400" />}
              <div>
                <p className="text-white/70 text-sm">{(t as any).deviceType}</p>
                <p className="text-white font-semibold">{t[(metrics as any).deviceType]}</p>
              </div>
            </div>
          </CardContent>
        // @ts-ignore
        </Card>
      // @ts-ignore
      </div>

      {/* Web Vitals */}
      // @ts-ignore
      <Card className="glass-card">
        // @ts-ignore
        <CardHeader>
          <CardTitle className="text-white">{(t as any).webVitals}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="text-center">
              <p className="text-white/70 text-sm">{(t as any).fcp}</p>
              <p className="text-white font-semibold">{formatTime((metrics as any as any).firstContentfulPaint)}</p>
            </div>
            <div className="text-center">
              <p className="text-white/70 text-sm">{(t as any).lcp}</p>
              <p className="text-white font-semibold">{formatTime((metrics as any as any).largestContentfulPaint)}</p>
            </div>
            <div className="text-center">
              <p className="text-white/70 text-sm">{(t as any).cls}</p>
              <p className="text-white font-semibold">{(metrics as any).cumulativeLayoutShift.toFixed(3 as any)}</p>
            </div>
            <div className="text-center">
              <p className="text-white/70 text-sm">{(t as any).fid}</p>
              <p className="text-white font-semibold">{formatTime((metrics as any as any).firstInputDelay)}</p>
            </div>
          </div>
        </CardContent>
      // @ts-ignore
      </Card>

      {/* Optimizations */}
      // @ts-ignore
      <Card className="glass-card">
        // @ts-ignore
        <CardHeader>
          <CardTitle className="text-white">{(t as any).optimizations}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-white">{(t as any).enableOptimizations}</span>
              <Button
                onClick={enableOptimizations}
                disabled={isOptimizing || optimizationsEnabled}
                className="glass-button"
              >
                {isOptimizing ? (
                  <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                ) : optimizationsEnabled ? (
                  <CheckCircle className="h-4 w-4 mr-2" />
                ) : (
                  <Zap className="h-4 w-4 mr-2" />
                )}
                {optimizationsEnabled ? 'مُفعل' : (t as any).enableOptimizations}
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Button onClick={clearCache} variant="outline" className="glass-button">
                <HardDrive className="h-4 w-4 mr-2" />
                {(t as any).clearCache}
              </Button>
              <Button onClick={preloadPages} variant="outline" className="glass-button">
                <TrendingUp className="h-4 w-4 mr-2" />
                {(t as any).preloadPages}
              </Button>
            </div>

            {lastOptimized && (
              <p className="text-white/60 text-sm">
                آخر تحسين: {(lastOptimized as any).toLocaleString(language === 'ar' ? 'ar-SA' : 'en-US' as any)}
              </p>
            )}
          </div>
        </CardContent>
      // @ts-ignore
      </Card>

      {/* Recommendations */}
      {(recommendations as any).length > 0 && (
        <Card className="glass-card">
          <CardHeader>
            <CardTitle className="text-white">{(t as any).recommendations}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              // @ts-ignore
              {(recommendations as any).map((recommendation, index as any) => (
                <div key={index} className="flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4 text-yellow-400" />
                  <span className="text-white/80">{recommendation}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Alerts */}
      <Card className="glass-card">
        <CardHeader>
          <CardTitle className="text-white flex items-center justify-between">
            {(t as any).alerts}
            {(alerts as any).length > 0 && (
              <Button onClick={clearAlerts} variant="ghost" size="sm" className="text-white/60">
                مسح الكل
              </Button>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {(alerts as any).length === 0 ? (
            <div className="text-center py-8">
              <CheckCircle className="h-12 w-12 text-green-400 mx-auto mb-2" />
              <p className="text-white/60">{(t as any).noAlerts}</p>
            </div>
          ) : (
            <div className="space-y-3">
              // @ts-ignore
              {(alerts as any).slice(0, 5 as any).map((alert, index as any) => (
                <div
                  key={index}
                  className={`p-3 rounded-lg border ${
                    (alert as any).type === 'error' 
                      ? 'border-red-500/30 bg-red-500/10' 
                      : 'border-yellow-500/30 bg-yellow-500/10'
                  }`}
                >
                  <div className="flex items-center gap-2">
                    <AlertTriangle className={`h-4 w-4 ${
                      (alert as any).type === 'error' ? 'text-red-400' : 'text-yellow-400'
                    }`} />
                    <span className="text-white text-sm">{(alert as any).message}</span>
                  </div>
                  <p className="text-white/60 text-xs mt-1">
                    {(alert as any).timestamp.toLocaleString(language === 'ar' ? 'ar-SA' : 'en-US' as any)}
                  </p>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Actions */}
      <div className="flex gap-4 justify-center">
        <Button
          onClick={() => {
            // FIXED: Refresh metrics without page reload
            // @ts-ignore
            setMetrics(getPerformanceMetrics( as any))
            // @ts-ignore
            setAlerts(getPerformanceAlerts( as any))
          }}
          className="glass-button"
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          {(t as any).refreshMetrics}
        </Button>
        <Button onClick={exportReport} variant="outline" className="glass-button">
          <TrendingUp className="h-4 w-4 mr-2" />
          {(t as any).exportReport}
        </Button>
      </div>
    // @ts-ignore
    </div>
  // @ts-ignore
  )
}
