import React from 'react'

// Customer data interface for reports
interface CustomerData {
  id: number
  first_name: string
  last_name: string
  email: string
  phone: string
  customer_type: 'business' | 'enterprise' | 'individual'
  status: 'active' | 'inactive' | 'pending' | 'prospect'
  company_name?: string
  total_spent: number
  total_orders: number
  last_contact_date: string
}

interface CustomerReportProps {
  customers: CustomerData[]
  language: 'ar' | 'en'
  title?: string
  companyName?: string
  generatedBy?: string
}

export const CustomerReport: React.FC<CustomerReportProps> = ({
  customers,
  language,
  title = 'Customer Report',
  companyName = 'Enterprise Management System',
  generatedBy = 'System'
}) => {
  const isRTL = language === 'ar'

  const translations = {
    ar: {
      customerReport: 'تقرير العملاء',
      totalCustomers: 'إجمالي العملاء',
      activeCustomers: 'العملاء النشطون',
      totalRevenue: 'إجمالي الإيرادات',
      customerName: 'اسم العميل',
      customerType: 'نوع العميل',
      status: 'الحالة',
      contact: 'معلومات الاتصال',
      revenue: 'الإيرادات',
      generatedOn: 'تم الإنشاء في',
      confidential: 'هذا التقرير سري ومخصص للاستخدام الداخلي فقط',
      individual: 'فردي',
      business: 'تجاري',
      enterprise: 'مؤسسي',
      active: 'نشط',
      inactive: 'غير نشط',
      prospect: 'محتمل'
    },
    en: {
      customerReport: 'Customer Report',
      totalCustomers: 'Total Customers',
      activeCustomers: 'Active Customers',
      totalRevenue: 'Total Revenue',
      customerName: 'Customer Name',
      customerType: 'Customer Type',
      status: 'Status',
      contact: 'Contact Information',
      revenue: 'Revenue',
      generatedOn: 'Generated on',
      confidential: 'This report is confidential and for internal use only',
      individual: 'Individual',
      business: 'Business',
      enterprise: 'Enterprise',
      active: 'Active',
      inactive: 'Inactive',
      prospect: 'Prospect'
    }
  }

  const t = translations[language]

  // Calculate statistics
  const totalCustomers = customers.length
  const activeCustomers = customers.filter(c => c.status === 'active').length
  const totalRevenue = customers.reduce((sum, c) => sum + (c.total_spent || 0), 0)

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat(language === 'ar' ? 'ar-SA' : 'en-US', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount)
  }

  const currentDate = new Date().toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')
  const currentTime = new Date().toLocaleTimeString(language === 'ar' ? 'ar-SA' : 'en-US')

  return (
    <div 
      className={`bg-white min-h-screen p-8 ${isRTL ? 'rtl' : 'ltr'}`}
      style={{ 
        fontFamily: isRTL ? "'Amiri', 'Cairo', Arial, sans-serif" : "'Arial', 'Helvetica', sans-serif",
        maxWidth: '210mm',
        margin: '0 auto',
        boxShadow: '0 0 10px rgba(0,0,0,0.1)'
      }}
    >
      {/* Professional Header */}
      <div style={{ borderBottom: '3px solid #1e40af', paddingBottom: '20px', marginBottom: '30px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '20px' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '15px' }}>
            <div style={{
              width: '60px',
              height: '60px',
              backgroundColor: '#1e40af',
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontSize: '24px',
              fontWeight: 'bold'
            }}>
              EMS
            </div>
            <div>
              <h1 style={{ 
                margin: '0', 
                fontSize: '24px', 
                fontWeight: 'bold', 
                color: '#1e40af',
                lineHeight: '1.2'
              }}>
                {companyName}
              </h1>
              <p style={{ 
                margin: '5px 0 0 0', 
                fontSize: '14px', 
                color: '#6b7280' 
              }}>
                {language === 'ar' ? 'حلول الأعمال المتقدمة' : 'Advanced Business Solutions'}
              </p>
            </div>
          </div>
          <div style={{ textAlign: isRTL ? 'left' : 'right', fontSize: '12px', color: '#6b7280' }}>
            <div>{currentDate}</div>
            <div>{generatedBy}</div>
          </div>
        </div>
        
        <div style={{ textAlign: 'center' }}>
          <h2 style={{ 
            margin: '0', 
            fontSize: '20px', 
            fontWeight: '600', 
            color: '#374151' 
          }}>
            {title}
          </h2>
        </div>
      </div>

      {/* Summary Statistics */}
      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(3, 1fr)', 
        gap: '20px', 
        marginBottom: '30px' 
      }}>
        <div style={{
          backgroundColor: '#f8fafc',
          border: '1px solid #e2e8f0',
          borderRadius: '8px',
          padding: '20px',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '32px', fontWeight: 'bold', color: '#1e40af', marginBottom: '5px' }}>
            {totalCustomers}
          </div>
          <div style={{ fontSize: '14px', color: '#6b7280' }}>
            {t.totalCustomers}
          </div>
        </div>

        <div style={{
          backgroundColor: '#f0fdf4',
          border: '1px solid #bbf7d0',
          borderRadius: '8px',
          padding: '20px',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '32px', fontWeight: 'bold', color: '#16a34a', marginBottom: '5px' }}>
            {activeCustomers}
          </div>
          <div style={{ fontSize: '14px', color: '#6b7280' }}>
            {t.activeCustomers}
          </div>
        </div>

        <div style={{
          backgroundColor: '#fefce8',
          border: '1px solid #fde047',
          borderRadius: '8px',
          padding: '20px',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#ca8a04', marginBottom: '5px' }}>
            {formatCurrency(totalRevenue)}
          </div>
          <div style={{ fontSize: '14px', color: '#6b7280' }}>
            {t.totalRevenue}
          </div>
        </div>
      </div>

      {/* Customer Table */}
      <div style={{ marginBottom: '30px' }}>
        <h3 style={{ 
          fontSize: '18px', 
          fontWeight: '600', 
          color: '#1e40af', 
          marginBottom: '15px',
          borderBottom: '2px solid #e2e8f0',
          paddingBottom: '10px'
        }}>
          {language === 'ar' ? 'تفاصيل العملاء' : 'Customer Details'}
        </h3>

        <table style={{ 
          width: '100%', 
          borderCollapse: 'collapse',
          border: '1px solid #e2e8f0',
          borderRadius: '8px',
          overflow: 'hidden'
        }}>
          <thead>
            <tr style={{ backgroundColor: '#f8fafc' }}>
              <th style={{ 
                padding: '12px', 
                textAlign: isRTL ? 'right' : 'left',
                borderBottom: '1px solid #e2e8f0',
                fontSize: '14px',
                fontWeight: '600',
                color: '#374151'
              }}>
                {t.customerName}
              </th>
              <th style={{ 
                padding: '12px', 
                textAlign: isRTL ? 'right' : 'left',
                borderBottom: '1px solid #e2e8f0',
                fontSize: '14px',
                fontWeight: '600',
                color: '#374151'
              }}>
                {t.customerType}
              </th>
              <th style={{ 
                padding: '12px', 
                textAlign: isRTL ? 'right' : 'left',
                borderBottom: '1px solid #e2e8f0',
                fontSize: '14px',
                fontWeight: '600',
                color: '#374151'
              }}>
                {t.status}
              </th>
              <th style={{ 
                padding: '12px', 
                textAlign: isRTL ? 'right' : 'left',
                borderBottom: '1px solid #e2e8f0',
                fontSize: '14px',
                fontWeight: '600',
                color: '#374151'
              }}>
                {t.contact}
              </th>
              <th style={{ 
                padding: '12px', 
                textAlign: isRTL ? 'right' : 'left',
                borderBottom: '1px solid #e2e8f0',
                fontSize: '14px',
                fontWeight: '600',
                color: '#374151'
              }}>
                {t.revenue}
              </th>
            </tr>
          </thead>
          <tbody>
            {customers.map((customer, index) => (
              <tr key={index} style={{ borderBottom: '1px solid #f3f4f6' }}>
                <td style={{ padding: '10px', fontSize: '13px', color: '#374151' }}>
                  <div style={{ fontWeight: '500' }}>
                    {customer.first_name} {customer.last_name}
                  </div>
                  {customer.company_name && (
                    <div style={{ fontSize: '11px', color: '#6b7280' }}>
                      {customer.company_name}
                    </div>
                  )}
                </td>
                <td style={{ padding: '10px', fontSize: '13px', color: '#374151' }}>
                  {t[customer.customer_type as keyof typeof t] || customer.customer_type}
                </td>
                <td style={{ padding: '10px', fontSize: '13px' }}>
                  <span style={{
                    padding: '4px 8px',
                    borderRadius: '12px',
                    fontSize: '11px',
                    fontWeight: '500',
                    backgroundColor: customer.status === 'active' ? '#dcfce7' : 
                                   customer.status === 'prospect' ? '#dbeafe' : '#f1f5f9',
                    color: customer.status === 'active' ? '#166534' : 
                           customer.status === 'prospect' ? '#1d4ed8' : '#475569'
                  }}>
                    {t[customer.status as keyof typeof t] || customer.status}
                  </span>
                </td>
                <td style={{ padding: '10px', fontSize: '12px', color: '#6b7280' }}>
                  <div>{customer.email}</div>
                  <div>{customer.phone}</div>
                </td>
                <td style={{ padding: '10px', fontSize: '13px', fontWeight: '500', color: '#374151' }}>
                  {formatCurrency(customer.total_spent || 0)}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Professional Footer */}
      <div style={{ 
        borderTop: '3px solid #1e40af', 
        paddingTop: '20px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'flex-end'
      }}>
        <div>
          <div style={{ fontWeight: '600', fontSize: '14px', color: '#374151' }}>
            {companyName}
          </div>
          <div style={{ fontSize: '12px', color: '#6b7280' }}>
            {language === 'ar' ? 'تم إنشاء هذا التقرير بواسطة النظام' : 'Generated by Enterprise Management System'}
          </div>
        </div>
        <div style={{ textAlign: isRTL ? 'left' : 'right', fontSize: '12px', color: '#6b7280' }}>
          <div>{t.generatedOn}: {currentDate}</div>
          <div>{language === 'ar' ? 'الوقت:' : 'Time:'} {currentTime}</div>
        </div>
      </div>

      <div style={{ 
        textAlign: 'center', 
        marginTop: '20px', 
        paddingTop: '15px',
        borderTop: '1px solid #e2e8f0',
        fontSize: '11px', 
        color: '#9ca3af' 
      }}>
        {t.confidential}
      </div>
    </div>
  )
}

export default CustomerReport
