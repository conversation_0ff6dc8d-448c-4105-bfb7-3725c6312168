import React from 'react';
import { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import {
  FileText,
  Download,
  Calendar,
  Filter,
  Settings,
  BarChart3,
  PieChart,
  TrendingUp,
  Users,
  DollarSign,
  Package,
  Clock,
  Mail,
  Share,
  Save,
  Play,
  Pause,
  RefreshCw
} from 'lucide-react'

interface ReportGeneratorProps {
  language: 'ar' | 'en'
}

interface ReportTemplate {
  id: string
  name: string
  description: string
  category: string
  fields: string[]
  charts: string[]
  frequency?: string
}

const translations = {
  ar: {
    reports: 'التقارير',
    generateReport: 'إنشاء تقرير',
    reportTemplates: 'قوالب التقارير',
    customReport: 'تقرير مخصص',
    scheduledReports: 'التقارير المجدولة',
    reportName: 'اسم التقرير',
    description: 'الوصف',
    category: 'الفئة',
    dateRange: 'نطاق التاريخ',
    startDate: 'تاريخ البداية',
    endDate: 'تاريخ النهاية',
    fields: 'الحقول',
    charts: 'الرسوم البيانية',
    format: 'التنسيق',
    frequency: 'التكرار',
    recipients: 'المستلمون',
    generate: 'إنشاء',
    schedule: 'جدولة',
    download: 'تحميل',
    share: 'مشاركة',
    save: 'حفظ',
    cancel: 'إلغاء',
    daily: 'يومي',
    weekly: 'أسبوعي',
    monthly: 'شهري',
    quarterly: 'ربع سنوي',
    yearly: 'سنوي',
    pdf: 'PDF',
    excel: 'Excel',
    csv: 'CSV',
    categories: {
      hr: 'الموارد البشرية',
      sales: 'المبيعات',
      inventory: 'المخزون',
      projects: 'المشاريع',
      finance: 'المالية'
    },
    templates: {
      employeeReport: 'تقرير الموظفين',
      salesReport: 'تقرير المبيعات',
      inventoryReport: 'تقرير المخزون',
      projectReport: 'تقرير المشاريع',
      attendanceReport: 'تقرير الحضور',
      leaveReport: 'تقرير الإجازات'
    }
  },
  en: {
    reports: 'Reports',
    generateReport: 'Generate Report',
    reportTemplates: 'Report Templates',
    customReport: 'Custom Report',
    scheduledReports: 'Scheduled Reports',
    reportName: 'Report Name',
    description: 'Description',
    category: 'Category',
    dateRange: 'Date Range',
    startDate: 'Start Date',
    endDate: 'End Date',
    fields: 'Fields',
    charts: 'Charts',
    format: 'Format',
    frequency: 'Frequency',
    recipients: 'Recipients',
    generate: 'Generate',
    schedule: 'Schedule',
    download: 'Download',
    share: 'Share',
    save: 'Save',
    cancel: 'Cancel',
    daily: 'Daily',
    weekly: 'Weekly',
    monthly: 'Monthly',
    quarterly: 'Quarterly',
    yearly: 'Yearly',
    pdf: 'PDF',
    excel: 'Excel',
    csv: 'CSV',
    categories: {
      hr: 'Human Resources',
      sales: 'Sales',
      inventory: 'Inventory',
      projects: 'Projects',
      finance: 'Finance'
    },
    templates: {
      employeeReport: 'Employee Report',
      salesReport: 'Sales Report',
      inventoryReport: 'Inventory Report',
      projectReport: 'Project Report',
      attendanceReport: 'Attendance Report',
      leaveReport: 'Leave Report'
    }
  }
}

export default function ReportGenerator({ language }: ReportGeneratorProps): React.ReactElement {
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [selectedTemplate, setSelectedTemplate] = useState<string>('')
  const [isGenerating, setIsGenerating] = useState(false)
  const [reportData, setReportData] = useState({
    name: '',
    description: '',
    category: '',
    startDate: '',
    endDate: '',
    format: 'pdf',
    frequency: '',
    recipients: '',
    fields: [] as string[],
    charts: [] as string[]
  })
  
  const t = translations[language]
  const isRTL = language === 'ar'

  const reportTemplates: ReportTemplate[] = [
    {
      id: 'employee',
      name: t.templates.employeeReport,
      description: language === 'ar' ? 'تقرير شامل عن بيانات الموظفين' : 'Comprehensive employee data report',
      category: 'hr',
      fields: ['name', 'position', 'department', 'salary', 'hire_date'],
      charts: ['department_distribution', 'salary_ranges']
    },
    {
      id: 'sales',
      name: t.templates.salesReport,
      description: language === 'ar' ? 'تقرير تفصيلي عن المبيعات والإيرادات' : 'Detailed sales and revenue report',
      category: 'sales',
      fields: ['order_number', 'customer', 'amount', 'date', 'status'],
      charts: ['sales_trend', 'top_customers', 'revenue_by_month']
    },
    {
      id: 'inventory',
      name: t.templates.inventoryReport,
      description: language === 'ar' ? 'تقرير حالة المخزون والمنتجات' : 'Inventory status and products report',
      category: 'inventory',
      fields: ['item_name', 'sku', 'quantity', 'unit_price', 'category'],
      charts: ['stock_levels', 'category_distribution', 'low_stock_alerts']
    },
    {
      id: 'projects',
      name: t.templates.projectReport,
      description: language === 'ar' ? 'تقرير تقدم المشاريع والمهام' : 'Project progress and tasks report',
      category: 'projects',
      fields: ['project_name', 'status', 'start_date', 'end_date', 'budget'],
      charts: ['project_status', 'timeline', 'budget_utilization']
    },
    {
      id: 'attendance',
      name: t.templates.attendanceReport,
      description: language === 'ar' ? 'تقرير حضور وانصراف الموظفين' : 'Employee attendance and time tracking report',
      category: 'hr',
      fields: ['employee', 'date', 'check_in', 'check_out', 'total_hours'],
      charts: ['attendance_trend', 'department_attendance', 'overtime_analysis']
    },
    {
      id: 'leave',
      name: t.templates.leaveReport,
      description: language === 'ar' ? 'تقرير طلبات الإجازات والأرصدة' : 'Leave requests and balance report',
      category: 'hr',
      fields: ['employee', 'leave_type', 'start_date', 'end_date', 'status'],
      charts: ['leave_types', 'monthly_leaves', 'department_leaves']
    }
  ]

  const availableFields = {
    hr: ['employee_name', 'department', 'position', 'salary', 'hire_date', 'status'],
    sales: ['order_number', 'customer_name', 'amount', 'date', 'status', 'salesperson'],
    inventory: ['item_name', 'sku', 'quantity', 'unit_price', 'category', 'location'],
    projects: ['project_name', 'status', 'start_date', 'end_date', 'budget', 'team_size'],
    finance: ['transaction_id', 'amount', 'type', 'date', 'account', 'description']
  }

  const availableCharts = {
    hr: ['department_distribution', 'salary_ranges', 'attendance_trend', 'leave_analysis'],
    sales: ['sales_trend', 'top_customers', 'revenue_by_month', 'product_performance'],
    inventory: ['stock_levels', 'category_distribution', 'low_stock_alerts', 'turnover_rate'],
    projects: ['project_status', 'timeline', 'budget_utilization', 'team_workload'],
    finance: ['expense_categories', 'revenue_trend', 'profit_margin', 'cash_flow']
  }

  const handleTemplateSelect = (templateId: string): void => {
    const template = reportTemplates.find(t => t.id === templateId)
    if (template) {
      setReportData({
        ...reportData,
        name: template.name,
        description: template.description,
        category: template.category,
        fields: template.fields,
        charts: template.charts
      })
      setSelectedTemplate(templateId)
    }
  }

  const handleFieldToggle = (field: string): void => {
    setReportData(prev => ({
      ...prev,
      fields: prev.fields.includes(field)
        ? prev.fields.filter(f => f !== field)
        : [...prev.fields, field]
    }))
  }

  const handleChartToggle = (chart: string): void => {
    setReportData(prev => ({
      ...prev,
      charts: prev.charts.includes(chart)
        ? prev.charts.filter(c => c !== chart)
        : [...prev.charts, chart]
    }))
  }

  const handleGenerate = async () => {
    setIsGenerating(true)
    // Simulate report generation
    setTimeout(() => {
      setIsGenerating(false)
      setIsDialogOpen(false)
      // Here you would typically trigger the actual report generation
      console.log('Generating report with data:', reportData)
    }, 2000)
  }

  const resetForm = (): void => {
    setReportData({
      name: '',
      description: '',
      category: '',
      startDate: '',
      endDate: '',
      format: 'pdf',
      frequency: '',
      recipients: '',
      fields: [],
      charts: []
    })
    setSelectedTemplate('')
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">{t.reports}</h1>
          <p className="text-white/70">
            {language === 'ar' ? 'إنشاء وإدارة التقارير المخصصة' : 'Generate and manage custom reports'}
          </p>
        </div>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button className="glass-button" onClick={resetForm}>
              <FileText className="h-4 w-4 mr-2" />
              {t.generateReport}
            </Button>
          </DialogTrigger>
        </Dialog>
      </div>

      {/* Report Templates */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white text-xl">{t.reportTemplates}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {reportTemplates.map((template) => (
              <Card key={template.id} className="glass-card border-white/10 hover:border-white/30 transition-colors cursor-pointer">
                <CardContent className="p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-2">
                      {template.category === 'hr' && <Users className="h-5 w-5 text-blue-400" />}
                      {template.category === 'sales' && <DollarSign className="h-5 w-5 text-green-400" />}
                      {template.category === 'inventory' && <Package className="h-5 w-5 text-orange-400" />}
                      {template.category === 'projects' && <BarChart3 className="h-5 w-5 text-purple-400" />}
                      <h3 className="text-white font-medium">{template.name}</h3>
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      className="glass-button p-2"
                      onClick={() => {
                        handleTemplateSelect(template.id)
                        setIsDialogOpen(true)
                      }}
                    >
                      <Play className="h-3 w-3" />
                    </Button>
                  </div>
                  <p className="text-white/70 text-sm mb-3">{template.description}</p>
                  <div className="flex items-center justify-between text-xs text-white/50">
                    <span>{t.categories[template.category as keyof typeof t.categories]}</span>
                    <span>{template.fields.length} {t.fields}</span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Generate Report Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="glass-card border-white/20 max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-white text-xl">{t.generateReport}</DialogTitle>
            <DialogDescription className="text-white/70">
              {language === 'ar' ? 'قم بتخصيص التقرير حسب احتياجاتك' : 'Customize your report according to your needs'}
            </DialogDescription>
          </DialogHeader>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 py-4">
            {/* Basic Information */}
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="reportName" className="text-white">{t.reportName}</Label>
                <Input
                  id="reportName"
                  value={reportData.name}
                  onChange={(e) => setReportData({...reportData, name: e.target.value})}
                  className="glass text-white border-white/30"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description" className="text-white">{t.description}</Label>
                <Textarea
                  id="description"
                  value={reportData.description}
                  onChange={(e) => setReportData({...reportData, description: e.target.value})}
                  className="glass text-white border-white/30"
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="category" className="text-white">{t.category}</Label>
                <Select value={reportData.category} onValueChange={(value) => setReportData({...reportData, category: value})}>
                  <SelectTrigger className="glass text-white border-white/30">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="glass-card border-white/20">
                    <SelectItem value="hr">{t.categories.hr}</SelectItem>
                    <SelectItem value="sales">{t.categories.sales}</SelectItem>
                    <SelectItem value="inventory">{t.categories.inventory}</SelectItem>
                    <SelectItem value="projects">{t.categories.projects}</SelectItem>
                    <SelectItem value="finance">{t.categories.finance}</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="startDate" className="text-white">{t.startDate}</Label>
                  <Input
                    id="startDate"
                    type="date"
                    value={reportData.startDate}
                    onChange={(e) => setReportData({...reportData, startDate: e.target.value})}
                    className="glass text-white border-white/30"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="endDate" className="text-white">{t.endDate}</Label>
                  <Input
                    id="endDate"
                    type="date"
                    value={reportData.endDate}
                    onChange={(e) => setReportData({...reportData, endDate: e.target.value})}
                    className="glass text-white border-white/30"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="format" className="text-white">{t.format}</Label>
                <Select value={reportData.format} onValueChange={(value) => setReportData({...reportData, format: value})}>
                  <SelectTrigger className="glass text-white border-white/30">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="glass-card border-white/20">
                    <SelectItem value="pdf">{t.pdf}</SelectItem>
                    <SelectItem value="excel">{t.excel}</SelectItem>
                    <SelectItem value="csv">{t.csv}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Fields and Charts Selection */}
            <div className="space-y-4">
              {reportData.category && (
                <>
                  <div className="space-y-2">
                    <Label className="text-white">{t.fields}</Label>
                    <div className="space-y-2 max-h-32 overflow-y-auto">
                      {availableFields[reportData.category as keyof typeof availableFields]?.map((field) => (
                        <div key={field} className="flex items-center space-x-2">
                          <Checkbox
                            id={field}
                            checked={reportData.fields.includes(field)}
                            onCheckedChange={() => handleFieldToggle(field)}
                          />
                          <Label htmlFor={field} className="text-white/80 text-sm">
                            {field.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label className="text-white">{t.charts}</Label>
                    <div className="space-y-2 max-h-32 overflow-y-auto">
                      {availableCharts[reportData.category as keyof typeof availableCharts]?.map((chart) => (
                        <div key={chart} className="flex items-center space-x-2">
                          <Checkbox
                            id={chart}
                            checked={reportData.charts.includes(chart)}
                            onCheckedChange={() => handleChartToggle(chart)}
                          />
                          <Label htmlFor={chart} className="text-white/80 text-sm">
                            {chart.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>

          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setIsDialogOpen(false)}
              className="glass text-white border-white/30 hover:bg-white/20"
            >
              {t.cancel}
            </Button>
            <Button 
              onClick={handleGenerate}
              disabled={isGenerating || !reportData.name || !reportData.category}
              className="gradient-bg-blue text-white"
            >
              {isGenerating ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  {language === 'ar' ? 'جاري الإنشاء...' : 'Generating...'}
                </>
              ) : (
                <>
                  <FileText className="h-4 w-4 mr-2" />
                  {t.generate}
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
