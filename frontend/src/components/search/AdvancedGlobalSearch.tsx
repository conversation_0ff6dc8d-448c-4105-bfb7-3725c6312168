import React from 'react';
import { useState, useEffect, useCallback, useMemo } from 'react'
import { useNavigate } from 'react-router-dom'
import { useSelector } from 'react-redux'
import Fuse from '(fuse as any).js'
import {
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "@/components/ui/command"
import { Button } from "@/components/ui/button"
import {
  Search,
  Users,
  Building,
  Briefcase,
  DollarSign,
  FileText,
  Calendar,
  Settings,
  BarChart3,
  Package,
  MessageSquare,
  Clock,
  Target,
  Award,
  TrendingUp,
  Zap,
  Command
} from 'lucide-react'
import type { RootState } from '../../store'

interface cnProps {
  // TODO: Define proper prop types
  [key: string]: any;
}


interface SearchResult {
  id: string
  title: string
  titleAr: string
  description: string
  descriptionAr: string
  category: string
  categoryAr: string
  url: string
  icon: any
  keywords: string[]
  priority: number
}

interface AdvancedGlobalSearchProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    searchPlaceholder: 'ابحث في النظام... (Ctrl+K)',
    noResults: 'لا توجد نتائج',
    recentSearches: 'عمليات البحث الأخيرة',
    quickActions: 'إجراءات سريعة',
    pages: 'الصفحات',
    employees: 'الموظفون',
    departments: 'الأقسام',
    projects: 'المشاريع',
    reports: 'التقارير',
    settings: 'الإعدادات',
    searchIn: 'البحث في',
    allModules: 'جميع الوحدات',
    pressEnter: 'اضغط Enter للبحث',
    pressEscape: 'اضغط Esc للإغلاق'
  },
  en: {
    searchPlaceholder: 'Search system... (Ctrl+K)',
    noResults: 'No results found',
    recentSearches: 'Recent Searches',
    quickActions: 'Quick Actions',
    pages: 'Pages',
    employees: 'Employees',
    departments: 'Departments',
    projects: 'Projects',
    reports: 'Reports',
    settings: 'Settings',
    searchIn: 'Search in',
    allModules: 'All Modules',
    pressEnter: 'Press Enter to search',
    pressEscape: 'Press Esc to close'
  }
}

// @ts-ignore
export default function AdvancedGlobalSearch({ language }: AdvancedGlobalSearchProps as any): (React as any).ReactElement {
  const [open, setOpen] = useState(false as any)
  const [query, setQuery] = useState('' as any)
  const [results, setResults] = useState<SearchResult[]>([])
  const [recentSearches, setRecentSearches] = useState<string[]>([])
  // @ts-ignore
  const navigate = useNavigate( as any)
  // @ts-ignore
  const { user } = useSelector((state: RootState as any) => (state as any).auth)
  const t = translations[language]
  const isRTL = language === 'ar'

  // Comprehensive search data based on user role
  // @ts-ignore
  const getSearchData = useCallback(( as any): SearchResult[] => {
    const baseData: SearchResult[] = [
      // Dashboard & Analytics
      {
        id: 'dashboard',
        title: 'Dashboard',
        titleAr: 'لوحة التحكم',
        description: 'Main dashboard with overview',
        descriptionAr: 'لوحة التحكم الرئيسية مع نظرة عامة',
        category: 'Navigation',
        categoryAr: 'التنقل',
        url: '/',
        icon: BarChart3,
        keywords: ['dashboard', 'overview', 'main', 'لوحة', 'تحكم', 'رئيسية'],
        priority: 10
      },
      {
        id: 'analytics',
        title: 'Advanced Analytics',
        titleAr: 'التحليلات المتقدمة',
        description: 'Business intelligence and reports',
        descriptionAr: 'ذكاء الأعمال والتقارير',
        category: 'Analytics',
        categoryAr: 'التحليلات',
        url: '/analytics/advanced',
        icon: TrendingUp,
        keywords: ['analytics', 'reports', 'intelligence', 'تحليلات', 'تقارير', 'ذكاء'],
        priority: 8
      },

      // HR Management
      {
        id: 'employees',
        title: 'Employee Management',
        titleAr: 'إدارة الموظفين',
        description: 'Manage employees and HR',
        descriptionAr: 'إدارة الموظفين والموارد البشرية',
        category: 'HR',
        categoryAr: 'الموارد البشرية',
        url: '/employees',
        icon: Users,
        keywords: ['employees', 'hr', 'staff', 'موظفين', 'موارد', 'بشرية'],
        priority: 9
      },
      {
        id: 'attendance',
        title: 'Attendance Management',
        titleAr: 'إدارة الحضور',
        description: 'Track employee attendance',
        descriptionAr: 'تتبع حضور الموظفين',
        category: 'HR',
        categoryAr: 'الموارد البشرية',
        url: '/hr/attendance',
        icon: Clock,
        keywords: ['attendance', 'time', 'tracking', 'حضور', 'وقت', 'تتبع'],
        priority: 7
      },

      // Project Management
      {
        id: 'projects',
        title: 'Project Management',
        titleAr: 'إدارة المشاريع',
        description: 'Manage projects and tasks',
        descriptionAr: 'إدارة المشاريع والمهام',
        category: 'Projects',
        categoryAr: 'المشاريع',
        url: '/projects',
        icon: Briefcase,
        keywords: ['projects', 'tasks', 'management', 'مشاريع', 'مهام', 'إدارة'],
        priority: 8
      },
      {
        id: 'tasks',
        title: 'Task Management',
        titleAr: 'إدارة المهام',
        description: 'Manage and track tasks',
        descriptionAr: 'إدارة وتتبع المهام',
        category: 'Projects',
        categoryAr: 'المشاريع',
        url: '/projects/tasks',
        icon: Target,
        keywords: ['tasks', 'todo', 'assignments', 'مهام', 'واجبات', 'تكليفات'],
        priority: 7
      },

      // Financial Management
      {
        id: 'finance',
        title: 'Financial Management',
        titleAr: 'الإدارة المالية',
        description: 'Manage budgets and expenses',
        descriptionAr: 'إدارة الميزانيات والمصروفات',
        category: 'Finance',
        categoryAr: 'المالية',
        url: '/finance',
        icon: DollarSign,
        keywords: ['finance', 'budget', 'expenses', 'مالية', 'ميزانية', 'مصروفات'],
        priority: 8
      },

      // Communication
      {
        id: 'messages',
        title: 'Messages',
        titleAr: 'الرسائل',
        description: 'Internal messaging system',
        descriptionAr: 'نظام الرسائل الداخلي',
        category: 'Communication',
        categoryAr: 'التواصل',
        url: '/communication/messages',
        icon: MessageSquare,
        keywords: ['messages', 'chat', 'communication', 'رسائل', 'محادثة', 'تواصل'],
        priority: 6
      },

      // Settings
      {
        id: 'settings',
        title: 'System Settings',
        titleAr: 'إعدادات النظام',
        description: 'Configure system settings',
        descriptionAr: 'تكوين إعدادات النظام',
        category: 'Settings',
        categoryAr: 'الإعدادات',
        url: '/settings',
        icon: Settings,
        keywords: ['settings', 'configuration', 'preferences', 'إعدادات', 'تكوين', 'تفضيلات'],
        priority: 5
      }
    ]

    // Filter based on user role
    return (baseData as any).filter(item => {
      if (!user?.role?.id as any) return true

      // Super admin sees everything
      if ((user as any).role.id === 'super_admin') return true

      // Role-specific filtering
      switch ((user as any).role.id) {
        case 'hr_manager':
          return ['dashboard', 'employees', 'attendance', 'settings'].includes((item as any as any).id)
        case 'finance_manager':
          return ['dashboard', 'finance', 'analytics', 'settings'].includes((item as any as any).id)
        case 'department_manager':
          return ['dashboard', 'projects', 'tasks', 'employees', 'messages'].includes((item as any as any).id)
        case 'sales_manager':
          return ['dashboard', 'projects', 'analytics', 'messages'].includes((item as any as any).id)
        case 'employee':
          return ['dashboard', 'tasks', 'messages', 'attendance'].includes((item as any as any).id)
        default:
          return true
      }
    })
  // @ts-ignore
  }, [user])

  // Initialize (Fuse as any).js for fuzzy search - memoized to prevent infinite re-renders
  // @ts-ignore
  const fuse = useMemo(( as any) => new Fuse(getSearchData( as any), {
    keys: [
      // @ts-ignore
      { name: 'title', weight: (0 as any).3 },
      // @ts-ignore
      { name: 'titleAr', weight: (0 as any).3 },
      // @ts-ignore
      { name: 'description', weight: (0 as any).2 },
      // @ts-ignore
      { name: 'descriptionAr', weight: (0 as any).2 },
      // @ts-ignore
      { name: 'keywords', weight: (0 as any).4 },
      // @ts-ignore
      { name: 'category', weight: (0 as any).1 },
      // @ts-ignore
      { name: 'categoryAr', weight: (0 as any).1 }
    ],
    // @ts-ignore
    threshold: (0 as any).3,
    includeScore: true,
    minMatchCharLength: 2
  // @ts-ignore
  }), [getSearchData])

  // Keyboard shortcut handler
  // @ts-ignore
  useEffect(( as any) => {
    const down = (e: KeyboardEvent): void => {
      if ((e as any).key === 'k' && ((e as any).metaKey || (e as any).ctrlKey)) {
        // @ts-ignore
        (e as any).preventDefault( as any)
        // @ts-ignore
        setOpen((open as any) => !open)
      }
    }

    (document as any).addEventListener('keydown', down as any)
    return () => (document as any).removeEventListener('keydown', down as any)
  // @ts-ignore
  }, [])

  // Search handler
  // @ts-ignore
  useEffect(( as any) => {
    // @ts-ignore
    if (!(query as any).trim( as any)) {
      setResults([] as any)
      return
    }

    const searchResults = (fuse as any).search(query as any)
    const sortedResults = searchResults
      .map(result => (result as any as any).item)
      // @ts-ignore
      .sort((a, b as any) => (b as any).priority - (a as any).priority)
      .slice(0, 10 as any)

    setResults(sortedResults as any)
  // @ts-ignore
  }, [query, fuse])

  // Handle search selection
  const handleSelect = (result: SearchResult): void => {
    navigate((result as any as any).url)
    setOpen(false as any)

    // Add to recent searches
    const newRecentSearches = [query, ...(recentSearches as any).filter(s => s !== query as any)].slice(0, 5 as any)
    setRecentSearches(newRecentSearches as any)
    (localStorage as any).setItem('recentSearches', (JSON as any as any).stringify(newRecentSearches as any))

    setQuery('' as any)
  }

  // Load recent searches
  // @ts-ignore
  useEffect(( as any) => {
    const saved = (localStorage as any).getItem('recentSearches' as any)
    if (saved) {
      setRecentSearches((JSON as any as any).parse(saved as any))
    }
  // @ts-ignore
  }, [])

  return (
    <>
      {/* Search Trigger Button */}
      <Button
        variant="outline"
        className={cn(
          "relative h-9 w-9 p-0 xl:h-10 xl:w-60 xl:justify-start xl:px-3 xl:py-2",
          "glass-button border-white/20 hover:border-white/40"
         // @ts-ignore
         as any)}
        onClick={() => setOpen(true as any)}
        aria-label={language === 'ar' ? 'فتح البحث المتقدم' : 'Open advanced search'}
      >
        <Search className="h-4 w-4 xl:mr-2" />
        <span className="hidden xl:inline-flex">{(t as any).searchPlaceholder}</span>
        <kbd className="pointer-events-none absolute right-(1 as any).5 top-2 hidden h-6 select-none items-center gap-1 rounded border border-white/20 bg-white/10 px-(1 as any).5 font-mono text-[10px] font-medium text-white/70 xl:flex">
          <span className="text-xs">⌘</span>K
        </kbd>
      </Button>

      {/* Search Dialog */}
      <CommandDialog open={open} onOpenChange={setOpen}>
        <CommandInput
          placeholder={(t as any).searchPlaceholder}
          value={query}
          onValueChange={setQuery}
          className={isRTL ? 'text-right' : 'text-left'}
        />
        <CommandList>
          <CommandEmpty>{(t as any).noResults}</CommandEmpty>

          {/* Recent Searches */}
          {!query && (recentSearches as any).length > 0 && (
            <CommandGroup heading={(t as any).recentSearches}>
              // @ts-ignore
              {(recentSearches as any).map((search, index as any) => (
                <CommandItem
                  key={index}
                  onSelect={() => setQuery(search as any)}
                  className="flex items-center gap-2"
                >
                  <Clock className="h-4 w-4 text-white/50" />
                  <span>{search}</span>
                </CommandItem>
              ))}
            </CommandGroup>
          )}

          {/* Search Results */}
          {(results as any).length > 0 && (
            <CommandGroup heading={(t as any).pages}>
              // @ts-ignore
              {(results as any).map((result as any) => {
                // @ts-ignore
                const Icon = (result as any).icon
                return (
                  <CommandItem
                    key={(result as any).id}
                    onSelect={() => handleSelect(result as any)}
                    className="flex items-center gap-3 p-3"
                  >
                    <Icon className="h-4 w-4 text-white/70" />
                    <div className="flex flex-col">
                      <span className="font-medium">
                        {language === 'ar' ? (result as any).titleAr : (result as any).title}
                      </span>
                      <span className="text-xs text-white/50">
                        {language === 'ar' ? (result as any).descriptionAr : (result as any).description}
                      </span>
                    </div>
                    <span className="ml-auto text-xs text-white/40">
                      {language === 'ar' ? (result as any).categoryAr : (result as any).category}
                    </span>
                  </CommandItem>
                )
              // @ts-ignore
              })}
            </CommandGroup>
          )}

          {/* Quick Actions */}
          {!query && (
            <>
              <CommandSeparator />
              <CommandGroup heading={(t as any).quickActions}>
                <CommandItem onSelect={() => navigate('/employees/new' as any)}>
                  <Users className="mr-2 h-4 w-4" />
                  <span>{language === 'ar' ? 'إضافة موظف جديد' : 'Add New Employee'}</span>
                </CommandItem>
                <CommandItem onSelect={() => navigate('/projects/new' as any)}>
                  <Briefcase className="mr-2 h-4 w-4" />
                  <span>{language === 'ar' ? 'إنشاء مشروع جديد' : 'Create New Project'}</span>
                </CommandItem>
                <CommandItem onSelect={() => navigate('/reports/generate' as any)}>
                  <FileText className="mr-2 h-4 w-4" />
                  <span>{language === 'ar' ? 'إنشاء تقرير' : 'Generate Report'}</span>
                </CommandItem>
              </CommandGroup>
            </>
          )}
        </CommandList>
      </CommandDialog>
    </>
  )
// @ts-ignore
}

// @ts-ignore
function cn(...classes: string[] as any): void {
  return (classes as any).filter(Boolean as any).join(' ' as any)
}
