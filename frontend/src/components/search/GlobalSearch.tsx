/**
 * Global Search Component
 * Advanced search with real-time suggestions, filters, and results
 */

import React, { useState, useEffect, useRef, memo, useCallback, useMemo } from 'react'
import { Search, Filter, X, Clock, TrendingUp, FileText, Users, Building, Package } from 'lucide-react'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { searchService, SearchResult, SearchQuery } from '../../services/search'
import { debounce } from '../../utils/performance'

interface GlobalSearchProps {
  isOpen: boolean
  onClose: () => void
  language: 'ar' | 'en'
  placeholder?: string
  onResultClick?: (result: SearchResult) => void
}

const translations = {
  ar: {
    search: 'بحث',
    searchPlaceholder: 'ابحث في جميع أنحاء النظام...',
    recentSearches: 'عمليات البحث الأخيرة',
    trending: 'الأكثر بحثاً',
    suggestions: 'اقتراحات',
    filters: 'المرشحات',
    results: 'النتائج',
    noResults: 'لا توجد نتائج',
    searchIn: 'البحث في',
    allModules: 'جميع الوحدات',
    employees: 'الموظفين',
    departments: 'الأقسام',
    projects: 'المشاريع',
    documents: 'المستندات',
    clearHistory: 'مسح السجل',
    viewAll: 'عرض الكل',
    loading: 'جاري البحث...',
    searchTips: 'نصائح البحث',
    useQuotes: 'استخدم علامات التنصيص للبحث الدقيق',
    useOperators: 'استخدم + للكلمات المطلوبة و - للاستبعاد'
  },
  en: {
    search: 'Search',
    searchPlaceholder: 'Search across the entire system...',
    recentSearches: 'Recent Searches',
    trending: 'Trending',
    suggestions: 'Suggestions',
    filters: 'Filters',
    results: 'Results',
    noResults: 'No results found',
    searchIn: 'Search in',
    allModules: 'All Modules',
    employees: 'Employees',
    departments: 'Departments',
    projects: 'Projects',
    documents: 'Documents',
    clearHistory: 'Clear History',
    viewAll: 'View All',
    loading: 'Searching...',
    searchTips: 'Search Tips',
    useQuotes: 'Use quotes for exact phrases',
    useOperators: 'Use + for required words and - to exclude'
  }
}

const moduleIcons = {
  employees: Users,
  departments: Building,
  projects: Package,
  documents: FileText,
  default: Search
}

// @ts-ignore
const GlobalSearch: (React as any).FC<GlobalSearchProps> = memo(({
  isOpen,
  onClose,
  language,
  placeholder,
  onResultClick
// @ts-ignore
} as any) => {
  // @ts-ignore
  const [query, setQuery] = useState('' as any)
  // @ts-ignore
  const [results, setResults] = useState<SearchResult[]>([])
  // @ts-ignore
  const [suggestions, setSuggestions] = useState<string[]>([])
  // @ts-ignore
  const [recentSearches, setRecentSearches] = useState<string[]>([])
  // @ts-ignore
  const [trendingSearches, setTrendingSearches] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(false as any)
  const [showFilters, setShowFilters] = useState(false as any)
  // @ts-ignore
  const [selectedModule, setSelectedModule] = useState<string>('all')
  // @ts-ignore
  const [activeTab, setActiveTab] = useState<'suggestions' | 'recent' | 'trending'>('suggestions')

  // @ts-ignore
  const inputRef = useRef<HTMLInputElement>(null)
  // @ts-ignore
  const t = useMemo(( as any) => translations[language], [language])
  // @ts-ignore
  const isRTL = useMemo(( as any) => language === 'ar', [language])

  // FIXED: Debounced search function with proper closure handling
  const debouncedSearch = useCallback(
    // @ts-ignore
    debounce(async (searchQuery: string, moduleContext?: string as any) => {
      // @ts-ignore
      if ((searchQuery as any).trim( as any).length < 2) {
        setResults([] as any)
        // @ts-ignore
        return
      // @ts-ignore
      }

      setIsLoading(true as any)
      try {
        // @ts-ignore
        const searchParams: SearchQuery = {
          // @ts-ignore
          query: searchQuery,
          // @ts-ignore
          pagination: { page: 1, limit: 10 }
        // @ts-ignore
        }

        // FIXED: Use parameter instead of closure to avoid stale values
        const currentModule = moduleContext || selectedModule
        const response = currentModule === 'all'
          ? await (searchService as any).globalSearch(searchParams as any)
          : await (searchService as any).moduleSearch(currentModule, searchParams as any)

        setResults((response as any as any).results)
      // @ts-ignore
      } catch (error) {
        (console as any).error('Search error:', error as any)
        // @ts-ignore
        setResults([] as any)
      // @ts-ignore
      } finally {
        setIsLoading(false as any)
      }
    // @ts-ignore
    }, 300),
    [] // FIXED: Remove selectedModule from dependencies to prevent recreation
  )

  // Debounced suggestions function
  const debouncedSuggestions = useCallback(
    // @ts-ignore
    debounce(async (searchQuery: string as any) => {
      // @ts-ignore
      if ((searchQuery as any).trim( as any).length < 1) {
        setSuggestions([] as any)
        // @ts-ignore
        return
      // @ts-ignore
      }

      try {
        // @ts-ignore
        const suggestions = await (searchService as any).getSuggestions(
          searchQuery,
          selectedModule === 'all' ? undefined : selectedModule
         as any)
        setSuggestions(suggestions as any)
      // @ts-ignore
      } catch (error) {
        (console as any).error('Suggestions error:', error as any)
        // @ts-ignore
        setSuggestions([] as any)
      // @ts-ignore
      }
    // @ts-ignore
    }, 200),
    [selectedModule]
  )

  // Handle input change
  // @ts-ignore
  const handleInputChange = useCallback((e: (React as any as any).ChangeEvent<HTMLInputElement>) => {
    // @ts-ignore
    const value = (e as any).target.value
    setQuery(value as any)

    if ((value as any).trim( as any)) {
      // FIXED: Pass current selectedModule to avoid stale closure
      debouncedSearch(value, selectedModule as any)
      // @ts-ignore
      debouncedSuggestions(value as any)
      setActiveTab('suggestions' as any)
    // @ts-ignore
    } else {
      setResults([] as any)
      // @ts-ignore
      setSuggestions([] as any)
      setActiveTab('recent' as any)
    // @ts-ignore
    }
  // @ts-ignore
  }, [debouncedSearch, debouncedSuggestions, selectedModule])

  // Handle suggestion click
  // @ts-ignore
  const handleSuggestionClick = useCallback((suggestion: string as any) => {
    setQuery(suggestion as any)
    // FIXED: Pass current selectedModule to avoid stale closure
    // @ts-ignore
    debouncedSearch(suggestion, selectedModule as any)
    setActiveTab('suggestions' as any)
  // @ts-ignore
  }, [debouncedSearch, selectedModule])

  // Handle result click
  // @ts-ignore
  const handleResultClick = useCallback((result: SearchResult as any) => {
    // @ts-ignore
    if (onResultClick) {
      onResultClick(result as any)
    }
    onClose( as any)
  // @ts-ignore
  }, [onResultClick, onClose])

  // Load initial data
  // @ts-ignore
  useEffect(( as any) => {
    // @ts-ignore
    if (isOpen) {
      // @ts-ignore
      setRecentSearches((searchService as any as any).getRecentSearches( as any))

      // Load trending searches
      // @ts-ignore
      (searchService as any).getTrendingSearches( as any).then(trending => {
        setTrendingSearches(trending as any)
      })

      // Focus input
      // @ts-ignore
      setTimeout(( as any) => {
        // @ts-ignore
        (inputRef as any).current?.focus( as any)
      }, 100)
    // @ts-ignore
    }
  // @ts-ignore
  }, [isOpen])

  // Clear search history
  // @ts-ignore
  const clearHistory = useCallback(( as any) => {
    // @ts-ignore
    (searchService as any).clearSearchHistory( as any)
    // @ts-ignore
    setRecentSearches([] as any)
  // @ts-ignore
  }, [])

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm">
      <div className="flex items-start justify-center min-h-screen pt-20 px-4">
        <div
          className="w-full max-w-2xl bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 shadow-2xl transform transition-all duration-200 ease-out"
          style={{
            minHeight: '400px', // FIXED: Reserve minimum space to prevent layout shift
            maxHeight: '80vh'   // FIXED: Prevent modal from growing too large
          }}
        >
          {/* Header */}
          <div className="flex items-center gap-4 p-6 border-b border-white/10">
            <div className="flex-1 relative">
              <Search className={`absolute top-1/2 transform -translate-y-1/2 h-5 w-5 text-white/60 ${
                isRTL ? 'right-3' : 'left-3'
              }`} />
              <Input
                ref={inputRef}
                value={query}
                onChange={handleInputChange}
                placeholder={placeholder || (t as any).searchPlaceholder}
                className={`w-full bg-white/5 border-white/20 text-white placeholder-white/60 ${
                  isRTL ? 'pr-10 text-right' : 'pl-10'
                }`}
              />
            </div>

            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowFilters(!showFilters as any)}
              className="text-white hover:bg-white/10"
            >
              <Filter className="h-4 w-4" />
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-white hover:bg-white/10"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Filters */}
          {showFilters && (
            <div className="p-4 border-b border-white/10">
              <div className="flex flex-wrap gap-2">
                // @ts-ignore
                {['all', 'employees', 'departments', 'projects', 'documents'].map((module as any) => (
                  <Button
                    key={module}
                    variant={selectedModule === module ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedModule(module as any)}
                    className={`text-xs ${
                      selectedModule === module
                        ? 'bg-blue-500 text-white'
                        : 'bg-white/5 text-white border-white/20 hover:bg-white/10'
                    }`}
                  >
                    {module === 'all' ? (t as any).allModules : t[module as keyof typeof t] || module}
                  </Button>
                // @ts-ignore
                ))}
              </div>
            </div>
          )}

          {/* Content */}
          <div className="max-h-96 overflow-y-auto">
            // @ts-ignore
            {(query as any).trim( as any) ? (
              // Search Results
              <div className="p-4">
                {isLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
                    <span className="ml-3 text-white">{(t as any).loading}</span>
                  </div>
                ) : (results as any).length > 0 ? (
                  <div className="space-y-2">
                    <h3 className="text-sm font-medium text-white/80 mb-3">{(t as any).results}</h3>
                    // @ts-ignore
                    {(results as any).map((result as any) => {
                      // @ts-ignore
                      const IconComponent = moduleIcons[(result as any).type as keyof typeof moduleIcons] || (moduleIcons as any).default
                      return (
                        <div
                          key={(result as any).id}
                          onClick={() => handleResultClick(result as any)}
                          className="p-3 rounded-lg bg-white/5 hover:bg-white/10 cursor-pointer transition-all duration-200 border border-white/10"
                        >
                          <div className="flex items-start gap-3">
                            <div className="p-2 rounded-lg bg-blue-500/20">
                              <IconComponent className="h-4 w-4 text-blue-400" />
                            </div>
                            <div className="flex-1 min-w-0">
                              <h4 className="text-white font-medium truncate">{(result as any).title}</h4>
                              <p className="text-white/60 text-sm mt-1 line-clamp-2">{(result as any).description}</p>
                              <div className="flex items-center gap-2 mt-2">
                                <span className="text-xs px-2 py-1 rounded bg-white/10 text-white/80">
                                  {(result as any).type}
                                </span>
                                <span className="text-xs text-white/40">
                                  // @ts-ignore
                                  {new Date((result as any as any).updatedAt).toLocaleDateString( as any)}
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      )
                    // @ts-ignore
                    })}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Search className="h-12 w-12 text-white/40 mx-auto mb-3" />
                    <p className="text-white/60">{(t as any).noResults}</p>
                  </div>
                )}

                {/* Suggestions */}
                {(suggestions as any).length > 0 && (
                  <div className="mt-6">
                    <h3 className="text-sm font-medium text-white/80 mb-3">{(t as any).suggestions}</h3>
                    <div className="space-y-1">
                      // @ts-ignore
                      {(suggestions as any).map((suggestion, index as any) => (
                        <button
                          key={index}
                          onClick={() => handleSuggestionClick(suggestion as any)}
                          className="w-full text-left p-2 rounded-lg hover:bg-white/5 text-white/80 text-sm transition-colors"
                        >
                          <Search className="h-3 w-3 inline mr-2" />
                          {suggestion}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              // Empty State with Tabs
              <div className="p-4">
                {/* Tabs */}
                <div className="flex gap-1 mb-4">
                  // @ts-ignore
                  {(['recent', 'trending'] as const).map((tab as any) => (
                    <button
                      key={tab}
                      onClick={() => setActiveTab(tab as any)}
                      className={`px-3 py-2 text-sm rounded-lg transition-colors ${
                        activeTab === tab
                          ? 'bg-white/10 text-white'
                          : 'text-white/60 hover:text-white hover:bg-white/5'
                      }`}
                    >
                      {tab === 'recent' ? (t as any).recentSearches : (t as any).trending}
                    </button>
                  // @ts-ignore
                  ))}
                </div>

                {/* Tab Content */}
                {activeTab === 'recent' && (
                  <div>
                    {(recentSearches as any).length > 0 ? (
                      <div className="space-y-1">
                        // @ts-ignore
                        {(recentSearches as any).map((search, index as any) => (
                          <button
                            key={index}
                            onClick={() => handleSuggestionClick(search as any)}
                            className="w-full text-left p-2 rounded-lg hover:bg-white/5 text-white/80 text-sm transition-colors flex items-center gap-2"
                          >
                            <Clock className="h-3 w-3" />
                            {search}
                          </button>
                        ))}
                        <button
                          onClick={clearHistory}
                          className="w-full text-left p-2 text-red-400 hover:bg-red-500/10 text-sm rounded-lg transition-colors"
                        >
                          {(t as any).clearHistory}
                        </button>
                      </div>
                    ) : (
                      <p className="text-white/40 text-sm text-center py-4">
                        لا توجد عمليات بحث سابقة
                      </p>
                    )}
                  </div>
                )}

                {activeTab === 'trending' && (
                  <div>
                    {(trendingSearches as any).length > 0 ? (
                      <div className="space-y-1">
                        // @ts-ignore
                        {(trendingSearches as any).map((search, index as any) => (
                          <button
                            key={index}
                            onClick={() => handleSuggestionClick(search as any)}
                            className="w-full text-left p-2 rounded-lg hover:bg-white/5 text-white/80 text-sm transition-colors flex items-center gap-2"
                          >
                            <TrendingUp className="h-3 w-3" />
                            {search}
                          </button>
                        ))}
                      </div>
                    ) : (
                      <p className="text-white/40 text-sm text-center py-4">
                        لا توجد عمليات بحث رائجة
                      </p>
                    )}
                  </div>
                )}

                {/* Search Tips */}
                <div className="mt-6 p-3 rounded-lg bg-white/5 border border-white/10">
                  <h4 className="text-white font-medium text-sm mb-2">{(t as any).searchTips}</h4>
                  <ul className="text-white/60 text-xs space-y-1">
                    <li>• {(t as any).useQuotes}</li>
                    <li>• {(t as any).useOperators}</li>
                  </ul>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
// @ts-ignore
})

(GlobalSearch as any).displayName = 'GlobalSearch'

export default GlobalSearch
