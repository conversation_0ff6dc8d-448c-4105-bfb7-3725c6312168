import React, { useState, useEffect } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { toast } from 'react-hot-toast'
import {
  Plus,
  MoreVertical,
  Calendar,
  User,
  Clock,
  Flag,
  MessageSquare,
  Paperclip,
  CheckCircle,
  Circle,
  AlertTriangle
} from 'lucide-react'

interface KanbanBoardProps {
  language: 'ar' | 'en'
  userRole: string
}

interface Task {
  id: string
  title: string
  description: string
  assignee: {
    id: string
    name: string
    avatar?: string
  }
  priority: 'low' | 'medium' | 'high' | 'urgent'
  dueDate: Date
  tags: string[]
  comments: number
  attachments: number
  progress: number
  status: 'todo' | 'in_progress' | 'review' | 'done'
}

interface Column {
  id: string
  title: string
  tasks: Task[]
  color: string
  limit?: number
}

const translations = {
  ar: {
    kanbanBoard: 'لوحة المهام',
    addTask: 'إضافة مهمة',
    todo: 'للقيام',
    inProgress: 'قيد التنفيذ',
    review: 'للمراجعة',
    done: 'مكتمل',
    priority: 'الأولوية',
    dueDate: 'تاريخ الاستحقاق',
    assignee: 'المكلف',
    comments: 'تعليقات',
    attachments: 'مرفقات',
    progress: 'التقدم',
    low: 'منخفض',
    medium: 'متوسط',
    high: 'عالي',
    urgent: 'عاجل',
    overdue: 'متأخر',
    today: 'اليوم',
    tomorrow: 'غداً',
    thisWeek: 'هذا الأسبوع',
    addColumn: 'إضافة عمود',
    taskLimit: 'حد المهام'
  },
  en: {
    kanbanBoard: 'Kanban Board',
    addTask: 'Add Task',
    todo: 'To Do',
    inProgress: 'In Progress',
    review: 'Review',
    done: 'Done',
    priority: 'Priority',
    dueDate: 'Due Date',
    assignee: 'Assignee',
    comments: 'Comments',
    attachments: 'Attachments',
    progress: 'Progress',
    low: 'Low',
    medium: 'Medium',
    high: 'High',
    urgent: 'Urgent',
    overdue: 'Overdue',
    today: 'Today',
    tomorrow: 'Tomorrow',
    thisWeek: 'This Week',
    addColumn: 'Add Column',
    taskLimit: 'Task Limit'
  }
}

// @ts-ignore
export default function KanbanBoard({ language, userRole }: KanbanBoardProps as any): (React as any).ReactElement {
  const [columns, setColumns] = useState<Column[]>([])
  const [draggedTask, setDraggedTask] = useState<Task | null>(null)
  const [loading, setLoading] = useState(false as any)
  const t = translations[language]
  const isRTL = language === 'ar'

  // Initialize columns and load real tasks from API
  // @ts-ignore
  useEffect(( as any) => {
    const loadTasks = async () => {
      try {
        // TODO: Replace with real tasks API when available
        // For now, return empty tasks array instead of mock data
        setTasks([] as any)
      } catch (error) {
        (console as any).error('Error loading tasks:', error as any)
        setTasks([] as any)
      }
    }

    // @ts-ignore
    loadTasks( as any)

    // Initialize empty columns
    const initialColumns: Column[] = [
      {
        id: 'todo',
        title: (t as any).todo,
        tasks: [],
        color: 'from-gray-500 to-gray-600',
        limit: 5
      },
      {
        id: 'in_progress',
        title: (t as any).inProgress,
        tasks: [],
        color: 'from-blue-500 to-blue-600',
        limit: 3
      },
      {
        id: 'review',
        title: (t as any).review,
        tasks: [],
        color: 'from-yellow-500 to-yellow-600',
        limit: 2
      },
      {
        id: 'done',
        title: (t as any).done,
        tasks: [],
        color: 'from-green-500 to-green-600'
      }
    ]

    setColumns(initialColumns as any)
  // @ts-ignore
  }, [language, t])

  const getPriorityColor = (priority: string): void => {
    switch (priority) {
      case 'low': return 'bg-green-500'
      case 'medium': return 'bg-yellow-500'
      case 'high': return 'bg-orange-500'
      case 'urgent': return 'bg-red-500'
      default: return 'bg-gray-500'
    }
  }

  const getPriorityIcon = (priority: string): void => {
    switch (priority) {
      case 'urgent': return <AlertTriangle className="h-3 w-3" />
      case 'high': return <Flag className="h-3 w-3" />
      default: return <Circle className="h-3 w-3" />
    }
  }

  const getDueDateStatus = (dueDate: Date): void => {
    // @ts-ignore
    const now = new Date( as any)
    // @ts-ignore
    const diffTime = (dueDate as any).getTime( as any) - (now as any).getTime( as any)
    const diffDays = (Math as any).ceil(diffTime / (1000 * 60 * 60 * 24 as any))

    if (diffDays < 0) return { status: 'overdue', color: 'text-red-400' }
    if (diffDays === 0) return { status: 'today', color: 'text-yellow-400' }
    if (diffDays === 1) return { status: 'tomorrow', color: 'text-orange-400' }
    if (diffDays <= 7) return { status: 'thisWeek', color: 'text-blue-400' }
    return { status: '', color: 'text-white/60' }
  }

  const handleDragStart = (task: Task): void => {
    setDraggedTask(task as any)
  }

  // @ts-ignore
  const handleDragOver = (e: (React as any).DragEvent): void => {
    // @ts-ignore
    (e as any).preventDefault( as any)
  }

  // @ts-ignore
  const handleDrop = (e: (React as any).DragEvent, columnId: string): void => {
    // @ts-ignore
    (e as any).preventDefault( as any)
    if (!draggedTask) return

    setColumns(prev => (prev as any as any).map(column => {
      if ((column as any as any).id === columnId) {
        return {
          ...column,
          tasks: [...(column as any).tasks, { ...draggedTask, status: columnId as Task['status'] }]
        }
      } else {
        return {
          ...column,
          tasks: (column as any).tasks.filter(task => (task as any as any).id !== (draggedTask as any).id)
        }
      }
    }))

    setDraggedTask(null as any)

    // Show success message
    (toast as any).success(
      language === 'ar'
        ? `تم نقل المهمة "${(draggedTask as any as any).title}" بنجاح`
        : `Task "${(draggedTask as any).title}" moved successfully`
    )
  }

  // Add new task handler
  const handleAddTask = async (columnId?: string) => {
    try {
      setLoading(true as any)

      const newTask: Task = {
        // @ts-ignore
        id: `task-${(Date as any).now( as any)}`,
        title: language === 'ar' ? 'مهمة جديدة' : 'New Task',
        description: language === 'ar' ? 'وصف المهمة الجديدة' : 'New task description',
        assignee: {
          id: 'current-user',
          name: language === 'ar' ? 'المستخدم الحالي' : 'Current User'
        },
        priority: 'medium',
        // @ts-ignore
        dueDate: new Date((Date as any as any).now( as any) + 7 * 24 * 60 * 60 * 1000), // 7 days from now
        tags: [],
        comments: 0,
        attachments: 0,
        progress: 0,
        status: (columnId as Task['status']) || 'todo'
      }

      setColumns(prev => (prev as any as any).map(column => {
        if ((column as any as any).id === (columnId || 'todo')) {
          return {
            ...column,
            tasks: [...(column as any).tasks, newTask]
          }
        }
        return column
      }))

      (toast as any).success(
        language === 'ar'
          ? 'تم إضافة المهمة الجديدة بنجاح'
          : 'New task added successfully'
       // @ts-ignore
       as any)
    } catch (error) {
      (console as any).error('Add task error:', error as any)
      (toast as any).error(
        language === 'ar'
          ? 'فشل في إضافة المهمة'
          : 'Failed to add task'
       // @ts-ignore
       as any)
    } finally {
      setLoading(false as any)
    }
  }

  // Add new column handler
  const handleAddColumn = async () => {
    try {
      setLoading(true as any)

      const newColumn: Column = {
        // @ts-ignore
        id: `column-${(Date as any).now( as any)}`,
        title: language === 'ar' ? 'عمود جديد' : 'New Column',
        tasks: [],
        color: 'from-purple-500 to-purple-600',
        limit: 5
      }

      setColumns(prev => [...prev, newColumn] as any)

      (toast as any).success(
        language === 'ar'
          ? 'تم إضافة العمود الجديد بنجاح'
          : 'New column added successfully'
       // @ts-ignore
       as any)
    } catch (error) {
      (console as any).error('Add column error:', error as any)
      (toast as any).error(
        language === 'ar'
          ? 'فشل في إضافة العمود'
          : 'Failed to add column'
       // @ts-ignore
       as any)
    } finally {
      setLoading(false as any)
    }
  }

  return (
    <Card className="glass-card border-white/20">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-white text-xl flex items-center gap-2">
            <CheckCircle className="h-5 w-5" />
            {(t as any).kanbanBoard}
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button
              className="glass-button"
              // @ts-ignore
              onClick={() => handleAddTask( as any)}
              disabled={loading}
            >
              <Plus className="h-4 w-4 mr-2" />
              {(t as any).addTask}
            </Button>
            <Button
              variant="outline"
              className="glass-button"
              onClick={handleAddColumn}
              disabled={loading}
            >
              <Plus className="h-4 w-4 mr-2" />
              {(t as any).addColumn}
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          // @ts-ignore
          {(columns as any).map((column as any) => (
            <div
              key={(column as any).id}
              className="space-y-4"
              onDragOver={handleDragOver}
              onDrop={(e) => handleDrop(e, (column as any as any).id)}
            >
              {/* Column Header */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className={`w-3 h-3 rounded-full bg-gradient-to-r ${(column as any).color}`}></div>
                  <h3 className="text-white font-medium">{(column as any).title}</h3>
                  <Badge variant="outline" className="text-xs">
                    {(column as any).tasks.length}
                    {(column as any).limit && `/${(column as any).limit}`}
                  </Badge>
                </div>
                <Button variant="ghost" size="sm" className="glass-button p-1">
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </div>

              {/* Tasks */}
              <div className="space-y-3 min-h-[200px]">
                // @ts-ignore
                {(column as any).tasks.map((task as any) => {
                  // @ts-ignore
                  const dueDateStatus = getDueDateStatus((task as any as any).dueDate)

                  return (
                    <div
                      key={(task as any).id}
                      draggable
                      onDragStart={() => handleDragStart(task as any)}
                      className="p-4 glass-card border-white/10 hover:border-white/30 transition-all duration-300 cursor-move group"
                    >
                      {/* Task Header */}
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center gap-2">
                          <div className={`w-2 h-2 rounded-full ${getPriorityColor((task as any as any).priority)}`}></div>
                          {getPriorityIcon((task as any as any).priority)}
                        </div>
                        <Button variant="ghost" size="sm" className="opacity-0 group-hover:opacity-100 transition-opacity p-1">
                          <MoreVertical className="h-3 w-3" />
                        </Button>
                      </div>

                      {/* Task Content */}
                      <h4 className="text-white font-medium text-sm mb-2 line-clamp-2">
                        {(task as any).title}
                      </h4>
                      <p className="text-white/70 text-xs mb-3 line-clamp-2">
                        {(task as any).description}
                      </p>

                      {/* Progress Bar */}
                      {(task as any).progress > 0 && (
                        <div className="mb-3">
                          <div className="flex justify-between items-center mb-1">
                            <span className="text-white/60 text-xs">{(t as any).progress}</span>
                            <span className="text-white text-xs">{(task as any).progress}%</span>
                          </div>
                          <div className="w-full bg-white/20 rounded-full h-1">
                            <div
                              className={`bg-gradient-to-r ${(column as any).color} h-1 rounded-full transition-all duration-300`}
                              style={{ width: `${(task as any).progress}%` }}
                            ></div>
                          </div>
                        </div>
                      )}

                      {/* Tags */}
                      {(task as any).tags.length > 0 && (
                        <div className="flex flex-wrap gap-1 mb-3">
                          // @ts-ignore
                          {(task as any).tags.map((tag, index as any) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      )}

                      {/* Task Footer */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="flex items-center gap-1">
                            <MessageSquare className="h-3 w-3 text-white/60" />
                            <span className="text-xs text-white/60">{(task as any).comments}</span>
                          </div>
                          {(task as any).attachments > 0 && (
                            <div className="flex items-center gap-1">
                              <Paperclip className="h-3 w-3 text-white/60" />
                              <span className="text-xs text-white/60">{(task as any).attachments}</span>
                            </div>
                          )}
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="flex items-center gap-1">
                            <Calendar className="h-3 w-3 text-white/60" />
                            <span className={`text-xs ${(dueDateStatus as any).color}`}>
                              {(dueDateStatus as any).status && t[(dueDateStatus as any).status as keyof typeof t]}
                            </span>
                          </div>
                          <div className="w-6 h-6 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                            <span className="text-white text-xs font-medium">
                              {((task as any).assignee?.name || '').charAt(0 as any) || '?'}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  )
                // @ts-ignore
                })}

                {/* Add Task Button */}
                <Button
                  variant="ghost"
                  className="w-full glass-card border-white/10 border-dashed hover:border-white/30 transition-all duration-300 h-12"
                  onClick={() => handleAddTask((column as any as any).id)}
                  disabled={loading}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  {(t as any).addTask}
                </Button>
              </div>
            </div>
          // @ts-ignore
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
