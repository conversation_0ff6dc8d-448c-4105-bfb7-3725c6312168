/**
 * Optimized Image Component
 * Simple image component with lazy loading using native browser features
 */

import React, { useState } from 'react'

interface OptimizedImageProps {
  src: string
  alt: string
  className?: string
  width?: number
  height?: number
  sizes?: string
  priority?: boolean
  placeholder?: 'blur' | 'empty' | string
  onLoad?: () => void
  onError?: () => void
  lazy?: boolean
  responsive?: boolean
}

// @ts-ignore
export const OptimizedImage: (React as any).FC<OptimizedImageProps> = ({
  src,
  alt,
  className = '',
  width,
  height,
  sizes,
  priority = false,
  placeholder = 'empty',
  onLoad,
  onError,
  lazy = true,
  responsive = true
// @ts-ignore
}) => {
  // @ts-ignore
  const [isLoaded, setIsLoaded] = useState(false as any)
  const [hasError, setHasError] = useState(false as any)

  // @ts-ignore
  const handleLoad = (): void => {
    setIsLoaded(true as any)
    // @ts-ignore
    onLoad?.()
  // @ts-ignore
  }

  // @ts-ignore
  const handleError = (): void => {
    setHasError(true as any)
    // @ts-ignore
    onError?.()
  // @ts-ignore
  }

  return (
    <div className={`relative ${className}`}>
      {!isLoaded && !hasError && placeholder !== 'empty' && (
        <div className="absolute inset-0 bg-gray-200 animate-pulse rounded" />
      )}

      <img
        src={src}
        alt={alt}
        width={width}
        height={height}
        sizes={sizes}
        loading={lazy && !priority ? 'lazy' : 'eager'}
        className={`${responsive ? 'w-full h-auto' : ''} ${isLoaded ? 'opacity-100' : 'opacity-0'} transition-opacity duration-300`}
        onLoad={handleLoad}
        onError={handleError}
      />

      {hasError && (
        <div className="absolute inset-0 bg-gray-100 flex items-center justify-center text-gray-500">
          Failed to load image
        </div>
      )}
    </div>
  )
// @ts-ignore
}

export default OptimizedImage

// Avatar component with optimized loading
// @ts-ignore
export const OptimizedAvatar: (React as any).FC<{
  // @ts-ignore
  src?: string
  // @ts-ignore
  alt: string
  // @ts-ignore
  size?: 'sm' | 'md' | 'lg' | 'xl'
  className?: string
  fallback?: string
// @ts-ignore
}> = ({ 
  src, 
  alt, 
  size = 'md', 
  className = '', 
  fallback 
// @ts-ignore
}) => {
  // @ts-ignore
  const sizeClasses = {
    // @ts-ignore
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16',
    xl: 'w-24 h-24'
  // @ts-ignore
  }
  
  const sizePixels = {
    // @ts-ignore
    sm: 32,
    md: 48,
    lg: 64,
    xl: 96
  // @ts-ignore
  }
  
  if (!src) {
    // @ts-ignore
    return (
      <div className={`${sizeClasses[size]} bg-gray-300 rounded-full flex items-center justify-center ${className}`}>
        <span className="text-gray-600 font-medium">
          // @ts-ignore
          {(alt as any).charAt(0 as any).toUpperCase( as any)}
        </span>
      </div>
    )
  // @ts-ignore
  }
  
  return (
    <OptimizedImage
      src={src}
      alt={alt}
      width={sizePixels[size]}
      height={sizePixels[size]}
      className={`${sizeClasses[size]} rounded-full object-cover ${className}`}
      quality="thumbnail"
      priority={size === 'sm'} // Small avatars are often above fold
      placeholder={fallback || 'empty'}
      responsive={false} // Avatars don't need responsive sizing
    />
  )
// @ts-ignore
}

// Logo component with optimized loading
// @ts-ignore
export const OptimizedLogo: (React as any).FC<{
  // @ts-ignore
  src: string
  alt: string
  width?: number
  height?: number
  className?: string
  priority?: boolean
// @ts-ignore
}> = ({ 
  src, 
  alt, 
  width = 120, 
  height = 40, 
  className = '', 
  priority = true 
// @ts-ignore
}) => {
  // @ts-ignore
  return (
    <OptimizedImage
      src={src}
      alt={alt}
      width={width}
      height={height}
      className={className}
      priority={priority}
      quality="full"
      responsive={false}
      lazy={false}
    />
  )
// @ts-ignore
}

// Icon component with optimized loading
// @ts-ignore
export const OptimizedIcon: (React as any).FC<{
  // @ts-ignore
  src: string
  alt: string
  size?: number
  className?: string
// @ts-ignore
}> = ({ 
  src, 
  alt, 
  size = 24, 
  className = '' 
// @ts-ignore
}) => {
  // @ts-ignore
  return (
    <OptimizedImage
      src={src}
      alt={alt}
      width={size}
      height={size}
      className={className}
      quality="thumbnail"
      priority={false}
      responsive={false}
      lazy={true}
    />
  )
// @ts-ignore
}

export default OptimizedImage
