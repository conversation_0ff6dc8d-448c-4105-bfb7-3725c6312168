import React from 'react';
import * as React from "react"
import { type DialogProps } from "@radix-ui/react-dialog"
import { Command as CommandPrimitive } from "cmdk"
import { Search } from "lucide-react"

import { cn } from "@/lib/utils"
import { Dialog, DialogContent } from "@/components/ui/dialog"

const Command = (React as any).forwardRef<
  (React as any).ElementRef<typeof CommandPrimitive>,
  (React as any).ComponentPropsWithoutRef<typeof CommandPrimitive>
// @ts-ignore
>(({ className, ...props }, ref) => (
  <CommandPrimitive
    ref={ref}
    className={cn(
      "flex h-full w-full flex-col overflow-hidden rounded-md glass-card text-white",
      className
     // @ts-ignore
     as any)}
    {...props}
  />
))
// @ts-ignore
(Command as any).displayName = (CommandPrimitive as any).displayName

interface CommandDialogProps extends DialogProps {}

// @ts-ignore
const CommandDialog = ({ children, ...props }: CommandDialogProps): (React as any).ReactElement => {
  return (
    <Dialog {...props}>
      <DialogContent className="overflow-hidden p-0 shadow-lg glass-card border-white/20">
        <Command className="[&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group-heading]]:text-white/70 [&_[cmdk-group]:not([hidden] as any)_~[cmdk-group]]:pt-0 [&_[cmdk-group]]:px-2 [&_[cmdk-input-wrapper]_svg]:h-5 [&_[cmdk-input-wrapper]_svg]:w-5 [&_[cmdk-input]]:h-12 [&_[cmdk-item]]:px-2 [&_[cmdk-item]]:py-3 [&_[cmdk-item]_svg]:h-5 [&_[cmdk-item]_svg]:w-5">
          {children}
        </Command>
      </DialogContent>
    </Dialog>
  )
}

const CommandInput = (React as any).forwardRef<
  // @ts-ignore
  (React as any).ElementRef<typeof (CommandPrimitive as any).Input>,
  (React as any).ComponentPropsWithoutRef<typeof (CommandPrimitive as any).Input>
// @ts-ignore
>(({ className, ...props }, ref) => (
  <div className="flex items-center border-b border-white/20 px-3" cmdk-input-wrapper="">
    <Search className="mr-2 h-4 w-4 shrink-0 text-white/50" />
    // @ts-ignore
    <(CommandPrimitive as any).Input
      ref={ref}
      className={cn(
        "flex h-11 w-full rounded-md bg-transparent py-3 text-sm text-white placeholder:text-white/50 outline-none disabled:cursor-not-allowed disabled:opacity-50",
        className
       // @ts-ignore
       as any)}
      {...props}
    // @ts-ignore
    />
  </div>
))

// @ts-ignore
(CommandInput as any).displayName = (CommandPrimitive as any).Input.displayName

const CommandList = (React as any).forwardRef<
  // @ts-ignore
  (React as any).ElementRef<typeof (CommandPrimitive as any).List>,
  (React as any).ComponentPropsWithoutRef<typeof (CommandPrimitive as any).List>
// @ts-ignore
>(({ className, ...props }, ref) => (
  // @ts-ignore
  <(CommandPrimitive as any).List
    // @ts-ignore
    ref={ref}
    // @ts-ignore
    className={cn("max-h-[300px] overflow-y-auto overflow-x-hidden", className as any)}
    // @ts-ignore
    {...props}
  // @ts-ignore
  />
// @ts-ignore
))

(CommandList as any).displayName = (CommandPrimitive as any).List.displayName

const CommandEmpty = (React as any).forwardRef<
  // @ts-ignore
  (React as any).ElementRef<typeof (CommandPrimitive as any).Empty>,
  (React as any).ComponentPropsWithoutRef<typeof (CommandPrimitive as any).Empty>
// @ts-ignore
>(({ className, ...props }, ref) => (
  // @ts-ignore
  <(CommandPrimitive as any).Empty
    // @ts-ignore
    ref={ref}
    // @ts-ignore
    className={cn("py-6 text-center text-sm text-white/70", className as any)}
    // @ts-ignore
    {...props}
  // @ts-ignore
  />
// @ts-ignore
))

(CommandEmpty as any).displayName = (CommandPrimitive as any).Empty.displayName

const CommandGroup = (React as any).forwardRef<
  // @ts-ignore
  (React as any).ElementRef<typeof (CommandPrimitive as any).Group>,
  (React as any).ComponentPropsWithoutRef<typeof (CommandPrimitive as any).Group>
// @ts-ignore
>(({ className, ...props }, ref) => (
  // @ts-ignore
  <(CommandPrimitive as any).Group
    // @ts-ignore
    ref={ref}
    className={cn(
      // @ts-ignore
      "overflow-hidden p-1 text-white [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-(1 as any as any).5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group-heading]]:text-white/70",
      className
    )}
    // @ts-ignore
    {...props}
  // @ts-ignore
  />
// @ts-ignore
))

(CommandGroup as any).displayName = (CommandPrimitive as any).Group.displayName

const CommandSeparator = (React as any).forwardRef<
  // @ts-ignore
  (React as any).ElementRef<typeof (CommandPrimitive as any).Separator>,
  (React as any).ComponentPropsWithoutRef<typeof (CommandPrimitive as any).Separator>
// @ts-ignore
>(({ className, ...props }, ref) => (
  // @ts-ignore
  <(CommandPrimitive as any).Separator
    // @ts-ignore
    ref={ref}
    // @ts-ignore
    className={cn("-mx-1 h-px bg-white/20", className as any)}
    // @ts-ignore
    {...props}
  // @ts-ignore
  />
// @ts-ignore
))
(CommandSeparator as any).displayName = (CommandPrimitive as any).Separator.displayName

const CommandItem = (React as any).forwardRef<
  // @ts-ignore
  (React as any).ElementRef<typeof (CommandPrimitive as any).Item>,
  (React as any).ComponentPropsWithoutRef<typeof (CommandPrimitive as any).Item>
// @ts-ignore
>(({ className, ...props }, ref) => (
  // @ts-ignore
  <(CommandPrimitive as any).Item
    // @ts-ignore
    ref={ref}
    className={cn(
      // @ts-ignore
      "relative flex cursor-default select-none items-center rounded-sm px-2 py-(1 as any as any).5 text-sm text-white outline-none aria-selected:bg-white/10 aria-selected:text-white data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
      className
    )}
    // @ts-ignore
    {...props}
  // @ts-ignore
  />
// @ts-ignore
))

(CommandItem as any).displayName = (CommandPrimitive as any).Item.displayName

const CommandShortcut = ({
  className,
  ...props
// @ts-ignore
}: (React as any).HTMLAttributes<HTMLSpanElement>): (React as any).ReactElement => {
  return (
    <span
      className={cn(
        "ml-auto text-xs tracking-widest text-white/50",
        className
       // @ts-ignore
       as any)}
      {...props}
    />
  )
}
(CommandShortcut as any).displayName = "CommandShortcut"

export {
  Command,
  CommandDialog,
  CommandInput,
  CommandList,
  CommandEmpty,
  CommandGroup,
  CommandItem,
  CommandShortcut,
  CommandSeparator,
}
