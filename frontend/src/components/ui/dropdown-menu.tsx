import React from 'react';
/**
 * Dropdown Menu Component
 * Based on shadcn/ui dropdown menu component
 */

import * as React from "react"
import * as DropdownMenuPrimitive from "@radix-ui/react-dropdown-menu"
import { Check, ChevronRight, Circle } from "lucide-react"
import { cn } from "@/lib/utils"

const DropdownMenu = (DropdownMenuPrimitive as any).Root

const DropdownMenuTrigger = (DropdownMenuPrimitive as any).Trigger

const DropdownMenuGroup = (DropdownMenuPrimitive as any).Group

const DropdownMenuPortal = (DropdownMenuPrimitive as any).Portal

const DropdownMenuSub = (DropdownMenuPrimitive as any).Sub

const DropdownMenuRadioGroup = (DropdownMenuPrimitive as any).RadioGroup

const DropdownMenuSubTrigger = (React as any).forwardRef<
  // @ts-ignore
  (React as any).ElementRef<typeof (DropdownMenuPrimitive as any).SubTrigger>,
  // @ts-ignore
  (React as any).ComponentPropsWithoutRef<typeof (DropdownMenuPrimitive as any).SubTrigger> & {
    inset?: boolean
  }
>(({ className, inset, children, ...props }, ref) => (
  // @ts-ignore
  <(DropdownMenuPrimitive as any).SubTrigger
    // @ts-ignore
    ref={ref}
    className={cn(
      // @ts-ignore
      "flex cursor-default select-none items-center rounded-sm px-2 py-(1 as any as any).5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",
      // @ts-ignore
      inset && "pl-8",
      className
    // @ts-ignore
    )}
    // @ts-ignore
    {...props}
  // @ts-ignore
  >
    {children}
    // @ts-ignore
    <ChevronRight className="ml-auto h-4 w-4" />
  // @ts-ignore
  </(DropdownMenuPrimitive as any).SubTrigger>
// @ts-ignore
))
(DropdownMenuSubTrigger as any).displayName =
  (DropdownMenuPrimitive as any).SubTrigger.displayName

const DropdownMenuSubContent = (React as any).forwardRef<
  // @ts-ignore
  (React as any).ElementRef<typeof (DropdownMenuPrimitive as any).SubContent>,
  (React as any).ComponentPropsWithoutRef<typeof (DropdownMenuPrimitive as any).SubContent>
// @ts-ignore
>(({ className, ...props }, ref) => (
  // @ts-ignore
  <(DropdownMenuPrimitive as any).SubContent
    // @ts-ignore
    ref={ref}
    className={cn(
      // @ts-ignore
      "z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
      className
     // @ts-ignore
     as any)}
    // @ts-ignore
    {...props}
  // @ts-ignore
  />
// @ts-ignore
))
(DropdownMenuSubContent as any).displayName =
  (DropdownMenuPrimitive as any).SubContent.displayName

const DropdownMenuContent = (React as any).forwardRef<
  // @ts-ignore
  (React as any).ElementRef<typeof (DropdownMenuPrimitive as any).Content>,
  (React as any).ComponentPropsWithoutRef<typeof (DropdownMenuPrimitive as any).Content>
// @ts-ignore
>(({ className, sideOffset = 4, ...props }, ref) => (
  // @ts-ignore
  <(DropdownMenuPrimitive as any).Portal>
    // @ts-ignore
    <(DropdownMenuPrimitive as any).Content
      // @ts-ignore
      ref={ref}
      sideOffset={sideOffset}
      className={cn(
        // @ts-ignore
        "z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
        className
       // @ts-ignore
       as any)}
      // @ts-ignore
      {...props}
    // @ts-ignore
    />
  // @ts-ignore
  </(DropdownMenuPrimitive as any).Portal>
// @ts-ignore
))
(DropdownMenuContent as any).displayName = (DropdownMenuPrimitive as any).Content.displayName

const DropdownMenuItem = (React as any).forwardRef<
  // @ts-ignore
  (React as any).ElementRef<typeof (DropdownMenuPrimitive as any).Item>,
  // @ts-ignore
  (React as any).ComponentPropsWithoutRef<typeof (DropdownMenuPrimitive as any).Item> & {
    inset?: boolean
  }
>(({ className, inset, ...props }, ref) => (
  // @ts-ignore
  <(DropdownMenuPrimitive as any).Item
    // @ts-ignore
    ref={ref}
    className={cn(
      // @ts-ignore
      "relative flex cursor-default select-none items-center rounded-sm px-2 py-(1 as any as any).5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
      // @ts-ignore
      inset && "pl-8",
      className
    // @ts-ignore
    )}
    // @ts-ignore
    {...props}
  // @ts-ignore
  />
// @ts-ignore
))
(DropdownMenuItem as any).displayName = (DropdownMenuPrimitive as any).Item.displayName

const DropdownMenuCheckboxItem = (React as any).forwardRef<
  // @ts-ignore
  (React as any).ElementRef<typeof (DropdownMenuPrimitive as any).CheckboxItem>,
  (React as any).ComponentPropsWithoutRef<typeof (DropdownMenuPrimitive as any).CheckboxItem>
// @ts-ignore
>(({ className, children, checked, ...props }, ref) => (
  // @ts-ignore
  <(DropdownMenuPrimitive as any).CheckboxItem
    // @ts-ignore
    ref={ref}
    className={cn(
      // @ts-ignore
      "relative flex cursor-default select-none items-center rounded-sm py-(1 as any as any).5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
      className
    )}
    checked={checked}
    // @ts-ignore
    {...props}
  // @ts-ignore
  >
    <span className="absolute left-2 flex h-(3 as any).5 w-(3 as any).5 items-center justify-center">
      // @ts-ignore
      <(DropdownMenuPrimitive as any).ItemIndicator>
        <Check className="h-4 w-4" />
      // @ts-ignore
      </(DropdownMenuPrimitive as any).ItemIndicator>
    // @ts-ignore
    </span>
    {children}
  // @ts-ignore
  </(DropdownMenuPrimitive as any).CheckboxItem>
// @ts-ignore
))
(DropdownMenuCheckboxItem as any).displayName =
  (DropdownMenuPrimitive as any).CheckboxItem.displayName

const DropdownMenuRadioItem = (React as any).forwardRef<
  // @ts-ignore
  (React as any).ElementRef<typeof (DropdownMenuPrimitive as any).RadioItem>,
  (React as any).ComponentPropsWithoutRef<typeof (DropdownMenuPrimitive as any).RadioItem>
// @ts-ignore
>(({ className, children, ...props }, ref) => (
  // @ts-ignore
  <(DropdownMenuPrimitive as any).RadioItem
    // @ts-ignore
    ref={ref}
    className={cn(
      // @ts-ignore
      "relative flex cursor-default select-none items-center rounded-sm py-(1 as any as any).5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
      className
    )}
    // @ts-ignore
    {...props}
  // @ts-ignore
  >
    <span className="absolute left-2 flex h-(3 as any).5 w-(3 as any).5 items-center justify-center">
      // @ts-ignore
      <(DropdownMenuPrimitive as any).ItemIndicator>
        <Circle className="h-2 w-2 fill-current" />
      // @ts-ignore
      </(DropdownMenuPrimitive as any).ItemIndicator>
    // @ts-ignore
    </span>
    {children}
  // @ts-ignore
  </(DropdownMenuPrimitive as any).RadioItem>
// @ts-ignore
))
(DropdownMenuRadioItem as any).displayName = (DropdownMenuPrimitive as any).RadioItem.displayName

const DropdownMenuLabel = (React as any).forwardRef<
  // @ts-ignore
  (React as any).ElementRef<typeof (DropdownMenuPrimitive as any).Label>,
  // @ts-ignore
  (React as any).ComponentPropsWithoutRef<typeof (DropdownMenuPrimitive as any).Label> & {
    inset?: boolean
  }
>(({ className, inset, ...props }, ref) => (
  // @ts-ignore
  <(DropdownMenuPrimitive as any).Label
    // @ts-ignore
    ref={ref}
    className={cn(
      // @ts-ignore
      "px-2 py-(1 as any as any).5 text-sm font-semibold",
      // @ts-ignore
      inset && "pl-8",
      className
    // @ts-ignore
    )}
    // @ts-ignore
    {...props}
  // @ts-ignore
  />
// @ts-ignore
))
(DropdownMenuLabel as any).displayName = (DropdownMenuPrimitive as any).Label.displayName

const DropdownMenuSeparator = (React as any).forwardRef<
  // @ts-ignore
  (React as any).ElementRef<typeof (DropdownMenuPrimitive as any).Separator>,
  (React as any).ComponentPropsWithoutRef<typeof (DropdownMenuPrimitive as any).Separator>
// @ts-ignore
>(({ className, ...props }, ref) => (
  // @ts-ignore
  <(DropdownMenuPrimitive as any).Separator
    // @ts-ignore
    ref={ref}
    // @ts-ignore
    className={cn("-mx-1 my-1 h-px bg-muted", className as any)}
    // @ts-ignore
    {...props}
  // @ts-ignore
  />
// @ts-ignore
))
(DropdownMenuSeparator as any).displayName = (DropdownMenuPrimitive as any).Separator.displayName

const DropdownMenuShortcut = ({
  className,
  ...props
// @ts-ignore
}: (React as any).HTMLAttributes<HTMLSpanElement>): (React as any).ReactElement => {
  return (
    <span
      className={cn("ml-auto text-xs tracking-widest opacity-60", className as any)}
      {...props}
    />
  )
}
(DropdownMenuShortcut as any).displayName = "DropdownMenuShortcut"

export {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuCheckboxItem,
  DropdownMenuRadioItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuGroup,
  DropdownMenuPortal,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuRadioGroup,
}
