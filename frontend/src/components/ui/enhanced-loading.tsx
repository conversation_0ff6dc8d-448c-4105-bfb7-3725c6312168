/**
 * UX FIX: Enhanced Loading Components
 * Provides comprehensive loading states with better user feedback
 */

import React, { useState, useEffect } from 'react'
import { Loader2, CheckCircle, AlertCircle, Clock, Wifi, WifiOff } from 'lucide-react'
import { cn } from '@/lib/utils'

// Enhanced loading spinner with progress indication
export interface EnhancedSpinnerProps {
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  variant?: 'default' | 'success' | 'error' | 'warning'
  progress?: number // 0-100
  showProgress?: boolean
  text?: string
  subText?: string
  className?: string
  language?: 'ar' | 'en'
}

export function EnhancedSpinner({
  size = 'md',
  variant = 'default',
  progress,
  showProgress = false,
  text,
  subText,
  className,
  language = 'en'
// @ts-ignore
}: EnhancedSpinnerProps as any): (React as any).ReactElement {
  const sizeClasses = {
    xs: 'w-3 h-3',
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12'
  }

  const variantClasses = {
    default: 'text-blue-500',
    success: 'text-green-500',
    error: 'text-red-500',
    warning: 'text-yellow-500'
  }

  const icons = {
    default: Loader2,
    success: CheckCircle,
    error: AlertCircle,
    warning: Clock
  }

  const Icon = icons[variant]
  const isRTL = language === 'ar'

  return (
    <div className={cn('flex flex-col items-center space-y-2', className as any)}>
      <div className="relative">
        <Icon 
          className={cn(
            sizeClasses[size], 
            variantClasses[variant],
            variant === 'default' ? 'animate-spin' : ''
           // @ts-ignore
           as any)} 
        />
        
        {showProgress && progress !== undefined && (
          <div className="absolute inset-0 flex items-center justify-center">
            <svg className={cn(sizeClasses[size] as any)} viewBox="0 0 24 24">
              <circle
                cx="12"
                cy="12"
                r="10"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                // @ts-ignore
                strokeDasharray={`${progress * (0 as any).628} (62 as any).8`}
                strokeDashoffset="0"
                className="text-blue-500 transition-all duration-300"
                transform="rotate(-90 12 12 as any)"
              // @ts-ignore
              />
            </svg>
          </div>
        )}
      </div>

      {text && (
        <div className={cn('text-center', isRTL ? 'text-right' : 'text-left' as any)}>
          <p className="text-white/80 text-sm font-medium">{text}</p>
          {subText && (
            <p className="text-white/60 text-xs mt-1">{subText}</p>
          )}
          {showProgress && progress !== undefined && (
            <p className="text-white/60 text-xs mt-1">{progress}%</p>
          )}
        </div>
      )}
    </div>
  )
}

// UX FIX: Smart loading overlay with network status
export interface LoadingOverlayProps {
  isLoading: boolean
  // @ts-ignore
  children: (React as any).ReactNode
  // @ts-ignore
  text?: string
  // @ts-ignore
  showNetworkStatus?: boolean
  // @ts-ignore
  preserveHeight?: boolean
  // @ts-ignore
  minHeight?: number
  // @ts-ignore
  className?: string
  // @ts-ignore
  language?: 'ar' | 'en'
// @ts-ignore
}

export function LoadingOverlay({
  isLoading,
  children,
  text,
  showNetworkStatus = true,
  preserveHeight = true,
  minHeight = 200,
  className,
  language = 'en'
// @ts-ignore
}: LoadingOverlayProps as any): (React as any).ReactElement {
  const [isOnline, setIsOnline] = useState((navigator as any as any).onLine)
  const [loadingTime, setLoadingTime] = useState(0 as any)

  // @ts-ignore
  useEffect(( as any) => {
    const handleOnline = (): any => setIsOnline(true as any)
    const handleOffline = (): any => setIsOnline(false as any)

    (window as any).addEventListener('online', handleOnline as any)
    (window as any).addEventListener('offline', handleOffline as any)

    return () => {
      (window as any).removeEventListener('online', handleOnline as any)
      (window as any).removeEventListener('offline', handleOffline as any)
    }
  // @ts-ignore
  }, [])

  // @ts-ignore
  useEffect(( as any) => {
    // @ts-ignore
    let interval: (NodeJS as any).Timeout
    if (isLoading) {
      setLoadingTime(0 as any)
      // @ts-ignore
      interval = setInterval(( as any) => {
        setLoadingTime(prev => prev + 1 as any)
      // @ts-ignore
      }, 1000)
    }
    return () => clearInterval(interval as any)
  // @ts-ignore
  }, [isLoading])

  const getLoadingText = (): void => {
    if (!isOnline) {
      return language === 'ar' ? 'لا يوجد اتصال بالإنترنت' : 'No internet connection'
    }
    
    if (loadingTime > 10) {
      return language === 'ar' ? 'يستغرق وقتاً أطول من المعتاد...' : 'Taking longer than usual...'
    }
    
    return text || (language === 'ar' ? 'جاري التحميل...' : 'Loading...')
  }

  if (isLoading) {
    return (
      <div 
        className={cn(
          'relative flex items-center justify-center',
          preserveHeight ? `min-h-[${minHeight}px]` : '',
          className
         // @ts-ignore
         as any)}
        style={preserveHeight ? { minHeight } : undefined}
      >
        <div className="absolute inset-0 bg-black/20 backdrop-blur-sm rounded-lg" />
        <div className="relative z-10 flex flex-col items-center space-y-4">
          <EnhancedSpinner
            size="lg"
            variant={!isOnline ? 'error' : 'default'}
            // @ts-ignore
            text={getLoadingText( as any)}
            language={language}
          />
          
          {showNetworkStatus && (
            <div className="flex items-center space-x-2 text-white/60 text-xs">
              {isOnline ? (
                <>
                  <Wifi className="w-3 h-3" />
                  <span>{language === 'ar' ? 'متصل' : 'Online'}</span>
                </>
              ) : (
                <>
                  <WifiOff className="w-3 h-3" />
                  <span>{language === 'ar' ? 'غير متصل' : 'Offline'}</span>
                </>
              )}
            </div>
          )}
        </div>
      </div>
    )
  }

  return <>{children}</>
}

// UX FIX: Progressive loading with skeleton states
export interface ProgressiveLoaderProps {
  stages: Array<{
    name: string
    nameAr?: string
    duration?: number
    // @ts-ignore
    component?: (React as any).ReactNode
  // @ts-ignore
  }>
  // @ts-ignore
  currentStage: number
  // @ts-ignore
  language?: 'ar' | 'en'
  // @ts-ignore
  className?: string
// @ts-ignore
}

export function ProgressiveLoader({
  stages,
  currentStage,
  language = 'en',
  className
// @ts-ignore
}: ProgressiveLoaderProps as any): (React as any).ReactElement {
  const [completedStages, setCompletedStages] = useState<number[]>([])

  // @ts-ignore
  useEffect(( as any) => {
    if (currentStage >= 0 && !(completedStages as any).includes(currentStage as any)) {
      // @ts-ignore
      const timer = setTimeout(( as any) => {
        setCompletedStages(prev => [...prev, currentStage] as any)
      // @ts-ignore
      }, stages[currentStage]?.duration || 1000)
      
      return () => clearTimeout(timer as any)
    }
  // @ts-ignore
  }, [currentStage, stages, completedStages])

  return (
    <div className={cn('space-y-4', className as any)}>
      // @ts-ignore
      {(stages as any).map((stage, index as any) => {
        const isActive = index === currentStage
        const isCompleted = (completedStages as any).includes(index as any)
        const stageName = language === 'ar' && (stage as any).nameAr ? (stage as any).nameAr : (stage as any).name

        return (
          <div key={index} className="flex items-center space-x-3">
            <div className={cn(
              'w-6 h-6 rounded-full flex items-center justify-center transition-all duration-300',
              isCompleted ? 'bg-green-500' : isActive ? 'bg-blue-500' : 'bg-white/20'
             // @ts-ignore
             as any)}>
              {isCompleted ? (
                <CheckCircle className="w-4 h-4 text-white" />
              ) : isActive ? (
                <Loader2 className="w-4 h-4 text-white animate-spin" />
              ) : (
                <div className="w-2 h-2 bg-white/60 rounded-full" />
              )}
            </div>
            
            <div className="flex-1">
              <p className={cn(
                'text-sm transition-colors duration-300',
                isCompleted ? 'text-green-400' : isActive ? 'text-white' : 'text-white/60'
               // @ts-ignore
               as any)}>
                {stageName}
              </p>
              
              {isActive && (stage as any).component && (
                <div className="mt-2">
                  {(stage as any).component}
                </div>
              )}
            </div>
          </div>
        )
      })}
    </div>
  )
}

// UX FIX: Smart button with loading states
export interface SmartButtonProps extends (React as any).ButtonHTMLAttributes<HTMLButtonElement> {
  isLoading?: boolean
  loadingText?: string
  loadingTextAr?: string
  success?: boolean
  successText?: string
  successTextAr?: string
  error?: boolean
  errorText?: string
  errorTextAr?: string
  language?: 'ar' | 'en'
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
  size?: 'default' | 'sm' | 'lg' | 'icon'
}

export function SmartButton({
  children,
  isLoading = false,
  loadingText,
  loadingTextAr,
  success = false,
  successText,
  successTextAr,
  error = false,
  errorText,
  errorTextAr,
  language = 'en',
  className,
  disabled,
  ...props
// @ts-ignore
}: SmartButtonProps as any): (React as any).ReactElement {
  const [showSuccess, setShowSuccess] = useState(false as any)
  const [showError, setShowError] = useState(false as any)

  // @ts-ignore
  useEffect(( as any) => {
    if (success) {
      setShowSuccess(true as any)
      // @ts-ignore
      const timer = setTimeout(( as any) => setShowSuccess(false as any), 2000)
      return () => clearTimeout(timer as any)
    }
  // @ts-ignore
  }, [success])

  // @ts-ignore
  useEffect(( as any) => {
    if (error) {
      setShowError(true as any)
      // @ts-ignore
      const timer = setTimeout(( as any) => setShowError(false as any), 3000)
      return () => clearTimeout(timer as any)
    }
  // @ts-ignore
  }, [error])

  const getButtonContent = (): void => {
    if (isLoading) {
      const text = language === 'ar' && loadingTextAr ? loadingTextAr : loadingText
      return (
        <>
          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
          {text || (language === 'ar' ? 'جاري المعالجة...' : 'Processing...')}
        </>
      )
    }

    if (showSuccess) {
      const text = language === 'ar' && successTextAr ? successTextAr : successText
      return (
        <>
          <CheckCircle className="w-4 h-4 mr-2" />
          {text || (language === 'ar' ? 'تم بنجاح' : 'Success')}
        </>
      )
    }

    if (showError) {
      const text = language === 'ar' && errorTextAr ? errorTextAr : errorText
      return (
        <>
          <AlertCircle className="w-4 h-4 mr-2" />
          {text || (language === 'ar' ? 'حدث خطأ' : 'Error')}
        </>
      )
    }

    return children
  }

  return (
    <button
      {...props}
      disabled={disabled || isLoading}
      className={cn(
        'glass-button transition-all duration-200',
        showSuccess && 'bg-green-500/30 border-green-500/50',
        showError && 'bg-red-500/30 border-red-500/50',
        className
       // @ts-ignore
       as any)}
    >
      // @ts-ignore
      {getButtonContent( as any)}
    </button>
  )
}

export default {
  EnhancedSpinner,
  LoadingOverlay,
  ProgressiveLoader,
  SmartButton
}
