import React from 'react';
import * as React from "react"

import { cn } from "@/lib/utils"

interface SkeletonProps {
  // TODO: Define proper prop types
  [key: string]: any;
}


function Skeleton({
  className,
  ...props
// @ts-ignore
}: (React as any as any).HTMLAttributes<HTMLDivElement>): (React as any).ReactElement {
  return (
    <div
      className={cn("animate-pulse rounded-md bg-muted", className as any)}
      {...props}
    />
  )
}

export { Skeleton }
