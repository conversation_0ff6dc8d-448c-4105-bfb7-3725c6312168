import React from 'react';
/**
 * Workflow Automation Component with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  Workflow,
  Play,
  Edit,
  Trash2,
  Clock,
  Zap,
  Users,
  FileText,
  Database,
  GitBranch,
  Eye
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { workflowService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface WorkflowAutomationProps {
  language: 'ar' | 'en'
}

interface WorkflowRule {
  id: number
  name: string
  nameAr: string
  description: string
  descriptionAr: string
  triggerType: string
  triggerCondition: string
  triggerValue: string
  actions: string // JSON string of actions array
  status: 'active' | 'inactive' | 'draft'
  category: string
  categoryAr: string
  lastRun?: string
  runCount: number
  createdDate: string
  updatedDate: string
}

const translations = {
  ar: {
    workflows: 'أتمتة سير العمل',
    addWorkflow: 'إضافة سير عمل',
    editWorkflow: 'تعديل سير العمل',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف سير العمل هذا؟',
    searchPlaceholder: 'البحث في سير العمل...',
    name: 'الاسم',
    description: 'الوصف',
    triggerType: 'نوع المحفز',
    triggerCondition: 'شرط المحفز',
    triggerValue: 'قيمة المحفز',
    actions: 'الإجراءات',
    status: 'الحالة',
    category: 'الفئة',
    lastRun: 'آخر تشغيل',
    runCount: 'عدد مرات التشغيل',
    createdDate: 'تاريخ الإنشاء',
    updatedDate: 'تاريخ التحديث',
    active: 'نشط',
    inactive: 'غير نشط',
    draft: 'مسودة',
    triggers: {
      newEmployee: 'موظف جديد',
      leaveRequest: 'طلب إجازة',
      lowStock: 'مخزون منخفض',
      projectDeadline: 'موعد تسليم مشروع',
      salesTarget: 'هدف مبيعات',
      attendanceLate: 'تأخير في الحضور'
    },
    categories: {
      hr: 'الموارد البشرية',
      sales: 'المبيعات',
      inventory: 'المخزون',
      projects: 'المشاريع',
      finance: 'المالية'
    }
  },
  en: {
    workflows: 'Workflow Automation',
    addWorkflow: 'Add Workflow',
    editWorkflow: 'Edit Workflow',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this workflow?',
    searchPlaceholder: 'Search workflows...',
    name: 'Name',
    description: 'Description',
    triggerType: 'Trigger Type',
    triggerCondition: 'Trigger Condition',
    triggerValue: 'Trigger Value',
    actions: 'Actions',
    status: 'Status',
    category: 'Category',
    lastRun: 'Last Run',
    runCount: 'Run Count',
    createdDate: 'Created Date',
    updatedDate: 'Updated Date',
    active: 'Active',
    inactive: 'Inactive',
    draft: 'Draft',
    triggers: {
      newEmployee: 'New Employee',
      leaveRequest: 'Leave Request',
      lowStock: 'Low Stock',
      projectDeadline: 'Project Deadline',
      salesTarget: 'Sales Target',
      attendanceLate: 'Late Attendance'
    },
    categories: {
      hr: 'Human Resources',
      sales: 'Sales',
      inventory: 'Inventory',
      projects: 'Projects',
      finance: 'Finance'
    }
  }
}

// @ts-ignore
export default function WorkflowAutomation({ language }: WorkflowAutomationProps as any): (React as any).ReactElement {
  const [showModal, setShowModal] = useState(false as any)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: workflows,
    selectedItem,
    loading,
    creating,
    updating,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<WorkflowRule>({
    service: workflowService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getStatusColor = (status: string): void => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 border-green-200'
      case 'inactive': return 'bg-red-100 text-red-800 border-red-200'
      case 'draft': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getCategoryIcon = (category: string): void => {
    switch (category) {
      case 'hr': return <Users className="h-4 w-4 text-blue-400" />
      case 'sales': return <Zap className="h-4 w-4 text-green-400" />
      case 'inventory': return <Database className="h-4 w-4 text-purple-400" />
      case 'projects': return <GitBranch className="h-4 w-4 text-orange-400" />
      case 'finance': return <FileText className="h-4 w-4 text-red-400" />
      default: return <Workflow className="h-4 w-4 text-gray-400" />
    }
  }

  // Table columns configuration
  const columns: TableColumn<WorkflowRule>[] = [
    {
      key: 'name',
      label: (t as any).name,
      sortable: true,
      render: (item: WorkflowRule) => (
        <div className="flex items-center gap-2">
          {getCategoryIcon((item as any as any).category)}
          <div>
            <div className="font-medium text-white">
              {language === 'ar' ? (item as any).nameAr : (item as any).name}
            </div>
            <div className="text-sm text-white/60">
              {language === 'ar' ? (item as any).descriptionAr?.substring(0, 50 as any) + '...' : (item as any).description?.substring(0, 50 as any) + '...'}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'category',
      label: (t as any).category,
      render: (item: WorkflowRule) => (
        <span className="text-white/80">
          {language === 'ar' ? (item as any).categoryAr : (item as any).category}
        </span>
      )
    },
    {
      key: 'triggerType',
      label: (t as any).triggerType,
      render: (item: WorkflowRule) => (
        <div className="flex items-center gap-1">
          <Zap className="h-3 w-3 text-yellow-400" />
          <span className="text-white/80">
            // @ts-ignore
            {(t as any).triggers[(item as any).triggerType as keyof typeof (t as any).triggers] || (item as any).triggerType}
          </span>
        </div>
      )
    },
    {
      key: 'status',
      label: (t as any).status,
      sortable: true,
      render: (item: WorkflowRule) => (
        <Badge className={getStatusColor((item as any as any).status)}>
          {t[(item as any).status] as string}
        </Badge>
      )
    },
    {
      key: 'runCount',
      label: (t as any).runCount,
      sortable: true,
      render: (item: WorkflowRule) => (
        <div className="flex items-center gap-1">
          <Play className="h-3 w-3 text-green-400" />
          <span className="text-white font-medium">{(item as any).runCount}</span>
        </div>
      )
    },
    {
      key: 'lastRun',
      label: (t as any).lastRun,
      sortable: true,
      render: (item: WorkflowRule) => (
        (item as any).lastRun ? (
          <div className="flex items-center gap-1">
            <Clock className="h-3 w-3 text-blue-400" />
            <span className="text-white/80">{(item as any).lastRun}</span>
          </div>
        ) : (
          <span className="text-white/50">Never</span>
        )
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction[] = [
    {
      label: (t as any).view,
      icon: Eye,
      onClick: (item: Record<string, unknown>) => {
        selectItem(item as unknown as WorkflowRule as any)
        setModalMode('view' as any)
        setShowModal(true as any)
      },
      variant: 'ghost'
    },
    {
      label: (t as any).edit,
      icon: Edit,
      onClick: (item: Record<string, unknown>) => {
        selectItem(item as unknown as WorkflowRule as any)
        setModalMode('edit' as any)
        setShowModal(true as any)
      },
      variant: 'ghost'
    },
    {
      label: (t as any).delete,
      icon: Trash2,
      onClick: (item: Record<string, unknown>) => {
        if ((window as any).confirm((t as any as any).confirmDelete)) {
          deleteItem((item as unknown as WorkflowRule as any).id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'status',
      label: (t as any).status,
      options: [
        { label: (t as any).active, value: 'active' },
        { label: (t as any).inactive, value: 'inactive' },
        { label: (t as any).draft, value: 'draft' }
      ]
    },
    {
      key: 'category',
      label: (t as any).category,
      options: [
        { label: (t as any).categories.hr, value: 'hr' },
        { label: (t as any).categories.sales, value: 'sales' },
        { label: (t as any).categories.inventory, value: 'inventory' },
        { label: (t as any).categories.projects, value: 'projects' },
        { label: (t as any).categories.finance, value: 'finance' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'name',
      label: (t as any).name,
      type: 'text',
      required: true
    },
    {
      name: 'nameAr',
      label: (t as any).name + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'description',
      label: (t as any).description,
      type: 'textarea',
      required: true
    },
    {
      name: 'descriptionAr',
      label: (t as any).description + ' (عربي)',
      type: 'textarea',
      required: true
    },
    {
      name: 'category',
      label: (t as any).category,
      type: 'text',
      required: true
    },
    {
      name: 'categoryAr',
      label: (t as any).category + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'triggerType',
      label: (t as any).triggerType,
      type: 'select',
      required: true,
      options: [
        { label: (t as any).triggers.newEmployee, value: 'newEmployee' },
        { label: (t as any).triggers.leaveRequest, value: 'leaveRequest' },
        { label: (t as any).triggers.lowStock, value: 'lowStock' },
        { label: (t as any).triggers.projectDeadline, value: 'projectDeadline' },
        { label: (t as any).triggers.salesTarget, value: 'salesTarget' },
        { label: (t as any).triggers.attendanceLate, value: 'attendanceLate' }
      ]
    },
    {
      name: 'triggerCondition',
      label: (t as any).triggerCondition,
      type: 'text',
      required: true
    },
    {
      name: 'triggerValue',
      label: (t as any).triggerValue,
      type: 'text',
      required: true
    },
    {
      name: 'actions',
      label: (t as any).actions,
      type: 'textarea',
      placeholder: 'JSON array of actions'
    },
    {
      name: 'status',
      label: (t as any).status,
      type: 'select',
      required: true,
      options: [
        { label: (t as any).active, value: 'active' },
        { label: (t as any).inactive, value: 'inactive' },
        { label: (t as any).draft, value: 'draft' }
      ]
    },
    {
      name: 'runCount',
      label: (t as any).runCount,
      type: 'number',
      min: 0
    },
    {
      name: 'lastRun',
      label: (t as any).lastRun,
      type: 'datetime-local'
    }
  ]

  // Event handlers
  const handleCreate = (): void => {
    setModalMode('create' as any)
    setShowModal(true as any)
  }

  const handleModalClose = (): void => {
    setShowModal(false as any)
    selectItem(null as any)
    // @ts-ignore
    clearError( as any)
  }

  const handleSave = async (data: Partial<WorkflowRule>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data as any)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem((selectedItem as any as any).id, data)
      }
      setShowModal(false as any)
    } catch (error) {
      (console as any).error('Save error:', error as any)
    }
  }

  const handleExport = async () => {
    try {
      await exportData('csv' as any)
    } catch (error) {
      (console as any).error('Export error:', error as any)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={(t as any).workflows}
        data={workflows}
        columns={columns}
        actions={actions as any}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={(t as any).searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? (t as any).addWorkflow : modalMode === 'edit' ? (t as any).editWorkflow : (t as any).view}
        fields={formFields}
        initialData={selectedItem ? {
          name: (selectedItem as any).name,
          nameAr: (selectedItem as any).nameAr,
          description: (selectedItem as any).description,
          descriptionAr: (selectedItem as any).descriptionAr,
          triggerType: (selectedItem as any).triggerType,
          triggerCondition: (selectedItem as any).triggerCondition,
          triggerValue: (selectedItem as any).triggerValue,
          actions: (selectedItem as any).actions,
          status: (selectedItem as any).status,
          category: (selectedItem as any).category
        } : undefined}
        language={language}
        loading={creating || updating}
      />
    </div>
  )

}
