import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'

export interface User {
  id: number
  username: string
  email: string
  firstName: string
  lastName: string
  avatar?: string
  role: UserRole
  department: string
  permissions: string[]
  preferences: UserPreferences
  lastLogin: string
  isActive: boolean
}

export interface UserRole {
  id: number
  name: string
  nameAr: string
  level: number // 1=Super Admin, 2=Admin, 3=Manager, 4=Employee, 5=Viewer
  permissions: Permission[]
  dashboardConfig: DashboardConfig
}

export interface Permission {
  module: string
  actions: string[] // ['create', 'read', 'update', 'delete', 'approve']
}

export interface UserPreferences {
  language: 'ar' | 'en'
  theme: 'dark' | 'light'
  timezone: string
  notifications: NotificationSettings
  dashboardLayout: string[]
}

export interface NotificationSettings {
  email: boolean
  push: boolean
  sms: boolean
  categories: string[]
}

export interface DashboardConfig {
  layout: string
  widgets: string[]
  defaultRoute: string
  allowedRoutes: string[]
  menuItems: MenuItem[]
}

export interface MenuItem {
  id: string
  name: string
  nameAr: string
  icon: string
  route?: string
  children?: MenuItem[]
  permissions?: string[]
}

interface AuthContextType {
  user: User | null
  login: (credentials: LoginCredentials) => Promise<boolean>
  logout: () => void
  hasPermission: (module: string, action: string) => boolean
  canAccessRoute: (route: string) => boolean
  isLoading: boolean
  error: string | null
}

interface LoginCredentials {
  username: string
  password: string
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

interface AuthProviderProps {
  children: ReactNode
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Mock roles configuration
  const roles: UserRole[] = [
    {
      id: 1,
      name: 'Super Admin',
      nameAr: 'مدير النظام الرئيسي',
      level: 1,
      permissions: [
        { module: '*', actions: ['*'] } // Full access
      ],
      dashboardConfig: {
        layout: 'admin',
        widgets: ['system-health', 'user-analytics', 'security-logs', 'performance-metrics'],
        defaultRoute: '/admin/dashboard',
        allowedRoutes: ['*'],
        menuItems: [] // Will be populated
      }
    },
    {
      id: 2,
      name: 'HR Manager',
      nameAr: 'مدير الموارد البشرية',
      level: 3,
      permissions: [
        { module: 'hr', actions: ['create', 'read', 'update', 'delete', 'approve'] },
        { module: 'employees', actions: ['create', 'read', 'update', 'delete'] },
        { module: 'attendance', actions: ['read', 'update', 'approve'] },
        { module: 'payroll', actions: ['read', 'update', 'approve'] },
        { module: 'reports', actions: ['read', 'create'] }
      ],
      dashboardConfig: {
        layout: 'hr',
        widgets: ['employee-stats', 'leave-requests', 'attendance-summary', 'hr-metrics'],
        defaultRoute: '/hr/dashboard',
        allowedRoutes: ['/hr/*', '/employees', '/reports/hr'],
        menuItems: []
      }
    },
    {
      id: 3,
      name: 'Finance Manager',
      nameAr: 'مدير المالية',
      level: 3,
      permissions: [
        { module: 'finance', actions: ['create', 'read', 'update', 'delete', 'approve'] },
        { module: 'budgets', actions: ['create', 'read', 'update', 'delete'] },
        { module: 'expenses', actions: ['read', 'update', 'approve'] },
        { module: 'reports', actions: ['read', 'create'] }
      ],
      dashboardConfig: {
        layout: 'finance',
        widgets: ['budget-overview', 'expense-summary', 'financial-metrics', 'cash-flow'],
        defaultRoute: '/finance/dashboard',
        allowedRoutes: ['/finance/*', '/reports/finance'],
        menuItems: []
      }
    },
    {
      id: 4,
      name: 'Project Manager',
      nameAr: 'مدير المشاريع',
      level: 3,
      permissions: [
        { module: 'projects', actions: ['create', 'read', 'update', 'delete'] },
        { module: 'tasks', actions: ['create', 'read', 'update', 'delete'] },
        { module: 'team', actions: ['read', 'update'] },
        { module: 'reports', actions: ['read', 'create'] }
      ],
      dashboardConfig: {
        layout: 'project',
        widgets: ['project-overview', 'task-summary', 'team-performance', 'project-timeline'],
        defaultRoute: '/projects/dashboard',
        allowedRoutes: ['/projects/*', '/tasks/*', '/reports/projects'],
        menuItems: []
      }
    },
    {
      id: 5,
      name: 'Employee',
      nameAr: 'موظف',
      level: 4,
      permissions: [
        { module: 'profile', actions: ['read', 'update'] },
        { module: 'attendance', actions: ['read', 'create'] },
        { module: 'leave', actions: ['create', 'read'] },
        { module: 'expenses', actions: ['create', 'read'] },
        { module: 'tasks', actions: ['read', 'update'] },
        { module: 'messages', actions: ['create', 'read', 'update'] }
      ],
      dashboardConfig: {
        layout: 'employee',
        widgets: ['my-tasks', 'my-attendance', 'my-leave-balance', 'team-updates'],
        defaultRoute: '/employee/dashboard',
        allowedRoutes: ['/employee/*', '/profile', '/tasks/my-tasks', '/messages'],
        menuItems: []
      }
    },
    {
      id: 6,
      name: 'Department Head',
      nameAr: 'رئيس القسم',
      level: 3,
      permissions: [
        { module: 'department', actions: ['read', 'update'] },
        { module: 'team', actions: ['read', 'update', 'approve'] },
        { module: 'projects', actions: ['read', 'update'] },
        { module: 'reports', actions: ['read', 'create'] },
        { module: 'budget', actions: ['read', 'update'] }
      ],
      dashboardConfig: {
        layout: 'department',
        widgets: ['department-overview', 'team-performance', 'budget-status', 'pending-approvals'],
        defaultRoute: '/department/dashboard',
        allowedRoutes: ['/department/*', '/team/*', '/projects/*', '/reports/department'],
        menuItems: []
      }
    }
  ]

  // Mock users
  const mockUsers: User[] = [
    {
      id: 1,
      username: 'admin',
      email: '<EMAIL>',
      firstName: 'أحمد',
      lastName: 'المدير',
      role: roles[0],
      department: 'إدارة النظام',
      permissions: ['*'],
      preferences: {
        language: 'ar',
        theme: 'dark',
        timezone: 'Asia/Riyadh',
        notifications: {
          email: true,
          push: true,
          sms: false,
          categories: ['all']
        },
        dashboardLayout: ['system-health', 'user-analytics']
      },
      lastLogin: '2024-01-20T10:30:00Z',
      isActive: true
    },
    {
      id: 2,
      username: 'hr.manager',
      email: '<EMAIL>',
      firstName: 'فاطمة',
      lastName: 'علي',
      role: roles[1],
      department: 'الموارد البشرية',
      permissions: ['hr.*', 'employees.*'],
      preferences: {
        language: 'ar',
        theme: 'dark',
        timezone: 'Asia/Riyadh',
        notifications: {
          email: true,
          push: true,
          sms: true,
          categories: ['hr', 'employees']
        },
        dashboardLayout: ['employee-stats', 'leave-requests']
      },
      lastLogin: '2024-01-20T09:15:00Z',
      isActive: true
    },
    {
      id: 3,
      username: 'employee',
      email: '<EMAIL>',
      firstName: 'محمد',
      lastName: 'حسن',
      role: roles[4],
      department: 'تقنية المعلومات',
      permissions: ['profile.*', 'tasks.read'],
      preferences: {
        language: 'ar',
        theme: 'dark',
        timezone: 'Asia/Riyadh',
        notifications: {
          email: true,
          push: true,
          sms: false,
          categories: ['tasks', 'messages']
        },
        dashboardLayout: ['my-tasks', 'my-attendance']
      },
      lastLogin: '2024-01-20T08:45:00Z',
      isActive: true
    }
  ]

  useEffect(() => {
    // Check for existing session
    const savedUser = localStorage.getItem('user')
    if (savedUser) {
      try {
        const userData = JSON.parse(savedUser)
        setUser(userData)
      } catch (error) {
        console.error('Error parsing saved user data:', error)
        localStorage.removeItem('user')
      }
    }
    setIsLoading(false)
  }, [])

  const login = async (credentials: LoginCredentials): Promise<boolean> => {
    setIsLoading(true)
    setError(null)

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      const foundUser = mockUsers.find(u => u.username === credentials.username)
      
      if (foundUser && credentials.password === 'password') {
        setUser(foundUser)
        localStorage.setItem('user', JSON.stringify(foundUser))
        setIsLoading(false)
        return true
      } else {
        setError('Invalid credentials')
        setIsLoading(false)
        return false
      }
    } catch (error) {
      setError('Login failed')
      setIsLoading(false)
      return false
    }
  }

  const logout = () => {
    setUser(null)
    localStorage.removeItem('user')
  }

  const hasPermission = (module: string, action: string): boolean => {
    if (!user) return false
    
    // Super admin has all permissions
    if (user.role.level === 1) return true
    
    return user.role.permissions.some(permission => {
      const moduleMatch = permission.module === '*' || permission.module === module
      const actionMatch = permission.actions.includes('*') || permission.actions.includes(action)
      return moduleMatch && actionMatch
    })
  }

  const canAccessRoute = (route: string): boolean => {
    if (!user) return false
    
    // Super admin can access all routes
    if (user.role.level === 1) return true
    
    const allowedRoutes = user.role.dashboardConfig.allowedRoutes
    
    return allowedRoutes.some(allowedRoute => {
      if (allowedRoute === '*') return true
      if (allowedRoute.endsWith('/*')) {
        const baseRoute = allowedRoute.slice(0, -2)
        return route.startsWith(baseRoute)
      }
      return route === allowedRoute
    })
  }

  const value: AuthContextType = {
    user,
    login,
    logout,
    hasPermission,
    canAccessRoute,
    isLoading,
    error
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
