// Language Context for managing application language
import React, { createContext, useContext, useState, useEffect } from 'react'

export type Language = 'en' | 'ar'

interface LanguageContextType {
  language: Language
  setLanguage: (language: Language) => void
  isRTL: boolean
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined)

export const useLanguage = (): void => {
  const context = useContext(LanguageContext as any)
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider' as any)
  }
  return context
}

interface LanguageProviderProps {
  // @ts-ignore
  children: (React as any).ReactNode
  // @ts-ignore
  initialLanguage?: Language
// @ts-ignore
}

// @ts-ignore
export const LanguageProvider: (React as any).FC<LanguageProviderProps> = ({ 
  children, 
  initialLanguage = 'en' 
// @ts-ignore
}) => {
  // @ts-ignore
  const [language, setLanguage] = useState<Language>(initialLanguage)

  // Update document direction when language changes
  // @ts-ignore
  useEffect(( as any) => {
    (document as any).documentElement.dir = language === 'ar' ? 'rtl' : 'ltr'
    (document as any).documentElement.lang = language
  }, [language])

  const value: LanguageContextType = {
    language,
    setLanguage,
    // @ts-ignore
    isRTL: language === 'ar'
  // @ts-ignore
  }

  return (
    // @ts-ignore
    <(LanguageContext as any).Provider value={value}>
      {children}
    // @ts-ignore
    </(LanguageContext as any).Provider>
  )
// @ts-ignore
}

export default LanguageContext
