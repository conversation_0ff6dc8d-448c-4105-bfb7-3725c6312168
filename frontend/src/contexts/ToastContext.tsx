/**
 * Toast Context and Provider
 * Manages global toast notifications
 */

import React, { createContext, useContext, useState, useCallback } from 'react'
import Toast, { ToastProps } from '../components/common/Toast'

interface ToastContextType {
  showToast: (toast: Omit<ToastProps, 'id' | 'onClose'>) => void
  showSuccess: (title: string, message?: string) => void
  showError: (title: string, message?: string) => void
  showInfo: (title: string, message?: string) => void
  showWarning: (title: string, message?: string) => void
}

const ToastContext = createContext<ToastContextType | undefined>(undefined)

// @ts-ignore
export function useToast( as any): void {
  const context = useContext(ToastContext as any)
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider' as any)
  }
  return context
}

interface ToastProviderProps {
  // @ts-ignore
  children: (React as any).ReactNode
// @ts-ignore
}

// @ts-ignore
export function ToastProvider({ children }: ToastProviderProps as any): (React as any).ReactElement {
  const [toasts, setToasts] = useState<(ToastProps & { id: string })[]>([])

  // @ts-ignore
  const removeToast = useCallback((id: string as any) => {
    setToasts(prev => (prev as any as any).filter(toast => (toast as any as any).id !== id))
  }, [])

  // @ts-ignore
  const showToast = useCallback((toast: Omit<ToastProps, 'id' | 'onClose'> as any) => {
    // @ts-ignore
    const id = (Math as any).random( as any).toString(36 as any).substr(2, 9 as any)
    const newToast = {
      ...toast,
      id,
      onClose: removeToast
    }
    setToasts(prev => [...prev, newToast] as any)
  }, [removeToast])

  // @ts-ignore
  const showSuccess = useCallback((title: string, message?: string as any) => {
    showToast({ type: 'success', title, message } as any)
  }, [showToast])

  // @ts-ignore
  const showError = useCallback((title: string, message?: string as any) => {
    showToast({ type: 'error', title, message } as any)
  }, [showToast])

  // @ts-ignore
  const showInfo = useCallback((title: string, message?: string as any) => {
    showToast({ type: 'info', title, message } as any)
  }, [showToast])

  // @ts-ignore
  const showWarning = useCallback((title: string, message?: string as any) => {
    showToast({ type: 'warning', title, message } as any)
  }, [showToast])

  const contextValue: ToastContextType = {
    showToast,
    showSuccess,
    showError,
    showInfo,
    showWarning
  }

  return (
    // @ts-ignore
    <(ToastContext as any).Provider value={contextValue}>
      {children}
      {/* Render toasts */}
      <div className="fixed top-0 right-0 z-50 p-4 space-y-2 pointer-events-none">
        // @ts-ignore
        {(toasts as any).map((toast, index as any) => (
          <div
            key={(toast as any).id}
            className="pointer-events-auto"
            style={{ 
              transform: `translateY(${index * 80}px as any)` 
            }}
          >
            <Toast {...toast} />
          </div>
        ))}
      </div>
    // @ts-ignore
    </(ToastContext as any).Provider>
  // @ts-ignore
  )
}
