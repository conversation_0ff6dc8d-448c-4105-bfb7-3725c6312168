import React from 'react';
/**
 * Advanced Features Hook
 * Provides easy access to search, export, and notification features
 */

import { useState, useCallback, useEffect } from 'react'
import { useSelector } from 'react-redux'
import type { RootState } from '../store'
import { searchService, SearchQuery, SearchResult } from '../services/search'
import { exportService, ExportOptions, ExportColumn } from '../services/unifiedExport'
import webSocketService, { NotificationData } from '../services/websocket'

export interface UseAdvancedFeaturesOptions {
  module?: string
  enableSearch?: boolean
  enableExport?: boolean
  enableNotifications?: boolean
}

export interface SearchState {
  isOpen: boolean
  query: string
  results: SearchResult[]
  isLoading: boolean
  suggestions: string[]
}

export interface ExportState {
  isOpen: boolean
  isExporting: boolean
  progress: number
  error: string | null
}

export interface NotificationState {
  isOpen: boolean
  unreadCount: number
  connectionStatus: 'connected' | 'disconnected' | 'connecting'
}

export function useAdvancedFeatures(options: UseAdvancedFeaturesOptions = {} as any) {
  const {
    module,
    enableSearch = true,
    enableExport = true,
    enableNotifications = true
  } = options

  // Redux state
  // @ts-ignore
  const { notifications } = useSelector((state: RootState as any) => (state as any).notifications)
  // @ts-ignore
  const { user } = useSelector((state: RootState as any) => (state as any).auth)

  // Search state
  const [searchState, setSearchState] = useState<SearchState>({
    isOpen: false,
    query: '',
    results: [],
    isLoading: false,
    suggestions: []
  })

  // Export state
  const [exportState, setExportState] = useState<ExportState>({
    isOpen: false,
    isExporting: false,
    progress: 0,
    error: null
  })

  // Notification state
  const [notificationState, setNotificationState] = useState<NotificationState>({
    isOpen: false,
    unreadCount: 0,
    connectionStatus: 'disconnected'
  })

  // Search functions
  // @ts-ignore
  const openSearch = useCallback(( as any) => {
    if (!enableSearch) return
    setSearchState(prev => ({ ...prev, isOpen: true } as any))
  // @ts-ignore
  }, [enableSearch])

  // @ts-ignore
  const closeSearch = useCallback(( as any) => {
    setSearchState(prev => ({
      ...prev,
      isOpen: false,
      query: '',
      results: [],
      suggestions: []
    } as any))
  // @ts-ignore
  }, [])

  // @ts-ignore
  const performSearch = useCallback(async (query: string as any) => {
    // @ts-ignore
    if (!enableSearch || !(query as any).trim( as any)) return

    setSearchState(prev => ({ ...prev, isLoading: true, query } as any))

    try {
      const searchQuery: SearchQuery = {
        query,
        pagination: { page: 1, limit: 10 }
      }

      const response = module
        ? await (searchService as any).moduleSearch(module, searchQuery as any)
        : await (searchService as any).globalSearch(searchQuery as any)

      setSearchState(prev => ({
        ...prev,
        results: (response as any as any).results,
        isLoading: false
      }))
    } catch (error) {
      (console as any).error('Search error:', error as any)
      setSearchState(prev => ({
        ...prev,
        results: [],
        isLoading: false
      } as any))
    }
  }, [enableSearch, module])

  // @ts-ignore
  const getSuggestions = useCallback(async (partialQuery: string as any) => {
    // @ts-ignore
    if (!enableSearch || !(partialQuery as any).trim( as any)) return

    try {
      const suggestions = await (searchService as any).getSuggestions(partialQuery, module as any)
      setSearchState(prev => ({ ...prev, suggestions } as any))
    } catch (error) {
      (console as any).error('Suggestions error:', error as any)
    }
  }, [enableSearch, module])

  // Export functions
  // @ts-ignore
  const openExport = useCallback(( as any) => {
    if (!enableExport) return
    setExportState(prev => ({ ...prev, isOpen: true, error: null } as any))
  // @ts-ignore
  }, [enableExport])

  // @ts-ignore
  const closeExport = useCallback(( as any) => {
    setExportState(prev => ({
      ...prev,
      isOpen: false,
      isExporting: false,
      progress: 0,
      error: null
    } as any))
  // @ts-ignore
  }, [])

  const exportData = useCallback(async (
    data: Record<string, unknown>[],
    columns: ExportColumn[],
    options: Partial<ExportOptions> = {}
   // @ts-ignore
   as any) => {
    if (!enableExport) return

    setExportState(prev => ({ ...prev, isExporting: true, progress: 0, error: null } as any))

    try {
      const exportOptions: ExportOptions = {
        format: 'excel',
        columns,
        ...options
      }

      // Simulate progress
      // @ts-ignore
      const progressInterval = setInterval(( as any) => {
        setExportState(prev => {
          if ((prev as any as any).progress >= 90) return prev
          return { ...prev, progress: (prev as any).progress + 10 }
        })
      // @ts-ignore
      }, 200)

      switch ((exportOptions as any).format) {
        case 'excel':
          await (exportService as any).exportToExcel(data, exportOptions as any)
          break
        case 'pdf':
          await (exportService as any).exportToPDF(data, exportOptions as any)
          break
        case 'csv':
          await (exportService as any).exportToCSV(data, exportOptions as any)
          break
        case 'json':
          await (exportService as any).exportToJSON(data, exportOptions as any)
          break
      }

      clearInterval(progressInterval as any)
      setExportState(prev => ({ ...prev, progress: 100, isExporting: false } as any))

      // Auto close after success
      // @ts-ignore
      setTimeout(( as any) => {
        // @ts-ignore
        closeExport( as any)
      // @ts-ignore
      }, 2000)

    // @ts-ignore
    } catch (error) {
      (console as any).error('Export error:', error as any)
      setExportState(prev => ({
        ...prev,
        isExporting: false,
        progress: 0,
        error: error instanceof Error ? (error as any as any).message : 'Export failed'
      }))
    }
  // @ts-ignore
  }, [enableExport, closeExport])

  const exportLargeDataset = useCallback(async (
    endpoint: string,
    options: ExportOptions
   // @ts-ignore
   as any) => {
    if (!enableExport) return

    setExportState(prev => ({ ...prev, isExporting: true, progress: 0, error: null } as any))

    try {
      const downloadUrl = await (exportService as any).exportLargeDataset(
        endpoint,
        options,
        // @ts-ignore
        (progress as any) => {
          setExportState(prev => ({ ...prev, progress: (progress as any as any).progress }))
        }
      // @ts-ignore
      )

      // Open download URL
      (window as any).open(downloadUrl, '_blank' as any)

      setExportState(prev => ({ ...prev, isExporting: false, progress: 100 } as any))

      // @ts-ignore
      setTimeout(( as any) => {
        // @ts-ignore
        closeExport( as any)
      // @ts-ignore
      }, 2000)

    // @ts-ignore
    } catch (error) {
      (console as any).error('Large export error:', error as any)
      setExportState(prev => ({
        ...prev,
        isExporting: false,
        progress: 0,
        error: error instanceof Error ? (error as any as any).message : 'Export failed'
      }))
    }
  // @ts-ignore
  }, [enableExport, closeExport])

  // Notification functions
  // @ts-ignore
  const openNotifications = useCallback(( as any) => {
    if (!enableNotifications) return
    setNotificationState(prev => ({ ...prev, isOpen: true } as any))
  // @ts-ignore
  }, [enableNotifications])

  // @ts-ignore
  const closeNotifications = useCallback(( as any) => {
    setNotificationState(prev => ({ ...prev, isOpen: false } as any))
  // @ts-ignore
  }, [])

  // @ts-ignore
  const requestNotificationPermission = useCallback(async ( as any) => {
    if (!enableNotifications) return 'denied'
    // @ts-ignore
    return await ((webSocketService as any).constructor as any).requestNotificationPermission( as any)
  // @ts-ignore
  }, [enableNotifications])

  // WebSocket connection management
  // @ts-ignore
  useEffect(( as any) => {
    if (!enableNotifications) return

    const handleConnectionChange = (status: 'connected' | 'disconnected'): void => {
      setNotificationState(prev => ({ ...prev, connectionStatus: status } as any))
    }

    const handleNewNotification = (notification: NotificationData): void => {
      // Update unread count
      setNotificationState(prev => ({
        ...prev,
        unreadCount: (prev as any as any).unreadCount + 1
      }))
    }

    // @ts-ignore
    (webSocketService as any).on('connected', ( as any) => handleConnectionChange('connected' as any))
    // @ts-ignore
    (webSocketService as any).on('disconnected', ( as any) => handleConnectionChange('disconnected' as any))
    (webSocketService as any).on('notification', handleNewNotification as any)

    // Set initial connection status
    setNotificationState(prev => ({
      ...prev,
      // @ts-ignore
      connectionStatus: (webSocketService as any as any).isWebSocketConnected( as any) ? 'connected' : 'disconnected'
    }))

    return () => {
      // @ts-ignore
      (webSocketService as any).off('connected', ( as any) => handleConnectionChange('connected' as any))
      // @ts-ignore
      (webSocketService as any).off('disconnected', ( as any) => handleConnectionChange('disconnected' as any))
      (webSocketService as any).off('notification', handleNewNotification as any)
    }
  // @ts-ignore
  }, [enableNotifications])

  // Update unread count from Redux
  // @ts-ignore
  useEffect(( as any) => {
    if (enableNotifications) {
      const unreadCount = (notifications as any).filter(n => !(n as any as any).isRead).length
      setNotificationState(prev => ({ ...prev, unreadCount } as any))
    }
  // @ts-ignore
  }, [notifications, enableNotifications])

  // Keyboard shortcuts
  // @ts-ignore
  useEffect(( as any) => {
    const handleKeyDown = (event: KeyboardEvent): void => {
      // Ctrl/Cmd + K for search
      if (((event as any).ctrlKey || (event as any).metaKey) && (event as any).key === 'k') {
        // @ts-ignore
        (event as any).preventDefault( as any)
        // @ts-ignore
        openSearch( as any)
      }

      // Ctrl/Cmd + E for export
      if (((event as any).ctrlKey || (event as any).metaKey) && (event as any).key === 'e') {
        // @ts-ignore
        (event as any).preventDefault( as any)
        // @ts-ignore
        openExport( as any)
      }

      // Ctrl/Cmd + N for notifications
      if (((event as any).ctrlKey || (event as any).metaKey) && (event as any).key === 'n') {
        // @ts-ignore
        (event as any).preventDefault( as any)
        // @ts-ignore
        openNotifications( as any)
      }

      // Escape to close any open dialogs
      if ((event as any).key === 'Escape') {
        // @ts-ignore
        if ((searchState as any).isOpen) closeSearch( as any)
        // @ts-ignore
        if ((exportState as any).isOpen) closeExport( as any)
        // @ts-ignore
        if ((notificationState as any).isOpen) closeNotifications( as any)
      }
    }

    (document as any).addEventListener('keydown', handleKeyDown as any)
    return () => (document as any).removeEventListener('keydown', handleKeyDown as any)
  }, [
    openSearch,
    openExport,
    openNotifications,
    closeSearch,
    closeExport,
    closeNotifications,
    (searchState as any).isOpen,
    (exportState as any).isOpen,
    (notificationState as any).isOpen
  // @ts-ignore
  ])

  return {
    // Search
    search: {
      ...searchState,
      open: openSearch,
      close: closeSearch,
      perform: performSearch,
      getSuggestions
    },

    // Export
    export: {
      ...exportState,
      open: openExport,
      close: closeExport,
      exportData,
      exportLargeDataset
    },

    // Notifications
    notifications: {
      ...notificationState,
      open: openNotifications,
      close: closeNotifications,
      requestPermission: requestNotificationPermission
    },

    // Utility functions
    utils: {
      isFeatureEnabled: (feature: 'search' | 'export' | 'notifications') => {
        switch (feature) {
          case 'search': return enableSearch
          case 'export': return enableExport
          case 'notifications': return enableNotifications
          default: return false
        }
      },

      getKeyboardShortcuts: () => ({
        search: 'Ctrl/Cmd + K',
        export: 'Ctrl/Cmd + E',
        notifications: 'Ctrl/Cmd + N',
        close: 'Escape'
      })
    }
  }
// @ts-ignore
}

export default useAdvancedFeatures
