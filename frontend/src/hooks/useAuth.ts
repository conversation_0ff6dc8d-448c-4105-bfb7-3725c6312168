import React from 'react';
/**
 * useAuth Hook
 * Custom hook for authentication state and actions
 */

import { useSelector, useDispatch } from 'react-redux'
import { useCallback } from 'react'
import type { RootState, AppDispatch } from '../store'
import { loginUser, logoutUser, verifyToken, clearError } from '../store/slices/authSlice'
import type { LoginCredentials } from '../services/api'

export const useAuth = () => {
  const dispatch = useDispatch<AppDispatch>()
  const authState = useSelector((state: RootState) => state.auth)

  const login = useCallback(async (credentials: LoginCredentials) => {
    return dispatch(loginUser(credentials))
  }, [dispatch])

  const logout = useCallback(async () => {
    return dispatch(logoutUser())
  }, [dispatch])

  const verify = useCallback(async () => {
    return dispatch(verifyToken())
  }, [dispatch])

  const clearAuthError = useCallback(() => {
    dispatch(clearError())
  }, [dispatch])

  return {
    // State
    user: authState.user,
    // SECURITY FIX: Token removed - handled by httpOnly cookies
    isAuthenticated: authState.isAuthenticated,
    isLoading: authState.isLoading,
    error: authState.error,
    loginAttempts: authState.loginAttempts,
    lastLoginAttempt: authState.lastLoginAttempt,

    // Actions
    login,
    logout,
    verify,
    clearError: clearAuthError,

    // Computed values - FIXED: Map backend role names to frontend role IDs
    isAdmin: authState.user?.role?.name === 'ADMIN' || authState.user?.role?.name === 'SUPERADMIN',
    isHRManager: authState.user?.role?.name === 'HR_MANAGER',
    isFinanceManager: authState.user?.role?.name === 'FINANCE_MANAGER',
    isDepartmentManager: authState.user?.role?.name === 'DEPARTMENT_MANAGER',
    isSalesManager: authState.user?.role?.name === 'SALES_MANAGER',
    isEmployee: authState.user?.role?.name === 'EMPLOYEE',
    userRole: authState.user?.role?.name?.toLowerCase().replace('_', '') || 'employee',
    userName: authState.user ? `${authState.user.first_name} ${authState.user.last_name}` : null,
    userEmail: authState.user?.email,
  }
}

export default useAuth
