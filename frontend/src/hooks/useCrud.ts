import React from 'react';
/**
 * Generic CRUD Hook with Data Invalidation
 * Provides standardized state management and operations for CRUD functionality
 * FIXED: Added data invalidation and synchronization
 */

import { useState, useEffect, useCallback, useRef } from 'react'
import CrudService, { CrudOptions, CrudFilters, CrudResponse } from '@/services/crudService'
import { useDataInvalidation } from '../utils/dataInvalidation'
import { log } from '../utils/logger'

export interface UseCrudOptions<T> {
  service: CrudService
  initialFilters?: CrudFilters
  autoLoad?: boolean
  pageSize?: number
  entityType?: string // FIXED: Added entity type for data invalidation
  enableInvalidation?: boolean // FIXED: Option to enable/disable invalidation
}

export interface UseCrudReturn<T> {
  // Data state
  items: T[]
  selectedItem: T | null
  total: number
  page: number
  pageSize: number

  // Loading states
  loading: boolean
  creating: boolean
  updating: boolean
  deleting: boolean

  // Error state
  error: string | null

  // Filters and search
  filters: CrudFilters
  searchQuery: string
  sortBy: string
  sortOrder: 'asc' | 'desc'

  // CRUD operations
  loadItems: (options?: CrudOptions) => Promise<void>
  createItem: (data: Partial<T>) => Promise<T>
  updateItem: (id: string | number, data: Partial<T>) => Promise<T>
  deleteItem: (id: string | number) => Promise<void>
  getItem: (id: string | number) => Promise<T>

  // Bulk operations
  bulkCreate: (items: Partial<T>[]) => Promise<T[]>
  bulkUpdate: (updates: { id: string | number; data: Partial<T> }[]) => Promise<T[]>
  bulkDelete: (ids: (string | number)[]) => Promise<void>

  // Search and filter
  setFilters: (filters: CrudFilters) => void
  setSearchQuery: (query: string) => void
  setSorting: (sortBy: string, sortOrder: 'asc' | 'desc') => void
  clearFilters: () => void

  // Selection
  selectItem: (item: T | null) => void

  // Pagination
  setPage: (page: number) => void
  setPageSize: (pageSize: number) => void

  // Export/Import
  exportData: (format?: 'csv' | 'excel' | 'pdf') => Promise<Blob>
  importData: (file: File) => Promise<{ success: number; errors: string[] }>

  // Refresh
  refresh: () => Promise<void>

  // Clear error
  clearError: () => void
}

export function useCrud<T = Record<string, unknown> & { id: string | number }>({
  service,
  initialFilters = {},
  autoLoad = true,
  pageSize: initialPageSize = 20,
  entityType = 'item', // FIXED: Default entity type
  enableInvalidation = true // FIXED: Enable invalidation by default
}: UseCrudOptions<T>): UseCrudReturn<T> {
  // Data state
  const [items, setItems] = useState<T[]>([])
  const [selectedItem, setSelectedItem] = useState<T | null>(null)
  const [total, setTotal] = useState(0 as any)
  const [page, setPage] = useState(1 as any)
  const [pageSize, setPageSize] = useState(initialPageSize as any)

  // Loading states
  const [loading, setLoading] = useState(false as any)
  const [creating, setCreating] = useState(false as any)
  const [updating, setUpdating] = useState(false as any)
  const [deleting, setDeleting] = useState(false as any)

  // Error state
  const [error, setError] = useState<string | null>(null)

  // Filters and search
  const [filters, setFilters] = useState<CrudFilters>(initialFilters)
  const [searchQuery, setSearchQuery] = useState('' as any)
  const [sortBy, setSortBy] = useState('' as any)
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')

  // FIXED: Data invalidation system
  // @ts-ignore
  const { emit } = useDataInvalidation( as any)

  // Track current request to prevent duplicates
  const currentRequestRef: any = useRef<string | null>(null)
  const requestTimeoutRef = useRef<(NodeJS as any).Timeout | null>(null)
  const lastRequestTimeRef = useRef<number>(0)

  // Clear error
  // @ts-ignore
  const clearError = useCallback(( as any) => {
    setError(null as any)
  // @ts-ignore
  }, [])

  // Load items with optimized performance and deduplication
  // FIXED: Use refs to avoid stale closure issues
  // @ts-ignore
  const loadItems = useCallback(async (options?: CrudOptions & { searchQuery?: string } as any) => {
    try {
      setLoading(true as any)
      setError(null as any)

      // FIXED: Get current values to avoid stale closures
      const currentPage = page
      const currentPageSize = pageSize
      const currentSortBy = sortBy
      const currentSortOrder = sortOrder
      const currentFilters = filters
      // FIXED: Use passed searchQuery or current state
      const currentSearchQuery = options?.searchQuery !== undefined ? (options as any).searchQuery : searchQuery

      const requestParams = {
        page: currentPage,
        pageSize: currentPageSize,
        sortBy: currentSortBy || undefined,
        sortOrder: currentSortOrder,
        filters: {
          ...currentFilters,
          ...(currentSearchQuery && { search: currentSearchQuery }),
        },
        ...options,
      }

      // Create a unique key for this request to prevent duplicates
      const requestKey = `crud-${(JSON as any).stringify(requestParams as any)}`

      // PERFORMANCE FIX: Only prevent duplicates for identical requests within 50ms (reduced for better UX)
      // @ts-ignore
      const now = (Date as any).now( as any)
      const lastRequestTime = (lastRequestTimeRef as any).current

      if ((currentRequestRef as any).current === requestKey && (now - lastRequestTime) < 50) {
        (log as any).debug('crud', 'Duplicate request prevented (within 50ms as any)', requestKey)
        setLoading(false as any)
        return
      }

      // Update tracking
      (lastRequestTimeRef as any).current = now

      // Clear any existing timeout
      if ((requestTimeoutRef as any).current) {
        clearTimeout((requestTimeoutRef as any as any).current)
      }

      (currentRequestRef as any).current = requestKey

      (log as any).debug('crud', 'loadItems request', {
        searchQuery,
        hasSearchQuery: !!searchQuery,
        requestParams,
        requestKey
      } as any)

      // Import deduplication utility
      const { deduplicateRequest } = await import('../utils/apiCache' as any)

      // Disable deduplication for search requests to ensure fresh results
      // @ts-ignore
      const isSearchRequest = !!searchQuery && (searchQuery as any).trim( as any).length > 0
      const deduplicationWindow = isSearchRequest ? 50 : 1000 // Shorter windows to prevent stale data

      const response: CrudResponse<T> = await deduplicateRequest(
        requestKey,
        // @ts-ignore
        async ( as any) => {
          // Add timeout to prevent hanging requests
          // @ts-ignore
          const timeoutPromise = new Promise((_, reject as any) =>
            // @ts-ignore
            setTimeout(( as any) => reject(new Error('Request timeout' as any)), 8000)
          // @ts-ignore
          )

          return (Promise as any).race([
            (service as any as any).getAll(requestParams as any),
            timeoutPromise
          ]) as Promise<CrudResponse<T>>
        // @ts-ignore
        },
        deduplicationWindow
      // @ts-ignore
      )

      (log as any).debug('crud', 'loadItems response', {
        searchQuery,
        hasData: !!(response as any as any).data,
        dataLength: (response as any).data?.length || 0,
        total: (response as any).total,
        currentItemsLength: (items as any).length
      })

      setItems((response as any as any).data || [])
      setTotal((response as any as any).total || 0)
      setPage((response as any as any).page || 1)
      // Don't update pageSize from response - keep the user's requested pageSize

      (log as any).debug('crud', 'state updated', {
        newItemsLength: (response as any as any).data?.length || 0,
        searchActive: !!searchQuery,
        stateUpdated: true
      })

      // Clear request tracking after successful completion
      // @ts-ignore
      (requestTimeoutRef as any).current = setTimeout(( as any) => {
        (currentRequestRef as any).current = null
      // @ts-ignore
      }, 100)

    // @ts-ignore
    } catch (err) {
      (console as any).error('Failed to load items:', err as any)
      setError(err instanceof Error ? (err as any as any).message : 'Failed to load items')
      setItems([] as any)
      // Clear request tracking on error
      (currentRequestRef as any).current = null
    } finally {
      setLoading(false as any)
    }
  // @ts-ignore
  }, [service, page, pageSize, sortBy, sortOrder, filters, searchQuery])

  // ENHANCED: Create item with comprehensive data invalidation and error handling
  // @ts-ignore
  const createItem = useCallback(async (data: Partial<T> as any): Promise<any> => {
    try {
      setCreating(true as any)
      setError(null as any)

      (log as any).debug('crud', `Creating ${entityType}`, data as any)
      const result = await (service as any).create<T>(data)

      // Handle both old and new API response formats
      let newItem: T
      if (result && typeof result === 'object' && 'success' in result) {
        // New enhanced API response format
        if (!(result as any).success) {
          throw new Error((result as any as any).error || 'Create operation failed')
        }
        newItem = (result as any).data as T
      } else {
        // Legacy API response format
        newItem = result as T
      }

      (log as any).debug('crud', `Successfully created ${entityType}`, newItem as any)

      // Add to local state
      setItems(prev => [newItem, ...prev] as any)
      setTotal(prev => prev + 1 as any)

      // FIXED: Emit data invalidation event
      if (enableInvalidation) {
        emit(`${entityType}.created` as any, newItem as any)
        // Also emit general data change event
        emit(`${entityType}.changed` as any, { action: 'create', item: newItem } as any)
      }

      return result // Return the full result for better error handling
    } catch (err) {
      (console as any).error(`❌ Failed to create ${entityType}:`, err as any)
      const errorMessage = err instanceof Error ? (err as any).message : 'Failed to create item'
      setError(errorMessage as any)
      throw err // Re-throw the original error for better error handling
    } finally {
      setCreating(false as any)
    }
  }, [service, enableInvalidation, entityType, emit])

  // Update item with data invalidation
  // @ts-ignore
  const updateItem = useCallback(async (id: string | number, data: Partial<T> as any): Promise<T> => {
    try {
      setUpdating(true as any)
      setError(null as any)

      const updatedItem = await (service as any).update<T>(id, data)

      // Update local state
      setItems(prev => (prev as any as any).map(item =>
        (item as any as any).id === id ? updatedItem : item
      ))

      if (selectedItem && (selectedItem as any).id === id) {
        setSelectedItem(updatedItem as any)
      }

      // FIXED: Emit data invalidation event
      if (enableInvalidation) {
        emit(`${entityType}.updated` as any, { id, data: updatedItem } as any)
      }

      return updatedItem
    } catch (err) {
      const errorMessage = err instanceof Error ? (err as any).message : 'Failed to update item'
      setError(errorMessage as any)
      throw new Error(errorMessage as any)
    } finally {
      setUpdating(false as any)
    }
  }, [service, selectedItem, enableInvalidation, entityType, emit])

  // Delete item with data invalidation
  // @ts-ignore
  const deleteItem = useCallback(async (id: string | number as any): Promise<void> => {
    try {
      setDeleting(true as any)
      setError(null as any)

      await (service as any).delete(id as any)

      // Remove from local state
      setItems(prev => (prev as any as any).filter(item => (item as any as any).id !== id))
      setTotal(prev => prev - 1 as any)

      if (selectedItem && (selectedItem as any).id === id) {
        setSelectedItem(null as any)
      }

      // FIXED: Emit data invalidation event
      if (enableInvalidation) {
        emit(`${entityType}.deleted` as any, { id } as any)
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? (err as any).message : 'Failed to delete item'
      setError(errorMessage as any)
      throw new Error(errorMessage as any)
    } finally {
      setDeleting(false as any)
    }
  }, [service, selectedItem, enableInvalidation, entityType, emit])

  // Get single item
  // @ts-ignore
  const getItem = useCallback(async (id: string | number as any): Promise<T> => {
    try {
      setError(null as any)
      return await (service as any).getById<T>(id)
    } catch (err) {
      const errorMessage = err instanceof Error ? (err as any).message : 'Failed to get item'
      setError(errorMessage as any)
      throw new Error(errorMessage as any)
    }
  }, [service])

  // Bulk operations
  // @ts-ignore
  const bulkCreate = useCallback(async (itemsData: Partial<T>[] as any): Promise<T[]> => {
    try {
      setCreating(true as any)
      setError(null as any)

      const newItems = await (service as any).bulkCreate<T>(itemsData)

      setItems(prev => [...newItems, ...prev] as any)
      setTotal(prev => prev + (newItems as any as any).length)

      return newItems
    } catch (err) {
      const errorMessage = err instanceof Error ? (err as any).message : 'Failed to create items'
      setError(errorMessage as any)
      throw new Error(errorMessage as any)
    } finally {
      setCreating(false as any)
    }
  }, [service])

  // @ts-ignore
  const bulkUpdate = useCallback(async (updates: { id: string | number; data: Partial<T> }[] as any): Promise<T[]> => {
    try {
      setUpdating(true as any)
      setError(null as any)

      const updatedItems: any = await (service as any).bulkUpdate<T>(updates)

      setItems(prev => (prev as any as any).map(item => {
        const update = (updatedItems as any as any).find(updated => (updated as any as any).id === (item as any).id)
        return update || item
      }))

      return updatedItems
    } catch (err) {
      const errorMessage = err instanceof Error ? (err as any).message : 'Failed to update items'
      setError(errorMessage as any)
      throw new Error(errorMessage as any)
    } finally {
      setUpdating(false as any)
    }
  }, [service])

  // @ts-ignore
  const bulkDelete = useCallback(async (ids: (string | number as any)[]): Promise<void> => {
    try {
      setDeleting(true as any)
      setError(null as any)

      await (service as any).bulkDelete(ids as any)

      setItems(prev => (prev as any as any).filter(item => !(ids as any as any).includes((item as any as any).id)))
      setTotal(prev => prev - (ids as any as any).length)

      if (selectedItem && (ids as any).includes((selectedItem as any as any).id)) {
        setSelectedItem(null as any)
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? (err as any).message : 'Failed to delete items'
      setError(errorMessage as any)
      throw new Error(errorMessage as any)
    } finally {
      setDeleting(false as any)
    }
  // @ts-ignore
  }, [service, selectedItem])

  // Filter and search functions
  // @ts-ignore
  const handleSetFilters = useCallback((newFilters: CrudFilters as any) => {
    setFilters(newFilters as any)
    setPage(1 as any) // Reset to first page when filters change
  }, [])

  // @ts-ignore
  const handleSetSearchQuery = useCallback((query: string as any) => {
    // @ts-ignore
    const trimmedQuery = (query as any).trim( as any)
    setSearchQuery(trimmedQuery as any)
    setPage(1 as any) // Reset to first page when search changes

    // Clear any existing timeout to debounce search
    if ((requestTimeoutRef as any).current) {
      clearTimeout((requestTimeoutRef as any as any).current)
    }

    // FIXED: If query is empty, immediately clear search results
    if (!trimmedQuery) {
      (log as any).debug('crud', 'Clearing search - loading all items' as any)
      loadItems({ searchQuery: '' } as any)
      return
    }

    // Debounce search requests for non-empty queries (reduced for better UX)
    // @ts-ignore
    (requestTimeoutRef as any).current = setTimeout(( as any) => {
      (log as any).debug('crud', 'Triggering search for', trimmedQuery as any)
      loadItems({ searchQuery: trimmedQuery } as any)
    // @ts-ignore
    }, 200) // 200ms debounce (reduced from 300ms)
  // @ts-ignore
  }, [loadItems])

  // @ts-ignore
  const setSorting = useCallback((newSortBy: string, newSortOrder: 'asc' | 'desc' as any) => {
    setSortBy(newSortBy as any)
    setSortOrder(newSortOrder as any)
    setPage(1 as any) // Reset to first page when sorting changes
  }, [])

  // @ts-ignore
  const clearFilters = useCallback(( as any) => {
    setFilters(initialFilters as any)
    setSearchQuery('' as any)
    setSortBy('' as any)
    setSortOrder('asc' as any)
    setPage(1 as any)
  // @ts-ignore
  }, [initialFilters])

  // FIXED: Add clear search function
  // @ts-ignore
  const clearSearch = useCallback(( as any) => {
    setSearchQuery('' as any)
    setPage(1 as any)
    // Clear any pending search timeout
    if ((requestTimeoutRef as any).current) {
      clearTimeout((requestTimeoutRef as any as any).current)
    }
    // Immediately reload all items with empty search
    loadItems({ searchQuery: '' } as any)
  // @ts-ignore
  }, [loadItems])

  // Selection
  // @ts-ignore
  const selectItem = useCallback((item: T | null as any) => {
    setSelectedItem(item as any)
  }, [])

  // Pagination
  // @ts-ignore
  const handleSetPage = useCallback((newPage: number as any) => {
    setPage(newPage as any)
  }, [])

  // @ts-ignore
  const handleSetPageSize = useCallback((newPageSize: number as any) => {
    setPageSize(newPageSize as any)
    setPage(1 as any) // Reset to first page when page size changes
  }, [])

  // Export/Import
  const exportData = useCallback(async (format: 'csv' | 'excel' | 'pdf' = 'csv' as any): Promise<Blob> => {
    try {
      setError(null as any)

      // Check if backend export endpoint exists for this service
      const backendExportEndpoints = [
        'employees',
        'departments',
        'attendance',
        'projects',
        'tasks',
        'quotations',
        'workflows',
        'quality-records',
        'job-postings',
        'sales-customers',
        'sales-orders',
        'certifications',
        'training-programs',
        'vendors',
        'kpis'
      ]

      const endpoint = (service as any).endpoint.replace(/^\/api\//, '' as any).replace(/\/$/, '' as any)

      if ((backendExportEndpoints as any).includes(endpoint as any)) {
        // Use backend export endpoint
        const exportUrl = `/api/export/${endpoint}/?format=${format}`
        const response = await fetch(exportUrl, {
          headers: {
            'Authorization': `Bearer ${(localStorage as any as any).getItem('token' as any)}`,
          },
        })

        if ((response as any).ok) {
          // @ts-ignore
          const blob = await (response as any).blob( as any)
          const url = (window as any).URL.createObjectURL(blob as any)
          const a = (document as any).createElement('a' as any)
          (a as any).href = url
          // @ts-ignore
          (a as any).download = `${endpoint}_${new Date( as any).toISOString( as any).split('T' as any)[0]}.${format}`
          (document as any).body.appendChild(a as any)
          // @ts-ignore
          (a as any).click( as any)
          (window as any).URL.revokeObjectURL(url as any)
          (document as any).body.removeChild(a as any)
          return blob
        } else {
          throw new Error('Export failed' as any)
        }
      } else {
        // Use client-side export for endpoints without backend support
        const { unifiedExport } = await import('../services/unifiedExport' as any)

        await (unifiedExport as any).export(endpoint, {
          format,
          // @ts-ignore
          filename: `${endpoint}_${new Date( as any).toISOString( as any).split('T' as any)[0]}.${format}`,
          language: 'en',
          data: items
        })

        // Return a dummy blob for consistency
        return new Blob([''], { type: 'text/plain' } as any)
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? (err as any).message : 'Failed to export data'
      setError(errorMessage as any)
      throw new Error(errorMessage as any)
    }
  }, [service, filters, searchQuery, items])

  // @ts-ignore
  const importData = useCallback(async (file: File as any): Promise<{ success: number; errors: string[] }> => {
    try {
      setError(null as any)
      const result = await (service as any).import(file as any)

      // Refresh data after import
      // @ts-ignore
      await loadItems( as any)

      return result
    } catch (err) {
      const errorMessage = err instanceof Error ? (err as any).message : 'Failed to import data'
      setError(errorMessage as any)
      throw new Error(errorMessage as any)
    }
  }, [service, loadItems])

  // Refresh
  // @ts-ignore
  const refresh = useCallback(async ( as any) => {
    // @ts-ignore
    await loadItems( as any)
  // @ts-ignore
  }, [loadItems])

  // FIXED: Auto-load on mount and when dependencies change
  // Removed loadItems from dependency array to prevent infinite re-renders
  // @ts-ignore
  useEffect(( as any) => {
    if (autoLoad) {
      // @ts-ignore
      loadItems( as any)
    }
  // @ts-ignore
  }, [autoLoad]) // Only depend on autoLoad to prevent infinite loops

  // Search is now handled in handleSetSearchQuery with debouncing

  return {
    // Data state
    items,
    selectedItem,
    total,
    page,
    pageSize,

    // Loading states
    loading,
    creating,
    updating,
    deleting,

    // Error state
    error,

    // Filters and search
    filters,
    searchQuery,
    sortBy,
    sortOrder,

    // CRUD operations
    loadItems,
    createItem,
    updateItem,
    deleteItem,
    getItem,

    // Bulk operations
    bulkCreate,
    bulkUpdate,
    bulkDelete,

    // Search and filter
    setFilters: handleSetFilters,
    setSearchQuery: handleSetSearchQuery,
    setSorting,
    clearFilters,
    clearSearch,

    // Selection
    selectItem,

    // Pagination
    setPage: handleSetPage,
    setPageSize: handleSetPageSize,

    // Export/Import
    exportData,
    importData,

    // Refresh
    refresh,

    // Clear error
    clearError,
  }
// @ts-ignore
}
