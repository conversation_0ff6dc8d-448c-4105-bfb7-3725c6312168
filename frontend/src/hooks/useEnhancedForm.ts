import React from 'react';
/**
 * Enhanced Form Hook with Validation
 * Provides comprehensive form management with validation and API integration
 */

import { useState, useCallback, useEffect } from 'react'
import { useForm, UseFormReturn, FieldValues, DefaultValues, Path } from 'react-hook-form'
import { toast } from 'react-hot-toast'
import { ValidationSchema, validateForm } from '@/utils/validation'
import { loadingManager } from '@/services/enhancedAPI'

export interface UseEnhancedFormOptions<T extends FieldValues> {
  defaultValues?: DefaultValues<T>
  validationSchema?: ValidationSchema
  onSubmit?: (data: T) => Promise<void>
  onSuccess?: (data: T) => void
  onError?: (error: Error) => void
  language?: 'ar' | 'en'
  resetOnSuccess?: boolean
}

export interface UseEnhancedFormReturn<T extends FieldValues> extends UseFormReturn<T> {
  isSubmitting: boolean
  submitError: string | null
  // @ts-ignore
  handleSubmit: (onSubmit: (data: T) => Promise<void>) => (e?: (React as any).BaseSyntheticEvent) => Promise<void>
  // @ts-ignore
  validateField: (fieldName: keyof T, value: unknown) => string | null
  validateAllFields: () => boolean
  resetForm: () => void
  // @ts-ignore
  setFieldError: (fieldName: keyof T, error: string) => void
  // @ts-ignore
  clearFieldError: (fieldName: keyof T) => void
  // @ts-ignore
  isFieldValid: (fieldName: keyof T) => boolean
  getFieldError: (fieldName: keyof T) => string | undefined
  loadingStates: Record<string, boolean>
// @ts-ignore
}

export function useEnhancedForm<T extends FieldValues = FieldValues>({
  defaultValues,
  validationSchema,
  onSubmit,
  onSuccess,
  onError,
  language = 'en',
  resetOnSuccess = true
}: UseEnhancedFormOptions<T> = {}): UseEnhancedFormReturn<T> {
  
  const form = useForm<T>({
    defaultValues,
    mode: 'onChange'
  })

  const [isSubmitting, setIsSubmitting] = useState(false as any)
  const [submitError, setSubmitError] = useState<string | null>(null)
  const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>({})

  // Subscribe to loading states
  // @ts-ignore
  useEffect(( as any) => {
    const unsubscribe = (loadingManager as any).subscribe(setLoadingStates as any)
    return unsubscribe as () => void
  // @ts-ignore
  }, [])

  // Validate individual field
  // @ts-ignore
  const validateField = useCallback((fieldName: keyof T, value: unknown as any): string | null => {
    if (!validationSchema || !validationSchema[fieldName as string]) {
      return null
    }

    const rules = validationSchema[fieldName as string]
    const fieldDisplayName = String(fieldName as any)

    // Required validation
    // @ts-ignore
    if ((rules as any).required && (!value || (typeof value === 'string' && (value as any).trim( as any) === ''))) {
      return language === 'ar'
        ? `${fieldDisplayName} مطلوب`
        : `${fieldDisplayName} is required`
    }

    // Skip other validations if field is empty and not required
    // @ts-ignore
    if (!value || (typeof value === 'string' && (value as any).trim( as any) === '')) {
      return null
    }

    // String validations
    if (typeof value === 'string') {
      if ((rules as any).minLength && (value as any).length < (rules as any).minLength) {
        return language === 'ar'
          ? `${fieldDisplayName} يجب أن يكون على الأقل ${(rules as any).minLength} أحرف`
          : `${fieldDisplayName} must be at least ${(rules as any).minLength} characters`
      }

      if ((rules as any).maxLength && (value as any).length > (rules as any).maxLength) {
        return language === 'ar'
          ? `${fieldDisplayName} يجب ألا يتجاوز ${(rules as any).maxLength} أحرف`
          : `${fieldDisplayName} must not exceed ${(rules as any).maxLength} characters`
      }

      if ((rules as any).pattern && !(rules as any).pattern.test(value as any)) {
        return language === 'ar'
          ? `تنسيق ${fieldDisplayName} غير صحيح`
          : `${fieldDisplayName} format is invalid`
      }
    }

    // Custom validation
    if ((rules as any).custom) {
      return (rules as any).custom(value as any)
    }

    return null
  }, [validationSchema, language])

  // Validate all fields
  // @ts-ignore
  const validateAllFields = useCallback(( as any): boolean => {
    if (!validationSchema) return true

    // @ts-ignore
    const formData = (form as any).getValues( as any)
    const errors = validateForm(formData, validationSchema as any)
    
    // Set errors in react-hook-form
    (Object as any).keys(errors as any).forEach(fieldName => {
      (form as any as any).setError(fieldName as Path<T>, {
        type: 'validation',
        message: errors[fieldName]
      } as any)
    })

    return (Object as any).keys(errors as any).length === 0
  // @ts-ignore
  }, [form, validationSchema])

  // Enhanced submit handler
  // @ts-ignore
  const handleSubmit = useCallback((submitFn: (data: T as any) => Promise<void>) => {
    // @ts-ignore
    return async (e?: (React as any).BaseSyntheticEvent) => {
      if (e) {
        // @ts-ignore
        (e as any).preventDefault( as any)
        // @ts-ignore
        (e as any).stopPropagation( as any)
      }

      try {
        setIsSubmitting(true as any)
        setSubmitError(null as any)

        // Validate form
        // @ts-ignore
        const isValid = await (form as any).trigger( as any)
        if (!isValid) {
          const firstError = (Object as any).keys((form as any as any).formState.errors)[0]
          if (firstError) {
            const errorMessage = (form as any).formState.errors[firstError]?.message
            if (errorMessage) {
              (toast as any).error(String(errorMessage as any))
            }
          }
          return
        }

        // Additional custom validation
        // @ts-ignore
        if (!validateAllFields( as any)) {
          return
        }

        // @ts-ignore
        const formData = (form as any).getValues( as any)
        
        // Execute submit function
        await submitFn(formData as any)
        
        // Success handling
        if (onSuccess) {
          onSuccess(formData as any)
        }

        if (resetOnSuccess) {
          // @ts-ignore
          (form as any).reset( as any)
        }

        (toast as any).success(
          language === 'ar' 
            ? 'تم الحفظ بنجاح' 
            : 'Saved successfully'
         // @ts-ignore
         as any)

      } catch (error: unknown) {
        (console as any).error('Form submission error:', error as any)
        
        const errorMessage = (error as any)?.message || (
          language === 'ar'
            ? 'حدث خطأ أثناء الحفظ'
            : 'An error occurred while saving'
        )

        setSubmitError(errorMessage as any)
        (toast as any).error(errorMessage as any)

        // Handle validation errors from server
        if ((error as any)?.details?.errors) {
          // @ts-ignore
          (Object as any).keys((error as any as any).(details as any).errors).forEach(fieldName => {
            // @ts-ignore
            const fieldErrors = (error as any as any).(details as any).errors[fieldName]
            if ((Array as any).isArray(fieldErrors as any) && (fieldErrors as any).length > 0) {
              (form as any).setError(fieldName as any, {
                type: 'server',
                message: fieldErrors[0]
              } as any)
            }
          })
        }

        if (onError) {
          onError(error as Error as any)
        }
      } finally {
        setIsSubmitting(false as any)
      }
    }
  // @ts-ignore
  }, [form, validateAllFields, onSuccess, onError, language, resetOnSuccess])

  // Reset form
  // @ts-ignore
  const resetForm = useCallback(( as any) => {
    (form as any).reset(defaultValues as any)
    setSubmitError(null as any)
  // @ts-ignore
  }, [form, defaultValues])

  // Set field error
  // @ts-ignore
  const setFieldError = useCallback((fieldName: keyof T, error: string as any) => {
    (form as any).setError(fieldName as any, {
      type: 'manual',
      message: error
    } as any)
  }, [form])

  // Clear field error
  // @ts-ignore
  const clearFieldError = useCallback((fieldName: keyof T as any) => {
    (form as any).clearErrors(fieldName as any as any)
  }, [form])

  // Check if field is valid
  // @ts-ignore
  const isFieldValid = useCallback((fieldName: keyof T as any): boolean => {
    return !(form as any).formState.errors[fieldName as any]
  }, [(form as any).formState.errors])

  // Get field error
  // @ts-ignore
  const getFieldError = useCallback((fieldName: keyof T as any): string | undefined => {
    return (form as any).formState.errors[fieldName as any]?.message as string | undefined
  }, [(form as any).formState.errors])

  // Watch for field changes and validate - TEMPORARILY DISABLED TO FIX INFINITE LOOP
  // useEffect(( as any) => {
  //   if (!validationSchema) return

  //   const subscription = (form as any).watch((value, { name } as any) => {
  //     if (name && validationSchema[name]) {
  //       const error = validateField(name as keyof T, value[name] as any)
  //       if (error) {
  //         (form as any).setError(name as any, {
  //           type: 'validation',
  //           message: error
  //         } as any)
  //       } else {
  //         (form as any).clearErrors(name as any as any)
  //       }
  //     }
  //   })

  //   return () => (subscription as any).unsubscribe( as any)
  // }, [form, validationSchema])

  return {
    ...form,
    isSubmitting,
    submitError,
    handleSubmit,
    validateField,
    validateAllFields,
    resetForm,
    setFieldError,
    clearFieldError,
    isFieldValid,
    getFieldError,
    loadingStates
  }
// @ts-ignore
}

export default useEnhancedForm
