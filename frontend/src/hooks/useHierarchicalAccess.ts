import React from 'react';
/**
 * useHierarchicalAccess Hook
 * React hook for hierarchical access control and role-based data filtering
 * 
 * Features:
 * - Hierarchical access information management
 * - Role-based data filtering
 * - Manager-subordinate access patterns
 * - Real-time access updates
 * - Caching and performance optimization
 */

import { useState, useEffect, useCallback, useMemo } from 'react'
import { useAuth } from './useAuth'
import { 
  hierarchicalAccessService, 
  HierarchicalAccessInfo, 
  RoleBasedKPIFilter,
  AccessSummary,
  HierarchicalPath
} from '../services/hierarchicalAccessService'
import logger from '../utils/logger'

interface UseHierarchicalAccessReturn {
  // Access information
  accessInfo: HierarchicalAccessInfo | null
  accessSummary: AccessSummary | null
  hierarchicalPath: HierarchicalPath[]
  kpiCategories: string[]
  
  // Access checks
  canAccessAllData: boolean
  isManager: boolean
  hasHierarchicalAccess: boolean
  isDepartmentScoped: boolean
  isProjectScoped: boolean
  
  // Data access
  accessibleEmployees: Array<{ id: number; name: string; position: string; department: string | null }>
  accessibleDepartments: Array<{ id: number; name: string; manager: string | null }>
  accessibleProjects: Array<{ id: number; name: string; manager: string | null }>
  
  // Filters
  roleBasedKPIFilters: RoleBasedKPIFilter | null
  
  // State management
  loading: boolean
  error: string | null
  
  // Actions
  refreshAccessInfo: () => Promise<void>
  canAccessEmployee: (employeeId: number) => Promise<boolean>
  canAccessDepartment: (departmentId: number) => Promise<boolean>
  canAccessProject: (projectId: number) => Promise<boolean>
  clearCache: () => void
}

export const useHierarchicalAccess = (): UseHierarchicalAccessReturn => {
  const { user, isAuthenticated } = useAuth()
  
  // State
  const [accessInfo, setAccessInfo] = useState<HierarchicalAccessInfo | null>(null)
  const [roleBasedKPIFilters, setRoleBasedKPIFilters] = useState<RoleBasedKPIFilter | null>(null)
  const [loading, setLoading] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)

  // Load hierarchical access information
  const loadAccessInfo = useCallback(async (forceRefresh = false) => {
    if (!isAuthenticated || !user) {
      setAccessInfo(null)
      setRoleBasedKPIFilters(null)
      return
    }

    try {
      setLoading(true)
      setError(null)

      // Try to get cached data first if not forcing refresh
      if (!forceRefresh) {
        const cachedInfo = hierarchicalAccessService.getCachedAccessInfo()
        if (cachedInfo) {
          setAccessInfo(cachedInfo)
          setLoading(false)
          return
        }
      }

      // Fetch fresh data
      const [accessData, kpiFilters] = await Promise.all([
        hierarchicalAccessService.getHierarchicalAccessInfo(forceRefresh),
        hierarchicalAccessService.getRoleBasedKPIFilters()
      ])

      setAccessInfo(accessData)
      setRoleBasedKPIFilters(kpiFilters)

      logger.info('useHierarchicalAccess', 'Hierarchical access info loaded successfully', {
        role: accessData.user_role,
        employeesCount: accessData.accessible_data.employees_count,
        isManager: accessData.access_summary.is_manager
      })

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load hierarchical access info'
      setError(errorMessage)
      logger.error('useHierarchicalAccess', 'Error loading hierarchical access info:', err)
    } finally {
      setLoading(false)
    }
  }, [isAuthenticated, user])

  // Load access info on mount and when authentication changes
  useEffect(() => {
    loadAccessInfo()
  }, [loadAccessInfo])

  // Refresh access information
  const refreshAccessInfo = useCallback(async () => {
    await loadAccessInfo(true)
  }, [loadAccessInfo])

  // Access check functions
  const canAccessEmployee = useCallback(async (employeeId: number): Promise<boolean> => {
    try {
      return await hierarchicalAccessService.canAccessEmployeeData(employeeId)
    } catch (error) {
      logger.error('useHierarchicalAccess', 'Error checking employee access:', error)
      return false
    }
  }, [])

  const canAccessDepartment = useCallback(async (departmentId: number): Promise<boolean> => {
    try {
      return await hierarchicalAccessService.canAccessDepartmentData(departmentId)
    } catch (error) {
      logger.error('useHierarchicalAccess', 'Error checking department access:', error)
      return false
    }
  }, [])

  const canAccessProject = useCallback(async (projectId: number): Promise<boolean> => {
    try {
      return await hierarchicalAccessService.canAccessProjectData(projectId)
    } catch (error) {
      logger.error('useHierarchicalAccess', 'Error checking project access:', error)
      return false
    }
  }, [])

  // Clear cache
  const clearCache = useCallback(() => {
    hierarchicalAccessService.clearCache()
    setAccessInfo(null)
    setRoleBasedKPIFilters(null)
  }, [])

  // Computed values
  const accessSummary = useMemo(() => {
    return accessInfo?.access_summary || null
  }, [accessInfo])

  const hierarchicalPath = useMemo(() => {
    return accessInfo?.hierarchical_path || []
  }, [accessInfo])

  const kpiCategories = useMemo(() => {
    return accessInfo?.kpi_categories || []
  }, [accessInfo])

  const canAccessAllData = useMemo(() => {
    return accessSummary?.can_access_all_data || false
  }, [accessSummary])

  const isManager = useMemo(() => {
    return accessSummary?.is_manager || false
  }, [accessSummary])

  const hasHierarchicalAccess = useMemo(() => {
    return accessSummary?.has_hierarchical_access || false
  }, [accessSummary])

  const isDepartmentScoped = useMemo(() => {
    return accessSummary?.department_scoped || false
  }, [accessSummary])

  const isProjectScoped = useMemo(() => {
    return accessSummary?.project_scoped || false
  }, [accessSummary])

  const accessibleEmployees = useMemo(() => {
    return accessInfo?.accessible_data.employees || []
  }, [accessInfo])

  const accessibleDepartments = useMemo(() => {
    return accessInfo?.accessible_data.departments || []
  }, [accessInfo])

  const accessibleProjects = useMemo(() => {
    return accessInfo?.accessible_data.projects || []
  }, [accessInfo])

  return {
    // Access information
    accessInfo,
    accessSummary,
    hierarchicalPath,
    kpiCategories,
    
    // Access checks
    canAccessAllData,
    isManager,
    hasHierarchicalAccess,
    isDepartmentScoped,
    isProjectScoped,
    
    // Data access
    accessibleEmployees,
    accessibleDepartments,
    accessibleProjects,
    
    // Filters
    roleBasedKPIFilters,
    
    // State management
    loading,
    error,
    
    // Actions
    refreshAccessInfo,
    canAccessEmployee,
    canAccessDepartment,
    canAccessProject,
    clearCache
  }
}

export default useHierarchicalAccess
