import React from 'react';
/**
 * useHierarchicalAccessRedux Hook
 * Redux-based hook for hierarchical access control to prevent duplicate API calls
 * 
 * PERFORMANCE FIX: Replaces multiple individual API calls with centralized Redux state
 */

import { useEffect, useCallback } from 'react'
import { useAppDispatch, useAppSelector } from '../store/hooks'
import { useAuth } from './useAuth'
import {
  fetchHierarchicalAccessInfo,
  checkEmployeeAccess,
  checkDepartmentAccess,
  checkProjectAccess,
  clearAccessData,
  clearError,
  clearCache,
  selectHierarchicalAccess,
  selectAccessInfo,
  selectAccessSummary,
  selectHierarchicalPath,
  selectKpiCategories,
  selectCanAccessAllData,
  selectIsManager,
  selectHasHierarchicalAccess,
  selectIsDepartmentScoped,
  selectIsProjectScoped,
  selectAccessibleEmployees,
  selectAccessibleDepartments,
  selectAccessibleProjects,
  selectRoleBasedKPIFilters
} from '../store/slices/hierarchicalAccessSlice'
import logger from '../utils/logger'

interface UseHierarchicalAccessReturn {
  // Access information
  accessInfo: ReturnType<typeof selectAccessInfo>
  accessSummary: ReturnType<typeof selectAccessSummary>
  hierarchicalPath: ReturnType<typeof selectHierarchicalPath>
  kpiCategories: ReturnType<typeof selectKpiCategories>
  
  // Access checks
  canAccessAllData: ReturnType<typeof selectCanAccessAllData>
  isManager: ReturnType<typeof selectIsManager>
  hasHierarchicalAccess: ReturnType<typeof selectHasHierarchicalAccess>
  isDepartmentScoped: ReturnType<typeof selectIsDepartmentScoped>
  isProjectScoped: ReturnType<typeof selectIsProjectScoped>
  
  // Data access
  accessibleEmployees: ReturnType<typeof selectAccessibleEmployees>
  accessibleDepartments: ReturnType<typeof selectAccessibleDepartments>
  accessibleProjects: ReturnType<typeof selectAccessibleProjects>
  
  // Filters
  roleBasedKPIFilters: ReturnType<typeof selectRoleBasedKPIFilters>
  
  // State management
  loading: boolean
  error: string | null
  isInitialized: boolean
  
  // Actions
  refreshAccessInfo: (forceRefresh?: boolean) => Promise<void>
  canAccessEmployee: (employeeId: number) => Promise<boolean>
  canAccessDepartment: (departmentId: number) => Promise<boolean>
  canAccessProject: (projectId: number) => Promise<boolean>
  clearCacheData: () => void
}

export const useHierarchicalAccessRedux = (): UseHierarchicalAccessReturn => {
  const dispatch = useAppDispatch()
  const { user, isAuthenticated } = useAuth()
  
  // Select data from Redux store
  const hierarchicalAccessState = useAppSelector(selectHierarchicalAccess)
  const accessInfo = useAppSelector(selectAccessInfo)
  const accessSummary = useAppSelector(selectAccessSummary)
  const hierarchicalPath = useAppSelector(selectHierarchicalPath)
  const kpiCategories = useAppSelector(selectKpiCategories)
  const canAccessAllData = useAppSelector(selectCanAccessAllData)
  const isManager = useAppSelector(selectIsManager)
  const hasHierarchicalAccess = useAppSelector(selectHasHierarchicalAccess)
  const isDepartmentScoped = useAppSelector(selectIsDepartmentScoped)
  const isProjectScoped = useAppSelector(selectIsProjectScoped)
  const accessibleEmployees = useAppSelector(selectAccessibleEmployees)
  const accessibleDepartments = useAppSelector(selectAccessibleDepartments)
  const accessibleProjects = useAppSelector(selectAccessibleProjects)
  const roleBasedKPIFilters = useAppSelector(selectRoleBasedKPIFilters)

  // Load access info when authentication changes
  useEffect(() => {
    if (isAuthenticated && user && !hierarchicalAccessState.isInitialized) {
      logger.info('useHierarchicalAccessRedux', 'Initializing hierarchical access data', {
        userId: user.id,
        userRole: user.role?.name
      })
      dispatch(fetchHierarchicalAccessInfo(false))
    } else if (!isAuthenticated) {
      // Clear data when user logs out
      dispatch(clearAccessData())
    }
  }, [isAuthenticated, user?.id, hierarchicalAccessState.isInitialized, dispatch])

  // Refresh access information
  const refreshAccessInfo = useCallback(async (forceRefresh = false) => {
    if (!isAuthenticated || !user) {
      logger.warn('useHierarchicalAccessRedux', 'Cannot refresh access info - user not authenticated')
      return
    }

    logger.info('useHierarchicalAccessRedux', 'Refreshing hierarchical access info', {
      forceRefresh,
      userId: user.id
    })
    
    await dispatch(fetchHierarchicalAccessInfo(forceRefresh))
  }, [dispatch, isAuthenticated, user])

  // Access check functions
  const canAccessEmployee = useCallback(async (employeeId: number): Promise<boolean> => {
    try {
      const result = await dispatch(checkEmployeeAccess(employeeId))
      return result.payload as boolean
    } catch (error) {
      logger.error('useHierarchicalAccessRedux', 'Error checking employee access:', error)
      return false
    }
  }, [dispatch])

  const canAccessDepartment = useCallback(async (departmentId: number): Promise<boolean> => {
    try {
      const result = await dispatch(checkDepartmentAccess(departmentId))
      return result.payload as boolean
    } catch (error) {
      logger.error('useHierarchicalAccessRedux', 'Error checking department access:', error)
      return false
    }
  }, [dispatch])

  const canAccessProject = useCallback(async (projectId: number): Promise<boolean> => {
    try {
      const result = await dispatch(checkProjectAccess(projectId))
      return result.payload as boolean
    } catch (error) {
      logger.error('useHierarchicalAccessRedux', 'Error checking project access:', error)
      return false
    }
  }, [dispatch])

  // Clear cache
  const clearCacheData = useCallback(() => {
    logger.info('useHierarchicalAccessRedux', 'Clearing hierarchical access cache')
    dispatch(clearCache())
  }, [dispatch])

  return {
    // Access information
    accessInfo,
    accessSummary,
    hierarchicalPath,
    kpiCategories,
    
    // Access checks
    canAccessAllData,
    isManager,
    hasHierarchicalAccess,
    isDepartmentScoped,
    isProjectScoped,
    
    // Data access
    accessibleEmployees,
    accessibleDepartments,
    accessibleProjects,
    
    // Filters
    roleBasedKPIFilters,
    
    // State management
    loading: hierarchicalAccessState.loading,
    error: hierarchicalAccessState.error,
    isInitialized: hierarchicalAccessState.isInitialized,
    
    // Actions
    refreshAccessInfo,
    canAccessEmployee,
    canAccessDepartment,
    canAccessProject,
    clearCacheData
  }
}

export default useHierarchicalAccessRedux
