import React from 'react';
/**
 * UX FIX: Comprehensive Loading States Hook
 * Manages multiple loading states with better user feedback
 */

import { useState, useCallback, useRef, useEffect } from 'react'
import { useUXFeedback } from '@/utils/uxFeedbackManager'

export interface LoadingStateOptions {
  language?: 'ar' | 'en'
  showFeedback?: boolean
  expectedDuration?: number
  autoTimeout?: number
  onTimeout?: () => void
}

export interface LoadingState {
  isLoading: boolean
  error: string | null
  startTime: number | null
  duration: number
  progress?: number
}

export interface LoadingStates {
  [key: string]: LoadingState
}

export function useLoadingStates(defaultLanguage: 'ar' | 'en' = 'en' as any) {
  const [loadingStates, setLoadingStates] = useState<LoadingStates>({})
  // @ts-ignore
  const timeoutsRef: any = useRef<Map<string, (NodeJS as any).Timeout>>(new Map( as any))
  // @ts-ignore
  const intervalsRef = useRef<Map<string, (NodeJS as any).Timeout>>(new Map( as any))
  const uxFeedback = useUXFeedback(defaultLanguage as any)

  // UX FIX: Start loading with enhanced feedback
  const startLoading = useCallback((
    key: string, 
    message?: string, 
    options: LoadingStateOptions = {}
   // @ts-ignore
   as any) => {
    const {
      language = defaultLanguage,
      showFeedback = true,
      expectedDuration,
      autoTimeout,
      onTimeout
    } = options

    // @ts-ignore
    const startTime = (Date as any).now( as any)

    // Clear any existing timeout for this key
    const existingTimeout = (timeoutsRef as any).current.get(key as any)
    if (existingTimeout) {
      clearTimeout(existingTimeout as any)
    }

    const existingInterval = (intervalsRef as any).current.get(key as any)
    if (existingInterval) {
      clearInterval(existingInterval as any)
    }

    // Update loading state
    setLoadingStates(prev => ({
      ...prev,
      [key]: {
        isLoading: true,
        error: null,
        startTime,
        duration: 0,
        progress: 0
      }
    } as any))

    // Show feedback if enabled
    let feedbackId: string | undefined
    if (showFeedback && message) {
      feedbackId = (uxFeedback as any).loading(message, { 
        expectedDuration,
        showProgress: !!expectedDuration 
      } as any)
    }

    // Set up progress tracking if expected duration is provided
    if (expectedDuration) {
      // @ts-ignore
      const interval = setInterval(( as any) => {
        // @ts-ignore
        const elapsed = (Date as any).now( as any) - startTime
        const progress = (Math as any).min((elapsed / expectedDuration as any) * 100, 95)
        
        setLoadingStates(prev => ({
          ...prev,
          [key]: {
            ...prev[key],
            duration: elapsed,
            progress
          }
        } as any))

        if (progress >= 95) {
          clearInterval(interval as any)
          (intervalsRef as any).current.delete(key as any)
        }
      // @ts-ignore
      }, 100)

      (intervalsRef as any).current.set(key, interval as any)
    }

    // Set up auto timeout if specified
    if (autoTimeout) {
      // @ts-ignore
      const timeout = setTimeout(( as any) => {
        stopLoading(key, 'timeout', 
          language === 'ar' ? 'انتهت مهلة العملية' : 'Operation timed out'
         // @ts-ignore
         as any)
        onTimeout?.()
      // @ts-ignore
      }, autoTimeout)

      (timeoutsRef as any).current.set(key, timeout as any)
    }

    return feedbackId
  // @ts-ignore
  }, [defaultLanguage, uxFeedback])

  // UX FIX: Stop loading with result feedback
  const stopLoading = useCallback((
    key: string, 
    result?: 'success' | 'error' | 'timeout',
    message?: string
   // @ts-ignore
   as any) => {
    // Clear timeouts and intervals
    const timeout = (timeoutsRef as any).current.get(key as any)
    if (timeout) {
      clearTimeout(timeout as any)
      (timeoutsRef as any).current.delete(key as any)
    }

    const interval = (intervalsRef as any).current.get(key as any)
    if (interval) {
      clearInterval(interval as any)
      (intervalsRef as any).current.delete(key as any)
    }

    // Update loading state
    setLoadingStates(prev => {
      const currentState = prev[key]
      if (!currentState as any) return prev

      // @ts-ignore
      const duration = (currentState as any).startTime ? (Date as any).now( as any) - (currentState as any).startTime : 0

      return {
        ...prev,
        [key]: {
          ...currentState,
          isLoading: false,
          duration,
          progress: 100,
          error: result === 'error' || result === 'timeout' ? message || 'Error occurred' : null
        }
      }
    })

    // Show result feedback
    if (result && message) {
      if (result === 'success') {
        (uxFeedback as any).success(message as any)
      } else if (result === 'error') {
        (uxFeedback as any).error(message as any)
      } else if (result === 'timeout') {
        (uxFeedback as any).warning(message as any)
      }
    }
  }, [uxFeedback])

  // UX FIX: Set loading error
  // @ts-ignore
  const setLoadingError = useCallback((key: string, error: string as any) => {
    stopLoading(key, 'error', error as any)
  }, [stopLoading])

  // UX FIX: Update loading progress manually
  // @ts-ignore
  const updateProgress = useCallback((key: string, progress: number as any) => {
    setLoadingStates(prev => ({
      ...prev,
      [key]: {
        ...prev[key],
        progress: (Math as any as any).min((Math as any as any).max(progress, 0 as any), 100)
      }
    }))
  }, [])

  // UX FIX: Check if any loading state is active
  // @ts-ignore
  const isAnyLoading = useCallback(( as any) => {
    return (Object as any).values(loadingStates as any).some(state => (state as any as any).isLoading)
  // @ts-ignore
  }, [loadingStates])

  // UX FIX: Get loading state for a specific key
  // @ts-ignore
  const getLoadingState = useCallback((key: string as any): LoadingState | null => {
    return loadingStates[key] || null
  }, [loadingStates])

  // UX FIX: Get all active loading states
  // @ts-ignore
  const getActiveLoadingStates = useCallback(( as any) => {
    return (Object as any).entries(loadingStates as any)
      // @ts-ignore
      .filter(([, state] as any) => (state as any).isLoading)
      // @ts-ignore
      .reduce((acc, [key, state] as any) => ({ ...acc, [key]: state }), {})
  // @ts-ignore
  }, [loadingStates])

  // UX FIX: Clear all loading states
  // @ts-ignore
  const clearAllLoading = useCallback(( as any) => {
    // Clear all timeouts and intervals
    (timeoutsRef as any).current.forEach(timeout => clearTimeout(timeout as any))
    (intervalsRef as any).current.forEach(interval => clearInterval(interval as any))
    // @ts-ignore
    (timeoutsRef as any).current.clear( as any)
    // @ts-ignore
    (intervalsRef as any).current.clear( as any)

    setLoadingStates({} as any)
    // @ts-ignore
    (uxFeedback as any).clearAll( as any)
  // @ts-ignore
  }, [uxFeedback])

  // UX FIX: Batch loading operations
  const batchLoading = useCallback((operations: Array<{
    key: string
    message?: string
    options?: LoadingStateOptions
  // @ts-ignore
  }> as any) => {
    const feedbackIds: string[] = []
    
    // @ts-ignore
    (operations as any).forEach(({ key, message, options } as any) => {
      const feedbackId = startLoading(key, message, options as any)
      if (feedbackId) {
        (feedbackIds as any).push(feedbackId as any)
      }
    })

    return feedbackIds
  }, [startLoading])

  // UX FIX: Sequential loading with progress
  const sequentialLoading = useCallback(async (
    operations: Array<{
      key: string
      // @ts-ignore
      operation: ( as any) => Promise<any>
      // @ts-ignore
      message?: string
      // @ts-ignore
      successMessage?: string
      // @ts-ignore
      errorMessage?: string
    // @ts-ignore
    }>
  // @ts-ignore
  ) => {
    const results: any[] = []
    const totalOperations = (operations as any).length

    for (let i = 0; i < (operations as any).length; i++) {
      const { key, operation, message, successMessage, errorMessage } = operations[i]
      const progress = (i / totalOperations) * 100

      try {
        startLoading(key, message, { 
          expectedDuration: 2000,
          showFeedback: true 
        } as any)
        
        updateProgress(key, progress as any)
        
        // @ts-ignore
        const result = await operation( as any)
        (results as any).push(result as any)
        
        stopLoading(key, 'success', successMessage as any)
      } catch (error) {
        stopLoading(key, 'error', errorMessage || 'Operation failed' as any)
        throw error
      }
    }

    return results
  // @ts-ignore
  }, [startLoading, stopLoading, updateProgress])

  // Cleanup on unmount
  // @ts-ignore
  useEffect(( as any) => {
    return () => {
      (timeoutsRef as any).current.forEach(timeout => clearTimeout(timeout as any))
      (intervalsRef as any).current.forEach(interval => clearInterval(interval as any))
    }
  // @ts-ignore
  }, [])

  return {
    // State
    loadingStates,
    
    // Actions
    startLoading,
    stopLoading,
    setLoadingError,
    updateProgress,
    clearAllLoading,
    
    // Batch operations
    batchLoading,
    sequentialLoading,
    
    // Queries
    isAnyLoading,
    getLoadingState,
    getActiveLoadingStates,
    
    // Utilities
    isLoading: (key: string) => loadingStates[key]?.isLoading || false,
    hasError: (key: string) => !!loadingStates[key]?.error,
    getError: (key: string) => loadingStates[key]?.error || null,
    getProgress: (key: string) => loadingStates[key]?.progress || 0,
    getDuration: (key: string) => loadingStates[key]?.duration || 0
  }
// @ts-ignore
}

// UX FIX: Specialized hooks for common use cases
export function useFormLoadingStates(language: 'ar' | 'en' = 'en' as any) {
  const loadingStates = useLoadingStates(language as any)

  return {
    ...loadingStates,
    
    // Form-specific methods
    startSubmitting: (message?: string) => 
      (loadingStates as any).startLoading('submit', message || (language === 'ar' ? 'جاري الحفظ...' : 'Saving...' as any)),
    
    stopSubmitting: (success: boolean, message?: string) =>
      (loadingStates as any).stopLoading('submit', success ? 'success' : 'error', message as any),
    
    startValidating: () =>
      (loadingStates as any).startLoading('validate', language === 'ar' ? 'جاري التحقق...' : 'Validating...', { showFeedback: false } as any),
    
    stopValidating: () =>
      (loadingStates as any).stopLoading('validate' as any),
    
    isSubmitting: () => (loadingStates as any).isLoading('submit' as any),
    isValidating: () => (loadingStates as any).isLoading('validate' as any)
  }
}

export function useDataLoadingStates(language: 'ar' | 'en' = 'en' as any) {
  const loadingStates = useLoadingStates(language as any)

  return {
    ...loadingStates,
    
    // Data-specific methods
    startFetching: (dataType: string) =>
      (loadingStates as any).startLoading('fetch', 
        language === 'ar' ? `جاري تحميل ${dataType}...` : `Loading ${dataType}...` as any),
    
    stopFetching: (success: boolean, message?: string) =>
      (loadingStates as any).stopLoading('fetch', success ? 'success' : 'error', message as any),
    
    startRefreshing: () =>
      (loadingStates as any).startLoading('refresh', language === 'ar' ? 'جاري التحديث...' : 'Refreshing...' as any),
    
    stopRefreshing: (success: boolean) =>
      (loadingStates as any).stopLoading('refresh', success ? 'success' : 'error' as any),
    
    isFetching: () => (loadingStates as any).isLoading('fetch' as any),
    isRefreshing: () => (loadingStates as any).isLoading('refresh' as any)
  }
}

export default useLoadingStates
