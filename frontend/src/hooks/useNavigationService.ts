/**
 * React Hook for Navigation Service Integration
 * Connects React Router with our centralized navigation service
 */

import { useEffect } from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import { navigationService } from '../services/navigationService'

/**
 * Hook to initialize navigation service with React Router
 * Call this in your main App component
 */
export function useNavigationServiceInit() {
  const navigate = useNavigate()
  const location = useLocation()

  useEffect(() => {
    // Initialize navigation service with React Router's navigate function
    navigationService.setNavigate(navigate)
  }, [navigate])

  useEffect(() => {
    // Listen for navigation events from the service
    const handleNavigationEvent = (event: CustomEvent) => {
      const { path } = event.detail
      navigate(path)
    }

    const handleRefreshEvent = () => {
      // Force re-render by navigating to current path
      navigate(location.pathname, { replace: true })
    }

    const handleForceRefreshEvent = () => {
      // Force complete refresh by navigating to current path
      navigate(location.pathname, { replace: true, state: { forceRefresh: Date.now() } })
    }

    // Add event listeners
    window.addEventListener('app:navigate', handleNavigationEvent as EventListener)
    window.addEventListener('app:refresh', handleRefreshEvent)
    window.addEventListener('app:force-refresh', handleForceRefreshEvent)

    // Cleanup
    return () => {
      window.removeEventListener('app:navigate', handleNavigationEvent as EventListener)
      window.removeEventListener('app:refresh', handleRefreshEvent)
      window.removeEventListener('app:force-refresh', handleForceRefreshEvent)
    }
  }, [navigate, location.pathname])
}

/**
 * Hook to use navigation service in components
 */
export function useNavigation() {
  return {
    navigateTo: navigationService.navigateTo.bind(navigationService),
    navigateToLogin: navigationService.navigateToLogin.bind(navigationService),
    navigateToHome: navigationService.navigateToHome.bind(navigationService),
    goBack: navigationService.goBack.bind(navigationService),
    goForward: navigationService.goForward.bind(navigationService),
    refreshPage: navigationService.refreshPage.bind(navigationService),
    openExternal: navigationService.openExternal.bind(navigationService),
    handleAuthRedirect: navigationService.handleAuthRedirect.bind(navigationService),
    handleError: navigationService.handleError.bind(navigationService),
    forceRefresh: navigationService.forceRefresh.bind(navigationService),
    getCurrentPath: navigationService.getCurrentPath.bind(navigationService),
    isCurrentPath: navigationService.isCurrentPath.bind(navigationService)
  }
}

export default useNavigation
