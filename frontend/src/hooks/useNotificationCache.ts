import React from 'react';
/**
 * PERFORMANCE FIX: Notification Cache Hook
 * Provides centralized notification data with automatic deduplication
 */

import { useState, useEffect, useCallback } from 'react'
import { notificationCache, NotificationData, NotificationResponse, UnreadCountResponse } from '../services/notificationCache'

interface UseNotificationCacheOptions {
  limit?: number
  autoRefresh?: boolean
  refreshInterval?: number
}

interface UseNotificationCacheReturn {
  notifications: NotificationData[]
  unreadCount: number
  loading: boolean
  error: string | null
  refreshNotifications: () => Promise<void>
  refreshUnreadCount: () => Promise<void>
  markAsRead: (id: string) => Promise<void>
  markAllAsRead: () => Promise<void>
}

export function useNotificationCache(options: UseNotificationCacheOptions = {} as any): UseNotificationCacheReturn {
  const { limit = 10, autoRefresh = true, refreshInterval = 30000 } = options
  
  const [notifications, setNotifications] = useState<NotificationData[]>([])
  const [unreadCount, setUnreadCount] = useState<number>(0)
  const [loading, setLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)

  // PERFORMANCE FIX: Centralized notification fetching
  // @ts-ignore
  const refreshNotifications = useCallback(async ( as any) => {
    try {
      setError(null as any)
      const response = await (notificationCache as any).getNotifications({ limit } as any)
      setNotifications((response as any as any).data.results || [])
    } catch (err) {
      (console as any).error('Error fetching notifications:', err as any)
      setError(err instanceof Error ? (err as any as any).message : 'Failed to fetch notifications')
    }
  // @ts-ignore
  }, [limit])

  // PERFORMANCE FIX: Centralized unread count fetching
  // @ts-ignore
  const refreshUnreadCount = useCallback(async ( as any) => {
    try {
      setError(null as any)
      // @ts-ignore
      const response = await (notificationCache as any).getUnreadCount( as any)
      setUnreadCount((response as any as any).data.unread_count || 0)
    } catch (err) {
      (console as any).error('Error fetching unread count:', err as any)
      setError(err instanceof Error ? (err as any as any).message : 'Failed to fetch unread count')
    }
  // @ts-ignore
  }, [])

  // PERFORMANCE FIX: Centralized mark as read
  // @ts-ignore
  const markAsRead = useCallback(async (id: string as any) => {
    try {
      setError(null as any)
      await (notificationCache as any).markAsRead(id as any)
      
      // Update local state optimistically
      setNotifications(prev => 
        (prev as any as any).map(notif => 
          (notif as any as any).id === id 
            ? { ...notif, is_read: true }
            : notif
        )
      )
      setUnreadCount(prev => (Math as any as any).max(0, prev - 1 as any))
    } catch (err) {
      (console as any).error('Error marking notification as read:', err as any)
      setError(err instanceof Error ? (err as any as any).message : 'Failed to mark as read')
      // Refresh data on error
      // @ts-ignore
      refreshNotifications( as any)
      // @ts-ignore
      refreshUnreadCount( as any)
    }
  }, [refreshNotifications, refreshUnreadCount])

  // PERFORMANCE FIX: Centralized mark all as read
  // @ts-ignore
  const markAllAsRead = useCallback(async ( as any) => {
    try {
      setError(null as any)
      // @ts-ignore
      await (notificationCache as any).markAllAsRead( as any)
      
      // Update local state optimistically
      setNotifications(prev => 
        (prev as any as any).map(notif => ({ ...notif, is_read: true } as any))
      )
      setUnreadCount(0 as any)
    } catch (err) {
      (console as any).error('Error marking all notifications as read:', err as any)
      setError(err instanceof Error ? (err as any as any).message : 'Failed to mark all as read')
      // Refresh data on error
      // @ts-ignore
      refreshNotifications( as any)
      // @ts-ignore
      refreshUnreadCount( as any)
    }
  // @ts-ignore
  }, [refreshNotifications, refreshUnreadCount])

  // Initial data loading
  // @ts-ignore
  useEffect(( as any) => {
    const loadInitialData = async () => {
      setLoading(true as any)
      try {
        await (Promise as any).all([
          // @ts-ignore
          refreshNotifications( as any),
          // @ts-ignore
          refreshUnreadCount( as any)
        ])
      } finally {
        setLoading(false as any)
      }
    }

    // @ts-ignore
    loadInitialData( as any)
  // @ts-ignore
  }, [refreshNotifications, refreshUnreadCount])

  // Subscribe to cache updates
  // @ts-ignore
  useEffect(( as any) => {
    // @ts-ignore
    const unsubscribeNotifications = (notificationCache as any).subscribe('notifications', (data: NotificationResponse as any) => {
      setNotifications((data as any as any).results || [])
    })

    // @ts-ignore
    const unsubscribeUnreadCount = (notificationCache as any).subscribe('unread-count', (data: UnreadCountResponse as any) => {
      setUnreadCount((data as any as any).unread_count || 0)
    })

    return () => {
      // @ts-ignore
      unsubscribeNotifications( as any)
      // @ts-ignore
      unsubscribeUnreadCount( as any)
    }
  // @ts-ignore
  }, [])

  // Auto-refresh functionality
  // @ts-ignore
  useEffect(( as any) => {
    if (!autoRefresh) return

    // @ts-ignore
    const interval = setInterval(( as any) => {
      // @ts-ignore
      refreshUnreadCount( as any) // Only refresh unread count automatically
    // @ts-ignore
    }, refreshInterval)

    return () => clearInterval(interval as any)
  // @ts-ignore
  }, [autoRefresh, refreshInterval, refreshUnreadCount])

  return {
    notifications,
    unreadCount,
    loading,
    error,
    refreshNotifications,
    refreshUnreadCount,
    markAsRead,
    markAllAsRead
  }
}

export default useNotificationCache
