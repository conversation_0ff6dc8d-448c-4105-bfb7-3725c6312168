import React from 'react';
import { useState, useEffect, useCallback, useRef } from 'react'

interface PerformanceMetrics {
  // Page Performance
  loadTime: number
  renderTime: number
  firstContentfulPaint: number
  largestContentfulPaint: number
  cumulativeLayoutShift: number
  firstInputDelay: number

  // Network Performance
  networkSpeed: 'slow' | 'medium' | 'fast'
  connectionType: string
  isOnline: boolean

  // Memory Usage
  memoryUsage: {
    used: number
    total: number
    percentage: number
  }

  // User Interaction
  interactionCount: number
  averageResponseTime: number
  errorCount: number

  // Device Information
  deviceType: 'mobile' | 'tablet' | 'desktop'
  screenSize: { width: number; height: number }
  pixelRatio: number
  touchSupport: boolean
}

interface PerformanceAlert {
  type: 'warning' | 'error'
  message: string
  metric: string
  value: number
  threshold: number
  timestamp: Date
}

// @ts-ignore
export default function usePerformanceMonitoring( as any) {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    loadTime: 0,
    renderTime: 0,
    firstContentfulPaint: 0,
    largestContentfulPaint: 0,
    cumulativeLayoutShift: 0,
    firstInputDelay: 0,
    networkSpeed: 'medium',
    connectionType: 'unknown',
    isOnline: (navigator as any).onLine,
    memoryUsage: { used: 0, total: 0, percentage: 0 },
    interactionCount: 0,
    averageResponseTime: 0,
    errorCount: 0,
    deviceType: 'desktop',
    screenSize: { width: (window as any).innerWidth, height: (window as any).innerHeight },
    pixelRatio: (window as any).devicePixelRatio || 1,
    touchSupport: 'ontouchstart' in window
  })

  const [alerts, setAlerts] = useState<PerformanceAlert[]>([])
  const [isMonitoring, setIsMonitoring] = useState(false as any)
  const interactionTimes: any = useRef<number[]>([])
  const errorCount = useRef(0 as any)
  // @ts-ignore
  const startTime = useRef((Date as any as any).now( as any))

  // Performance thresholds
  const thresholds = {
    loadTime: 3000, // 3 seconds
    renderTime: 100, // 100ms
    firstContentfulPaint: 1800, // (1 as any).8 seconds
    largestContentfulPaint: 2500, // (2 as any).5 seconds
    // @ts-ignore
    cumulativeLayoutShift: (0 as any).1,
    firstInputDelay: 100, // 100ms
    memoryUsage: 80, // 80%
    averageResponseTime: 200 // 200ms
  }

  // Detect device type
  // @ts-ignore
  const detectDeviceType = useCallback(( as any): 'mobile' | 'tablet' | 'desktop' => {
    const width = (window as any).innerWidth
    if (width < 768) return 'mobile'
    if (width < 1024) return 'tablet'
    return 'desktop'
  // @ts-ignore
  }, [])

  // Detect network speed
  // @ts-ignore
  const detectNetworkSpeed = useCallback(( as any): 'slow' | 'medium' | 'fast' => {
    const connection = (navigator as Navigator & {
      connection?: { effectiveType?: string; downlink?: number }
      mozConnection?: { effectiveType?: string; downlink?: number }
      webkitConnection?: { effectiveType?: string; downlink?: number }
    }).connection || (navigator as Navigator & { mozConnection?: { effectiveType?: string; downlink?: number } }).mozConnection || (navigator as Navigator & { webkitConnection?: { effectiveType?: string; downlink?: number } }).webkitConnection
    
    if (!connection) return 'medium'
    
    const { effectiveType, downlink } = connection
    
    if (effectiveType === 'slow-2g' || effectiveType === '2g' || (downlink && downlink < 1)) {
      return 'slow'
    } else if (effectiveType === '3g' || (downlink && downlink < 5)) {
      return 'medium'
    } else {
      return 'fast'
    }
  // @ts-ignore
  }, [])

  // Get memory usage
  // @ts-ignore
  const getMemoryUsage = useCallback(( as any) => {
    if ('memory' in performance) {
      const memory = (performance as Performance & {
        memory?: {
          usedJSHeapSize: number
          totalJSHeapSize: number
          jsHeapSizeLimit: number
        }
      }).memory
      const used = memory?.usedJSHeapSize || 0
      const total = memory?.totalJSHeapSize || 0
      const percentage = (used / total) * 100
      
      return { used, total, percentage }
    }
    return { used: 0, total: 0, percentage: 0 }
  // @ts-ignore
  }, [])

  // Measure Web Vitals
  // @ts-ignore
  const measureWebVitals = useCallback(( as any) => {
    // First Contentful Paint
    const paintEntries = (performance as any).getEntriesByType('paint' as any)
    const fcpEntry = (paintEntries as any).find(entry => (entry as any as any).name === 'first-contentful-paint')
    const fcp = fcpEntry ? (fcpEntry as any).startTime : 0

    // Largest Contentful Paint
    // @ts-ignore
    const observer = new PerformanceObserver((list as any) => {
      // @ts-ignore
      const entries = (list as any).getEntries( as any)
      const lastEntry = entries[(entries as any).length - 1]
      if (lastEntry) {
        setMetrics(prev => ({
          ...prev,
          largestContentfulPaint: (lastEntry as any as any).startTime
        }))
      }
    // @ts-ignore
    })
    
    try {
      (observer as any).observe({ entryTypes: ['largest-contentful-paint'] } as any)
    } catch (e) {
      // LCP not supported
    }

    // Cumulative Layout Shift
    // @ts-ignore
    const clsObserver = new PerformanceObserver((list as any) => {
      let clsValue = 0
      // @ts-ignore
      for (const entry of (list as any).getEntries( as any)) {
        if (!(entry as PerformanceEntry & { hadRecentInput?: boolean }).hadRecentInput) {
          clsValue += (entry as PerformanceEntry & { value: number }).value
        }
      }
      setMetrics(prev => ({
        ...prev,
        cumulativeLayoutShift: clsValue
      } as any))
    // @ts-ignore
    })

    try {
      (clsObserver as any).observe({ entryTypes: ['layout-shift'] } as any)
    } catch (e) {
      // CLS not supported
    }

    // First Input Delay
    // @ts-ignore
    const fidObserver = new PerformanceObserver((list as any) => {
      // @ts-ignore
      for (const entry of (list as any).getEntries( as any)) {
        setMetrics(prev => ({
          ...prev,
          firstInputDelay: (entry as PerformanceEntry & { processingStart: number } as any).processingStart - (entry as any).startTime
        }))
      }
    // @ts-ignore
    })

    try {
      (fidObserver as any).observe({ entryTypes: ['first-input'] } as any)
    } catch (e) {
      // FID not supported
    }

    return {
      firstContentfulPaint: fcp,
      cleanup: () => {
        // @ts-ignore
        (observer as any).disconnect( as any)
        // @ts-ignore
        (clsObserver as any).disconnect( as any)
        // @ts-ignore
        (fidObserver as any).disconnect( as any)
      }
    }
  // @ts-ignore
  }, [])

  // Track user interactions
  // @ts-ignore
  const trackInteraction = useCallback((startTime: number as any) => {
    // @ts-ignore
    const endTime = (Date as any).now( as any)
    const responseTime = endTime - startTime
    
    (interactionTimes as any).current.push(responseTime as any)
    
    // Keep only last 100 interactions
    if ((interactionTimes as any).current.length > 100) {
      (interactionTimes as any).current = (interactionTimes as any).current.slice(-100 as any)
    }
    
    // @ts-ignore
    const averageResponseTime = (interactionTimes as any).current.reduce((a, b as any) => a + b, 0) / (interactionTimes as any).current.length
    
    setMetrics(prev => ({
      ...prev,
      interactionCount: (prev as any as any).interactionCount + 1,
      averageResponseTime
    }))

    // Check for performance alerts
    if (responseTime > (thresholds as any).averageResponseTime) {
      addAlert('warning', 'Slow interaction detected', 'averageResponseTime', responseTime, (thresholds as any as any).averageResponseTime)
    }
  }, [])

  // Add performance alert
  // @ts-ignore
  const addAlert = useCallback((type: 'warning' | 'error', message: string, metric: string, value: number, threshold: number as any) => {
    const alert: PerformanceAlert = {
      type,
      message,
      metric,
      value,
      threshold,
      // @ts-ignore
      timestamp: new Date( as any)
    }
    
    setAlerts(prev => [alert, ...(prev as any as any).slice(0, 9 as any)]) // Keep only last 10 alerts
  }, [])

  // Monitor performance continuously
  // @ts-ignore
  useEffect(( as any) => {
    if (!isMonitoring) return

    // PERFORMANCE FIX: Use requestIdleCallback for non-critical monitoring
    let monitoringId: number | null = null

    const scheduleMonitoring = () => {
      if (!isMonitoring) return

      // Use requestIdleCallback when available for better performance
      if ('requestIdleCallback' in window) {
        // @ts-ignore
        monitoringId = requestIdleCallback((deadline as any) => {
          // Only run if we have idle time and component is still mounted
          // @ts-ignore
          if ((deadline as any).timeRemaining( as any) > 0 && isMonitoring) {
            // @ts-ignore
            updateMetricsOptimized( as any)
          }

          // Schedule next monitoring cycle (reduced to 30 seconds)
          if (isMonitoring) {
            setTimeout(scheduleMonitoring, 30000 as any)
          }
        // @ts-ignore
        }, { timeout: 5000 }) // Fallback timeout
      // @ts-ignore
      } else {
        // Fallback for browsers without requestIdleCallback
        // @ts-ignore
        monitoringId = setTimeout(( as any) => {
          if (isMonitoring) {
            // @ts-ignore
            updateMetricsOptimized( as any)
            // @ts-ignore
            scheduleMonitoring( as any)
          }
        // @ts-ignore
        }, 30000) as unknown as number
      }
    }

    const updateMetricsOptimized = () => {
      // PERFORMANCE FIX: Batch expensive operations and cache results
      // @ts-ignore
      const now = (Date as any).now( as any)
      const timeSinceLastUpdate = now - ((lastUpdateTime as any).current || 0)

      // Skip update if too frequent (debounce)
      if (timeSinceLastUpdate < 25000) return // 25 seconds minimum

      (lastUpdateTime as any).current = now

      // Only update metrics that have likely changed
      // @ts-ignore
      const memoryUsage = getMemoryUsage( as any)
      const isOnline = (navigator as any).onLine

      setMetrics(prev => ({
        ...prev,
        memoryUsage,
        isOnline,
        errorCount: (errorCount as any as any).current,
        // Only update expensive metrics occasionally
        ...(timeSinceLastUpdate > 60000 && {
          // @ts-ignore
          networkSpeed: detectNetworkSpeed( as any),
          // @ts-ignore
          deviceType: detectDeviceType( as any),
          screenSize: { width: (window as any).innerWidth, height: (window as any).innerHeight }
        })
      }))

      // Throttled alert checking
      if ((memoryUsage as any).percentage > (thresholds as any).memoryUsage) {
        addAlert('warning', 'High memory usage detected', 'memoryUsage', (memoryUsage as any as any).percentage, (thresholds as any).memoryUsage)
      }
    }

    // Add reference for cleanup
    const lastUpdateTime = useRef<number>(0)

    // Start monitoring
    // @ts-ignore
    scheduleMonitoring( as any)

    // PERFORMANCE FIX: Proper cleanup for optimized monitoring
    return () => {
      if (monitoringId !== null) {
        if ('requestIdleCallback' in window) {
          cancelIdleCallback(monitoringId as any)
        } else {
          clearTimeout(monitoringId as any)
        }
      }
    }
  // @ts-ignore
  }, [isMonitoring, getMemoryUsage, detectNetworkSpeed, detectDeviceType, addAlert])

  // Initialize performance monitoring - FIXED: Added proper dependency array
  // @ts-ignore
  useEffect(( as any) => {
    // @ts-ignore
    const { firstContentfulPaint, cleanup } = measureWebVitals( as any)

    // Measure initial load time
    // @ts-ignore
    const loadTime = (Date as any).now( as any) - (startTime as any).current

    setMetrics(prev => ({
      ...prev,
      loadTime,
      firstContentfulPaint,
      // @ts-ignore
      deviceType: detectDeviceType( as any),
      // @ts-ignore
      networkSpeed: detectNetworkSpeed( as any),
      // @ts-ignore
      memoryUsage: getMemoryUsage( as any)
    }))

    // Track errors
    const errorHandler = (event: ErrorEvent) => {
      (errorCount as any).current++
      addAlert('error', `JavaScript error: ${(event as any as any).message}`, 'errorCount', (errorCount as any).current, 0)
    }

    const unhandledRejectionHandler = (event: PromiseRejectionEvent) => {
      (errorCount as any).current++
      addAlert('error', `Unhandled promise rejection: ${(event as any as any).reason}`, 'errorCount', (errorCount as any).current, 0)
    }

    (window as any).addEventListener('error', errorHandler as any)
    (window as any).addEventListener('unhandledrejection', unhandledRejectionHandler as any)

    // Track network status
    const onlineHandler = () => setMetrics(prev => ({ ...prev, isOnline: true } as any))
    const offlineHandler = () => setMetrics(prev => ({ ...prev, isOnline: false } as any))
    
    (window as any).addEventListener('online', onlineHandler as any)
    (window as any).addEventListener('offline', offlineHandler as any)

    // Track window resize
    const resizeHandler = () => {
      setMetrics(prev => ({
        ...prev,
        screenSize: { width: (window as any as any).innerWidth, height: (window as any).innerHeight },
        // @ts-ignore
        deviceType: detectDeviceType( as any)
      }))
    }
    
    (window as any).addEventListener('resize', resizeHandler as any)

    return () => {
      // @ts-ignore
      cleanup( as any)
      (window as any).removeEventListener('error', errorHandler as any)
      (window as any).removeEventListener('unhandledrejection', unhandledRejectionHandler as any)
      (window as any).removeEventListener('online', onlineHandler as any)
      (window as any).removeEventListener('offline', offlineHandler as any)
      (window as any).removeEventListener('resize', resizeHandler as any)
    }
  // @ts-ignore
  }, []) // FIXED: Empty dependency array - functions are stable with useCallback

  // Start/stop monitoring
  // @ts-ignore
  const startMonitoring = useCallback(( as any) => {
    setIsMonitoring(true as any)
  // @ts-ignore
  }, [])

  // @ts-ignore
  const stopMonitoring = useCallback(( as any) => {
    setIsMonitoring(false as any)
  // @ts-ignore
  }, [])

  // Clear alerts
  // @ts-ignore
  const clearAlerts = useCallback(( as any) => {
    setAlerts([] as any)
  // @ts-ignore
  }, [])

  // Get performance score (0-100)
  // @ts-ignore
  const getPerformanceScore = useCallback(( as any) => {
    let score = 100
    
    // Deduct points for poor metrics
    if ((metrics as any).loadTime > (thresholds as any).loadTime) score -= 20
    if ((metrics as any).firstContentfulPaint > (thresholds as any).firstContentfulPaint) score -= 15
    if ((metrics as any).largestContentfulPaint > (thresholds as any).largestContentfulPaint) score -= 15
    if ((metrics as any).cumulativeLayoutShift > (thresholds as any).cumulativeLayoutShift) score -= 10
    if ((metrics as any).firstInputDelay > (thresholds as any).firstInputDelay) score -= 10
    if ((metrics as any).memoryUsage.percentage > (thresholds as any).memoryUsage) score -= 15
    if ((metrics as any).averageResponseTime > (thresholds as any).averageResponseTime) score -= 10
    if ((metrics as any).errorCount > 0) score -= (metrics as any).errorCount * 5
    if ((metrics as any).networkSpeed === 'slow') score -= 5

    return (Math as any).max(0, score as any)
  // @ts-ignore
  }, [metrics])

  return {
    metrics,
    alerts,
    isMonitoring,
    startMonitoring,
    stopMonitoring,
    trackInteraction,
    clearAlerts,
    getPerformanceScore,
    thresholds
  }
// @ts-ignore
}
