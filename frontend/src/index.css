@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Modern Glass Morphism EMS Styles */

/* Body with Gradient Background */
body {
  margin: 0;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-attachment: fixed;
  color: #ffffff;
  font-family: 'Cairo', 'Tajawal', system-ui, sans-serif;
  font-weight: 400;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Glass Morphism Effects */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

/* Modern Button Styles */
.modern-button {
  background: rgba(255, 255, 255, 0.12);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.25);
  border-radius: 12px;
  box-shadow: 0 8px 24px 0 rgba(31, 38, 135, 0.3);
  transition: all 0.3s ease;
  color: #ffffff;
}

.modern-button:hover {
  background: rgba(255, 255, 255, 0.18);
  border: 1px solid rgba(255, 255, 255, 0.35);
  box-shadow: 0 12px 32px 0 rgba(31, 38, 135, 0.4);
  transform: translateY(-2px);
}

/* Modern Input Styles */
.modern-input {
  background: rgba(255, 255, 255, 0.12);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.25);
  border-radius: 12px;
  box-shadow: 0 8px 24px 0 rgba(31, 38, 135, 0.3);
  color: #ffffff;
  transition: all 0.3s ease;
}

.modern-input:focus {
  background: rgba(255, 255, 255, 0.18);
  border: 1px solid rgba(255, 255, 255, 0.45);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.4);
  outline: none;
}

.modern-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

/* Modern Card Styles */
.modern-card {
  background: rgba(255, 255, 255, 0.12);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.25);
  border-radius: 12px;
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.4);
  transition: all 0.3s ease;
  width: 100%;
  max-width: 100%;
  overflow: hidden;
}

.modern-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px 0 rgba(31, 38, 135, 0.5);
}

/* Responsive Card Styles */
@media (min-width: 640px) {
  .modern-card {
    border-radius: 16px;
    box-shadow: 0 16px 48px 0 rgba(31, 38, 135, 0.4);
  }

  .modern-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 60px 0 rgba(31, 38, 135, 0.5);
  }
}

/* Glass Card Styles - Used in new pages */
.glass-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  box-shadow: 0 4px 16px 0 rgba(31, 38, 135, 0.37);
  transition: all 0.3s ease;
  width: 100%;
  max-width: 100%;
  overflow: hidden;
}

.glass-card:hover {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 8px 24px 0 rgba(31, 38, 135, 0.45);
}

/* Responsive Glass Card Styles */
@media (min-width: 640px) {
  .glass-card {
    border-radius: 12px;
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  }

  .glass-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px 0 rgba(31, 38, 135, 0.45);
  }
}

/* Gradient Backgrounds */
.gradient-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-bg-alt {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.gradient-bg-blue {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.gradient-bg-purple {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

/* Animated Gradients */
.animated-gradient {
  background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Floating Animation */
.floating {
  animation: floating 6s ease-in-out infinite;
}

@keyframes floating {
  0% { transform: translate(0, 0px); }
  50% { transform: translate(0, -10px); }
  100% { transform: translate(0, 0px); }
}

/* Fade In Up Animation */
.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out forwards;
  opacity: 0;
  transform: translateY(30px);
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Animation Delays */
.animation-delay-200 {
  animation-delay: 0.2s;
}

.animation-delay-400 {
  animation-delay: 0.4s;
}

.animation-delay-600 {
  animation-delay: 0.6s;
}

.animation-delay-800 {
  animation-delay: 0.8s;
}

.animation-delay-1000 {
  animation-delay: 1s;
}

.animation-delay-1200 {
  animation-delay: 1.2s;
}

.animation-delay-1400 {
  animation-delay: 1.4s;
}

.animation-delay-1600 {
  animation-delay: 1.6s;
}

/* Text Gradient Animation */
.animate-text-gradient {
  background-size: 300% 300%;
  animation: gradientText 3s ease infinite;
}

@keyframes gradientText {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Gradient X Animation */
.animate-gradient-x {
  background-size: 200% 200%;
  animation: gradientX 4s ease infinite;
}

@keyframes gradientX {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* Scale on Hover */
.hover-scale:hover {
  transform: scale(1.05);
  transition: transform 0.3s ease;
}

/* Bounce Animation */
.animate-bounce-slow {
  animation: bounceSlow 3s infinite;
}

@keyframes bounceSlow {
  0%, 20%, 53%, 80%, 100% {
    animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
    transform: translate3d(0,0,0);
  }
  40%, 43% {
    animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
    transform: translate3d(0, -15px, 0);
  }
  70% {
    animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
    transform: translate3d(0, -7px, 0);
  }
  90% {
    transform: translate3d(0,-2px,0);
  }
}

/* Slide In Animation */
.animate-slide-in-left {
  animation: slideInLeft 0.8s ease-out forwards;
  opacity: 0;
  transform: translateX(-50px);
}

.animate-slide-in-right {
  animation: slideInRight 0.8s ease-out forwards;
  opacity: 0;
  transform: translateX(50px);
}

@keyframes slideInLeft {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Rotate Animation */
.animate-rotate-slow {
  animation: rotateSlow 8s linear infinite;
}

@keyframes rotateSlow {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Blob Animation */
.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

/* Pulse Animation */
.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* Glow Effects */
.glow {
  box-shadow: 0 0 20px rgba(102, 126, 234, 0.6);
}

.glow-hover:hover {
  box-shadow: 0 0 30px rgba(102, 126, 234, 0.8);
  transition: box-shadow 0.3s ease;
}

/* Shimmer Effect */
.shimmer {
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.3) 50%, rgba(255, 255, 255, 0.1) 100%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* Tilt Effect */
.tilt:hover {
  transform: perspective(1000px) rotateX(5deg) rotateY(5deg);
  transition: transform 0.3s ease;
}

/* Neon Glow Text */
.neon {
  text-shadow: 0 0 5px rgba(102, 126, 234, 0.8),
               0 0 10px rgba(102, 126, 234, 0.8),
               0 0 15px rgba(102, 126, 234, 0.8),
               0 0 20px rgba(102, 126, 234, 0.8);
}

/* Text Styling Fixes */
.text-white {
  color: #ffffff !important;
}

.text-white\/90 {
  color: rgba(255, 255, 255, 0.9) !important;
}

.text-white\/80 {
  color: rgba(255, 255, 255, 0.8) !important;
}

.text-white\/70 {
  color: rgba(255, 255, 255, 0.7) !important;
}

.text-white\/60 {
  color: rgba(255, 255, 255, 0.6) !important;
}

/* Better Text Readability */
h1, h2, h3, h4, h5, h6 {
  color: #ffffff;
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 0.5rem;
}

p {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  margin-bottom: 1rem;
}

/* Input Text Fixes */
input, textarea, select {
  color: #ffffff !important;
}

input::placeholder, textarea::placeholder {
  color: rgba(255, 255, 255, 0.6) !important;
}

/* Button Text */
button {
  color: #ffffff;
  font-weight: 500;
}

/* Table Text */
table th, table td {
  color: rgba(255, 255, 255, 0.9);
}

/* Arabic Text Support */
.arabic-text {
  font-family: 'Cairo', 'Tajawal', sans-serif;
  direction: rtl;
  text-align: right;
}

/* RTL Support */
[dir="rtl"] {
  direction: rtl;
  text-align: right;
}

[dir="ltr"] {
  direction: ltr;
  text-align: left;
}

/* Fix Text Contrast Issues */
.modern-input, .modern-button {
  color: #ffffff !important;
}

.modern-input:focus {
  outline: none !important;
}

/* Card Text Fixes */
.modern-card h1, .modern-card h2, .modern-card h3, .modern-card h4, .modern-card h5, .modern-card h6 {
  color: #ffffff !important;
}

.modern-card p, .modern-card span, .modern-card div {
  color: rgba(255, 255, 255, 0.9) !important;
}

/* Table Text Visibility */
.modern-card table {
  color: rgba(255, 255, 255, 0.9) !important;
}

.modern-card table th {
  color: #ffffff !important;
  font-weight: 600;
}

.modern-card table td {
  color: rgba(255, 255, 255, 0.85) !important;
}

/* Button Text Visibility */
.modern-button, .modern-card button {
  color: #ffffff !important;
}

/* Label Text */
label {
  color: rgba(255, 255, 255, 0.9) !important;
  font-weight: 500;
}

/* Link Text */
a {
  color: rgba(255, 255, 255, 0.9) !important;
}

a:hover {
  color: #ffffff !important;
}

/* Badge and Status Text */
.badge, .status {
  color: #ffffff !important;
  font-weight: 500;
}

/* Ensure all text elements are visible */
* {
  color: inherit;
}

/* Override any dark text that might be invisible */
.text-gray-900, .text-gray-800, .text-gray-700, .text-gray-600, .text-gray-500 {
  color: rgba(255, 255, 255, 0.9) !important;
}

.text-gray-400, .text-gray-300 {
  color: rgba(255, 255, 255, 0.7) !important;
}

/* Ensure form elements are visible */
select option {
  background: rgba(0, 0, 0, 0.8) !important;
  color: #ffffff !important;
}

/* Dropdown and menu text */
.dropdown-menu, .menu {
  background: rgba(0, 0, 0, 0.8) !important;
  color: #ffffff !important;
}

/* Tooltip text */
.tooltip {
  background: rgba(0, 0, 0, 0.9) !important;
  color: #ffffff !important;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  body {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    background-attachment: fixed;
    color: #e2e8f0;
  }

  .glass {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .modern-card {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .modern-button {
    background: rgba(0, 0, 0, 0.25);
    border: 1px solid rgba(255, 255, 255, 0.15);
  }

  .modern-input {
    background: rgba(0, 0, 0, 0.25);
    border: 1px solid rgba(255, 255, 255, 0.15);
  }
}
