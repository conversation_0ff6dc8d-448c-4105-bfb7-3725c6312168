import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Users, 
  Clock, 
  CheckCircle, 
  Star, 
  TrendingUp, 
  Award,
  Target,
  Activity
} from 'lucide-react';

interface AgentPerformanceProps {
  language: 'ar' | 'en';
}

interface AgentPerformanceData {
  agent_name: string;
  tickets_resolved: number;
  avg_resolution_time: string;
  customer_rating: number;
}

interface AgentDetails {
  id: number;
  user: {
    first_name: string;
    last_name: string;
    username: string;
  };
  agent_id: string;
  specializations: string[];
  max_concurrent_tickets: number;
  is_available: boolean;
  performance_rating: number;
  total_tickets_resolved: number;
  average_resolution_time?: string;
}

const AgentPerformance: React.FC<AgentPerformanceProps> = ({ language }) => {
  const [performanceData, setPerformanceData] = useState<AgentPerformanceData[]>([]);
  const [agents, setAgents] = useState<AgentDetails[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchAgentData();
  }, []);

  const fetchAgentData = async () => {
    try {
      const token = localStorage.getItem('token');
      const headers = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      };

      // Fetch agent performance data
      const performanceResponse = await fetch('/api/customer-service/dashboard/agent_performance/', {
        headers
      });
      const performanceData = await performanceResponse.json();
      setPerformanceData(performanceData);

      // Fetch agent details
      const agentsResponse = await fetch('/api/customer-service/agents/', {
        headers
      });
      const agentsData = await agentsResponse.json();
      setAgents(agentsData.results || []);

      setLoading(false);
    } catch (error) {
      console.error('Error fetching agent data:', error);
      setLoading(false);
    }
  };

  const renderStars = (rating: number): void => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${i < rating ? 'text-yellow-400 fill-current' : 'text-gray-400'}`}
      />
    ));
  };

  const getPerformanceColor = (rating: number): void => {
    if (rating >= 4.5) return 'text-green-400';
    if (rating >= 4.0) return 'text-blue-400';
    if (rating >= 3.5) return 'text-yellow-400';
    return 'text-red-400';
  };

  const getAvailabilityBadge = (isAvailable: boolean): void => {
    return isAvailable 
      ? <Badge className="bg-green-500/20 text-green-300 border-green-500/30">Available</Badge>
      : <Badge className="bg-red-500/20 text-red-300 border-red-500/30">Unavailable</Badge>;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-white/10 rounded w-64"></div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="h-32 bg-white/10 rounded-xl"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Calculate overall stats
  const totalAgents = agents.length;
  const availableAgents = agents.filter(agent => agent.is_available).length;
  const avgPerformanceRating = agents.length > 0 
    ? agents.reduce((sum, agent) => sum + agent.performance_rating, 0) / agents.length 
    : 0;
  const totalTicketsResolved = performanceData.reduce((sum, data) => sum + data.tickets_resolved, 0);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Agent Performance</h1>
            <p className="text-white/70">Monitor and analyze support agent performance metrics</p>
          </div>
        </div>

        {/* Overview Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="glass-card border-white/20">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-white/80">Total Agents</CardTitle>
              <Users className="h-4 w-4 text-blue-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{totalAgents}</div>
              <p className="text-xs text-white/60 mt-1">{availableAgents} available</p>
            </CardContent>
          </Card>

          <Card className="glass-card border-white/20">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-white/80">Avg Performance</CardTitle>
              <Award className="h-4 w-4 text-yellow-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{avgPerformanceRating.toFixed(1)}/5</div>
              <div className="flex items-center mt-1">
                {renderStars(Math.round(avgPerformanceRating))}
              </div>
            </CardContent>
          </Card>

          <Card className="glass-card border-white/20">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-white/80">Tickets Resolved</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{totalTicketsResolved}</div>
              <p className="text-xs text-white/60 mt-1">This period</p>
            </CardContent>
          </Card>

          <Card className="glass-card border-white/20">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-white/80">Team Efficiency</CardTitle>
              <TrendingUp className="h-4 w-4 text-purple-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">94%</div>
              <p className="text-xs text-white/60 mt-1">Overall efficiency</p>
            </CardContent>
          </Card>
        </div>

        {/* Agent Performance Table */}
        <Card className="glass-card border-white/20">
          <CardHeader>
            <CardTitle className="text-white">Individual Agent Performance</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {agents.length === 0 ? (
                <div className="text-center py-8">
                  <Users className="h-12 w-12 text-white/40 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-white mb-2">No agents found</h3>
                  <p className="text-white/60">Support agents will appear here</p>
                </div>
              ) : (
                agents.map((agent) => {
                  const performanceInfo = performanceData.find(p => 
                    p.agent_name.includes(agent.user.first_name) && p.agent_name.includes(agent.user.last_name)
                  );

                  return (
                    <div key={agent.id} className="p-6 bg-white/5 rounded-lg">
                      <div className="flex items-start justify-between">
                        <div className="flex items-center gap-4">
                          <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                            <span className="text-white font-semibold">
                              {((agent.user?.first_name || '').charAt(0) + (agent.user?.last_name || '').charAt(0)) || '??'}
                            </span>
                          </div>
                          <div>
                            <h3 className="text-lg font-semibold text-white">
                              {agent.user.first_name} {agent.user.last_name}
                            </h3>
                            <p className="text-white/60 text-sm">Agent ID: {agent.agent_id}</p>
                            <div className="flex items-center gap-2 mt-1">
                              {getAvailabilityBadge(agent.is_available)}
                              {agent.specializations.length > 0 && (
                                <Badge className="bg-blue-500/20 text-blue-300 border-blue-500/30">
                                  {agent.specializations.join(', ')}
                                </Badge>
                              )}
                            </div>
                          </div>
                        </div>

                        <div className="text-right">
                          <div className="flex items-center gap-1 mb-2">
                            <span className="text-white/80 text-sm">Performance:</span>
                            <span className={`font-semibold ${getPerformanceColor(agent.performance_rating)}`}>
                              {agent.performance_rating.toFixed(1)}/5
                            </span>
                          </div>
                          <div className="flex items-center gap-1">
                            {renderStars(Math.round(agent.performance_rating))}
                          </div>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-4 pt-4 border-t border-white/10">
                        <div className="text-center">
                          <div className="flex items-center justify-center gap-1 mb-1">
                            <CheckCircle className="h-4 w-4 text-green-400" />
                            <span className="text-white/80 text-sm">Resolved</span>
                          </div>
                          <div className="text-xl font-bold text-white">
                            {performanceInfo?.tickets_resolved || agent.total_tickets_resolved}
                          </div>
                        </div>

                        <div className="text-center">
                          <div className="flex items-center justify-center gap-1 mb-1">
                            <Clock className="h-4 w-4 text-yellow-400" />
                            <span className="text-white/80 text-sm">Avg Time</span>
                          </div>
                          <div className="text-xl font-bold text-white">
                            {performanceInfo?.avg_resolution_time || 'N/A'}
                          </div>
                        </div>

                        <div className="text-center">
                          <div className="flex items-center justify-center gap-1 mb-1">
                            <Star className="h-4 w-4 text-purple-400" />
                            <span className="text-white/80 text-sm">Customer Rating</span>
                          </div>
                          <div className="text-xl font-bold text-white">
                            {performanceInfo?.customer_rating?.toFixed(1) || 'N/A'}
                          </div>
                        </div>

                        <div className="text-center">
                          <div className="flex items-center justify-center gap-1 mb-1">
                            <Target className="h-4 w-4 text-blue-400" />
                            <span className="text-white/80 text-sm">Capacity</span>
                          </div>
                          <div className="text-xl font-bold text-white">
                            {agent.max_concurrent_tickets}
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })
              )}
            </div>
          </CardContent>
        </Card>

        {/* Performance Insights */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="glass-card border-white/20">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Performance Insights
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="p-4 bg-green-500/10 border border-green-500/20 rounded-lg">
                <h4 className="text-green-300 font-medium mb-2">Top Performer</h4>
                <p className="text-white/80 text-sm">
                  {agents.length > 0 && 
                    `${agents.reduce((prev, current) => 
                      prev.performance_rating > current.performance_rating ? prev : current
                    ).user.first_name} ${agents.reduce((prev, current) => 
                      prev.performance_rating > current.performance_rating ? prev : current
                    ).user.last_name} leads with ${agents.reduce((prev, current) => 
                      prev.performance_rating > current.performance_rating ? prev : current
                    ).performance_rating.toFixed(1)}/5 rating`
                  }
                </p>
              </div>
              
              <div className="p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg">
                <h4 className="text-blue-300 font-medium mb-2">Team Availability</h4>
                <p className="text-white/80 text-sm">
                  {availableAgents} out of {totalAgents} agents are currently available ({((availableAgents/totalAgents)*100).toFixed(0)}%)
                </p>
              </div>
              
              <div className="p-4 bg-purple-500/10 border border-purple-500/20 rounded-lg">
                <h4 className="text-purple-300 font-medium mb-2">Workload Distribution</h4>
                <p className="text-white/80 text-sm">
                  Average capacity: {agents.length > 0 ? (agents.reduce((sum, agent) => sum + agent.max_concurrent_tickets, 0) / agents.length).toFixed(1) : 0} tickets per agent
                </p>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-card border-white/20">
            <CardHeader>
              <CardTitle className="text-white">Performance Trends</CardTitle>
            </CardHeader>
            <CardContent className="flex items-center justify-center h-64">
              <div className="text-center">
                <TrendingUp className="h-16 w-16 text-white/40 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-2">Performance Charts</h3>
                <p className="text-white/60">
                  Detailed performance charts and trends will be available here
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default AgentPerformance;
