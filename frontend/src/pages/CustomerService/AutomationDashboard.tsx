import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  <PERSON><PERSON><PERSON>, 
  Zap, 
  Bot, 
  GitBranch, 
  Play, 
  Pause, 
  BarChart3,
  Plus,
  CheckCircle,
  XCircle,
  Clock,
  TrendingUp
} from 'lucide-react';

interface AutomationDashboardProps {
  language: 'ar' | 'en';
}

interface RoutingRule {
  id: number;
  name: string;
  condition_type: string;
  action_type: string;
  is_active: boolean;
  priority: number;
  created_at: string;
}

interface WorkflowAutomation {
  id: number;
  name: string;
  trigger_event: string;
  is_active: boolean;
  total_executions: number;
  success_rate: number;
  created_at: string;
}

interface AIAssistant {
  id: number;
  name: string;
  assistant_type: string;
  is_active: boolean;
  total_interactions: number;
  success_rate: number;
}

interface AutomationMetrics {
  routing_rules: {
    total_active: number;
    rules_by_type: Record<string, number>;
  };
  ai_assistants: {
    total_active: number;
    total_interactions: number;
    average_success_rate: number;
  };
  workflows: {
    total_active: number;
    total_executions: number;
    average_success_rate: number;
  };
  integrations: {
    total_active: number;
    sync_success_rate: number;
  };
}

// @ts-ignore
const AutomationDashboard: (React as any).FC<AutomationDashboardProps> = ({ language }) => {
  // @ts-ignore
  const [routingRules, setRoutingRules] = useState<RoutingRule[]>([]);
  // @ts-ignore
  const [workflows, setWorkflows] = useState<WorkflowAutomation[]>([]);
  // @ts-ignore
  const [aiAssistants, setAiAssistants] = useState<AIAssistant[]>([]);
  // @ts-ignore
  const [metrics, setMetrics] = useState<AutomationMetrics | null>(null);
  const [loading, setLoading] = useState(true as any);

  // @ts-ignore
  useEffect(( as any) => {
    // @ts-ignore
    fetchAutomationData( as any);
  // @ts-ignore
  }, []);

  // @ts-ignore
  const fetchAutomationData: any = async () => {
    // @ts-ignore
    try {
      // @ts-ignore
      const token = (localStorage as any).getItem('token' as any);
      const headers: any = {
        // @ts-ignore
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      // @ts-ignore
      };

      // Fetch routing rules
      const rulesResponse: any = await fetch('/api/customer-service/routing-rules/', { headers } as any);
      const rulesData: any = await (rulesResponse as any).json( as any);
      setRoutingRules((rulesData as any as any).results || []);

      // Fetch workflows
      const workflowsResponse: any = await fetch('/api/customer-service/workflows/', { headers } as any);
      const workflowsData: any = await (workflowsResponse as any).json( as any);
      setWorkflows((workflowsData as any as any).results || []);

      // Fetch AI assistants
      const aiResponse: any = await fetch('/api/customer-service/ai-assistants/', { headers } as any);
      const aiData: any = await (aiResponse as any).json( as any);
      setAiAssistants((aiData as any as any).results || []);

      // Fetch automation metrics
      const metricsResponse: any = await fetch('/api/customer-service/advanced-analytics/automation_metrics/', { headers } as any);
      const metricsData: any = await (metricsResponse as any).json( as any);
      setMetrics(metricsData as any);

      setLoading(false as any);
    // @ts-ignore
    } catch (error) {
      // @ts-ignore
      (console as any).error('Error fetching automation data:', error as any);
      setLoading(false as any);
    // @ts-ignore
    }
  // @ts-ignore
  };

  // @ts-ignore
  const toggleRuleStatus: any = async (ruleId: number, isActive: boolean) => {
    // @ts-ignore
    try {
      // @ts-ignore
      const token = (localStorage as any).getItem('token' as any);
      await fetch(`/api/customer-service/routing-rules/${ruleId}/`, {
        // @ts-ignore
        method: 'PATCH',
        headers: {
          // @ts-ignore
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        // @ts-ignore
        },
        // @ts-ignore
        body: (JSON as any as any).stringify({ is_active: !isActive } as any)
      // @ts-ignore
      });
      
      // Update local state
      // @ts-ignore
      setRoutingRules(prev => (prev as any as any).map(rule => 
        // @ts-ignore
        (rule as any as any).id === ruleId ? { ...rule, is_active: !isActive } : rule
      ));
    // @ts-ignore
    } catch (error) {
      // @ts-ignore
      (console as any).error('Error toggling rule status:', error as any);
    // @ts-ignore
    }
  // @ts-ignore
  };

  // @ts-ignore
  const toggleWorkflowStatus: any = async (workflowId: number, isActive: boolean) => {
    // @ts-ignore
    try {
      // @ts-ignore
      const token = (localStorage as any).getItem('token' as any);
      await fetch(`/api/customer-service/workflows/${workflowId}/`, {
        // @ts-ignore
        method: 'PATCH',
        headers: {
          // @ts-ignore
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        // @ts-ignore
        },
        // @ts-ignore
        body: (JSON as any as any).stringify({ is_active: !isActive } as any)
      // @ts-ignore
      });
      
      // Update local state
      // @ts-ignore
      setWorkflows(prev => (prev as any as any).map(workflow => 
        // @ts-ignore
        (workflow as any as any).id === workflowId ? { ...workflow, is_active: !isActive } : workflow
      ));
    // @ts-ignore
    } catch (error) {
      // @ts-ignore
      (console as any).error('Error toggling workflow status:', error as any);
    // @ts-ignore
    }
  // @ts-ignore
  };

  // @ts-ignore
  const getConditionTypeIcon: any = (type: string): void => {
    // @ts-ignore
    switch (type) {
      // @ts-ignore
      case 'keyword': return '🔍';
      case 'category': return '📁';
      case 'priority': return '⚡';
      case 'customer_tier': return '👑';
      case 'time_of_day': return '🕐';
      default: return '⚙️';
    // @ts-ignore
    }
  // @ts-ignore
  };

  // @ts-ignore
  const getActionTypeIcon: any = (type: string): void => {
    // @ts-ignore
    switch (type) {
      // @ts-ignore
      case 'assign_agent': return '👤';
      case 'assign_team': return '👥';
      case 'set_priority': return '🔥';
      case 'escalate': return '📈';
      default: return '⚡';
    // @ts-ignore
    }
  // @ts-ignore
  };

  if (loading) {
    // @ts-ignore
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-white/10 rounded w-64"></div>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              // @ts-ignore
              {[...Array(4 as any)].map((_, i as any) => (
                <div key={i} className="h-32 bg-white/10 rounded-xl"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  // @ts-ignore
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Automation Dashboard</h1>
            <p className="text-white/70">Manage AI assistants, routing rules, and workflows</p>
          </div>
          <Button className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600">
            <Plus className="h-4 w-4 mr-2" />
            Create Automation
          </Button>
        </div>

        {/* Metrics Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="glass-card border-white/20">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-white/80">Routing Rules</CardTitle>
              <GitBranch className="h-4 w-4 text-blue-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{metrics?.(routing_rules as any).total_active || 0}</div>
              <p className="text-xs text-white/60 mt-1">Active rules</p>
            </CardContent>
          </Card>

          <Card className="glass-card border-white/20">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-white/80">AI Assistants</CardTitle>
              <Bot className="h-4 w-4 text-green-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{metrics?.(ai_assistants as any).total_active || 0}</div>
              <p className="text-xs text-white/60 mt-1">{metrics?.(ai_assistants as any).total_interactions || 0} interactions</p>
            </CardContent>
          </Card>

          <Card className="glass-card border-white/20">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-white/80">Workflows</CardTitle>
              <Zap className="h-4 w-4 text-yellow-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{metrics?.(workflows as any).total_active || 0}</div>
              <p className="text-xs text-white/60 mt-1">{metrics?.(workflows as any).total_executions || 0} executions</p>
            </CardContent>
          </Card>

          <Card className="glass-card border-white/20">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-white/80">Success Rate</CardTitle>
              <TrendingUp className="h-4 w-4 text-purple-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">
                {((metrics?.(ai_assistants as any).average_success_rate || 0) + (metrics?.(workflows as any).average_success_rate || 0)) / 2}%
              </div>
              <p className="text-xs text-white/60 mt-1">Overall automation</p>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Routing Rules */}
          <Card className="glass-card border-white/20">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <GitBranch className="h-5 w-5" />
                Routing Rules
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {(routingRules as any).length === 0 ? (
                  <div className="text-center py-8">
                    <Settings className="h-12 w-12 text-white/40 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-white mb-2">No routing rules</h3>
                    <p className="text-white/60">Create rules to automate ticket routing</p>
                  </div>
                ) : (
                  // @ts-ignore
                  (routingRules as any).map((rule as any) => (
                    <div key={(rule as any).id} className="p-4 bg-white/5 rounded-lg">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <span className="text-lg">{getConditionTypeIcon((rule as any as any).condition_type)}</span>
                            <h4 className="font-medium text-white">{(rule as any).name}</h4>
                            <Badge className={(rule as any).is_active ? 'bg-green-500/20 text-green-300 border-green-500/30' : 'bg-gray-500/20 text-gray-300 border-gray-500/30'}>
                              {(rule as any).is_active ? 'Active' : 'Inactive'}
                            </Badge>
                          </div>
                          <div className="flex items-center gap-4 text-sm text-white/60">
                            <span>Condition: {(rule as any).condition_type}</span>
                            <span>Action: {getActionTypeIcon((rule as any as any).action_type)} {(rule as any).action_type}</span>
                            <span>Priority: {(rule as any).priority}</span>
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleRuleStatus((rule as any as any).id, (rule as any).is_active)}
                          className="text-white/60 hover:text-white"
                        >
                          {(rule as any).is_active ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                        </Button>
                      </div>
                    </div>
                  ))
                // @ts-ignore
                )}
              </div>
            </CardContent>
          </Card>

          {/* AI Assistants */}
          <Card className="glass-card border-white/20">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Bot className="h-5 w-5" />
                AI Assistants
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {(aiAssistants as any).length === 0 ? (
                  <div className="text-center py-8">
                    <Bot className="h-12 w-12 text-white/40 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-white mb-2">No AI assistants</h3>
                    <p className="text-white/60">Configure AI assistants for automation</p>
                  </div>
                ) : (
                  // @ts-ignore
                  (aiAssistants as any).map((assistant as any) => (
                    <div key={(assistant as any).id} className="p-4 bg-white/5 rounded-lg">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <Bot className="h-4 w-4 text-blue-400" />
                            <h4 className="font-medium text-white">{(assistant as any).name}</h4>
                            <Badge className={(assistant as any).is_active ? 'bg-green-500/20 text-green-300 border-green-500/30' : 'bg-gray-500/20 text-gray-300 border-gray-500/30'}>
                              {(assistant as any).is_active ? 'Online' : 'Offline'}
                            </Badge>
                          </div>
                          <div className="flex items-center gap-4 text-sm text-white/60">
                            <span>Type: {(assistant as any).assistant_type}</span>
                            <span>Interactions: {(assistant as any).total_interactions}</span>
                            <span>Success: {(assistant as any).success_rate}%</span>
                          </div>
                        </div>
                        <div className="flex items-center gap-1">
                          {(assistant as any).success_rate >= 80 ? (
                            <CheckCircle className="h-4 w-4 text-green-400" />
                          ) : (assistant as any).success_rate >= 60 ? (
                            <Clock className="h-4 w-4 text-yellow-400" />
                          ) : (
                            <XCircle className="h-4 w-4 text-red-400" />
                          )}
                        </div>
                      </div>
                    </div>
                  ))
                // @ts-ignore
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Workflows */}
        <Card className="glass-card border-white/20">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <Zap className="h-5 w-5" />
              Workflow Automations
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {(workflows as any).length === 0 ? (
                <div className="col-span-full text-center py-8">
                  <Zap className="h-12 w-12 text-white/40 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-white mb-2">No workflows</h3>
                  <p className="text-white/60">Create workflows to automate processes</p>
                </div>
              ) : (
                // @ts-ignore
                (workflows as any).map((workflow as any) => (
                  <div key={(workflow as any).id} className="p-4 bg-white/5 rounded-lg">
                    <div className="flex items-start justify-between mb-3">
                      <h4 className="font-medium text-white">{(workflow as any).name}</h4>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleWorkflowStatus((workflow as any as any).id, (workflow as any).is_active)}
                        className="text-white/60 hover:text-white"
                      >
                        {(workflow as any).is_active ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                      </Button>
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <Badge className={(workflow as any).is_active ? 'bg-green-500/20 text-green-300 border-green-500/30' : 'bg-gray-500/20 text-gray-300 border-gray-500/30'}>
                          {(workflow as any).is_active ? 'Active' : 'Inactive'}
                        </Badge>
                        <span className="text-sm text-white/60">Trigger: {(workflow as any).trigger_event}</span>
                      </div>
                      
                      <div className="flex items-center justify-between text-sm text-white/60">
                        <span>Executions: {(workflow as any).total_executions}</span>
                        <span className="flex items-center gap-1">
                          <BarChart3 className="h-3 w-3" />
                          {(workflow as any).success_rate}% success
                        </span>
                      </div>
                    </div>
                  </div>
                ))
              // @ts-ignore
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
// @ts-ignore
};

export default AutomationDashboard;
