/**
 * Customer Feedback Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  Star,
  MessageSquare,
  ThumbsUp,
  User,
  Calendar,
  Eye,
  Edit,
  Trash2
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { customerFeedbackService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface CustomerFeedbackProps {
  language: 'ar' | 'en'
}

interface FeedbackData {
  id: number
  customerName: string
  customerNameAr: string
  feedbackType: string
  overallRating: number
  responseTimeRating?: number
  solutionQualityRating?: number
  agentProfessionalismRating?: number
  comments: string
  commentsAr: string
  suggestions: string
  suggestionsAr: string
  createdAt: string
  relatedTicketId?: string
  relatedTicketTitle?: string
}

const translations = {
  ar: {
    customerFeedback: 'تقييمات العملاء',
    addFeedback: 'إضافة تقييم',
    editFeedback: 'تعديل التقييم',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذا التقييم؟',
    searchPlaceholder: 'البحث في تقييمات العملاء...',
    customerName: 'اسم العميل',
    feedbackType: 'نوع التقييم',
    overallRating: 'التقييم العام',
    responseTimeRating: 'تقييم وقت الاستجابة',
    solutionQualityRating: 'تقييم جودة الحل',
    agentProfessionalismRating: 'تقييم احترافية الوكيل',
    comments: 'التعليقات',
    suggestions: 'الاقتراحات',
    createdAt: 'تاريخ الإنشاء',
    relatedTicket: 'التذكرة المرتبطة',
    feedbackTypes: {
      ticketRating: 'تقييم التذكرة',
      serviceSurvey: 'استطلاع الخدمة',
      productFeedback: 'تقييم المنتج',
      generalFeedback: 'تقييم عام'
    }
  },
  en: {
    customerFeedback: 'Customer Feedback',
    addFeedback: 'Add Feedback',
    editFeedback: 'Edit Feedback',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this feedback?',
    searchPlaceholder: 'Search customer feedback...',
    customerName: 'Customer Name',
    feedbackType: 'Feedback Type',
    overallRating: 'Overall Rating',
    responseTimeRating: 'Response Time Rating',
    solutionQualityRating: 'Solution Quality Rating',
    agentProfessionalismRating: 'Agent Professionalism Rating',
    comments: 'Comments',
    suggestions: 'Suggestions',
    createdAt: 'Created At',
    relatedTicket: 'Related Ticket',
    feedbackTypes: {
      ticketRating: 'Ticket Rating',
      serviceSurvey: 'Service Survey',
      productFeedback: 'Product Feedback',
      generalFeedback: 'General Feedback'
    }
  }
}

// @ts-ignore
export default function CustomerFeedback({ language }: CustomerFeedbackProps as any): (React as any).ReactElement {
  const [showModal, setShowModal] = useState(false as any)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: feedback,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<FeedbackData>({
    service: customerFeedbackService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const renderStars = (rating: number): void => {
    // @ts-ignore
    return (Array as any).from({ length: 5 }, (_, i as any) => (
      <Star
        key={i}
        className={`h-4 w-4 ${i < rating ? 'text-yellow-400 fill-current' : 'text-gray-400'}`}
      />
    ))
  }

  const getFeedbackTypeColor = (type: string): void => {
    switch (type) {
      case 'ticketRating': return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'serviceSurvey': return 'bg-green-100 text-green-800 border-green-200'
      case 'productFeedback': return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'generalFeedback': return 'bg-orange-100 text-orange-800 border-orange-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  // Table columns configuration
  const columns: TableColumn<FeedbackData>[] = [
    {
      key: 'customerName',
      label: (t as any).customerName,
      sortable: true,
      render: (item: FeedbackData) => (
        <div className="flex items-center gap-2">
          <div className="w-10 h-10 rounded-full bg-blue-500 flex items-center justify-center text-white font-medium">
            // @ts-ignore
            {(((item as any).customerName || '').charAt(0 as any) + (((item as any).customerName || '').split(' ' as any)[1]?.charAt(0 as any) || '')).toUpperCase( as any) || '??'}
          </div>
          <span className="text-white/80">
            {language === 'ar' ? (item as any).customerNameAr : (item as any).customerName}
          </span>
        </div>
      )
    },
    {
      key: 'feedbackType',
      label: (t as any).feedbackType,
      render: (item: FeedbackData) => (
        <Badge className={getFeedbackTypeColor((item as any as any).feedbackType)}>
          // @ts-ignore
          {(t as any).feedbackTypes[(item as any).feedbackType as keyof typeof (t as any).feedbackTypes]}
        </Badge>
      )
    },
    {
      key: 'overallRating',
      label: (t as any).overallRating,
      sortable: true,
      render: (item: FeedbackData) => (
        <div className="flex items-center gap-2">
          <div className="flex">{renderStars((item as any as any).overallRating)}</div>
          <span className="text-white font-medium">{(item as any).overallRating}/5</span>
        </div>
      )
    },
    {
      key: 'responseTimeRating',
      label: (t as any).responseTimeRating,
      render: (item: FeedbackData) => (
        (item as any).responseTimeRating ? (
          <div className="flex items-center gap-1">
            <div className="flex">{renderStars((item as any as any).responseTimeRating)}</div>
            <span className="text-white/80 text-sm">{(item as any).responseTimeRating}/5</span>
          </div>
        ) : (
          <span className="text-white/50">-</span>
        )
      )
    },
    {
      key: 'solutionQualityRating',
      label: (t as any).solutionQualityRating,
      render: (item: FeedbackData) => (
        (item as any).solutionQualityRating ? (
          <div className="flex items-center gap-1">
            <div className="flex">{renderStars((item as any as any).solutionQualityRating)}</div>
            <span className="text-white/80 text-sm">{(item as any).solutionQualityRating}/5</span>
          </div>
        ) : (
          <span className="text-white/50">-</span>
        )
      )
    },
    {
      key: 'comments',
      label: (t as any).comments,
      render: (item: FeedbackData) => (
        <div className="max-w-xs">
          <span className="text-white/80 text-sm">
            {language === 'ar' ? (item as any).commentsAr?.substring(0, 50 as any) + '...' : (item as any).comments?.substring(0, 50 as any) + '...'}
          </span>
        </div>
      )
    },
    {
      key: 'createdAt',
      label: (t as any).createdAt,
      sortable: true,
      render: (item: FeedbackData) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">{(item as any).createdAt}</span>
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction<FeedbackData>[] = [
    {
      label: (t as any).view,
      icon: Eye,
      onClick: (item: FeedbackData) => {
        selectItem(item as any)
        setModalMode('view' as any)
        setShowModal(true as any)
      },
      variant: 'ghost'
    },
    {
      label: (t as any).edit,
      icon: Edit,
      onClick: (item: FeedbackData) => {
        selectItem(item as any)
        setModalMode('edit' as any)
        setShowModal(true as any)
      },
      variant: 'ghost'
    },
    {
      label: (t as any).delete,
      icon: Trash2,
      onClick: async (item: FeedbackData) => {
        if ((window as any).confirm((t as any as any).confirmDelete)) {
          await deleteItem((item as any as any).id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'feedbackType',
      label: (t as any).feedbackType,
      options: [
        { label: (t as any).feedbackTypes.ticketRating, value: 'ticketRating' },
        { label: (t as any).feedbackTypes.serviceSurvey, value: 'serviceSurvey' },
        { label: (t as any).feedbackTypes.productFeedback, value: 'productFeedback' },
        { label: (t as any).feedbackTypes.generalFeedback, value: 'generalFeedback' }
      ]
    },
    {
      key: 'overallRating',
      label: (t as any).overallRating,
      options: [
        { label: '5 Stars', value: '5' },
        { label: '4 Stars', value: '4' },
        { label: '3 Stars', value: '3' },
        { label: '2 Stars', value: '2' },
        { label: '1 Star', value: '1' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'customerName',
      label: (t as any).customerName,
      type: 'text',
      required: true
    },
    {
      name: 'customerNameAr',
      label: (t as any).customerName + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'feedbackType',
      label: (t as any).feedbackType,
      type: 'select',
      required: true,
      options: [
        { label: (t as any).feedbackTypes.ticketRating, value: 'ticketRating' },
        { label: (t as any).feedbackTypes.serviceSurvey, value: 'serviceSurvey' },
        { label: (t as any).feedbackTypes.productFeedback, value: 'productFeedback' },
        { label: (t as any).feedbackTypes.generalFeedback, value: 'generalFeedback' }
      ]
    },
    {
      name: 'overallRating',
      label: (t as any).overallRating,
      type: 'number',
      required: true,
      min: 1,
      max: 5
    },
    {
      name: 'responseTimeRating',
      label: (t as any).responseTimeRating,
      type: 'number',
      min: 1,
      max: 5
    },
    {
      name: 'solutionQualityRating',
      label: (t as any).solutionQualityRating,
      type: 'number',
      min: 1,
      max: 5
    },
    {
      name: 'agentProfessionalismRating',
      label: (t as any).agentProfessionalismRating,
      type: 'number',
      min: 1,
      max: 5
    },
    {
      name: 'comments',
      label: (t as any).comments,
      type: 'textarea'
    },
    {
      name: 'commentsAr',
      label: (t as any).comments + ' (عربي)',
      type: 'textarea'
    },
    {
      name: 'suggestions',
      label: (t as any).suggestions,
      type: 'textarea'
    },
    {
      name: 'suggestionsAr',
      label: (t as any).suggestions + ' (عربي)',
      type: 'textarea'
    },
    {
      name: 'relatedTicketId',
      label: (t as any).relatedTicket + ' ID',
      type: 'text'
    },
    {
      name: 'relatedTicketTitle',
      label: (t as any).relatedTicket + ' Title',
      type: 'text'
    }
  ]

  // Event handlers
  const handleCreate = (): void => {
    setModalMode('create' as any)
    setShowModal(true as any)
  }

  const handleModalClose = (): void => {
    setShowModal(false as any)
    selectItem(null as any)
    // @ts-ignore
    clearError( as any)
  }

  const handleSave = async (data: Partial<FeedbackData>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data as any)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem((selectedItem as any as any).id, data)
      }
      setShowModal(false as any)
    } catch (error) {
      (console as any).error('Save error:', error as any)
    }
  }

  const handleExport = async () => {
    try {
      await exportData('csv' as any)
    } catch (error) {
      (console as any).error('Export error:', error as any)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={(t as any).customerFeedback}
        data={feedback}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={(t as any).searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? (t as any).addFeedback : modalMode === 'edit' ? (t as any).editFeedback : (t as any).view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
