/**
 * Customer Feedback Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  Star,
  MessageSquare,
  ThumbsUp,
  User,
  Calendar,
  Eye,
  Edit,
  Trash2
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { customerFeedbackService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface CustomerFeedbackProps {
  language: 'ar' | 'en'
}

interface FeedbackData {
  id: number
  customerName: string
  customerNameAr: string
  feedbackType: string
  overallRating: number
  responseTimeRating?: number
  solutionQualityRating?: number
  agentProfessionalismRating?: number
  comments: string
  commentsAr: string
  suggestions: string
  suggestionsAr: string
  createdAt: string
  relatedTicketId?: string
  relatedTicketTitle?: string
}

const translations = {
  ar: {
    customerFeedback: 'تقييمات العملاء',
    addFeedback: 'إضافة تقييم',
    editFeedback: 'تعديل التقييم',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذا التقييم؟',
    searchPlaceholder: 'البحث في تقييمات العملاء...',
    customerName: 'اسم العميل',
    feedbackType: 'نوع التقييم',
    overallRating: 'التقييم العام',
    responseTimeRating: 'تقييم وقت الاستجابة',
    solutionQualityRating: 'تقييم جودة الحل',
    agentProfessionalismRating: 'تقييم احترافية الوكيل',
    comments: 'التعليقات',
    suggestions: 'الاقتراحات',
    createdAt: 'تاريخ الإنشاء',
    relatedTicket: 'التذكرة المرتبطة',
    feedbackTypes: {
      ticketRating: 'تقييم التذكرة',
      serviceSurvey: 'استطلاع الخدمة',
      productFeedback: 'تقييم المنتج',
      generalFeedback: 'تقييم عام'
    }
  },
  en: {
    customerFeedback: 'Customer Feedback',
    addFeedback: 'Add Feedback',
    editFeedback: 'Edit Feedback',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this feedback?',
    searchPlaceholder: 'Search customer feedback...',
    customerName: 'Customer Name',
    feedbackType: 'Feedback Type',
    overallRating: 'Overall Rating',
    responseTimeRating: 'Response Time Rating',
    solutionQualityRating: 'Solution Quality Rating',
    agentProfessionalismRating: 'Agent Professionalism Rating',
    comments: 'Comments',
    suggestions: 'Suggestions',
    createdAt: 'Created At',
    relatedTicket: 'Related Ticket',
    feedbackTypes: {
      ticketRating: 'Ticket Rating',
      serviceSurvey: 'Service Survey',
      productFeedback: 'Product Feedback',
      generalFeedback: 'General Feedback'
    }
  }
}

export default function CustomerFeedback({ language }: CustomerFeedbackProps): React.ReactElement {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: feedback,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<FeedbackData>({
    service: customerFeedbackService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const renderStars = (rating: number): void => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${i < rating ? 'text-yellow-400 fill-current' : 'text-gray-400'}`}
      />
    ))
  }

  const getFeedbackTypeColor = (type: string): void => {
    switch (type) {
      case 'ticketRating': return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'serviceSurvey': return 'bg-green-100 text-green-800 border-green-200'
      case 'productFeedback': return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'generalFeedback': return 'bg-orange-100 text-orange-800 border-orange-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  // Table columns configuration
  const columns: TableColumn<FeedbackData>[] = [
    {
      key: 'customerName',
      label: t.customerName,
      sortable: true,
      render: (item: FeedbackData) => (
        <div className="flex items-center gap-2">
          <div className="w-10 h-10 rounded-full bg-blue-500 flex items-center justify-center text-white font-medium">
            {((item.customerName || '').charAt(0) + ((item.customerName || '').split(' ')[1]?.charAt(0) || '')).toUpperCase() || '??'}
          </div>
          <span className="text-white/80">
            {language === 'ar' ? item.customerNameAr : item.customerName}
          </span>
        </div>
      )
    },
    {
      key: 'feedbackType',
      label: t.feedbackType,
      render: (item: FeedbackData) => (
        <Badge className={getFeedbackTypeColor(item.feedbackType)}>
          {t.feedbackTypes[item.feedbackType as keyof typeof t.feedbackTypes]}
        </Badge>
      )
    },
    {
      key: 'overallRating',
      label: t.overallRating,
      sortable: true,
      render: (item: FeedbackData) => (
        <div className="flex items-center gap-2">
          <div className="flex">{renderStars(item.overallRating)}</div>
          <span className="text-white font-medium">{item.overallRating}/5</span>
        </div>
      )
    },
    {
      key: 'responseTimeRating',
      label: t.responseTimeRating,
      render: (item: FeedbackData) => (
        item.responseTimeRating ? (
          <div className="flex items-center gap-1">
            <div className="flex">{renderStars(item.responseTimeRating)}</div>
            <span className="text-white/80 text-sm">{item.responseTimeRating}/5</span>
          </div>
        ) : (
          <span className="text-white/50">-</span>
        )
      )
    },
    {
      key: 'solutionQualityRating',
      label: t.solutionQualityRating,
      render: (item: FeedbackData) => (
        item.solutionQualityRating ? (
          <div className="flex items-center gap-1">
            <div className="flex">{renderStars(item.solutionQualityRating)}</div>
            <span className="text-white/80 text-sm">{item.solutionQualityRating}/5</span>
          </div>
        ) : (
          <span className="text-white/50">-</span>
        )
      )
    },
    {
      key: 'comments',
      label: t.comments,
      render: (item: FeedbackData) => (
        <div className="max-w-xs">
          <span className="text-white/80 text-sm">
            {language === 'ar' ? item.commentsAr?.substring(0, 50) + '...' : item.comments?.substring(0, 50) + '...'}
          </span>
        </div>
      )
    },
    {
      key: 'createdAt',
      label: t.createdAt,
      sortable: true,
      render: (item: FeedbackData) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">{item.createdAt}</span>
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction<FeedbackData>[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: FeedbackData) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: FeedbackData) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: FeedbackData) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'feedbackType',
      label: t.feedbackType,
      options: [
        { label: t.feedbackTypes.ticketRating, value: 'ticketRating' },
        { label: t.feedbackTypes.serviceSurvey, value: 'serviceSurvey' },
        { label: t.feedbackTypes.productFeedback, value: 'productFeedback' },
        { label: t.feedbackTypes.generalFeedback, value: 'generalFeedback' }
      ]
    },
    {
      key: 'overallRating',
      label: t.overallRating,
      options: [
        { label: '5 Stars', value: '5' },
        { label: '4 Stars', value: '4' },
        { label: '3 Stars', value: '3' },
        { label: '2 Stars', value: '2' },
        { label: '1 Star', value: '1' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'customerName',
      label: t.customerName,
      type: 'text',
      required: true
    },
    {
      name: 'customerNameAr',
      label: t.customerName + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'feedbackType',
      label: t.feedbackType,
      type: 'select',
      required: true,
      options: [
        { label: t.feedbackTypes.ticketRating, value: 'ticketRating' },
        { label: t.feedbackTypes.serviceSurvey, value: 'serviceSurvey' },
        { label: t.feedbackTypes.productFeedback, value: 'productFeedback' },
        { label: t.feedbackTypes.generalFeedback, value: 'generalFeedback' }
      ]
    },
    {
      name: 'overallRating',
      label: t.overallRating,
      type: 'number',
      required: true,
      min: 1,
      max: 5
    },
    {
      name: 'responseTimeRating',
      label: t.responseTimeRating,
      type: 'number',
      min: 1,
      max: 5
    },
    {
      name: 'solutionQualityRating',
      label: t.solutionQualityRating,
      type: 'number',
      min: 1,
      max: 5
    },
    {
      name: 'agentProfessionalismRating',
      label: t.agentProfessionalismRating,
      type: 'number',
      min: 1,
      max: 5
    },
    {
      name: 'comments',
      label: t.comments,
      type: 'textarea'
    },
    {
      name: 'commentsAr',
      label: t.comments + ' (عربي)',
      type: 'textarea'
    },
    {
      name: 'suggestions',
      label: t.suggestions,
      type: 'textarea'
    },
    {
      name: 'suggestionsAr',
      label: t.suggestions + ' (عربي)',
      type: 'textarea'
    },
    {
      name: 'relatedTicketId',
      label: t.relatedTicket + ' ID',
      type: 'text'
    },
    {
      name: 'relatedTicketTitle',
      label: t.relatedTicket + ' Title',
      type: 'text'
    }
  ]

  // Event handlers
  const handleCreate = (): void => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = (): void => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<FeedbackData>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async () => {
    try {
      await exportData('csv')
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.customerFeedback}
        data={feedback}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addFeedback : modalMode === 'edit' ? t.editFeedback : t.view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
