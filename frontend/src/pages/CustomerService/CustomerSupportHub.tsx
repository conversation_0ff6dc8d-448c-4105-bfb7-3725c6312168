import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Bot,
  MessageCircle,
  Headphones,
  BookOpen,
  Star,
  Clock,
  Users,
  Zap,
  ArrowRight,
  CheckCircle,
  HelpCircle,
  Phone,
  Mail
} from 'lucide-react';

interface CustomerSupportHubProps {
  language: 'ar' | 'en';
}

interface SupportStats {
  total_tickets: number;
  avg_response_time: string;
  customer_satisfaction: number;
  active_agents: number;
  ai_interactions_today: number;
  knowledge_base_articles: number;
}

const CustomerSupportHub: React.FC<CustomerSupportHubProps> = ({ language }) => {
  const [stats, setStats] = useState<SupportStats | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    fetchSupportStats();
  }, []);

  const fetchSupportStats: any = async () => {
    try {
      // TODO: Replace with real customer service API when endpoints are implemented
      // Return empty data instead of mock data
      setStats({
        total_tickets: 0,
        avg_response_time: '0 hrs',
        customer_satisfaction: 0,
        active_agents: 0,
        ai_interactions_today: 0,
        knowledge_base_articles: 0
      });

      setLoading(false);
    } catch (error) {
      console.error('Error fetching support stats:', error);
      setLoading(false);
    }
  };

  const supportChannels: any = [
    {
      id: 'ai-chat',
      title: language === 'ar' ? 'مساعد الذكاء الاصطناعي' : 'AI Chat Assistant',
      description: language === 'ar'
        ? 'احصل على مساعدة فورية من مساعد الذكاء الاصطناعي المتاح 24/7'
        : 'Get instant help from our 24/7 AI-powered assistant',
      icon: Bot,
      color: 'from-blue-500 to-purple-500',
      status: 'online',
      responseTime: '< 1 min',
      action: () => {
        // The chat widget is already available on the page
        const chatButton = document.querySelector('[data-chat-widget]') as HTMLElement;
        if (chatButton) {
          chatButton.click();
        }
      }
    },
    {
      id: 'live-chat',
      title: language === 'ar' ? 'الدردشة المباشرة' : 'Live Chat',
      description: language === 'ar'
        ? 'تحدث مع وكيل دعم بشري للحصول على مساعدة شخصية'
        : 'Chat with a human support agent for personalized assistance',
      icon: MessageCircle,
      color: 'from-green-500 to-blue-500',
      status: 'online',
      responseTime: '< 5 min',
      action: () => window.location.href = '/admin/support/live-chat'
    },
    {
      id: 'phone-support',
      title: language === 'ar' ? 'الدعم الهاتفي' : 'Phone Support',
      description: language === 'ar'
        ? 'اتصل بفريق الدعم للحصول على مساعدة فورية'
        : 'Call our support team for immediate assistance',
      icon: Phone,
      color: 'from-orange-500 to-red-500',
      status: 'business-hours',
      responseTime: 'Immediate',
      action: () => window.open('tel:+1234567890')
    },
    {
      id: 'email-support',
      title: language === 'ar' ? 'الدعم عبر البريد الإلكتروني' : 'Email Support',
      description: language === 'ar'
        ? 'أرسل استفسارك عبر البريد الإلكتروني وسنرد عليك قريباً'
        : 'Send us your inquiry via email and we\'ll get back to you soon',
      icon: Mail,
      color: 'from-purple-500 to-pink-500',
      status: 'online',
      responseTime: '< 24 hrs',
      action: () => window.open('mailto:<EMAIL>')
    }
  ];

  const quickActions: any = [
    {
      title: language === 'ar' ? 'إنشاء تذكرة دعم' : 'Create Support Ticket',
      description: language === 'ar' ? 'أنشئ تذكرة دعم جديدة' : 'Create a new support ticket',
      icon: HelpCircle,
      href: '/admin/support/tickets'
    },
    {
      title: language === 'ar' ? 'قاعدة المعرفة' : 'Knowledge Base',
      description: language === 'ar' ? 'ابحث في مقالات المساعدة' : 'Browse help articles',
      icon: BookOpen,
      href: '/admin/support/knowledge-base'
    },
    {
      title: language === 'ar' ? 'حالة الخدمة' : 'Service Status',
      description: language === 'ar' ? 'تحقق من حالة الخدمات' : 'Check service status',
      icon: CheckCircle,
      href: '/admin/support/dashboard'
    }
  ];

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-white/10 rounded w-64"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="h-32 bg-white/10 rounded-xl"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">
            {language === 'ar' ? 'مركز دعم العملاء' : 'Customer Support Hub'}
          </h1>
          <p className="text-xl text-white/70 mb-6">
            {language === 'ar'
              ? 'نحن هنا لمساعدتك! اختر الطريقة المفضلة للحصول على الدعم'
              : 'We\'re here to help! Choose your preferred way to get support'
            }
          </p>
          <div className="flex justify-center gap-4 text-sm text-white/60">
            <div className="flex items-center gap-1">
              <Clock className="h-4 w-4" />
              <span>{language === 'ar' ? 'متاح 24/7' : 'Available 24/7'}</span>
            </div>
            <div className="flex items-center gap-1">
              <Users className="h-4 w-4" />
              <span>{stats?.active_agents || 0} {language === 'ar' ? 'وكيل متاح' : 'agents online'}</span>
            </div>
            <div className="flex items-center gap-1">
              <Star className="h-4 w-4" />
              <span>{stats?.customer_satisfaction || 0}/5 {language === 'ar' ? 'تقييم العملاء' : 'customer rating'}</span>
            </div>
          </div>
        </div>

        {/* Support Channels */}
        <div>
          <h2 className="text-2xl font-semibold text-white mb-6 text-center">
            {language === 'ar' ? 'قنوات الدعم' : 'Support Channels'}
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {supportChannels.map((channel) => (
              <Card
                key={channel.id}
                className="glass-card border-white/20 hover:border-white/40 transition-all duration-300 cursor-pointer group"
                onClick={channel.action}
              >
                <CardContent className="p-6 text-center">
                  <div className={`w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r ${channel.color} flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                    <channel.icon className="h-8 w-8 text-white" />
                  </div>

                  <h3 className="text-lg font-semibold text-white mb-2">{channel.title}</h3>
                  <p className="text-white/70 text-sm mb-4">{channel.description}</p>

                  <div className="flex items-center justify-between text-xs">
                    <Badge className={
                      channel.status === 'online'
                        ? 'bg-green-500/20 text-green-300 border-green-500/30'
                        : 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30'
                    }>
                      {channel.status === 'online'
                        ? (language === 'ar' ? 'متاح' : 'Online')
                        : (language === 'ar' ? 'ساعات العمل' : 'Business Hours')
                      }
                    </Badge>
                    <span className="text-white/60">{channel.responseTime}</span>
                  </div>

                  <Button
                    className="w-full mt-4 bg-white/10 hover:bg-white/20 text-white border-white/20"
                    variant="outline"
                  >
                    {language === 'ar' ? 'ابدأ المحادثة' : 'Start Chat'}
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Quick Actions */}
        <div>
          <h2 className="text-2xl font-semibold text-white mb-6 text-center">
            {language === 'ar' ? 'إجراءات سريعة' : 'Quick Actions'}
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {quickActions.map((action, index) => (
              <Card
                key={index}
                className="glass-card border-white/20 hover:border-white/40 transition-all duration-300 cursor-pointer"
                onClick={() => window.location.href = action.href}
              >
                <CardContent className="p-6">
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                      <action.icon className="h-6 w-6 text-white" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-white mb-1">{action.title}</h3>
                      <p className="text-white/70 text-sm">{action.description}</p>
                    </div>
                    <ArrowRight className="h-5 w-5 text-white/40" />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Support Stats */}
        <Card className="glass-card border-white/20">
          <CardHeader>
            <CardTitle className="text-white text-center">
              {language === 'ar' ? 'إحصائيات الدعم' : 'Support Statistics'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
              <div>
                <div className="text-2xl font-bold text-white">{stats?.total_tickets || 0}</div>
                <div className="text-white/60 text-sm">
                  {language === 'ar' ? 'إجمالي التذاكر' : 'Total Tickets'}
                </div>
              </div>
              <div>
                <div className="text-2xl font-bold text-white">{stats?.avg_response_time || 'N/A'}</div>
                <div className="text-white/60 text-sm">
                  {language === 'ar' ? 'متوسط وقت الاستجابة' : 'Avg Response Time'}
                </div>
              </div>
              <div>
                <div className="text-2xl font-bold text-white">{stats?.ai_interactions_today || 0}</div>
                <div className="text-white/60 text-sm">
                  {language === 'ar' ? 'تفاعلات الذكاء الاصطناعي اليوم' : 'AI Interactions Today'}
                </div>
              </div>
              <div>
                <div className="text-2xl font-bold text-white">{stats?.knowledge_base_articles || 0}</div>
                <div className="text-white/60 text-sm">
                  {language === 'ar' ? 'مقالات قاعدة المعرفة' : 'Knowledge Base Articles'}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Help Notice */}
        <Card className="glass-card border-white/20 bg-gradient-to-r from-blue-500/10 to-purple-500/10">
          <CardContent className="p-6 text-center">
            <Bot className="h-12 w-12 text-blue-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-white mb-2">
              {language === 'ar' ? 'مساعد الذكاء الاصطناعي متاح الآن!' : 'AI Assistant is Available Now!'}
            </h3>
            <p className="text-white/70 mb-4">
              {language === 'ar'
                ? 'انقر على أيقونة الدردشة في الزاوية السفلية اليمنى للحصول على مساعدة فورية من مساعد الذكاء الاصطناعي'
                : 'Click the chat icon in the bottom-right corner to get instant help from our AI assistant'
              }
            </p>
            <div className="flex items-center justify-center gap-2 text-sm text-white/60">
              <Zap className="h-4 w-4" />
              <span>{language === 'ar' ? 'استجابة فورية • متاح 24/7 • ذكي ومفيد' : 'Instant Response • 24/7 Available • Smart & Helpful'}</span>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default CustomerSupportHub;
