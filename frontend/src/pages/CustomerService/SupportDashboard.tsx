import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Ticket,
  MessageSquare,
  Clock,
  CheckCircle,
  AlertTriangle,
  Users,
  TrendingUp,
  Star,
  BarChart3,
  Plus
} from 'lucide-react';

interface DashboardStats {
  total_tickets: number;
  open_tickets: number;
  in_progress_tickets: number;
  resolved_tickets: number;
  avg_resolution_time: string;
  customer_satisfaction: number;
  sla_compliance: number;
  active_chat_sessions: number;
}

interface TicketTrend {
  date: string;
  created: number;
  resolved: number;
}

const SupportDashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [trends, setTrends] = useState<TicketTrend[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      // TODO: Replace with real customer service API when endpoints are implemented
      // For now, return empty data instead of mock data
      setStats({
        total_tickets: 0,
        open_tickets: 0,
        in_progress_tickets: 0,
        resolved_tickets: 0,
        avg_resolution_time: '0 hrs',
        customer_satisfaction: 0,
        sla_compliance: 0,
        active_chat_sessions: 0
      });

      setTrends([]);

      setLoading(false);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-white/10 rounded w-64"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="h-32 bg-white/10 rounded-xl"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  const getStatusColor = (status: string): void => {
    switch (status) {
      case 'open': return 'bg-red-500/20 text-red-300 border-red-500/30';
      case 'in_progress': return 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30';
      case 'resolved': return 'bg-green-500/20 text-green-300 border-green-500/30';
      default: return 'bg-gray-500/20 text-gray-300 border-gray-500/30';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Customer Service Dashboard</h1>
            <p className="text-white/70">Manage support tickets, live chat, and customer satisfaction</p>
          </div>
          <Button className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600">
            <Plus className="h-4 w-4 mr-2" />
            New Ticket
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="glass-card border-white/20">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-white/80">Total Tickets</CardTitle>
              <Ticket className="h-4 w-4 text-blue-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{stats?.total_tickets || 0}</div>
              <p className="text-xs text-white/60 mt-1">All time tickets</p>
            </CardContent>
          </Card>

          <Card className="glass-card border-white/20">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-white/80">Open Tickets</CardTitle>
              <AlertTriangle className="h-4 w-4 text-red-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{stats?.open_tickets || 0}</div>
              <p className="text-xs text-white/60 mt-1">Awaiting response</p>
            </CardContent>
          </Card>

          <Card className="glass-card border-white/20">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-white/80">Avg Resolution</CardTitle>
              <Clock className="h-4 w-4 text-yellow-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{stats?.avg_resolution_time || 'N/A'}</div>
              <p className="text-xs text-white/60 mt-1">Average time</p>
            </CardContent>
          </Card>

          <Card className="glass-card border-white/20">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-white/80">Satisfaction</CardTitle>
              <Star className="h-4 w-4 text-green-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{stats?.customer_satisfaction || 0}/5</div>
              <p className="text-xs text-white/60 mt-1">Customer rating</p>
            </CardContent>
          </Card>
        </div>

        {/* Performance Metrics */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="glass-card border-white/20">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                SLA Performance
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-white/80">SLA Compliance</span>
                <Badge className="bg-green-500/20 text-green-300 border-green-500/30">
                  {stats?.sla_compliance || 0}%
                </Badge>
              </div>
              <div className="w-full bg-white/10 rounded-full h-2">
                <div
                  className="bg-gradient-to-r from-green-500 to-blue-500 h-2 rounded-full transition-all duration-500"
                  style={{ width: `${stats?.sla_compliance || 0}%` }}
                ></div>
              </div>
              <div className="grid grid-cols-2 gap-4 mt-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-white">{stats?.in_progress_tickets || 0}</div>
                  <div className="text-xs text-white/60">In Progress</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-white">{stats?.resolved_tickets || 0}</div>
                  <div className="text-xs text-white/60">Resolved</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-card border-white/20">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <MessageSquare className="h-5 w-5" />
                Live Support
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-white/80">Active Chat Sessions</span>
                <Badge className="bg-blue-500/20 text-blue-300 border-blue-500/30">
                  {stats?.active_chat_sessions || 0}
                </Badge>
              </div>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-white/60 text-sm">Response Time</span>
                  <span className="text-white text-sm">Under 2 min</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-white/60 text-sm">Agents Online</span>
                  <span className="text-white text-sm">3/5</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-white/60 text-sm">Queue Length</span>
                  <span className="text-white text-sm">0</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card className="glass-card border-white/20">
          <CardHeader>
            <CardTitle className="text-white">Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button variant="outline" className="border-white/20 text-white hover:bg-white/10">
                <Ticket className="h-4 w-4 mr-2" />
                View All Tickets
              </Button>
              <Button variant="outline" className="border-white/20 text-white hover:bg-white/10">
                <Users className="h-4 w-4 mr-2" />
                Manage Agents
              </Button>
              <Button variant="outline" className="border-white/20 text-white hover:bg-white/10">
                <TrendingUp className="h-4 w-4 mr-2" />
                View Reports
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default SupportDashboard;
