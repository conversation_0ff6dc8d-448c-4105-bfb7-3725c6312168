/**
 * Ticket Management Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  MessageSquare,
  User,
  Clock,
  AlertCircle,
  CheckCircle,
  XCircle,
  Eye,
  Edit,
  Trash2,
  Star
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { ticketService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface TicketManagementProps {
  language: 'ar' | 'en'
}

interface SupportTicket {
  id: number
  ticketId: string
  title: string
  titleAr: string
  description: string
  descriptionAr: string
  status: 'open' | 'in-progress' | 'resolved' | 'closed'
  priority: 'low' | 'medium' | 'high' | 'critical'
  customerName: string
  customerNameAr: string
  assignedAgent: string
  assignedAgentAr: string
  category: string
  categoryAr: string
  createdAt: string
  updatedAt: string
  customerSatisfactionRating?: number
}

const translations = {
  ar: {
    ticketManagement: 'إدارة التذاكر',
    addTicket: 'إضافة تذكرة',
    editTicket: 'تعديل التذكرة',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذه التذكرة؟',
    searchPlaceholder: 'البحث في التذاكر...',
    ticketId: 'رقم التذكرة',
    title: 'العنوان',
    description: 'الوصف',
    status: 'الحالة',
    priority: 'الأولوية',
    customer: 'العميل',
    assignedAgent: 'الوكيل المكلف',
    category: 'الفئة',
    createdAt: 'تاريخ الإنشاء',
    rating: 'التقييم',
    open: 'مفتوح',
    'in-progress': 'قيد التنفيذ',
    resolved: 'محلول',
    closed: 'مغلق',
    low: 'منخفض',
    medium: 'متوسط',
    high: 'عالي',
    critical: 'حرج',
    categories: {
      technical: 'تقني',
      billing: 'فوترة',
      general: 'عام',
      complaint: 'شكوى'
    }
  },
  en: {
    ticketManagement: 'Ticket Management',
    addTicket: 'Add Ticket',
    editTicket: 'Edit Ticket',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this ticket?',
    searchPlaceholder: 'Search tickets...',
    ticketId: 'Ticket ID',
    title: 'Title',
    description: 'Description',
    status: 'Status',
    priority: 'Priority',
    customer: 'Customer',
    assignedAgent: 'Assigned Agent',
    category: 'Category',
    createdAt: 'Created At',
    rating: 'Rating',
    open: 'Open',
    'in-progress': 'In Progress',
    resolved: 'Resolved',
    closed: 'Closed',
    low: 'Low',
    medium: 'Medium',
    high: 'High',
    critical: 'Critical',
    categories: {
      technical: 'Technical',
      billing: 'Billing',
      general: 'General',
      complaint: 'Complaint'
    }
  }
}

export default function TicketManagement({ language }: TicketManagementProps): React.ReactElement {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: tickets,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<SupportTicket>({
    service: ticketService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getStatusIcon = (status: string): void => {
    switch (status) {
      case 'open': return <AlertCircle className="h-4 w-4 text-red-400" />
      case 'in-progress': return <Clock className="h-4 w-4 text-yellow-400" />
      case 'resolved': return <CheckCircle className="h-4 w-4 text-green-400" />
      case 'closed': return <XCircle className="h-4 w-4 text-gray-400" />
      default: return <AlertCircle className="h-4 w-4 text-gray-400" />
    }
  }

  const getStatusColor = (status: string): void => {
    switch (status) {
      case 'open': return 'bg-red-100 text-red-800 border-red-200'
      case 'in-progress': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'resolved': return 'bg-green-100 text-green-800 border-green-200'
      case 'closed': return 'bg-gray-100 text-gray-800 border-gray-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getPriorityColor = (priority: string): void => {
    switch (priority) {
      case 'critical': return 'text-red-400'
      case 'high': return 'text-orange-400'
      case 'medium': return 'text-yellow-400'
      case 'low': return 'text-green-400'
      default: return 'text-gray-400'
    }
  }

  // Table columns configuration
  const columns: TableColumn<SupportTicket>[] = [
    {
      key: 'title',
      label: t.title,
      sortable: true,
      render: (item: SupportTicket) => (
        <div className="flex items-center gap-2">
          <MessageSquare className="h-4 w-4 text-blue-400" />
          <div>
            <div className="font-medium text-white">
              {language === 'ar' ? item.titleAr : item.title}
            </div>
            <div className="text-sm text-white/60">
              {item.ticketId}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: SupportTicket) => (
        <div className="flex items-center gap-2">
          {getStatusIcon(item.status)}
          <Badge className={getStatusColor(item.status)}>
            {String(t[item.status as keyof typeof t])}
          </Badge>
        </div>
      )
    },
    {
      key: 'priority',
      label: t.priority,
      sortable: true,
      render: (item: SupportTicket) => (
        <span className={`font-medium ${getPriorityColor(item.priority)}`}>
          {String(t[item.priority as keyof typeof t])}
        </span>
      )
    },
    {
      key: 'customer',
      label: t.customer,
      render: (item: SupportTicket) => (
        <div className="flex items-center gap-1">
          <User className="h-3 w-3 text-purple-400" />
          <span className="text-white/80">
            {language === 'ar' ? item.customerNameAr : item.customerName}
          </span>
        </div>
      )
    },
    {
      key: 'assignedAgent',
      label: t.assignedAgent,
      render: (item: SupportTicket) => (
        <span className="text-white/80">
          {language === 'ar' ? item.assignedAgentAr : item.assignedAgent}
        </span>
      )
    },
    {
      key: 'category',
      label: t.category,
      render: (item: SupportTicket) => (
        <span className="text-white/80">
          {language === 'ar' ? item.categoryAr : item.category}
        </span>
      )
    },
    {
      key: 'createdAt',
      label: t.createdAt,
      sortable: true,
      render: (item: SupportTicket) => (
        <div className="flex items-center gap-1">
          <Clock className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">{item.createdAt}</span>
        </div>
      )
    },
    {
      key: 'rating',
      label: t.rating,
      render: (item: SupportTicket) => (
        item.customerSatisfactionRating ? (
          <div className="flex items-center gap-1">
            <Star className="h-3 w-3 text-yellow-400 fill-current" />
            <span className="text-white/80">{item.customerSatisfactionRating}/5</span>
          </div>
        ) : (
          <span className="text-white/50">-</span>
        )
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction<SupportTicket>[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: SupportTicket) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: SupportTicket) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: SupportTicket) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'status',
      label: t.status,
      options: [
        { label: t.open, value: 'open' },
        { label: t['in-progress'], value: 'in-progress' },
        { label: t.resolved, value: 'resolved' },
        { label: t.closed, value: 'closed' }
      ]
    },
    {
      key: 'priority',
      label: t.priority,
      options: [
        { label: t.low, value: 'low' },
        { label: t.medium, value: 'medium' },
        { label: t.high, value: 'high' },
        { label: t.critical, value: 'critical' }
      ]
    },
    {
      key: 'category',
      label: t.category,
      options: [
        { label: t.categories.technical, value: 'technical' },
        { label: t.categories.billing, value: 'billing' },
        { label: t.categories.general, value: 'general' },
        { label: t.categories.complaint, value: 'complaint' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'ticketId',
      label: t.ticketId,
      type: 'text',
      required: true
    },
    {
      name: 'title',
      label: t.title,
      type: 'text',
      required: true
    },
    {
      name: 'titleAr',
      label: t.title + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'description',
      label: t.description,
      type: 'textarea',
      required: true
    },
    {
      name: 'descriptionAr',
      label: t.description + ' (عربي)',
      type: 'textarea',
      required: true
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { label: t.open, value: 'open' },
        { label: t['in-progress'], value: 'in-progress' },
        { label: t.resolved, value: 'resolved' },
        { label: t.closed, value: 'closed' }
      ]
    },
    {
      name: 'priority',
      label: t.priority,
      type: 'select',
      required: true,
      options: [
        { label: t.low, value: 'low' },
        { label: t.medium, value: 'medium' },
        { label: t.high, value: 'high' },
        { label: t.critical, value: 'critical' }
      ]
    },
    {
      name: 'customerName',
      label: t.customer,
      type: 'text',
      required: true
    },
    {
      name: 'customerNameAr',
      label: t.customer + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'assignedAgent',
      label: t.assignedAgent,
      type: 'text'
    },
    {
      name: 'assignedAgentAr',
      label: t.assignedAgent + ' (عربي)',
      type: 'text'
    },
    {
      name: 'category',
      label: t.category,
      type: 'text',
      required: true
    },
    {
      name: 'categoryAr',
      label: t.category + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'customerSatisfactionRating',
      label: t.rating,
      type: 'number',
      min: 1,
      max: 5
    }
  ]

  // Event handlers
  const handleCreate = (): void => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = (): void => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<SupportTicket>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async (format: 'csv' | 'excel' | 'pdf' = 'csv') => {
    try {
      if (format === 'pdf') {
        // Use new PDF generation API for support ticket reports
        const response = await fetch(`http://localhost:8000/api/pdf/generate/support-ticket-report/?language=${language}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('auth_token') || ''}`,
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const pdfBlob = await response.blob()
        const url = window.URL.createObjectURL(pdfBlob)
        const link = document.createElement('a')
        link.href = url
        link.download = `support-tickets-report-${language}-${new Date().toISOString().split('T')[0]}.pdf`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
      } else {
        await exportData('csv')
      }
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.ticketManagement}
        data={tickets}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addTicket : modalMode === 'edit' ? t.editTicket : t.view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
