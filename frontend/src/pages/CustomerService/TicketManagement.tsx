/**
 * Ticket Management Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  MessageSquare,
  User,
  Clock,
  AlertCircle,
  CheckCircle,
  XCircle,
  Eye,
  Edit,
  Trash2,
  Star
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { ticketService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface TicketManagementProps {
  language: 'ar' | 'en'
}

interface SupportTicket {
  id: number
  ticketId: string
  title: string
  titleAr: string
  description: string
  descriptionAr: string
  status: 'open' | 'in-progress' | 'resolved' | 'closed'
  priority: 'low' | 'medium' | 'high' | 'critical'
  customerName: string
  customerNameAr: string
  assignedAgent: string
  assignedAgentAr: string
  category: string
  categoryAr: string
  createdAt: string
  updatedAt: string
  customerSatisfactionRating?: number
}

const translations = {
  ar: {
    ticketManagement: 'إدارة التذاكر',
    addTicket: 'إضافة تذكرة',
    editTicket: 'تعديل التذكرة',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذه التذكرة؟',
    searchPlaceholder: 'البحث في التذاكر...',
    ticketId: 'رقم التذكرة',
    title: 'العنوان',
    description: 'الوصف',
    status: 'الحالة',
    priority: 'الأولوية',
    customer: 'العميل',
    assignedAgent: 'الوكيل المكلف',
    category: 'الفئة',
    createdAt: 'تاريخ الإنشاء',
    rating: 'التقييم',
    open: 'مفتوح',
    'in-progress': 'قيد التنفيذ',
    resolved: 'محلول',
    closed: 'مغلق',
    low: 'منخفض',
    medium: 'متوسط',
    high: 'عالي',
    critical: 'حرج',
    categories: {
      technical: 'تقني',
      billing: 'فوترة',
      general: 'عام',
      complaint: 'شكوى'
    }
  },
  en: {
    ticketManagement: 'Ticket Management',
    addTicket: 'Add Ticket',
    editTicket: 'Edit Ticket',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this ticket?',
    searchPlaceholder: 'Search tickets...',
    ticketId: 'Ticket ID',
    title: 'Title',
    description: 'Description',
    status: 'Status',
    priority: 'Priority',
    customer: 'Customer',
    assignedAgent: 'Assigned Agent',
    category: 'Category',
    createdAt: 'Created At',
    rating: 'Rating',
    open: 'Open',
    'in-progress': 'In Progress',
    resolved: 'Resolved',
    closed: 'Closed',
    low: 'Low',
    medium: 'Medium',
    high: 'High',
    critical: 'Critical',
    categories: {
      technical: 'Technical',
      billing: 'Billing',
      general: 'General',
      complaint: 'Complaint'
    }
  }
}

// @ts-ignore
export default function TicketManagement({ language }: TicketManagementProps as any): (React as any).ReactElement {
  const [showModal, setShowModal] = useState(false as any)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: tickets,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<SupportTicket>({
    service: ticketService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getStatusIcon = (status: string): void => {
    switch (status) {
      case 'open': return <AlertCircle className="h-4 w-4 text-red-400" />
      case 'in-progress': return <Clock className="h-4 w-4 text-yellow-400" />
      case 'resolved': return <CheckCircle className="h-4 w-4 text-green-400" />
      case 'closed': return <XCircle className="h-4 w-4 text-gray-400" />
      default: return <AlertCircle className="h-4 w-4 text-gray-400" />
    }
  }

  const getStatusColor = (status: string): void => {
    switch (status) {
      case 'open': return 'bg-red-100 text-red-800 border-red-200'
      case 'in-progress': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'resolved': return 'bg-green-100 text-green-800 border-green-200'
      case 'closed': return 'bg-gray-100 text-gray-800 border-gray-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getPriorityColor = (priority: string): void => {
    switch (priority) {
      case 'critical': return 'text-red-400'
      case 'high': return 'text-orange-400'
      case 'medium': return 'text-yellow-400'
      case 'low': return 'text-green-400'
      default: return 'text-gray-400'
    }
  }

  // Table columns configuration
  const columns: TableColumn<SupportTicket>[] = [
    {
      key: 'title',
      label: (t as any).title,
      sortable: true,
      render: (item: SupportTicket) => (
        <div className="flex items-center gap-2">
          <MessageSquare className="h-4 w-4 text-blue-400" />
          <div>
            <div className="font-medium text-white">
              {language === 'ar' ? (item as any).titleAr : (item as any).title}
            </div>
            <div className="text-sm text-white/60">
              {(item as any).ticketId}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'status',
      label: (t as any).status,
      sortable: true,
      render: (item: SupportTicket) => (
        <div className="flex items-center gap-2">
          {getStatusIcon((item as any as any).status)}
          <Badge className={getStatusColor((item as any as any).status)}>
            {String(t[(item as any as any).status as keyof typeof t])}
          </Badge>
        </div>
      )
    },
    {
      key: 'priority',
      label: (t as any).priority,
      sortable: true,
      render: (item: SupportTicket) => (
        <span className={`font-medium ${getPriorityColor((item as any as any).priority)}`}>
          {String(t[(item as any as any).priority as keyof typeof t])}
        </span>
      )
    },
    {
      key: 'customer',
      label: (t as any).customer,
      render: (item: SupportTicket) => (
        <div className="flex items-center gap-1">
          <User className="h-3 w-3 text-purple-400" />
          <span className="text-white/80">
            {language === 'ar' ? (item as any).customerNameAr : (item as any).customerName}
          </span>
        </div>
      )
    },
    {
      key: 'assignedAgent',
      label: (t as any).assignedAgent,
      render: (item: SupportTicket) => (
        <span className="text-white/80">
          {language === 'ar' ? (item as any).assignedAgentAr : (item as any).assignedAgent}
        </span>
      )
    },
    {
      key: 'category',
      label: (t as any).category,
      render: (item: SupportTicket) => (
        <span className="text-white/80">
          {language === 'ar' ? (item as any).categoryAr : (item as any).category}
        </span>
      )
    },
    {
      key: 'createdAt',
      label: (t as any).createdAt,
      sortable: true,
      render: (item: SupportTicket) => (
        <div className="flex items-center gap-1">
          <Clock className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">{(item as any).createdAt}</span>
        </div>
      )
    },
    {
      key: 'rating',
      label: (t as any).rating,
      render: (item: SupportTicket) => (
        (item as any).customerSatisfactionRating ? (
          <div className="flex items-center gap-1">
            <Star className="h-3 w-3 text-yellow-400 fill-current" />
            <span className="text-white/80">{(item as any).customerSatisfactionRating}/5</span>
          </div>
        ) : (
          <span className="text-white/50">-</span>
        )
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction<SupportTicket>[] = [
    {
      label: (t as any).view,
      icon: Eye,
      onClick: (item: SupportTicket) => {
        selectItem(item as any)
        setModalMode('view' as any)
        setShowModal(true as any)
      },
      variant: 'ghost'
    },
    {
      label: (t as any).edit,
      icon: Edit,
      onClick: (item: SupportTicket) => {
        selectItem(item as any)
        setModalMode('edit' as any)
        setShowModal(true as any)
      },
      variant: 'ghost'
    },
    {
      label: (t as any).delete,
      icon: Trash2,
      onClick: async (item: SupportTicket) => {
        if ((window as any).confirm((t as any as any).confirmDelete)) {
          await deleteItem((item as any as any).id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'status',
      label: (t as any).status,
      options: [
        { label: (t as any).open, value: 'open' },
        { label: t['in-progress'], value: 'in-progress' },
        { label: (t as any).resolved, value: 'resolved' },
        { label: (t as any).closed, value: 'closed' }
      ]
    },
    {
      key: 'priority',
      label: (t as any).priority,
      options: [
        { label: (t as any).low, value: 'low' },
        { label: (t as any).medium, value: 'medium' },
        { label: (t as any).high, value: 'high' },
        { label: (t as any).critical, value: 'critical' }
      ]
    },
    {
      key: 'category',
      label: (t as any).category,
      options: [
        { label: (t as any).categories.technical, value: 'technical' },
        { label: (t as any).categories.billing, value: 'billing' },
        { label: (t as any).categories.general, value: 'general' },
        { label: (t as any).categories.complaint, value: 'complaint' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'ticketId',
      label: (t as any).ticketId,
      type: 'text',
      required: true
    },
    {
      name: 'title',
      label: (t as any).title,
      type: 'text',
      required: true
    },
    {
      name: 'titleAr',
      label: (t as any).title + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'description',
      label: (t as any).description,
      type: 'textarea',
      required: true
    },
    {
      name: 'descriptionAr',
      label: (t as any).description + ' (عربي)',
      type: 'textarea',
      required: true
    },
    {
      name: 'status',
      label: (t as any).status,
      type: 'select',
      required: true,
      options: [
        { label: (t as any).open, value: 'open' },
        { label: t['in-progress'], value: 'in-progress' },
        { label: (t as any).resolved, value: 'resolved' },
        { label: (t as any).closed, value: 'closed' }
      ]
    },
    {
      name: 'priority',
      label: (t as any).priority,
      type: 'select',
      required: true,
      options: [
        { label: (t as any).low, value: 'low' },
        { label: (t as any).medium, value: 'medium' },
        { label: (t as any).high, value: 'high' },
        { label: (t as any).critical, value: 'critical' }
      ]
    },
    {
      name: 'customerName',
      label: (t as any).customer,
      type: 'text',
      required: true
    },
    {
      name: 'customerNameAr',
      label: (t as any).customer + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'assignedAgent',
      label: (t as any).assignedAgent,
      type: 'text'
    },
    {
      name: 'assignedAgentAr',
      label: (t as any).assignedAgent + ' (عربي)',
      type: 'text'
    },
    {
      name: 'category',
      label: (t as any).category,
      type: 'text',
      required: true
    },
    {
      name: 'categoryAr',
      label: (t as any).category + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'customerSatisfactionRating',
      label: (t as any).rating,
      type: 'number',
      min: 1,
      max: 5
    }
  ]

  // Event handlers
  const handleCreate = (): void => {
    setModalMode('create' as any)
    setShowModal(true as any)
  }

  const handleModalClose = (): void => {
    setShowModal(false as any)
    selectItem(null as any)
    // @ts-ignore
    clearError( as any)
  }

  const handleSave = async (data: Partial<SupportTicket>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data as any)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem((selectedItem as any as any).id, data)
      }
      setShowModal(false as any)
    } catch (error) {
      (console as any).error('Save error:', error as any)
    }
  }

  const handleExport = async (format: 'csv' | 'excel' | 'pdf' = 'csv') => {
    try {
      if (format === 'pdf') {
        // Use new PDF generation API for support ticket reports
        const response = await fetch(`http://localhost:8000/api/pdf/generate/support-ticket-report/?language=${language}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${(localStorage as any as any).getItem('auth_token' as any) || ''}`,
          }
        })

        if (!(response as any).ok) {
          throw new Error(`HTTP error! status: ${(response as any as any).status}`)
        }

        // @ts-ignore
        const pdfBlob = await (response as any).blob( as any)
        const url = (window as any).URL.createObjectURL(pdfBlob as any)
        const link = (document as any).createElement('a' as any)
        (link as any).href = url
        // @ts-ignore
        (link as any).download = `support-tickets-report-${language}-${new Date( as any).toISOString( as any).split('T' as any)[0]}.pdf`
        (document as any).body.appendChild(link as any)
        // @ts-ignore
        (link as any).click( as any)
        (document as any).body.removeChild(link as any)
        (window as any).URL.revokeObjectURL(url as any)
      } else {
        await exportData('csv' as any)
      }
    } catch (error) {
      (console as any).error('Export error:', error as any)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={(t as any).ticketManagement}
        data={tickets}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={(t as any).searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? (t as any).addTicket : modalMode === 'edit' ? (t as any).editTicket : (t as any).view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
