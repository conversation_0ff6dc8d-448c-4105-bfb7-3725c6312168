import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Building,
  Plus,
  Search,
  Users,
  Edit,
  Trash2,
  TrendingUp,
  Calendar,
  BarChart3
} from 'lucide-react'

interface DepartmentsProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    departments: 'الأقسام',
    addDepartment: 'إضافة قسم',
    searchDepartments: 'البحث في الأقسام',
    totalDepartments: 'إجمالي الأقسام',
    totalEmployees: 'إجمالي الموظفين',
    avgEmployeesPerDept: 'متوسط الموظفين لكل قسم',
    departmentList: 'قائمة الأقسام',
    name: 'اسم القسم',
    description: 'الوصف',
    employeeCount: 'عدد الموظفين',
    manager: 'المدير',
    createdDate: 'تاريخ الإنشاء',
    actions: 'الإجراءات',
    edit: 'تعديل',
    delete: 'حذف',
    view: 'عرض',
    searchPlaceholder: 'البحث بالاسم أو الوصف...',
    noDepartments: 'لا يوجد أقسام',
    loading: 'جاري التحميل...',
    employees: 'موظف',
    manageDepartments: 'إدارة الأقسام والهيكل التنظيمي'
  },
  en: {
    departments: 'Departments',
    addDepartment: 'Add Department',
    searchDepartments: 'Search Departments',
    totalDepartments: 'Total Departments',
    totalEmployees: 'Total Employees',
    avgEmployeesPerDept: 'Avg Employees per Dept',
    departmentList: 'Department List',
    name: 'Department Name',
    description: 'Description',
    employeeCount: 'Employee Count',
    manager: 'Manager',
    createdDate: 'Created Date',
    actions: 'Actions',
    edit: 'Edit',
    delete: 'Delete',
    view: 'View',
    searchPlaceholder: 'Search by name or description...',
    noDepartments: 'No departments found',
    loading: 'Loading...',
    employees: 'employees',
    manageDepartments: 'Manage departments and organizational structure'
  }
}

// Sample department data
const sampleDepartments = [
  {
    id: 1,
    name: 'الموارد البشرية',
    nameEn: 'Human Resources',
    description: 'إدارة شؤون الموظفين والتوظيف',
    descriptionEn: 'Managing employee affairs and recruitment',
    employeeCount: 8,
    manager: 'أحمد حسن',
    managerEn: 'Ahmed Hassan',
    createdDate: '2020-01-15',
    color: 'bg-blue-500'
  },
  {
    id: 2,
    name: 'تكنولوجيا المعلومات',
    nameEn: 'Information Technology',
    description: 'تطوير وصيانة الأنظمة التقنية',
    descriptionEn: 'Development and maintenance of technical systems',
    employeeCount: 15,
    manager: 'فاطمة علي',
    managerEn: 'Fatima Ali',
    createdDate: '2020-02-01',
    color: 'bg-green-500'
  },
  {
    id: 3,
    name: 'المالية',
    nameEn: 'Finance',
    description: 'إدارة الشؤون المالية والمحاسبة',
    descriptionEn: 'Managing financial affairs and accounting',
    employeeCount: 6,
    manager: 'عمر سالم',
    managerEn: 'Omar Salem',
    createdDate: '2020-01-20',
    color: 'bg-purple-500'
  },
  {
    id: 4,
    name: 'التسويق',
    nameEn: 'Marketing',
    description: 'الترويج والتسويق للمنتجات والخدمات',
    descriptionEn: 'Promotion and marketing of products and services',
    employeeCount: 10,
    manager: 'سارة محمود',
    managerEn: 'Sara Mahmoud',
    createdDate: '2020-03-10',
    color: 'bg-orange-500'
  },
  {
    id: 5,
    name: 'العمليات',
    nameEn: 'Operations',
    description: 'إدارة العمليات التشغيلية اليومية',
    descriptionEn: 'Managing daily operational processes',
    employeeCount: 12,
    manager: 'خالد إبراهيم',
    managerEn: 'Khalid Ibrahim',
    createdDate: '2020-02-15',
    color: 'bg-red-500'
  }
]

export default function Departments({ language }: DepartmentsProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [departments] = useState(sampleDepartments)
  const t = translations[language]

  const filteredDepartments = departments.filter(dept =>
    (language === 'ar' ? dept.name : dept.nameEn)
      .toLowerCase()
      .includes(searchTerm.toLowerCase()) ||
    (language === 'ar' ? dept.description : dept.descriptionEn)
      .toLowerCase()
      .includes(searchTerm.toLowerCase())
  )

  const totalEmployees = departments.reduce((sum, dept) => sum + dept.employeeCount, 0)
  const avgEmployees = Math.round(totalEmployees / departments.length)

  const stats = [
    {
      title: t.totalDepartments,
      value: departments.length.toString(),
      icon: Building,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      title: t.totalEmployees,
      value: totalEmployees.toString(),
      icon: Users,
      color: 'text-green-600',
      bgColor: 'bg-green-100'
    },
    {
      title: t.avgEmployeesPerDept,
      value: avgEmployees.toString(),
      icon: BarChart3,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100'
    }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-4xl font-bold text-white drop-shadow-lg">
            {t.departments}
          </h1>
          <p className="text-white/80 mt-2 text-lg">
            {t.manageDepartments}
          </p>
        </div>
        <Button className="gradient-bg-blue hover:scale-105 transform transition-all duration-300 glow-hover text-white font-semibold px-6 py-3">
          <Plus className="h-5 w-5 mr-2" />
          {t.addDepartment}
        </Button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {stats.map((stat, index) => (
          <Card key={index} className="modern-card border-0 hover:scale-105 transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-white/80">
                    {stat.title}
                  </p>
                  <p className="text-3xl font-bold text-white mt-2">
                    {stat.value}
                  </p>
                </div>
                <div className="p-4 rounded-2xl gradient-bg-purple glow floating">
                  <stat.icon className="h-7 w-7 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Search */}
      <Card className="modern-card border-0">
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <Label htmlFor="search" className="text-white/90 font-medium">{t.searchDepartments}</Label>
              <div className="relative mt-2">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60 h-5 w-5" />
                <Input
                  id="search"
                  placeholder={t.searchPlaceholder}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="glass text-white placeholder-white/60 border-white/30 focus:border-white/50 pl-12 py-3"
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Department Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredDepartments.map((department) => (
          <Card key={department.id} className="modern-card border-0 hover:scale-105 transition-all duration-300">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="w-14 h-14 gradient-bg-alt rounded-2xl flex items-center justify-center glow floating">
                  <Building className="h-7 w-7 text-white" />
                </div>
                <div className="flex gap-2">
                  <Button variant="ghost" size="sm" className="glass text-white hover:bg-white/20 p-2">
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm" className="glass text-red-300 hover:bg-red-500/20 p-2">
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <CardTitle className="text-xl text-white mt-4">
                {language === 'ar' ? department.name : department.nameEn}
              </CardTitle>
              <CardDescription className="text-white/70">
                {language === 'ar' ? department.description : department.descriptionEn}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 glass rounded-xl">
                  <span className="text-sm text-white/80">
                    {t.employeeCount}
                  </span>
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4 text-white/60" />
                    <span className="font-medium text-white">
                      {department.employeeCount} {t.employees}
                    </span>
                  </div>
                </div>

                <div className="flex items-center justify-between p-3 glass rounded-xl">
                  <span className="text-sm text-white/80">
                    {t.manager}
                  </span>
                  <span className="font-medium text-white">
                    {language === 'ar' ? department.manager : department.managerEn}
                  </span>
                </div>

                <div className="flex items-center justify-between p-3 glass rounded-xl">
                  <span className="text-sm text-white/80">
                    {t.createdDate}
                  </span>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-white/60" />
                    <span className="text-sm text-white">
                      {new Date(department.createdDate).toLocaleDateString(
                        language === 'ar' ? 'ar-SA' : 'en-US'
                      )}
                    </span>
                  </div>
                </div>
              </div>

              <div className="mt-6 pt-4 border-t border-white/20">
                <Button className="w-full glass text-white border-white/30 hover:bg-white/20 py-3">
                  {t.view} {t.employees}
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredDepartments.length === 0 && (
        <Card className="modern-card border-0">
          <CardContent className="p-12">
            <div className="text-center">
              <Building className="h-16 w-16 text-white/40 mx-auto mb-4 floating" />
              <p className="text-white/60 text-lg">{t.noDepartments}</p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
