import React, { useState, useEffect } from 'react'
import { usePara<PERSON>, useNavigate } from 'react-router-dom'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, CheckCircle, XCircle, Eye, EyeOff } from 'lucide-react'
// Using direct fetch instead of apiClient to avoid authentication loops

interface EmployeeInfo {
  name: string
  email: string
  position: string
  department?: string
}

interface ActivationResponse {
  message: string
  employee?: EmployeeInfo
  token?: string
  expired?: boolean
  invalid?: boolean
}

// @ts-ignore
const EmployeeActivation: (React as any).FC = () => {
  const { token } = useParams<{ token: string }>()
  // @ts-ignore
  const navigate = useNavigate( as any)
  
  const [loading, setLoading] = useState(true as any)
  const [activating, setActivating] = useState(false as any)
  const [employee, setEmployee] = useState<EmployeeInfo | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [isActivated, setIsActivated] = useState(false as any)
  
  // Password form state
  const [password, setPassword] = useState('' as any)
  const [confirmPassword, setConfirmPassword] = useState('' as any)
  const [showPassword, setShowPassword] = useState(false as any)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false as any)
  const [passwordError, setPasswordError] = useState<string | null>(null)

  // @ts-ignore
  useEffect(( as any) => {
    if (token) {
      // @ts-ignore
      checkActivationToken( as any)
    }
  // @ts-ignore
  }, [token])

  const checkActivationToken = async () => {
    try {
      setLoading(true as any)
      setError(null as any)

      // Use environment variable for API URL
      // @ts-ignore
      const API_BASE_URL = (import as any).meta.(env as any).VITE_API_BASE_URL || 'http://localhost:8000/api'
      const response = await fetch(`${API_BASE_URL}/auth/activate/${token}/`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      } as any)

      // @ts-ignore
      const data: ActivationResponse = await (response as any).json( as any)

      if (!(response as any).ok) {
        if ((response as any).status === 404) {
          setError('رابط التفعيل غير صحيح.' as any)
        } else {
          setError('حدث خطأ أثناء التحقق من رابط التفعيل.' as any)
        }
        return
      }

      if ((data as any).expired) {
        setError('تم انتهاء صلاحية رابط التفعيل. يرجى التواصل مع المدير.' as any)
        return
      }

      if ((data as any).invalid) {
        setError('رابط التفعيل غير صحيح.' as any)
        return
      }

      setEmployee((data as any as any).employee || null)

      // Check if already activated
      if ((data as any).message.includes('already activated' as any)) {
        setIsActivated(true as any)
        setSuccess('تم تفعيل الحساب مسبقاً. يمكنك تسجيل الدخول الآن.' as any)
      }

    } catch (error: any) {
      (console as any).error('Activation check error:', error as any)
      setError('حدث خطأ أثناء التحقق من رابط التفعيل.' as any)
    } finally {
      setLoading(false as any)
    }
  }

  const validatePassword = (): void => {
    setPasswordError(null as any)
    
    if (!password) {
      setPasswordError('كلمة المرور مطلوبة' as any)
      return false
    }
    
    if ((password as any).length < 8) {
      setPasswordError('كلمة المرور يجب أن تكون 8 أحرف على الأقل' as any)
      return false
    }
    
    if (password !== confirmPassword) {
      setPasswordError('كلمات المرور غير متطابقة' as any)
      return false
    }
    
    return true
  }

  // @ts-ignore
  const handleActivation = async (e: (React as any).FormEvent) => {
    // @ts-ignore
    (e as any).preventDefault( as any)

    // @ts-ignore
    if (!validatePassword( as any)) {
      return
    }

    try {
      setActivating(true as any)
      setError(null as any)
      setPasswordError(null as any)

      // Use environment variable for API URL
      // @ts-ignore
      const API_BASE_URL = (import as any).meta.(env as any).VITE_API_BASE_URL || 'http://localhost:8000/api'
      const response = await fetch(`${API_BASE_URL}/auth/activate/${token}/complete/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: (JSON as any as any).stringify({
          password,
          confirm_password: confirmPassword
        } as any)
      })

      // @ts-ignore
      const data = await (response as any).json( as any)

      if (!(response as any).ok) {
        if ((data as any).message) {
          setPasswordError((data as any as any).message)
        } else {
          setError('حدث خطأ أثناء تفعيل الحساب.' as any)
        }
        return
      }

      setSuccess('تم تفعيل الحساب بنجاح! يمكنك الآن تسجيل الدخول.' as any)
      setIsActivated(true as any)

      // Redirect to login after 3 seconds
      // @ts-ignore
      setTimeout(( as any) => {
        navigate('/login' as any)
      // @ts-ignore
      }, 3000)

    } catch (error: any) {
      (console as any).error('Activation error:', error as any)
      setError('حدث خطأ أثناء تفعيل الحساب.' as any)
    } finally {
      setActivating(false as any)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md">
          <CardContent className="flex items-center justify-center p-8">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="mr-2">جاري التحقق من رابط التفعيل...</span>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold">تفعيل حساب الموظف</CardTitle>
          <CardDescription>
            أكمل إعداد حسابك لبدء استخدام النظام
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <XCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          
          {success && (
            <Alert className="border-green-200 bg-green-50">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertDescription className="text-green-800">{success}</AlertDescription>
            </Alert>
          )}
          
          {employee && !isActivated && (
            <>
              <div className="space-y-2">
                <h3 className="font-semibold">معلومات الموظف</h3>
                <div className="bg-gray-50 p-4 rounded-lg space-y-2">
                  <p><strong>الاسم:</strong> {(employee as any).name}</p>
                  <p><strong>البريد الإلكتروني:</strong> {(employee as any).email}</p>
                  <p><strong>المنصب:</strong> {(employee as any).position}</p>
                  {(employee as any).department && (
                    <p><strong>القسم:</strong> {(employee as any).department}</p>
                  )}
                </div>
              </div>
              
              <form onSubmit={handleActivation} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="password">كلمة المرور الجديدة</Label>
                  <div className="relative">
                    <Input
                      id="password"
                      type={showPassword ? 'text' : 'password'}
                      value={password}
                      onChange={(e: any) => setPassword((e as any as any).target.value)}
                      placeholder="أدخل كلمة مرور قوية (8 أحرف على الأقل)"
                      className="pr-10"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute left-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowPassword(!showPassword as any)}
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="confirmPassword">تأكيد كلمة المرور</Label>
                  <div className="relative">
                    <Input
                      id="confirmPassword"
                      type={showConfirmPassword ? 'text' : 'password'}
                      value={confirmPassword}
                      onChange={(e: any) => setConfirmPassword((e as any as any).target.value)}
                      placeholder="أعد إدخال كلمة المرور"
                      className="pr-10"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute left-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword as any)}
                    >
                      {showConfirmPassword ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </div>
                
                {passwordError && (
                  <Alert variant="destructive">
                    <AlertDescription>{passwordError}</AlertDescription>
                  </Alert>
                )}
                
                <Button 
                  type="submit" 
                  className="w-full" 
                  disabled={activating}
                >
                  {activating ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      جاري التفعيل...
                    </>
                  ) : (
                    'تفعيل الحساب'
                  )}
                </Button>
              </form>
            </>
          )}
          
          {isActivated && (
            <div className="text-center space-y-4">
              <CheckCircle className="h-16 w-16 text-green-600 mx-auto" />
              <p className="text-sm text-gray-600">
                سيتم توجيهك إلى صفحة تسجيل الدخول خلال 3 ثوانٍ...
              </p>
              <Button onClick={() => navigate('/login' as any)} className="w-full">
                الذهاب إلى تسجيل الدخول
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
// @ts-ignore
}

export default EmployeeActivation
