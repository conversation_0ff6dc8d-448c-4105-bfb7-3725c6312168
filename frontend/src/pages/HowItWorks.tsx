import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  Users,
  Building,
  BarChart3,
  Shield,
  Globe,
  Zap,
  CheckCircle,
  ArrowRight,
  Star,
  Database,
  Cloud,
  Smartphone,
  Monitor,
  Server,
  Lock,
  Workflow,
  GitBranch,
  Layers,
  Code,
  Settings,
  RefreshCw,
  ChevronRight,
  Play
} from 'lucide-react'

interface HowItWorksProps {
  language: 'ar' | 'en'
  setLanguage: (lang: 'ar' | 'en') => void
}

const translations = {
  ar: {
    title: 'كيف يعمل نمو',
    subtitle: 'فهم شامل لهندسة وعمل نظام إدارة المؤسسات',

    // Architecture Section
    architectureTitle: 'هندسة النظام',
    architectureSubtitle: 'بنية تقنية متقدمة وقابلة للتوسع',

    frontendTitle: 'الواجهة الأمامية',
    frontendDesc: 'واجهة مستخدم حديثة مبنية بـ React و TypeScript',

    backendTitle: 'الخادم الخلفي',
    backendDesc: 'API قوي مبني بـ Django مع قاعدة بيانات PostgreSQL',

    cloudTitle: 'البنية السحابية',
    cloudDesc: 'استضافة سحابية آمنة مع نسخ احتياطية تلقائية',

    securityTitle: 'الأمان',
    securityDesc: 'تشفير متقدم وحماية متعددة الطبقات',

    // Process Section
    processTitle: 'عملية التطوير',
    processSubtitle: 'منهجية احترافية لضمان الجودة والأداء',

    step1Title: 'التخطيط والتحليل',
    step1Desc: 'دراسة المتطلبات وتصميم الحلول المناسبة',

    step2Title: 'التطوير والبرمجة',
    step2Desc: 'كتابة الكود باستخدام أحدث التقنيات والمعايير',

    step3Title: 'الاختبار والجودة',
    step3Desc: 'اختبارات شاملة لضمان الأداء والموثوقية',

    step4Title: 'النشر والصيانة',
    step4Desc: 'نشر آمن مع دعم مستمر وتحديثات دورية',

    // Technology Stack
    techStackTitle: 'المكدس التقني',
    techStackSubtitle: 'تقنيات حديثة ومجربة لأفضل أداء',

    // Features
    featuresTitle: 'الميزات الرئيسية',
    featuresSubtitle: 'حلول شاملة لجميع احتياجات مؤسستك',

    // Integration
    integrationTitle: 'التكامل والربط',
    integrationSubtitle: 'ربط سلس مع جميع الأنظمة والأدوات',

    // CTA
    ctaTitle: 'جاهز لتجربة النظام؟',
    ctaSubtitle: 'ابدأ رحلتك مع نمو اليوم',
    startTrial: 'ابدأ التجربة المجانية',
    contactUs: 'تواصل معنا'
  },
  en: {
    title: 'How Numu Works',
    subtitle: 'Comprehensive understanding of EMS architecture and operations',

    // Architecture Section
    architectureTitle: 'System Architecture',
    architectureSubtitle: 'Advanced and scalable technical infrastructure',

    frontendTitle: 'Frontend',
    frontendDesc: 'Modern user interface built with React & TypeScript',

    backendTitle: 'Backend',
    backendDesc: 'Robust API built with Django and PostgreSQL database',

    cloudTitle: 'Cloud Infrastructure',
    cloudDesc: 'Secure cloud hosting with automatic backups',

    securityTitle: 'Security',
    securityDesc: 'Advanced encryption and multi-layer protection',

    // Process Section
    processTitle: 'Development Process',
    processSubtitle: 'Professional methodology ensuring quality and performance',

    step1Title: 'Planning & Analysis',
    step1Desc: 'Requirements study and appropriate solution design',

    step2Title: 'Development & Programming',
    step2Desc: 'Code writing using latest technologies and standards',

    step3Title: 'Testing & Quality',
    step3Desc: 'Comprehensive testing ensuring performance and reliability',

    step4Title: 'Deployment & Maintenance',
    step4Desc: 'Secure deployment with continuous support and regular updates',

    // Technology Stack
    techStackTitle: 'Technology Stack',
    techStackSubtitle: 'Modern and proven technologies for optimal performance',

    // Features
    featuresTitle: 'Key Features',
    featuresSubtitle: 'Comprehensive solutions for all your organization needs',

    // Integration
    integrationTitle: 'Integration & Connectivity',
    integrationSubtitle: 'Seamless connection with all systems and tools',

    // CTA
    ctaTitle: 'Ready to Experience the System?',
    ctaSubtitle: 'Start your journey with Numu today',
    startTrial: 'Start Free Trial',
    contactUs: 'Contact Us'
  }
}

export default function HowItWorks({ language, setLanguage }: HowItWorksProps) {
  const [activeTab, setActiveTab] = useState('architecture')
  const t = translations[language]
  const isRTL = language === 'ar'

  return (
    <div className={`min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Background Effects */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
        <div className="absolute top-40 left-40 w-80 h-80 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"></div>
      </div>

      {/* Navigation */}
      <nav className="relative z-10 p-6">
        <div className="max-w-7xl mx-auto flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center">
              <span className="text-white font-bold text-xl">ن</span>
            </div>
            <span className="text-white text-xl font-bold">نمو</span>
          </div>

          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              onClick={() => setLanguage(language === 'ar' ? 'en' : 'ar')}
              className="glass-button"
            >
              <Globe className="h-4 w-4 mr-2" />
              {language === 'ar' ? 'English' : 'العربية'}
            </Button>
            <Button className="glass-button bg-gradient-to-r from-blue-500 to-purple-500">
              {t.startTrial}
            </Button>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative z-10 pt-20 pb-32">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-5xl md:text-7xl font-bold text-white mb-6 leading-tight animate-fade-in-up">
            {t.title}
          </h1>
          <p className="text-xl md:text-2xl text-white/80 mb-12 max-w-4xl mx-auto animate-fade-in-up animation-delay-200">
            {t.subtitle}
          </p>
        </div>
      </section>

      {/* Tab Navigation */}
      <section className="relative z-10 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-center mb-16">
            <div className="glass-card p-2 rounded-2xl border border-white/20">
              <div className="flex space-x-2">
                {[
                  { id: 'architecture', label: language === 'ar' ? 'الهندسة' : 'Architecture', icon: Layers },
                  { id: 'process', label: language === 'ar' ? 'العملية' : 'Process', icon: Workflow },
                  { id: 'technology', label: language === 'ar' ? 'التقنيات' : 'Technology', icon: Code },
                  { id: 'features', label: language === 'ar' ? 'الميزات' : 'Features', icon: Star }
                ].map((tab) => (
                  <Button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`glass-button px-6 py-3 ${
                      activeTab === tab.id
                        ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white'
                        : 'text-white/70 hover:text-white'
                    }`}
                  >
                    <tab.icon className="h-5 w-5 mr-2" />
                    {tab.label}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Content Sections */}
      <section className="relative z-10 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

          {/* Architecture Tab */}
          {activeTab === 'architecture' && (
            <div className="animate-fade-in-up">
              <div className="text-center mb-16">
                <h2 className="text-4xl font-bold text-white mb-6">{t.architectureTitle}</h2>
                <p className="text-xl text-white/70 max-w-3xl mx-auto">{t.architectureSubtitle}</p>
              </div>

              {/* Architecture Diagram */}
              <div className="mb-16">
                <div className="glass-card p-12 rounded-3xl border border-white/20">
                  <div className="relative">
                    {/* Architecture SVG */}
                    <svg className="w-full h-96" viewBox="0 0 800 400" fill="none">
                      {/* User Layer */}
                      <rect x="50" y="50" width="150" height="80" rx="10" fill="url(#gradient1)" />
                      <text x="125" y="95" textAnchor="middle" fill="white" className="text-sm font-semibold">
                        {language === 'ar' ? 'المستخدمون' : 'Users'}
                      </text>

                      {/* Frontend Layer */}
                      <rect x="250" y="50" width="150" height="80" rx="10" fill="url(#gradient2)" />
                      <text x="325" y="95" textAnchor="middle" fill="white" className="text-sm font-semibold">
                        {language === 'ar' ? 'الواجهة الأمامية' : 'Frontend'}
                      </text>

                      {/* Backend Layer */}
                      <rect x="450" y="50" width="150" height="80" rx="10" fill="url(#gradient3)" />
                      <text x="525" y="95" textAnchor="middle" fill="white" className="text-sm font-semibold">
                        {language === 'ar' ? 'الخادم الخلفي' : 'Backend'}
                      </text>

                      {/* Database Layer */}
                      <rect x="650" y="50" width="100" height="80" rx="10" fill="url(#gradient4)" />
                      <text x="700" y="95" textAnchor="middle" fill="white" className="text-sm font-semibold">
                        {language === 'ar' ? 'قاعدة البيانات' : 'Database'}
                      </text>

                      {/* Security Layer */}
                      <rect x="150" y="200" width="500" height="60" rx="10" fill="url(#gradient5)" fillOpacity="0.3" />
                      <text x="400" y="235" textAnchor="middle" fill="white" className="text-sm font-semibold">
                        {language === 'ar' ? 'طبقة الأمان والحماية' : 'Security & Protection Layer'}
                      </text>

                      {/* Cloud Infrastructure */}
                      <rect x="100" y="320" width="600" height="60" rx="10" fill="url(#gradient6)" fillOpacity="0.2" />
                      <text x="400" y="355" textAnchor="middle" fill="white" className="text-sm font-semibold">
                        {language === 'ar' ? 'البنية التحتية السحابية' : 'Cloud Infrastructure'}
                      </text>

                      {/* Arrows */}
                      <defs>
                        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                          <polygon points="0 0, 10 3.5, 0 7" fill="white" />
                        </marker>

                        <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                          <stop offset="0%" stopColor="#3B82F6" />
                          <stop offset="100%" stopColor="#1D4ED8" />
                        </linearGradient>
                        <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
                          <stop offset="0%" stopColor="#8B5CF6" />
                          <stop offset="100%" stopColor="#7C3AED" />
                        </linearGradient>
                        <linearGradient id="gradient3" x1="0%" y1="0%" x2="100%" y2="100%">
                          <stop offset="0%" stopColor="#10B981" />
                          <stop offset="100%" stopColor="#059669" />
                        </linearGradient>
                        <linearGradient id="gradient4" x1="0%" y1="0%" x2="100%" y2="100%">
                          <stop offset="0%" stopColor="#F59E0B" />
                          <stop offset="100%" stopColor="#D97706" />
                        </linearGradient>
                        <linearGradient id="gradient5" x1="0%" y1="0%" x2="100%" y2="100%">
                          <stop offset="0%" stopColor="#EF4444" />
                          <stop offset="100%" stopColor="#DC2626" />
                        </linearGradient>
                        <linearGradient id="gradient6" x1="0%" y1="0%" x2="100%" y2="100%">
                          <stop offset="0%" stopColor="#6366F1" />
                          <stop offset="100%" stopColor="#4F46E5" />
                        </linearGradient>
                      </defs>

                      {/* Connection Lines */}
                      <line x1="200" y1="90" x2="250" y2="90" stroke="white" strokeWidth="2" markerEnd="url(#arrowhead)" />
                      <line x1="400" y1="90" x2="450" y2="90" stroke="white" strokeWidth="2" markerEnd="url(#arrowhead)" />
                      <line x1="600" y1="90" x2="650" y2="90" stroke="white" strokeWidth="2" markerEnd="url(#arrowhead)" />
                    </svg>
                  </div>
                </div>
              </div>

              {/* Architecture Components */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <Card className="glass-card border-white/20 text-center">
                  <CardHeader>
                    <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                      <Monitor className="h-8 w-8 text-white" />
                    </div>
                    <CardTitle className="text-white">{t.frontendTitle}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-white/70">{t.frontendDesc}</p>
                    <div className="mt-4 flex flex-wrap gap-2">
                      <span className="glass-button px-3 py-1 text-xs text-white/80 border border-white/20 rounded-full">React</span>
                      <span className="glass-button px-3 py-1 text-xs text-white/80 border border-white/20 rounded-full">TypeScript</span>
                      <span className="glass-button px-3 py-1 text-xs text-white/80 border border-white/20 rounded-full">Tailwind CSS</span>
                    </div>
                  </CardContent>
                </Card>

                <Card className="glass-card border-white/20 text-center">
                  <CardHeader>
                    <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                      <Server className="h-8 w-8 text-white" />
                    </div>
                    <CardTitle className="text-white">{t.backendTitle}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-white/70">{t.backendDesc}</p>
                    <div className="mt-4 flex flex-wrap gap-2">
                      <span className="glass-button px-3 py-1 text-xs text-white/80 border border-white/20 rounded-full">Django</span>
                      <span className="glass-button px-3 py-1 text-xs text-white/80 border border-white/20 rounded-full">PostgreSQL</span>
                      <span className="glass-button px-3 py-1 text-xs text-white/80 border border-white/20 rounded-full">Redis</span>
                    </div>
                  </CardContent>
                </Card>

                <Card className="glass-card border-white/20 text-center">
                  <CardHeader>
                    <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                      <Cloud className="h-8 w-8 text-white" />
                    </div>
                    <CardTitle className="text-white">{t.cloudTitle}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-white/70">{t.cloudDesc}</p>
                    <div className="mt-4 flex flex-wrap gap-2">
                      <span className="glass-button px-3 py-1 text-xs text-white/80 border border-white/20 rounded-full">AWS</span>
                      <span className="glass-button px-3 py-1 text-xs text-white/80 border border-white/20 rounded-full">Docker</span>
                      <span className="glass-button px-3 py-1 text-xs text-white/80 border border-white/20 rounded-full">Kubernetes</span>
                    </div>
                  </CardContent>
                </Card>

                <Card className="glass-card border-white/20 text-center">
                  <CardHeader>
                    <div className="w-16 h-16 bg-gradient-to-r from-red-500 to-red-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                      <Shield className="h-8 w-8 text-white" />
                    </div>
                    <CardTitle className="text-white">{t.securityTitle}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-white/70">{t.securityDesc}</p>
                    <div className="mt-4 flex flex-wrap gap-2">
                      <span className="glass-button px-3 py-1 text-xs text-white/80 border border-white/20 rounded-full">SSL/TLS</span>
                      <span className="glass-button px-3 py-1 text-xs text-white/80 border border-white/20 rounded-full">OAuth 2.0</span>
                      <span className="glass-button px-3 py-1 text-xs text-white/80 border border-white/20 rounded-full">AES-256</span>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          )}

          {/* Process Tab */}
          {activeTab === 'process' && (
            <div className="animate-fade-in-up">
              <div className="text-center mb-16">
                <h2 className="text-4xl font-bold text-white mb-6">{t.processTitle}</h2>
                <p className="text-xl text-white/70 max-w-3xl mx-auto">{t.processSubtitle}</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <div className="text-center">
                  <div className="relative mb-8">
                    <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center mx-auto mb-4 floating">
                      <Settings className="h-10 w-10 text-white" />
                    </div>
                    <div className="absolute top-10 left-1/2 transform -translate-x-1/2 w-px h-16 bg-gradient-to-b from-blue-500 to-transparent lg:hidden"></div>
                  </div>
                  <h3 className="text-xl font-bold text-white mb-4">{t.step1Title}</h3>
                  <p className="text-white/70">{t.step1Desc}</p>
                </div>

                <div className="text-center">
                  <div className="relative mb-8">
                    <div className="w-20 h-20 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4 floating animation-delay-2000">
                      <Code className="h-10 w-10 text-white" />
                    </div>
                    <div className="absolute top-10 left-1/2 transform -translate-x-1/2 w-px h-16 bg-gradient-to-b from-purple-500 to-transparent lg:hidden"></div>
                  </div>
                  <h3 className="text-xl font-bold text-white mb-4">{t.step2Title}</h3>
                  <p className="text-white/70">{t.step2Desc}</p>
                </div>

                <div className="text-center">
                  <div className="relative mb-8">
                    <div className="w-20 h-20 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4 floating animation-delay-4000">
                      <CheckCircle className="h-10 w-10 text-white" />
                    </div>
                    <div className="absolute top-10 left-1/2 transform -translate-x-1/2 w-px h-16 bg-gradient-to-b from-green-500 to-transparent lg:hidden"></div>
                  </div>
                  <h3 className="text-xl font-bold text-white mb-4">{t.step3Title}</h3>
                  <p className="text-white/70">{t.step3Desc}</p>
                </div>

                <div className="text-center">
                  <div className="relative mb-8">
                    <div className="w-20 h-20 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center mx-auto mb-4 floating animation-delay-1000">
                      <RefreshCw className="h-10 w-10 text-white" />
                    </div>
                  </div>
                  <h3 className="text-xl font-bold text-white mb-4">{t.step4Title}</h3>
                  <p className="text-white/70">{t.step4Desc}</p>
                </div>
              </div>
            </div>
          )}

          {/* Technology Tab */}
          {activeTab === 'technology' && (
            <div className="animate-fade-in-up">
              <div className="text-center mb-16">
                <h2 className="text-4xl font-bold text-white mb-6">{t.techStackTitle}</h2>
                <p className="text-xl text-white/70 max-w-3xl mx-auto">{t.techStackSubtitle}</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                {/* Frontend Technologies */}
                <Card className="glass-card border-white/20">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center">
                      <Monitor className="h-6 w-6 mr-2 text-blue-400" />
                      {language === 'ar' ? 'تقنيات الواجهة الأمامية' : 'Frontend Technologies'}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-white/80">React 18</span>
                      <span className="bg-blue-500/20 text-blue-300 px-2 py-1 rounded-full text-xs">Latest</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-white/80">TypeScript</span>
                      <span className="bg-blue-500/20 text-blue-300 px-2 py-1 rounded-full text-xs">5.0+</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-white/80">Tailwind CSS</span>
                      <span className="bg-green-500/20 text-green-300 px-2 py-1 rounded-full text-xs">3.0+</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-white/80">Vite</span>
                      <span className="bg-purple-500/20 text-purple-300 px-2 py-1 rounded-full text-xs">Fast</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-white/80">Redux Toolkit</span>
                      <span className="bg-orange-500/20 text-orange-300 px-2 py-1 rounded-full text-xs">State</span>
                    </div>
                  </CardContent>
                </Card>

                {/* Backend Technologies */}
                <Card className="glass-card border-white/20">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center">
                      <Server className="h-6 w-6 mr-2 text-green-400" />
                      {language === 'ar' ? 'تقنيات الخادم الخلفي' : 'Backend Technologies'}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-white/80">Django</span>
                      <span className="bg-green-500/20 text-green-300 px-2 py-1 rounded-full text-xs">4.2+</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-white/80">PostgreSQL</span>
                      <span className="bg-blue-500/20 text-blue-300 px-2 py-1 rounded-full text-xs">15+</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-white/80">Redis</span>
                      <span className="bg-red-500/20 text-red-300 px-2 py-1 rounded-full text-xs">Cache</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-white/80">Celery</span>
                      <span className="bg-yellow-500/20 text-yellow-300 px-2 py-1 rounded-full text-xs">Tasks</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-white/80">REST API</span>
                      <span className="bg-purple-500/20 text-purple-300 px-2 py-1 rounded-full text-xs">DRF</span>
                    </div>
                  </CardContent>
                </Card>

                {/* DevOps & Infrastructure */}
                <Card className="glass-card border-white/20">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center">
                      <Cloud className="h-6 w-6 mr-2 text-purple-400" />
                      {language === 'ar' ? 'البنية التحتية' : 'Infrastructure'}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-white/80">AWS</span>
                      <span className="bg-orange-500/20 text-orange-300 px-2 py-1 rounded-full text-xs">Cloud</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-white/80">Docker</span>
                      <span className="bg-blue-500/20 text-blue-300 px-2 py-1 rounded-full text-xs">Container</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-white/80">Kubernetes</span>
                      <span className="bg-green-500/20 text-green-300 px-2 py-1 rounded-full text-xs">Orchestration</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-white/80">GitHub Actions</span>
                      <span className="bg-purple-500/20 text-purple-300 px-2 py-1 rounded-full text-xs">CI/CD</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-white/80">Nginx</span>
                      <span className="bg-red-500/20 text-red-300 px-2 py-1 rounded-full text-xs">Proxy</span>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          )}

          {/* Features Tab */}
          {activeTab === 'features' && (
            <div className="animate-fade-in-up">
              <div className="text-center mb-16">
                <h2 className="text-4xl font-bold text-white mb-6">{t.featuresTitle}</h2>
                <p className="text-xl text-white/70 max-w-3xl mx-auto">{t.featuresSubtitle}</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {/* HR Management */}
                <Card className="glass-card border-white/20 hover:border-white/40 transition-all duration-300">
                  <CardHeader>
                    <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mb-4">
                      <Users className="h-6 w-6 text-white" />
                    </div>
                    <CardTitle className="text-white">
                      {language === 'ar' ? 'إدارة الموارد البشرية' : 'HR Management'}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2 text-white/70">
                      <li className="flex items-center">
                        <CheckCircle className="h-4 w-4 text-green-400 mr-2" />
                        {language === 'ar' ? 'إدارة الموظفين' : 'Employee Management'}
                      </li>
                      <li className="flex items-center">
                        <CheckCircle className="h-4 w-4 text-green-400 mr-2" />
                        {language === 'ar' ? 'نظام الحضور والانصراف' : 'Attendance System'}
                      </li>
                      <li className="flex items-center">
                        <CheckCircle className="h-4 w-4 text-green-400 mr-2" />
                        {language === 'ar' ? 'إدارة الإجازات' : 'Leave Management'}
                      </li>
                      <li className="flex items-center">
                        <CheckCircle className="h-4 w-4 text-green-400 mr-2" />
                        {language === 'ar' ? 'تقييم الأداء' : 'Performance Reviews'}
                      </li>
                    </ul>
                  </CardContent>
                </Card>

                {/* Financial Management */}
                <Card className="glass-card border-white/20 hover:border-white/40 transition-all duration-300">
                  <CardHeader>
                    <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-xl flex items-center justify-center mb-4">
                      <BarChart3 className="h-6 w-6 text-white" />
                    </div>
                    <CardTitle className="text-white">
                      {language === 'ar' ? 'الإدارة المالية' : 'Financial Management'}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2 text-white/70">
                      <li className="flex items-center">
                        <CheckCircle className="h-4 w-4 text-green-400 mr-2" />
                        {language === 'ar' ? 'إدارة الميزانيات' : 'Budget Management'}
                      </li>
                      <li className="flex items-center">
                        <CheckCircle className="h-4 w-4 text-green-400 mr-2" />
                        {language === 'ar' ? 'تتبع المصروفات' : 'Expense Tracking'}
                      </li>
                      <li className="flex items-center">
                        <CheckCircle className="h-4 w-4 text-green-400 mr-2" />
                        {language === 'ar' ? 'التقارير المالية' : 'Financial Reports'}
                      </li>
                      <li className="flex items-center">
                        <CheckCircle className="h-4 w-4 text-green-400 mr-2" />
                        {language === 'ar' ? 'إدارة الأصول' : 'Asset Management'}
                      </li>
                    </ul>
                  </CardContent>
                </Card>

                {/* Project Management */}
                <Card className="glass-card border-white/20 hover:border-white/40 transition-all duration-300">
                  <CardHeader>
                    <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mb-4">
                      <Building className="h-6 w-6 text-white" />
                    </div>
                    <CardTitle className="text-white">
                      {language === 'ar' ? 'إدارة المشاريع' : 'Project Management'}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2 text-white/70">
                      <li className="flex items-center">
                        <CheckCircle className="h-4 w-4 text-green-400 mr-2" />
                        {language === 'ar' ? 'تخطيط المشاريع' : 'Project Planning'}
                      </li>
                      <li className="flex items-center">
                        <CheckCircle className="h-4 w-4 text-green-400 mr-2" />
                        {language === 'ar' ? 'إدارة المهام' : 'Task Management'}
                      </li>
                      <li className="flex items-center">
                        <CheckCircle className="h-4 w-4 text-green-400 mr-2" />
                        {language === 'ar' ? 'تتبع التقدم' : 'Progress Tracking'}
                      </li>
                      <li className="flex items-center">
                        <CheckCircle className="h-4 w-4 text-green-400 mr-2" />
                        {language === 'ar' ? 'تعاون الفريق' : 'Team Collaboration'}
                      </li>
                    </ul>
                  </CardContent>
                </Card>

                {/* Security & Compliance */}
                <Card className="glass-card border-white/20 hover:border-white/40 transition-all duration-300">
                  <CardHeader>
                    <div className="w-12 h-12 bg-gradient-to-r from-red-500 to-red-600 rounded-xl flex items-center justify-center mb-4">
                      <Shield className="h-6 w-6 text-white" />
                    </div>
                    <CardTitle className="text-white">
                      {language === 'ar' ? 'الأمان والامتثال' : 'Security & Compliance'}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2 text-white/70">
                      <li className="flex items-center">
                        <CheckCircle className="h-4 w-4 text-green-400 mr-2" />
                        {language === 'ar' ? 'تشفير البيانات' : 'Data Encryption'}
                      </li>
                      <li className="flex items-center">
                        <CheckCircle className="h-4 w-4 text-green-400 mr-2" />
                        {language === 'ar' ? 'التحكم في الوصول' : 'Access Control'}
                      </li>
                      <li className="flex items-center">
                        <CheckCircle className="h-4 w-4 text-green-400 mr-2" />
                        {language === 'ar' ? 'سجل الأنشطة' : 'Activity Logs'}
                      </li>
                      <li className="flex items-center">
                        <CheckCircle className="h-4 w-4 text-green-400 mr-2" />
                        {language === 'ar' ? 'النسخ الاحتياطية' : 'Backup Systems'}
                      </li>
                    </ul>
                  </CardContent>
                </Card>

                {/* Analytics & Reporting */}
                <Card className="glass-card border-white/20 hover:border-white/40 transition-all duration-300">
                  <CardHeader>
                    <div className="w-12 h-12 bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-xl flex items-center justify-center mb-4">
                      <BarChart3 className="h-6 w-6 text-white" />
                    </div>
                    <CardTitle className="text-white">
                      {language === 'ar' ? 'التحليلات والتقارير' : 'Analytics & Reporting'}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2 text-white/70">
                      <li className="flex items-center">
                        <CheckCircle className="h-4 w-4 text-green-400 mr-2" />
                        {language === 'ar' ? 'لوحات المعلومات' : 'Dashboards'}
                      </li>
                      <li className="flex items-center">
                        <CheckCircle className="h-4 w-4 text-green-400 mr-2" />
                        {language === 'ar' ? 'التقارير المخصصة' : 'Custom Reports'}
                      </li>
                      <li className="flex items-center">
                        <CheckCircle className="h-4 w-4 text-green-400 mr-2" />
                        {language === 'ar' ? 'تحليل البيانات' : 'Data Analysis'}
                      </li>
                      <li className="flex items-center">
                        <CheckCircle className="h-4 w-4 text-green-400 mr-2" />
                        {language === 'ar' ? 'مؤشرات الأداء' : 'KPI Tracking'}
                      </li>
                    </ul>
                  </CardContent>
                </Card>

                {/* Mobile & Integration */}
                <Card className="glass-card border-white/20 hover:border-white/40 transition-all duration-300">
                  <CardHeader>
                    <div className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-xl flex items-center justify-center mb-4">
                      <Smartphone className="h-6 w-6 text-white" />
                    </div>
                    <CardTitle className="text-white">
                      {language === 'ar' ? 'الجوال والتكامل' : 'Mobile & Integration'}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2 text-white/70">
                      <li className="flex items-center">
                        <CheckCircle className="h-4 w-4 text-green-400 mr-2" />
                        {language === 'ar' ? 'تطبيق الجوال' : 'Mobile App'}
                      </li>
                      <li className="flex items-center">
                        <CheckCircle className="h-4 w-4 text-green-400 mr-2" />
                        {language === 'ar' ? 'واجهات برمجية' : 'API Integration'}
                      </li>
                      <li className="flex items-center">
                        <CheckCircle className="h-4 w-4 text-green-400 mr-2" />
                        {language === 'ar' ? 'تزامن البيانات' : 'Data Sync'}
                      </li>
                      <li className="flex items-center">
                        <CheckCircle className="h-4 w-4 text-green-400 mr-2" />
                        {language === 'ar' ? 'إشعارات فورية' : 'Real-time Notifications'}
                      </li>
                    </ul>
                  </CardContent>
                </Card>
              </div>
            </div>
          )}
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative z-10 py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="glass-card p-12 rounded-3xl border border-white/20">
            <h2 className="text-4xl font-bold text-white mb-6">{t.ctaTitle}</h2>
            <p className="text-xl text-white/70 mb-8">{t.ctaSubtitle}</p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="glass-button bg-gradient-to-r from-blue-500 to-purple-500 text-lg px-8 py-4">
                <Play className="h-5 w-5 mr-2" />
                {t.startTrial}
              </Button>
              <Button size="lg" variant="outline" className="glass-button text-lg px-8 py-4">
                {t.contactUs}
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
