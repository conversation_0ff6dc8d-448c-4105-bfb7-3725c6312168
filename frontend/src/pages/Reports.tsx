import { useState } from 'react'
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  BarChart3,
  Download,
  Calendar,
  Filter,
  TrendingUp,
  Users,
  Building,
  DollarSign,
  FileText,
  PieChart,
  LineChart,
  Activity
} from 'lucide-react'

interface ReportsProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    reports: 'التقارير',
    generateReport: 'إنشاء تقرير',
    downloadReport: 'تحميل التقرير',
    filterReports: 'تصفية التقارير',
    reportTypes: 'أنواع التقارير',
    employeeReports: 'تقارير الموظفين',
    departmentReports: 'تقارير الأقسام',
    financialReports: 'التقارير المالية',
    performanceReports: 'تقارير الأداء',
    recentReports: 'التقارير الحديثة',
    quickReports: 'التقارير السريعة',
    customReports: 'التقارير المخصصة',
    reportName: 'اسم التقرير',
    reportType: 'نوع التقرير',
    dateRange: 'النطاق الزمني',
    status: 'الحالة',
    actions: 'الإجراءات',
    completed: 'مكتمل',
    pending: 'قيد الانتظار',
    failed: 'فشل',
    view: 'عرض',
    download: 'تحميل',
    delete: 'حذف',
    noReports: 'لا يوجد تقارير',
    loading: 'جاري التحميل...',
    manageReports: 'إدارة وإنشاء التقارير التحليلية',
    totalReports: 'إجمالي التقارير',
    completedReports: 'التقارير المكتملة',
    pendingReports: 'التقارير المعلقة',
    employeeAttendance: 'حضور الموظفين',
    salaryReport: 'تقرير الرواتب',
    departmentPerformance: 'أداء الأقسام',
    monthlyOverview: 'النظرة الشهرية',
    yearlyAnalysis: 'التحليل السنوي',
    hrMetrics: 'مقاييس الموارد البشرية'
  },
  en: {
    reports: 'Reports',
    generateReport: 'Generate Report',
    downloadReport: 'Download Report',
    filterReports: 'Filter Reports',
    reportTypes: 'Report Types',
    employeeReports: 'Employee Reports',
    departmentReports: 'Department Reports',
    financialReports: 'Financial Reports',
    performanceReports: 'Performance Reports',
    recentReports: 'Recent Reports',
    quickReports: 'Quick Reports',
    customReports: 'Custom Reports',
    reportName: 'Report Name',
    reportType: 'Report Type',
    dateRange: 'Date Range',
    status: 'Status',
    actions: 'Actions',
    completed: 'Completed',
    pending: 'Pending',
    failed: 'Failed',
    view: 'View',
    download: 'Download',
    delete: 'Delete',
    noReports: 'No reports found',
    loading: 'Loading...',
    manageReports: 'Manage and generate analytical reports',
    totalReports: 'Total Reports',
    completedReports: 'Completed Reports',
    pendingReports: 'Pending Reports',
    employeeAttendance: 'Employee Attendance',
    salaryReport: 'Salary Report',
    departmentPerformance: 'Department Performance',
    monthlyOverview: 'Monthly Overview',
    yearlyAnalysis: 'Yearly Analysis',
    hrMetrics: 'HR Metrics'
  }
}

// Sample report data
const sampleReports = [
  {
    id: 1,
    name: 'تقرير حضور الموظفين - نوفمبر 2024',
    nameEn: 'Employee Attendance Report - November 2024',
    type: 'employee',
    dateRange: '2024-11-01 to 2024-11-30',
    status: 'completed',
    createdDate: '2024-11-30',
    size: '2.3 MB'
  },
  {
    id: 2,
    name: 'تقرير أداء الأقسام - الربع الثالث',
    nameEn: 'Department Performance Report - Q3',
    type: 'department',
    dateRange: '2024-07-01 to 2024-09-30',
    status: 'completed',
    createdDate: '2024-10-15',
    size: '1.8 MB'
  },
  {
    id: 3,
    name: 'التقرير المالي الشهري - أكتوبر',
    nameEn: 'Monthly Financial Report - October',
    type: 'financial',
    dateRange: '2024-10-01 to 2024-10-31',
    status: 'pending',
    createdDate: '2024-11-01',
    size: '-'
  },
  {
    id: 4,
    name: 'تقرير الرواتب - نوفمبر 2024',
    nameEn: 'Salary Report - November 2024',
    type: 'financial',
    dateRange: '2024-11-01 to 2024-11-30',
    status: 'completed',
    createdDate: '2024-11-28',
    size: '956 KB'
  }
]

const quickReportTypes = [
  {
    title: 'employeeAttendance',
    icon: Users,
    color: 'bg-blue-500',
    description: 'تقرير حضور وغياب الموظفين'
  },
  {
    title: 'salaryReport',
    icon: DollarSign,
    color: 'bg-green-500',
    description: 'تقرير الرواتب والمكافآت'
  },
  {
    title: 'departmentPerformance',
    icon: Building,
    color: 'bg-purple-500',
    description: 'تقرير أداء الأقسام'
  },
  {
    title: 'monthlyOverview',
    icon: BarChart3,
    color: 'bg-orange-500',
    description: 'النظرة العامة الشهرية'
  },
  {
    title: 'yearlyAnalysis',
    icon: LineChart,
    color: 'bg-red-500',
    description: 'التحليل السنوي'
  },
  {
    title: 'hrMetrics',
    icon: Activity,
    color: 'bg-indigo-500',
    description: 'مقاييس الموارد البشرية'
  }
]

export default function Reports({ language }: ReportsProps) {
  const [reports] = useState(sampleReports)
  const t = translations[language]

  const completedReports = reports.filter(report => report.status === 'completed').length
  const pendingReports = reports.filter(report => report.status === 'pending').length

  const stats = [
    {
      title: t.totalReports,
      value: reports.length.toString(),
      icon: FileText,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      title: t.completedReports,
      value: completedReports.toString(),
      icon: BarChart3,
      color: 'text-green-600',
      bgColor: 'bg-green-100'
    },
    {
      title: t.pendingReports,
      value: pendingReports.toString(),
      icon: Activity,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100'
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      case 'failed':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return t.completed
      case 'pending':
        return t.pending
      case 'failed':
        return t.failed
      default:
        return status
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-4xl font-bold text-white drop-shadow-lg">
            {t.reports}
          </h1>
          <p className="text-white/80 mt-2 text-lg">
            {t.manageReports}
          </p>
        </div>
        <Button className="gradient-bg-blue hover:scale-105 transform transition-all duration-300 glow-hover text-white font-semibold px-6 py-3">
          <BarChart3 className="h-5 w-5 mr-2" />
          {t.generateReport}
        </Button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {stats.map((stat, index) => (
          <Card key={index} className="modern-card border-0 hover:scale-105 transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-white/80">
                    {stat.title}
                  </p>
                  <p className="text-3xl font-bold text-white mt-2">
                    {stat.value}
                  </p>
                </div>
                <div className="p-4 rounded-2xl gradient-bg-alt glow floating">
                  <stat.icon className="h-7 w-7 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Quick Reports */}
      <Card className="modern-card border-0">
        <CardHeader>
          <CardTitle className="text-white text-xl">{t.quickReports}</CardTitle>
          <CardDescription className="text-white/70">
            {language === 'ar'
              ? 'إنشاء التقارير الشائعة بنقرة واحدة'
              : 'Generate common reports with one click'
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {quickReportTypes.map((reportType, index) => (
              <div
                key={index}
                className="flex items-center gap-4 p-4 glass rounded-xl hover:glass-card cursor-pointer transition-all duration-300 hover:scale-105 glow-hover"
              >
                <div className="p-3 rounded-xl gradient-bg-purple glow">
                  <reportType.icon className="h-6 w-6 text-white" />
                </div>
                <div className="flex-1">
                  <h3 className="font-medium text-white">
                    {t[reportType.title as keyof typeof t]}
                  </h3>
                  <p className="text-sm text-white/60">
                    {language === 'ar' ? reportType.description : `Generate ${t[reportType.title as keyof typeof t]} report`}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recent Reports */}
      <Card className="modern-card border-0">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-white text-xl">{t.recentReports}</CardTitle>
              <CardDescription className="text-white/70">
                {language === 'ar'
                  ? 'آخر التقارير المُنشأة'
                  : 'Recently generated reports'
                }
              </CardDescription>
            </div>
            <Button className="glass text-white border-white/30 hover:bg-white/20">
              <Filter className="h-4 w-4 mr-2" />
              {t.filterReports}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-white/20">
                  <th className="text-left py-4 px-4 font-medium text-white/90">
                    {t.reportName}
                  </th>
                  <th className="text-left py-4 px-4 font-medium text-white/90">
                    {t.dateRange}
                  </th>
                  <th className="text-left py-4 px-4 font-medium text-white/90">
                    {t.status}
                  </th>
                  <th className="text-left py-4 px-4 font-medium text-white/90">
                    {language === 'ar' ? 'الحجم' : 'Size'}
                  </th>
                  <th className="text-left py-4 px-4 font-medium text-white/90">
                    {t.actions}
                  </th>
                </tr>
              </thead>
              <tbody>
                {reports.map((report) => (
                  <tr key={report.id} className="border-b border-white/10 hover:glass transition-all duration-300">
                    <td className="py-4 px-4">
                      <div className="flex items-center gap-3">
                        <div className="w-12 h-12 gradient-bg-blue rounded-xl flex items-center justify-center glow floating">
                          <FileText className="h-6 w-6 text-white" />
                        </div>
                        <div>
                          <p className="font-medium text-white">
                            {language === 'ar' ? report.name : report.nameEn}
                          </p>
                          <p className="text-sm text-white/60">
                            {new Date(report.createdDate).toLocaleDateString(
                              language === 'ar' ? 'ar-SA' : 'en-US'
                            )}
                          </p>
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-4 text-white/90">
                      {report.dateRange}
                    </td>
                    <td className="py-4 px-4">
                      <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium glass ${getStatusColor(report.status)}`}>
                        {getStatusText(report.status)}
                      </span>
                    </td>
                    <td className="py-4 px-4 text-white/90">
                      {report.size}
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex items-center gap-2">
                        <Button variant="ghost" size="sm" className="glass text-white hover:bg-white/20 p-2">
                          <FileText className="h-4 w-4" />
                        </Button>
                        {report.status === 'completed' && (
                          <Button variant="ghost" size="sm" className="glass text-green-300 hover:bg-green-500/20 p-2">
                            <Download className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
