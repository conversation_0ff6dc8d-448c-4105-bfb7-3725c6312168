import { useState, useEffect } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Users,
  Search,
  Filter,
  Plus,
  Eye,
  Edit,
  Trash2,
  MoreHorizontal,
  UserPlus,
  Shield,
  Lock,
  Unlock,
  Mail,
  Phone,
  Calendar,
  Building,
  Crown,
  UserCheck,
  UserX,
  Settings,
  Download,
  Upload,
  Clock
} from 'lucide-react'

interface UserManagementProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    title: 'إدارة المستخدمين',
    subtitle: 'إدارة حسابات المستخدمين والصلاحيات',
    userOverview: 'نظرة عامة على المستخدمين',
    totalUsers: 'إجمالي المستخدمين',
    activeUsers: 'المستخدمون النشطون',
    inactiveUsers: 'المستخدمون غير النشطين',
    newUsers: 'مستخدمون جدد',
    searchUsers: 'البحث في المستخدمين...',
    addUser: 'إضافة مستخدم',
    exportUsers: 'تصدير المستخدمين',
    importUsers: 'استيراد المستخدمين',
    userList: 'قائمة المستخدمين',
    fullName: 'الاسم الكامل',
    email: 'البريد الإلكتروني',
    phone: 'رقم الهاتف',
    role: 'الدور',
    department: 'القسم',
    status: 'الحالة',
    lastLogin: 'آخر تسجيل دخول',
    joinDate: 'تاريخ الانضمام',
    actions: 'الإجراءات',
    viewProfile: 'عرض الملف الشخصي',
    editUser: 'تعديل المستخدم',
    deleteUser: 'حذف المستخدم',
    resetPassword: 'إعادة تعيين كلمة المرور',
    activateUser: 'تفعيل المستخدم',
    deactivateUser: 'إلغاء تفعيل المستخدم',
    active: 'نشط',
    inactive: 'غير نشط',
    pending: 'قيد المراجعة',
    suspended: 'موقوف',
    superAdmin: 'مدير عام',
    hrManager: 'مدير الموارد البشرية',
    financeManager: 'مدير المالية',
    departmentManager: 'مدير القسم',
    employee: 'موظف',
    it: 'تقنية المعلومات',
    hr: 'الموارد البشرية',
    finance: 'المالية',
    marketing: 'التسويق',
    operations: 'العمليات',
    sales: 'المبيعات',
    permissions: 'الصلاحيات',
    managePermissions: 'إدارة الصلاحيات',
    userDetails: 'تفاصيل المستخدم',
    personalInfo: 'المعلومات الشخصية',
    accountInfo: 'معلومات الحساب',
    securitySettings: 'إعدادات الأمان',
    rolePermissions: 'صلاحيات الدور',
    lastActivity: 'آخر نشاط',
    loginHistory: 'تاريخ تسجيل الدخول',
    deviceInfo: 'معلومات الجهاز',
    ipAddress: 'عنوان IP',
    browser: 'المتصفح',
    location: 'الموقع'
  },
  en: {
    title: 'User Management',
    subtitle: 'Manage user accounts and permissions',
    userOverview: 'User Overview',
    totalUsers: 'Total Users',
    activeUsers: 'Active Users',
    inactiveUsers: 'Inactive Users',
    newUsers: 'New Users',
    searchUsers: 'Search users...',
    addUser: 'Add User',
    exportUsers: 'Export Users',
    importUsers: 'Import Users',
    userList: 'User List',
    fullName: 'Full Name',
    email: 'Email',
    phone: 'Phone',
    role: 'Role',
    department: 'Department',
    status: 'Status',
    lastLogin: 'Last Login',
    joinDate: 'Join Date',
    actions: 'Actions',
    viewProfile: 'View Profile',
    editUser: 'Edit User',
    deleteUser: 'Delete User',
    resetPassword: 'Reset Password',
    activateUser: 'Activate User',
    deactivateUser: 'Deactivate User',
    active: 'Active',
    inactive: 'Inactive',
    pending: 'Pending',
    suspended: 'Suspended',
    superAdmin: 'Super Admin',
    hrManager: 'HR Manager',
    financeManager: 'Finance Manager',
    departmentManager: 'Department Manager',
    employee: 'Employee',
    it: 'IT',
    hr: 'HR',
    finance: 'Finance',
    marketing: 'Marketing',
    operations: 'Operations',
    sales: 'Sales',
    permissions: 'Permissions',
    managePermissions: 'Manage Permissions',
    userDetails: 'User Details',
    personalInfo: 'Personal Information',
    accountInfo: 'Account Information',
    securitySettings: 'Security Settings',
    rolePermissions: 'Role Permissions',
    lastActivity: 'Last Activity',
    loginHistory: 'Login History',
    deviceInfo: 'Device Info',
    ipAddress: 'IP Address',
    browser: 'Browser',
    location: 'Location'
  }
}

export default function UserManagement({ language }: UserManagementProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedRole, setSelectedRole] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('')
  const [selectedDepartment, setSelectedDepartment] = useState('')
  const t = translations[language]
  const isRTL = language === 'ar'

  // User metrics
  const [userMetrics] = useState({
    totalUsers: 156,
    activeUsers: 142,
    inactiveUsers: 14,
    newUsers: 8
  })

  // Sample users data
  const [users] = useState([
    {
      id: 1,
      fullName: 'أحمد محمد علي',
      fullNameEn: 'Ahmed Mohammed Ali',
      email: '<EMAIL>',
      phone: '+966501234567',
      role: 'superAdmin',
      roleAr: 'مدير عام',
      department: 'it',
      departmentAr: 'تقنية المعلومات',
      status: 'active',
      lastLogin: '2024-01-28 14:30',
      joinDate: '2023-01-15',
      avatar: null,
      permissions: ['all'],
      lastActivity: '2024-01-28 15:45',
      ipAddress: '*************',
      browser: 'Chrome 120.0',
      location: 'الرياض، السعودية'
    },
    {
      id: 2,
      fullName: 'سارة أحمد محمد',
      fullNameEn: 'Sara Ahmed Mohammed',
      email: '<EMAIL>',
      phone: '+966501234568',
      role: 'hrManager',
      roleAr: 'مدير الموارد البشرية',
      department: 'hr',
      departmentAr: 'الموارد البشرية',
      status: 'active',
      lastLogin: '2024-01-28 09:15',
      joinDate: '2023-02-01',
      avatar: null,
      permissions: ['hr_management', 'employee_management'],
      lastActivity: '2024-01-28 16:20',
      ipAddress: '*************',
      browser: 'Firefox 121.0',
      location: 'جدة، السعودية'
    },
    {
      id: 3,
      fullName: 'محمد عبدالله الأحمد',
      fullNameEn: 'Mohammed Abdullah Al-Ahmad',
      email: '<EMAIL>',
      phone: '+966501234569',
      role: 'financeManager',
      roleAr: 'مدير المالية',
      department: 'finance',
      departmentAr: 'المالية',
      status: 'active',
      lastLogin: '2024-01-27 16:45',
      joinDate: '2023-03-10',
      avatar: null,
      permissions: ['finance_management', 'budget_management'],
      lastActivity: '2024-01-27 17:30',
      ipAddress: '*************',
      browser: 'Safari 17.0',
      location: 'الدمام، السعودية'
    },
    {
      id: 4,
      fullName: 'فاطمة علي حسن',
      fullNameEn: 'Fatima Ali Hassan',
      email: '<EMAIL>',
      phone: '+966501234570',
      role: 'employee',
      roleAr: 'موظف',
      department: 'marketing',
      departmentAr: 'التسويق',
      status: 'inactive',
      lastLogin: '2024-01-20 11:30',
      joinDate: '2023-06-15',
      avatar: null,
      permissions: ['basic_access'],
      lastActivity: '2024-01-20 12:15',
      ipAddress: '*************',
      browser: 'Edge 120.0',
      location: 'مكة، السعودية'
    }
  ])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-500/20 text-green-400 border-green-500/30'
      case 'inactive':
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30'
      case 'pending':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
      case 'suspended':
        return 'bg-red-500/20 text-red-400 border-red-500/30'
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30'
    }
  }

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'superAdmin':
        return <Crown className="h-4 w-4" />
      case 'hrManager':
      case 'financeManager':
      case 'departmentManager':
        return <Shield className="h-4 w-4" />
      case 'employee':
        return <Users className="h-4 w-4" />
      default:
        return <Users className="h-4 w-4" />
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <UserCheck className="h-4 w-4" />
      case 'inactive':
        return <UserX className="h-4 w-4" />
      case 'pending':
        return <Clock className="h-4 w-4" />
      case 'suspended':
        return <Lock className="h-4 w-4" />
      default:
        return <Users className="h-4 w-4" />
    }
  }

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.fullNameEn.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesRole = !selectedRole || user.role === selectedRole
    const matchesStatus = !selectedStatus || user.status === selectedStatus
    const matchesDepartment = !selectedDepartment || user.department === selectedDepartment

    return matchesSearch && matchesRole && matchesStatus && matchesDepartment
  })

  const userMetricsCards = [
    {
      title: t.totalUsers,
      value: userMetrics.totalUsers.toString(),
      change: '+12',
      trend: 'up',
      icon: Users,
      color: 'from-blue-500 to-blue-600'
    },
    {
      title: t.activeUsers,
      value: userMetrics.activeUsers.toString(),
      change: '+5',
      trend: 'up',
      icon: UserCheck,
      color: 'from-green-500 to-green-600'
    },
    {
      title: t.inactiveUsers,
      value: userMetrics.inactiveUsers.toString(),
      change: '-2',
      trend: 'down',
      icon: UserX,
      color: 'from-red-500 to-red-600'
    },
    {
      title: t.newUsers,
      value: userMetrics.newUsers.toString(),
      change: '+3',
      trend: 'up',
      icon: UserPlus,
      color: 'from-purple-500 to-purple-600'
    }
  ]

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">{t.title}</h1>
          <p className="text-white/70">{t.subtitle}</p>
        </div>

        <div className="flex items-center gap-3">
          <Button variant="outline" className="glass-button">
            <Upload className="h-4 w-4 mr-2" />
            {t.importUsers}
          </Button>
          <Button variant="outline" className="glass-button">
            <Download className="h-4 w-4 mr-2" />
            {t.exportUsers}
          </Button>
          <Button className="glass-button">
            <Plus className="h-4 w-4 mr-2" />
            {t.addUser}
          </Button>
        </div>
      </div>

      {/* User Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {userMetricsCards.map((card, index) => (
          <Card key={index} className="glass-card border-white/20 hover:border-white/40 transition-all">
            <CardContent className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <p className="text-white/70 text-sm mb-1">{card.title}</p>
                  <p className="text-2xl font-bold text-white">{card.value}</p>
                </div>
                <div className={`p-3 rounded-xl bg-gradient-to-r ${card.color} shadow-lg`}>
                  <card.icon className="h-6 w-6 text-white" />
                </div>
              </div>

              <div className="flex items-center gap-2">
                <span className={`text-sm font-medium ${
                  card.trend === 'up' ? 'text-green-400' :
                  card.trend === 'down' ? 'text-red-400' : 'text-blue-400'
                }`}>
                  {card.change}
                </span>
                <span className="text-xs text-white/60">vs last month</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Search and Filters */}
      <Card className="glass-card border-white/20">
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/60" />
              <Input
                placeholder={t.searchUsers}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 glass-input"
              />
            </div>

            <select
              value={selectedRole}
              onChange={(e) => setSelectedRole(e.target.value)}
              className="glass-input"
            >
              <option value="">{t.role}</option>
              <option value="superAdmin">{t.superAdmin}</option>
              <option value="hrManager">{t.hrManager}</option>
              <option value="financeManager">{t.financeManager}</option>
              <option value="departmentManager">{t.departmentManager}</option>
              <option value="employee">{t.employee}</option>
            </select>

            <select
              value={selectedDepartment}
              onChange={(e) => setSelectedDepartment(e.target.value)}
              className="glass-input"
            >
              <option value="">{t.department}</option>
              <option value="it">{t.it}</option>
              <option value="hr">{t.hr}</option>
              <option value="finance">{t.finance}</option>
              <option value="marketing">{t.marketing}</option>
              <option value="operations">{t.operations}</option>
              <option value="sales">{t.sales}</option>
            </select>

            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="glass-input"
            >
              <option value="">{t.status}</option>
              <option value="active">{t.active}</option>
              <option value="inactive">{t.inactive}</option>
              <option value="pending">{t.pending}</option>
              <option value="suspended">{t.suspended}</option>
            </select>

            <Button variant="outline" className="glass-button">
              <Filter className="h-4 w-4 mr-2" />
              Filter
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Users List */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white text-xl">{t.userList}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-white/20">
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.fullName}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.email}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.role}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.department}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.status}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.lastLogin}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.actions}</th>
                </tr>
              </thead>
              <tbody>
                {filteredUsers.map((user) => (
                  <tr key={user.id} className="border-b border-white/10 hover:bg-white/5 transition-colors">
                    <td className="py-4 px-4">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-white font-bold">
                          {(language === 'ar' ? user.fullName : user.fullNameEn).charAt(0)}
                        </div>
                        <div>
                          <p className="text-white font-medium">
                            {language === 'ar' ? user.fullName : user.fullNameEn}
                          </p>
                          <p className="text-white/60 text-sm">{user.phone}</p>
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-4 text-white">
                      {user.email}
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex items-center gap-2">
                        {getRoleIcon(user.role)}
                        <span className="text-white">
                          {language === 'ar' ? user.roleAr : t[user.role as keyof typeof t]}
                        </span>
                      </div>
                    </td>
                    <td className="py-4 px-4 text-white">
                      {language === 'ar' ? user.departmentAr : t[user.department as keyof typeof t]}
                    </td>
                    <td className="py-4 px-4">
                      <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(user.status)}`}>
                        {getStatusIcon(user.status)}
                        {t[user.status as keyof typeof t]}
                      </span>
                    </td>
                    <td className="py-4 px-4 text-white/70 text-sm">
                      {user.lastLogin}
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex items-center gap-2">
                        <Button size="sm" variant="outline" className="glass-button p-2" title={t.viewProfile}>
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="outline" className="glass-button p-2" title={t.editUser}>
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="outline" className="glass-button p-2" title={t.managePermissions}>
                          <Shield className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="outline" className="glass-button p-2" title={t.resetPassword}>
                          <Lock className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="outline" className="glass-button p-2">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
