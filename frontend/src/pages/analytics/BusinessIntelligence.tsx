import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Badge } from '../../components/ui/badge'
import { 
  BarChart3, 
  Plus, 
  Eye, 
  Download,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Users,
  ShoppingCart,
  Target,
  Calendar,
  Filter,
  RefreshCw,
  PieChart,
  LineChart,
  Activity
} from 'lucide-react'

interface BusinessIntelligenceProps {
  language: 'ar' | 'en'
}

interface KPI {
  id: string
  name: string
  nameAr: string
  value: number
  previousValue: number
  target: number
  unit: string
  unitAr: string
  category: string
  categoryAr: string
  trend: 'up' | 'down' | 'stable'
  changePercentage: number
  lastUpdated: string
}

interface Report {
  id: string
  title: string
  titleAr: string
  description: string
  descriptionAr: string
  type: 'financial' | 'sales' | 'hr' | 'operations' | 'customer'
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly'
  lastGenerated: string
  nextScheduled: string
  status: 'active' | 'inactive' | 'pending'
  format: 'pdf' | 'excel' | 'dashboard'
  recipients: string[]
  recipientsAr: string[]
}

const mockKPIs: KPI[] = [
  {
    id: '1',
    name: 'Total Revenue',
    nameAr: 'إجمالي الإيرادات',
    value: 2450000,
    previousValue: 2200000,
    target: 2500000,
    unit: 'SAR',
    unitAr: 'ر.س',
    category: 'Financial',
    categoryAr: 'مالية',
    trend: 'up',
    changePercentage: 11.4,
    lastUpdated: '2024-01-22'
  },
  {
    id: '2',
    name: 'Customer Satisfaction',
    nameAr: 'رضا العملاء',
    value: 4.2,
    previousValue: 4.0,
    target: 4.5,
    unit: '/5',
    unitAr: '/5',
    category: 'Customer',
    categoryAr: 'العملاء',
    trend: 'up',
    changePercentage: 5.0,
    lastUpdated: '2024-01-22'
  },
  {
    id: '3',
    name: 'Employee Productivity',
    nameAr: 'إنتاجية الموظفين',
    value: 87,
    previousValue: 92,
    target: 90,
    unit: '%',
    unitAr: '%',
    category: 'HR',
    categoryAr: 'الموارد البشرية',
    trend: 'down',
    changePercentage: -5.4,
    lastUpdated: '2024-01-22'
  },
  {
    id: '4',
    name: 'Sales Conversion Rate',
    nameAr: 'معدل تحويل المبيعات',
    value: 23.5,
    previousValue: 21.8,
    target: 25.0,
    unit: '%',
    unitAr: '%',
    category: 'Sales',
    categoryAr: 'المبيعات',
    trend: 'up',
    changePercentage: 7.8,
    lastUpdated: '2024-01-22'
  }
]

const mockReports: Report[] = [
  {
    id: '1',
    title: 'Monthly Financial Report',
    titleAr: 'التقرير المالي الشهري',
    description: 'Comprehensive monthly financial performance report',
    descriptionAr: 'تقرير شامل للأداء المالي الشهري',
    type: 'financial',
    frequency: 'monthly',
    lastGenerated: '2024-01-01',
    nextScheduled: '2024-02-01',
    status: 'active',
    format: 'pdf',
    recipients: ['CFO', 'Finance Manager', 'CEO'],
    recipientsAr: ['المدير المالي', 'مدير المالية', 'الرئيس التنفيذي']
  },
  {
    id: '2',
    title: 'Sales Performance Dashboard',
    titleAr: 'لوحة أداء المبيعات',
    description: 'Real-time sales metrics and performance indicators',
    descriptionAr: 'مقاييس المبيعات في الوقت الفعلي ومؤشرات الأداء',
    type: 'sales',
    frequency: 'daily',
    lastGenerated: '2024-01-22',
    nextScheduled: '2024-01-23',
    status: 'active',
    format: 'dashboard',
    recipients: ['Sales Manager', 'Sales Team'],
    recipientsAr: ['مدير المبيعات', 'فريق المبيعات']
  },
  {
    id: '3',
    title: 'HR Analytics Report',
    titleAr: 'تقرير تحليلات الموارد البشرية',
    description: 'Employee performance and engagement analytics',
    descriptionAr: 'تحليلات أداء الموظفين والمشاركة',
    type: 'hr',
    frequency: 'weekly',
    lastGenerated: '2024-01-15',
    nextScheduled: '2024-01-29',
    status: 'active',
    format: 'excel',
    recipients: ['HR Manager', 'Department Managers'],
    recipientsAr: ['مدير الموارد البشرية', 'مديري الأقسام']
  }
]

export default function BusinessIntelligence({ language }: BusinessIntelligenceProps) {
  const [kpis, setKpis] = useState<KPI[]>(mockKPIs)
  const [reports, setReports] = useState<Report[]>(mockReports)
  const [selectedPeriod, setSelectedPeriod] = useState('monthly')
  const [selectedCategory, setSelectedCategory] = useState('all')

  const text = {
    ar: {
      title: 'ذكاء الأعمال',
      description: 'تحليلات متقدمة ومؤشرات الأداء الرئيسية',
      newReport: 'تقرير جديد',
      refresh: 'تحديث',
      export: 'تصدير',
      kpis: 'مؤشرات الأداء الرئيسية',
      reports: 'التقارير',
      dashboards: 'لوحات المعلومات',
      analytics: 'التحليلات',
      target: 'الهدف',
      actual: 'الفعلي',
      change: 'التغيير',
      trend: 'الاتجاه',
      lastUpdated: 'آخر تحديث',
      reportTitle: 'عنوان التقرير',
      type: 'النوع',
      frequency: 'التكرار',
      status: 'الحالة',
      lastGenerated: 'آخر إنشاء',
      nextScheduled: 'المجدول القادم',
      recipients: 'المستلمون',
      actions: 'الإجراءات',
      view: 'عرض',
      download: 'تحميل',
      financial: 'مالية',
      sales: 'مبيعات',
      hr: 'موارد بشرية',
      operations: 'عمليات',
      customer: 'عملاء',
      daily: 'يومي',
      weekly: 'أسبوعي',
      monthly: 'شهري',
      quarterly: 'ربع سنوي',
      yearly: 'سنوي',
      active: 'نشط',
      inactive: 'غير نشط',
      pending: 'معلق',
      pdf: 'PDF',
      excel: 'Excel',
      dashboard: 'لوحة معلومات',
      totalKPIs: 'إجمالي المؤشرات',
      onTarget: 'على الهدف',
      improving: 'في تحسن',
      needsAttention: 'يحتاج انتباه'
    },
    en: {
      title: 'Business Intelligence',
      description: 'Advanced analytics and key performance indicators',
      newReport: 'New Report',
      refresh: 'Refresh',
      export: 'Export',
      kpis: 'Key Performance Indicators',
      reports: 'Reports',
      dashboards: 'Dashboards',
      analytics: 'Analytics',
      target: 'Target',
      actual: 'Actual',
      change: 'Change',
      trend: 'Trend',
      lastUpdated: 'Last Updated',
      reportTitle: 'Report Title',
      type: 'Type',
      frequency: 'Frequency',
      status: 'Status',
      lastGenerated: 'Last Generated',
      nextScheduled: 'Next Scheduled',
      recipients: 'Recipients',
      actions: 'Actions',
      view: 'View',
      download: 'Download',
      financial: 'Financial',
      sales: 'Sales',
      hr: 'HR',
      operations: 'Operations',
      customer: 'Customer',
      daily: 'Daily',
      weekly: 'Weekly',
      monthly: 'Monthly',
      quarterly: 'Quarterly',
      yearly: 'Yearly',
      active: 'Active',
      inactive: 'Inactive',
      pending: 'Pending',
      pdf: 'PDF',
      excel: 'Excel',
      dashboard: 'Dashboard',
      totalKPIs: 'Total KPIs',
      onTarget: 'On Target',
      improving: 'Improving',
      needsAttention: 'Needs Attention'
    }
  }

  const t = text[language]

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <TrendingUp className="w-4 h-4 text-green-400" />
      case 'down': return <TrendingDown className="w-4 h-4 text-red-400" />
      default: return <Activity className="w-4 h-4 text-gray-400" />
    }
  }

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'up': return 'text-green-400'
      case 'down': return 'text-red-400'
      default: return 'text-gray-400'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-500/20 text-green-300 border-green-500/30'
      case 'inactive': return 'bg-gray-500/20 text-gray-300 border-gray-500/30'
      case 'pending': return 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30'
      default: return 'bg-gray-500/20 text-gray-300 border-gray-500/30'
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'financial': return 'bg-green-500/20 text-green-300 border-green-500/30'
      case 'sales': return 'bg-blue-500/20 text-blue-300 border-blue-500/30'
      case 'hr': return 'bg-purple-500/20 text-purple-300 border-purple-500/30'
      case 'operations': return 'bg-orange-500/20 text-orange-300 border-orange-500/30'
      case 'customer': return 'bg-pink-500/20 text-pink-300 border-pink-500/30'
      default: return 'bg-gray-500/20 text-gray-300 border-gray-500/30'
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'Financial': return <DollarSign className="w-5 h-5" />
      case 'Sales': return <ShoppingCart className="w-5 h-5" />
      case 'HR': return <Users className="w-5 h-5" />
      case 'Customer': return <Target className="w-5 h-5" />
      default: return <BarChart3 className="w-5 h-5" />
    }
  }

  const filteredKPIs = kpis.filter(kpi => 
    selectedCategory === 'all' || kpi.category === selectedCategory
  )

  const onTargetKPIs = kpis.filter(kpi => kpi.value >= kpi.target).length
  const improvingKPIs = kpis.filter(kpi => kpi.trend === 'up').length
  const needsAttentionKPIs = kpis.filter(kpi => kpi.value < kpi.target || kpi.trend === 'down').length

  return (
    <div className="min-h-screen gradient-bg p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">{t.title}</h1>
            <p className="text-white/70">{t.description}</p>
          </div>
          <div className="flex gap-3">
            <Button variant="outline" className="glass-button">
              <RefreshCw className="w-4 h-4 mr-2" />
              {t.refresh}
            </Button>
            <Button variant="outline" className="glass-button">
              <Download className="w-4 h-4 mr-2" />
              {t.export}
            </Button>
            <Button className="bg-gradient-to-r from-sky-500 to-blue-600 hover:from-sky-600 hover:to-blue-700">
              <Plus className="w-4 h-4 mr-2" />
              {t.newReport}
            </Button>
          </div>
        </div>

        {/* Overview Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="glass-card border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.totalKPIs}</p>
                  <p className="text-2xl font-bold text-white">{kpis.length}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-sky-500/20 flex items-center justify-center">
                  <BarChart3 className="w-6 h-6 text-sky-400" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-card border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.onTarget}</p>
                  <p className="text-2xl font-bold text-white">{onTargetKPIs}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-green-500/20 flex items-center justify-center">
                  <Target className="w-6 h-6 text-green-400" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-card border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.improving}</p>
                  <p className="text-2xl font-bold text-white">{improvingKPIs}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-blue-500/20 flex items-center justify-center">
                  <TrendingUp className="w-6 h-6 text-blue-400" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-card border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.needsAttention}</p>
                  <p className="text-2xl font-bold text-white">{needsAttentionKPIs}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-orange-500/20 flex items-center justify-center">
                  <TrendingDown className="w-6 h-6 text-orange-400" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <select
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(e.target.value)}
                className="glass-input px-3 py-2 rounded-lg"
              >
                <option value="daily">{t.daily}</option>
                <option value="weekly">{t.weekly}</option>
                <option value="monthly">{t.monthly}</option>
                <option value="quarterly">{t.quarterly}</option>
                <option value="yearly">{t.yearly}</option>
              </select>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="glass-input px-3 py-2 rounded-lg"
              >
                <option value="all">جميع الفئات</option>
                <option value="Financial">{t.financial}</option>
                <option value="Sales">{t.sales}</option>
                <option value="HR">{t.hr}</option>
                <option value="Operations">{t.operations}</option>
                <option value="Customer">{t.customer}</option>
              </select>
            </div>
          </CardContent>
        </Card>

        {/* KPIs Grid */}
        <div>
          <h2 className="text-2xl font-bold text-white mb-4">{t.kpis}</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {filteredKPIs.map((kpi) => (
              <Card key={kpi.id} className="glass-card border-white/20">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="w-10 h-10 rounded-full bg-sky-500/20 flex items-center justify-center">
                      {getCategoryIcon(kpi.category)}
                    </div>
                    <div className="flex items-center gap-1">
                      {getTrendIcon(kpi.trend)}
                      <span className={`text-sm font-medium ${getTrendColor(kpi.trend)}`}>
                        {kpi.changePercentage > 0 ? '+' : ''}{kpi.changePercentage.toFixed(1)}%
                      </span>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <h3 className="text-white font-medium text-sm">
                      {language === 'ar' ? kpi.nameAr : kpi.name}
                    </h3>
                    <div className="flex items-baseline gap-2">
                      <span className="text-2xl font-bold text-white">
                        {kpi.value.toLocaleString()}
                      </span>
                      <span className="text-white/60 text-sm">
                        {language === 'ar' ? kpi.unitAr : kpi.unit}
                      </span>
                    </div>
                    <div className="flex justify-between text-xs text-white/60">
                      <span>{t.target}: {kpi.target.toLocaleString()}</span>
                      <span>{kpi.lastUpdated}</span>
                    </div>
                    
                    {/* Progress bar */}
                    <div className="w-full bg-white/10 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${
                          kpi.value >= kpi.target ? 'bg-green-500' : 'bg-orange-500'
                        }`}
                        style={{ width: `${Math.min((kpi.value / kpi.target) * 100, 100)}%` }}
                      ></div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Reports Section */}
        <div>
          <h2 className="text-2xl font-bold text-white mb-4">{t.reports}</h2>
          <Card className="glass-card border-white/20">
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-white/10">
                      <th className="text-right p-4 text-white/70 font-medium">{t.reportTitle}</th>
                      <th className="text-right p-4 text-white/70 font-medium">{t.type}</th>
                      <th className="text-right p-4 text-white/70 font-medium">{t.frequency}</th>
                      <th className="text-right p-4 text-white/70 font-medium">{t.status}</th>
                      <th className="text-right p-4 text-white/70 font-medium">{t.lastGenerated}</th>
                      <th className="text-right p-4 text-white/70 font-medium">{t.nextScheduled}</th>
                      <th className="text-right p-4 text-white/70 font-medium">{t.actions}</th>
                    </tr>
                  </thead>
                  <tbody>
                    {reports.map((report) => (
                      <tr key={report.id} className="border-b border-white/5 hover:bg-white/5">
                        <td className="p-4">
                          <div>
                            <span className="text-white font-medium">
                              {language === 'ar' ? report.titleAr : report.title}
                            </span>
                            <div className="text-xs text-white/60 mt-1">
                              {language === 'ar' ? report.descriptionAr : report.description}
                            </div>
                          </div>
                        </td>
                        <td className="p-4">
                          <Badge className={`${getTypeColor(report.type)} border`}>
                            {t[report.type as keyof typeof t]}
                          </Badge>
                        </td>
                        <td className="p-4">
                          <span className="text-white">{t[report.frequency as keyof typeof t]}</span>
                        </td>
                        <td className="p-4">
                          <Badge className={`${getStatusColor(report.status)} border`}>
                            {t[report.status as keyof typeof t]}
                          </Badge>
                        </td>
                        <td className="p-4">
                          <span className="text-white/70">{report.lastGenerated}</span>
                        </td>
                        <td className="p-4">
                          <span className="text-white/70">{report.nextScheduled}</span>
                        </td>
                        <td className="p-4">
                          <div className="flex gap-2">
                            <Button size="sm" variant="ghost" className="text-white/70 hover:text-white">
                              <Eye className="w-4 h-4" />
                            </Button>
                            <Button size="sm" variant="ghost" className="text-blue-400 hover:text-blue-300">
                              <Download className="w-4 h-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
