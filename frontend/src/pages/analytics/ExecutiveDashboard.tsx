import React, { useState, useEffect } from 'react';
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign,
  Users,
  Package,
  AlertTriangle,
  Target,
  BarChart3,
  PieChart,
  Activity,
  Calendar,
  Download,
  RefreshCw,
  Settings
} from 'lucide-react';
// Language is passed as prop, not from context

interface KPIMetric {
  id: number;
  name: string;
  name_ar: string;
  type: string;
  value: number;
  unit: string;
  status: 'GOOD' | 'WARNING' | 'CRITICAL' | 'NO_DATA';
  target: number | null;
  period_end: string;
}

interface DashboardData {
  id: number;
  name: string;
  name_ar: string;
  dashboard_type: string;
  layout_config: any;
  refresh_interval: number;
  widgets: any[];
}

interface ExecutiveDashboardProps {
  language?: 'ar' | 'en';
}

const ExecutiveDashboard: React.FC<ExecutiveDashboardProps> = ({ language = 'en' }) => {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [kpiMetrics, setKpiMetrics] = useState<KPIMetric[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState('current_month');

  useEffect(() => {
    fetchDashboardData();
    fetchKPIMetrics();
    
    // Set up auto-refresh
    const interval = setInterval(() => {
      fetchKPIMetrics();
    }, 300000); // 5 minutes

    return () => clearInterval(interval);
  }, []);

  const fetchDashboardData = async () => {
    try {
      const response = await fetch('/api/dashboards/?dashboard_type=EXECUTIVE');
      if (response.ok) {
        const data = await response.json();
        if (data.results && data.results.length > 0) {
          const dashboard = data.results[0];
          const dashboardDetailResponse = await fetch(`/api/dashboards/${dashboard.id}/data/`);
          if (dashboardDetailResponse.ok) {
            const dashboardDetail = await dashboardDetailResponse.json();
            setDashboardData(dashboardDetail);
          }
        }
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    }
  };

  const fetchKPIMetrics = async () => {
    try {
      setRefreshing(true);
      const response = await fetch('/api/kpi-metrics/dashboard_metrics/');
      if (response.ok) {
        const data = await response.json();
        setKpiMetrics(data);
      }
    } catch (error) {
      console.error('Error fetching KPI metrics:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const getStatusColor = (status: string): void => {
    const colors = {
      'GOOD': 'text-green-600 bg-green-50 border-green-200',
      'WARNING': 'text-yellow-600 bg-yellow-50 border-yellow-200',
      'CRITICAL': 'text-red-600 bg-red-50 border-red-200',
      'NO_DATA': 'text-gray-600 bg-gray-50 border-gray-200'
    };
    return colors[status as keyof typeof colors] || colors.NO_DATA;
  };

  const getStatusIcon = (status: string): void => {
    switch (status) {
      case 'GOOD':
        return <TrendingUp className="h-4 w-4 text-green-600" />;
      case 'WARNING':
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      case 'CRITICAL':
        return <TrendingDown className="h-4 w-4 text-red-600" />;
      default:
        return <Activity className="h-4 w-4 text-gray-600" />;
    }
  };

  const formatValue = (value: number, unit: string): string => {
    if (typeof value !== 'number' || isNaN(value)) return '--';

    if (unit === 'SAR') {
      return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      }).format(value);
    } else if (unit === '%') {
      return `${value.toFixed(1)}%`;
    } else if (unit === 'days') {
      return `${value.toFixed(1)} ${language === 'ar' ? 'أيام' : 'days'}`;
    } else {
      return `${value.toLocaleString()} ${unit}`;
    }
  };

  const getAchievementPercentage = (value: number, target: number | null): void => {
    if (!target) return 0;
    return Math.min((value / target) * 100, 100);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">
          {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">
            {language === 'ar' ? 'لوحة القيادة التنفيذية' : 'Executive Dashboard'}
          </h1>
          <p className="text-muted-foreground">
            {language === 'ar' 
              ? 'نظرة عامة شاملة على الأداء والمؤشرات الرئيسية'
              : 'Comprehensive overview of performance and key metrics'
            }
          </p>
        </div>
        <div className="flex gap-2">
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-[180px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="current_month">
                {language === 'ar' ? 'الشهر الحالي' : 'Current Month'}
              </SelectItem>
              <SelectItem value="last_month">
                {language === 'ar' ? 'الشهر الماضي' : 'Last Month'}
              </SelectItem>
              <SelectItem value="quarter">
                {language === 'ar' ? 'الربع الحالي' : 'Current Quarter'}
              </SelectItem>
              <SelectItem value="year">
                {language === 'ar' ? 'السنة الحالية' : 'Current Year'}
              </SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={fetchKPIMetrics} disabled={refreshing}>
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            {language === 'ar' ? 'تحديث' : 'Refresh'}
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            {language === 'ar' ? 'تصدير' : 'Export'}
          </Button>
          <Button variant="outline">
            <Settings className="h-4 w-4 mr-2" />
            {language === 'ar' ? 'إعدادات' : 'Settings'}
          </Button>
        </div>
      </div>

      {/* KPI Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {kpiMetrics.map((metric) => (
          <Card key={metric.id} className={`border-l-4 ${getStatusColor(metric.status)}`}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? metric.name_ar : metric.name}
              </CardTitle>
              {getStatusIcon(metric.status)}
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold mb-2">
                {formatValue(metric.value, metric.unit)}
              </div>
              
              {metric.target && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm text-muted-foreground">
                    <span>{language === 'ar' ? 'الهدف:' : 'Target:'}</span>
                    <span>{formatValue(metric.target, metric.unit)}</span>
                  </div>
                  <Progress 
                    value={getAchievementPercentage(metric.value, metric.target)} 
                    className="h-2"
                  />
                  <div className="text-xs text-muted-foreground">
                    {getAchievementPercentage(metric.value, metric.target).toFixed(1)}% 
                    {language === 'ar' ? ' من الهدف' : ' of target'}
                  </div>
                </div>
              )}
              
              <div className="flex items-center justify-between mt-2">
                <Badge variant={metric.status === 'GOOD' ? 'default' : 'destructive'}>
                  {language === 'ar' 
                    ? metric.status === 'GOOD' ? 'جيد' 
                      : metric.status === 'WARNING' ? 'تحذير'
                      : metric.status === 'CRITICAL' ? 'حرج'
                      : 'لا توجد بيانات'
                    : metric.status.replace('_', ' ')
                  }
                </Badge>
                <span className="text-xs text-muted-foreground">
                  {new Date(metric.period_end).toLocaleDateString()}
                </span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Charts and Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Revenue Trend Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              {language === 'ar' ? 'اتجاه الإيرادات' : 'Revenue Trend'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-64 flex items-center justify-center text-muted-foreground">
              {language === 'ar' ? 'مخطط الإيرادات - قيد التطوير' : 'Revenue Chart - Coming Soon'}
            </div>
          </CardContent>
        </Card>

        {/* Asset Status Distribution */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PieChart className="h-5 w-5" />
              {language === 'ar' ? 'توزيع حالة الأصول' : 'Asset Status Distribution'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-64 flex items-center justify-center text-muted-foreground">
              {language === 'ar' ? 'مخطط الأصول - قيد التطوير' : 'Asset Chart - Coming Soon'}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>
            {language === 'ar' ? 'إجراءات سريعة' : 'Quick Actions'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Button variant="outline" className="h-20 flex-col">
              <BarChart3 className="h-6 w-6 mb-2" />
              <span>{language === 'ar' ? 'التقارير المالية' : 'Financial Reports'}</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <Package className="h-6 w-6 mb-2" />
              <span>{language === 'ar' ? 'إدارة الأصول' : 'Asset Management'}</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <Users className="h-6 w-6 mb-2" />
              <span>{language === 'ar' ? 'إدارة الموظفين' : 'Employee Management'}</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <Target className="h-6 w-6 mb-2" />
              <span>{language === 'ar' ? 'إدارة المؤشرات' : 'KPI Management'}</span>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            {language === 'ar' ? 'النشاط الأخير' : 'Recent Activity'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center gap-4 p-3 border rounded-lg">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <div className="flex-1">
                <div className="font-medium">
                  {language === 'ar' ? 'تم تحديث مؤشر الإيرادات الشهرية' : 'Monthly Revenue KPI Updated'}
                </div>
                <div className="text-sm text-muted-foreground">
                  {language === 'ar' ? 'منذ 5 دقائق' : '5 minutes ago'}
                </div>
              </div>
            </div>
            <div className="flex items-center gap-4 p-3 border rounded-lg">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <div className="flex-1">
                <div className="font-medium">
                  {language === 'ar' ? 'تم إنشاء تقرير جديد' : 'New Report Generated'}
                </div>
                <div className="text-sm text-muted-foreground">
                  {language === 'ar' ? 'منذ 15 دقيقة' : '15 minutes ago'}
                </div>
              </div>
            </div>
            <div className="flex items-center gap-4 p-3 border rounded-lg">
              <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
              <div className="flex-1">
                <div className="font-medium">
                  {language === 'ar' ? 'تحذير: انخفاض في معدل استخدام الأصول' : 'Warning: Asset Utilization Rate Declined'}
                </div>
                <div className="text-sm text-muted-foreground">
                  {language === 'ar' ? 'منذ ساعة واحدة' : '1 hour ago'}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ExecutiveDashboard;
