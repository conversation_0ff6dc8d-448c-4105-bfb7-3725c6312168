import React, { useState, useEffect } from 'react';
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign,
  Users,
  Package,
  AlertTriangle,
  Target,
  BarChart3,
  PieChart,
  Activity,
  Calendar,
  Download,
  RefreshCw,
  Settings
} from 'lucide-react';
// Language is passed as prop, not from context

interface KPIMetric {
  id: number;
  name: string;
  name_ar: string;
  type: string;
  value: number;
  unit: string;
  status: 'GOOD' | 'WARNING' | 'CRITICAL' | 'NO_DATA';
  target: number | null;
  period_end: string;
}

interface DashboardData {
  id: number;
  name: string;
  name_ar: string;
  dashboard_type: string;
  layout_config: any;
  refresh_interval: number;
  widgets: any[];
}

interface ExecutiveDashboardProps {
  language?: 'ar' | 'en';
}

// @ts-ignore
const ExecutiveDashboard: (React as any).FC<ExecutiveDashboardProps> = ({ language = 'en' }) => {
  // @ts-ignore
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  // @ts-ignore
  const [kpiMetrics, setKpiMetrics] = useState<KPIMetric[]>([]);
  const [loading, setLoading] = useState(true as any);
  const [refreshing, setRefreshing] = useState(false as any);
  const [selectedPeriod, setSelectedPeriod] = useState('current_month' as any);

  // @ts-ignore
  useEffect(( as any) => {
    // @ts-ignore
    fetchDashboardData( as any);
    fetchKPIMetrics( as any);
    
    // Set up auto-refresh
    // @ts-ignore
    const interval: any = setInterval(( as any) => {
      // @ts-ignore
      fetchKPIMetrics( as any);
    // @ts-ignore
    }, 300000); // 5 minutes

    // @ts-ignore
    return () => clearInterval(interval as any);
  // @ts-ignore
  }, []);

  // @ts-ignore
  const fetchDashboardData: any = async () => {
    // @ts-ignore
    try {
      // @ts-ignore
      const response = await fetch('/api/dashboards/?dashboard_type=EXECUTIVE' as any);
      if ((response as any).ok) {
        // @ts-ignore
        const data: any = await (response as any).json( as any);
        // @ts-ignore
        if ((data as any).results && (data as any).results.length > 0) {
          // @ts-ignore
          const dashboard: any = (data as any).results[0];
          const dashboardDetailResponse: any = await fetch(`/api/dashboards/${(dashboard as any as any).id}/data/`);
          if ((dashboardDetailResponse as any).ok) {
            // @ts-ignore
            const dashboardDetail: any = await (dashboardDetailResponse as any).json( as any);
            setDashboardData(dashboardDetail as any);
          // @ts-ignore
          }
        // @ts-ignore
        }
      // @ts-ignore
      }
    // @ts-ignore
    } catch (error) {
      // @ts-ignore
      (console as any).error('Error fetching dashboard data:', error as any);
    // @ts-ignore
    }
  // @ts-ignore
  };

  // @ts-ignore
  const fetchKPIMetrics: any = async () => {
    // @ts-ignore
    try {
      // @ts-ignore
      setRefreshing(true as any);
      const response: any = await fetch('/api/kpi-metrics/dashboard_metrics/' as any);
      if ((response as any).ok) {
        // @ts-ignore
        const data: any = await (response as any).json( as any);
        setKpiMetrics(data as any);
      // @ts-ignore
      }
    // @ts-ignore
    } catch (error) {
      // @ts-ignore
      (console as any).error('Error fetching KPI metrics:', error as any);
    // @ts-ignore
    } finally {
      // @ts-ignore
      setLoading(false as any);
      setRefreshing(false as any);
    // @ts-ignore
    }
  // @ts-ignore
  };

  // @ts-ignore
  const getStatusColor: any = (status: string): void => {
    // @ts-ignore
    const colors = {
      // @ts-ignore
      'GOOD': 'text-green-600 bg-green-50 border-green-200',
      'WARNING': 'text-yellow-600 bg-yellow-50 border-yellow-200',
      'CRITICAL': 'text-red-600 bg-red-50 border-red-200',
      'NO_DATA': 'text-gray-600 bg-gray-50 border-gray-200'
    // @ts-ignore
    };
    return colors[status as keyof typeof colors] || (colors as any).NO_DATA;
  // @ts-ignore
  };

  // @ts-ignore
  const getStatusIcon: any = (status: string): void => {
    // @ts-ignore
    switch (status) {
      // @ts-ignore
      case 'GOOD':
        return <TrendingUp className="h-4 w-4 text-green-600" />;
      case 'WARNING':
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      case 'CRITICAL':
        return <TrendingDown className="h-4 w-4 text-red-600" />;
      default:
        return <Activity className="h-4 w-4 text-gray-600" />;
    // @ts-ignore
    }
  // @ts-ignore
  };

  // @ts-ignore
  const formatValue: any = (value: number, unit: string): string => {
    // @ts-ignore
    if (typeof value !== 'number' || isNaN(value as any)) return '--';

    if (unit === 'SAR') {
      // @ts-ignore
      return new (Intl as any).NumberFormat('ar-SA', {
        // @ts-ignore
        style: 'currency',
        currency: 'SAR',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      // @ts-ignore
      } as any).format(value as any);
    // @ts-ignore
    } else if (unit === '%') {
      // @ts-ignore
      return `${(value as any).toFixed(1 as any)}%`;
    // @ts-ignore
    } else if (unit === 'days') {
      // @ts-ignore
      return `${(value as any).toFixed(1 as any)} ${language === 'ar' ? 'أيام' : 'days'}`;
    // @ts-ignore
    } else {
      // @ts-ignore
      return `${(value as any).toLocaleString( as any)} ${unit}`;
    // @ts-ignore
    }
  // @ts-ignore
  };

  // @ts-ignore
  const getAchievementPercentage: any = (value: number, target: number | null): void => {
    // @ts-ignore
    if (!target) return 0;
    return (Math as any).min((value / target as any) * 100, 100);
  // @ts-ignore
  };

  if (loading) {
    // @ts-ignore
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">
          {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
        </div>
      </div>
    );
  // @ts-ignore
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">
            {language === 'ar' ? 'لوحة القيادة التنفيذية' : 'Executive Dashboard'}
          </h1>
          <p className="text-muted-foreground">
            {language === 'ar' 
              ? 'نظرة عامة شاملة على الأداء والمؤشرات الرئيسية'
              : 'Comprehensive overview of performance and key metrics'
            }
          </p>
        </div>
        <div className="flex gap-2">
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-[180px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="current_month">
                {language === 'ar' ? 'الشهر الحالي' : 'Current Month'}
              </SelectItem>
              <SelectItem value="last_month">
                {language === 'ar' ? 'الشهر الماضي' : 'Last Month'}
              </SelectItem>
              <SelectItem value="quarter">
                {language === 'ar' ? 'الربع الحالي' : 'Current Quarter'}
              </SelectItem>
              <SelectItem value="year">
                {language === 'ar' ? 'السنة الحالية' : 'Current Year'}
              </SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={fetchKPIMetrics} disabled={refreshing}>
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            {language === 'ar' ? 'تحديث' : 'Refresh'}
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            {language === 'ar' ? 'تصدير' : 'Export'}
          </Button>
          <Button variant="outline">
            <Settings className="h-4 w-4 mr-2" />
            {language === 'ar' ? 'إعدادات' : 'Settings'}
          </Button>
        </div>
      </div>

      {/* KPI Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        // @ts-ignore
        {(kpiMetrics as any).map((metric as any) => (
          <Card key={(metric as any).id} className={`border-l-4 ${getStatusColor((metric as any as any).status)}`}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? (metric as any).name_ar : (metric as any).name}
              </CardTitle>
              {getStatusIcon((metric as any as any).status)}
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold mb-2">
                {formatValue((metric as any as any).value, (metric as any).unit)}
              </div>
              
              {(metric as any).target && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm text-muted-foreground">
                    <span>{language === 'ar' ? 'الهدف:' : 'Target:'}</span>
                    <span>{formatValue((metric as any as any).target, (metric as any).unit)}</span>
                  </div>
                  <Progress 
                    value={getAchievementPercentage((metric as any as any).value, (metric as any).target)} 
                    className="h-2"
                  />
                  <div className="text-xs text-muted-foreground">
                    {getAchievementPercentage((metric as any as any).value, (metric as any).target).toFixed(1 as any)}% 
                    {language === 'ar' ? ' من الهدف' : ' of target'}
                  </div>
                </div>
              )}
              
              <div className="flex items-center justify-between mt-2">
                <Badge variant={(metric as any).status === 'GOOD' ? 'default' : 'destructive'}>
                  {language === 'ar' 
                    ? (metric as any).status === 'GOOD' ? 'جيد' 
                      : (metric as any).status === 'WARNING' ? 'تحذير'
                      : (metric as any).status === 'CRITICAL' ? 'حرج'
                      : 'لا توجد بيانات'
                    : (metric as any).status.replace('_', ' ' as any)
                  }
                </Badge>
                <span className="text-xs text-muted-foreground">
                  // @ts-ignore
                  {new Date((metric as any as any).period_end).toLocaleDateString( as any)}
                </span>
              </div>
            </CardContent>
          </Card>
        // @ts-ignore
        ))}
      </div>

      {/* Charts and Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Revenue Trend Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              {language === 'ar' ? 'اتجاه الإيرادات' : 'Revenue Trend'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-64 flex items-center justify-center text-muted-foreground">
              {language === 'ar' ? 'مخطط الإيرادات - قيد التطوير' : 'Revenue Chart - Coming Soon'}
            </div>
          </CardContent>
        </Card>

        {/* Asset Status Distribution */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PieChart className="h-5 w-5" />
              {language === 'ar' ? 'توزيع حالة الأصول' : 'Asset Status Distribution'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-64 flex items-center justify-center text-muted-foreground">
              {language === 'ar' ? 'مخطط الأصول - قيد التطوير' : 'Asset Chart - Coming Soon'}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>
            {language === 'ar' ? 'إجراءات سريعة' : 'Quick Actions'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Button variant="outline" className="h-20 flex-col">
              <BarChart3 className="h-6 w-6 mb-2" />
              <span>{language === 'ar' ? 'التقارير المالية' : 'Financial Reports'}</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <Package className="h-6 w-6 mb-2" />
              <span>{language === 'ar' ? 'إدارة الأصول' : 'Asset Management'}</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <Users className="h-6 w-6 mb-2" />
              <span>{language === 'ar' ? 'إدارة الموظفين' : 'Employee Management'}</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <Target className="h-6 w-6 mb-2" />
              <span>{language === 'ar' ? 'إدارة المؤشرات' : 'KPI Management'}</span>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            {language === 'ar' ? 'النشاط الأخير' : 'Recent Activity'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center gap-4 p-3 border rounded-lg">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <div className="flex-1">
                <div className="font-medium">
                  {language === 'ar' ? 'تم تحديث مؤشر الإيرادات الشهرية' : 'Monthly Revenue KPI Updated'}
                </div>
                <div className="text-sm text-muted-foreground">
                  {language === 'ar' ? 'منذ 5 دقائق' : '5 minutes ago'}
                </div>
              </div>
            </div>
            <div className="flex items-center gap-4 p-3 border rounded-lg">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <div className="flex-1">
                <div className="font-medium">
                  {language === 'ar' ? 'تم إنشاء تقرير جديد' : 'New Report Generated'}
                </div>
                <div className="text-sm text-muted-foreground">
                  {language === 'ar' ? 'منذ 15 دقيقة' : '15 minutes ago'}
                </div>
              </div>
            </div>
            <div className="flex items-center gap-4 p-3 border rounded-lg">
              <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
              <div className="flex-1">
                <div className="font-medium">
                  {language === 'ar' ? 'تحذير: انخفاض في معدل استخدام الأصول' : 'Warning: Asset Utilization Rate Declined'}
                </div>
                <div className="text-sm text-muted-foreground">
                  {language === 'ar' ? 'منذ ساعة واحدة' : '1 hour ago'}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
// @ts-ignore
};

export default ExecutiveDashboard;
