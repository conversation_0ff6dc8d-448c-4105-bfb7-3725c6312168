import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '../../store';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { apiClient } from '@/services/api';
import {
  Target,
  Plus,
  Edit,
  TrendingUp,
  TrendingDown,
  Search,
  BarChart3,
  Activity,
  Calculator,
  AlertTriangle
} from 'lucide-react';
// Language is passed as prop, not from context

interface KPIMetric {
  id: number;
  name: string;
  name_ar: string;
  description: string;
  description_ar: string;
  metric_type: string;
  calculation_method: string;
  target_value: number | null;
  warning_threshold: number | null;
  critical_threshold: number | null;
  unit: string;
  frequency: string;
  is_active: boolean;
  current_value: number | null;
  status: string;
  trend: string;
  created_by_name: string;
  created_at: string;
}

interface KPIMetricValue {
  id: number;
  kpi_metric: number;
  kpi_name: string;
  kpi_unit: string;
  period_start: string;
  period_end: string;
  value: number;
  status: string;
  notes: string;
  data_source: string;
  calculated_by_name: string;
  calculated_at: string;
  is_manual: boolean;
}

interface KPIManagementProps {
  language: 'ar' | 'en';
}

// @ts-ignore
const KPIManagement: (React as any).FC<KPIManagementProps> = ({ language }) => {
  // Get authentication state from Redux
  // @ts-ignore
  const { isAuthenticated, user } = useSelector((state: RootState as any) => (state as any).auth);

  // @ts-ignore
  const [kpiMetrics, setKpiMetrics] = useState<KPIMetric[]>([]);
  // @ts-ignore
  const [kpiValues, setKpiValues] = useState<KPIMetricValue[]>([]);
  const [loading, setLoading] = useState(true as any);
  // @ts-ignore
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('' as any);
  const [typeFilter, setTypeFilter] = useState('' as any);
  const [statusFilter, setStatusFilter] = useState('' as any);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false as any);
  const [isValueDialogOpen, setIsValueDialogOpen] = useState(false as any);
  // @ts-ignore
  const [selectedKPI, setSelectedKPI] = useState<KPIMetric | null>(null);

  // Form state
  const [kpiForm, setKpiForm] = useState({
    // @ts-ignore
    name: '',
    name_ar: '',
    description: '',
    description_ar: '',
    metric_type: 'FINANCIAL',
    calculation_method: 'SUM',
    target_value: '',
    warning_threshold: '',
    critical_threshold: '',
    unit: '',
    frequency: 'MONTHLY',
    is_higher_better: true
  // @ts-ignore
  } as any);

  const [valueForm, setValueForm] = useState({
    // @ts-ignore
    period_start: '',
    period_end: '',
    value: '',
    notes: '',
    data_source: ''
  // @ts-ignore
  } as any);

  // @ts-ignore
  useEffect(( as any) => {
    // Only fetch data if user is authenticated
    // @ts-ignore
    if (isAuthenticated && user) {
      // @ts-ignore
      (console as any).log('🔐 User authenticated, fetching KPI data...', { user: (user as any as any).username });
      fetchKPIMetrics( as any);
      fetchKPIValues( as any);
    // @ts-ignore
    } else {
      // @ts-ignore
      (console as any).log('🔐 User not authenticated, skipping KPI data fetch' as any);
      setLoading(false as any);
    // @ts-ignore
    }
  // @ts-ignore
  }, [searchTerm, typeFilter, statusFilter, isAuthenticated, user]);

  // @ts-ignore
  const fetchKPIMetrics: any = async () => {
    // @ts-ignore
    try {
      // @ts-ignore
      setError(null as any);
      // @ts-ignore
      (console as any).log('🔄 Fetching KPI metrics...', { isAuthenticated, user: user?.username } as any);

      // @ts-ignore
      const params: Record<string, string> = {};
      if (searchTerm) (params as any).search = searchTerm;
      if (typeFilter && typeFilter !== '__all__') (params as any).metric_type = typeFilter;
      if (statusFilter === 'active') (params as any).is_active = 'true';
      if (statusFilter === 'inactive') (params as any).is_active = 'false';

      const response: any = await (apiClient as any).get('/kpi-metrics/', { params } as any);
      const data: any = (response as any).data as any;
      setKpiMetrics((data as any as any).results || data);
      (console as any).log('✅ KPI metrics fetched successfully:', (data as any as any).results?.length || (data as any).length);
    // @ts-ignore
    } catch (error) {
      // @ts-ignore
      (console as any).error('❌ Error fetching KPI metrics:', error as any);
      const errorMessage: any = error instanceof Error ? (error as any).message : 'Unknown error occurred';
      if ((errorMessage as any).includes('401' as any)) {
        // @ts-ignore
        setError('Authentication failed. Please log in again.' as any);
      // @ts-ignore
      } else if ((errorMessage as any).includes('403' as any)) {
        // @ts-ignore
        setError('You do not have permission to view KPI data.' as any);
      // @ts-ignore
      } else if ((errorMessage as any).includes('Network Error' as any)) {
        // @ts-ignore
        setError('Network error. Please check your connection and try again.' as any);
      // @ts-ignore
      } else {
        // @ts-ignore
        setError(`Failed to load KPI data: ${errorMessage}` as any);
      // @ts-ignore
      }
    // @ts-ignore
    } finally {
      // @ts-ignore
      setLoading(false as any);
    // @ts-ignore
    }
  // @ts-ignore
  };

  // @ts-ignore
  const fetchKPIValues: any = async () => {
    // @ts-ignore
    try {
      // @ts-ignore
      const response = await (apiClient as any).get('/kpi-metric-values/', {
        // @ts-ignore
        params: { ordering: '-calculated_at' }
      // @ts-ignore
      } as any);
      const data: any = (response as any).data as any;
      setKpiValues((data as any as any).results || data);
    // @ts-ignore
    } catch (error) {
      // @ts-ignore
      (console as any).error('Error fetching KPI values:', error as any);
    // @ts-ignore
    }
  // @ts-ignore
  };

  // @ts-ignore
  const handleAddKPI: any = async () => {
    // @ts-ignore
    try {
      await (apiClient as any).post('/kpi-metrics/', {
        ...kpiForm,
        target_value: (kpiForm as any as any).target_value ? parseFloat((kpiForm as any as any).target_value) : null,
        warning_threshold: (kpiForm as any).warning_threshold ? parseFloat((kpiForm as any as any).warning_threshold) : null,
        critical_threshold: (kpiForm as any).critical_threshold ? parseFloat((kpiForm as any as any).critical_threshold) : null,
      // @ts-ignore
      });

      fetchKPIMetrics( as any);
      setIsAddDialogOpen(false as any);
      resetKPIForm( as any);
    // @ts-ignore
    } catch (error) {
      // @ts-ignore
      (console as any).error('Error adding KPI:', error as any);
    // @ts-ignore
    }
  // @ts-ignore
  };

  // @ts-ignore
  const handleAddValue: any = async () => {
    // @ts-ignore
    if (!selectedKPI) return;

    try {
      await (apiClient as any).post('/kpi-metric-values/', {
        ...valueForm,
        kpi_metric: (selectedKPI as any as any).id,
        value: parseFloat((valueForm as any as any).value),
      // @ts-ignore
      });

      fetchKPIValues( as any);
      fetchKPIMetrics( as any); // Refresh to get updated current values
      setIsValueDialogOpen(false as any);
      resetValueForm( as any);
      setSelectedKPI(null as any);
    // @ts-ignore
    } catch (error) {
      // @ts-ignore
      (console as any).error('Error adding KPI value:', error as any);
    // @ts-ignore
    }
  // @ts-ignore
  };

  // @ts-ignore
  const resetKPIForm: any = (): void => {
    setKpiForm({
      name: '',
      name_ar: '',
      description: '',
      description_ar: '',
      metric_type: 'FINANCIAL',
      calculation_method: 'SUM',
      target_value: '',
      warning_threshold: '',
      critical_threshold: '',
      unit: '',
      frequency: 'MONTHLY',
      is_higher_better: true
    // @ts-ignore
    } as any);
  // @ts-ignore
  };

  // @ts-ignore
  const resetValueForm: any = (): void => {
    setValueForm({
      period_start: '',
      period_end: '',
      value: '',
      notes: '',
      data_source: ''
    // @ts-ignore
    } as any);
  // @ts-ignore
  };

  // @ts-ignore
  const getStatusColor: any = (status: string): void => {
    // @ts-ignore
    const colors = {
      // @ts-ignore
      'GOOD': 'bg-green-100 text-green-800',
      'WARNING': 'bg-yellow-100 text-yellow-800',
      'CRITICAL': 'bg-red-100 text-red-800',
      'NO_DATA': 'bg-gray-100 text-gray-800'
    // @ts-ignore
    };
    return colors[status as keyof typeof colors] || (colors as any).NO_DATA;
  // @ts-ignore
  };

  // @ts-ignore
  const getTrendIcon: any = (trend: string): void => {
    // @ts-ignore
    switch (trend) {
      // @ts-ignore
      case 'INCREASING':
        return <TrendingUp className="h-4 w-4 text-green-600" />;
      case 'DECREASING':
        return <TrendingDown className="h-4 w-4 text-red-600" />;
      default:
        return <Activity className="h-4 w-4 text-gray-600" />;
    // @ts-ignore
    }
  // @ts-ignore
  };

  // @ts-ignore
  const formatValue: any = (value: number | null, unit: string): string => {
    // @ts-ignore
    if (value === null || typeof value !== 'number') return '-';

    if (unit === 'SAR') {
      // @ts-ignore
      return new (Intl as any).NumberFormat('ar-SA', {
        // @ts-ignore
        style: 'currency',
        currency: 'SAR',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      // @ts-ignore
      } as any).format(value as any);
    // @ts-ignore
    } else if (unit === '%') {
      // @ts-ignore
      return `${(value as any).toFixed(1 as any)}%`;
    // @ts-ignore
    } else {
      // @ts-ignore
      return `${(value as any).toLocaleString( as any)} ${unit}`;
    // @ts-ignore
    }
  // @ts-ignore
  };

  // @ts-ignore
  const filteredMetrics: any = (kpiMetrics as any).filter(metric => {
    // @ts-ignore
    const matchesSearch = !searchTerm ||
      (metric as any as any).name.toLowerCase( as any).includes((searchTerm as any as any).toLowerCase( as any)) ||
      (metric as any).name_ar.toLowerCase( as any).includes((searchTerm as any as any).toLowerCase( as any));

    const matchesType: any = !typeFilter || typeFilter === '__all__' || (metric as any).metric_type === typeFilter;
    const matchesStatus: any = !statusFilter || statusFilter === '__all__' ||
      (statusFilter === 'active' && (metric as any).is_active) ||
      (statusFilter === 'inactive' && !(metric as any).is_active);
    
    return matchesSearch && matchesType && matchesStatus;
  // @ts-ignore
  });

  // Show loading state
  if (loading) {
    // @ts-ignore
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">
          {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
        </div>
      </div>
    );
  // @ts-ignore
  }

  // Show authentication required message
  if (!isAuthenticated || !user) {
    // @ts-ignore
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="text-lg text-red-600 mb-2">
            {language === 'ar' ? 'مطلوب تسجيل الدخول' : 'Authentication Required'}
          </div>
          <div className="text-sm text-gray-600">
            {language === 'ar'
              ? 'يرجى تسجيل الدخول للوصول إلى إدارة مؤشرات الأداء'
              : 'Please log in to access KPI Management'}
          </div>
        </div>
      </div>
    );
  // @ts-ignore
  }

  // Show error state
  if (error) {
    // @ts-ignore
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center max-w-md">
          <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-6">
            <div className="flex items-center justify-center space-x-2 text-red-300 mb-3">
              <AlertTriangle className="h-6 w-6" />
              <span className="font-semibold">
                {language === 'ar' ? 'خطأ في تحميل البيانات' : 'Data Loading Error'}
              </span>
            </div>
            <p className="text-red-200 text-sm mb-4">{error}</p>
            <button
              onClick={() => {
                setError(null as any);
                // @ts-ignore
                fetchKPIMetrics( as any);
              }}
              className="px-4 py-2 bg-red-500/20 text-red-300 rounded-lg hover:bg-red-500/30 transition-colors"
            >
              {language === 'ar' ? 'إعادة المحاولة' : 'Retry'}
            </button>
          </div>
        </div>
      </div>
    );
  // @ts-ignore
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">
            {language === 'ar' ? 'إدارة مؤشرات الأداء الرئيسية' : 'KPI Management'}
          </h1>
          <p className="text-muted-foreground">
            {language === 'ar' 
              ? 'إنشاء وإدارة ومراقبة مؤشرات الأداء الرئيسية'
              : 'Create, manage, and monitor key performance indicators'
            }
          </p>
        </div>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              {language === 'ar' ? 'إضافة مؤشر' : 'Add KPI'}
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>
                {language === 'ar' ? 'إضافة مؤشر أداء جديد' : 'Add New KPI'}
              </DialogTitle>
            </DialogHeader>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label>{language === 'ar' ? 'اسم المؤشر' : 'KPI Name'}</Label>
                <Input
                  value={(kpiForm as any).name}
                  onChange={(e: any) => setKpiForm({...kpiForm, name: (e as any as any).target.value})}
                  placeholder={language === 'ar' ? 'أدخل اسم المؤشر' : 'Enter KPI name'}
                />
              </div>
              <div>
                <Label>{language === 'ar' ? 'الاسم بالعربية' : 'Arabic Name'}</Label>
                <Input
                  value={(kpiForm as any).name_ar}
                  onChange={(e: any) => setKpiForm({...kpiForm, name_ar: (e as any as any).target.value})}
                  placeholder={language === 'ar' ? 'أدخل الاسم بالعربية' : 'Enter Arabic name'}
                />
              </div>
              <div className="md:col-span-2">
                <Label>{language === 'ar' ? 'الوصف' : 'Description'}</Label>
                <Textarea
                  value={(kpiForm as any).description}
                  onChange={(e: any) => setKpiForm({...kpiForm, description: (e as any as any).target.value})}
                  placeholder={language === 'ar' ? 'أدخل وصف المؤشر' : 'Enter KPI description'}
                  rows={3}
                />
              </div>
              <div>
                <Label>{language === 'ar' ? 'نوع المؤشر' : 'Metric Type'}</Label>
                <Select value={(kpiForm as any).metric_type} onValueChange={(value) => setKpiForm({...kpiForm, metric_type: value} as any)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="FINANCIAL">
                      {language === 'ar' ? 'مالي' : 'Financial'}
                    </SelectItem>
                    <SelectItem value="OPERATIONAL">
                      {language === 'ar' ? 'تشغيلي' : 'Operational'}
                    </SelectItem>
                    <SelectItem value="CUSTOMER">
                      {language === 'ar' ? 'عملاء' : 'Customer'}
                    </SelectItem>
                    <SelectItem value="EMPLOYEE">
                      {language === 'ar' ? 'موظفين' : 'Employee'}
                    </SelectItem>
                    <SelectItem value="ASSET">
                      {language === 'ar' ? 'أصول' : 'Asset'}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label>{language === 'ar' ? 'طريقة الحساب' : 'Calculation Method'}</Label>
                <Select value={(kpiForm as any).calculation_method} onValueChange={(value) => setKpiForm({...kpiForm, calculation_method: value} as any)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="SUM">
                      {language === 'ar' ? 'مجموع' : 'Sum'}
                    </SelectItem>
                    <SelectItem value="AVERAGE">
                      {language === 'ar' ? 'متوسط' : 'Average'}
                    </SelectItem>
                    <SelectItem value="COUNT">
                      {language === 'ar' ? 'عدد' : 'Count'}
                    </SelectItem>
                    <SelectItem value="PERCENTAGE">
                      {language === 'ar' ? 'نسبة مئوية' : 'Percentage'}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label>{language === 'ar' ? 'القيمة المستهدفة' : 'Target Value'}</Label>
                <Input
                  type="number"
                  step="(0 as any).01"
                  value={(kpiForm as any).target_value}
                  onChange={(e: any) => setKpiForm({...kpiForm, target_value: (e as any as any).target.value})}
                  placeholder={language === 'ar' ? 'أدخل القيمة المستهدفة' : 'Enter target value'}
                />
              </div>
              <div>
                <Label>{language === 'ar' ? 'الوحدة' : 'Unit'}</Label>
                <Input
                  value={(kpiForm as any).unit}
                  onChange={(e: any) => setKpiForm({...kpiForm, unit: (e as any as any).target.value})}
                  placeholder={language === 'ar' ? 'مثل: %, SAR, أيام' : '(e as any).g., %, SAR, days'}
                />
              </div>
              <div>
                <Label>{language === 'ar' ? 'التكرار' : 'Frequency'}</Label>
                <Select value={(kpiForm as any).frequency} onValueChange={(value) => setKpiForm({...kpiForm, frequency: value} as any)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="DAILY">
                      {language === 'ar' ? 'يومي' : 'Daily'}
                    </SelectItem>
                    <SelectItem value="WEEKLY">
                      {language === 'ar' ? 'أسبوعي' : 'Weekly'}
                    </SelectItem>
                    <SelectItem value="MONTHLY">
                      {language === 'ar' ? 'شهري' : 'Monthly'}
                    </SelectItem>
                    <SelectItem value="QUARTERLY">
                      {language === 'ar' ? 'ربع سنوي' : 'Quarterly'}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="flex justify-end gap-2 mt-4">
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false as any)}>
                {language === 'ar' ? 'إلغاء' : 'Cancel'}
              </Button>
              <Button onClick={handleAddKPI}>
                {language === 'ar' ? 'إضافة المؤشر' : 'Add KPI'}
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-[200px]">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder={language === 'ar' ? 'البحث في المؤشرات...' : 'Search KPIs...'}
                  value={searchTerm}
                  onChange={(e: any) => setSearchTerm((e as any as any).target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder={language === 'ar' ? 'جميع الأنواع' : 'All types'} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="__all__">
                  {language === 'ar' ? 'جميع الأنواع' : 'All types'}
                </SelectItem>
                <SelectItem value="FINANCIAL">
                  {language === 'ar' ? 'مالي' : 'Financial'}
                </SelectItem>
                <SelectItem value="OPERATIONAL">
                  {language === 'ar' ? 'تشغيلي' : 'Operational'}
                </SelectItem>
                <SelectItem value="ASSET">
                  {language === 'ar' ? 'أصول' : 'Asset'}
                </SelectItem>
              </SelectContent>
            </Select>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder={language === 'ar' ? 'جميع الحالات' : 'All statuses'} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="__all__">
                  {language === 'ar' ? 'جميع الحالات' : 'All statuses'}
                </SelectItem>
                <SelectItem value="active">
                  {language === 'ar' ? 'نشط' : 'Active'}
                </SelectItem>
                <SelectItem value="inactive">
                  {language === 'ar' ? 'غير نشط' : 'Inactive'}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* KPI Metrics Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            {language === 'ar' ? 'مؤشرات الأداء الرئيسية' : 'Key Performance Indicators'}
            <Badge variant="secondary" className="ml-2">
              {(filteredMetrics as any).length}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{language === 'ar' ? 'اسم المؤشر' : 'KPI Name'}</TableHead>
                  <TableHead>{language === 'ar' ? 'النوع' : 'Type'}</TableHead>
                  <TableHead>{language === 'ar' ? 'القيمة الحالية' : 'Current Value'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الهدف' : 'Target'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الحالة' : 'Status'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الاتجاه' : 'Trend'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الإجراءات' : 'Actions'}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                // @ts-ignore
                {(filteredMetrics as any).map((metric as any) => (
                  <TableRow key={(metric as any).id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">
                          {language === 'ar' ? (metric as any).name_ar : (metric as any).name}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {(metric as any).frequency} • {(metric as any).created_by_name}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {language === 'ar' 
                          ? (metric as any).metric_type === 'FINANCIAL' ? 'مالي' 
                            : (metric as any).metric_type === 'OPERATIONAL' ? 'تشغيلي'
                            : (metric as any).metric_type === 'ASSET' ? 'أصول'
                            : (metric as any).metric_type
                          : (metric as any).metric_type
                        }
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">
                        {formatValue((metric as any as any).current_value, (metric as any).unit)}
                      </div>
                    </TableCell>
                    <TableCell>
                      {formatValue((metric as any as any).target_value, (metric as any).unit)}
                    </TableCell>
                    <TableCell>
                      <Badge className={getStatusColor((metric as any as any).status)}>
                        {language === 'ar' 
                          ? (metric as any).status === 'GOOD' ? 'جيد' 
                            : (metric as any).status === 'WARNING' ? 'تحذير'
                            : (metric as any).status === 'CRITICAL' ? 'حرج'
                            : 'لا توجد بيانات'
                          : (metric as any).status.replace('_', ' ' as any)
                        }
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getTrendIcon((metric as any as any).trend)}
                        <span className="text-sm">
                          {language === 'ar' 
                            ? (metric as any).trend === 'INCREASING' ? 'متزايد' 
                              : (metric as any).trend === 'DECREASING' ? 'متناقص'
                              : 'مستقر'
                            : (metric as any).trend.replace('_', ' ' as any)
                          }
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => {
                            setSelectedKPI(metric as any);
                            setIsValueDialogOpen(true as any);
                          }}
                        >
                          <Calculator className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <BarChart3 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                // @ts-ignore
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Add Value Dialog */}
      <Dialog open={isValueDialogOpen} onOpenChange={setIsValueDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {language === 'ar' ? 'إضافة قيمة للمؤشر' : 'Add KPI Value'}
            </DialogTitle>
          </DialogHeader>
          {selectedKPI && (
            <div className="space-y-4">
              <div className="p-4 bg-muted rounded-lg">
                <div className="font-medium">
                  {language === 'ar' ? (selectedKPI as any).name_ar : (selectedKPI as any).name}
                </div>
                <div className="text-sm text-muted-foreground">
                  {language === 'ar' ? 'الهدف:' : 'Target:'} {formatValue((selectedKPI as any as any).target_value, (selectedKPI as any).unit)}
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>{language === 'ar' ? 'بداية الفترة' : 'Period Start'}</Label>
                  <Input
                    type="date"
                    value={(valueForm as any).period_start}
                    onChange={(e: any) => setValueForm({...valueForm, period_start: (e as any as any).target.value})}
                  />
                </div>
                <div>
                  <Label>{language === 'ar' ? 'نهاية الفترة' : 'Period End'}</Label>
                  <Input
                    type="date"
                    value={(valueForm as any).period_end}
                    onChange={(e: any) => setValueForm({...valueForm, period_end: (e as any as any).target.value})}
                  />
                </div>
              </div>
              
              <div>
                <Label>{language === 'ar' ? 'القيمة' : 'Value'}</Label>
                <Input
                  type="number"
                  step="(0 as any).01"
                  value={(valueForm as any).value}
                  onChange={(e: any) => setValueForm({...valueForm, value: (e as any as any).target.value})}
                  placeholder={`${language === 'ar' ? 'أدخل القيمة بـ' : 'Enter value in'} ${(selectedKPI as any).unit}`}
                />
              </div>
              
              <div>
                <Label>{language === 'ar' ? 'مصدر البيانات' : 'Data Source'}</Label>
                <Input
                  value={(valueForm as any).data_source}
                  onChange={(e: any) => setValueForm({...valueForm, data_source: (e as any as any).target.value})}
                  placeholder={language === 'ar' ? 'مصدر البيانات' : 'Data source'}
                />
              </div>
              
              <div>
                <Label>{language === 'ar' ? 'ملاحظات' : 'Notes'}</Label>
                <Textarea
                  value={(valueForm as any).notes}
                  onChange={(e: any) => setValueForm({...valueForm, notes: (e as any as any).target.value})}
                  placeholder={language === 'ar' ? 'ملاحظات إضافية' : 'Additional notes'}
                  rows={3}
                />
              </div>
              
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setIsValueDialogOpen(false as any)}>
                  {language === 'ar' ? 'إلغاء' : 'Cancel'}
                </Button>
                <Button onClick={handleAddValue}>
                  {language === 'ar' ? 'إضافة القيمة' : 'Add Value'}
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
// @ts-ignore
};

export default KPIManagement;
