/**
 * Modern KPI Management Page with Full CRUD Implementation
 * Uses standardized CRUD components for consistency and enhanced functionality
 */

import React, { useState, useMemo } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  Target,
  TrendingUp,
  TrendingDown,
  BarChart3,
  Activity,
  Calculator,
  AlertTriangle,
  Eye,
  Edit,
  Trash2,
  Calendar,
  User,
  Building,
  DollarSign,
  Percent,
  Clock
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { kpiService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'
import { formatKPIValue, formatTargetDisplay, getKPIStatusColor } from '@/utils/kpiFormatting'
import { transformKPIWithArabicSupport } from '@/utils/kpiFilters'

interface ModernKPIManagementProps {
  // TODO: Define proper prop types
  [key: string]: any;
}


interface KPIManagementProps {
  language: 'ar' | 'en'
}

interface KPIMetric {
  id: string
  name: string
  name_ar: string
  description: string
  description_ar: string
  metric_type: string
  calculation_method: string
  target_value: number | string | null  // API returns strings, we convert to numbers
  warning_threshold: number | string | null
  critical_threshold: number | string | null
  unit: string
  frequency: string
  is_active: boolean
  current_value: number | string | null
  status: string
  trend: string
  created_by_name: string
  created_at: string
  last_updated: string
  is_higher_better: boolean
}

const translations = {
  ar: {
    title: 'إدارة مؤشرات الأداء الرئيسية',
    subtitle: 'إنشاء وإدارة ومراقبة مؤشرات الأداء الرئيسية',
    addKPI: 'إضافة مؤشر أداء',
    editKPI: 'تعديل مؤشر الأداء',
    viewKPI: 'عرض مؤشر الأداء',
    deleteKPI: 'حذف مؤشر الأداء',
    name: 'اسم المؤشر',
    nameAr: 'الاسم بالعربية',
    description: 'الوصف',
    descriptionAr: 'الوصف بالعربية',
    metricType: 'نوع المؤشر',
    calculationMethod: 'طريقة الحساب',
    targetValue: 'القيمة المستهدفة',
    warningThreshold: 'عتبة التحذير',
    criticalThreshold: 'العتبة الحرجة',
    unit: 'الوحدة',
    frequency: 'التكرار',
    isActive: 'نشط',
    currentValue: 'القيمة الحالية',
    status: 'الحالة',
    trend: 'الاتجاه',
    createdBy: 'أنشأ بواسطة',
    createdAt: 'تاريخ الإنشاء',
    lastUpdated: 'آخر تحديث',
    isHigherBetter: 'الأعلى أفضل',
    actions: 'الإجراءات',
    search: 'البحث في مؤشرات الأداء...',
    filterByType: 'تصفية حسب النوع',
    filterByStatus: 'تصفية حسب الحالة',
    filterByFrequency: 'تصفية حسب التكرار',
    allTypes: 'جميع الأنواع',
    allStatuses: 'جميع الحالات',
    allFrequencies: 'جميع التكرارات',
    active: 'نشط',
    inactive: 'غير نشط',
    good: 'جيد',
    warning: 'تحذير',
    critical: 'حرج',
    noData: 'لا توجد بيانات',
    daily: 'يومي',
    weekly: 'أسبوعي',
    monthly: 'شهري',
    quarterly: 'ربع سنوي',
    yearly: 'سنوي',
    financial: 'مالي',
    operational: 'تشغيلي',
    hr: 'موارد بشرية',
    customer: 'عملاء',
    strategic: 'استراتيجي'
  },
  en: {
    title: 'Key Performance Indicators Management',
    subtitle: 'Create, manage, and monitor key performance indicators',
    addKPI: 'Add KPI',
    editKPI: 'Edit KPI',
    viewKPI: 'View KPI',
    deleteKPI: 'Delete KPI',
    name: 'Name',
    nameAr: 'Arabic Name',
    description: 'Description',
    descriptionAr: 'Arabic Description',
    metricType: 'Metric Type',
    calculationMethod: 'Calculation Method',
    targetValue: 'Target Value',
    warningThreshold: 'Warning Threshold',
    criticalThreshold: 'Critical Threshold',
    unit: 'Unit',
    frequency: 'Frequency',
    isActive: 'Active',
    currentValue: 'Current Value',
    status: 'Status',
    trend: 'Trend',
    createdBy: 'Created By',
    createdAt: 'Created At',
    lastUpdated: 'Last Updated',
    isHigherBetter: 'Higher is Better',
    actions: 'Actions',
    search: 'Search KPIs...',
    filterByType: 'Filter by Type',
    filterByStatus: 'Filter by Status',
    filterByFrequency: 'Filter by Frequency',
    allTypes: 'All Types',
    allStatuses: 'All Statuses',
    allFrequencies: 'All Frequencies',
    active: 'Active',
    inactive: 'Inactive',
    good: 'Good',
    warning: 'Warning',
    critical: 'Critical',
    noData: 'No Data',
    daily: 'Daily',
    weekly: 'Weekly',
    monthly: 'Monthly',
    quarterly: 'Quarterly',
    yearly: 'Yearly',
    financial: 'Financial',
    operational: 'Operational',
    hr: 'HR',
    customer: 'Customer',
    strategic: 'Strategic'
  }
}

// Status badge component
// @ts-ignore
const StatusBadge = ({ status, language }: { status: string, language: 'ar' | 'en' }): (React as any).ReactElement => {
  const getStatusColor = (status: string): void => {
    // @ts-ignore
    switch ((status as any).toLowerCase( as any)) {
      case 'good':
      case 'جيد':
        return 'bg-green-100 text-green-800'
      case 'warning':
      case 'تحذير':
        return 'bg-yellow-100 text-yellow-800'
      case 'critical':
      case 'حرج':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string): void => {
    if (language === 'ar') {
      // @ts-ignore
      switch ((status as any).toLowerCase( as any)) {
        case 'good': return 'جيد'
        case 'warning': return 'تحذير'
        case 'critical': return 'حرج'
        default: return 'لا توجد بيانات'
      }
    }
    return status
  }

  return (
    <Badge className={getStatusColor(status as any)}>
      {getStatusText(status as any)}
    </Badge>
  )
}

// Trend icon component
// @ts-ignore
const TrendIcon = ({ trend }: { trend: string }): (React as any).ReactElement => {
  // @ts-ignore
  switch (trend?.toLowerCase( as any)) {
    case 'up':
    case 'increasing':
      return <TrendingUp className="h-4 w-4 text-green-500" />
    case 'down':
    case 'decreasing':
      return <TrendingDown className="h-4 w-4 text-red-500" />
    default:
      return <Activity className="h-4 w-4 text-gray-500" />
  }
}

// Metric type icon component
// @ts-ignore
const MetricTypeIcon = ({ type }: { type: string }): (React as any).ReactElement => {
  // @ts-ignore
  switch (type?.toLowerCase( as any)) {
    case 'financial':
    case 'مالي':
      return <DollarSign className="h-4 w-4 text-green-500" />
    case 'percentage':
    case 'نسبة':
      return <Percent className="h-4 w-4 text-blue-500" />
    case 'time':
    case 'وقت':
      return <Clock className="h-4 w-4 text-purple-500" />
    case 'hr':
    case 'موارد بشرية':
      return <User className="h-4 w-4 text-orange-500" />
    case 'operational':
    case 'تشغيلي':
      return <Building className="h-4 w-4 text-indigo-500" />
    default:
      return <BarChart3 className="h-4 w-4 text-gray-500" />
  }
}

// @ts-ignore
export default function ModernKPIManagement({ language }: KPIManagementProps as any): (React as any).ReactElement {
  const [showModal, setShowModal] = useState(false as any)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: rawKpis,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<KPIMetric>({
    service: kpiService,
    autoLoad: true,
    pageSize: 20,
    entityType: 'kpi',
    enableInvalidation: true
  })

  // Transform KPI data to handle string-to-number conversion for target values
  // @ts-ignore
  const kpis = useMemo(( as any) => {
    return (rawKpis as any).map(kpi => {
      // Convert string target_value to number if needed
      const transformedKpi = {
        ...kpi,
        target_value: (kpi as any as any).target_value !== null && (kpi as any).target_value !== undefined ?
          (typeof (kpi as any).target_value === 'string' ? parseFloat((kpi as any as any).target_value) : (kpi as any).target_value) :
          null,
        warning_threshold: (kpi as any).warning_threshold !== null && (kpi as any).warning_threshold !== undefined ?
          (typeof (kpi as any).warning_threshold === 'string' ? parseFloat((kpi as any as any).warning_threshold) : (kpi as any).warning_threshold) :
          null,
        critical_threshold: (kpi as any).critical_threshold !== null && (kpi as any).critical_threshold !== undefined ?
          (typeof (kpi as any).critical_threshold === 'string' ? parseFloat((kpi as any as any).critical_threshold) : (kpi as any).critical_threshold) :
          null,
        current_value: (kpi as any).current_value !== null && (kpi as any).current_value !== undefined ?
          (typeof (kpi as any).current_value === 'string' ? parseFloat((kpi as any as any).current_value) : (kpi as any).current_value) :
          null
      }

      (console as any).log('🔧 KPI Data Transformation:', {
        id: (kpi as any as any).id,
        name: (kpi as any).name,
        original_target: (kpi as any).target_value,
        transformed_target: (transformedKpi as any).target_value,
        target_type: typeof (transformedKpi as any).target_value
      })

      return transformedKpi
    })
  // @ts-ignore
  }, [rawKpis])

  // Table columns configuration
  const columns: TableColumn<KPIMetric>[] = [
    {
      key: 'name',
      label: (t as any).name,
      sortable: true,
      render: (kpi) => (
        <div className="flex items-center space-x-2 rtl:space-x-reverse">
          <MetricTypeIcon type={(kpi as any).metric_type} />
          <div>
            <div className="font-medium">
              {language === 'ar' ? ((kpi as any).name_ar || (kpi as any).name) : (kpi as any).name}
            </div>
            <div className="text-sm text-gray-500">
              {(kpi as any).frequency} • {(kpi as any).created_by_name}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'metric_type',
      label: (t as any).metricType,
      sortable: true,
      render: (kpi) => (
        <Badge variant="outline">
          {language === 'ar' ? 
            ((kpi as any).metric_type === 'FINANCIAL' ? 'مالي' : 
             (kpi as any).metric_type === 'EMPLOYEE' ? 'موظفين' : 
             (kpi as any).metric_type === 'OPERATIONAL' ? 'تشغيلي' : (kpi as any).metric_type) 
            : (kpi as any).metric_type}
        </Badge>
      )
    },
    {
      key: 'current_value',
      label: (t as any).currentValue,
      sortable: true,
      render: (kpi) => (
        <div className="text-right rtl:text-left">
          {(kpi as any).current_value !== null ? 
            formatKPIValue((kpi as any as any).current_value, (kpi as any).unit, { language, precision: 2 }) : 
            <span className="text-gray-400">-</span>
          }
        </div>
      )
    },
    {
      key: 'target_value',
      label: (t as any).targetValue,
      sortable: true,
      render: (kpi) => {
        // Check if target value exists and is not zero (handle both string and number types)
        const hasValidTarget = (kpi as any).target_value !== null &&
                              (kpi as any).target_value !== undefined &&
                              (kpi as any).target_value !== 0 &&
                              (kpi as any).target_value !== '0' &&
                              (kpi as any).target_value !== '(0 as any).00'

        return (
          <div className="text-right rtl:text-left text-sm text-gray-600">
            {hasValidTarget ?
              formatKPIValue((kpi as any as any).target_value, (kpi as any).unit, { language, precision: 1 }) :
              <span className="text-gray-400">--</span>
            }
          </div>
        )
      }
    },
    {
      key: 'status',
      label: (t as any).status,
      sortable: true,
      render: (kpi) => <StatusBadge status={(kpi as any).status || 'no data'} language={language} />
    },
    {
      key: 'trend',
      label: (t as any).trend,
      render: (kpi) => (
        <div className="flex items-center justify-center">
          <TrendIcon trend={(kpi as any).trend || 'stable'} />
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction<KPIMetric>[] = [
    {
      label: (t as any).viewKPI,
      icon: Eye,
      onClick: (kpi) => {
        (console as any).log('👁️ Viewing KPI:', (kpi as any as any).id, (kpi as any).name)
        selectItem(kpi as any)
        setModalMode('view' as any)
        setShowModal(true as any)
      }
    },
    {
      label: (t as any).editKPI,
      icon: Edit,
      onClick: (kpi) => {
        (console as any).log('✏️ Editing KPI:', (kpi as any as any).id, (kpi as any).name)
        selectItem(kpi as any)
        setModalMode('edit' as any)
        setShowModal(true as any)
      }
    },
    {
      label: (t as any).deleteKPI,
      icon: Trash2,
      onClick: async (kpi) => {
        const confirmMessage = language === 'ar'
          ? `هل أنت متأكد من حذف المؤشر "${(kpi as any).name_ar || (kpi as any).name}"؟`
          : `Are you sure you want to delete "${(kpi as any).name}"?`

        if (confirm(confirmMessage as any)) {
          try {
            (console as any).log('🗑️ Deleting KPI:', (kpi as any as any).id, (kpi as any).name)
            await deleteItem((kpi as any as any).id)
            (console as any).log('✅ KPI deleted successfully' as any)
          } catch (error) {
            (console as any).error('❌ Error deleting KPI:', error as any)
          }
        }
      },
      variant: 'destructive'
    }
  ]

  // Filter options configuration
  const filterOptions: FilterOption[] = [
    {
      key: 'metric_type',
      label: (t as any).filterByType,
      options: [
        { label: (t as any).allTypes, value: '' },
        { label: (t as any).financial, value: 'FINANCIAL' },
        { label: (t as any).hr, value: 'EMPLOYEE' },
        { label: (t as any).operational, value: 'OPERATIONAL' },
        { label: (t as any).customer, value: 'CUSTOMER' },
        { label: (t as any).strategic, value: 'STRATEGIC' }
      ]
    },
    {
      key: 'status',
      label: (t as any).filterByStatus,
      options: [
        { label: (t as any).allStatuses, value: '' },
        { label: (t as any).good, value: 'good' },
        { label: (t as any).warning, value: 'warning' },
        { label: (t as any).critical, value: 'critical' },
        { label: (t as any).noData, value: 'no_data' }
      ]
    },
    {
      key: 'frequency',
      label: (t as any).filterByFrequency,
      options: [
        { label: (t as any).allFrequencies, value: '' },
        { label: (t as any).daily, value: 'DAILY' },
        { label: (t as any).weekly, value: 'WEEKLY' },
        { label: (t as any).monthly, value: 'MONTHLY' },
        { label: (t as any).quarterly, value: 'QUARTERLY' },
        { label: (t as any).yearly, value: 'YEARLY' }
      ]
    },
    {
      key: 'is_active',
      label: 'Status',
      options: [
        { label: language === 'ar' ? 'الكل' : 'All', value: '' },
        { label: (t as any).active, value: 'true' },
        { label: (t as any).inactive, value: 'false' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'name',
      label: (t as any).name,
      type: 'text',
      required: true,
      placeholder: language === 'ar' ? 'أدخل اسم المؤشر' : 'Enter KPI name'
    },
    {
      name: 'name_ar',
      label: (t as any).nameAr,
      type: 'text',
      required: true,
      placeholder: language === 'ar' ? 'أدخل الاسم بالعربية' : 'Enter Arabic name'
    },
    {
      name: 'description',
      label: (t as any).description,
      type: 'textarea',
      placeholder: language === 'ar' ? 'أدخل وصف المؤشر' : 'Enter KPI description'
    },
    {
      name: 'description_ar',
      label: (t as any).descriptionAr,
      type: 'textarea',
      placeholder: language === 'ar' ? 'أدخل الوصف بالعربية' : 'Enter Arabic description'
    },
    {
      name: 'metric_type',
      label: (t as any).metricType,
      type: 'select',
      required: true,
      options: [
        { label: (t as any).financial, value: 'FINANCIAL' },
        { label: (t as any).hr, value: 'EMPLOYEE' },
        { label: (t as any).operational, value: 'OPERATIONAL' },
        { label: (t as any).customer, value: 'CUSTOMER' },
        { label: language === 'ar' ? 'أصول' : 'Asset', value: 'ASSET' },
        { label: language === 'ar' ? 'مخصص' : 'Custom', value: 'CUSTOM' }
      ]
    },
    {
      name: 'calculation_method',
      label: (t as any).calculationMethod,
      type: 'select',
      required: true,
      options: [
        { label: 'SUM', value: 'SUM' },
        { label: 'AVERAGE', value: 'AVERAGE' },
        { label: 'COUNT', value: 'COUNT' },
        { label: 'PERCENTAGE', value: 'PERCENTAGE' },
        { label: 'RATIO', value: 'RATIO' },
        { label: 'CUSTOM_FORMULA', value: 'CUSTOM_FORMULA' }
      ]
    },
    {
      name: 'target_value',
      label: (t as any).targetValue,
      type: 'number',
      // @ts-ignore
      step: (0 as any).01,
      placeholder: language === 'ar' ? 'أدخل القيمة المستهدفة' : 'Enter target value'
    },
    {
      name: 'warning_threshold',
      label: (t as any).warningThreshold,
      type: 'number',
      // @ts-ignore
      step: (0 as any).01,
      placeholder: language === 'ar' ? 'أدخل عتبة التحذير' : 'Enter warning threshold'
    },
    {
      name: 'critical_threshold',
      label: (t as any).criticalThreshold,
      type: 'number',
      // @ts-ignore
      step: (0 as any).01,
      placeholder: language === 'ar' ? 'أدخل العتبة الحرجة' : 'Enter critical threshold'
    },
    {
      name: 'unit',
      label: (t as any).unit,
      type: 'select',
      options: [
        { label: '%', value: '%' },
        { label: 'SAR', value: 'SAR' },
        { label: 'USD', value: 'USD' },
        { label: language === 'ar' ? 'ساعات' : 'Hours', value: 'hours' },
        { label: language === 'ar' ? 'أيام' : 'Days', value: 'days' },
        { label: '/5', value: '/5' },
        { label: '/10', value: '/10' },
        { label: language === 'ar' ? 'عدد' : 'Count', value: 'count' }
      ]
    },
    {
      name: 'frequency',
      label: (t as any).frequency,
      type: 'select',
      required: true,
      options: [
        { label: (t as any).daily, value: 'DAILY' },
        { label: (t as any).weekly, value: 'WEEKLY' },
        { label: (t as any).monthly, value: 'MONTHLY' },
        { label: (t as any).quarterly, value: 'QUARTERLY' },
        { label: (t as any).yearly, value: 'YEARLY' }
      ]
    },
    {
      name: 'is_higher_better',
      label: (t as any).isHigherBetter,
      type: 'checkbox',
      defaultValue: true
    },
    {
      name: 'is_active',
      label: (t as any).isActive,
      type: 'checkbox',
      defaultValue: true
    }
  ]

  // Handle modal close
  const handleModalClose = (): void => {
    setShowModal(false as any)
    selectItem(null as any)
    // @ts-ignore
    clearError( as any)
  }

  // Handle form submit
  const handleSubmit = async (data: Partial<KPIMetric>) => {
    try {
      (console as any).log('🔧 KPI Management: Form submission started', { modalMode, data } as any)

      // Validate required fields
      if (!(data as any).name || !(data as any).name_ar || !(data as any).metric_type || !(data as any).frequency) {
        throw new Error('Required fields are missing' as any)
      }

      // Process numeric fields
      const processedData = {
        ...data,
        target_value: (data as any).target_value ? Number((data as any as any).target_value) : null,
        warning_threshold: (data as any).warning_threshold ? Number((data as any as any).warning_threshold) : null,
        critical_threshold: (data as any).critical_threshold ? Number((data as any as any).critical_threshold) : null,
        is_active: (data as any).is_active !== false, // Default to true
        is_higher_better: (data as any).is_higher_better !== false // Default to true
      }

      if (modalMode === 'create') {
        (console as any).log('📝 Creating new KPI with processed data:', processedData as any)
        const result = await createItem(processedData as any)
        (console as any).log('✅ KPI created successfully:', result as any)
      } else if (modalMode === 'edit' && selectedItem) {
        (console as any).log('✏️ Updating KPI:', (selectedItem as any as any).id, 'with processed data:', processedData)
        const result = await updateItem((selectedItem as any as any).id, processedData)
        (console as any).log('✅ KPI updated successfully:', result as any)
      }

      // @ts-ignore
      handleModalClose( as any)
      (console as any).log('🎉 KPI operation completed successfully' as any)
    } catch (error) {
      (console as any).error('❌ Error saving KPI:', error as any)
      // Error will be displayed by CrudModal, don't close modal
      throw error // Re-throw to let CrudModal handle the error display
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">{(t as any).title}</h1>
          <p className="text-gray-600 mt-2">{(t as any).subtitle}</p>
        </div>
      </div>

      {/* CRUD Table */}
      <CrudTable
        title={(t as any).title}
        data={kpis}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={(t as any).search}
        language={language}
        onCreate={() => {
          setModalMode('create' as any)
          setShowModal(true as any)
        }}
        onRefresh={refresh}
        onExport={exportData}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
        createButtonText={(t as any).addKPI}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        mode={modalMode}
        title={
          modalMode === 'create' ? (t as any).addKPI :
          modalMode === 'edit' ? (t as any).editKPI : (t as any).viewKPI
        }
        fields={formFields}
        initialData={selectedItem}
        onSave={handleSubmit}
        loading={creating || updating}
        language={language}
      />
    </div>
  )
}
