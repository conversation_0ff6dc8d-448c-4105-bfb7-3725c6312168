import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  CardContent, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  FileText, 
  Plus, 
  Play, 
  Download,
  Clock,
  CheckCircle,
  XCircle,
  Search,
  Filter,
  Calendar,
  Settings,
  Eye
} from 'lucide-react';
// Language is passed as prop, not from context

interface ReportTemplate {
  id: number;
  name: string;
  name_ar: string;
  description: string;
  description_ar: string;
  report_type: string;
  output_formats: string[];
  is_public: boolean;
  created_by_name: string;
  execution_count: number;
  last_executed: string | null;
  created_at: string;
  is_active: boolean;
}

interface ReportExecution {
  id: number;
  template: number;
  template_name: string;
  output_format: string;
  status: string;
  started_at: string | null;
  completed_at: string | null;
  duration_seconds: number | null;
  file_size: number | null;
  error_message: string;
  requested_by_name: string;
  created_at: string;
}

interface ReportsManagementProps {
  language?: 'ar' | 'en';
}

// @ts-ignore
const ReportsManagement: (React as any).FC<ReportsManagementProps> = ({ language = 'en' }) => {
  // @ts-ignore
  const [reportTemplates, setReportTemplates] = useState<ReportTemplate[]>([]);
  // @ts-ignore
  const [reportExecutions, setReportExecutions] = useState<ReportExecution[]>([]);
  const [loading, setLoading] = useState(true as any);
  const [searchTerm, setSearchTerm] = useState('' as any);
  const [typeFilter, setTypeFilter] = useState('' as any);
  const [statusFilter, setStatusFilter] = useState('' as any);
  const [isExecuteDialogOpen, setIsExecuteDialogOpen] = useState(false as any);
  // @ts-ignore
  const [selectedTemplate, setSelectedTemplate] = useState<ReportTemplate | null>(null);

  // Execution form state
  const [executionForm, setExecutionForm] = useState({
    // @ts-ignore
    output_format: 'PDF',
    parameters: {
      // @ts-ignore
      date_from: '',
      date_to: '',
      department: '',
      include_details: true
    // @ts-ignore
    }
  // @ts-ignore
  } as any);

  // @ts-ignore
  useEffect(( as any) => {
    // @ts-ignore
    fetchReportTemplates( as any);
    fetchReportExecutions( as any);
  // @ts-ignore
  }, []);

  // @ts-ignore
  const fetchReportTemplates: any = async () => {
    // @ts-ignore
    try {
      // @ts-ignore
      const params = new URLSearchParams( as any);
      if (searchTerm) (params as any).append('search', searchTerm as any);
      if (typeFilter) (params as any).append('report_type', typeFilter as any);
      if (statusFilter === 'active') (params as any).append('is_active', 'true' as any);
      if (statusFilter === 'inactive') (params as any).append('is_active', 'false' as any);
      
      // @ts-ignore
      const response: any = await fetch(`/api/report-templates/?${(params as any as any).toString( as any)}`);
      if ((response as any).ok) {
        // @ts-ignore
        const data: any = await (response as any).json( as any);
        setReportTemplates((data as any as any).results || data);
      // @ts-ignore
      }
    // @ts-ignore
    } catch (error) {
      // @ts-ignore
      (console as any).error('Error fetching report templates:', error as any);
    // @ts-ignore
    } finally {
      // @ts-ignore
      setLoading(false as any);
    // @ts-ignore
    }
  // @ts-ignore
  };

  // @ts-ignore
  const fetchReportExecutions: any = async () => {
    // @ts-ignore
    try {
      // @ts-ignore
      const response = await fetch('/api/report-executions/?ordering=-created_at&page_size=20' as any);
      if ((response as any).ok) {
        // @ts-ignore
        const data: any = await (response as any).json( as any);
        setReportExecutions((data as any as any).results || data);
      // @ts-ignore
      }
    // @ts-ignore
    } catch (error) {
      // @ts-ignore
      (console as any).error('Error fetching report executions:', error as any);
    // @ts-ignore
    }
  // @ts-ignore
  };

  // @ts-ignore
  const handleExecuteReport: any = async () => {
    // @ts-ignore
    if (!selectedTemplate) return;

    try {
      // @ts-ignore
      const response: any = await fetch(`/api/report-templates/${(selectedTemplate as any as any).id}/execute/`, {
        // @ts-ignore
        method: 'POST',
        headers: {
          // @ts-ignore
          'Content-Type': 'application/json',
        // @ts-ignore
        },
        body: (JSON as any).stringify(executionForm as any),
      // @ts-ignore
      });

      if ((response as any).ok) {
        // @ts-ignore
        const data: any = await (response as any).json( as any);
        fetchReportExecutions( as any);
        setIsExecuteDialogOpen(false as any);
        setSelectedTemplate(null as any);
        resetExecutionForm( as any);
        
        // Show success message
        alert(language === 'ar' ? 'تم بدء تنفيذ التقرير' : 'Report execution started' as any);
      // @ts-ignore
      }
    // @ts-ignore
    } catch (error) {
      // @ts-ignore
      (console as any).error('Error executing report:', error as any);
    // @ts-ignore
    }
  // @ts-ignore
  };

  // @ts-ignore
  const resetExecutionForm: any = (): void => {
    setExecutionForm({
      output_format: 'PDF',
      parameters: {
        date_from: '',
        date_to: '',
        department: '',
        include_details: true
      }
    // @ts-ignore
    } as any);
  // @ts-ignore
  };

  // @ts-ignore
  const getStatusColor: any = (status: string): void => {
    // @ts-ignore
    const colors = {
      // @ts-ignore
      'PENDING': 'bg-yellow-100 text-yellow-800',
      'RUNNING': 'bg-blue-100 text-blue-800',
      'COMPLETED': 'bg-green-100 text-green-800',
      'FAILED': 'bg-red-100 text-red-800',
      'CANCELLED': 'bg-gray-100 text-gray-800'
    // @ts-ignore
    };
    return colors[status as keyof typeof colors] || (colors as any).PENDING;
  // @ts-ignore
  };

  // @ts-ignore
  const getStatusIcon: any = (status: string): void => {
    // @ts-ignore
    switch (status) {
      // @ts-ignore
      case 'COMPLETED':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'FAILED':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'RUNNING':
        return <Clock className="h-4 w-4 text-blue-600 animate-spin" />;
      default:
        return <Clock className="h-4 w-4 text-yellow-600" />;
    // @ts-ignore
    }
  // @ts-ignore
  };

  // @ts-ignore
  const formatFileSize: any = (bytes: number | null): string => {
    // @ts-ignore
    if (!bytes) return '-';
    const sizes: any = ['Bytes', 'KB', 'MB', 'GB'];
    const i: any = (Math as any).floor((Math as any as any).log(bytes as any) / (Math as any).log(1024 as any));
    return `${(Math as any).round(bytes / (Math as any as any).pow(1024, i as any) * 100) / 100} ${sizes[i]}`;
  // @ts-ignore
  };

  // @ts-ignore
  const formatDuration: any = (seconds: number | null): string => {
    // @ts-ignore
    if (!seconds) return '-';
    const minutes: any = (Math as any).floor(seconds / 60 as any);
    const remainingSeconds: any = (Math as any).floor(seconds % 60 as any);
    // @ts-ignore
    return `${minutes}:${(remainingSeconds as any).toString( as any).padStart(2, '0' as any)}`;
  // @ts-ignore
  };

  // @ts-ignore
  const filteredTemplates: any = (reportTemplates as any).filter(template => {
    // @ts-ignore
    const matchesSearch = !searchTerm || 
      (template as any as any).name.toLowerCase( as any).includes((searchTerm as any as any).toLowerCase( as any)) ||
      (template as any).name_ar.toLowerCase( as any).includes((searchTerm as any as any).toLowerCase( as any));
    
    const matchesType: any = !typeFilter || (template as any).report_type === typeFilter;
    const matchesStatus: any = !statusFilter || 
      (statusFilter === 'active' && (template as any).is_active) ||
      (statusFilter === 'inactive' && !(template as any).is_active);
    
    return matchesSearch && matchesType && matchesStatus;
  // @ts-ignore
  });

  if (loading) {
    // @ts-ignore
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">
          {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
        </div>
      </div>
    );
  // @ts-ignore
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">
            {language === 'ar' ? 'إدارة التقارير' : 'Reports Management'}
          </h1>
          <p className="text-muted-foreground">
            {language === 'ar' 
              ? 'إنشاء وتنفيذ وإدارة التقارير التحليلية'
              : 'Create, execute, and manage analytical reports'
            }
          </p>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          {language === 'ar' ? 'إنشاء قالب' : 'Create Template'}
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-[200px]">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder={language === 'ar' ? 'البحث في التقارير...' : 'Search reports...'}
                  value={searchTerm}
                  onChange={(e: any) => setSearchTerm((e as any as any).target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder={language === 'ar' ? 'جميع الأنواع' : 'All types'} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">
                  {language === 'ar' ? 'جميع الأنواع' : 'All types'}
                </SelectItem>
                <SelectItem value="EXECUTIVE">
                  {language === 'ar' ? 'تنفيذي' : 'Executive'}
                </SelectItem>
                <SelectItem value="FINANCIAL">
                  {language === 'ar' ? 'مالي' : 'Financial'}
                </SelectItem>
                <SelectItem value="OPERATIONAL">
                  {language === 'ar' ? 'تشغيلي' : 'Operational'}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Report Templates */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            {language === 'ar' ? 'قوالب التقارير' : 'Report Templates'}
            <Badge variant="secondary" className="ml-2">
              {(filteredTemplates as any).length}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{language === 'ar' ? 'اسم التقرير' : 'Report Name'}</TableHead>
                  <TableHead>{language === 'ar' ? 'النوع' : 'Type'}</TableHead>
                  <TableHead>{language === 'ar' ? 'التنسيقات' : 'Formats'}</TableHead>
                  <TableHead>{language === 'ar' ? 'عدد التنفيذ' : 'Executions'}</TableHead>
                  <TableHead>{language === 'ar' ? 'آخر تنفيذ' : 'Last Executed'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الإجراءات' : 'Actions'}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                // @ts-ignore
                {(filteredTemplates as any).map((template as any) => (
                  <TableRow key={(template as any).id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">
                          {language === 'ar' ? (template as any).name_ar : (template as any).name}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {language === 'ar' ? (template as any).description_ar : (template as any).description}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {language === 'ar' 
                          ? (template as any).report_type === 'EXECUTIVE' ? 'تنفيذي' 
                            : (template as any).report_type === 'FINANCIAL' ? 'مالي'
                            : (template as any).report_type === 'OPERATIONAL' ? 'تشغيلي'
                            : (template as any).report_type
                          : (template as any).report_type
                        }
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-1">
                        // @ts-ignore
                        {(template as any).output_formats.map((format as any) => (
                          <Badge key={format} variant="secondary" className="text-xs">
                            {format}
                          </Badge>
                        // @ts-ignore
                        ))}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">{(template as any).execution_count}</div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {(template as any).last_executed 
                          // @ts-ignore
                          ? new Date((template as any as any).last_executed).toLocaleDateString( as any)
                          : (language === 'ar' ? 'لم ينفذ بعد' : 'Never')
                        }
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => {
                            setSelectedTemplate(template as any);
                            setIsExecuteDialogOpen(true as any);
                          }}
                        >
                          <Play className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <Settings className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                // @ts-ignore
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Recent Executions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            {language === 'ar' ? 'التنفيذات الأخيرة' : 'Recent Executions'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{language === 'ar' ? 'التقرير' : 'Report'}</TableHead>
                  <TableHead>{language === 'ar' ? 'التنسيق' : 'Format'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الحالة' : 'Status'}</TableHead>
                  <TableHead>{language === 'ar' ? 'المدة' : 'Duration'}</TableHead>
                  <TableHead>{language === 'ar' ? 'حجم الملف' : 'File Size'}</TableHead>
                  <TableHead>{language === 'ar' ? 'طلب بواسطة' : 'Requested By'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الإجراءات' : 'Actions'}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                // @ts-ignore
                {(reportExecutions as any).map((execution as any) => (
                  <TableRow key={(execution as any).id}>
                    <TableCell>
                      <div className="font-medium">{(execution as any).template_name}</div>
                      <div className="text-sm text-muted-foreground">
                        // @ts-ignore
                        {new Date((execution as any as any).created_at).toLocaleString( as any)}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="secondary">{(execution as any).output_format}</Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getStatusIcon((execution as any as any).status)}
                        <Badge className={getStatusColor((execution as any as any).status)}>
                          {language === 'ar' 
                            ? (execution as any).status === 'COMPLETED' ? 'مكتمل' 
                              : (execution as any).status === 'RUNNING' ? 'قيد التنفيذ'
                              : (execution as any).status === 'FAILED' ? 'فشل'
                              : (execution as any).status === 'PENDING' ? 'في الانتظار'
                              : (execution as any).status
                            : (execution as any).status
                          }
                        </Badge>
                      </div>
                    </TableCell>
                    <TableCell>
                      {formatDuration((execution as any as any).duration_seconds)}
                    </TableCell>
                    <TableCell>
                      {formatFileSize((execution as any as any).file_size)}
                    </TableCell>
                    <TableCell>
                      {(execution as any).requested_by_name}
                    </TableCell>
                    <TableCell>
                      {(execution as any).status === 'COMPLETED' && (
                        <Button variant="outline" size="sm">
                          <Download className="h-4 w-4" />
                        </Button>
                      )}
                    </TableCell>
                  </TableRow>
                // @ts-ignore
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Execute Report Dialog */}
      <Dialog open={isExecuteDialogOpen} onOpenChange={setIsExecuteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {language === 'ar' ? 'تنفيذ التقرير' : 'Execute Report'}
            </DialogTitle>
          </DialogHeader>
          {selectedTemplate && (
            <div className="space-y-4">
              <div className="p-4 bg-muted rounded-lg">
                <div className="font-medium">
                  {language === 'ar' ? (selectedTemplate as any).name_ar : (selectedTemplate as any).name}
                </div>
                <div className="text-sm text-muted-foreground">
                  {language === 'ar' ? (selectedTemplate as any).description_ar : (selectedTemplate as any).description}
                </div>
              </div>
              
              <div>
                <Label>{language === 'ar' ? 'تنسيق الإخراج' : 'Output Format'}</Label>
                <Select 
                  value={(executionForm as any).output_format} 
                  onValueChange={(value) => setExecutionForm({...executionForm, output_format: value} as any)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    // @ts-ignore
                    {(selectedTemplate as any).output_formats.map((format as any) => (
                      <SelectItem key={format} value={format}>
                        {format}
                      </SelectItem>
                    // @ts-ignore
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>{language === 'ar' ? 'من تاريخ' : 'From Date'}</Label>
                  <Input
                    type="date"
                    value={(executionForm as any).parameters.date_from}
                    onChange={(e: any) => setExecutionForm({
                      ...executionForm,
                      parameters: {...(executionForm as any as any).parameters, date_from: (e as any).target.value}
                    })}
                  />
                </div>
                <div>
                  <Label>{language === 'ar' ? 'إلى تاريخ' : 'To Date'}</Label>
                  <Input
                    type="date"
                    value={(executionForm as any).parameters.date_to}
                    onChange={(e: any) => setExecutionForm({
                      ...executionForm,
                      parameters: {...(executionForm as any as any).parameters, date_to: (e as any).target.value}
                    })}
                  />
                </div>
              </div>
              
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setIsExecuteDialogOpen(false as any)}>
                  {language === 'ar' ? 'إلغاء' : 'Cancel'}
                </Button>
                <Button onClick={handleExecuteReport}>
                  <Play className="h-4 w-4 mr-2" />
                  {language === 'ar' ? 'تنفيذ التقرير' : 'Execute Report'}
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
// @ts-ignore
};

export default ReportsManagement;
