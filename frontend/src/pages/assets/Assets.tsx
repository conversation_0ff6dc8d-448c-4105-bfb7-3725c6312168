import { useState } from 'react'
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Package,
  Plus,
  Search,
  Filter,
  Download,
  QrCode,
  MapPin,
  Calendar,
  User,
  Alert<PERSON>riangle,
  CheckCircle,
  Clock,
  Wrench,
  Eye,
  Edit,
  Trash2,
  BarChart3,
  TrendingUp,
  DollarSign
} from 'lucide-react'

interface AssetsProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    assets: 'الأصول',
    addAsset: 'إضافة أصل',
    assetOverview: 'نظرة عامة على الأصول',
    search: 'بحث',
    filter: 'تصفية',
    export: 'تصدير',
    scanQR: 'مسح QR',
    assetId: 'معرف الأصل',
    assetName: 'اسم الأصل',
    category: 'الفئة',
    location: 'الموقع',
    assignedTo: 'مكلف إلى',
    status: 'الحالة',
    purchaseDate: 'تاريخ الشراء',
    purchasePrice: 'سعر الشراء',
    currentValue: 'القيمة الحالية',
    warrantyExpiry: 'انتهاء الضمان',
    actions: 'الإجراءات',
    available: 'متاح',
    inUse: 'قيد الاستخدام',
    maintenance: 'صيانة',
    retired: 'متقاعد',
    lost: 'مفقود',
    damaged: 'تالف',
    viewDetails: 'عرض التفاصيل',
    editAsset: 'تعديل الأصل',
    deleteAsset: 'حذف الأصل',
    totalAssets: 'إجمالي الأصول',
    availableAssets: 'الأصول المتاحة',
    assetsInUse: 'الأصول قيد الاستخدام',
    maintenanceRequired: 'تحتاج صيانة',
    totalValue: 'القيمة الإجمالية',
    depreciation: 'الاستهلاك',
    computers: 'أجهزة كمبيوتر',
    furniture: 'أثاث',
    vehicles: 'مركبات',
    equipment: 'معدات',
    software: 'برمجيات',
    other: 'أخرى',
    serialNumber: 'الرقم التسلسلي',
    manufacturer: 'الشركة المصنعة',
    model: 'الطراز',
    notes: 'ملاحظات',
    assetHistory: 'تاريخ الأصل',
    maintenanceHistory: 'تاريخ الصيانة',
    assignmentHistory: 'تاريخ التكليف',
    generateReport: 'إنشاء تقرير',
    bulkActions: 'إجراءات مجمعة',
    assetTracking: 'تتبع الأصول',
    locationTracking: 'تتبع الموقع',
    warrantyAlert: 'تنبيه الضمان',
    depreciationReport: 'تقرير الاستهلاك'
  },
  en: {
    assets: 'Assets',
    addAsset: 'Add Asset',
    assetOverview: 'Asset Overview',
    search: 'Search',
    filter: 'Filter',
    export: 'Export',
    scanQR: 'Scan QR',
    assetId: 'Asset ID',
    assetName: 'Asset Name',
    category: 'Category',
    location: 'Location',
    assignedTo: 'Assigned To',
    status: 'Status',
    purchaseDate: 'Purchase Date',
    purchasePrice: 'Purchase Price',
    currentValue: 'Current Value',
    warrantyExpiry: 'Warranty Expiry',
    actions: 'Actions',
    available: 'Available',
    inUse: 'In Use',
    maintenance: 'Maintenance',
    retired: 'Retired',
    lost: 'Lost',
    damaged: 'Damaged',
    viewDetails: 'View Details',
    editAsset: 'Edit Asset',
    deleteAsset: 'Delete Asset',
    totalAssets: 'Total Assets',
    availableAssets: 'Available Assets',
    assetsInUse: 'Assets In Use',
    maintenanceRequired: 'Maintenance Required',
    totalValue: 'Total Value',
    depreciation: 'Depreciation',
    computers: 'Computers',
    furniture: 'Furniture',
    vehicles: 'Vehicles',
    equipment: 'Equipment',
    software: 'Software',
    other: 'Other',
    serialNumber: 'Serial Number',
    manufacturer: 'Manufacturer',
    model: 'Model',
    notes: 'Notes',
    assetHistory: 'Asset History',
    maintenanceHistory: 'Maintenance History',
    assignmentHistory: 'Assignment History',
    generateReport: 'Generate Report',
    bulkActions: 'Bulk Actions',
    assetTracking: 'Asset Tracking',
    locationTracking: 'Location Tracking',
    warrantyAlert: 'Warranty Alert',
    depreciationReport: 'Depreciation Report'
  }
}

export default function Assets({ language }: AssetsProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [selectedStatus, setSelectedStatus] = useState('all')
  const [viewMode, setViewMode] = useState('grid') // grid or table
  const t = translations[language]
  const isRTL = language === 'ar'

  // Mock data
  const assetStats = {
    totalAssets: 1247,
    availableAssets: 892,
    assetsInUse: 298,
    maintenanceRequired: 57,
    totalValue: 2850000,
    depreciation: 15.2
  }

  const assets = [
    {
      id: 1,
      assetId: 'AST-001',
      name: 'MacBook Pro 16"',
      nameAr: 'ماك بوك برو 16 بوصة',
      category: 'computers',
      location: 'مكتب الطابق الثالث',
      assignedTo: 'أحمد محمد',
      status: 'inUse',
      purchaseDate: '2023-06-15',
      purchasePrice: 12000,
      currentValue: 9600,
      warrantyExpiry: '2026-06-15',
      serialNumber: 'MBP2023001',
      manufacturer: 'Apple',
      model: 'MacBook Pro M2',
      notes: 'للتطوير والبرمجة',
      lastMaintenance: '2024-01-10',
      nextMaintenance: '2024-07-10'
    },
    {
      id: 2,
      assetId: 'AST-002',
      name: 'مكتب تنفيذي',
      nameAr: 'مكتب تنفيذي',
      category: 'furniture',
      location: 'مكتب المدير العام',
      assignedTo: 'فاطمة علي',
      status: 'inUse',
      purchaseDate: '2023-03-20',
      purchasePrice: 3500,
      currentValue: 2800,
      warrantyExpiry: '2028-03-20',
      serialNumber: 'DESK2023002',
      manufacturer: 'Office Plus',
      model: 'Executive Desk XL',
      notes: 'مكتب خشبي فاخر',
      lastMaintenance: '2023-12-15',
      nextMaintenance: '2024-06-15'
    },
    {
      id: 3,
      assetId: 'AST-003',
      name: 'طابعة ليزر',
      nameAr: 'طابعة ليزر',
      category: 'equipment',
      location: 'غرفة الطباعة',
      assignedTo: null,
      status: 'maintenance',
      purchaseDate: '2022-11-10',
      purchasePrice: 2200,
      currentValue: 1320,
      warrantyExpiry: '2024-11-10',
      serialNumber: 'LP2022003',
      manufacturer: 'HP',
      model: 'LaserJet Pro 4000',
      notes: 'تحتاج استبدال خرطوشة الحبر',
      lastMaintenance: '2024-01-20',
      nextMaintenance: '2024-02-20'
    },
    {
      id: 4,
      assetId: 'AST-004',
      name: 'سيارة الشركة',
      nameAr: 'سيارة الشركة',
      category: 'vehicles',
      location: 'موقف السيارات',
      assignedTo: 'محمد حسن',
      status: 'available',
      purchaseDate: '2023-01-15',
      purchasePrice: 85000,
      currentValue: 68000,
      warrantyExpiry: '2026-01-15',
      serialNumber: 'CAR2023004',
      manufacturer: 'Toyota',
      model: 'Camry 2023',
      notes: 'للمهام الرسمية',
      lastMaintenance: '2024-01-05',
      nextMaintenance: '2024-04-05'
    }
  ]

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'inUse':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'maintenance':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'retired':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      case 'lost':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'damaged':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'available':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'inUse':
        return <User className="h-4 w-4 text-blue-500" />
      case 'maintenance':
        return <Wrench className="h-4 w-4 text-yellow-500" />
      case 'retired':
        return <Clock className="h-4 w-4 text-gray-500" />
      case 'lost':
        return <AlertTriangle className="h-4 w-4 text-red-500" />
      case 'damaged':
        return <AlertTriangle className="h-4 w-4 text-orange-500" />
      default:
        return <Package className="h-4 w-4 text-gray-500" />
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'computers':
        return '💻'
      case 'furniture':
        return '🪑'
      case 'vehicles':
        return '🚗'
      case 'equipment':
        return '⚙️'
      case 'software':
        return '💿'
      default:
        return '📦'
    }
  }

  const isWarrantyExpiring = (warrantyDate: string) => {
    const expiry = new Date(warrantyDate)
    const today = new Date()
    const diffTime = expiry.getTime() - today.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays <= 90 && diffDays > 0
  }

  const isWarrantyExpired = (warrantyDate: string) => {
    const expiry = new Date(warrantyDate)
    const today = new Date()
    return expiry < today
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">{t.assets}</h1>
          <p className="text-white/70">إدارة ومتابعة أصول الشركة والمعدات</p>
        </div>
        <div className="flex gap-2">
          <Button className="glass-button">
            <Plus className="h-4 w-4 mr-2" />
            {t.addAsset}
          </Button>
          <Button variant="outline" className="glass-button">
            <QrCode className="h-4 w-4 mr-2" />
            {t.scanQR}
          </Button>
          <Button variant="outline" className="glass-button">
            <Download className="h-4 w-4 mr-2" />
            {t.export}
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-6">
        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.totalAssets}</p>
                <p className="text-2xl font-bold text-white">{assetStats.totalAssets.toLocaleString()}</p>
              </div>
              <Package className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.availableAssets}</p>
                <p className="text-2xl font-bold text-green-400">{assetStats.availableAssets.toLocaleString()}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.assetsInUse}</p>
                <p className="text-2xl font-bold text-blue-400">{assetStats.assetsInUse.toLocaleString()}</p>
              </div>
              <User className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.maintenanceRequired}</p>
                <p className="text-2xl font-bold text-yellow-400">{assetStats.maintenanceRequired}</p>
              </div>
              <Wrench className="h-8 w-8 text-yellow-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.totalValue}</p>
                <p className="text-2xl font-bold text-purple-400">{formatCurrency(assetStats.totalValue)}</p>
              </div>
              <DollarSign className="h-8 w-8 text-purple-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.depreciation}</p>
                <p className="text-2xl font-bold text-orange-400">{assetStats.depreciation}%</p>
              </div>
              <TrendingUp className="h-8 w-8 text-orange-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Assets Grid/Table */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <CardTitle className="text-white text-xl">{t.assetOverview}</CardTitle>
              <CardDescription className="text-white/70">
                قائمة بجميع أصول الشركة وحالتها
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 h-4 w-4" />
                <Input
                  placeholder={t.search}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 glass-input"
                />
              </div>
              <Button variant="outline" className="glass-button">
                <Filter className="h-4 w-4" />
              </Button>
              <Button 
                variant="outline" 
                className="glass-button"
                onClick={() => setViewMode(viewMode === 'grid' ? 'table' : 'grid')}
              >
                <BarChart3 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {assets.map((asset) => (
              <Card key={asset.id} className="glass-card border-white/10 hover:border-white/30 transition-all">
                <CardContent className="p-6">
                  <div className="space-y-4">
                    {/* Asset Header */}
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3">
                        <div className="text-2xl">{getCategoryIcon(asset.category)}</div>
                        <div>
                          <h3 className="text-white font-semibold">{asset.name}</h3>
                          <p className="text-white/60 text-sm">{asset.assetId}</p>
                        </div>
                      </div>
                      <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(asset.status)}`}>
                        {getStatusIcon(asset.status)}
                        {t[asset.status as keyof typeof t]}
                      </div>
                    </div>

                    {/* Asset Details */}
                    <div className="space-y-2 text-sm">
                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4 text-blue-400" />
                        <span className="text-white/80">{asset.location}</span>
                      </div>
                      {asset.assignedTo && (
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4 text-green-400" />
                          <span className="text-white/80">{asset.assignedTo}</span>
                        </div>
                      )}
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-purple-400" />
                        <span className="text-white/80">{asset.purchaseDate}</span>
                      </div>
                    </div>

                    {/* Value Information */}
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="text-white/70">سعر الشراء</p>
                        <p className="text-white font-medium">{formatCurrency(asset.purchasePrice)}</p>
                      </div>
                      <div>
                        <p className="text-white/70">القيمة الحالية</p>
                        <p className="text-white font-medium">{formatCurrency(asset.currentValue)}</p>
                      </div>
                    </div>

                    {/* Warranty Status */}
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-white/70">الضمان</span>
                        <span className={`font-medium ${
                          isWarrantyExpired(asset.warrantyExpiry) ? 'text-red-400' :
                          isWarrantyExpiring(asset.warrantyExpiry) ? 'text-yellow-400' : 'text-green-400'
                        }`}>
                          {asset.warrantyExpiry}
                        </span>
                      </div>
                      {isWarrantyExpiring(asset.warrantyExpiry) && (
                        <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-2">
                          <p className="text-yellow-400 text-xs">⚠️ الضمان ينتهي قريباً</p>
                        </div>
                      )}
                      {isWarrantyExpired(asset.warrantyExpiry) && (
                        <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-2">
                          <p className="text-red-400 text-xs">❌ انتهى الضمان</p>
                        </div>
                      )}
                    </div>

                    {/* Actions */}
                    <div className="flex gap-2 pt-2">
                      <Button size="sm" className="glass-button flex-1">
                        <Eye className="h-3 w-3 mr-1" />
                        {t.viewDetails}
                      </Button>
                      <Button size="sm" variant="outline" className="glass-button">
                        <Edit className="h-3 w-3" />
                      </Button>
                      <Button size="sm" variant="outline" className="glass-button text-red-400 hover:bg-red-500/20">
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
