import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  ShoppingCart,
  Plus,
  Search,
  Filter,
  Download,
  Eye,
  Edit,
  Trash2,
  RefreshCw,
  Calendar,
  DollarSign,
  Package,
  Truck,
  CheckCircle,
  Clock,
  AlertTriangle,
  User
} from 'lucide-react'

interface PurchaseOrdersProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    purchaseOrders: 'أوامر الشراء',
    createOrder: 'إنشاء أمر شراء',
    search: 'بحث',
    filter: 'تصفية',
    export: 'تصدير',
    refresh: 'تحديث',
    orderNumber: 'رقم الأمر',
    supplier: 'المورد',
    orderDate: 'تاريخ الأمر',
    deliveryDate: 'تاريخ التسليم',
    totalAmount: 'المبلغ الإجمالي',
    status: 'الحالة',
    actions: 'الإجراءات',
    viewDetails: 'عرض التفاصيل',
    editOrder: 'تعديل الأمر',
    deleteOrder: 'حذف الأمر',
    pending: 'معلق',
    approved: 'معتمد',
    shipped: 'تم الشحن',
    delivered: 'تم التسليم',
    cancelled: 'ملغي',
    totalOrders: 'إجمالي الأوامر',
    pendingOrders: 'أوامر معلقة',
    completedOrders: 'أوامر مكتملة',
    totalValue: 'القيمة الإجمالية',
    items: 'العناصر',
    requestedBy: 'طلب بواسطة',
    approvedBy: 'معتمد بواسطة',
    priority: 'الأولوية',
    high: 'عالي',
    medium: 'متوسط',
    low: 'منخفض',
    urgent: 'عاجل'
  },
  en: {
    purchaseOrders: 'Purchase Orders',
    createOrder: 'Create Order',
    search: 'Search',
    filter: 'Filter',
    export: 'Export',
    refresh: 'Refresh',
    orderNumber: 'Order Number',
    supplier: 'Supplier',
    orderDate: 'Order Date',
    deliveryDate: 'Delivery Date',
    totalAmount: 'Total Amount',
    status: 'Status',
    actions: 'Actions',
    viewDetails: 'View Details',
    editOrder: 'Edit Order',
    deleteOrder: 'Delete Order',
    pending: 'Pending',
    approved: 'Approved',
    shipped: 'Shipped',
    delivered: 'Delivered',
    cancelled: 'Cancelled',
    totalOrders: 'Total Orders',
    pendingOrders: 'Pending Orders',
    completedOrders: 'Completed Orders',
    totalValue: 'Total Value',
    items: 'Items',
    requestedBy: 'Requested By',
    approvedBy: 'Approved By',
    priority: 'Priority',
    high: 'High',
    medium: 'Medium',
    low: 'Low',
    urgent: 'Urgent'
  }
}

export default function PurchaseOrders({ language }: PurchaseOrdersProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [refreshing, setRefreshing] = useState(false)
  const t = translations[language]
  const isRTL = language === 'ar'

  const orderStats = {
    totalOrders: 245,
    pendingOrders: 18,
    completedOrders: 198,
    totalValue: 2850000
  }

  const purchaseOrders = [
    {
      id: 1,
      orderNumber: 'PO-2024-001',
      supplier: 'شركة التقنية المتقدمة',
      orderDate: '2024-01-15',
      deliveryDate: '2024-01-25',
      totalAmount: 45000,
      status: 'approved',
      items: 5,
      requestedBy: 'أحمد محمد',
      approvedBy: 'فاطمة علي',
      priority: 'high'
    },
    {
      id: 2,
      orderNumber: 'PO-2024-002',
      supplier: 'مؤسسة الأثاث الحديث',
      orderDate: '2024-01-18',
      deliveryDate: '2024-02-05',
      totalAmount: 32000,
      status: 'shipped',
      items: 12,
      requestedBy: 'محمد حسن',
      approvedBy: 'سارة أحمد',
      priority: 'medium'
    },
    {
      id: 3,
      orderNumber: 'PO-2024-003',
      supplier: 'شركة الصيانة الشاملة',
      orderDate: '2024-01-20',
      deliveryDate: '2024-01-30',
      totalAmount: 18500,
      status: 'pending',
      items: 3,
      requestedBy: 'علي حسين',
      approvedBy: null,
      priority: 'urgent'
    },
    {
      id: 4,
      orderNumber: 'PO-2024-004',
      supplier: 'شركة المعدات المكتبية',
      orderDate: '2024-01-22',
      deliveryDate: '2024-02-10',
      totalAmount: 25000,
      status: 'delivered',
      items: 8,
      requestedBy: 'نورا خالد',
      approvedBy: 'أحمد محمد',
      priority: 'low'
    }
  ]

  const handleRefresh = async () => {
    setRefreshing(true)
    await new Promise(resolve => setTimeout(resolve, 1000))
    setRefreshing(false)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'approved':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'shipped':
        return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'delivered':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />
      case 'approved':
        return <CheckCircle className="h-4 w-4 text-blue-500" />
      case 'shipped':
        return <Truck className="h-4 w-4 text-purple-500" />
      case 'delivered':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'cancelled':
        return <AlertTriangle className="h-4 w-4 text-red-500" />
      default:
        return <Package className="h-4 w-4 text-gray-500" />
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'border-l-red-500 bg-red-500/10'
      case 'high':
        return 'border-l-orange-500 bg-orange-500/10'
      case 'medium':
        return 'border-l-blue-500 bg-blue-500/10'
      case 'low':
        return 'border-l-green-500 bg-green-500/10'
      default:
        return 'border-l-gray-500 bg-gray-500/10'
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount)
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">{t.purchaseOrders}</h1>
          <p className="text-white/70">إدارة ومتابعة أوامر الشراء والمشتريات</p>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={handleRefresh}
            disabled={refreshing}
            className="glass-button"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            {t.refresh}
          </Button>
          <Button className="glass-button">
            <Plus className="h-4 w-4 mr-2" />
            {t.createOrder}
          </Button>
          <Button variant="outline" className="glass-button">
            <Download className="h-4 w-4 mr-2" />
            {t.export}
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.totalOrders}</p>
                <p className="text-2xl font-bold text-white">{orderStats.totalOrders}</p>
              </div>
              <ShoppingCart className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.pendingOrders}</p>
                <p className="text-2xl font-bold text-yellow-400">{orderStats.pendingOrders}</p>
              </div>
              <Clock className="h-8 w-8 text-yellow-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.completedOrders}</p>
                <p className="text-2xl font-bold text-green-400">{orderStats.completedOrders}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.totalValue}</p>
                <p className="text-2xl font-bold text-purple-400">{formatCurrency(orderStats.totalValue)}</p>
              </div>
              <DollarSign className="h-8 w-8 text-purple-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Purchase Orders List */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <CardTitle className="text-white text-xl">أوامر الشراء</CardTitle>
              <CardDescription className="text-white/70">
                قائمة بجميع أوامر الشراء وحالتها
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 h-4 w-4" />
                <Input
                  placeholder={t.search}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 glass-input"
                />
              </div>
              <Button variant="outline" className="glass-button">
                <Filter className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {purchaseOrders.map((order) => (
              <div key={order.id} className={`p-4 glass-card border-white/10 hover:border-white/30 transition-all border-l-4 ${getPriorityColor(order.priority)}`}>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-3">
                      <h4 className="text-white font-semibold">{order.orderNumber}</h4>
                      <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(order.status)}`}>
                        {getStatusIcon(order.status)}
                        {t[order.status as keyof typeof t]}
                      </div>
                      {order.priority === 'urgent' && (
                        <span className="bg-red-500/20 text-red-400 text-xs px-2 py-1 rounded-full">
                          {t.urgent}
                        </span>
                      )}
                    </div>
                    
                    <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 text-sm text-white/80 mb-3">
                      <div>
                        <span className="text-white/60">المورد: </span>
                        {order.supplier}
                      </div>
                      <div>
                        <span className="text-white/60">تاريخ الأمر: </span>
                        {order.orderDate}
                      </div>
                      <div>
                        <span className="text-white/60">تاريخ التسليم: </span>
                        {order.deliveryDate}
                      </div>
                      <div>
                        <span className="text-white/60">العناصر: </span>
                        {order.items}
                      </div>
                    </div>

                    <div className="grid grid-cols-2 lg:grid-cols-3 gap-4 text-sm text-white/80">
                      <div>
                        <span className="text-white/60">طلب بواسطة: </span>
                        {order.requestedBy}
                      </div>
                      <div>
                        <span className="text-white/60">معتمد بواسطة: </span>
                        {order.approvedBy || 'غير معتمد'}
                      </div>
                      <div>
                        <span className="text-white/60">المبلغ الإجمالي: </span>
                        <span className="text-green-400 font-bold">{formatCurrency(order.totalAmount)}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex gap-2">
                    <Button size="sm" className="glass-button">
                      <Eye className="h-3 w-3 mr-1" />
                      {t.viewDetails}
                    </Button>
                    <Button size="sm" variant="outline" className="glass-button">
                      <Edit className="h-3 w-3" />
                    </Button>
                    <Button size="sm" variant="outline" className="glass-button text-red-400 hover:bg-red-500/20">
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
