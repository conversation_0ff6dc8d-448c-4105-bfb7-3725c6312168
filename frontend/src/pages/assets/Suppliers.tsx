import { useState } from 'react'
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Truck,
  Plus,
  Search,
  Filter,
  Download,
  Phone,
  Mail,
  MapPin,
  Star,
  Eye,
  Edit,
  Trash2,
  RefreshCw,
  Building,
  User,
  Calendar,
  DollarSign
} from 'lucide-react'

interface SuppliersProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    suppliers: 'الموردون',
    addSupplier: 'إضافة مورد',
    search: 'بحث',
    filter: 'تصفية',
    export: 'تصدير',
    refresh: 'تحديث',
    supplierName: 'اسم المورد',
    contactPerson: 'الشخص المسؤول',
    phone: 'الهاتف',
    email: 'البريد الإلكتروني',
    address: 'العنوان',
    category: 'الفئة',
    rating: 'التقييم',
    status: 'الحالة',
    actions: 'الإجراءات',
    viewDetails: 'عرض التفاصيل',
    editSupplier: 'تعديل المورد',
    deleteSupplier: 'حذف المورد',
    active: 'نشط',
    inactive: 'غير نشط',
    pending: 'معلق',
    blocked: 'محظور',
    totalSuppliers: 'إجمالي الموردين',
    activeSuppliers: 'الموردون النشطون',
    newSuppliers: 'موردون جدد',
    topRated: 'الأعلى تقييماً',
    contractValue: 'قيمة العقد',
    lastOrder: 'آخر طلب',
    joinDate: 'تاريخ الانضمام',
    orders: 'الطلبات',
    performance: 'الأداء',
    excellent: 'ممتاز',
    good: 'جيد',
    average: 'متوسط',
    poor: 'ضعيف',
    office: 'مكتبية',
    technology: 'تقنية',
    furniture: 'أثاث',
    maintenance: 'صيانة',
    catering: 'تموين',
    cleaning: 'تنظيف'
  },
  en: {
    suppliers: 'Suppliers',
    addSupplier: 'Add Supplier',
    search: 'Search',
    filter: 'Filter',
    export: 'Export',
    refresh: 'Refresh',
    supplierName: 'Supplier Name',
    contactPerson: 'Contact Person',
    phone: 'Phone',
    email: 'Email',
    address: 'Address',
    category: 'Category',
    rating: 'Rating',
    status: 'Status',
    actions: 'Actions',
    viewDetails: 'View Details',
    editSupplier: 'Edit Supplier',
    deleteSupplier: 'Delete Supplier',
    active: 'Active',
    inactive: 'Inactive',
    pending: 'Pending',
    blocked: 'Blocked',
    totalSuppliers: 'Total Suppliers',
    activeSuppliers: 'Active Suppliers',
    newSuppliers: 'New Suppliers',
    topRated: 'Top Rated',
    contractValue: 'Contract Value',
    lastOrder: 'Last Order',
    joinDate: 'Join Date',
    orders: 'Orders',
    performance: 'Performance',
    excellent: 'Excellent',
    good: 'Good',
    average: 'Average',
    poor: 'Poor',
    office: 'Office',
    technology: 'Technology',
    furniture: 'Furniture',
    maintenance: 'Maintenance',
    catering: 'Catering',
    cleaning: 'Cleaning'
  }
}

export default function Suppliers({ language }: SuppliersProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [refreshing, setRefreshing] = useState(false)
  const t = translations[language]
  const isRTL = language === 'ar'

  const supplierStats = {
    totalSuppliers: 156,
    activeSuppliers: 142,
    newSuppliers: 8,
    topRated: 45
  }

  const suppliers = [
    {
      id: 1,
      name: 'شركة التقنية المتقدمة',
      nameEn: 'Advanced Technology Co.',
      contactPerson: 'أحمد محمد',
      phone: '+966501234567',
      email: '<EMAIL>',
      address: 'الرياض، المملكة العربية السعودية',
      category: 'technology',
      rating: 4.8,
      status: 'active',
      contractValue: 250000,
      lastOrder: '2024-01-15',
      joinDate: '2022-03-10',
      orders: 45,
      performance: 'excellent'
    },
    {
      id: 2,
      name: 'مؤسسة الأثاث الحديث',
      nameEn: 'Modern Furniture Est.',
      contactPerson: 'فاطمة علي',
      phone: '+966502345678',
      email: '<EMAIL>',
      address: 'جدة، المملكة العربية السعودية',
      category: 'furniture',
      rating: 4.5,
      status: 'active',
      contractValue: 180000,
      lastOrder: '2024-01-20',
      joinDate: '2021-08-15',
      orders: 32,
      performance: 'good'
    },
    {
      id: 3,
      name: 'شركة الصيانة الشاملة',
      nameEn: 'Comprehensive Maintenance Co.',
      contactPerson: 'محمد حسن',
      phone: '+966503456789',
      email: '<EMAIL>',
      address: 'الدمام، المملكة العربية السعودية',
      category: 'maintenance',
      rating: 4.2,
      status: 'pending',
      contractValue: 120000,
      lastOrder: '2024-01-10',
      joinDate: '2023-01-20',
      orders: 18,
      performance: 'good'
    }
  ]

  const handleRefresh = async () => {
    setRefreshing(true)
    await new Promise(resolve => setTimeout(resolve, 1000))
    setRefreshing(false)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'inactive':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'blocked':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getPerformanceColor = (performance: string) => {
    switch (performance) {
      case 'excellent':
        return 'text-green-400'
      case 'good':
        return 'text-blue-400'
      case 'average':
        return 'text-yellow-400'
      case 'poor':
        return 'text-red-400'
      default:
        return 'text-gray-400'
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'technology':
        return '💻'
      case 'furniture':
        return '🪑'
      case 'maintenance':
        return '🔧'
      case 'office':
        return '📋'
      case 'catering':
        return '🍽️'
      case 'cleaning':
        return '🧹'
      default:
        return '📦'
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount)
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">{t.suppliers}</h1>
          <p className="text-white/70">إدارة ومتابعة الموردين والشركاء التجاريين</p>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={handleRefresh}
            disabled={refreshing}
            className="glass-button"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            {t.refresh}
          </Button>
          <Button className="glass-button">
            <Plus className="h-4 w-4 mr-2" />
            {t.addSupplier}
          </Button>
          <Button variant="outline" className="glass-button">
            <Download className="h-4 w-4 mr-2" />
            {t.export}
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.totalSuppliers}</p>
                <p className="text-2xl font-bold text-white">{supplierStats.totalSuppliers}</p>
              </div>
              <Building className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.activeSuppliers}</p>
                <p className="text-2xl font-bold text-green-400">{supplierStats.activeSuppliers}</p>
              </div>
              <Truck className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.newSuppliers}</p>
                <p className="text-2xl font-bold text-purple-400">{supplierStats.newSuppliers}</p>
              </div>
              <Plus className="h-8 w-8 text-purple-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.topRated}</p>
                <p className="text-2xl font-bold text-yellow-400">{supplierStats.topRated}</p>
              </div>
              <Star className="h-8 w-8 text-yellow-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Suppliers List */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <CardTitle className="text-white text-xl">قائمة الموردين</CardTitle>
              <CardDescription className="text-white/70">
                إدارة معلومات الموردين والشركاء
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 h-4 w-4" />
                <Input
                  placeholder={t.search}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 glass-input"
                />
              </div>
              <Button variant="outline" className="glass-button">
                <Filter className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {suppliers.map((supplier) => (
              <Card key={supplier.id} className="glass-card border-white/10 hover:border-white/30 transition-all">
                <CardContent className="p-6">
                  <div className="space-y-4">
                    {/* Supplier Header */}
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3">
                        <div className="text-2xl">{getCategoryIcon(supplier.category)}</div>
                        <div>
                          <h3 className="text-white font-semibold">{supplier.name}</h3>
                          <p className="text-white/60 text-sm">{supplier.contactPerson}</p>
                        </div>
                      </div>
                      <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(supplier.status)}`}>
                        {t[supplier.status as keyof typeof t]}
                      </div>
                    </div>

                    {/* Contact Information */}
                    <div className="space-y-2 text-sm">
                      <div className="flex items-center gap-2">
                        <Phone className="h-4 w-4 text-blue-400" />
                        <span className="text-white/80">{supplier.phone}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4 text-green-400" />
                        <span className="text-white/80">{supplier.email}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4 text-purple-400" />
                        <span className="text-white/80">{supplier.address}</span>
                      </div>
                    </div>

                    {/* Performance Metrics */}
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="text-white/70">التقييم</p>
                        <div className="flex items-center gap-1">
                          <Star className="h-4 w-4 text-yellow-400 fill-current" />
                          <span className="text-white font-medium">{supplier.rating}</span>
                        </div>
                      </div>
                      <div>
                        <p className="text-white/70">الأداء</p>
                        <p className={`font-medium ${getPerformanceColor(supplier.performance)}`}>
                          {t[supplier.performance as keyof typeof t]}
                        </p>
                      </div>
                      <div>
                        <p className="text-white/70">قيمة العقد</p>
                        <p className="text-white font-medium">{formatCurrency(supplier.contractValue)}</p>
                      </div>
                      <div>
                        <p className="text-white/70">الطلبات</p>
                        <p className="text-white font-medium">{supplier.orders}</p>
                      </div>
                    </div>

                    {/* Last Order Info */}
                    <div className="flex items-center justify-between text-xs text-white/60">
                      <div className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        <span>آخر طلب: {supplier.lastOrder}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <User className="h-3 w-3" />
                        <span>انضم: {supplier.joinDate}</span>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex gap-2 pt-2">
                      <Button size="sm" className="glass-button flex-1">
                        <Eye className="h-3 w-3 mr-1" />
                        {t.viewDetails}
                      </Button>
                      <Button size="sm" variant="outline" className="glass-button">
                        <Edit className="h-3 w-3" />
                      </Button>
                      <Button size="sm" variant="outline" className="glass-button text-red-400 hover:bg-red-500/20">
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
