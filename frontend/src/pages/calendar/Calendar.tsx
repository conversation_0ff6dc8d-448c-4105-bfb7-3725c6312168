import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Badge } from '../../components/ui/badge'
import {
  Calendar as CalendarIcon,
  Plus,
  ChevronLeft,
  ChevronRight,
  Clock,
  MapPin,
  Users,
  Video,
  Phone,
  Edit,
  Trash2,
  Filter
} from 'lucide-react'

interface CalendarProps {
  language: 'ar' | 'en'
}

interface Event {
  id: string
  title: string
  titleAr: string
  description: string
  descriptionAr: string
  startTime: string
  endTime: string
  date: string
  type: 'meeting' | 'appointment' | 'deadline' | 'holiday' | 'training' | 'interview'
  location?: string
  locationAr?: string
  attendees: string[]
  attendeesAr: string[]
  isOnline: boolean
  meetingLink?: string
  priority: 'low' | 'medium' | 'high' | 'urgent'
  status: 'scheduled' | 'in-progress' | 'completed' | 'cancelled'
  organizer: string
  organizerAr: string
  reminders: number[] // minutes before event
}

const mockEvents: Event[] = [
  {
    id: '1',
    title: 'Weekly Team Meeting',
    titleAr: 'اجتماع الفريق الأسبوعي',
    description: 'Weekly sync meeting with the development team',
    descriptionAr: 'اجتماع مزامنة أسبوعي مع فريق التطوير',
    startTime: '09:00',
    endTime: '10:00',
    date: '2024-01-22',
    type: 'meeting',
    location: 'Conference Room A',
    locationAr: 'قاعة الاجتماعات أ',
    attendees: ['Ahmed Al-Rashid', 'Sarah Hassan', 'Mohammed Ali'],
    attendeesAr: ['أحمد الراشد', 'سارة حسن', 'محمد علي'],
    isOnline: false,
    priority: 'medium',
    status: 'scheduled',
    organizer: 'Omar Abdullah',
    organizerAr: 'عمر عبدالله',
    reminders: [15, 5]
  },
  {
    id: '2',
    title: 'Client Presentation',
    titleAr: 'عرض تقديمي للعميل',
    description: 'Quarterly business review with ABC Company',
    descriptionAr: 'مراجعة الأعمال الربع سنوية مع شركة أي بي سي',
    startTime: '14:00',
    endTime: '15:30',
    date: '2024-01-22',
    type: 'meeting',
    location: 'Online',
    locationAr: 'عبر الإنترنت',
    attendees: ['Fatima Mohammed', 'Layla Ahmad'],
    attendeesAr: ['فاطمة محمد', 'ليلى أحمد'],
    isOnline: true,
    meetingLink: 'https://meet.company.com/abc-review',
    priority: 'high',
    status: 'scheduled',
    organizer: 'Fatima Mohammed',
    organizerAr: 'فاطمة محمد',
    reminders: [30, 15, 5]
  },
  {
    id: '3',
    title: 'Project Deadline',
    titleAr: 'موعد تسليم المشروع',
    description: 'Final submission for Project Alpha',
    descriptionAr: 'التسليم النهائي لمشروع ألفا',
    startTime: '17:00',
    endTime: '17:00',
    date: '2024-01-25',
    type: 'deadline',
    attendees: ['Development Team'],
    attendeesAr: ['فريق التطوير'],
    isOnline: false,
    priority: 'urgent',
    status: 'scheduled',
    organizer: 'Project Manager',
    organizerAr: 'مدير المشروع',
    reminders: [1440, 480, 60] // 1 day, 8 hours, 1 hour
  },
  {
    id: '4',
    title: 'Job Interview - Software Engineer',
    titleAr: 'مقابلة عمل - مهندس برمجيات',
    description: 'Technical interview for senior software engineer position',
    descriptionAr: 'مقابلة تقنية لمنصب مهندس برمجيات أول',
    startTime: '11:00',
    endTime: '12:00',
    date: '2024-01-23',
    type: 'interview',
    location: 'HR Office',
    locationAr: 'مكتب الموارد البشرية',
    attendees: ['HR Manager', 'Tech Lead'],
    attendeesAr: ['مدير الموارد البشرية', 'قائد تقني'],
    isOnline: false,
    priority: 'medium',
    status: 'scheduled',
    organizer: 'HR Department',
    organizerAr: 'قسم الموارد البشرية',
    reminders: [60, 15]
  }
]

const daysOfWeek = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت']
const daysOfWeekEn = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']

export default function Calendar({ language }: CalendarProps) {
  const [events, setEvents] = useState<Event[]>(mockEvents)
  const [currentDate, setCurrentDate] = useState(new Date())
  const [selectedDate, setSelectedDate] = useState<string | null>(null)
  const [viewMode, setViewMode] = useState<'month' | 'week' | 'day'>('month')
  const [typeFilter, setTypeFilter] = useState<string>('all')

  const text = {
    ar: {
      title: 'التقويم والجدولة',
      description: 'إدارة المواعيد والأحداث والاجتماعات',
      newEvent: 'حدث جديد',
      today: 'اليوم',
      month: 'شهر',
      week: 'أسبوع',
      day: 'يوم',
      events: 'الأحداث',
      noEvents: 'لا توجد أحداث في هذا التاريخ',
      eventDetails: 'تفاصيل الحدث',
      time: 'الوقت',
      location: 'المكان',
      attendees: 'الحضور',
      organizer: 'المنظم',
      priority: 'الأولوية',
      status: 'الحالة',
      type: 'النوع',
      edit: 'تعديل',
      delete: 'حذف',
      join: 'انضمام',
      meeting: 'اجتماع',
      appointment: 'موعد',
      deadline: 'موعد نهائي',
      holiday: 'عطلة',
      training: 'تدريب',
      interview: 'مقابلة',
      low: 'منخفض',
      medium: 'متوسط',
      high: 'عالي',
      urgent: 'عاجل',
      scheduled: 'مجدول',
      'in-progress': 'قيد التنفيذ',
      completed: 'مكتمل',
      cancelled: 'ملغي',
      online: 'عبر الإنترنت',
      offline: 'حضوري'
    },
    en: {
      title: 'Calendar & Scheduling',
      description: 'Manage appointments, events, and meetings',
      newEvent: 'New Event',
      today: 'Today',
      month: 'Month',
      week: 'Week',
      day: 'Day',
      events: 'Events',
      noEvents: 'No events on this date',
      eventDetails: 'Event Details',
      time: 'Time',
      location: 'Location',
      attendees: 'Attendees',
      organizer: 'Organizer',
      priority: 'Priority',
      status: 'Status',
      type: 'Type',
      edit: 'Edit',
      delete: 'Delete',
      join: 'Join',
      meeting: 'Meeting',
      appointment: 'Appointment',
      deadline: 'Deadline',
      holiday: 'Holiday',
      training: 'Training',
      interview: 'Interview',
      low: 'Low',
      medium: 'Medium',
      high: 'High',
      urgent: 'Urgent',
      scheduled: 'Scheduled',
      'in-progress': 'In Progress',
      completed: 'Completed',
      cancelled: 'Cancelled',
      online: 'Online',
      offline: 'Offline'
    }
  }

  const t = text[language]

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'meeting': return 'bg-primary/20 text-primary border-primary/30'
      case 'appointment': return 'bg-secondary/20 text-secondary border-secondary/30'
      case 'deadline': return 'bg-destructive/20 text-destructive border-destructive/30'
      case 'holiday': return 'bg-accent/20 text-accent border-accent/30'
      case 'training': return 'bg-muted/20 text-muted-foreground border-muted/30'
      case 'interview': return 'bg-primary/15 text-primary border-primary/25'
      default: return 'bg-muted/20 text-muted-foreground border-muted/30'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low': return 'bg-secondary/20 text-secondary'
      case 'medium': return 'bg-accent/20 text-accent'
      case 'high': return 'bg-primary/20 text-primary'
      case 'urgent': return 'bg-destructive/20 text-destructive'
      default: return 'bg-muted/20 text-muted-foreground'
    }
  }

  const formatDate = (date: Date) => {
    return date.toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US', {
      year: 'numeric',
      month: 'long'
    })
  }

  const getEventsForDate = (date: string) => {
    return events.filter(event =>
      event.date === date &&
      (typeFilter === 'all' || event.type === typeFilter)
    )
  }

  const generateCalendarDays = () => {
    const year = currentDate.getFullYear()
    const month = currentDate.getMonth()
    const firstDay = new Date(year, month, 1)
    const lastDay = new Date(year, month + 1, 0)
    const daysInMonth = lastDay.getDate()
    const startingDayOfWeek = firstDay.getDay()

    const days = []

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(null)
    }

    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(day)
    }

    return days
  }

  const navigateMonth = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate)
    if (direction === 'prev') {
      newDate.setMonth(newDate.getMonth() - 1)
    } else {
      newDate.setMonth(newDate.getMonth() + 1)
    }
    setCurrentDate(newDate)
  }

  const formatDateString = (day: number) => {
    const year = currentDate.getFullYear()
    const month = currentDate.getMonth()
    return new Date(year, month, day).toISOString().split('T')[0]
  }

  const calendarDays = generateCalendarDays()
  const selectedDateEvents = selectedDate ? getEventsForDate(selectedDate) : []

  return (
    <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">{t.title}</h1>
            <p className="text-white/70">{t.description}</p>
          </div>
          <div className="flex gap-3">
            <Button variant="outline" className="modern-button">
              {t.today}
            </Button>
            <Button className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70">
              <Plus className="w-4 h-4 mr-2" />
              {t.newEvent}
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Calendar */}
          <div className="lg:col-span-2">
            <Card className="modern-card">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => navigateMonth('prev')}
                      className="text-white/70 hover:text-white"
                    >
                      <ChevronLeft className="w-4 h-4" />
                    </Button>
                    <h2 className="text-xl font-semibold text-white">
                      {formatDate(currentDate)}
                    </h2>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => navigateMonth('next')}
                      className="text-white/70 hover:text-white"
                    >
                      <ChevronRight className="w-4 h-4" />
                    </Button>
                  </div>
                  <div className="flex gap-2">
                    <select
                      value={typeFilter}
                      onChange={(e) => setTypeFilter(e.target.value)}
                      className="modern-input px-3 py-1 text-sm rounded"
                    >
                      <option value="all">جميع الأنواع</option>
                      <option value="meeting">{t.meeting}</option>
                      <option value="appointment">{t.appointment}</option>
                      <option value="deadline">{t.deadline}</option>
                      <option value="training">{t.training}</option>
                      <option value="interview">{t.interview}</option>
                    </select>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                {/* Days of week header */}
                <div className="grid grid-cols-7 gap-1 mb-2">
                  {(language === 'ar' ? daysOfWeek : daysOfWeekEn).map((day) => (
                    <div key={day} className="p-2 text-center text-white/70 text-sm font-medium">
                      {day}
                    </div>
                  ))}
                </div>

                {/* Calendar grid */}
                <div className="grid grid-cols-7 gap-1">
                  {calendarDays.map((day, index) => {
                    if (day === null) {
                      return <div key={index} className="p-2 h-20"></div>
                    }

                    const dateString = formatDateString(day)
                    const dayEvents = getEventsForDate(dateString)
                    const isSelected = selectedDate === dateString
                    const isToday = dateString === new Date().toISOString().split('T')[0]

                    return (
                      <div
                        key={day}
                        className={`p-2 h-20 border border-white/10 rounded cursor-pointer transition-colors ${
                          isSelected ? 'bg-primary/20 border-primary/50' :
                          isToday ? 'bg-secondary/20 border-secondary/50' :
                          'hover:bg-white/5'
                        }`}
                        onClick={() => setSelectedDate(dateString)}
                      >
                        <div className="text-white text-sm font-medium mb-1">{day}</div>
                        <div className="space-y-1">
                          {dayEvents.slice(0, 2).map((event) => (
                            <div
                              key={event.id}
                              className={`text-xs p-1 rounded truncate ${getTypeColor(event.type)}`}
                            >
                              {language === 'ar' ? event.titleAr : event.title}
                            </div>
                          ))}
                          {dayEvents.length > 2 && (
                            <div className="text-xs text-white/50">
                              +{dayEvents.length - 2} المزيد
                            </div>
                          )}
                        </div>
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Events Sidebar */}
          <div>
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="text-white">
                  {selectedDate ? `${t.events} - ${selectedDate}` : t.events}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {selectedDateEvents.length === 0 ? (
                  <div className="text-center py-8 text-white/50">
                    <CalendarIcon className="w-12 h-12 mx-auto mb-4 text-white/30" />
                    <p>{t.noEvents}</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {selectedDateEvents.map((event) => (
                      <div key={event.id} className="p-4 rounded-lg bg-white/5 border border-white/10">
                        <div className="flex items-start justify-between mb-2">
                          <h3 className="text-white font-medium">
                            {language === 'ar' ? event.titleAr : event.title}
                          </h3>
                          <div className="flex gap-1">
                            <Button size="sm" variant="ghost" className="h-6 w-6 p-0 text-white/70">
                              <Edit className="w-3 h-3" />
                            </Button>
                            <Button size="sm" variant="ghost" className="h-6 w-6 p-0 text-red-400">
                              <Trash2 className="w-3 h-3" />
                            </Button>
                          </div>
                        </div>

                        <div className="space-y-2 text-sm">
                          <div className="flex items-center gap-2 text-white/70">
                            <Clock className="w-4 h-4" />
                            <span>{event.startTime} - {event.endTime}</span>
                          </div>

                          {event.location && (
                            <div className="flex items-center gap-2 text-white/70">
                              {event.isOnline ? <Video className="w-4 h-4" /> : <MapPin className="w-4 h-4" />}
                              <span>{language === 'ar' ? event.locationAr : event.location}</span>
                            </div>
                          )}

                          <div className="flex items-center gap-2 text-white/70">
                            <Users className="w-4 h-4" />
                            <span>{event.attendees.length} مشارك</span>
                          </div>

                          <div className="flex gap-2 mt-3">
                            <Badge className={`${getTypeColor(event.type)} border text-xs`}>
                              {t[event.type as keyof typeof t]}
                            </Badge>
                            <Badge className={`${getPriorityColor(event.priority)} border text-xs`}>
                              {t[event.priority as keyof typeof t]}
                            </Badge>
                          </div>

                          {event.isOnline && event.meetingLink && (
                            <Button size="sm" className="w-full mt-3 bg-green-600 hover:bg-green-700">
                              <Video className="w-4 h-4 mr-2" />
                              {t.join}
                            </Button>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
    </div>
  )
}
