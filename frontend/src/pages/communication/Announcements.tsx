import { useState } from 'react'
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  Megaphone,
  Plus,
  Search,
  Filter,
  Eye,
  Edit,
  Trash2,
  Pin,
  Calendar,
  User,
  AlertTriangle,
  Info,
  CheckCircle,
  RefreshCw,
  Send,
  Bell
} from 'lucide-react'

interface AnnouncementsProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    announcements: 'الإعلانات',
    createAnnouncement: 'إنشاء إعلان',
    search: 'بحث',
    filter: 'تصفية',
    refresh: 'تحديث',
    title: 'العنوان',
    content: 'المحتوى',
    priority: 'الأولوية',
    targetAudience: 'الجمهور المستهدف',
    publishDate: 'تاريخ النشر',
    expiryDate: 'تاريخ الانتهاء',
    status: 'الحالة',
    actions: 'الإجراءات',
    viewAnnouncement: 'عرض الإعلان',
    editAnnouncement: 'تعديل الإعلان',
    deleteAnnouncement: 'حذف الإعلان',
    pinAnnouncement: 'تثبيت الإعلان',
    published: 'منشور',
    draft: 'مسودة',
    expired: 'منتهي الصلاحية',
    scheduled: 'مجدول',
    high: 'عالي',
    medium: 'متوسط',
    low: 'منخفض',
    urgent: 'عاجل',
    allEmployees: 'جميع الموظفين',
    management: 'الإدارة',
    hrTeam: 'فريق الموارد البشرية',
    itTeam: 'فريق تقنية المعلومات',
    financeTeam: 'فريق المالية',
    totalAnnouncements: 'إجمالي الإعلانات',
    activeAnnouncements: 'الإعلانات النشطة',
    pinnedAnnouncements: 'الإعلانات المثبتة',
    draftAnnouncements: 'المسودات',
    publishedBy: 'نشر بواسطة',
    readBy: 'قرأه',
    views: 'المشاهدات'
  },
  en: {
    announcements: 'Announcements',
    createAnnouncement: 'Create Announcement',
    search: 'Search',
    filter: 'Filter',
    refresh: 'Refresh',
    title: 'Title',
    content: 'Content',
    priority: 'Priority',
    targetAudience: 'Target Audience',
    publishDate: 'Publish Date',
    expiryDate: 'Expiry Date',
    status: 'Status',
    actions: 'Actions',
    viewAnnouncement: 'View Announcement',
    editAnnouncement: 'Edit Announcement',
    deleteAnnouncement: 'Delete Announcement',
    pinAnnouncement: 'Pin Announcement',
    published: 'Published',
    draft: 'Draft',
    expired: 'Expired',
    scheduled: 'Scheduled',
    high: 'High',
    medium: 'Medium',
    low: 'Low',
    urgent: 'Urgent',
    allEmployees: 'All Employees',
    management: 'Management',
    hrTeam: 'HR Team',
    itTeam: 'IT Team',
    financeTeam: 'Finance Team',
    totalAnnouncements: 'Total Announcements',
    activeAnnouncements: 'Active Announcements',
    pinnedAnnouncements: 'Pinned Announcements',
    draftAnnouncements: 'Draft Announcements',
    publishedBy: 'Published By',
    readBy: 'Read By',
    views: 'Views'
  }
}

export default function Announcements({ language }: AnnouncementsProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [refreshing, setRefreshing] = useState(false)
  const [showCreateForm, setShowCreateForm] = useState(false)
  const t = translations[language]
  const isRTL = language === 'ar'

  const announcementStats = {
    totalAnnouncements: 45,
    activeAnnouncements: 12,
    pinnedAnnouncements: 3,
    draftAnnouncements: 5
  }

  const announcements = [
    {
      id: 1,
      title: 'تحديث سياسة العمل عن بُعد',
      titleEn: 'Remote Work Policy Update',
      content: 'تم تحديث سياسة العمل عن بُعد لتشمل مرونة أكثر في ساعات العمل...',
      contentEn: 'Remote work policy has been updated to include more flexibility in working hours...',
      priority: 'high',
      targetAudience: 'allEmployees',
      publishDate: '2024-01-20',
      expiryDate: '2024-02-20',
      status: 'published',
      publishedBy: 'فاطمة علي',
      views: 245,
      readBy: 198,
      isPinned: true
    },
    {
      id: 2,
      title: 'إجازة عيد الفطر المبارك',
      titleEn: 'Eid Al-Fitr Holiday',
      content: 'نعلن عن إجازة عيد الفطر المبارك من تاريخ...',
      contentEn: 'We announce the Eid Al-Fitr holiday from...',
      priority: 'medium',
      targetAudience: 'allEmployees',
      publishDate: '2024-01-18',
      expiryDate: '2024-04-15',
      status: 'published',
      publishedBy: 'أحمد محمد',
      views: 312,
      readBy: 289,
      isPinned: true
    },
    {
      id: 3,
      title: 'تدريب أمان المعلومات',
      titleEn: 'Information Security Training',
      content: 'دورة تدريبية إلزامية حول أمان المعلومات...',
      contentEn: 'Mandatory training course on information security...',
      priority: 'urgent',
      targetAudience: 'allEmployees',
      publishDate: '2024-01-22',
      expiryDate: '2024-02-22',
      status: 'published',
      publishedBy: 'محمد حسن',
      views: 156,
      readBy: 89,
      isPinned: false
    },
    {
      id: 4,
      title: 'تحديث نظام الحضور والانصراف',
      titleEn: 'Attendance System Update',
      content: 'سيتم تحديث نظام الحضور والانصراف يوم الجمعة...',
      contentEn: 'The attendance system will be updated on Friday...',
      priority: 'medium',
      targetAudience: 'allEmployees',
      publishDate: '2024-01-25',
      expiryDate: '2024-01-26',
      status: 'expired',
      publishedBy: 'سارة أحمد',
      views: 89,
      readBy: 76,
      isPinned: false
    }
  ]

  const handleRefresh = async () => {
    setRefreshing(true)
    await new Promise(resolve => setTimeout(resolve, 1000))
    setRefreshing(false)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'draft':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      case 'expired':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'scheduled':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'border-l-red-500 bg-red-500/10'
      case 'high':
        return 'border-l-orange-500 bg-orange-500/10'
      case 'medium':
        return 'border-l-blue-500 bg-blue-500/10'
      case 'low':
        return 'border-l-green-500 bg-green-500/10'
      default:
        return 'border-l-gray-500 bg-gray-500/10'
    }
  }

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return <AlertTriangle className="h-4 w-4 text-red-500" />
      case 'high':
        return <AlertTriangle className="h-4 w-4 text-orange-500" />
      case 'medium':
        return <Info className="h-4 w-4 text-blue-500" />
      case 'low':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      default:
        return <Info className="h-4 w-4 text-gray-500" />
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">{t.announcements}</h1>
          <p className="text-white/70">إدارة ونشر الإعلانات والأخبار المهمة</p>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={handleRefresh}
            disabled={refreshing}
            className="glass-button"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            {t.refresh}
          </Button>
          <Button 
            onClick={() => setShowCreateForm(true)}
            className="glass-button"
          >
            <Plus className="h-4 w-4 mr-2" />
            {t.createAnnouncement}
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.totalAnnouncements}</p>
                <p className="text-2xl font-bold text-white">{announcementStats.totalAnnouncements}</p>
              </div>
              <Megaphone className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.activeAnnouncements}</p>
                <p className="text-2xl font-bold text-green-400">{announcementStats.activeAnnouncements}</p>
              </div>
              <Bell className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.pinnedAnnouncements}</p>
                <p className="text-2xl font-bold text-yellow-400">{announcementStats.pinnedAnnouncements}</p>
              </div>
              <Pin className="h-8 w-8 text-yellow-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.draftAnnouncements}</p>
                <p className="text-2xl font-bold text-gray-400">{announcementStats.draftAnnouncements}</p>
              </div>
              <Edit className="h-8 w-8 text-gray-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Announcements List */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <CardTitle className="text-white text-xl">الإعلانات</CardTitle>
              <CardDescription className="text-white/70">
                قائمة بجميع الإعلانات والأخبار
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 h-4 w-4" />
                <Input
                  placeholder={t.search}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 glass-input"
                />
              </div>
              <Button variant="outline" className="glass-button">
                <Filter className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {announcements.map((announcement) => (
              <div key={announcement.id} className={`p-4 glass-card border-white/10 hover:border-white/30 transition-all border-l-4 ${getPriorityColor(announcement.priority)}`}>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-3">
                      {announcement.isPinned && (
                        <Pin className="h-4 w-4 text-yellow-400" />
                      )}
                      <h4 className="text-white font-semibold">
                        {language === 'ar' ? announcement.title : announcement.titleEn}
                      </h4>
                      <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(announcement.status)}`}>
                        {t[announcement.status as keyof typeof t]}
                      </div>
                      {getPriorityIcon(announcement.priority)}
                    </div>
                    
                    <p className="text-white/80 text-sm mb-3 line-clamp-2">
                      {language === 'ar' ? announcement.content : announcement.contentEn}
                    </p>

                    <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 text-sm text-white/70">
                      <div className="flex items-center gap-1">
                        <User className="h-3 w-3" />
                        <span>{announcement.publishedBy}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        <span>{announcement.publishDate}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Eye className="h-3 w-3" />
                        <span>{announcement.views} مشاهدة</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <CheckCircle className="h-3 w-3" />
                        <span>{announcement.readBy} قراءة</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex gap-2">
                    <Button size="sm" className="glass-button">
                      <Eye className="h-3 w-3 mr-1" />
                      {t.viewAnnouncement}
                    </Button>
                    <Button size="sm" variant="outline" className="glass-button">
                      <Edit className="h-3 w-3" />
                    </Button>
                    <Button size="sm" variant="outline" className="glass-button">
                      <Pin className="h-3 w-3" />
                    </Button>
                    <Button size="sm" variant="outline" className="glass-button text-red-400 hover:bg-red-500/20">
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
