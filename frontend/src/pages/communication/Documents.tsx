import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  FileText,
  Upload,
  Search,
  Filter,
  Download,
  Eye,
  Edit,
  Trash2,
  Share,
  Folder,
  File,
  Image,
  Video,
  Archive,
  RefreshCw,
  Calendar,
  User,
  Lock,
  Globe,
  Star,
  MoreVertical
} from 'lucide-react'

interface DocumentsProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    documents: 'المستندات',
    uploadDocument: 'رفع مستند',
    search: 'بحث',
    filter: 'تصفية',
    refresh: 'تحديث',
    fileName: 'اسم الملف',
    fileType: 'نوع الملف',
    fileSize: 'حجم الملف',
    uploadDate: 'تاريخ الرفع',
    uploadedBy: 'رفع بواسطة',
    lastModified: 'آخر تعديل',
    permissions: 'الصلاحيات',
    actions: 'الإجراءات',
    viewDocument: 'عرض المستند',
    editDocument: 'تعديل المستند',
    deleteDocument: 'حذف المستند',
    shareDocument: 'مشاركة المستند',
    downloadDocument: 'تحميل المستند',
    public: 'عام',
    private: 'خاص',
    restricted: 'مقيد',
    totalDocuments: 'إجمالي المستندات',
    recentUploads: 'الرفوعات الحديثة',
    sharedDocuments: 'المستندات المشتركة',
    myDocuments: 'مستنداتي',
    folders: 'المجلدات',
    allFiles: 'جميع الملفات',
    images: 'الصور',
    videos: 'الفيديوهات',
    archives: 'الأرشيف',
    policies: 'السياسات',
    procedures: 'الإجراءات',
    forms: 'النماذج',
    reports: 'التقارير',
    contracts: 'العقود',
    presentations: 'العروض التقديمية',
    version: 'الإصدار',
    tags: 'العلامات',
    category: 'الفئة',
    description: 'الوصف'
  },
  en: {
    documents: 'Documents',
    uploadDocument: 'Upload Document',
    search: 'Search',
    filter: 'Filter',
    refresh: 'Refresh',
    fileName: 'File Name',
    fileType: 'File Type',
    fileSize: 'File Size',
    uploadDate: 'Upload Date',
    uploadedBy: 'Uploaded By',
    lastModified: 'Last Modified',
    permissions: 'Permissions',
    actions: 'Actions',
    viewDocument: 'View Document',
    editDocument: 'Edit Document',
    deleteDocument: 'Delete Document',
    shareDocument: 'Share Document',
    downloadDocument: 'Download Document',
    public: 'Public',
    private: 'Private',
    restricted: 'Restricted',
    totalDocuments: 'Total Documents',
    recentUploads: 'Recent Uploads',
    sharedDocuments: 'Shared Documents',
    myDocuments: 'My Documents',
    folders: 'Folders',
    allFiles: 'All Files',
    images: 'Images',
    videos: 'Videos',
    archives: 'Archives',
    policies: 'Policies',
    procedures: 'Procedures',
    forms: 'Forms',
    reports: 'Reports',
    contracts: 'Contracts',
    presentations: 'Presentations',
    version: 'Version',
    tags: 'Tags',
    category: 'Category',
    description: 'Description'
  }
}

export default function Documents({ language }: DocumentsProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('allFiles')
  const [refreshing, setRefreshing] = useState(false)
  const t = translations[language]
  const isRTL = language === 'ar'

  const documentStats = {
    totalDocuments: 1247,
    recentUploads: 23,
    sharedDocuments: 156,
    myDocuments: 89
  }

  const categories = [
    { id: 'allFiles', name: t.allFiles, icon: File, count: 1247 },
    { id: 'policies', name: t.policies, icon: FileText, count: 45 },
    { id: 'procedures', name: t.procedures, icon: FileText, count: 78 },
    { id: 'forms', name: t.forms, icon: FileText, count: 123 },
    { id: 'reports', name: t.reports, icon: FileText, count: 234 },
    { id: 'contracts', name: t.contracts, icon: FileText, count: 67 },
    { id: 'presentations', name: t.presentations, icon: FileText, count: 89 },
    { id: 'images', name: t.images, icon: Image, count: 345 },
    { id: 'videos', name: t.videos, icon: Video, count: 23 },
    { id: 'archives', name: t.archives, icon: Archive, count: 156 }
  ]

  const documents = [
    {
      id: 1,
      name: 'سياسة الموارد البشرية 2024',
      nameEn: 'HR Policy 2024',
      type: 'PDF',
      size: '2.3 MB',
      uploadDate: '2024-01-20',
      uploadedBy: 'فاطمة علي',
      lastModified: '2024-01-22',
      permissions: 'public',
      category: 'policies',
      version: '1.2',
      downloads: 145,
      views: 234,
      isStarred: true,
      tags: ['سياسة', 'موارد بشرية', '2024']
    },
    {
      id: 2,
      name: 'نموذج طلب إجازة',
      nameEn: 'Leave Request Form',
      type: 'DOCX',
      size: '156 KB',
      uploadDate: '2024-01-18',
      uploadedBy: 'أحمد محمد',
      lastModified: '2024-01-19',
      permissions: 'public',
      category: 'forms',
      version: '2.0',
      downloads: 89,
      views: 156,
      isStarred: false,
      tags: ['نموذج', 'إجازة', 'موارد بشرية']
    },
    {
      id: 3,
      name: 'تقرير الأداء المالي Q4',
      nameEn: 'Financial Performance Report Q4',
      type: 'XLSX',
      size: '4.7 MB',
      uploadDate: '2024-01-15',
      uploadedBy: 'محمد حسن',
      lastModified: '2024-01-16',
      permissions: 'restricted',
      category: 'reports',
      version: '1.0',
      downloads: 23,
      views: 45,
      isStarred: true,
      tags: ['تقرير', 'مالي', 'Q4', 'أداء']
    },
    {
      id: 4,
      name: 'عقد شراكة تقنية',
      nameEn: 'Technology Partnership Contract',
      type: 'PDF',
      size: '1.8 MB',
      uploadDate: '2024-01-12',
      uploadedBy: 'سارة أحمد',
      lastModified: '2024-01-14',
      permissions: 'private',
      category: 'contracts',
      version: '3.1',
      downloads: 12,
      views: 28,
      isStarred: false,
      tags: ['عقد', 'شراكة', 'تقنية']
    }
  ]

  const handleRefresh = async () => {
    setRefreshing(true)
    await new Promise(resolve => setTimeout(resolve, 1000))
    setRefreshing(false)
  }

  const getFileIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'pdf':
        return <FileText className="h-6 w-6 text-red-400" />
      case 'docx':
      case 'doc':
        return <FileText className="h-6 w-6 text-blue-400" />
      case 'xlsx':
      case 'xls':
        return <FileText className="h-6 w-6 text-green-400" />
      case 'pptx':
      case 'ppt':
        return <FileText className="h-6 w-6 text-orange-400" />
      case 'jpg':
      case 'jpeg':
      case 'png':
        return <Image className="h-6 w-6 text-purple-400" />
      case 'mp4':
      case 'avi':
        return <Video className="h-6 w-6 text-pink-400" />
      default:
        return <File className="h-6 w-6 text-gray-400" />
    }
  }

  const getPermissionColor = (permission: string) => {
    switch (permission) {
      case 'public':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'private':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'restricted':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getPermissionIcon = (permission: string) => {
    switch (permission) {
      case 'public':
        return <Globe className="h-3 w-3" />
      case 'private':
        return <Lock className="h-3 w-3" />
      case 'restricted':
        return <User className="h-3 w-3" />
      default:
        return <File className="h-3 w-3" />
    }
  }

  const formatFileSize = (size: string) => {
    return size
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">{t.documents}</h1>
          <p className="text-white/70">إدارة ومشاركة المستندات والملفات</p>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={handleRefresh}
            disabled={refreshing}
            className="glass-button"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            {t.refresh}
          </Button>
          <Button className="glass-button">
            <Upload className="h-4 w-4 mr-2" />
            {t.uploadDocument}
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.totalDocuments}</p>
                <p className="text-2xl font-bold text-white">{documentStats.totalDocuments}</p>
              </div>
              <FileText className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.recentUploads}</p>
                <p className="text-2xl font-bold text-green-400">{documentStats.recentUploads}</p>
              </div>
              <Upload className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.sharedDocuments}</p>
                <p className="text-2xl font-bold text-purple-400">{documentStats.sharedDocuments}</p>
              </div>
              <Share className="h-8 w-8 text-purple-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.myDocuments}</p>
                <p className="text-2xl font-bold text-orange-400">{documentStats.myDocuments}</p>
              </div>
              <User className="h-8 w-8 text-orange-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Categories Sidebar */}
        <Card className="glass-card border-white/20">
          <CardHeader>
            <CardTitle className="text-white text-lg">{t.folders}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`w-full flex items-center justify-between p-3 rounded-lg transition-all ${
                    selectedCategory === category.id
                      ? 'bg-white/20 text-white'
                      : 'text-white/70 hover:bg-white/10 hover:text-white'
                  }`}
                >
                  <div className="flex items-center gap-3">
                    <category.icon className="h-4 w-4" />
                    <span className="text-sm">{category.name}</span>
                  </div>
                  <span className="text-xs bg-white/20 px-2 py-1 rounded-full">
                    {category.count}
                  </span>
                </button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Documents List */}
        <div className="lg:col-span-3">
          <Card className="glass-card border-white/20">
            <CardHeader>
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div>
                  <CardTitle className="text-white text-xl">المستندات</CardTitle>
                  <CardDescription className="text-white/70">
                    قائمة بجميع المستندات والملفات
                  </CardDescription>
                </div>
                <div className="flex gap-2">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 h-4 w-4" />
                    <Input
                      placeholder={t.search}
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 glass-input"
                    />
                  </div>
                  <Button variant="outline" className="glass-button">
                    <Filter className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {documents.map((document) => (
                  <div key={document.id} className="p-4 glass-card border-white/10 hover:border-white/30 transition-all">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-4 flex-1">
                        <div className="p-3 rounded-lg bg-white/10">
                          {getFileIcon(document.type)}
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-3 mb-2">
                            <h4 className="text-white font-medium truncate">
                              {language === 'ar' ? document.name : document.nameEn}
                            </h4>
                            {document.isStarred && (
                              <Star className="h-4 w-4 text-yellow-400 fill-current" />
                            )}
                            <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${getPermissionColor(document.permissions)}`}>
                              {getPermissionIcon(document.permissions)}
                              {t[document.permissions as keyof typeof t]}
                            </div>
                          </div>
                          
                          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 text-sm text-white/70 mb-2">
                            <div>
                              <span className="text-white/60">النوع: </span>
                              {document.type}
                            </div>
                            <div>
                              <span className="text-white/60">الحجم: </span>
                              {document.size}
                            </div>
                            <div>
                              <span className="text-white/60">الإصدار: </span>
                              {document.version}
                            </div>
                            <div>
                              <span className="text-white/60">المشاهدات: </span>
                              {document.views}
                            </div>
                          </div>

                          <div className="flex items-center gap-4 text-xs text-white/60">
                            <div className="flex items-center gap-1">
                              <User className="h-3 w-3" />
                              <span>{document.uploadedBy}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Calendar className="h-3 w-3" />
                              <span>{document.uploadDate}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Download className="h-3 w-3" />
                              <span>{document.downloads} تحميل</span>
                            </div>
                          </div>

                          {/* Tags */}
                          <div className="flex flex-wrap gap-1 mt-2">
                            {document.tags.map((tag, index) => (
                              <span key={index} className="bg-white/10 text-white/80 text-xs px-2 py-1 rounded-full">
                                {tag}
                              </span>
                            ))}
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex gap-2">
                        <Button size="sm" className="glass-button">
                          <Eye className="h-3 w-3 mr-1" />
                          {t.viewDocument}
                        </Button>
                        <Button size="sm" variant="outline" className="glass-button">
                          <Download className="h-3 w-3" />
                        </Button>
                        <Button size="sm" variant="outline" className="glass-button">
                          <Share className="h-3 w-3" />
                        </Button>
                        <Button size="sm" variant="outline" className="glass-button">
                          <MoreVertical className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
