import { useState } from 'react'
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Video,
  Plus,
  Search,
  Filter,
  Calendar,
  Clock,
  Users,
  MapPin,
  Eye,
  Edit,
  Trash2,
  Copy,
  Phone,
  Monitor,
  RefreshCw,
  User,
  CheckCircle,
  AlertTriangle,
  PlayCircle,
  Pause,
  Square
} from 'lucide-react'

interface MeetingsProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    meetings: 'الاجتماعات',
    scheduleMeeting: 'جدولة اجتماع',
    search: 'بحث',
    filter: 'تصفية',
    refresh: 'تحديث',
    meetingTitle: 'عنوان الاجتماع',
    dateTime: 'التاريخ والوقت',
    duration: 'المدة',
    attendees: 'الحاضرون',
    location: 'المكان',
    type: 'النوع',
    status: 'الحالة',
    actions: 'الإجراءات',
    viewMeeting: 'عرض الاجتماع',
    editMeeting: 'تعديل الاجتماع',
    deleteMeeting: 'حذف الاجتماع',
    joinMeeting: 'انضمام للاجتماع',
    copyLink: 'نسخ الرابط',
    scheduled: 'مجدول',
    ongoing: 'جاري',
    completed: 'مكتمل',
    cancelled: 'ملغي',
    inPerson: 'حضوري',
    virtual: 'افتراضي',
    hybrid: 'مختلط',
    totalMeetings: 'إجمالي الاجتماعات',
    todayMeetings: 'اجتماعات اليوم',
    upcomingMeetings: 'الاجتماعات القادمة',
    completedMeetings: 'الاجتماعات المكتملة',
    organizer: 'المنظم',
    agenda: 'جدول الأعمال',
    notes: 'الملاحظات',
    recording: 'التسجيل',
    minutes: 'دقيقة',
    hour: 'ساعة',
    hours: 'ساعات',
    participants: 'المشاركون',
    required: 'مطلوب',
    optional: 'اختياري',
    accepted: 'مقبول',
    declined: 'مرفوض',
    pending: 'معلق'
  },
  en: {
    meetings: 'Meetings',
    scheduleMeeting: 'Schedule Meeting',
    search: 'Search',
    filter: 'Filter',
    refresh: 'Refresh',
    meetingTitle: 'Meeting Title',
    dateTime: 'Date & Time',
    duration: 'Duration',
    attendees: 'Attendees',
    location: 'Location',
    type: 'Type',
    status: 'Status',
    actions: 'Actions',
    viewMeeting: 'View Meeting',
    editMeeting: 'Edit Meeting',
    deleteMeeting: 'Delete Meeting',
    joinMeeting: 'Join Meeting',
    copyLink: 'Copy Link',
    scheduled: 'Scheduled',
    ongoing: 'Ongoing',
    completed: 'Completed',
    cancelled: 'Cancelled',
    inPerson: 'In-Person',
    virtual: 'Virtual',
    hybrid: 'Hybrid',
    totalMeetings: 'Total Meetings',
    todayMeetings: "Today's Meetings",
    upcomingMeetings: 'Upcoming Meetings',
    completedMeetings: 'Completed Meetings',
    organizer: 'Organizer',
    agenda: 'Agenda',
    notes: 'Notes',
    recording: 'Recording',
    minutes: 'minutes',
    hour: 'hour',
    hours: 'hours',
    participants: 'Participants',
    required: 'Required',
    optional: 'Optional',
    accepted: 'Accepted',
    declined: 'Declined',
    pending: 'Pending'
  }
}

export default function Meetings({ language }: MeetingsProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [refreshing, setRefreshing] = useState(false)
  const t = translations[language]
  const isRTL = language === 'ar'

  const meetingStats = {
    totalMeetings: 156,
    todayMeetings: 8,
    upcomingMeetings: 23,
    completedMeetings: 125
  }

  const meetings = [
    {
      id: 1,
      title: 'اجتماع فريق التطوير الأسبوعي',
      titleEn: 'Weekly Development Team Meeting',
      dateTime: '2024-01-25 10:00',
      duration: 60,
      attendees: 8,
      location: 'قاعة الاجتماعات A',
      locationEn: 'Conference Room A',
      type: 'inPerson',
      status: 'scheduled',
      organizer: 'أحمد محمد',
      agenda: 'مراجعة التقدم الأسبوعي، مناقشة المشاكل، التخطيط للأسبوع القادم',
      participants: [
        { name: 'فاطمة علي', status: 'accepted', required: true },
        { name: 'محمد حسن', status: 'accepted', required: true },
        { name: 'سارة أحمد', status: 'pending', required: false }
      ],
      hasRecording: false,
      meetingLink: null
    },
    {
      id: 2,
      title: 'مراجعة الميزانية الربعية',
      titleEn: 'Quarterly Budget Review',
      dateTime: '2024-01-25 14:00',
      duration: 90,
      attendees: 5,
      location: 'اجتماع افتراضي',
      locationEn: 'Virtual Meeting',
      type: 'virtual',
      status: 'ongoing',
      organizer: 'فاطمة علي',
      agenda: 'مراجعة الأداء المالي، تحليل الانحرافات، التخطيط للربع القادم',
      participants: [
        { name: 'أحمد محمد', status: 'accepted', required: true },
        { name: 'محمد حسن', status: 'accepted', required: true },
        { name: 'علي حسين', status: 'accepted', required: true }
      ],
      hasRecording: true,
      meetingLink: 'https://meet.numu.sa/budget-review'
    },
    {
      id: 3,
      title: 'تدريب أمان المعلومات',
      titleEn: 'Information Security Training',
      dateTime: '2024-01-24 09:00',
      duration: 120,
      attendees: 25,
      location: 'القاعة الكبرى',
      locationEn: 'Main Hall',
      type: 'hybrid',
      status: 'completed',
      organizer: 'محمد حسن',
      agenda: 'أساسيات أمان المعلومات، السياسات الجديدة، أفضل الممارسات',
      participants: [
        { name: 'جميع الموظفين', status: 'accepted', required: true }
      ],
      hasRecording: true,
      meetingLink: 'https://meet.numu.sa/security-training'
    },
    {
      id: 4,
      title: 'اجتماع مجلس الإدارة',
      titleEn: 'Board Meeting',
      dateTime: '2024-01-26 16:00',
      duration: 180,
      attendees: 7,
      location: 'قاعة مجلس الإدارة',
      locationEn: 'Board Room',
      type: 'inPerson',
      status: 'scheduled',
      organizer: 'سارة أحمد',
      agenda: 'مراجعة الأداء السنوي، الاستراتيجية المستقبلية، الموافقة على الميزانية',
      participants: [
        { name: 'أعضاء مجلس الإدارة', status: 'accepted', required: true }
      ],
      hasRecording: false,
      meetingLink: null
    }
  ]

  const handleRefresh = async () => {
    setRefreshing(true)
    await new Promise(resolve => setTimeout(resolve, 1000))
    setRefreshing(false)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'ongoing':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'completed':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'scheduled':
        return <Clock className="h-4 w-4 text-blue-500" />
      case 'ongoing':
        return <PlayCircle className="h-4 w-4 text-green-500" />
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-gray-500" />
      case 'cancelled':
        return <AlertTriangle className="h-4 w-4 text-red-500" />
      default:
        return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'virtual':
        return <Monitor className="h-4 w-4 text-blue-400" />
      case 'inPerson':
        return <MapPin className="h-4 w-4 text-green-400" />
      case 'hybrid':
        return <Video className="h-4 w-4 text-purple-400" />
      default:
        return <Video className="h-4 w-4 text-gray-400" />
    }
  }

  const formatDuration = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes} ${t.minutes}`
    } else {
      const hours = Math.floor(minutes / 60)
      const remainingMinutes = minutes % 60
      if (remainingMinutes === 0) {
        return `${hours} ${hours === 1 ? t.hour : t.hours}`
      } else {
        return `${hours} ${hours === 1 ? t.hour : t.hours} ${remainingMinutes} ${t.minutes}`
      }
    }
  }

  const formatDateTime = (dateTime: string) => {
    const date = new Date(dateTime)
    return date.toLocaleString(language === 'ar' ? 'ar-SA' : 'en-US', {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">{t.meetings}</h1>
          <p className="text-white/70">جدولة وإدارة الاجتماعات والمؤتمرات</p>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={handleRefresh}
            disabled={refreshing}
            className="glass-button"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            {t.refresh}
          </Button>
          <Button className="glass-button">
            <Plus className="h-4 w-4 mr-2" />
            {t.scheduleMeeting}
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.totalMeetings}</p>
                <p className="text-2xl font-bold text-white">{meetingStats.totalMeetings}</p>
              </div>
              <Video className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.todayMeetings}</p>
                <p className="text-2xl font-bold text-green-400">{meetingStats.todayMeetings}</p>
              </div>
              <Calendar className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.upcomingMeetings}</p>
                <p className="text-2xl font-bold text-purple-400">{meetingStats.upcomingMeetings}</p>
              </div>
              <Clock className="h-8 w-8 text-purple-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.completedMeetings}</p>
                <p className="text-2xl font-bold text-gray-400">{meetingStats.completedMeetings}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-gray-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Meetings List */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <CardTitle className="text-white text-xl">الاجتماعات</CardTitle>
              <CardDescription className="text-white/70">
                قائمة بجميع الاجتماعات المجدولة والمكتملة
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 h-4 w-4" />
                <Input
                  placeholder={t.search}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 glass-input"
                />
              </div>
              <Button variant="outline" className="glass-button">
                <Filter className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {meetings.map((meeting) => (
              <div key={meeting.id} className="p-4 glass-card border-white/10 hover:border-white/30 transition-all">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-3">
                      <h4 className="text-white font-semibold">
                        {language === 'ar' ? meeting.title : meeting.titleEn}
                      </h4>
                      <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(meeting.status)}`}>
                        {getStatusIcon(meeting.status)}
                        {t[meeting.status as keyof typeof t]}
                      </div>
                      {getTypeIcon(meeting.type)}
                      {meeting.hasRecording && (
                        <Video className="h-4 w-4 text-red-400" />
                      )}
                    </div>
                    
                    <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 text-sm text-white/80 mb-3">
                      <div className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        <span>{formatDateTime(meeting.dateTime)}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        <span>{formatDuration(meeting.duration)}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Users className="h-3 w-3" />
                        <span>{meeting.attendees} مشارك</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <MapPin className="h-3 w-3" />
                        <span>{language === 'ar' ? meeting.location : meeting.locationEn}</span>
                      </div>
                    </div>

                    <div className="text-sm text-white/70 mb-3">
                      <div className="flex items-center gap-1 mb-1">
                        <User className="h-3 w-3" />
                        <span className="text-white/60">المنظم: </span>
                        <span>{meeting.organizer}</span>
                      </div>
                      <p className="text-white/80 line-clamp-2">{meeting.agenda}</p>
                    </div>

                    {/* Participants */}
                    <div className="flex flex-wrap gap-2">
                      {meeting.participants.slice(0, 3).map((participant, index) => (
                        <div key={index} className="flex items-center gap-1 bg-white/10 px-2 py-1 rounded-full text-xs">
                          <div className={`w-2 h-2 rounded-full ${
                            participant.status === 'accepted' ? 'bg-green-400' :
                            participant.status === 'declined' ? 'bg-red-400' : 'bg-yellow-400'
                          }`}></div>
                          <span className="text-white/80">{participant.name}</span>
                        </div>
                      ))}
                      {meeting.participants.length > 3 && (
                        <div className="bg-white/10 px-2 py-1 rounded-full text-xs text-white/80">
                          +{meeting.participants.length - 3} أكثر
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex gap-2">
                    {meeting.status === 'ongoing' && meeting.meetingLink && (
                      <Button size="sm" className="glass-button bg-green-500/20 text-green-400">
                        <Video className="h-3 w-3 mr-1" />
                        {t.joinMeeting}
                      </Button>
                    )}
                    <Button size="sm" className="glass-button">
                      <Eye className="h-3 w-3 mr-1" />
                      {t.viewMeeting}
                    </Button>
                    <Button size="sm" variant="outline" className="glass-button">
                      <Edit className="h-3 w-3" />
                    </Button>
                    {meeting.meetingLink && (
                      <Button size="sm" variant="outline" className="glass-button">
                        <Copy className="h-3 w-3" />
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
