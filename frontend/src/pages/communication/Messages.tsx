import { useState, useEffect, useRef } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  MessageSquare,
  Send,
  Search,
  Filter,
  Paperclip,
  Smile,
  MoreVertical,
  Phone,
  Video,
  Info,
  Star,
  Archive,
  Trash2,
  Users,
  Circle,
  CheckCheck,
  Clock,
  Plus,
  Mic,
  Image,
  File
} from 'lucide-react'

interface MessagesProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    messages: 'الرسائل',
    newMessage: 'رسالة جديدة',
    search: 'بحث',
    filter: 'تصفية',
    typeMessage: 'اكتب رسالة...',
    send: 'إرسال',
    online: 'متصل',
    offline: 'غير متصل',
    lastSeen: 'آخر ظهور',
    typing: 'يكتب...',
    delivered: 'تم التسليم',
    read: 'تم القراءة',
    sent: 'تم الإرسال',
    attachFile: 'إرفاق ملف',
    attachImage: 'إرفاق صورة',
    voiceMessage: 'رسالة صوتية',
    videoCall: 'مكالمة فيديو',
    voiceCall: 'مكالمة صوتية',
    contactInfo: 'معلومات الاتصال',
    starMessage: 'تمييز الرسالة',
    archiveChat: 'أرشفة المحادثة',
    deleteChat: 'حذف المحادثة',
    groupChat: 'محادثة جماعية',
    addParticipants: 'إضافة مشاركين',
    chatSettings: 'إعدادات المحادثة',
    muteNotifications: 'كتم الإشعارات',
    clearHistory: 'مسح التاريخ',
    blockUser: 'حظر المستخدم',
    reportUser: 'الإبلاغ عن المستخدم',
    today: 'اليوم',
    yesterday: 'أمس',
    thisWeek: 'هذا الأسبوع',
    unreadMessages: 'رسائل غير مقروءة',
    allChats: 'جميع المحادثات',
    groups: 'المجموعات',
    archived: 'المؤرشفة',
    starred: 'المميزة'
  },
  en: {
    messages: 'Messages',
    newMessage: 'New Message',
    search: 'Search',
    filter: 'Filter',
    typeMessage: 'Type a message...',
    send: 'Send',
    online: 'Online',
    offline: 'Offline',
    lastSeen: 'Last seen',
    typing: 'Typing...',
    delivered: 'Delivered',
    read: 'Read',
    sent: 'Sent',
    attachFile: 'Attach File',
    attachImage: 'Attach Image',
    voiceMessage: 'Voice Message',
    videoCall: 'Video Call',
    voiceCall: 'Voice Call',
    contactInfo: 'Contact Info',
    starMessage: 'Star Message',
    archiveChat: 'Archive Chat',
    deleteChat: 'Delete Chat',
    groupChat: 'Group Chat',
    addParticipants: 'Add Participants',
    chatSettings: 'Chat Settings',
    muteNotifications: 'Mute Notifications',
    clearHistory: 'Clear History',
    blockUser: 'Block User',
    reportUser: 'Report User',
    today: 'Today',
    yesterday: 'Yesterday',
    thisWeek: 'This Week',
    unreadMessages: 'Unread Messages',
    allChats: 'All Chats',
    groups: 'Groups',
    archived: 'Archived',
    starred: 'Starred'
  }
}

export default function Messages({ language }: MessagesProps) {
  const [selectedChat, setSelectedChat] = useState<number | null>(1)
  const [messageText, setMessageText] = useState('')
  const [searchTerm, setSearchTerm] = useState('')
  const [activeTab, setActiveTab] = useState('all')
  const [isTyping, setIsTyping] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const t = translations[language]
  const isRTL = language === 'ar'

  // Mock data
  const chats = [
    {
      id: 1,
      name: 'أحمد محمد',
      avatar: '👨‍💻',
      lastMessage: 'شكراً لك على التقرير، سأراجعه اليوم',
      lastMessageTime: '10:30 ص',
      unreadCount: 2,
      isOnline: true,
      isTyping: false,
      isGroup: false,
      department: 'تقنية المعلومات'
    },
    {
      id: 2,
      name: 'فريق التطوير',
      avatar: '👥',
      lastMessage: 'فاطمة: تم الانتهاء من المرحلة الأولى',
      lastMessageTime: '09:45 ص',
      unreadCount: 5,
      isOnline: false,
      isTyping: true,
      isGroup: true,
      participants: ['أحمد محمد', 'فاطمة علي', 'محمد حسن', 'سارة أحمد']
    },
    {
      id: 3,
      name: 'فاطمة علي',
      avatar: '👩‍💼',
      lastMessage: 'هل يمكننا مناقشة الميزانية غداً؟',
      lastMessageTime: 'أمس',
      unreadCount: 0,
      isOnline: true,
      isTyping: false,
      isGroup: false,
      department: 'الموارد البشرية'
    },
    {
      id: 4,
      name: 'محمد حسن',
      avatar: '👨‍💼',
      lastMessage: 'تم إرسال التقرير المالي',
      lastMessageTime: 'أمس',
      unreadCount: 1,
      isOnline: false,
      isTyping: false,
      isGroup: false,
      department: 'المالية'
    }
  ]

  const messages = [
    {
      id: 1,
      senderId: 1,
      senderName: 'أحمد محمد',
      content: 'مرحباً، كيف حال المشروع؟',
      timestamp: '09:30 ص',
      status: 'read',
      type: 'text'
    },
    {
      id: 2,
      senderId: 'me',
      senderName: 'أنت',
      content: 'المشروع يسير بشكل جيد، سأرسل لك التحديث قريباً',
      timestamp: '09:32 ص',
      status: 'read',
      type: 'text'
    },
    {
      id: 3,
      senderId: 1,
      senderName: 'أحمد محمد',
      content: 'ممتاز! هل تحتاج أي مساعدة إضافية؟',
      timestamp: '09:35 ص',
      status: 'read',
      type: 'text'
    },
    {
      id: 4,
      senderId: 'me',
      senderName: 'أنت',
      content: 'شكراً، كل شيء تحت السيطرة حالياً',
      timestamp: '09:37 ص',
      status: 'delivered',
      type: 'text'
    },
    {
      id: 5,
      senderId: 1,
      senderName: 'أحمد محمد',
      content: 'شكراً لك على التقرير، سأراجعه اليوم',
      timestamp: '10:30 ص',
      status: 'sent',
      type: 'text'
    }
  ]

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  // Simulate typing indicator
  useEffect(() => {
    if (messageText) {
      setIsTyping(true)
      const timer = setTimeout(() => setIsTyping(false), 1000)
      return () => clearTimeout(timer)
    }
  }, [messageText])

  const handleSendMessage = () => {
    if (messageText.trim()) {
      // Add message logic here
      setMessageText('')
    }
  }

  const getMessageStatusIcon = (status: string) => {
    switch (status) {
      case 'sent':
        return <CheckCheck className="h-3 w-3 text-gray-400" />
      case 'delivered':
        return <CheckCheck className="h-3 w-3 text-blue-400" />
      case 'read':
        return <CheckCheck className="h-3 w-3 text-green-400" />
      default:
        return <Clock className="h-3 w-3 text-gray-400" />
    }
  }

  const selectedChatData = chats.find(chat => chat.id === selectedChat)

  return (
    <div className={`h-[calc(100vh-8rem)] ${isRTL ? 'rtl' : 'ltr'}`}>
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 h-full">
        {/* Chat List Sidebar */}
        <div className="lg:col-span-1">
          <Card className="glass-card border-white/20 h-full">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-white text-xl">{t.messages}</CardTitle>
                <Button size="sm" className="glass-button">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 h-4 w-4" />
                <Input
                  placeholder={t.search}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 glass-input"
                />
              </div>
            </CardHeader>
            
            {/* Chat Tabs */}
            <div className="px-6 pb-3">
              <div className="flex space-x-1 bg-white/10 p-1 rounded-lg">
                {['all', 'groups', 'starred'].map((tab) => (
                  <button
                    key={tab}
                    onClick={() => setActiveTab(tab)}
                    className={`flex-1 py-1 px-2 rounded-md text-xs font-medium transition-all ${
                      activeTab === tab
                        ? 'bg-white/20 text-white shadow-lg'
                        : 'text-white/70 hover:text-white hover:bg-white/10'
                    }`}
                  >
                    {t[tab as keyof typeof t]}
                  </button>
                ))}
              </div>
            </div>

            <CardContent className="p-0 flex-1 overflow-y-auto">
              <div className="space-y-1">
                {chats.map((chat) => (
                  <div
                    key={chat.id}
                    onClick={() => setSelectedChat(chat.id)}
                    className={`flex items-center gap-3 p-4 mx-3 rounded-xl cursor-pointer transition-all ${
                      selectedChat === chat.id
                        ? 'glass-card border-white/30'
                        : 'hover:bg-white/5'
                    }`}
                  >
                    <div className="relative">
                      <div className="w-12 h-12 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-xl">
                        {chat.avatar}
                      </div>
                      {chat.isOnline && (
                        <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-gray-800"></div>
                      )}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <h4 className="text-white font-medium truncate">{chat.name}</h4>
                        <span className="text-white/60 text-xs">{chat.lastMessageTime}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <p className="text-white/70 text-sm truncate">
                          {chat.isTyping ? (
                            <span className="text-blue-400 italic">{t.typing}</span>
                          ) : (
                            chat.lastMessage
                          )}
                        </p>
                        {chat.unreadCount > 0 && (
                          <div className="bg-blue-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                            {chat.unreadCount}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Chat Area */}
        <div className="lg:col-span-3">
          {selectedChatData ? (
            <Card className="glass-card border-white/20 h-full flex flex-col">
              {/* Chat Header */}
              <CardHeader className="pb-3 border-b border-white/10">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="relative">
                      <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-lg">
                        {selectedChatData.avatar}
                      </div>
                      {selectedChatData.isOnline && (
                        <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-400 rounded-full border-2 border-gray-800"></div>
                      )}
                    </div>
                    <div>
                      <h3 className="text-white font-semibold">{selectedChatData.name}</h3>
                      <p className="text-white/60 text-sm">
                        {selectedChatData.isOnline ? t.online : `${t.lastSeen} 2h ago`}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Button size="sm" variant="outline" className="glass-button">
                      <Phone className="h-4 w-4" />
                    </Button>
                    <Button size="sm" variant="outline" className="glass-button">
                      <Video className="h-4 w-4" />
                    </Button>
                    <Button size="sm" variant="outline" className="glass-button">
                      <Info className="h-4 w-4" />
                    </Button>
                    <Button size="sm" variant="outline" className="glass-button">
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>

              {/* Messages Area */}
              <CardContent className="flex-1 overflow-y-auto p-4 space-y-4">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${message.senderId === 'me' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`max-w-xs lg:max-w-md px-4 py-2 rounded-2xl ${
                        message.senderId === 'me'
                          ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white'
                          : 'glass-card border-white/20 text-white'
                      }`}
                    >
                      <p className="text-sm">{message.content}</p>
                      <div className={`flex items-center gap-1 mt-1 ${
                        message.senderId === 'me' ? 'justify-end' : 'justify-start'
                      }`}>
                        <span className="text-xs opacity-70">{message.timestamp}</span>
                        {message.senderId === 'me' && getMessageStatusIcon(message.status)}
                      </div>
                    </div>
                  </div>
                ))}
                <div ref={messagesEndRef} />
              </CardContent>

              {/* Message Input */}
              <div className="p-4 border-t border-white/10">
                <div className="flex items-center gap-2">
                  <Button size="sm" variant="outline" className="glass-button">
                    <Paperclip className="h-4 w-4" />
                  </Button>
                  <Button size="sm" variant="outline" className="glass-button">
                    <Image className="h-4 w-4" />
                  </Button>
                  <div className="flex-1 relative">
                    <Input
                      placeholder={t.typeMessage}
                      value={messageText}
                      onChange={(e) => setMessageText(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                      className="glass-input pr-12"
                    />
                    <Button
                      size="sm"
                      variant="outline"
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 glass-button"
                    >
                      <Smile className="h-4 w-4" />
                    </Button>
                  </div>
                  <Button size="sm" variant="outline" className="glass-button">
                    <Mic className="h-4 w-4" />
                  </Button>
                  <Button 
                    onClick={handleSendMessage}
                    className="glass-button"
                    disabled={!messageText.trim()}
                  >
                    <Send className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </Card>
          ) : (
            <Card className="glass-card border-white/20 h-full flex items-center justify-center">
              <div className="text-center">
                <MessageSquare className="h-16 w-16 text-white/40 mx-auto mb-4" />
                <h3 className="text-white text-xl font-semibold mb-2">اختر محادثة</h3>
                <p className="text-white/60">اختر محادثة من القائمة لبدء المراسلة</p>
              </div>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}
