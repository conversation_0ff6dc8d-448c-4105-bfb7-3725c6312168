import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Badge } from '../../components/ui/badge'
import { 
  Shield, 
  Plus, 
  Search, 
  Eye, 
  Edit, 
  Trash2,
  AlertTriangle,
  CheckCircle,
  Clock,
  FileText,
  Download,
  Calendar,
  TrendingUp,
  Users,
  Target
} from 'lucide-react'

interface ComplianceAuditProps {
  language: 'ar' | 'en'
}

interface ComplianceItem {
  id: string
  title: string
  titleAr: string
  description: string
  descriptionAr: string
  category: string
  categoryAr: string
  regulation: string
  regulationAr: string
  status: 'compliant' | 'non-compliant' | 'pending' | 'in-review' | 'expired'
  priority: 'low' | 'medium' | 'high' | 'critical'
  lastAuditDate: string
  nextAuditDate: string
  auditor: string
  auditorAr: string
  department: string
  departmentAr: string
  findings: string
  findingsAr: string
  actionRequired: string
  actionRequiredAr: string
  dueDate: string
  assignedTo: string
  assignedToAr: string
  evidenceFiles: string[]
  riskLevel: 'low' | 'medium' | 'high' | 'critical'
  complianceScore: number
}

const mockComplianceItems: ComplianceItem[] = [
  {
    id: '1',
    title: 'Data Protection Compliance',
    titleAr: 'امتثال حماية البيانات',
    description: 'GDPR and local data protection regulations compliance',
    descriptionAr: 'امتثال لوائح حماية البيانات العامة والمحلية',
    category: 'Data Security',
    categoryAr: 'أمان البيانات',
    regulation: 'GDPR / Saudi Data Protection Law',
    regulationAr: 'قانون حماية البيانات العام / قانون حماية البيانات السعودي',
    status: 'compliant',
    priority: 'high',
    lastAuditDate: '2024-01-15',
    nextAuditDate: '2024-07-15',
    auditor: 'Sarah Hassan',
    auditorAr: 'سارة حسن',
    department: 'IT Security',
    departmentAr: 'أمان تقنية المعلومات',
    findings: 'All data protection measures are in place and functioning properly',
    findingsAr: 'جميع تدابير حماية البيانات موجودة وتعمل بشكل صحيح',
    actionRequired: 'Continue monitoring and update policies annually',
    actionRequiredAr: 'مواصلة المراقبة وتحديث السياسات سنوياً',
    dueDate: '2024-12-31',
    assignedTo: 'IT Security Team',
    assignedToAr: 'فريق أمان تقنية المعلومات',
    evidenceFiles: ['gdpr-compliance-report.pdf', 'data-audit-2024.xlsx'],
    riskLevel: 'low',
    complianceScore: 95
  },
  {
    id: '2',
    title: 'Financial Reporting Standards',
    titleAr: 'معايير التقارير المالية',
    description: 'Compliance with IFRS and local accounting standards',
    descriptionAr: 'الامتثال للمعايير الدولية للتقارير المالية والمعايير المحاسبية المحلية',
    category: 'Financial',
    categoryAr: 'مالية',
    regulation: 'IFRS / SOCPA Standards',
    regulationAr: 'المعايير الدولية للتقارير المالية / معايير الهيئة السعودية للمحاسبين القانونيين',
    status: 'in-review',
    priority: 'high',
    lastAuditDate: '2024-01-10',
    nextAuditDate: '2024-04-10',
    auditor: 'Ahmed Al-Rashid',
    auditorAr: 'أحمد الراشد',
    department: 'Finance',
    departmentAr: 'المالية',
    findings: 'Minor discrepancies in quarterly reporting format',
    findingsAr: 'تباينات طفيفة في تنسيق التقارير الربع سنوية',
    actionRequired: 'Update reporting templates and train staff',
    actionRequiredAr: 'تحديث قوالب التقارير وتدريب الموظفين',
    dueDate: '2024-03-15',
    assignedTo: 'Finance Manager',
    assignedToAr: 'مدير المالية',
    evidenceFiles: ['financial-audit-q4.pdf'],
    riskLevel: 'medium',
    complianceScore: 82
  },
  {
    id: '3',
    title: 'Workplace Safety Standards',
    titleAr: 'معايير السلامة المهنية',
    description: 'Occupational health and safety compliance',
    descriptionAr: 'امتثال الصحة والسلامة المهنية',
    category: 'Health & Safety',
    categoryAr: 'الصحة والسلامة',
    regulation: 'OSHA / Saudi Labor Law',
    regulationAr: 'إدارة السلامة والصحة المهنية / قانون العمل السعودي',
    status: 'non-compliant',
    priority: 'critical',
    lastAuditDate: '2024-01-20',
    nextAuditDate: '2024-02-20',
    auditor: 'Omar Abdullah',
    auditorAr: 'عمر عبدالله',
    department: 'Operations',
    departmentAr: 'العمليات',
    findings: 'Missing emergency evacuation procedures and safety equipment',
    findingsAr: 'نقص في إجراءات الإخلاء الطارئ ومعدات السلامة',
    actionRequired: 'Immediate implementation of safety protocols and equipment installation',
    actionRequiredAr: 'التنفيذ الفوري لبروتوكولات السلامة وتركيب المعدات',
    dueDate: '2024-02-05',
    assignedTo: 'Operations Manager',
    assignedToAr: 'مدير العمليات',
    evidenceFiles: [],
    riskLevel: 'critical',
    complianceScore: 45
  },
  {
    id: '4',
    title: 'Anti-Money Laundering (AML)',
    titleAr: 'مكافحة غسل الأموال',
    description: 'AML policies and procedures compliance',
    descriptionAr: 'امتثال سياسات وإجراءات مكافحة غسل الأموال',
    category: 'Financial Crime',
    categoryAr: 'الجرائم المالية',
    regulation: 'SAMA AML Regulations',
    regulationAr: 'لوائح مؤسسة النقد العربي السعودي لمكافحة غسل الأموال',
    status: 'pending',
    priority: 'high',
    lastAuditDate: '2023-12-15',
    nextAuditDate: '2024-06-15',
    auditor: 'Fatima Mohammed',
    auditorAr: 'فاطمة محمد',
    department: 'Compliance',
    departmentAr: 'الامتثال',
    findings: 'Audit in progress - preliminary review shows good practices',
    findingsAr: 'التدقيق قيد التنفيذ - المراجعة الأولية تظهر ممارسات جيدة',
    actionRequired: 'Complete audit and update training materials',
    actionRequiredAr: 'إكمال التدقيق وتحديث مواد التدريب',
    dueDate: '2024-03-30',
    assignedTo: 'Compliance Officer',
    assignedToAr: 'مسؤول الامتثال',
    evidenceFiles: ['aml-procedures.pdf'],
    riskLevel: 'medium',
    complianceScore: 78
  }
]

export default function ComplianceAudit({ language }: ComplianceAuditProps) {
  const [complianceItems, setComplianceItems] = useState<ComplianceItem[]>(mockComplianceItems)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [categoryFilter, setCategoryFilter] = useState<string>('all')
  const [priorityFilter, setPriorityFilter] = useState<string>('all')

  const text = {
    ar: {
      title: 'الامتثال والتدقيق',
      description: 'إدارة ومراقبة الامتثال التنظيمي والتدقيق',
      newCompliance: 'عنصر امتثال جديد',
      search: 'البحث في عناصر الامتثال...',
      complianceTitle: 'عنوان الامتثال',
      category: 'الفئة',
      regulation: 'اللائحة',
      status: 'الحالة',
      priority: 'الأولوية',
      lastAudit: 'آخر تدقيق',
      nextAudit: 'التدقيق القادم',
      auditor: 'المدقق',
      department: 'القسم',
      riskLevel: 'مستوى المخاطر',
      complianceScore: 'نقاط الامتثال',
      actions: 'الإجراءات',
      view: 'عرض',
      edit: 'تعديل',
      delete: 'حذف',
      download: 'تحميل',
      totalItems: 'إجمالي العناصر',
      compliantItems: 'العناصر المتوافقة',
      avgScore: 'متوسط النقاط',
      criticalIssues: 'القضايا الحرجة',
      compliant: 'متوافق',
      'non-compliant': 'غير متوافق',
      pending: 'معلق',
      'in-review': 'قيد المراجعة',
      expired: 'منتهي',
      low: 'منخفض',
      medium: 'متوسط',
      high: 'عالي',
      critical: 'حرج',
      findings: 'النتائج',
      actionRequired: 'الإجراء المطلوب',
      dueDate: 'تاريخ الاستحقاق',
      assignedTo: 'مُكلف إلى',
      evidence: 'الأدلة'
    },
    en: {
      title: 'Compliance & Audit',
      description: 'Manage and monitor regulatory compliance and auditing',
      newCompliance: 'New Compliance Item',
      search: 'Search compliance items...',
      complianceTitle: 'Compliance Title',
      category: 'Category',
      regulation: 'Regulation',
      status: 'Status',
      priority: 'Priority',
      lastAudit: 'Last Audit',
      nextAudit: 'Next Audit',
      auditor: 'Auditor',
      department: 'Department',
      riskLevel: 'Risk Level',
      complianceScore: 'Compliance Score',
      actions: 'Actions',
      view: 'View',
      edit: 'Edit',
      delete: 'Delete',
      download: 'Download',
      totalItems: 'Total Items',
      compliantItems: 'Compliant Items',
      avgScore: 'Average Score',
      criticalIssues: 'Critical Issues',
      compliant: 'Compliant',
      'non-compliant': 'Non-Compliant',
      pending: 'Pending',
      'in-review': 'In Review',
      expired: 'Expired',
      low: 'Low',
      medium: 'Medium',
      high: 'High',
      critical: 'Critical',
      findings: 'Findings',
      actionRequired: 'Action Required',
      dueDate: 'Due Date',
      assignedTo: 'Assigned To',
      evidence: 'Evidence'
    }
  }

  const t = text[language]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'compliant': return 'bg-green-500/20 text-green-300 border-green-500/30'
      case 'non-compliant': return 'bg-red-500/20 text-red-300 border-red-500/30'
      case 'pending': return 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30'
      case 'in-review': return 'bg-blue-500/20 text-blue-300 border-blue-500/30'
      case 'expired': return 'bg-gray-500/20 text-gray-300 border-gray-500/30'
      default: return 'bg-gray-500/20 text-gray-300 border-gray-500/30'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low': return 'bg-green-500/20 text-green-300 border-green-500/30'
      case 'medium': return 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30'
      case 'high': return 'bg-orange-500/20 text-orange-300 border-orange-500/30'
      case 'critical': return 'bg-red-500/20 text-red-300 border-red-500/30'
      default: return 'bg-gray-500/20 text-gray-300 border-gray-500/30'
    }
  }

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low': return 'bg-green-500/20 text-green-300'
      case 'medium': return 'bg-yellow-500/20 text-yellow-300'
      case 'high': return 'bg-orange-500/20 text-orange-300'
      case 'critical': return 'bg-red-500/20 text-red-300'
      default: return 'bg-gray-500/20 text-gray-300'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'compliant': return <CheckCircle className="w-4 h-4" />
      case 'non-compliant': return <AlertTriangle className="w-4 h-4" />
      case 'pending': return <Clock className="w-4 h-4" />
      case 'in-review': return <Eye className="w-4 h-4" />
      case 'expired': return <Calendar className="w-4 h-4" />
      default: return <Clock className="w-4 h-4" />
    }
  }

  const filteredItems = complianceItems.filter(item => {
    const matchesSearch = item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.titleAr.includes(searchTerm) ||
                         item.regulation.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.regulationAr.includes(searchTerm)
    const matchesStatus = statusFilter === 'all' || item.status === statusFilter
    const matchesCategory = categoryFilter === 'all' || item.category === categoryFilter
    const matchesPriority = priorityFilter === 'all' || item.priority === priorityFilter
    return matchesSearch && matchesStatus && matchesCategory && matchesPriority
  })

  const compliantItems = complianceItems.filter(item => item.status === 'compliant').length
  const avgScore = complianceItems.length > 0 
    ? complianceItems.reduce((sum, item) => sum + item.complianceScore, 0) / complianceItems.length 
    : 0
  const criticalIssues = complianceItems.filter(item => item.priority === 'critical' && item.status === 'non-compliant').length

  return (
    <div className="min-h-screen gradient-bg p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">{t.title}</h1>
            <p className="text-white/70">{t.description}</p>
          </div>
          <div className="flex gap-3">
            <Button variant="outline" className="glass-button">
              <Download className="w-4 h-4 mr-2" />
              تقرير الامتثال
            </Button>
            <Button className="bg-gradient-to-r from-sky-500 to-blue-600 hover:from-sky-600 hover:to-blue-700">
              <Plus className="w-4 h-4 mr-2" />
              {t.newCompliance}
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="glass-card border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.totalItems}</p>
                  <p className="text-2xl font-bold text-white">{complianceItems.length}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-sky-500/20 flex items-center justify-center">
                  <Shield className="w-6 h-6 text-sky-400" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-card border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.compliantItems}</p>
                  <p className="text-2xl font-bold text-white">{compliantItems}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-green-500/20 flex items-center justify-center">
                  <CheckCircle className="w-6 h-6 text-green-400" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-card border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.avgScore}</p>
                  <p className="text-2xl font-bold text-white">{avgScore.toFixed(1)}%</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-purple-500/20 flex items-center justify-center">
                  <Target className="w-6 h-6 text-purple-400" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-card border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.criticalIssues}</p>
                  <p className="text-2xl font-bold text-white">{criticalIssues}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-red-500/20 flex items-center justify-center">
                  <AlertTriangle className="w-6 h-6 text-red-400" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 w-4 h-4" />
                  <Input
                    placeholder={t.search}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 glass-input"
                  />
                </div>
              </div>
              <div className="flex gap-4">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="glass-input px-3 py-2 rounded-lg"
                >
                  <option value="all">جميع الحالات</option>
                  <option value="compliant">{t.compliant}</option>
                  <option value="non-compliant">{t['non-compliant']}</option>
                  <option value="pending">{t.pending}</option>
                  <option value="in-review">{t['in-review']}</option>
                  <option value="expired">{t.expired}</option>
                </select>
                <select
                  value={categoryFilter}
                  onChange={(e) => setCategoryFilter(e.target.value)}
                  className="glass-input px-3 py-2 rounded-lg"
                >
                  <option value="all">جميع الفئات</option>
                  <option value="Data Security">أمان البيانات</option>
                  <option value="Financial">مالية</option>
                  <option value="Health & Safety">الصحة والسلامة</option>
                  <option value="Financial Crime">الجرائم المالية</option>
                </select>
                <select
                  value={priorityFilter}
                  onChange={(e) => setPriorityFilter(e.target.value)}
                  className="glass-input px-3 py-2 rounded-lg"
                >
                  <option value="all">جميع الأولويات</option>
                  <option value="low">{t.low}</option>
                  <option value="medium">{t.medium}</option>
                  <option value="high">{t.high}</option>
                  <option value="critical">{t.critical}</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Compliance Table */}
        <Card className="glass-card border-white/20">
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-white/10">
                    <th className="text-right p-4 text-white/70 font-medium">{t.complianceTitle}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.category}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.status}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.priority}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.complianceScore}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.nextAudit}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.auditor}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.actions}</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredItems.map((item) => (
                    <tr key={item.id} className="border-b border-white/5 hover:bg-white/5">
                      <td className="p-4">
                        <div>
                          <span className="text-white font-medium">
                            {language === 'ar' ? item.titleAr : item.title}
                          </span>
                          <div className="text-xs text-white/60 mt-1">
                            {language === 'ar' ? item.regulationAr : item.regulation}
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <span className="text-white">{language === 'ar' ? item.categoryAr : item.category}</span>
                      </td>
                      <td className="p-4">
                        <Badge className={`${getStatusColor(item.status)} border flex items-center gap-1 w-fit`}>
                          {getStatusIcon(item.status)}
                          {t[item.status as keyof typeof t]}
                        </Badge>
                      </td>
                      <td className="p-4">
                        <Badge className={`${getPriorityColor(item.priority)} border`}>
                          {t[item.priority as keyof typeof t]}
                        </Badge>
                      </td>
                      <td className="p-4">
                        <div className="flex items-center gap-2">
                          <span className="text-white font-medium">{item.complianceScore}%</span>
                          <div className={`w-2 h-2 rounded-full ${getRiskColor(item.riskLevel)}`}></div>
                        </div>
                      </td>
                      <td className="p-4">
                        <span className="text-white/70">{item.nextAuditDate}</span>
                      </td>
                      <td className="p-4">
                        <span className="text-white">{language === 'ar' ? item.auditorAr : item.auditor}</span>
                      </td>
                      <td className="p-4">
                        <div className="flex gap-2">
                          <Button size="sm" variant="ghost" className="text-white/70 hover:text-white">
                            <Eye className="w-4 h-4" />
                          </Button>
                          <Button size="sm" variant="ghost" className="text-white/70 hover:text-white">
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button size="sm" variant="ghost" className="text-blue-400 hover:text-blue-300">
                            <Download className="w-4 h-4" />
                          </Button>
                          <Button size="sm" variant="ghost" className="text-red-400 hover:text-red-300">
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
