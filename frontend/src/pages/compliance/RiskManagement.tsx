import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Badge } from '../../components/ui/badge'
import { 
  AlertTriangle, 
  Plus, 
  Search, 
  Eye, 
  Edit, 
  Trash2,
  Shield,
  TrendingUp,
  Target,
  Clock,
  CheckCircle,
  XCircle,
  Activity,
  BarChart3
} from 'lucide-react'

interface RiskManagementProps {
  language: 'ar' | 'en'
}

interface Risk {
  id: string
  title: string
  titleAr: string
  description: string
  descriptionAr: string
  category: string
  categoryAr: string
  probability: 'very-low' | 'low' | 'medium' | 'high' | 'very-high'
  impact: 'very-low' | 'low' | 'medium' | 'high' | 'very-high'
  riskLevel: 'low' | 'medium' | 'high' | 'critical'
  status: 'identified' | 'assessed' | 'mitigated' | 'monitored' | 'closed'
  owner: string
  ownerAr: string
  department: string
  departmentAr: string
  identifiedDate: string
  lastReviewDate: string
  nextReviewDate: string
  mitigationPlan: string
  mitigationPlanAr: string
  mitigationStatus: 'not-started' | 'in-progress' | 'completed' | 'overdue'
  residualRisk: 'low' | 'medium' | 'high' | 'critical'
  cost: number
  likelihood: number // 1-5
  severity: number // 1-5
  riskScore: number
}

const mockRisks: Risk[] = [
  {
    id: '1',
    title: 'Data Breach Risk',
    titleAr: 'مخاطر اختراق البيانات',
    description: 'Risk of unauthorized access to sensitive customer and employee data',
    descriptionAr: 'مخاطر الوصول غير المصرح به إلى بيانات العملاء والموظفين الحساسة',
    category: 'Cybersecurity',
    categoryAr: 'الأمن السيبراني',
    probability: 'medium',
    impact: 'very-high',
    riskLevel: 'high',
    status: 'mitigated',
    owner: 'IT Security Manager',
    ownerAr: 'مدير أمان تقنية المعلومات',
    department: 'IT',
    departmentAr: 'تقنية المعلومات',
    identifiedDate: '2024-01-10',
    lastReviewDate: '2024-01-20',
    nextReviewDate: '2024-04-20',
    mitigationPlan: 'Implement multi-factor authentication, regular security audits, and employee training',
    mitigationPlanAr: 'تنفيذ المصادقة متعددة العوامل والتدقيق الأمني المنتظم وتدريب الموظفين',
    mitigationStatus: 'in-progress',
    residualRisk: 'medium',
    cost: 50000,
    likelihood: 3,
    severity: 5,
    riskScore: 15
  },
  {
    id: '2',
    title: 'Key Personnel Departure',
    titleAr: 'مغادرة الموظفين الأساسيين',
    description: 'Risk of losing critical employees and institutional knowledge',
    descriptionAr: 'مخاطر فقدان الموظفين الحيويين والمعرفة المؤسسية',
    category: 'Human Resources',
    categoryAr: 'الموارد البشرية',
    probability: 'medium',
    impact: 'high',
    riskLevel: 'medium',
    status: 'assessed',
    owner: 'HR Manager',
    ownerAr: 'مدير الموارد البشرية',
    department: 'HR',
    departmentAr: 'الموارد البشرية',
    identifiedDate: '2024-01-05',
    lastReviewDate: '2024-01-15',
    nextReviewDate: '2024-03-15',
    mitigationPlan: 'Succession planning, knowledge documentation, competitive retention packages',
    mitigationPlanAr: 'تخطيط الخلافة وتوثيق المعرفة وحزم الاحتفاظ التنافسية',
    mitigationStatus: 'not-started',
    residualRisk: 'medium',
    cost: 30000,
    likelihood: 3,
    severity: 4,
    riskScore: 12
  },
  {
    id: '3',
    title: 'Supply Chain Disruption',
    titleAr: 'اضطراب سلسلة التوريد',
    description: 'Risk of supplier failures affecting business operations',
    descriptionAr: 'مخاطر فشل الموردين التي تؤثر على العمليات التجارية',
    category: 'Operations',
    categoryAr: 'العمليات',
    probability: 'high',
    impact: 'high',
    riskLevel: 'critical',
    status: 'identified',
    owner: 'Operations Manager',
    ownerAr: 'مدير العمليات',
    department: 'Operations',
    departmentAr: 'العمليات',
    identifiedDate: '2024-01-12',
    lastReviewDate: '2024-01-18',
    nextReviewDate: '2024-02-18',
    mitigationPlan: 'Diversify supplier base, establish backup suppliers, increase inventory buffers',
    mitigationPlanAr: 'تنويع قاعدة الموردين وإنشاء موردين احتياطيين وزيادة مخازن المخزون',
    mitigationStatus: 'not-started',
    residualRisk: 'high',
    cost: 75000,
    likelihood: 4,
    severity: 4,
    riskScore: 16
  },
  {
    id: '4',
    title: 'Regulatory Compliance Failure',
    titleAr: 'فشل الامتثال التنظيمي',
    description: 'Risk of non-compliance with industry regulations and standards',
    descriptionAr: 'مخاطر عدم الامتثال للوائح والمعايير الصناعية',
    category: 'Compliance',
    categoryAr: 'الامتثال',
    probability: 'low',
    impact: 'very-high',
    riskLevel: 'medium',
    status: 'monitored',
    owner: 'Compliance Officer',
    ownerAr: 'مسؤول الامتثال',
    department: 'Legal',
    departmentAr: 'القانونية',
    identifiedDate: '2024-01-08',
    lastReviewDate: '2024-01-22',
    nextReviewDate: '2024-04-22',
    mitigationPlan: 'Regular compliance audits, staff training, legal consultation',
    mitigationPlanAr: 'عمليات تدقيق الامتثال المنتظمة وتدريب الموظفين والاستشارة القانونية',
    mitigationStatus: 'completed',
    residualRisk: 'low',
    cost: 25000,
    likelihood: 2,
    severity: 5,
    riskScore: 10
  }
]

export default function RiskManagement({ language }: RiskManagementProps) {
  const [risks, setRisks] = useState<Risk[]>(mockRisks)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [categoryFilter, setCategoryFilter] = useState<string>('all')
  const [riskLevelFilter, setRiskLevelFilter] = useState<string>('all')

  const text = {
    ar: {
      title: 'إدارة المخاطر',
      description: 'تحديد وتقييم ومراقبة المخاطر المؤسسية',
      newRisk: 'مخاطرة جديدة',
      search: 'البحث في المخاطر...',
      riskTitle: 'عنوان المخاطرة',
      category: 'الفئة',
      probability: 'الاحتمالية',
      impact: 'التأثير',
      riskLevel: 'مستوى المخاطر',
      status: 'الحالة',
      owner: 'المسؤول',
      department: 'القسم',
      riskScore: 'نتيجة المخاطر',
      lastReview: 'آخر مراجعة',
      nextReview: 'المراجعة القادمة',
      actions: 'الإجراءات',
      view: 'عرض',
      edit: 'تعديل',
      delete: 'حذف',
      totalRisks: 'إجمالي المخاطر',
      criticalRisks: 'المخاطر الحرجة',
      avgRiskScore: 'متوسط نتيجة المخاطر',
      mitigatedRisks: 'المخاطر المخففة',
      'very-low': 'منخفض جداً',
      low: 'منخفض',
      medium: 'متوسط',
      high: 'عالي',
      'very-high': 'عالي جداً',
      critical: 'حرج',
      identified: 'محدد',
      assessed: 'مقيم',
      mitigated: 'مخفف',
      monitored: 'مراقب',
      closed: 'مغلق',
      'not-started': 'لم يبدأ',
      'in-progress': 'قيد التنفيذ',
      completed: 'مكتمل',
      overdue: 'متأخر',
      mitigationPlan: 'خطة التخفيف',
      mitigationStatus: 'حالة التخفيف',
      residualRisk: 'المخاطر المتبقية',
      cost: 'التكلفة',
      sar: 'ر.س'
    },
    en: {
      title: 'Risk Management',
      description: 'Identify, assess, and monitor organizational risks',
      newRisk: 'New Risk',
      search: 'Search risks...',
      riskTitle: 'Risk Title',
      category: 'Category',
      probability: 'Probability',
      impact: 'Impact',
      riskLevel: 'Risk Level',
      status: 'Status',
      owner: 'Owner',
      department: 'Department',
      riskScore: 'Risk Score',
      lastReview: 'Last Review',
      nextReview: 'Next Review',
      actions: 'Actions',
      view: 'View',
      edit: 'Edit',
      delete: 'Delete',
      totalRisks: 'Total Risks',
      criticalRisks: 'Critical Risks',
      avgRiskScore: 'Average Risk Score',
      mitigatedRisks: 'Mitigated Risks',
      'very-low': 'Very Low',
      low: 'Low',
      medium: 'Medium',
      high: 'High',
      'very-high': 'Very High',
      critical: 'Critical',
      identified: 'Identified',
      assessed: 'Assessed',
      mitigated: 'Mitigated',
      monitored: 'Monitored',
      closed: 'Closed',
      'not-started': 'Not Started',
      'in-progress': 'In Progress',
      completed: 'Completed',
      overdue: 'Overdue',
      mitigationPlan: 'Mitigation Plan',
      mitigationStatus: 'Mitigation Status',
      residualRisk: 'Residual Risk',
      cost: 'Cost',
      sar: 'SAR'
    }
  }

  const t = text[language]

  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'low': return 'bg-green-500/20 text-green-300 border-green-500/30'
      case 'medium': return 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30'
      case 'high': return 'bg-orange-500/20 text-orange-300 border-orange-500/30'
      case 'critical': return 'bg-red-500/20 text-red-300 border-red-500/30'
      default: return 'bg-gray-500/20 text-gray-300 border-gray-500/30'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'identified': return 'bg-blue-500/20 text-blue-300 border-blue-500/30'
      case 'assessed': return 'bg-purple-500/20 text-purple-300 border-purple-500/30'
      case 'mitigated': return 'bg-orange-500/20 text-orange-300 border-orange-500/30'
      case 'monitored': return 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30'
      case 'closed': return 'bg-green-500/20 text-green-300 border-green-500/30'
      default: return 'bg-gray-500/20 text-gray-300 border-gray-500/30'
    }
  }

  const getMitigationStatusColor = (status: string) => {
    switch (status) {
      case 'not-started': return 'bg-gray-500/20 text-gray-300'
      case 'in-progress': return 'bg-blue-500/20 text-blue-300'
      case 'completed': return 'bg-green-500/20 text-green-300'
      case 'overdue': return 'bg-red-500/20 text-red-300'
      default: return 'bg-gray-500/20 text-gray-300'
    }
  }

  const getRiskIcon = (level: string) => {
    switch (level) {
      case 'low': return <Shield className="w-4 h-4" />
      case 'medium': return <Activity className="w-4 h-4" />
      case 'high': return <AlertTriangle className="w-4 h-4" />
      case 'critical': return <XCircle className="w-4 h-4" />
      default: return <Shield className="w-4 h-4" />
    }
  }

  const filteredRisks = risks.filter(risk => {
    const matchesSearch = risk.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         risk.titleAr.includes(searchTerm) ||
                         risk.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         risk.descriptionAr.includes(searchTerm)
    const matchesStatus = statusFilter === 'all' || risk.status === statusFilter
    const matchesCategory = categoryFilter === 'all' || risk.category === categoryFilter
    const matchesRiskLevel = riskLevelFilter === 'all' || risk.riskLevel === riskLevelFilter
    return matchesSearch && matchesStatus && matchesCategory && matchesRiskLevel
  })

  const criticalRisks = risks.filter(risk => risk.riskLevel === 'critical').length
  const avgRiskScore = risks.length > 0 
    ? risks.reduce((sum, risk) => sum + risk.riskScore, 0) / risks.length 
    : 0
  const mitigatedRisks = risks.filter(risk => risk.status === 'mitigated' || risk.status === 'closed').length

  return (
    <div className="min-h-screen gradient-bg p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">{t.title}</h1>
            <p className="text-white/70">{t.description}</p>
          </div>
          <Button className="bg-gradient-to-r from-sky-500 to-blue-600 hover:from-sky-600 hover:to-blue-700">
            <Plus className="w-4 h-4 mr-2" />
            {t.newRisk}
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="glass-card border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.totalRisks}</p>
                  <p className="text-2xl font-bold text-white">{risks.length}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-sky-500/20 flex items-center justify-center">
                  <AlertTriangle className="w-6 h-6 text-sky-400" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-card border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.criticalRisks}</p>
                  <p className="text-2xl font-bold text-white">{criticalRisks}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-red-500/20 flex items-center justify-center">
                  <XCircle className="w-6 h-6 text-red-400" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-card border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.avgRiskScore}</p>
                  <p className="text-2xl font-bold text-white">{avgRiskScore.toFixed(1)}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-purple-500/20 flex items-center justify-center">
                  <BarChart3 className="w-6 h-6 text-purple-400" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-card border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.mitigatedRisks}</p>
                  <p className="text-2xl font-bold text-white">{mitigatedRisks}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-green-500/20 flex items-center justify-center">
                  <CheckCircle className="w-6 h-6 text-green-400" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 w-4 h-4" />
                  <Input
                    placeholder={t.search}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 glass-input"
                  />
                </div>
              </div>
              <div className="flex gap-4">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="glass-input px-3 py-2 rounded-lg"
                >
                  <option value="all">جميع الحالات</option>
                  <option value="identified">{t.identified}</option>
                  <option value="assessed">{t.assessed}</option>
                  <option value="mitigated">{t.mitigated}</option>
                  <option value="monitored">{t.monitored}</option>
                  <option value="closed">{t.closed}</option>
                </select>
                <select
                  value={categoryFilter}
                  onChange={(e) => setCategoryFilter(e.target.value)}
                  className="glass-input px-3 py-2 rounded-lg"
                >
                  <option value="all">جميع الفئات</option>
                  <option value="Cybersecurity">الأمن السيبراني</option>
                  <option value="Human Resources">الموارد البشرية</option>
                  <option value="Operations">العمليات</option>
                  <option value="Compliance">الامتثال</option>
                  <option value="Financial">المالية</option>
                </select>
                <select
                  value={riskLevelFilter}
                  onChange={(e) => setRiskLevelFilter(e.target.value)}
                  className="glass-input px-3 py-2 rounded-lg"
                >
                  <option value="all">جميع المستويات</option>
                  <option value="low">{t.low}</option>
                  <option value="medium">{t.medium}</option>
                  <option value="high">{t.high}</option>
                  <option value="critical">{t.critical}</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Risks Table */}
        <Card className="glass-card border-white/20">
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-white/10">
                    <th className="text-right p-4 text-white/70 font-medium">{t.riskTitle}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.category}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.riskLevel}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.status}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.riskScore}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.owner}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.nextReview}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.actions}</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredRisks.map((risk) => (
                    <tr key={risk.id} className="border-b border-white/5 hover:bg-white/5">
                      <td className="p-4">
                        <div>
                          <span className="text-white font-medium">
                            {language === 'ar' ? risk.titleAr : risk.title}
                          </span>
                          <div className="text-xs text-white/60 mt-1">
                            {language === 'ar' ? risk.departmentAr : risk.department}
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <span className="text-white">{language === 'ar' ? risk.categoryAr : risk.category}</span>
                      </td>
                      <td className="p-4">
                        <Badge className={`${getRiskLevelColor(risk.riskLevel)} border flex items-center gap-1`}>
                          {getRiskIcon(risk.riskLevel)}
                          {t[risk.riskLevel as keyof typeof t]}
                        </Badge>
                      </td>
                      <td className="p-4">
                        <Badge className={`${getStatusColor(risk.status)} border`}>
                          {t[risk.status as keyof typeof t]}
                        </Badge>
                      </td>
                      <td className="p-4">
                        <div className="flex items-center gap-2">
                          <span className="text-white font-semibold">{risk.riskScore}</span>
                          <div className="text-xs text-white/60">
                            ({risk.likelihood}×{risk.severity})
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <span className="text-white">{language === 'ar' ? risk.ownerAr : risk.owner}</span>
                      </td>
                      <td className="p-4">
                        <span className="text-white/70">{risk.nextReviewDate}</span>
                      </td>
                      <td className="p-4">
                        <div className="flex gap-2">
                          <Button size="sm" variant="ghost" className="text-white/70 hover:text-white">
                            <Eye className="w-4 h-4" />
                          </Button>
                          <Button size="sm" variant="ghost" className="text-white/70 hover:text-white">
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button size="sm" variant="ghost" className="text-red-400 hover:text-red-300">
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
