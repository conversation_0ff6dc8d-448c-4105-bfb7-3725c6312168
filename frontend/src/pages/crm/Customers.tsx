import { useState, useEffect } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Users,
  UserPlus,
  Search,
  Filter,
  Download,
  Eye,
  Edit,
  Phone,
  Mail,
  MapPin,
  Calendar,
  DollarSign,
  TrendingUp,
  Star,
  Building,
  MoreHorizontal
} from 'lucide-react'

interface CustomersProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    title: 'إدارة العملاء',
    subtitle: 'إدارة شاملة لقاعدة بيانات العملاء والعلاقات',
    totalCustomers: 'إجمالي العملاء',
    activeCustomers: 'العملاء النشطون',
    newCustomers: 'عملاء جدد',
    customerValue: 'قيمة العملاء',
    searchCustomers: 'البحث في العملاء...',
    addCustomer: 'إضافة عميل',
    exportData: 'تصدير البيانات',
    filterBy: 'تصفية حسب',
    customerType: 'نوع العميل',
    status: 'الحالة',
    customerList: 'قائمة العملاء',
    customerName: 'اسم العميل',
    company: 'الشركة',
    email: 'البريد الإلكتروني',
    phone: 'الهاتف',
    location: 'الموقع',
    joinDate: 'تاريخ الانضمام',
    totalOrders: 'إجمالي الطلبات',
    totalValue: 'إجمالي القيمة',
    lastOrder: 'آخر طلب',
    rating: 'التقييم',
    actions: 'الإجراءات',
    viewProfile: 'عرض الملف الشخصي',
    editCustomer: 'تعديل العميل',
    active: 'نشط',
    inactive: 'غير نشط',
    vip: 'عميل مميز',
    regular: 'عادي',
    corporate: 'شركة',
    individual: 'فرد',
    thisMonth: 'هذا الشهر',
    refresh: 'تحديث'
  },
  en: {
    title: 'Customer Management',
    subtitle: 'Comprehensive customer database and relationship management',
    totalCustomers: 'Total Customers',
    activeCustomers: 'Active Customers',
    newCustomers: 'New Customers',
    customerValue: 'Customer Value',
    searchCustomers: 'Search customers...',
    addCustomer: 'Add Customer',
    exportData: 'Export Data',
    filterBy: 'Filter By',
    customerType: 'Customer Type',
    status: 'Status',
    customerList: 'Customer List',
    customerName: 'Customer Name',
    company: 'Company',
    email: 'Email',
    phone: 'Phone',
    location: 'Location',
    joinDate: 'Join Date',
    totalOrders: 'Total Orders',
    totalValue: 'Total Value',
    lastOrder: 'Last Order',
    rating: 'Rating',
    actions: 'Actions',
    viewProfile: 'View Profile',
    editCustomer: 'Edit Customer',
    active: 'Active',
    inactive: 'Inactive',
    vip: 'VIP',
    regular: 'Regular',
    corporate: 'Corporate',
    individual: 'Individual',
    thisMonth: 'This Month',
    refresh: 'Refresh'
  }
}

export default function Customers({ language }: CustomersProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedType, setSelectedType] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('')
  const [refreshing, setRefreshing] = useState(false)
  const t = translations[language]
  const isRTL = language === 'ar'

  // Customer metrics
  const [customerMetrics, setCustomerMetrics] = useState({
    totalCustomers: 1247,
    activeCustomers: 1142,
    newCustomers: 23,
    customerValue: 2850000
  })

  // Sample customer data
  const [customers] = useState([
    {
      id: 1,
      name: 'أحمد محمد الأحمد',
      nameEn: 'Ahmed Mohammed Al-Ahmed',
      company: 'شركة التقنية المتقدمة',
      companyEn: 'Advanced Technology Company',
      email: '<EMAIL>',
      phone: '+966501234567',
      location: 'الرياض، السعودية',
      locationEn: 'Riyadh, Saudi Arabia',
      joinDate: '2023-01-15',
      totalOrders: 45,
      totalValue: 125000,
      lastOrder: '2024-01-20',
      rating: 4.8,
      status: 'active',
      type: 'corporate',
      avatar: 'A'
    },
    {
      id: 2,
      name: 'فاطمة سالم العتيبي',
      nameEn: 'Fatima Salem Al-Otaibi',
      company: 'مؤسسة النور للتجارة',
      companyEn: 'Al-Noor Trading Est.',
      email: '<EMAIL>',
      phone: '+966507654321',
      location: 'جدة، السعودية',
      locationEn: 'Jeddah, Saudi Arabia',
      joinDate: '2022-08-20',
      totalOrders: 78,
      totalValue: 285000,
      lastOrder: '2024-01-25',
      rating: 4.9,
      status: 'active',
      type: 'vip',
      avatar: 'F'
    },
    {
      id: 3,
      name: 'عمر خالد الشمري',
      nameEn: 'Omar Khalid Al-Shamri',
      company: 'فرد',
      companyEn: 'Individual',
      email: '<EMAIL>',
      phone: '+966509876543',
      location: 'الدمام، السعودية',
      locationEn: 'Dammam, Saudi Arabia',
      joinDate: '2023-03-10',
      totalOrders: 12,
      totalValue: 35000,
      lastOrder: '2024-01-18',
      rating: 4.2,
      status: 'active',
      type: 'individual',
      avatar: 'O'
    }
  ])

  const handleRefresh = async () => {
    setRefreshing(true)
    await new Promise(resolve => setTimeout(resolve, 1000))
    setRefreshing(false)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-500/20 text-green-400 border-green-500/30'
      case 'inactive':
        return 'bg-red-500/20 text-red-400 border-red-500/30'
      case 'vip':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30'
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'corporate':
        return 'bg-blue-500/20 text-blue-400 border-blue-500/30'
      case 'individual':
        return 'bg-purple-500/20 text-purple-400 border-purple-500/30'
      case 'vip':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30'
    }
  }

  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.nameEn.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.company.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.email.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = !selectedType || customer.type === selectedType
    const matchesStatus = !selectedStatus || customer.status === selectedStatus
    
    return matchesSearch && matchesType && matchesStatus
  })

  const customerMetricsCards = [
    {
      title: t.totalCustomers,
      value: customerMetrics.totalCustomers.toLocaleString(),
      change: '+12.5%',
      trend: 'up',
      icon: Users,
      color: 'from-blue-500 to-blue-600'
    },
    {
      title: t.activeCustomers,
      value: customerMetrics.activeCustomers.toLocaleString(),
      change: '+8.3%',
      trend: 'up',
      icon: TrendingUp,
      color: 'from-green-500 to-green-600'
    },
    {
      title: t.newCustomers,
      value: customerMetrics.newCustomers.toString(),
      change: t.thisMonth,
      trend: 'stable',
      icon: UserPlus,
      color: 'from-purple-500 to-purple-600'
    },
    {
      title: t.customerValue,
      value: new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR',
        notation: 'compact'
      }).format(customerMetrics.customerValue),
      change: '+15.7%',
      trend: 'up',
      icon: DollarSign,
      color: 'from-orange-500 to-orange-600'
    }
  ]

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">{t.title}</h1>
          <p className="text-white/70">{t.subtitle}</p>
        </div>
        
        <div className="flex items-center gap-3">
          <Button
            onClick={handleRefresh}
            disabled={refreshing}
            className="text-white/80 hover:text-white hover:bg-white/15 rounded-xl p-3 backdrop-blur-10"
          >
            <Calendar className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            {t.refresh}
          </Button>
          <Button className="text-white/80 hover:text-white hover:bg-white/15 rounded-xl p-3 backdrop-blur-10">
            <UserPlus className="h-4 w-4 mr-2" />
            {t.addCustomer}
          </Button>
          <Button variant="outline" className="text-white/80 hover:text-white hover:bg-white/15 rounded-xl p-3 backdrop-blur-10">
            <Download className="h-4 w-4 mr-2" />
            {t.exportData}
          </Button>
        </div>
      </div>

      {/* Customer Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {customerMetricsCards.map((card, index) => (
          <Card key={index} className="glass-card border-white/20 hover:border-white/40 transition-all">
            <CardContent className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <p className="text-white/70 text-sm mb-1">{card.title}</p>
                  <p className="text-2xl font-bold text-white">{card.value}</p>
                </div>
                <div className={`p-3 rounded-xl bg-gradient-to-r ${card.color} shadow-lg`}>
                  <card.icon className="h-6 w-6 text-white" />
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <span className={`text-sm font-medium ${
                  card.trend === 'up' ? 'text-green-400' : 
                  card.trend === 'down' ? 'text-red-400' : 'text-blue-400'
                }`}>
                  {card.change}
                </span>
                <span className="text-xs text-white/60">vs last month</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Search and Filters */}
      <Card className="glass-card border-white/20">
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/60" />
              <Input
                placeholder={t.searchCustomers}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-white/10 border-white/20 text-white placeholder:text-white/60"
              />
            </div>
            
            <select
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value)}
              className="bg-white/10 border-white/20 text-white rounded-lg px-3 py-2"
            >
              <option value="">{t.customerType}</option>
              <option value="corporate">{t.corporate}</option>
              <option value="individual">{t.individual}</option>
              <option value="vip">{t.vip}</option>
            </select>
            
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="bg-white/10 border-white/20 text-white rounded-lg px-3 py-2"
            >
              <option value="">{t.status}</option>
              <option value="active">{t.active}</option>
              <option value="inactive">{t.inactive}</option>
            </select>
            
            <Button variant="outline" className="text-white/80 hover:text-white hover:bg-white/15 rounded-xl p-3 backdrop-blur-10">
              <Filter className="h-4 w-4 mr-2" />
              {t.filterBy}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Customer List */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white text-xl">{t.customerList}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-white/20">
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.customerName}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.company}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.email}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.location}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.totalOrders}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.totalValue}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.rating}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.status}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.actions}</th>
                </tr>
              </thead>
              <tbody>
                {filteredCustomers.map((customer) => (
                  <tr key={customer.id} className="border-b border-white/10 hover:bg-white/5 transition-colors">
                    <td className="py-4 px-4">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-white font-bold">
                          {customer.avatar}
                        </div>
                        <div>
                          <p className="text-white font-medium">
                            {language === 'ar' ? customer.name : customer.nameEn}
                          </p>
                          <p className="text-white/60 text-sm">{customer.phone}</p>
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-4 text-white">
                      {language === 'ar' ? customer.company : customer.companyEn}
                    </td>
                    <td className="py-4 px-4 text-white">{customer.email}</td>
                    <td className="py-4 px-4 text-white">
                      {language === 'ar' ? customer.location : customer.locationEn}
                    </td>
                    <td className="py-4 px-4 text-white font-medium">{customer.totalOrders}</td>
                    <td className="py-4 px-4 text-white">
                      {new Intl.NumberFormat('ar-SA', {
                        style: 'currency',
                        currency: 'SAR'
                      }).format(customer.totalValue)}
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex items-center gap-1">
                        <Star className="h-4 w-4 text-yellow-400 fill-current" />
                        <span className="text-white text-sm">{customer.rating}</span>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(customer.status)}`}>
                        {t[customer.status as keyof typeof t]}
                      </span>
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex items-center gap-2">
                        <Button size="sm" variant="outline" className="text-white/80 hover:text-white hover:bg-white/15 rounded-lg p-2 backdrop-blur-10">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="outline" className="text-white/80 hover:text-white hover:bg-white/15 rounded-lg p-2 backdrop-blur-10">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="outline" className="text-white/80 hover:text-white hover:bg-white/15 rounded-lg p-2 backdrop-blur-10">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
