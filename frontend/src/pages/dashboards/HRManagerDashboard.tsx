import { useState, useEffect } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import type { RootState, AppDispatch } from '../../store'
import {
  Users,
  UserCheck,
  UserX,
  Clock,
  Calendar,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  DollarSign,
  Award,
  Target,
  RefreshCw,
  Eye,
  Plus,
  FileText
} from 'lucide-react'

interface HRManagerDashboardProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    welcome: 'مرحباً بك',
    hrOverview: 'نظرة عامة على الموارد البشرية',
    employeeStats: 'إحصائيات الموظفين',
    attendanceOverview: 'نظرة عامة على الحضور',
    leaveManagement: 'إدارة الإجازات',
    performanceMetrics: 'مقاييس الأداء',
    payrollSummary: 'ملخص الرواتب',
    recentActivities: 'الأنشطة الحديثة',
    quickActions: 'إجراءات سريعة',
    totalEmployees: 'إجمالي الموظفين',
    activeEmployees: 'الموظفون النشطون',
    newHires: 'التوظيفات الجديدة',
    presentToday: 'الحاضرون اليوم',
    absentToday: 'الغائبون اليوم',
    lateArrivals: 'المتأخرون',
    pendingLeaves: 'الإجازات المعلقة',
    approvedLeaves: 'الإجازات المعتمدة',
    overtimeHours: 'ساعات العمل الإضافي',
    avgPerformance: 'متوسط الأداء',
    topPerformers: 'أفضل الموظفين',
    trainingCompleted: 'التدريبات المكتملة',
    monthlyPayroll: 'الرواتب الشهرية',
    benefitsCost: 'تكلفة المزايا',
    recruitmentCost: 'تكلفة التوظيف',
    refresh: 'تحديث',
    viewDetails: 'عرض التفاصيل',
    addEmployee: 'إضافة موظف',
    processPayroll: 'معالجة الرواتب',
    generateReport: 'إنشاء تقرير',
    manageLeaves: 'إدارة الإجازات',
    viewAll: 'عرض الكل',
    approve: 'موافقة',
    reject: 'رفض',
    pending: 'معلق',
    approved: 'معتمد',
    rejected: 'مرفوض'
  },
  en: {
    welcome: 'Welcome',
    hrOverview: 'HR Overview',
    employeeStats: 'Employee Statistics',
    attendanceOverview: 'Attendance Overview',
    leaveManagement: 'Leave Management',
    performanceMetrics: 'Performance Metrics',
    payrollSummary: 'Payroll Summary',
    recentActivities: 'Recent Activities',
    quickActions: 'Quick Actions',
    totalEmployees: 'Total Employees',
    activeEmployees: 'Active Employees',
    newHires: 'New Hires',
    presentToday: 'Present Today',
    absentToday: 'Absent Today',
    lateArrivals: 'Late Arrivals',
    pendingLeaves: 'Pending Leaves',
    approvedLeaves: 'Approved Leaves',
    overtimeHours: 'Overtime Hours',
    avgPerformance: 'Avg Performance',
    topPerformers: 'Top Performers',
    trainingCompleted: 'Training Completed',
    monthlyPayroll: 'Monthly Payroll',
    benefitsCost: 'Benefits Cost',
    recruitmentCost: 'Recruitment Cost',
    refresh: 'Refresh',
    viewDetails: 'View Details',
    addEmployee: 'Add Employee',
    processPayroll: 'Process Payroll',
    generateReport: 'Generate Report',
    manageLeaves: 'Manage Leaves',
    viewAll: 'View All',
    approve: 'Approve',
    reject: 'Reject',
    pending: 'Pending',
    approved: 'Approved',
    rejected: 'Rejected'
  }
}

export default function HRManagerDashboard({ language }: HRManagerDashboardProps) {
  const { user } = useSelector((state: RootState) => state.auth)
  const [refreshing, setRefreshing] = useState(false)
  const t = translations[language]
  const isRTL = language === 'ar'

  // HR-specific metrics
  const [hrMetrics, setHrMetrics] = useState({
    totalEmployees: 1247,
    activeEmployees: 1198,
    newHires: 12,
    presentToday: 1156,
    absentToday: 42,
    lateArrivals: 15,
    pendingLeaves: 23,
    approvedLeaves: 156,
    overtimeHours: 2340,
    avgPerformance: 87.5,
    topPerformers: 45,
    trainingCompleted: 89,
    monthlyPayroll: 18500000,
    benefitsCost: 2100000,
    recruitmentCost: 450000
  })

  const handleRefresh = async () => {
    setRefreshing(true)
    await new Promise(resolve => setTimeout(resolve, 1000))
    setRefreshing(false)
  }

  const employeeStatsCards = [
    {
      title: t.totalEmployees,
      value: hrMetrics.totalEmployees.toLocaleString(),
      change: '+12',
      trend: 'up',
      icon: Users,
      color: 'from-blue-500 to-blue-600'
    },
    {
      title: t.activeEmployees,
      value: hrMetrics.activeEmployees.toLocaleString(),
      change: '+5',
      trend: 'up',
      icon: UserCheck,
      color: 'from-green-500 to-green-600'
    },
    {
      title: t.newHires,
      value: hrMetrics.newHires.toString(),
      change: '+3',
      trend: 'up',
      icon: Plus,
      color: 'from-purple-500 to-purple-600'
    },
    {
      title: t.presentToday,
      value: hrMetrics.presentToday.toLocaleString(),
      change: '92.7%',
      trend: 'stable',
      icon: CheckCircle,
      color: 'from-emerald-500 to-emerald-600'
    },
    {
      title: t.absentToday,
      value: hrMetrics.absentToday.toString(),
      change: '-5',
      trend: 'down',
      icon: UserX,
      color: 'from-red-500 to-red-600'
    },
    {
      title: t.lateArrivals,
      value: hrMetrics.lateArrivals.toString(),
      change: '+2',
      trend: 'up',
      icon: Clock,
      color: 'from-orange-500 to-orange-600'
    }
  ]

  const quickActions = [
    { title: t.addEmployee, icon: Plus, href: '/employees/add', color: 'from-blue-500 to-blue-600' },
    { title: t.manageLeaves, icon: Calendar, href: '/hr/leave', color: 'from-green-500 to-green-600' },
    { title: t.processPayroll, icon: DollarSign, href: '/hr/payroll', color: 'from-purple-500 to-purple-600' },
    { title: t.generateReport, icon: FileText, href: '/reports', color: 'from-orange-500 to-orange-600' }
  ]

  const pendingLeaveRequests = [
    {
      id: 1,
      employee: 'أحمد محمد',
      department: 'تقنية المعلومات',
      type: 'إجازة سنوية',
      duration: '5 أيام',
      startDate: '2024-02-01',
      status: 'pending',
      urgency: 'normal'
    },
    {
      id: 2,
      employee: 'فاطمة علي',
      department: 'التسويق',
      type: 'إجازة مرضية',
      duration: '2 أيام',
      startDate: '2024-01-25',
      status: 'pending',
      urgency: 'urgent'
    },
    {
      id: 3,
      employee: 'محمد حسن',
      department: 'المالية',
      type: 'إجازة طارئة',
      duration: '1 يوم',
      startDate: '2024-01-30',
      status: 'pending',
      urgency: 'urgent'
    }
  ]

  const recentActivities = [
    {
      id: 1,
      type: 'new_hire',
      message: 'New employee onboarded: Sara Al-Ahmad',
      messageAr: 'تم تعيين موظف جديد: سارة الأحمد',
      timestamp: '10 minutes ago',
      icon: Users,
      severity: 'success'
    },
    {
      id: 2,
      type: 'leave_request',
      message: 'Leave request submitted by Ahmed Mohammed',
      messageAr: 'تم تقديم طلب إجازة من أحمد محمد',
      timestamp: '25 minutes ago',
      icon: Calendar,
      severity: 'info'
    },
    {
      id: 3,
      type: 'performance_review',
      message: 'Performance review completed for IT team',
      messageAr: 'تم إكمال مراجعة الأداء لفريق تقنية المعلومات',
      timestamp: '1 hour ago',
      icon: Award,
      severity: 'success'
    },
    {
      id: 4,
      type: 'payroll',
      message: 'Monthly payroll processed successfully',
      messageAr: 'تم معالجة الرواتب الشهرية بنجاح',
      timestamp: '2 hours ago',
      icon: DollarSign,
      severity: 'success'
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'approved':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'rejected':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'urgent':
        return 'border-l-red-500'
      case 'high':
        return 'border-l-orange-500'
      default:
        return 'border-l-blue-500'
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">
            {t.welcome}, {user?.name}
          </h1>
          <p className="text-white/70">لوحة تحكم مدير الموارد البشرية - إدارة شاملة للموظفين</p>
        </div>

        <div className="flex items-center gap-3">
          <Button
            onClick={handleRefresh}
            disabled={refreshing}
            className="glass-button"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            {t.refresh}
          </Button>
          <Button variant="outline" className="glass-button">
            <FileText className="h-4 w-4 mr-2" />
            {t.generateReport}
          </Button>
        </div>
      </div>

      {/* Employee Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
        {employeeStatsCards.map((card, index) => (
          <Card key={index} className="glass-card border-white/20 hover:border-white/40 transition-all">
            <CardContent className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <p className="text-white/70 text-sm mb-1">{card.title}</p>
                  <p className="text-2xl font-bold text-white">{card.value}</p>
                </div>
                <div className={`p-3 rounded-xl bg-gradient-to-r ${card.color} shadow-lg`}>
                  <card.icon className="h-6 w-6 text-white" />
                </div>
              </div>

              <div className="flex items-center gap-2">
                <span className={`text-sm font-medium ${
                  card.trend === 'up' ? 'text-green-400' :
                  card.trend === 'down' ? 'text-red-400' : 'text-blue-400'
                }`}>
                  {card.change}
                </span>
                <span className="text-xs text-white/60">vs yesterday</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Main Dashboard Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Quick Actions */}
        <Card className="glass-card border-white/20">
          <CardHeader>
            <CardTitle className="text-white text-xl">{t.quickActions}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              {quickActions.map((action, index) => (
                <div
                  key={index}
                  className="group flex flex-col items-center gap-3 p-6 glass-card border-white/10 hover:border-white/30 cursor-pointer transition-all duration-300 hover:scale-105"
                >
                  <div className={`p-4 rounded-2xl bg-gradient-to-r ${action.color} shadow-lg group-hover:shadow-xl transition-all`}>
                    <action.icon className="h-8 w-8 text-white" />
                  </div>
                  <span className="font-medium text-white text-center text-sm">
                    {action.title}
                  </span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Pending Leave Requests */}
        <Card className="lg:col-span-2 glass-card border-white/20">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-white text-xl">{t.leaveManagement}</CardTitle>
              <Button variant="outline" size="sm" className="glass-button">
                <Eye className="h-4 w-4 mr-2" />
                {t.viewAll}
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {pendingLeaveRequests.map((request) => (
                <div key={request.id} className={`p-4 glass-card border-white/10 hover:border-white/30 transition-all border-l-4 ${getUrgencyColor(request.urgency)}`}>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h4 className="text-white font-medium">{request.employee}</h4>
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(request.status)}`}>
                          {t[request.status as keyof typeof t]}
                        </span>
                        {request.urgency === 'urgent' && (
                          <span className="bg-red-500/20 text-red-400 text-xs px-2 py-1 rounded-full">
                            عاجل
                          </span>
                        )}
                      </div>
                      <div className="grid grid-cols-2 gap-4 text-sm text-white/80">
                        <div>
                          <span className="text-white/60">القسم: </span>
                          {request.department}
                        </div>
                        <div>
                          <span className="text-white/60">النوع: </span>
                          {request.type}
                        </div>
                        <div>
                          <span className="text-white/60">المدة: </span>
                          {request.duration}
                        </div>
                        <div>
                          <span className="text-white/60">تاريخ البداية: </span>
                          {request.startDate}
                        </div>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Button size="sm" className="glass-button">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        {t.approve}
                      </Button>
                      <Button size="sm" variant="outline" className="glass-button text-red-400 hover:bg-red-500/20">
                        <UserX className="h-3 w-3 mr-1" />
                        {t.reject}
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance & Financial Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="glass-card border-white/20">
          <CardHeader>
            <CardTitle className="text-white text-xl flex items-center gap-2">
              <Award className="h-5 w-5" />
              {t.performanceMetrics}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <span className="text-white/80">{t.avgPerformance}</span>
                <span className="text-blue-400 font-bold">{hrMetrics.avgPerformance}%</span>
              </div>
              <div className="w-full bg-white/20 rounded-full h-3">
                <div
                  className="bg-gradient-to-r from-blue-500 to-green-500 h-3 rounded-full transition-all duration-1000"
                  style={{ width: `${hrMetrics.avgPerformance}%` }}
                ></div>
              </div>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-white/70">{t.topPerformers}</p>
                  <p className="text-green-400 font-bold">{hrMetrics.topPerformers}</p>
                </div>
                <div>
                  <p className="text-white/70">{t.trainingCompleted}</p>
                  <p className="text-blue-400 font-bold">{hrMetrics.trainingCompleted}%</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardHeader>
            <CardTitle className="text-white text-xl flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              {t.payrollSummary}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-1 gap-4">
                <div className="flex items-center justify-between">
                  <span className="text-white/80">{t.monthlyPayroll}</span>
                  <span className="text-green-400 font-bold">
                    {new Intl.NumberFormat('ar-SA', {
                      style: 'currency',
                      currency: 'SAR'
                    }).format(hrMetrics.monthlyPayroll)}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/80">{t.benefitsCost}</span>
                  <span className="text-blue-400 font-bold">
                    {new Intl.NumberFormat('ar-SA', {
                      style: 'currency',
                      currency: 'SAR'
                    }).format(hrMetrics.benefitsCost)}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/80">{t.recruitmentCost}</span>
                  <span className="text-orange-400 font-bold">
                    {new Intl.NumberFormat('ar-SA', {
                      style: 'currency',
                      currency: 'SAR'
                    }).format(hrMetrics.recruitmentCost)}
                  </span>
                </div>
              </div>
              <div className="pt-4 border-t border-white/20">
                <div className="flex items-center justify-between">
                  <span className="text-white font-medium">Total HR Cost</span>
                  <span className="text-white font-bold">
                    {new Intl.NumberFormat('ar-SA', {
                      style: 'currency',
                      currency: 'SAR'
                    }).format(hrMetrics.monthlyPayroll + hrMetrics.benefitsCost + hrMetrics.recruitmentCost)}
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
