import { useState, useEffect } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  ShoppingCart, 
  Users, 
  TrendingUp, 
  DollarSign,
  Target,
  Package,
  BarChart3,
  RefreshCw,
  Download,
  Filter,
  Bell,
  Activity,
  Zap,
  Eye,
  Plus
} from 'lucide-react'

interface SalesManagerDashboardProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    dashboard: 'لوحة تحكم مدير المبيعات',
    welcome: 'مرحباً بك في نظام إدارة المبيعات',
    online: 'متصل',
    refresh: 'تحديث',
    export: 'تصدير',
    filter: 'تصفية',
    viewAll: 'عرض الكل',
    
    // Stats
    totalSales: 'إجمالي المبيعات',
    newCustomers: 'عملاء جدد',
    salesTarget: 'هدف المبيعات',
    avgDealSize: 'متوسط حجم الصفقة',
    conversionRate: 'معدل التحويل',
    activePipeline: 'خط الأنابيب النشط',
    
    // Quick Actions
    quickActions: 'إجراءات سريعة',
    newOrder: 'طلب جديد',
    addCustomer: 'إضافة عميل',
    createQuote: 'إنشاء عرض سعر',
    viewPipeline: 'عرض خط الأنابيب',
    salesReport: 'تقرير المبيعات',
    manageProducts: 'إدارة المنتجات',
    
    // Recent Activities
    recentActivities: 'الأنشطة الحديثة',
    
    // Sales Performance
    salesPerformance: 'أداء المبيعات',
    thisMonth: 'هذا الشهر',
    lastMonth: 'الشهر الماضي',
    growth: 'نمو',
    
    // Top Products
    topProducts: 'أفضل المنتجات',
    
    // Pipeline Status
    pipelineStatus: 'حالة خط الأنابيب',
    leads: 'عملاء محتملون',
    qualified: 'مؤهل',
    proposal: 'اقتراح',
    negotiation: 'تفاوض',
    closed: 'مغلق'
  },
  en: {
    dashboard: 'Sales Manager Dashboard',
    welcome: 'Welcome to Sales Management System',
    online: 'Online',
    refresh: 'Refresh',
    export: 'Export',
    filter: 'Filter',
    viewAll: 'View All',
    
    // Stats
    totalSales: 'Total Sales',
    newCustomers: 'New Customers',
    salesTarget: 'Sales Target',
    avgDealSize: 'Avg Deal Size',
    conversionRate: 'Conversion Rate',
    activePipeline: 'Active Pipeline',
    
    // Quick Actions
    quickActions: 'Quick Actions',
    newOrder: 'New Order',
    addCustomer: 'Add Customer',
    createQuote: 'Create Quote',
    viewPipeline: 'View Pipeline',
    salesReport: 'Sales Report',
    manageProducts: 'Manage Products',
    
    // Recent Activities
    recentActivities: 'Recent Activities',
    
    // Sales Performance
    salesPerformance: 'Sales Performance',
    thisMonth: 'This Month',
    lastMonth: 'Last Month',
    growth: 'Growth',
    
    // Top Products
    topProducts: 'Top Products',
    
    // Pipeline Status
    pipelineStatus: 'Pipeline Status',
    leads: 'Leads',
    qualified: 'Qualified',
    proposal: 'Proposal',
    negotiation: 'Negotiation',
    closed: 'Closed'
  }
}

export default function SalesManagerDashboard({ language }: SalesManagerDashboardProps) {
  const t = translations[language]
  const [refreshing, setRefreshing] = useState(false)

  // Mock data for sales dashboard
  const salesStats = [
    { title: t.totalSales, value: '2,450,000 ر.س', icon: DollarSign, color: 'from-green-500 to-green-600', change: '+12%' },
    { title: t.newCustomers, value: '156', icon: Users, color: 'from-blue-500 to-blue-600', change: '+8%' },
    { title: t.salesTarget, value: '85%', icon: Target, color: 'from-purple-500 to-purple-600', change: '+5%' },
    { title: t.avgDealSize, value: '45,000 ر.س', icon: TrendingUp, color: 'from-orange-500 to-orange-600', change: '+15%' },
    { title: t.conversionRate, value: '24%', icon: BarChart3, color: 'from-cyan-500 to-cyan-600', change: '+3%' },
    { title: t.activePipeline, value: '89', icon: Package, color: 'from-pink-500 to-pink-600', change: '+7%' }
  ]

  const quickActions = [
    { title: t.newOrder, icon: Plus, color: 'from-blue-500 to-blue-600' },
    { title: t.addCustomer, icon: Users, color: 'from-green-500 to-green-600' },
    { title: t.createQuote, icon: ShoppingCart, color: 'from-purple-500 to-purple-600' },
    { title: t.viewPipeline, icon: TrendingUp, color: 'from-orange-500 to-orange-600' },
    { title: t.salesReport, icon: BarChart3, color: 'from-cyan-500 to-cyan-600' },
    { title: t.manageProducts, icon: Package, color: 'from-pink-500 to-pink-600' }
  ]

  const recentActivities = [
    { title: 'عميل جديد: شركة التقنية المتقدمة', time: 'منذ 5 دقائق', icon: Users },
    { title: 'طلب جديد بقيمة 125,000 ر.س', time: 'منذ 15 دقيقة', icon: ShoppingCart },
    { title: 'عرض سعر تم قبوله', time: 'منذ 30 دقيقة', icon: TrendingUp },
    { title: 'اجتماع مع العميل المحتمل', time: 'منذ ساعة', icon: Activity }
  ]

  const handleRefresh = () => {
    setRefreshing(true)
    setTimeout(() => setRefreshing(false), 1000)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 sm:gap-6 mb-6 sm:mb-8">
        <div>
          <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-white drop-shadow-lg">
            {t.dashboard}
          </h1>
          <p className="text-white/80 mt-1 sm:mt-2 text-sm sm:text-base lg:text-lg">
            {t.welcome}
          </p>
        </div>
        
        <div className="flex flex-wrap items-center gap-2 sm:gap-3">
          <Button
            onClick={handleRefresh}
            disabled={refreshing}
            className="glass-button text-xs sm:text-sm px-3 sm:px-4 py-2"
          >
            <RefreshCw className={`h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            <span className="hidden sm:inline">{t.refresh}</span>
          </Button>
          <Button variant="outline" className="glass-button text-xs sm:text-sm px-3 sm:px-4 py-2">
            <Download className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
            <span className="hidden sm:inline">{t.export}</span>
          </Button>
          <Button variant="outline" className="glass-button text-xs sm:text-sm px-3 sm:px-4 py-2">
            <Filter className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
            <span className="hidden sm:inline">{t.filter}</span>
          </Button>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4 sm:gap-6">
        {salesStats.map((stat, index) => (
          <Card key={index} className="glass-card border-white/20 hover:border-white/40 hover:scale-105 transition-all duration-300">
            <CardContent className="p-4 sm:p-6">
              <div className="flex items-start justify-between mb-3 sm:mb-4">
                <div className="flex-1">
                  <p className="text-xs sm:text-sm font-medium text-white/70 mb-1">
                    {stat.title}
                  </p>
                  <p className="text-lg sm:text-xl lg:text-2xl font-bold text-white">
                    {stat.value}
                  </p>
                </div>
                <div className={`p-2 sm:p-3 rounded-lg sm:rounded-xl bg-gradient-to-r ${stat.color} shadow-lg`}>
                  <stat.icon className="h-4 w-4 sm:h-5 sm:w-5 lg:h-6 lg:w-6 text-white" />
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className={`text-xs sm:text-sm font-medium text-green-400`}>
                  {stat.change}
                </span>
                <span className="text-xs text-white/60">
                  {t.thisMonth}
                </span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6">
        {/* Quick Actions */}
        <Card className="lg:col-span-2 glass-card border-white/20">
          <CardHeader className="p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2 sm:gap-4">
              <CardTitle className="text-white text-lg sm:text-xl">{t.quickActions}</CardTitle>
              <Button variant="outline" size="sm" className="glass-button text-xs sm:text-sm px-3 py-2">
                <Eye className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                <span className="hidden sm:inline">{t.viewAll}</span>
              </Button>
            </div>
          </CardHeader>
          <CardContent className="p-4 sm:p-6">
            <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-3 gap-3 sm:gap-4">
              {quickActions.map((action, index) => (
                <div
                  key={index}
                  className="group flex flex-col items-center gap-2 sm:gap-3 p-3 sm:p-4 lg:p-6 glass-card border-white/10 hover:border-white/30 cursor-pointer transition-all duration-300 hover:scale-105"
                >
                  <div className={`p-2 sm:p-3 lg:p-4 rounded-xl sm:rounded-2xl bg-gradient-to-r ${action.color} shadow-lg group-hover:shadow-xl transition-all`}>
                    <action.icon className="h-5 w-5 sm:h-6 sm:w-6 lg:h-8 lg:w-8 text-white" />
                  </div>
                  <span className="font-medium text-white text-center text-xs sm:text-sm">
                    {action.title}
                  </span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recent Activities */}
        <Card className="glass-card border-white/20">
          <CardHeader className="p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2 sm:gap-4">
              <CardTitle className="text-white text-lg sm:text-xl">{t.recentActivities}</CardTitle>
              <Button variant="outline" size="sm" className="glass-button text-xs sm:text-sm px-3 py-2">
                <Eye className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                <span className="hidden sm:inline">{t.viewAll}</span>
              </Button>
            </div>
          </CardHeader>
          <CardContent className="p-4 sm:p-6">
            <div className="space-y-2 sm:space-y-3">
              {recentActivities.map((activity, index) => (
                <div key={index} className="flex items-start gap-2 sm:gap-3 p-3 sm:p-4 glass-card border-white/10 hover:border-white/30 transition-all duration-300">
                  <div className="p-2 sm:p-3 rounded-lg sm:rounded-xl bg-gradient-to-r from-blue-500 to-purple-600 shadow-lg">
                    <activity.icon className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-xs sm:text-sm font-medium text-white mb-1">
                      {activity.title}
                    </p>
                    <p className="text-xs text-white/40">
                      {activity.time}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
