import { useState, useEffect } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Users,
  Building,
  Search,
  Filter,
  Download,
  Eye,
  Edit,
  Calendar,
  Clock,
  CheckCircle,
  AlertTriangle,
  User,
  Briefcase,
  Target,
  TrendingUp
} from 'lucide-react'

interface DepartmentCustomersProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    title: 'عملاء القسم',
    subtitle: 'إدارة العملاء المخصصين للقسم والمشاريع والخدمات',
    assignedCustomers: 'العملاء المخصصون',
    activeProjects: 'المشاريع النشطة',
    serviceRequests: 'طلبات الخدمة',
    teamWorkload: 'عبء العمل',
    searchCustomers: 'البحث في العملاء...',
    addCustomer: 'إضافة عميل',
    exportData: 'تصدير البيانات',
    customerName: 'اسم العميل',
    assignedTo: 'مخصص لـ',
    projectStatus: 'حالة المشروع',
    priority: 'الأولوية',
    deadline: 'الموعد النهائي',
    progress: 'التقدم',
    actions: 'الإجراءات',
    viewProject: 'عرض المشروع',
    assignTeam: 'تخصيص فريق',
    high: 'عالية',
    medium: 'متوسطة',
    low: 'منخفضة',
    inProgress: 'قيد التنفيذ',
    completed: 'مكتمل',
    onHold: 'معلق',
    planning: 'تخطيط',
    refresh: 'تحديث',
    projectManagement: 'إدارة المشاريع',
    teamAllocation: 'توزيع الفريق',
    departmentOverview: 'نظرة عامة على القسم',
    filterBy: 'تصفية حسب'
  },
  en: {
    title: 'Department Customers',
    subtitle: 'Management of customers assigned to department projects and services',
    assignedCustomers: 'Assigned Customers',
    activeProjects: 'Active Projects',
    serviceRequests: 'Service Requests',
    teamWorkload: 'Team Workload',
    searchCustomers: 'Search customers...',
    addCustomer: 'Add Customer',
    exportData: 'Export Data',
    customerName: 'Customer Name',
    assignedTo: 'Assigned To',
    projectStatus: 'Project Status',
    priority: 'Priority',
    deadline: 'Deadline',
    progress: 'Progress',
    actions: 'Actions',
    viewProject: 'View Project',
    assignTeam: 'Assign Team',
    high: 'High',
    medium: 'Medium',
    low: 'Low',
    inProgress: 'In Progress',
    completed: 'Completed',
    onHold: 'On Hold',
    planning: 'Planning',
    refresh: 'Refresh',
    projectManagement: 'Project Management',
    teamAllocation: 'Team Allocation',
    departmentOverview: 'Department Overview',
    filterBy: 'Filter By'
  }
}

export default function DepartmentCustomers({ language }: DepartmentCustomersProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('')
  const [selectedPriority, setSelectedPriority] = useState('')
  const [refreshing, setRefreshing] = useState(false)
  const t = translations[language]
  const isRTL = language === 'ar'

  // Department-specific customer metrics
  const [departmentMetrics, setDepartmentMetrics] = useState({
    assignedCustomers: 18,
    activeProjects: 12,
    serviceRequests: 8,
    teamWorkload: 85
  })

  // Sample department customer data
  const [customers] = useState([
    {
      id: 1,
      name: 'شركة التقنية المتقدمة',
      nameEn: 'Advanced Technology Company',
      projectName: 'تطوير نظام إدارة المخزون',
      projectNameEn: 'Inventory Management System Development',
      assignedTo: 'فريق التطوير أ',
      assignedToEn: 'Development Team A',
      projectStatus: 'inProgress',
      priority: 'high',
      deadline: '2024-03-15',
      progress: 75,
      startDate: '2023-12-01',
      budget: 450000,
      teamMembers: ['أحمد محمد', 'فاطمة علي', 'عمر سالم'],
      teamMembersEn: ['Ahmed Mohammed', 'Fatima Ali', 'Omar Salem'],
      lastUpdate: '2024-01-20'
    },
    {
      id: 2,
      name: 'مؤسسة النور للتجارة',
      nameEn: 'Al-Noor Trading Est.',
      projectName: 'استشارات تحسين العمليات',
      projectNameEn: 'Process Improvement Consulting',
      assignedTo: 'فريق الاستشارات',
      assignedToEn: 'Consulting Team',
      projectStatus: 'planning',
      priority: 'medium',
      deadline: '2024-04-30',
      progress: 25,
      startDate: '2024-01-15',
      budget: 180000,
      teamMembers: ['سارة أحمد', 'محمد علي'],
      teamMembersEn: ['Sarah Ahmed', 'Mohammed Ali'],
      lastUpdate: '2024-01-18'
    },
    {
      id: 3,
      name: 'شركة البناء الحديث',
      nameEn: 'Modern Construction Co.',
      projectName: 'نظام إدارة المشاريع',
      projectNameEn: 'Project Management System',
      assignedTo: 'فريق التطوير ب',
      assignedToEn: 'Development Team B',
      projectStatus: 'onHold',
      priority: 'low',
      deadline: '2024-06-15',
      progress: 40,
      startDate: '2023-10-01',
      budget: 320000,
      teamMembers: ['خالد سالم', 'نورا محمد'],
      teamMembersEn: ['Khalid Salem', 'Nora Mohammed'],
      lastUpdate: '2024-01-10'
    }
  ])

  const handleRefresh = async () => {
    setRefreshing(true)
    await new Promise(resolve => setTimeout(resolve, 1000))
    setRefreshing(false)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'inProgress':
        return 'bg-blue-500/20 text-blue-400 border-blue-500/30'
      case 'completed':
        return 'bg-green-500/20 text-green-400 border-green-500/30'
      case 'onHold':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
      case 'planning':
        return 'bg-purple-500/20 text-purple-400 border-purple-500/30'
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-500/20 text-red-400 border-red-500/30'
      case 'medium':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
      case 'low':
        return 'bg-green-500/20 text-green-400 border-green-500/30'
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'inProgress':
        return <Clock className="h-4 w-4" />
      case 'completed':
        return <CheckCircle className="h-4 w-4" />
      case 'onHold':
        return <AlertTriangle className="h-4 w-4" />
      case 'planning':
        return <Target className="h-4 w-4" />
      default:
        return <Clock className="h-4 w-4" />
    }
  }

  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.nameEn.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.projectName.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = !selectedStatus || customer.projectStatus === selectedStatus
    const matchesPriority = !selectedPriority || customer.priority === selectedPriority

    return matchesSearch && matchesStatus && matchesPriority
  })

  const departmentMetricsCards = [
    {
      title: t.assignedCustomers,
      value: departmentMetrics.assignedCustomers.toString(),
      change: '+2',
      trend: 'up',
      icon: Users,
      color: 'from-blue-500 to-blue-600'
    },
    {
      title: t.activeProjects,
      value: departmentMetrics.activeProjects.toString(),
      change: '+3',
      trend: 'up',
      icon: Briefcase,
      color: 'from-green-500 to-green-600'
    },
    {
      title: t.serviceRequests,
      value: departmentMetrics.serviceRequests.toString(),
      change: '+1',
      trend: 'up',
      icon: Target,
      color: 'from-purple-500 to-purple-600'
    },
    {
      title: t.teamWorkload,
      value: `${departmentMetrics.teamWorkload}%`,
      change: '+5%',
      trend: 'up',
      icon: TrendingUp,
      color: 'from-orange-500 to-orange-600'
    }
  ]

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">{t.title}</h1>
          <p className="text-white/70">{t.subtitle}</p>
        </div>

        <div className="flex items-center gap-3">
          <Button
            onClick={handleRefresh}
            disabled={refreshing}
            className="text-white/80 hover:text-white hover:bg-white/15 rounded-xl p-3 backdrop-blur-10"
          >
            <Calendar className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            {t.refresh}
          </Button>
          <Button className="text-white/80 hover:text-white hover:bg-white/15 rounded-xl p-3 backdrop-blur-10">
            <Building className="h-4 w-4 mr-2" />
            {t.addCustomer}
          </Button>
          <Button variant="outline" className="text-white/80 hover:text-white hover:bg-white/15 rounded-xl p-3 backdrop-blur-10">
            <Download className="h-4 w-4 mr-2" />
            {t.exportData}
          </Button>
        </div>
      </div>

      {/* Department Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {departmentMetricsCards.map((card, index) => (
          <Card key={index} className="glass-card border-white/20 hover:border-white/40 transition-all">
            <CardContent className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <p className="text-white/70 text-sm mb-1">{card.title}</p>
                  <p className="text-2xl font-bold text-white">{card.value}</p>
                </div>
                <div className={`p-3 rounded-xl bg-gradient-to-r ${card.color} shadow-lg`}>
                  <card.icon className="h-6 w-6 text-white" />
                </div>
              </div>

              <div className="flex items-center gap-2">
                <span className={`text-sm font-medium ${
                  card.trend === 'up' ? 'text-green-400' :
                  card.trend === 'down' ? 'text-red-400' : 'text-blue-400'
                }`}>
                  {card.change}
                </span>
                <span className="text-xs text-white/60">this month</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Search and Filters */}
      <Card className="glass-card border-white/20">
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/60" />
              <Input
                placeholder={t.searchCustomers}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-white/10 border-white/20 text-white placeholder:text-white/60"
              />
            </div>

            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="bg-white/10 border-white/20 text-white rounded-lg px-3 py-2"
            >
              <option value="">{t.projectStatus}</option>
              <option value="inProgress">{t.inProgress}</option>
              <option value="completed">{t.completed}</option>
              <option value="onHold">{t.onHold}</option>
              <option value="planning">{t.planning}</option>
            </select>

            <select
              value={selectedPriority}
              onChange={(e) => setSelectedPriority(e.target.value)}
              className="bg-white/10 border-white/20 text-white rounded-lg px-3 py-2"
            >
              <option value="">{t.priority}</option>
              <option value="high">{t.high}</option>
              <option value="medium">{t.medium}</option>
              <option value="low">{t.low}</option>
            </select>

            <Button variant="outline" className="text-white/80 hover:text-white hover:bg-white/15 rounded-xl p-3 backdrop-blur-10">
              <Filter className="h-4 w-4 mr-2" />
              {t.filterBy}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Customer Projects List */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white text-xl">{t.projectManagement}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredCustomers.map((customer) => (
              <div key={customer.id} className="p-6 glass-card border-white/10 hover:border-white/30 transition-all">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-white font-semibold text-lg">
                        {language === 'ar' ? customer.name : customer.nameEn}
                      </h3>
                      <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(customer.projectStatus)}`}>
                        {getStatusIcon(customer.projectStatus)}
                        {t[customer.projectStatus as keyof typeof t]}
                      </span>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getPriorityColor(customer.priority)}`}>
                        {t[customer.priority as keyof typeof t]}
                      </span>
                    </div>

                    <p className="text-white/80 text-lg mb-3">
                      {language === 'ar' ? customer.projectName : customer.projectNameEn}
                    </p>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-4">
                      <div>
                        <span className="text-white/60">{t.assignedTo}: </span>
                        <span className="text-white">
                          {language === 'ar' ? customer.assignedTo : customer.assignedToEn}
                        </span>
                      </div>
                      <div>
                        <span className="text-white/60">{t.deadline}: </span>
                        <span className="text-white">{customer.deadline}</span>
                      </div>
                      <div>
                        <span className="text-white/60">Budget: </span>
                        <span className="text-white">
                          {new Intl.NumberFormat('ar-SA', {
                            style: 'currency',
                            currency: 'SAR'
                          }).format(customer.budget)}
                        </span>
                      </div>
                      <div>
                        <span className="text-white/60">Last Update: </span>
                        <span className="text-white">{customer.lastUpdate}</span>
                      </div>
                    </div>

                    <div className="flex items-center gap-6 mb-4">
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-white/60 text-sm">{t.progress}</span>
                          <span className="text-white text-sm font-medium">{customer.progress}%</span>
                        </div>
                        <div className="w-full bg-white/20 rounded-full h-2">
                          <div
                            className={`h-2 rounded-full ${
                              customer.progress > 80 ? 'bg-green-500' :
                              customer.progress > 50 ? 'bg-blue-500' : 'bg-yellow-500'
                            }`}
                            style={{ width: `${customer.progress}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4 text-white/60" />
                        <span className="text-white/60 text-sm">Team: </span>
                        <div className="flex items-center gap-1">
                          {(language === 'ar' ? customer.teamMembers : customer.teamMembersEn).map((member, index) => (
                            <span key={index} className="text-white text-sm">
                              {member}{index < customer.teamMembers.length - 1 ? ', ' : ''}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-2 ml-4">
                    <Button size="sm" variant="outline" className="text-white/80 hover:text-white hover:bg-white/15 rounded-lg p-2 backdrop-blur-10">
                      <Briefcase className="h-4 w-4" />
                    </Button>
                    <Button size="sm" variant="outline" className="text-white/80 hover:text-white hover:bg-white/15 rounded-lg p-2 backdrop-blur-10">
                      <Users className="h-4 w-4" />
                    </Button>
                    <Button size="sm" variant="outline" className="text-white/80 hover:text-white hover:bg-white/15 rounded-lg p-2 backdrop-blur-10">
                      <Eye className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <div className="flex items-center justify-between pt-4 border-t border-white/10">
                  <div className="flex items-center gap-4">
                    <div className="text-sm">
                      <span className="text-white/60">Start Date: </span>
                      <span className="text-white">{customer.startDate}</span>
                    </div>
                    <div className="text-sm">
                      <span className="text-white/60">Team Size: </span>
                      <span className="text-white">{customer.teamMembers.length}</span>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <Button size="sm" className="text-white/80 hover:text-white hover:bg-white/15 rounded-lg p-2 backdrop-blur-10">
                      <Users className="h-4 w-4 mr-1" />
                      {t.assignTeam}
                    </Button>
                    <Button size="sm" variant="outline" className="text-white/80 hover:text-white hover:bg-white/15 rounded-lg p-2 backdrop-blur-10">
                      <Briefcase className="h-4 w-4 mr-1" />
                      {t.viewProject}
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
