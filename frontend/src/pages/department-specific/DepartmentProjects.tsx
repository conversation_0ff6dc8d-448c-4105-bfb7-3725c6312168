import { useState, useEffect } from 'react'
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Briefcase,
  Users,
  Calendar,
  Clock,
  Target,
  TrendingUp,
  Plus,
  Search,
  Filter,
  Download,
  CheckSquare,
  AlertTriangle,
  CheckCircle,
  Eye,
  Edit,
  MoreHorizontal,
  User,
  BarChart3
} from 'lucide-react'

interface DepartmentProjectsProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    title: 'إدارة المشاريع - مدير القسم',
    subtitle: 'إدارة مشاريع القسم وتتبع الأداء',
    totalProjects: 'إجمالي المشاريع',
    activeProjects: 'المشاريع النشطة',
    completedProjects: 'المشاريع المكتملة',
    projectsOnTime: 'المشاريع في الوقت المحدد',
    searchProjects: 'البحث في المشاريع...',
    createProject: 'إنشاء مشروع',
    exportData: 'تصدير البيانات',
    filterBy: 'تصفية حسب',
    priority: 'الأولوية',
    status: 'الحالة',
    assignee: 'المسؤول',
    projectList: 'قائمة المشاريع',
    projectName: 'اسم المشروع',
    description: 'الوصف',
    startDate: 'تاريخ البداية',
    endDate: 'تاريخ النهاية',
    progress: 'التقدم',
    teamSize: 'حجم الفريق',
    actions: 'الإجراءات',
    viewProject: 'عرض المشروع',
    editProject: 'تعديل المشروع',
    active: 'نشط',
    completed: 'مكتمل',
    onHold: 'معلق',
    planning: 'تخطيط',
    high: 'عالي',
    medium: 'متوسط',
    low: 'منخفض',
    onTime: 'في الوقت المحدد',
    delayed: 'متأخر',
    ahead: 'متقدم',
    thisMonth: 'هذا الشهر',
    refresh: 'تحديث',
    teamMembers: 'أعضاء الفريق',
    deadline: 'الموعد النهائي',
    budget: 'الميزانية'
  },
  en: {
    title: 'Project Management - Department Manager',
    subtitle: 'Manage department projects and track performance',
    totalProjects: 'Total Projects',
    activeProjects: 'Active Projects',
    completedProjects: 'Completed Projects',
    projectsOnTime: 'Projects On Time',
    searchProjects: 'Search projects...',
    createProject: 'Create Project',
    exportData: 'Export Data',
    filterBy: 'Filter By',
    priority: 'Priority',
    status: 'Status',
    assignee: 'Assignee',
    projectList: 'Project List',
    projectName: 'Project Name',
    description: 'Description',
    startDate: 'Start Date',
    endDate: 'End Date',
    progress: 'Progress',
    teamSize: 'Team Size',
    actions: 'Actions',
    viewProject: 'View Project',
    editProject: 'Edit Project',
    active: 'Active',
    completed: 'Completed',
    onHold: 'On Hold',
    planning: 'Planning',
    high: 'High',
    medium: 'Medium',
    low: 'Low',
    onTime: 'On Time',
    delayed: 'Delayed',
    ahead: 'Ahead',
    thisMonth: 'This Month',
    refresh: 'Refresh',
    teamMembers: 'Team Members',
    deadline: 'Deadline',
    budget: 'Budget'
  }
}

export default function DepartmentProjects({ language }: DepartmentProjectsProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedPriority, setSelectedPriority] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('')
  const [refreshing, setRefreshing] = useState(false)
  const t = translations[language]
  const isRTL = language === 'ar'

  // Department-specific project metrics
  const [projectMetrics, setProjectMetrics] = useState({
    totalProjects: 12,
    activeProjects: 8,
    completedProjects: 4,
    projectsOnTime: 6
  })

  // Sample project data with department-specific information
  const [projects] = useState([
    {
      id: 1,
      name: 'تطوير نظام إدارة المحتوى',
      nameEn: 'Content Management System Development',
      description: 'تطوير نظام شامل لإدارة المحتوى الرقمي',
      descriptionEn: 'Comprehensive digital content management system development',
      startDate: '2024-01-01',
      endDate: '2024-03-31',
      progress: 75,
      status: 'active',
      priority: 'high',
      teamSize: 5,
      budget: 150000,
      assignee: 'أحمد محمد',
      assigneeEn: 'Ahmed Mohammed',
      timeline: 'onTime'
    },
    {
      id: 2,
      name: 'تحديث واجهة المستخدم',
      nameEn: 'User Interface Update',
      description: 'تحديث وتحسين واجهة المستخدم للتطبيق الرئيسي',
      descriptionEn: 'Update and improve user interface for main application',
      startDate: '2024-01-15',
      endDate: '2024-02-28',
      progress: 90,
      status: 'active',
      priority: 'medium',
      teamSize: 3,
      budget: 80000,
      assignee: 'فاطمة علي',
      assigneeEn: 'Fatima Ali',
      timeline: 'ahead'
    },
    {
      id: 3,
      name: 'تطوير تطبيق الجوال',
      nameEn: 'Mobile App Development',
      description: 'تطوير تطبيق جوال للعملاء',
      descriptionEn: 'Customer mobile application development',
      startDate: '2023-11-01',
      endDate: '2024-01-31',
      progress: 100,
      status: 'completed',
      priority: 'high',
      teamSize: 4,
      budget: 200000,
      assignee: 'عمر سالم',
      assigneeEn: 'Omar Salem',
      timeline: 'onTime'
    },
    {
      id: 4,
      name: 'تحسين الأمان السيبراني',
      nameEn: 'Cybersecurity Enhancement',
      description: 'تعزيز أنظمة الأمان والحماية',
      descriptionEn: 'Enhance security and protection systems',
      startDate: '2024-02-01',
      endDate: '2024-04-30',
      progress: 45,
      status: 'active',
      priority: 'high',
      teamSize: 6,
      budget: 180000,
      assignee: 'نورا أحمد',
      assigneeEn: 'Nora Ahmed',
      timeline: 'delayed'
    }
  ])

  const handleRefresh = async () => {
    setRefreshing(true)
    await new Promise(resolve => setTimeout(resolve, 1000))
    setRefreshing(false)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-500/20 text-green-400 border-green-500/30'
      case 'completed':
        return 'bg-blue-500/20 text-blue-400 border-blue-500/30'
      case 'onHold':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
      case 'planning':
        return 'bg-purple-500/20 text-purple-400 border-purple-500/30'
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-500/20 text-red-400 border-red-500/30'
      case 'medium':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
      case 'low':
        return 'bg-green-500/20 text-green-400 border-green-500/30'
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30'
    }
  }

  const getTimelineColor = (timeline: string) => {
    switch (timeline) {
      case 'onTime':
        return 'text-green-400'
      case 'delayed':
        return 'text-red-400'
      case 'ahead':
        return 'text-blue-400'
      default:
        return 'text-gray-400'
    }
  }

  const getProgressColor = (progress: number) => {
    if (progress >= 90) return 'bg-green-500'
    if (progress >= 70) return 'bg-blue-500'
    if (progress >= 50) return 'bg-yellow-500'
    return 'bg-red-500'
  }

  const filteredProjects = projects.filter(project => {
    const matchesSearch = project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.nameEn.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.assignee.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesPriority = !selectedPriority || project.priority === selectedPriority
    const matchesStatus = !selectedStatus || project.status === selectedStatus
    
    return matchesSearch && matchesPriority && matchesStatus
  })

  const projectMetricsCards = [
    {
      title: t.totalProjects,
      value: projectMetrics.totalProjects.toString(),
      change: '+2',
      trend: 'up',
      icon: Briefcase,
      color: 'from-blue-500 to-blue-600'
    },
    {
      title: t.activeProjects,
      value: projectMetrics.activeProjects.toString(),
      change: '+1',
      trend: 'up',
      icon: Target,
      color: 'from-green-500 to-green-600'
    },
    {
      title: t.completedProjects,
      value: projectMetrics.completedProjects.toString(),
      change: '+1',
      trend: 'up',
      icon: CheckCircle,
      color: 'from-purple-500 to-purple-600'
    },
    {
      title: t.projectsOnTime,
      value: projectMetrics.projectsOnTime.toString(),
      change: t.onTime,
      trend: 'stable',
      icon: Clock,
      color: 'from-cyan-500 to-cyan-600'
    }
  ]

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">{t.title}</h1>
          <p className="text-white/70">{t.subtitle}</p>
        </div>
        
        <div className="flex items-center gap-3">
          <Button
            onClick={handleRefresh}
            disabled={refreshing}
            className="glass-button"
          >
            <Clock className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            {t.refresh}
          </Button>
          <Button className="glass-button">
            <Plus className="h-4 w-4 mr-2" />
            {t.createProject}
          </Button>
          <Button variant="outline" className="glass-button">
            <Download className="h-4 w-4 mr-2" />
            {t.exportData}
          </Button>
        </div>
      </div>

      {/* Project Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {projectMetricsCards.map((card, index) => (
          <Card key={index} className="glass-card border-white/20 hover:border-white/40 transition-all">
            <CardContent className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <p className="text-white/70 text-sm mb-1">{card.title}</p>
                  <p className="text-2xl font-bold text-white">{card.value}</p>
                </div>
                <div className={`p-3 rounded-xl bg-gradient-to-r ${card.color} shadow-lg`}>
                  <card.icon className="h-6 w-6 text-white" />
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <span className={`text-sm font-medium ${
                  card.trend === 'up' ? 'text-green-400' : 
                  card.trend === 'down' ? 'text-red-400' : 'text-blue-400'
                }`}>
                  {card.change}
                </span>
                <span className="text-xs text-white/60">vs last month</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Search and Filters */}
      <Card className="glass-card border-white/20">
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/60" />
              <Input
                placeholder={t.searchProjects}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 glass-input"
              />
            </div>
            
            <select
              value={selectedPriority}
              onChange={(e) => setSelectedPriority(e.target.value)}
              className="glass-input"
            >
              <option value="">{t.priority}</option>
              <option value="high">{t.high}</option>
              <option value="medium">{t.medium}</option>
              <option value="low">{t.low}</option>
            </select>
            
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="glass-input"
            >
              <option value="">{t.status}</option>
              <option value="active">{t.active}</option>
              <option value="completed">{t.completed}</option>
              <option value="onHold">{t.onHold}</option>
              <option value="planning">{t.planning}</option>
            </select>
            
            <Button variant="outline" className="glass-button">
              <Filter className="h-4 w-4 mr-2" />
              {t.filterBy}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Project List */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white text-xl">{t.projectList}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredProjects.map((project) => (
              <div key={project.id} className="p-6 glass-card border-white/10 hover:border-white/30 transition-all">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-white font-semibold text-lg">
                        {language === 'ar' ? project.name : project.nameEn}
                      </h3>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getPriorityColor(project.priority)}`}>
                        {t[project.priority as keyof typeof t]}
                      </span>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(project.status)}`}>
                        {t[project.status as keyof typeof t]}
                      </span>
                    </div>
                    <p className="text-white/70 mb-3">
                      {language === 'ar' ? project.description : project.descriptionEn}
                    </p>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-white/60">{t.assignee}: </span>
                        <span className="text-white">{language === 'ar' ? project.assignee : project.assigneeEn}</span>
                      </div>
                      <div>
                        <span className="text-white/60">{t.teamSize}: </span>
                        <span className="text-white">{project.teamSize} {t.teamMembers}</span>
                      </div>
                      <div>
                        <span className="text-white/60">{t.deadline}: </span>
                        <span className="text-white">{project.endDate}</span>
                      </div>
                      <div>
                        <span className="text-white/60">{t.budget}: </span>
                        <span className="text-white">
                          {new Intl.NumberFormat('ar-SA', {
                            style: 'currency',
                            currency: 'SAR'
                          }).format(project.budget)}
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2 ml-4">
                    <Button size="sm" variant="outline" className="glass-button p-2">
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button size="sm" variant="outline" className="glass-button p-2">
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button size="sm" variant="outline" className="glass-button p-2">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2">
                      <span className="text-white/60 text-sm">{t.progress}:</span>
                      <div className="w-32 bg-white/20 rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full ${getProgressColor(project.progress)}`}
                          style={{ width: `${project.progress}%` }}
                        ></div>
                      </div>
                      <span className="text-white text-sm font-medium">{project.progress}%</span>
                    </div>
                  </div>
                  
                  <div className={`flex items-center gap-1 ${getTimelineColor(project.timeline)}`}>
                    {project.timeline === 'onTime' && <CheckCircle className="h-4 w-4" />}
                    {project.timeline === 'delayed' && <AlertTriangle className="h-4 w-4" />}
                    {project.timeline === 'ahead' && <TrendingUp className="h-4 w-4" />}
                    <span className="text-sm font-medium">
                      {t[project.timeline as keyof typeof t]}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
