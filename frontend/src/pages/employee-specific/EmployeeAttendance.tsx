/**
 * Employee Attendance Page - Check In/Out and View History
 * Employees can check in/out and view their attendance history
 */

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Calendar,
  Clock,
  CheckCircle,
  XCircle,
  Eye,
  User,
  MapPin,
  Timer,
  TrendingUp,
  LogIn,
  LogOut,
  History,
  BarChart3
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { attendanceService } from '@/services/crudService'

interface EmployeeAttendanceProps {
  language: 'ar' | 'en'
}

interface AttendanceRecord {
  id: number
  date: string
  checkIn: string
  checkOut?: string
  totalHours?: number
  status: 'present' | 'absent' | 'late' | 'halfDay'
  location: string
  locationAr: string
  notes?: string
  notesAr?: string
  isLate: boolean
  lateMinutes?: number
  overtimeHours?: number
}

interface TodayAttendance {
  hasCheckedIn: boolean
  hasCheckedOut: boolean
  checkInTime?: string
  checkOutTime?: string
  totalHours?: number
  status: 'not_started' | 'checked_in' | 'checked_out'
}

const translations = {
  ar: {
    employeeAttendance: 'الحضور والانصراف',
    todayAttendance: 'حضور اليوم',
    attendanceHistory: 'سجل الحضور',
    checkInNow: 'تسجيل الدخول',
    checkOutNow: 'تسجيل الخروج',
    alreadyCheckedIn: 'تم تسجيل الدخول بالفعل',
    alreadyCheckedOut: 'تم تسجيل الخروج بالفعل',
    checkInSuccess: 'تم تسجيل الدخول بنجاح',
    checkOutSuccess: 'تم تسجيل الخروج بنجاح',
    currentTime: 'الوقت الحالي',
    workingHours: 'ساعات العمل',
    thisWeek: 'هذا الأسبوع',
    thisMonth: 'هذا الشهر',
    view: 'عرض',
    date: 'التاريخ',
    checkIn: 'وقت الدخول',
    checkOut: 'وقت الخروج',
    totalHours: 'إجمالي الساعات',
    status: 'الحالة',
    location: 'الموقع',
    present: 'حاضر',
    absent: 'غائب',
    late: 'متأخر',
    halfDay: 'نصف يوم',
    notStarted: 'لم يبدأ',
    checkedIn: 'مسجل دخول',
    checkedOut: 'مسجل خروج',
    hours: 'ساعات',
    minutes: 'دقائق'
  },
  en: {
    employeeAttendance: 'Attendance & Time Tracking',
    todayAttendance: "Today's Attendance",
    attendanceHistory: 'Attendance History',
    checkInNow: 'Check In',
    checkOutNow: 'Check Out',
    alreadyCheckedIn: 'Already checked in',
    alreadyCheckedOut: 'Already checked out',
    checkInSuccess: 'Checked in successfully',
    checkOutSuccess: 'Checked out successfully',
    currentTime: 'Current Time',
    workingHours: 'Working Hours',
    thisWeek: 'This Week',
    thisMonth: 'This Month',
    view: 'View',
    date: 'Date',
    checkIn: 'Check In',
    checkOut: 'Check Out',
    totalHours: 'Total Hours',
    status: 'Status',
    location: 'Location',
    present: 'Present',
    absent: 'Absent',
    late: 'Late',
    halfDay: 'Half Day',
    notStarted: 'Not Started',
    checkedIn: 'Checked In',
    checkedOut: 'Checked Out',
    hours: 'hours',
    minutes: 'minutes'
  }
}

// @ts-ignore
export default function EmployeeAttendance({ language }: EmployeeAttendanceProps as any): (React as any).ReactElement {
  // @ts-ignore
  const [currentTime, setCurrentTime] = useState(new Date( as any))
  const [todayAttendance, setTodayAttendance] = useState<TodayAttendance>({
    hasCheckedIn: false,
    hasCheckedOut: false,
    status: 'not_started'
  })
  const [showHistory, setShowHistory] = useState(false as any)

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook for attendance history
  const {
    items: attendanceRecords,
    loading,
    error,
    createItem,
    refresh,
    exportData,
    clearError
  } = useCrud<AttendanceRecord>({
    service: attendanceService,
    autoLoad: true,
    pageSize: 20
  })

  // Update current time every second
  // @ts-ignore
  useEffect(( as any) => {
    // @ts-ignore
    const timer = setInterval(( as any) => {
      // @ts-ignore
      setCurrentTime(new Date( as any))
    // @ts-ignore
    }, 1000)

    return () => clearInterval(timer as any)
  // @ts-ignore
  }, [])

  // Load today's attendance status
  // @ts-ignore
  useEffect(( as any) => {
    // @ts-ignore
    loadTodayAttendance( as any)
  // @ts-ignore
  }, [])

  const loadTodayAttendance = async () => {
    try {
      // @ts-ignore
      const today = new Date( as any).toISOString( as any).split('T' as any)[0]
      const todayRecord = (attendanceRecords as any).find(record => (record as any as any).date === today)

      if (todayRecord) {
        setTodayAttendance({
          hasCheckedIn: !!(todayRecord as any as any).checkIn,
          hasCheckedOut: !!(todayRecord as any).checkOut,
          checkInTime: (todayRecord as any).checkIn,
          checkOutTime: (todayRecord as any).checkOut,
          totalHours: (todayRecord as any).totalHours,
          status: (todayRecord as any).checkOut ? 'checked_out' : 'checked_in'
        })
      }
    } catch (error) {
      (console as any).error('Error loading today attendance:', error as any)
    }
  }

  // Check-in/Check-out functions
  const handleCheckIn = async () => {
    try {
      // @ts-ignore
      const now = new Date( as any)
      // @ts-ignore
      const timeString = (now as any).toTimeString( as any).split(' ' as any)[0].substring(0, 5 as any) // HH:MM format
      // @ts-ignore
      const today = (now as any).toISOString( as any).split('T' as any)[0]

      const attendanceData = {
        date: today,
        checkIn: timeString,
        status: 'present',
        location: 'Office',
        locationAr: 'المكتب',
        isLate: false,
        lateMinutes: 0
      }

      await createItem(attendanceData as any as any)

      setTodayAttendance({
        hasCheckedIn: true,
        hasCheckedOut: false,
        checkInTime: timeString,
        status: 'checked_in'
      } as any)

      alert((t as any as any).checkInSuccess)
    } catch (error) {
      (console as any).error('Check-in error:', error as any)
      alert('Check-in failed. Please try again.' as any)
    }
  }

  const handleCheckOut = async () => {
    try {
      // @ts-ignore
      const now = new Date( as any)
      // @ts-ignore
      const timeString = (now as any).toTimeString( as any).split(' ' as any)[0].substring(0, 5 as any) // HH:MM format
      // @ts-ignore
      const today = (now as any).toISOString( as any).split('T' as any)[0]

      // Find today's record
      const todayRecord = (attendanceRecords as any).find(record => (record as any as any).date === today)

      if (todayRecord && (todayAttendance as any).checkInTime) {
        const totalHours = calculateTotalHours((todayAttendance as any as any).checkInTime, timeString)

        const updatedData = {
          ...todayRecord,
          checkOut: timeString,
          totalHours: totalHours
        }

        // Note: This would typically be an update operation
        // For now, we'll create a new record with checkout info
        await createItem(updatedData as any)

        setTodayAttendance({
          ...todayAttendance,
          hasCheckedOut: true,
          checkOutTime: timeString,
          totalHours: totalHours,
          status: 'checked_out'
        } as any)

        alert((t as any as any).checkOutSuccess)
      }
    } catch (error) {
      (console as any).error('Check-out error:', error as any)
      alert('Check-out failed. Please try again.' as any)
    }
  }

  // Helper functions
  const getStatusColor = (status: string): void => {
    switch (status) {
      case 'present':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'late':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'absent':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'halfDay':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }



  const calculateTotalHours = (checkIn: string, checkOut: string): void => {
    if (!checkIn || !checkOut) return 0
    const start = new Date(`2000-01-01T${checkIn}` as any)
    const end = new Date(`2000-01-01T${checkOut}` as any)
    // @ts-ignore
    const diffMs = (end as any).getTime( as any) - (start as any).getTime( as any)
    return (Math as any).round((diffMs / (1000 * 60 * 60 as any)) * 100) / 100
  }

  const formatTime = (timeString?: string): string => {
    if (!timeString) return '-'
    return timeString
  }

  const formatDuration = (hours?: number): string => {
    if (!hours) return '-'
    const h = (Math as any).floor(hours as any)
    const m = (Math as any).round((hours - h as any) * 60)
    return `${h}${(t as any).hours} ${m}${(t as any).minutes}`
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-white">{(t as any).employeeAttendance}</h1>
        <div className="text-white/70">
          // @ts-ignore
          {(t as any).currentTime}: {(currentTime as any).toLocaleTimeString( as any)}
        </div>
      </div>

      {/* Today's Attendance Card */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Clock className="h-5 w-5" />
            {(t as any).todayAttendance}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Status Display */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 glass-card border-white/10 rounded-lg">
              <div className="text-white/70 text-sm mb-1">{(t as any).status}</div>
              <Badge className={
                (todayAttendance as any).status === 'checked_out' ? 'bg-green-100 text-green-800' :
                (todayAttendance as any).status === 'checked_in' ? 'bg-blue-100 text-blue-800' :
                'bg-gray-100 text-gray-800'
              }>
                {t[(todayAttendance as any).status as keyof typeof t]}
              </Badge>
            </div>

            <div className="text-center p-4 glass-card border-white/10 rounded-lg">
              <div className="text-white/70 text-sm mb-1">{(t as any).checkIn}</div>
              <div className="text-white font-medium">
                {formatTime((todayAttendance as any as any).checkInTime)}
              </div>
            </div>

            <div className="text-center p-4 glass-card border-white/10 rounded-lg">
              <div className="text-white/70 text-sm mb-1">{(t as any).checkOut}</div>
              <div className="text-white font-medium">
                {formatTime((todayAttendance as any as any).checkOutTime)}
              </div>
            </div>
          </div>

          {/* Working Hours */}
          {(todayAttendance as any).totalHours && (
            <div className="text-center p-4 glass-card border-white/10 rounded-lg">
              <div className="text-white/70 text-sm mb-1">{(t as any).workingHours}</div>
              <div className="text-2xl font-bold text-blue-400">
                {formatDuration((todayAttendance as any as any).totalHours)}
              </div>
            </div>
          )}

          {/* Check In/Out Buttons */}
          <div className="flex justify-center gap-4">
            {!(todayAttendance as any).hasCheckedIn ? (
              <Button
                onClick={handleCheckIn}
                className="glass-button bg-green-500/20 hover:bg-green-500/30 border-green-500/50"
                size="lg"
              >
                <LogIn className="h-5 w-5 mr-2" />
                {(t as any).checkInNow}
              </Button>
            ) : !(todayAttendance as any).hasCheckedOut ? (
              <Button
                onClick={handleCheckOut}
                className="glass-button bg-red-500/20 hover:bg-red-500/30 border-red-500/50"
                size="lg"
              >
                <LogOut className="h-5 w-5 mr-2" />
                {(t as any).checkOutNow}
              </Button>
            ) : (
              <div className="text-center">
                <CheckCircle className="h-12 w-12 text-green-400 mx-auto mb-2" />
                <p className="text-white/70">{(t as any).alreadyCheckedOut}</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Attendance History */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-white flex items-center gap-2">
              <History className="h-5 w-5" />
              {(t as any).attendanceHistory}
            </CardTitle>
            <Button
              onClick={() => setShowHistory(!showHistory as any)}
              variant="outline"
              className="glass-button"
            >
              <Eye className="h-4 w-4 mr-2" />
              {showHistory ? 'Hide' : (t as any).view}
            </Button>
          </div>
        </CardHeader>

        {showHistory && (
          <CardContent>
            <div className="space-y-3">
              // @ts-ignore
              {(attendanceRecords as any).slice(0, 10 as any).map((record as any) => (
                <div key={(record as any).id} className="flex items-center justify-between p-3 glass-card border-white/10 rounded-lg">
                  <div className="flex items-center gap-3">
                    <Calendar className="h-4 w-4 text-blue-400" />
                    <div>
                      <div className="text-white font-medium">{(record as any).date}</div>
                      <div className="text-white/60 text-sm">
                        {language === 'ar' ? (record as any).locationAr : (record as any).location}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-4">
                    <div className="text-center">
                      <div className="text-white/70 text-xs">{(t as any).checkIn}</div>
                      <div className="text-white text-sm">{formatTime((record as any as any).checkIn)}</div>
                    </div>

                    <div className="text-center">
                      <div className="text-white/70 text-xs">{(t as any).checkOut}</div>
                      <div className="text-white text-sm">{formatTime((record as any as any).checkOut)}</div>
                    </div>

                    <div className="text-center">
                      <div className="text-white/70 text-xs">{(t as any).totalHours}</div>
                      <div className="text-white text-sm">{formatDuration((record as any as any).totalHours)}</div>
                    </div>

                    <Badge className={getStatusColor((record as any as any).status)}>
                      {t[(record as any).status as keyof typeof t]}
                    </Badge>
                  </div>
                </div>
              // @ts-ignore
              ))}

              {(attendanceRecords as any).length === 0 && (
                <div className="text-center py-8 text-white/60">
                  No attendance records found
                </div>
              )}
            </div>
          </CardContent>
        )}
      </Card>
    </div>
  )
}
