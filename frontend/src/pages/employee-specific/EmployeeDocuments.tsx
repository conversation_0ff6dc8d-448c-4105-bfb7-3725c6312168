/**
 * Employee Documents Page - READ-ONLY Implementation
 * Restricted version for employees - can only view essential company documents
 * No create, edit, delete, or export capabilities
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  FileText,
  Download,
  Eye,
  File,
  Image,
  Video,
  Calendar,
  User,
  Lock,
  Globe,
  Folder,
  RefreshCw
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { documentService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface EmployeeDocumentsProps {
  language: 'ar' | 'en'
}

interface Document {
  id: number
  title: string
  titleAr: string
  description: string
  descriptionAr: string
  fileName: string
  fileSize: number
  fileType: string
  category: string
  tags: string[]
  uploadDate: string
  uploadedBy: string
  lastModified: string
  downloadCount: number
  isPublic: boolean
  accessLevel: 'public' | 'internal' | 'restricted' | 'confidential'
  department: string
  version: string
}

// Translations
const translations = {
  ar: {
    employeeDocuments: 'المستندات المتاحة',
    searchPlaceholder: 'البحث في المستندات...',
    view: 'عرض',
    download: 'تحميل',
    title: 'العنوان',
    category: 'الفئة',
    fileType: 'نوع الملف',
    fileSize: 'حجم الملف',
    uploadDate: 'تاريخ الرفع',
    uploadedBy: 'رفع بواسطة',
    accessLevel: 'مستوى الوصول',
    public: 'عام',
    internal: 'داخلي',
    restricted: 'مقيد',
    confidential: 'سري',
    all: 'الكل',
    filterByCategory: 'تصفية حسب الفئة',
    filterByType: 'تصفية حسب النوع',
    filterByAccess: 'تصفية حسب مستوى الوصول',
    policies: 'السياسات',
    procedures: 'الإجراءات',
    forms: 'النماذج',
    manuals: 'الأدلة',
    reports: 'التقارير',
    contracts: 'العقود',
    presentations: 'العروض التقديمية',
    images: 'الصور',
    videos: 'الفيديوهات',
    archives: 'الأرشيف'
  },
  en: {
    employeeDocuments: 'Available Documents',
    searchPlaceholder: 'Search documents...',
    view: 'View',
    download: 'Download',
    title: 'Title',
    category: 'Category',
    fileType: 'File Type',
    fileSize: 'File Size',
    uploadDate: 'Upload Date',
    uploadedBy: 'Uploaded By',
    accessLevel: 'Access Level',
    public: 'Public',
    internal: 'Internal',
    restricted: 'Restricted',
    confidential: 'Confidential',
    all: 'All',
    filterByCategory: 'Filter by Category',
    filterByType: 'Filter by Type',
    filterByAccess: 'Filter by Access Level',
    policies: 'Policies',
    procedures: 'Procedures',
    forms: 'Forms',
    manuals: 'Manuals',
    reports: 'Reports',
    contracts: 'Contracts',
    presentations: 'Presentations',
    images: 'Images',
    videos: 'Videos',
    archives: 'Archives'
  }
}

// @ts-ignore
export default function EmployeeDocuments({ language }: EmployeeDocumentsProps as any): (React as any).ReactElement {
  const [showModal, setShowModal] = useState(false as any)

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook - READ-ONLY for employees
  const {
    items: documents,
    selectedItem,
    loading,
    error,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    clearError
  } = useCrud<Document>({
    service: documentService,
    autoLoad: true,
    pageSize: 20
  })

  // Filter documents to only show public and internal documents for employees
  const filteredDocuments = (documents as any).filter(doc => 
    (doc as any as any).accessLevel === 'public' || (doc as any).accessLevel === 'internal'
  )

  // Table columns configuration
  const columns: TableColumn<Document>[] = [
    {
      key: 'title',
      label: (t as any).title,
      sortable: true,
      render: (item: Document) => (
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <FileText className="h-5 w-5 text-blue-400" />
          <div>
            <div className="font-medium text-white">
              {language === 'ar' ? (item as any).titleAr : (item as any).title}
            </div>
            <div className="text-sm text-gray-400">
              {(item as any).fileName}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'category',
      label: (t as any).category,
      sortable: true,
      render: (item: Document) => (
        <Badge className="bg-purple-500/20 text-purple-400">
          {t[(item as any).category as keyof typeof t] || (item as any).category}
        </Badge>
      )
    },
    {
      key: 'fileType',
      label: (t as any).fileType,
      render: (item: Document) => {
        const getFileIcon = (type: string): void => {
          if ((type as any).includes('image' as any)) return <Image className="h-4 w-4" />
          if ((type as any).includes('video' as any)) return <Video className="h-4 w-4" />
          return <File className="h-4 w-4" />
        }
        return (
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            {getFileIcon((item as any as any).fileType)}
            <span className="text-gray-300">{(item as any).fileType}</span>
          </div>
        )
      }
    },
    {
      key: 'fileSize',
      label: (t as any).fileSize,
      render: (item: Document) => (
        <span className="text-gray-300">
          {((item as any).fileSize / 1024 / 1024).toFixed(2 as any)} MB
        </span>
      )
    },
    {
      key: 'uploadDate',
      label: (t as any).uploadDate,
      sortable: true,
      render: (item: Document) => (
        <div className="flex items-center space-x-2 rtl:space-x-reverse">
          <Calendar className="h-4 w-4 text-gray-400" />
          <span className="text-gray-300">{(item as any).uploadDate}</span>
        </div>
      )
    },
    {
      key: 'accessLevel',
      label: (t as any).accessLevel,
      render: (item: Document) => {
        const accessColors = {
          public: 'bg-green-500/20 text-green-400',
          internal: 'bg-blue-500/20 text-blue-400',
          restricted: 'bg-orange-500/20 text-orange-400',
          confidential: 'bg-red-500/20 text-red-400'
        }
        const accessIcons = {
          public: <Globe className="h-3 w-3" />,
          internal: <Folder className="h-3 w-3" />,
          restricted: <Lock className="h-3 w-3" />,
          confidential: <Lock className="h-3 w-3" />
        }
        return (
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            {accessIcons[(item as any).accessLevel]}
            <Badge className={accessColors[(item as any).accessLevel]}>
              {t[(item as any).accessLevel]}
            </Badge>
          </div>
        )
      }
    }
  ]

  // Table actions configuration - RESTRICTED for employees (VIEW and DOWNLOAD ONLY)
  const actions: TableAction<Document>[] = [
    {
      label: (t as any).view,
      icon: Eye,
      onClick: (item: Document) => {
        selectItem(item as any)
        setShowModal(true as any)
      },
      variant: 'ghost'
    },
    {
      label: (t as any).download,
      icon: Download,
      onClick: (item: Document) => {
        // Handle document download
        (console as any).log('Download document:', (item as any as any).fileName)
      },
      variant: 'ghost',
      className: 'text-green-400 hover:text-green-300 hover:bg-green-500/20'
    }
    // REMOVED: Edit, Delete, Upload, and other management actions
  ]

  // Filter options - LIMITED for employees
  const filterOptions: FilterOption[] = [
    {
      key: 'category',
      label: (t as any).filterByCategory,
      options: [
        { value: '', label: (t as any).all },
        { value: 'policies', label: (t as any).policies },
        { value: 'procedures', label: (t as any).procedures },
        { value: 'forms', label: (t as any).forms },
        { value: 'manuals', label: (t as any).manuals }
        // REMOVED: Sensitive categories like contracts, reports
      ]
    },
    {
      key: 'fileType',
      label: (t as any).filterByType,
      options: [
        { value: '', label: (t as any).all },
        { value: 'pdf', label: 'PDF' },
        { value: 'doc', label: 'Word' },
        { value: 'image', label: (t as any).images }
      ]
    }
    // REMOVED: Access level filter - employees only see public/internal
  ]

  // Event handlers - RESTRICTED for employees
  const handleModalClose = (): void => {
    setShowModal(false as any)
    selectItem(null as any)
    // @ts-ignore
    clearError( as any)
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* READ-ONLY Table for Employee Documents */}
      <CrudTable
        title={(t as any).employeeDocuments}
        data={filteredDocuments}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={(t as any).searchPlaceholder}
        language={language}
        // REMOVED: onCreate - employees cannot create documents
        onRefresh={refresh}
        // REMOVED: onExport - employees cannot export data
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
        showCreateButton={false}
        showExportButton={false}
      />

      {/* VIEW-ONLY Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        // REMOVED: onSave - employees cannot save/edit documents
        title={(t as any).view}
        fields={[]} // No form fields needed for view-only
        initialData={selectedItem as any}
        language={language}
        loading={false}
        readOnly={true}
      />
    </div>
  )
}
