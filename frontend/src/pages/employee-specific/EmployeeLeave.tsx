import { useState, useEffect } from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Calendar,
  Clock,
  Plus,
  Search,
  Filter,
  Eye,
  Edit,
  X,
  CheckCircle,
  AlertTriangle,
  CalendarDays,
  Plane,
  Heart,
  Briefcase,
  User,
  FileText
} from 'lucide-react'

interface EmployeeLeaveProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    title: 'طلبات الإجازة - الموظف',
    subtitle: 'إدارة طلبات الإجازة والرصيد المتاح',
    leaveBalance: 'رصيد الإجازات',
    requestLeave: 'طلب إجازة',
    myRequests: 'طلباتي',
    searchRequests: 'البحث في الطلبات...',
    newRequest: 'طلب جديد',
    leaveType: 'نوع الإجازة',
    startDate: 'تاريخ البداية',
    endDate: 'تاريخ النهاية',
    duration: 'المدة',
    reason: 'السبب',
    status: 'الحالة',
    actions: 'الإجراءات',
    submit: 'إرسال',
    cancel: 'إلغاء',
    edit: 'تعديل',
    view: 'عرض',
    delete: 'حذف',
    pending: 'قيد المراجعة',
    approved: 'موافق عليه',
    rejected: 'مرفوض',
    days: 'أيام',
    hours: 'ساعات',
    annual: 'إجازة سنوية',
    sick: 'إجازة مرضية',
    emergency: 'إجازة طارئة',
    maternity: 'إجازة أمومة',
    paternity: 'إجازة أبوة',
    unpaid: 'إجازة بدون راتب',
    available: 'متاح',
    used: 'مستخدم',
    remaining: 'متبقي',
    totalDays: 'إجمالي الأيام',
    requestDate: 'تاريخ الطلب',
    approvedBy: 'موافق من',
    comments: 'التعليقات',
    attachments: 'المرفقات',
    uploadDocument: 'رفع مستند',
    medicalCertificate: 'شهادة طبية',
    required: 'مطلوب',
    optional: 'اختياري',
    selectDates: 'اختر التواريخ',
    calculateDuration: 'حساب المدة',
    workingDays: 'أيام العمل',
    weekends: 'عطل نهاية الأسبوع',
    holidays: 'العطل الرسمية',
    total: 'الإجمالي',
    filter: 'تصفية'
  },
  en: {
    title: 'Leave Requests - Employee',
    subtitle: 'Manage leave requests and available balance',
    leaveBalance: 'Leave Balance',
    requestLeave: 'Request Leave',
    myRequests: 'My Requests',
    searchRequests: 'Search requests...',
    newRequest: 'New Request',
    leaveType: 'Leave Type',
    startDate: 'Start Date',
    endDate: 'End Date',
    duration: 'Duration',
    reason: 'Reason',
    status: 'Status',
    actions: 'Actions',
    submit: 'Submit',
    cancel: 'Cancel',
    edit: 'Edit',
    view: 'View',
    delete: 'Delete',
    pending: 'Pending',
    approved: 'Approved',
    rejected: 'Rejected',
    days: 'Days',
    hours: 'Hours',
    annual: 'Annual Leave',
    sick: 'Sick Leave',
    emergency: 'Emergency Leave',
    maternity: 'Maternity Leave',
    paternity: 'Paternity Leave',
    unpaid: 'Unpaid Leave',
    available: 'Available',
    used: 'Used',
    remaining: 'Remaining',
    totalDays: 'Total Days',
    requestDate: 'Request Date',
    approvedBy: 'Approved By',
    comments: 'Comments',
    attachments: 'Attachments',
    uploadDocument: 'Upload Document',
    medicalCertificate: 'Medical Certificate',
    required: 'Required',
    optional: 'Optional',
    selectDates: 'Select Dates',
    calculateDuration: 'Calculate Duration',
    workingDays: 'Working Days',
    weekends: 'Weekends',
    holidays: 'Public Holidays',
    total: 'Total',
    filter: 'Filter'
  }
}

export default function EmployeeLeave({ language }: EmployeeLeaveProps) {
  const [showNewRequest, setShowNewRequest] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('')
  const t = translations[language]
  const isRTL = language === 'ar'

  // Leave balance data
  const [leaveBalance] = useState([
    { type: 'annual', typeAr: 'إجازة سنوية', total: 30, used: 12, remaining: 18, icon: Plane, color: 'from-blue-500 to-blue-600' },
    { type: 'sick', typeAr: 'إجازة مرضية', total: 15, used: 3, remaining: 12, icon: Heart, color: 'from-red-500 to-red-600' },
    { type: 'emergency', typeAr: 'إجازة طارئة', total: 5, used: 1, remaining: 4, icon: AlertTriangle, color: 'from-orange-500 to-orange-600' },
    { type: 'unpaid', typeAr: 'إجازة بدون راتب', total: 10, used: 0, remaining: 10, icon: Briefcase, color: 'from-gray-500 to-gray-600' }
  ])

  // Leave requests data
  const [leaveRequests] = useState([
    {
      id: 1,
      type: 'annual',
      typeAr: 'إجازة سنوية',
      startDate: '2024-02-15',
      endDate: '2024-02-20',
      duration: 6,
      reason: 'إجازة عائلية',
      reasonEn: 'Family vacation',
      status: 'approved',
      requestDate: '2024-01-20',
      approvedBy: 'سارة أحمد',
      approvedByEn: 'Sara Ahmed',
      comments: 'موافق عليها'
    },
    {
      id: 2,
      type: 'sick',
      typeAr: 'إجازة مرضية',
      startDate: '2024-01-10',
      endDate: '2024-01-12',
      duration: 3,
      reason: 'مرض',
      reasonEn: 'Illness',
      status: 'approved',
      requestDate: '2024-01-09',
      approvedBy: 'سارة أحمد',
      approvedByEn: 'Sara Ahmed',
      comments: 'مع شهادة طبية'
    },
    {
      id: 3,
      type: 'annual',
      typeAr: 'إجازة سنوية',
      startDate: '2024-03-01',
      endDate: '2024-03-05',
      duration: 5,
      reason: 'سفر',
      reasonEn: 'Travel',
      status: 'pending',
      requestDate: '2024-02-01',
      approvedBy: '',
      approvedByEn: '',
      comments: 'قيد المراجعة'
    }
  ])

  const [newRequest, setNewRequest] = useState({
    type: '',
    startDate: '',
    endDate: '',
    reason: '',
    attachments: []
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-500/20 text-green-400 border-green-500/30'
      case 'pending':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
      case 'rejected':
        return 'bg-red-500/20 text-red-400 border-red-500/30'
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30'
    }
  }

  const getLeaveTypeIcon = (type: string) => {
    switch (type) {
      case 'annual':
        return <Plane className="h-4 w-4" />
      case 'sick':
        return <Heart className="h-4 w-4" />
      case 'emergency':
        return <AlertTriangle className="h-4 w-4" />
      case 'unpaid':
        return <Briefcase className="h-4 w-4" />
      default:
        return <Calendar className="h-4 w-4" />
    }
  }

  const filteredRequests = leaveRequests.filter(request => {
    const matchesSearch = request.reason.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         request.reasonEn.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = !selectedStatus || request.status === selectedStatus
    return matchesSearch && matchesStatus
  })

  const handleSubmitRequest = () => {
    // Submit logic here
    setShowNewRequest(false)
    setNewRequest({ type: '', startDate: '', endDate: '', reason: '', attachments: [] })
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">{t.title}</h1>
          <p className="text-white/70">{t.subtitle}</p>
        </div>

        <div className="flex items-center gap-3">
          <Button onClick={() => setShowNewRequest(true)} className="glass-button">
            <Plus className="h-4 w-4 mr-2" />
            {t.newRequest}
          </Button>
        </div>
      </div>

      {/* Leave Balance Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {leaveBalance.map((balance, index) => (
          <Card key={index} className="glass-card border-white/20 hover:border-white/40 transition-all">
            <CardContent className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <p className="text-white/70 text-sm mb-1">
                    {language === 'ar' ? balance.typeAr : balance.type}
                  </p>
                  <p className="text-2xl font-bold text-white">{balance.remaining}</p>
                  <p className="text-white/60 text-xs">{t.remaining} {t.days}</p>
                </div>
                <div className={`p-3 rounded-xl bg-gradient-to-r ${balance.color} shadow-lg`}>
                  <balance.icon className="h-6 w-6 text-white" />
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between text-xs">
                  <span className="text-white/60">{t.total}: {balance.total}</span>
                  <span className="text-white/60">{t.used}: {balance.used}</span>
                </div>
                <div className="w-full bg-white/20 rounded-full h-2">
                  <div
                    className={`bg-gradient-to-r ${balance.color} h-2 rounded-full transition-all duration-1000`}
                    style={{ width: `${(balance.remaining / balance.total) * 100}%` }}
                  ></div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* New Request Modal */}
      {showNewRequest && (
        <Card className="glass-card border-white/20">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-white text-xl">{t.newRequest}</CardTitle>
              <Button variant="ghost" size="sm" onClick={() => setShowNewRequest(false)} className="text-white/80 hover:text-white">
                <X className="h-5 w-5" />
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-white/70 text-sm mb-2">{t.leaveType}</label>
                <select
                  value={newRequest.type}
                  onChange={(e) => setNewRequest({...newRequest, type: e.target.value})}
                  className="glass-input w-full"
                >
                  <option value="">{t.leaveType}</option>
                  <option value="annual">{t.annual}</option>
                  <option value="sick">{t.sick}</option>
                  <option value="emergency">{t.emergency}</option>
                  <option value="unpaid">{t.unpaid}</option>
                </select>
              </div>
              <div>
                <label className="block text-white/70 text-sm mb-2">{t.duration}</label>
                <div className="flex items-center gap-2">
                  <Input
                    type="number"
                    placeholder="0"
                    className="glass-input"
                  />
                  <span className="text-white/70">{t.days}</span>
                </div>
              </div>
              <div>
                <label className="block text-white/70 text-sm mb-2">{t.startDate}</label>
                <Input
                  type="date"
                  value={newRequest.startDate}
                  onChange={(e) => setNewRequest({...newRequest, startDate: e.target.value})}
                  className="glass-input"
                />
              </div>
              <div>
                <label className="block text-white/70 text-sm mb-2">{t.endDate}</label>
                <Input
                  type="date"
                  value={newRequest.endDate}
                  onChange={(e) => setNewRequest({...newRequest, endDate: e.target.value})}
                  className="glass-input"
                />
              </div>
            </div>

            <div>
              <label className="block text-white/70 text-sm mb-2">{t.reason}</label>
              <textarea
                value={newRequest.reason}
                onChange={(e) => setNewRequest({...newRequest, reason: e.target.value})}
                className="glass-input w-full h-24 resize-none"
                placeholder={t.reason}
              />
            </div>

            <div>
              <label className="block text-white/70 text-sm mb-2">{t.attachments} ({t.optional})</label>
              <Button variant="outline" className="glass-button w-full">
                <FileText className="h-4 w-4 mr-2" />
                {t.uploadDocument}
              </Button>
            </div>

            <div className="flex gap-3 pt-4">
              <Button onClick={handleSubmitRequest} className="glass-button flex-1">
                {t.submit}
              </Button>
              <Button onClick={() => setShowNewRequest(false)} variant="outline" className="glass-button flex-1">
                {t.cancel}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Search and Filters */}
      <Card className="glass-card border-white/20">
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/60" />
              <Input
                placeholder={t.searchRequests}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 glass-input"
              />
            </div>

            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="glass-input"
            >
              <option value="">{t.status}</option>
              <option value="pending">{t.pending}</option>
              <option value="approved">{t.approved}</option>
              <option value="rejected">{t.rejected}</option>
            </select>

            <Button variant="outline" className="glass-button">
              <Filter className="h-4 w-4 mr-2" />
              {t.filter}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Requests List */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white text-xl">{t.myRequests}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredRequests.map((request) => (
              <div key={request.id} className="p-6 glass-card border-white/10 hover:border-white/30 transition-all">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 rounded-lg bg-white/10">
                      {getLeaveTypeIcon(request.type)}
                    </div>
                    <div>
                      <h3 className="text-white font-semibold">
                        {language === 'ar' ? request.typeAr : request.type}
                      </h3>
                      <p className="text-white/70 text-sm">
                        {language === 'ar' ? request.reason : request.reasonEn}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor(request.status)}`}>
                      {t[request.status as keyof typeof t]}
                    </span>
                    <div className="flex items-center gap-2">
                      <Button size="sm" variant="outline" className="glass-button p-2">
                        <Eye className="h-4 w-4" />
                      </Button>
                      {request.status === 'pending' && (
                        <Button size="sm" variant="outline" className="glass-button p-2">
                          <Edit className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="text-white/60">{t.startDate}: </span>
                    <span className="text-white">{request.startDate}</span>
                  </div>
                  <div>
                    <span className="text-white/60">{t.endDate}: </span>
                    <span className="text-white">{request.endDate}</span>
                  </div>
                  <div>
                    <span className="text-white/60">{t.duration}: </span>
                    <span className="text-white">{request.duration} {t.days}</span>
                  </div>
                  <div>
                    <span className="text-white/60">{t.requestDate}: </span>
                    <span className="text-white">{request.requestDate}</span>
                  </div>
                </div>

                {request.approvedBy && (
                  <div className="mt-3 pt-3 border-t border-white/10">
                    <span className="text-white/60 text-sm">{t.approvedBy}: </span>
                    <span className="text-white text-sm">
                      {language === 'ar' ? request.approvedBy : request.approvedByEn}
                    </span>
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
