import { useState, useEffect } from 'react'
import { useSelector } from 'react-redux'
import type { RootState } from '../../store'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Briefcase,
  GraduationCap,
  Edit,
  Save,
  X,
  Camera,
  Download,
  Upload,
  FileText,
  Award,
  Clock,
  DollarSign
} from 'lucide-react'

interface EmployeeProfileProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    title: 'الملف الشخصي - الموظف',
    subtitle: 'إدارة المعلومات الشخصية والمهنية',
    personalInfo: 'المعلومات الشخصية',
    contactInfo: 'معلومات الاتصال',
    jobInfo: 'المعلومات الوظيفية',
    documents: 'المستندات',
    skills: 'المهارات',
    edit: 'تعديل',
    save: 'حفظ',
    cancel: 'إلغاء',
    upload: 'رفع',
    download: 'تحميل',
    fullName: 'الاسم الكامل',
    email: 'البريد الإلكتروني',
    phone: 'رقم الهاتف',
    address: 'العنوان',
    birthDate: 'تاريخ الميلاد',
    nationalId: 'رقم الهوية',
    position: 'المنصب',
    department: 'القسم',
    manager: 'المدير المباشر',
    joinDate: 'تاريخ الانضمام',
    salary: 'الراتب',
    employeeId: 'رقم الموظف',
    emergencyContact: 'جهة الاتصال الطارئة',
    education: 'التعليم',
    experience: 'الخبرة',
    certifications: 'الشهادات',
    profilePicture: 'الصورة الشخصية',
    changePhoto: 'تغيير الصورة',
    personalDocuments: 'المستندات الشخصية',
    workDocuments: 'مستندات العمل',
    contract: 'العقد',
    resume: 'السيرة الذاتية',
    idCopy: 'صورة الهوية',
    certificates: 'الشهادات',
    updateSuccess: 'تم تحديث المعلومات بنجاح',
    updateProfile: 'تحديث الملف الشخصي',
    viewOnly: 'للعرض فقط',
    editable: 'قابل للتعديل'
  },
  en: {
    title: 'Employee Profile',
    subtitle: 'Manage personal and professional information',
    personalInfo: 'Personal Information',
    contactInfo: 'Contact Information',
    jobInfo: 'Job Information',
    documents: 'Documents',
    skills: 'Skills',
    edit: 'Edit',
    save: 'Save',
    cancel: 'Cancel',
    upload: 'Upload',
    download: 'Download',
    fullName: 'Full Name',
    email: 'Email',
    phone: 'Phone',
    address: 'Address',
    birthDate: 'Birth Date',
    nationalId: 'National ID',
    position: 'Position',
    department: 'Department',
    manager: 'Direct Manager',
    joinDate: 'Join Date',
    salary: 'Salary',
    employeeId: 'Employee ID',
    emergencyContact: 'Emergency Contact',
    education: 'Education',
    experience: 'Experience',
    certifications: 'Certifications',
    profilePicture: 'Profile Picture',
    changePhoto: 'Change Photo',
    personalDocuments: 'Personal Documents',
    workDocuments: 'Work Documents',
    contract: 'Contract',
    resume: 'Resume',
    idCopy: 'ID Copy',
    certificates: 'Certificates',
    updateSuccess: 'Profile updated successfully',
    updateProfile: 'Update Profile',
    viewOnly: 'View Only',
    editable: 'Editable'
  }
}

export default function EmployeeProfile({ language }: EmployeeProfileProps) {
  const { user } = useSelector((state: RootState) => state.auth)
  const [isEditing, setIsEditing] = useState(false)
  const [profileData, setProfileData] = useState({
    fullName: language === 'ar' ? (user?.nameAr || '') : (user?.name || ''),
    email: user?.email || '',
    phone: user?.phone || '',
    address: language === 'ar' ? (user?.address || '') : (user?.addressEn || ''),
    birthDate: user?.birthDate || '',
    nationalId: user?.nationalId || '',
    position: language === 'ar' ? (user?.position || '') : (user?.positionEn || ''),
    department: language === 'ar' ? (user?.departmentAr || '') : (user?.department || ''),
    manager: language === 'ar' ? (user?.manager || '') : (user?.managerEn || ''),
    joinDate: user?.joinDate || '',
    salary: user?.salary || 0,
    employeeId: user?.employeeId || '',
    emergencyContact: language === 'ar' ? (user?.emergencyContact || '') : (user?.emergencyContactEn || ''),
    education: language === 'ar' ? (user?.education || '') : (user?.educationEn || ''),
    experience: language === 'ar' ? (user?.experience || '') : (user?.experienceEn || '')
  })

  // Update profile data when user or language changes
  useEffect(() => {
    if (user) {
      setProfileData({
        fullName: language === 'ar' ? (user.nameAr || '') : (user.name || ''),
        email: user.email || '',
        phone: user.phone || '',
        address: language === 'ar' ? (user.address || '') : (user.addressEn || ''),
        birthDate: user.birthDate || '',
        nationalId: user.nationalId || '',
        position: language === 'ar' ? (user.position || '') : (user.positionEn || ''),
        department: language === 'ar' ? (user.departmentAr || '') : (user.department || ''),
        manager: language === 'ar' ? (user.manager || '') : (user.managerEn || ''),
        joinDate: user.joinDate || '',
        salary: user.salary || 0,
        employeeId: user.employeeId || '',
        emergencyContact: language === 'ar' ? (user.emergencyContact || '') : (user.emergencyContactEn || ''),
        education: language === 'ar' ? (user.education || '') : (user.educationEn || ''),
        experience: language === 'ar' ? (user.experience || '') : (user.experienceEn || '')
      })
    }
  }, [user, language])

  const [documents] = useState([
    { id: 1, name: 'العقد الوظيفي', nameEn: 'Employment Contract', type: 'contract', size: '2.4 MB', date: '2023-01-15' },
    { id: 2, name: 'السيرة الذاتية', nameEn: 'Resume', type: 'resume', size: '1.2 MB', date: '2023-01-10' },
    { id: 3, name: 'صورة الهوية', nameEn: 'ID Copy', type: 'id', size: '0.8 MB', date: '2023-01-12' },
    { id: 4, name: 'شهادة البكالوريوس', nameEn: 'Bachelor Certificate', type: 'certificate', size: '1.5 MB', date: '2023-01-08' }
  ])

  const [skills] = useState([
    { name: 'React.js', level: 90 },
    { name: 'TypeScript', level: 85 },
    { name: 'Node.js', level: 80 },
    { name: 'Python', level: 75 },
    { name: 'SQL', level: 85 },
    { name: 'Git', level: 90 }
  ])

  const t = translations[language]
  const isRTL = language === 'ar'

  const handleSave = () => {
    // Save logic here
    setIsEditing(false)
    // Show success message
  }

  const handleCancel = () => {
    setIsEditing(false)
    // Reset form data
  }

  const getDocumentIcon = (type: string) => {
    switch (type) {
      case 'contract':
        return <FileText className="h-5 w-5 text-blue-400" />
      case 'resume':
        return <User className="h-5 w-5 text-green-400" />
      case 'id':
        return <Award className="h-5 w-5 text-purple-400" />
      case 'certificate':
        return <GraduationCap className="h-5 w-5 text-orange-400" />
      default:
        return <FileText className="h-5 w-5 text-gray-400" />
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">{t.title}</h1>
          <p className="text-white/70">{t.subtitle}</p>
        </div>

        <div className="flex items-center gap-3">
          {!isEditing ? (
            <Button onClick={() => setIsEditing(true)} className="glass-button">
              <Edit className="h-4 w-4 mr-2" />
              {t.edit}
            </Button>
          ) : (
            <div className="flex gap-2">
              <Button onClick={handleSave} className="glass-button">
                <Save className="h-4 w-4 mr-2" />
                {t.save}
              </Button>
              <Button onClick={handleCancel} variant="outline" className="glass-button">
                <X className="h-4 w-4 mr-2" />
                {t.cancel}
              </Button>
            </div>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Profile Picture & Basic Info */}
        <div className="lg:col-span-1">
          <Card className="glass-card border-white/20">
            <CardContent className="p-6 text-center">
              <div className="relative inline-block mb-4">
                <div className="w-32 h-32 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-white text-4xl font-bold mx-auto">
                  {profileData.fullName.charAt(0)}
                </div>
                {isEditing && (
                  <button className="absolute bottom-0 right-0 p-2 bg-blue-500 rounded-full text-white hover:bg-blue-600 transition-colors">
                    <Camera className="h-4 w-4" />
                  </button>
                )}
              </div>
              <h3 className="text-xl font-bold text-white mb-1">{profileData.fullName}</h3>
              <p className="text-white/70 mb-2">{profileData.position}</p>
              <p className="text-white/60 text-sm">{profileData.department}</p>

              <div className="mt-6 space-y-3">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-white/70">{t.employeeId}:</span>
                  <span className="text-white">{profileData.employeeId}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-white/70">{t.joinDate}:</span>
                  <span className="text-white">{profileData.joinDate}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Personal Information */}
        <div className="lg:col-span-2 space-y-6">
          {/* Personal Info */}
          <Card className="glass-card border-white/20">
            <CardHeader>
              <CardTitle className="text-white text-xl flex items-center gap-2">
                <User className="h-5 w-5" />
                {t.personalInfo}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-white/70 text-sm mb-2">{t.fullName}</label>
                  {isEditing ? (
                    <Input
                      value={profileData.fullName}
                      onChange={(e) => setProfileData({...profileData, fullName: e.target.value})}
                      className="glass-input"
                    />
                  ) : (
                    <p className="text-white p-3 glass-card border-white/10">{profileData.fullName}</p>
                  )}
                </div>
                <div>
                  <label className="block text-white/70 text-sm mb-2">{t.nationalId}</label>
                  <p className="text-white p-3 glass-card border-white/10">{profileData.nationalId}</p>
                  <span className="text-xs text-white/50">{t.viewOnly}</span>
                </div>
                <div>
                  <label className="block text-white/70 text-sm mb-2">{t.birthDate}</label>
                  <p className="text-white p-3 glass-card border-white/10">{profileData.birthDate}</p>
                  <span className="text-xs text-white/50">{t.viewOnly}</span>
                </div>
                <div>
                  <label className="block text-white/70 text-sm mb-2">{t.emergencyContact}</label>
                  {isEditing ? (
                    <Input
                      value={profileData.emergencyContact}
                      onChange={(e) => setProfileData({...profileData, emergencyContact: e.target.value})}
                      className="glass-input"
                    />
                  ) : (
                    <p className="text-white p-3 glass-card border-white/10">{profileData.emergencyContact}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Contact Info */}
          <Card className="glass-card border-white/20">
            <CardHeader>
              <CardTitle className="text-white text-xl flex items-center gap-2">
                <Mail className="h-5 w-5" />
                {t.contactInfo}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-white/70 text-sm mb-2">{t.email}</label>
                  {isEditing ? (
                    <Input
                      value={profileData.email}
                      onChange={(e) => setProfileData({...profileData, email: e.target.value})}
                      className="glass-input"
                    />
                  ) : (
                    <p className="text-white p-3 glass-card border-white/10">{profileData.email}</p>
                  )}
                </div>
                <div>
                  <label className="block text-white/70 text-sm mb-2">{t.phone}</label>
                  {isEditing ? (
                    <Input
                      value={profileData.phone}
                      onChange={(e) => setProfileData({...profileData, phone: e.target.value})}
                      className="glass-input"
                    />
                  ) : (
                    <p className="text-white p-3 glass-card border-white/10">{profileData.phone}</p>
                  )}
                </div>
                <div className="md:col-span-2">
                  <label className="block text-white/70 text-sm mb-2">{t.address}</label>
                  {isEditing ? (
                    <Input
                      value={profileData.address}
                      onChange={(e) => setProfileData({...profileData, address: e.target.value})}
                      className="glass-input"
                    />
                  ) : (
                    <p className="text-white p-3 glass-card border-white/10">{profileData.address}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Skills & Documents */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Skills */}
        <Card className="glass-card border-white/20">
          <CardHeader>
            <CardTitle className="text-white text-xl flex items-center gap-2">
              <Award className="h-5 w-5" />
              {t.skills}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {skills.map((skill, index) => (
                <div key={index}>
                  <div className="flex justify-between mb-2">
                    <span className="text-white text-sm font-medium">{skill.name}</span>
                    <span className="text-white/70 text-sm">{skill.level}%</span>
                  </div>
                  <div className="w-full bg-white/20 rounded-full h-2">
                    <div
                      className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-1000"
                      style={{ width: `${skill.level}%` }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Documents */}
        <Card className="glass-card border-white/20">
          <CardHeader>
            <CardTitle className="text-white text-xl flex items-center gap-2">
              <FileText className="h-5 w-5" />
              {t.documents}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {documents.map((doc) => (
                <div key={doc.id} className="flex items-center justify-between p-3 glass-card border-white/10 hover:border-white/30 transition-all">
                  <div className="flex items-center gap-3">
                    {getDocumentIcon(doc.type)}
                    <div>
                      <p className="text-white text-sm font-medium">
                        {language === 'ar' ? doc.name : doc.nameEn}
                      </p>
                      <p className="text-white/60 text-xs">{doc.size} • {doc.date}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button size="sm" variant="outline" className="glass-button p-2">
                      <Download className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}

              <Button className="w-full glass-button mt-4">
                <Upload className="h-4 w-4 mr-2" />
                {t.upload} {t.documents}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
