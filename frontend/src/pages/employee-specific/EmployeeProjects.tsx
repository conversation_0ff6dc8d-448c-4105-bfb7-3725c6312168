/**
 * Employee Projects Page - READ-ONLY Implementation
 * Restricted version for employees - can only view assigned projects
 * No create, edit, delete, or export capabilities
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Progress } from '@/components/ui/progress'
import { Textarea } from '@/components/ui/textarea'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  FolderOpen,
  Users,
  Calendar,
  Target,
  Clock,
  CheckCircle,
  AlertTriangle,
  Eye,
  User,
  RefreshCw,
  Edit,
  Save,
  Plus,
  MessageSquare,
  FileText,
  BarChart3,
  Activity
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { projectService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface EmployeeProjectsProps {
  language: 'ar' | 'en'
}

// FIXED: Interface now matches backend ProjectSerializer exactly
interface Project {
  id: number
  name: string
  name_ar: string
  description: string
  description_ar: string
  project_manager: number
  project_manager_name: string
  department: number
  department_name: string
  team_members: number[]
  team_members_count: number
  client: string
  budget_amount: number
  start_date: string
  end_date: string
  actual_start_date?: string
  actual_end_date?: string
  status: 'PLANNING' | 'IN_PROGRESS' | 'ON_HOLD' | 'COMPLETED' | 'CANCELLED'
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  progress_percentage: number
  tasks_count: number
  is_active: boolean
  created_at: string
  updated_at: string
}

// Translations
const translations = {
  ar: {
    employeeProjects: 'المشاريع المكلف بها',
    searchPlaceholder: 'البحث في المشاريع...',
    view: 'عرض',
    name: 'اسم المشروع',
    status: 'الحالة',
    priority: 'الأولوية',
    progress: 'التقدم',
    startDate: 'تاريخ البداية',
    endDate: 'تاريخ النهاية',
    manager: 'مدير المشروع',
    teamSize: 'حجم الفريق',
    department: 'القسم',
    planning: 'التخطيط',
    in_progress: 'قيد التنفيذ',
    on_hold: 'معلق',
    completed: 'مكتمل',
    cancelled: 'ملغي',
    low: 'منخفضة',
    medium: 'متوسطة',
    high: 'عالية',
    urgent: 'عاجلة',
    all: 'الكل',
    filterByStatus: 'تصفية حسب الحالة',
    filterByPriority: 'تصفية حسب الأولوية',
    filterByDepartment: 'تصفية حسب القسم'
  },
  en: {
    employeeProjects: 'Assigned Projects',
    searchPlaceholder: 'Search projects...',
    view: 'View',
    name: 'Project Name',
    status: 'Status',
    priority: 'Priority',
    progress: 'Progress',
    startDate: 'Start Date',
    endDate: 'End Date',
    manager: 'Project Manager',
    teamSize: 'Team Size',
    department: 'Department',
    planning: 'Planning',
    in_progress: 'In Progress',
    on_hold: 'On Hold',
    completed: 'Completed',
    cancelled: 'Cancelled',
    low: 'Low',
    medium: 'Medium',
    high: 'High',
    urgent: 'Urgent',
    all: 'All',
    filterByStatus: 'Filter by Status',
    filterByPriority: 'Filter by Priority',
    filterByDepartment: 'Filter by Department'
  }
}

// @ts-ignore
export default function EmployeeProjects({ language }: EmployeeProjectsProps as any): (React as any).ReactElement {
  const [showModal, setShowModal] = useState(false as any)

  // Enhanced state for project management features
  const [selectedProject, setSelectedProject] = useState<Project | null>(null)
  const [showProjectDetails, setShowProjectDetails] = useState(false as any)
  const [showProgressUpdate, setShowProgressUpdate] = useState(false as any)
  const [newProgress, setNewProgress] = useState(0 as any)
  const [progressNote, setProgressNote] = useState('' as any)
  const [isUpdatingProgress, setIsUpdatingProgress] = useState(false as any)

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook - READ-ONLY for employees
  const {
    items: projects,
    selectedItem,
    loading,
    error,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    clearError
  } = useCrud<Project>({
    service: projectService,
    autoLoad: true,
    pageSize: 20
  })

  // Enhanced project management functions
  const handleViewProject = (project: Project): void => {
    setSelectedProject(project as any)
    setShowProjectDetails(true as any)
  }

  const handleUpdateProgress = (project: Project): void => {
    setSelectedProject(project as any)
    setNewProgress((project as any as any).progress_percentage)
    setProgressNote('' as any)
    setShowProgressUpdate(true as any)
  }

  const handleSaveProgress = async () => {
    if (!selectedProject) return

    setIsUpdatingProgress(true as any)
    try {
      // Call the backend API to update project progress
      const response = await fetch(`http://localhost:8000/api/projects/${(selectedProject as any as any).id}/update-progress/`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Include cookies for authentication
        body: (JSON as any).stringify({
          progress_percentage: newProgress,
          progress_note: progressNote
        } as any)
      })

      if (!(response as any).ok) {
        // @ts-ignore
        const errorData = await (response as any).json( as any)
        throw new Error((errorData as any as any).error || 'Failed to update progress')
      }

      // @ts-ignore
      const result = await (response as any).json( as any)
      (console as any).log('Progress updated successfully:', result as any)

      // Close the modal and refresh the data
      setShowProgressUpdate(false as any)
      // @ts-ignore
      refresh( as any)

      // Show success message (you can add a toast notification here)
      (console as any).log('✅ Progress updated successfully!' as any)

    } catch (error) {
      (console as any).error('❌ Error updating progress:', error as any)
      // You can add error handling UI here (toast notification, etc.)
      alert(`Error updating progress: ${(error as any as any).message}`)
    } finally {
      setIsUpdatingProgress(false as any)
    }
  }

  // Table columns configuration
  const columns: TableColumn<Project>[] = [
    {
      key: 'name',
      label: (t as any).name,
      sortable: true,
      render: (item: Project) => (
        <div
          className="group flex items-start gap-3 cursor-pointer hover:bg-blue-50 dark:hover:bg-blue-900/20 p-3 rounded-lg transition-all duration-200 border border-transparent hover:border-blue-200 dark:hover:border-blue-800"
          onClick={() => handleViewProject(item as any)}
          title={language === 'ar' ? 'انقر لعرض تفاصيل المشروع' : 'Click to view project details'}
        >
          <div className="flex-shrink-0 mt-1">
            <FolderOpen className="h-5 w-5 text-blue-500 group-hover:text-blue-600 transition-colors" />
          </div>
          <div className="flex-1 min-w-0">
            <div className="font-semibold text-gray-900 dark:text-gray-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors leading-tight">
              {language === 'ar' ? (item as any).name_ar : (item as any).name}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400 mt-1 line-clamp-2">
              {language === 'ar' ? (item as any).description_ar : (item as any).description}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'status',
      label: (t as any).status,
      sortable: true,
      render: (item: Project) => {
        const statusConfig = {
          PLANNING: {
            className: 'bg-amber-100 text-amber-800 border-amber-200 dark:bg-amber-900/20 dark:text-amber-400 dark:border-amber-800',
            icon: '📋'
          },
          IN_PROGRESS: {
            className: 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800',
            icon: '🚀'
          },
          ON_HOLD: {
            className: 'bg-orange-100 text-orange-800 border-orange-200 dark:bg-orange-900/20 dark:text-orange-400 dark:border-orange-800',
            icon: '⏸️'
          },
          COMPLETED: {
            className: 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800',
            icon: '✅'
          },
          CANCELLED: {
            className: 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800',
            icon: '❌'
          }
        }
        const statusLabels = {
          PLANNING: language === 'ar' ? 'تخطيط' : 'Planning',
          IN_PROGRESS: language === 'ar' ? 'قيد التنفيذ' : 'In Progress',
          ON_HOLD: language === 'ar' ? 'معلق' : 'On Hold',
          COMPLETED: language === 'ar' ? 'مكتمل' : 'Completed',
          CANCELLED: language === 'ar' ? 'ملغي' : 'Cancelled'
        }
        const config = statusConfig[(item as any).status]
        return (
          <Badge className={`${(config as any).className} border font-medium px-3 py-1 text-xs`}>
            <span className="mr-1">{(config as any).icon}</span>
            {statusLabels[(item as any).status]}
          </Badge>
        )
      }
    },
    {
      key: 'priority',
      label: (t as any).priority,
      sortable: true,
      render: (item: Project) => {
        const priorityConfig = {
          LOW: {
            className: 'bg-gray-100 text-gray-700 border-gray-200 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-700',
            icon: '⬇️'
          },
          MEDIUM: {
            className: 'bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800',
            icon: '➡️'
          },
          HIGH: {
            className: 'bg-orange-100 text-orange-800 border-orange-200 dark:bg-orange-900/20 dark:text-orange-400 dark:border-orange-800',
            icon: '⬆️'
          },
          CRITICAL: {
            className: 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800',
            icon: '🔥'
          }
        }
        const priorityLabels = {
          LOW: language === 'ar' ? 'منخفض' : 'Low',
          MEDIUM: language === 'ar' ? 'متوسط' : 'Medium',
          HIGH: language === 'ar' ? 'عالي' : 'High',
          CRITICAL: language === 'ar' ? 'حرج' : 'Critical'
        }
        const config = priorityConfig[(item as any).priority]
        return (
          <Badge className={`${(config as any).className} border font-medium px-3 py-1 text-xs`}>
            <span className="mr-1">{(config as any).icon}</span>
            {priorityLabels[(item as any).priority]}
          </Badge>
        )
      }
    },
    {
      key: 'progress_percentage',
      label: language === 'ar' ? 'التقدم' : 'Progress',
      sortable: true,
      render: (item: Project) => (
        <div
          className="group flex items-center gap-3 cursor-pointer hover:bg-blue-50 dark:hover:bg-blue-900/20 p-3 rounded-lg transition-all duration-200 border border-transparent hover:border-blue-200 dark:hover:border-blue-800"
          onClick={() => handleUpdateProgress(item as any)}
          title={language === 'ar' ? 'انقر لتحديث التقدم' : 'Click to update progress'}
        >
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between mb-1">
              <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
                {language === 'ar' ? 'التقدم' : 'Progress'}
              </span>
              <span className="text-sm font-semibold text-gray-900 dark:text-gray-100">
                {(item as any).progress_percentage}%
              </span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 overflow-hidden">
              <div
                className="h-full rounded-full transition-all duration-500 ease-out bg-gradient-to-r from-blue-500 to-blue-600 dark:from-blue-400 dark:to-blue-500"
                style={{ width: `${(item as any).progress_percentage}%` }}
              />
            </div>
          </div>
          <Edit className="h-4 w-4 text-gray-400 group-hover:text-blue-500 opacity-0 group-hover:opacity-100 transition-all duration-200 flex-shrink-0" />
        </div>
      )
    },
    {
      key: 'project_manager_name',
      label: language === 'ar' ? 'مدير المشروع' : 'Manager',
      render: (item: Project) => (
        <div className="flex items-center gap-2">
          <div className="flex-shrink-0 w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
            <User className="h-4 w-4 text-blue-600 dark:text-blue-400" />
          </div>
          <div className="flex-1 min-w-0">
            <div className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
              {(item as any).project_manager_name}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              {language === 'ar' ? 'مدير المشروع' : 'Project Manager'}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'team_members_count',
      label: language === 'ar' ? 'حجم الفريق' : 'Team Size',
      render: (item: Project) => (
        <div className="flex items-center gap-2">
          <div className="flex-shrink-0 w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
            <Users className="h-4 w-4 text-green-600 dark:text-green-400" />
          </div>
          <div className="flex-1">
            <div className="text-lg font-bold text-gray-900 dark:text-gray-100">
              {(item as any).team_members_count}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              {language === 'ar' ? 'أعضاء' : 'members'}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'start_date',
      label: language === 'ar' ? 'تاريخ البداية' : 'Start Date',
      sortable: true,
      render: (item: Project) => (
        <div className="flex items-center gap-2">
          <div className="flex-shrink-0 w-8 h-8 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center">
            <Calendar className="h-4 w-4 text-purple-600 dark:text-purple-400" />
          </div>
          <div className="flex-1">
            <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
              {new Date((item as any as any).start_date).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US' as any)}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              {language === 'ar' ? 'البداية' : 'Start'}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'end_date',
      label: language === 'ar' ? 'تاريخ النهاية' : 'End Date',
      sortable: true,
      render: (item: Project) => (
        <div className="flex items-center gap-2">
          <div className="flex-shrink-0 w-8 h-8 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center">
            <Calendar className="h-4 w-4 text-red-600 dark:text-red-400" />
          </div>
          <div className="flex-1">
            <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
              {new Date((item as any as any).end_date).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US' as any)}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              {language === 'ar' ? 'النهاية' : 'End'}
            </div>
          </div>
        </div>
      )
    }
  ]

  // Table actions configuration - ENHANCED for employees
  const actions: TableAction<Project>[] = [
    {
      label: language === 'ar' ? 'عرض التفاصيل' : 'View Details',
      icon: Eye,
      onClick: (item: Project) => handleViewProject(item as any),
      variant: 'ghost'
    },
    {
      label: language === 'ar' ? 'تحديث التقدم' : 'Update Progress',
      icon: BarChart3,
      onClick: (item: Project) => handleUpdateProgress(item as any),
      variant: 'ghost'
    },
    {
      label: language === 'ar' ? 'إضافة تعليق' : 'Add Comment',
      icon: MessageSquare,
      onClick: (item: Project) => {
        // TODO: Implement comment functionality
        (console as any).log('Add comment for project:', (item as any as any).id)
      },
      variant: 'ghost'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'status',
      label: (t as any).filterByStatus,
      options: [
        { value: '', label: (t as any).all },
        { value: 'planning', label: (t as any).planning },
        { value: 'in_progress', label: (t as any).in_progress },
        { value: 'on_hold', label: (t as any).on_hold },
        { value: 'completed', label: (t as any).completed },
        { value: 'cancelled', label: (t as any).cancelled }
      ]
    },
    {
      key: 'priority',
      label: (t as any).filterByPriority,
      options: [
        { value: '', label: (t as any).all },
        { value: 'low', label: (t as any).low },
        { value: 'medium', label: (t as any).medium },
        { value: 'high', label: (t as any).high },
        { value: 'urgent', label: (t as any).urgent }
      ]
    }
  ]

  // Event handlers - RESTRICTED for employees
  const handleModalClose = (): void => {
    setShowModal(false as any)
    selectItem(null as any)
    // @ts-ignore
    clearError( as any)
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Page Header */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center gap-4">
          <div className="flex-shrink-0 w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-xl flex items-center justify-center">
            <FolderOpen className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div className="flex-1">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {language === 'ar' ? 'مشاريعي' : 'My Projects'}
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              {language === 'ar' ? 'إدارة وتتبع تقدم مشاريعك' : 'Manage and track your project progress'}
            </p>
          </div>
        </div>
      </div>

      {/* Projects Table */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">{/* READ-ONLY Table for Employee Projects */}
      <CrudTable
        title={(t as any).employeeProjects}
        data={projects}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={(t as any).searchPlaceholder}
        language={language}
        // REMOVED: onCreate - employees cannot create projects
        onRefresh={refresh}
        // REMOVED: onExport - employees cannot export data
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
        showCreateButton={false}
        showExportButton={false}
      />

      {/* VIEW-ONLY Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        // REMOVED: onSave - employees cannot save/edit projects
        title={(t as any).view}
        fields={[]} // No form fields needed for view-only
        initialData={selectedItem as any}
        language={language}
        loading={false}
        readOnly={true}
      />

      {/* Enhanced Project Details Modal */}
      <Dialog open={showProjectDetails} onOpenChange={setShowProjectDetails}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-xl font-bold">
              {selectedProject && (language === 'ar' ? (selectedProject as any).name_ar : (selectedProject as any).name)}
            </DialogTitle>
          </DialogHeader>

          {selectedProject && (
            <Tabs defaultValue="overview" className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="overview">{language === 'ar' ? 'نظرة عامة' : 'Overview'}</TabsTrigger>
                <TabsTrigger value="progress">{language === 'ar' ? 'التقدم' : 'Progress'}</TabsTrigger>
                <TabsTrigger value="team">{language === 'ar' ? 'الفريق' : 'Team'}</TabsTrigger>
                <TabsTrigger value="activity">{language === 'ar' ? 'النشاط' : 'Activity'}</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm">{language === 'ar' ? 'معلومات المشروع' : 'Project Info'}</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      <div>
                        <span className="text-sm text-gray-400">{language === 'ar' ? 'الوصف:' : 'Description:'}</span>
                        <p className="text-sm">{language === 'ar' ? (selectedProject as any).description_ar : (selectedProject as any).description}</p>
                      </div>
                      <div>
                        <span className="text-sm text-gray-400">{language === 'ar' ? 'العميل:' : 'Client:'}</span>
                        <p className="text-sm">{(selectedProject as any).client}</p>
                      </div>
                      <div>
                        <span className="text-sm text-gray-400">{language === 'ar' ? 'الميزانية:' : 'Budget:'}</span>
                        // @ts-ignore
                        <p className="text-sm">${(selectedProject as any).budget_amount?.toLocaleString( as any)}</p>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm">{language === 'ar' ? 'الجدول الزمني' : 'Timeline'}</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      <div>
                        <span className="text-sm text-gray-400">{language === 'ar' ? 'تاريخ البداية:' : 'Start Date:'}</span>
                        <p className="text-sm">{new Date((selectedProject as any as any).start_date).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US' as any)}</p>
                      </div>
                      <div>
                        <span className="text-sm text-gray-400">{language === 'ar' ? 'تاريخ النهاية:' : 'End Date:'}</span>
                        <p className="text-sm">{new Date((selectedProject as any as any).end_date).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US' as any)}</p>
                      </div>
                      <div>
                        <span className="text-sm text-gray-400">{language === 'ar' ? 'الحالة:' : 'Status:'}</span>
                        <Badge className="ml-2">
                          {(selectedProject as any).status === 'IN_PROGRESS' ? (language === 'ar' ? 'قيد التنفيذ' : 'In Progress') : (selectedProject as any).status}
                        </Badge>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="progress" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">{language === 'ar' ? 'تقدم المشروع' : 'Project Progress'}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <div className="flex justify-between items-center mb-2">
                          <span className="text-sm text-gray-400">{language === 'ar' ? 'التقدم الإجمالي' : 'Overall Progress'}</span>
                          <span className="text-sm font-medium">{(selectedProject as any).progress_percentage}%</span>
                        </div>
                        <Progress value={(selectedProject as any).progress_percentage} className="w-full" />
                      </div>
                      <Button
                        onClick={() => handleUpdateProgress(selectedProject as any)}
                        className="w-full"
                      >
                        <Edit className="h-4 w-4 mr-2" />
                        {language === 'ar' ? 'تحديث التقدم' : 'Update Progress'}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="team" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">{language === 'ar' ? 'فريق المشروع' : 'Project Team'}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div>
                        <span className="text-sm text-gray-400">{language === 'ar' ? 'مدير المشروع:' : 'Project Manager:'}</span>
                        <p className="text-sm">{(selectedProject as any).project_manager_name}</p>
                      </div>
                      <div>
                        <span className="text-sm text-gray-400">{language === 'ar' ? 'حجم الفريق:' : 'Team Size:'}</span>
                        <p className="text-sm">{(selectedProject as any).team_members_count} {language === 'ar' ? 'عضو' : 'members'}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="activity" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">{language === 'ar' ? 'نشاط المشروع' : 'Project Activity'}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center py-8 text-gray-400">
                      <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>{language === 'ar' ? 'لا توجد أنشطة حديثة' : 'No recent activity'}</p>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          )}
        </DialogContent>
      </Dialog>

      {/* Progress Update Modal */}
      <Dialog open={showProgressUpdate} onOpenChange={setShowProgressUpdate}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>{language === 'ar' ? 'تحديث تقدم المشروع' : 'Update Project Progress'}</DialogTitle>
          </DialogHeader>

          {selectedProject && (
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">
                  {language === 'ar' ? 'النسبة المئوية للتقدم' : 'Progress Percentage'}
                </label>
                <div className="mt-2">
                  <Input
                    type="number"
                    min="0"
                    max="100"
                    value={newProgress}
                    onChange={(e: any) => setNewProgress(Number((e as any as any).target.value))}
                    className="w-full"
                  />
                  <Progress value={newProgress} className="w-full mt-2" />
                </div>
              </div>

              <div>
                <label className="text-sm font-medium">
                  {language === 'ar' ? 'ملاحظات التقدم' : 'Progress Notes'}
                </label>
                <Textarea
                  value={progressNote}
                  onChange={(e: any) => setProgressNote((e as any as any).target.value)}
                  placeholder={language === 'ar' ? 'أضف ملاحظات حول التقدم...' : 'Add notes about the progress...'}
                  className="mt-2"
                  rows={3}
                />
              </div>

              <div className="flex justify-end space-x-2">
                <Button
                  variant="outline"
                  onClick={() => setShowProgressUpdate(false as any)}
                  disabled={isUpdatingProgress}
                >
                  {language === 'ar' ? 'إلغاء' : 'Cancel'}
                </Button>
                <Button
                  onClick={handleSaveProgress}
                  disabled={isUpdatingProgress}
                >
                  {isUpdatingProgress ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      {language === 'ar' ? 'جاري الحفظ...' : 'Saving...'}
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      {language === 'ar' ? 'حفظ' : 'Save'}
                    </>
                  )}
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
      </div>
    </div>
  )
}
