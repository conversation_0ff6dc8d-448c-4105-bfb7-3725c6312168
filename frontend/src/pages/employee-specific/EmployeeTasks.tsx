import { useState, useEffect } from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  CheckSquare,
  Clock,
  Calendar,
  User,
  Flag,
  Plus,
  Search,
  Filter,
  Eye,
  Edit,
  CheckCircle,
  AlertTriangle,
  Target,
  TrendingUp,
  BarChart3
} from 'lucide-react'

interface EmployeeTasksProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    title: 'مهامي - الموظف',
    subtitle: 'إدارة المهام الشخصية والمشاريع',
    myTasks: 'مهامي',
    pendingTasks: 'المهام المعلقة',
    completedTasks: 'المهام المكتملة',
    overdueTasks: 'المهام المتأخرة',
    taskProgress: 'تقدم المهام',
    searchTasks: 'البحث في المهام...',
    addTask: 'إضافة مهمة',
    filterBy: 'تصفية حسب',
    priority: 'الأولوية',
    status: 'الحالة',
    project: 'المشروع',
    taskList: 'قائمة المهام',
    taskName: 'اسم المهمة',
    description: 'الوصف',
    dueDate: 'تاريخ الاستحقاق',
    assignedBy: 'مُعين من',
    actions: 'الإجراءات',
    viewTask: 'عرض المهمة',
    editTask: 'تعديل المهمة',
    markComplete: 'تحديد كمكتمل',
    pending: 'معلق',
    inProgress: 'قيد التنفيذ',
    completed: 'مكتمل',
    overdue: 'متأخر',
    high: 'عالي',
    medium: 'متوسط',
    low: 'منخفض',
    today: 'اليوم',
    thisWeek: 'هذا الأسبوع',
    nextWeek: 'الأسبوع القادم',
    refresh: 'تحديث',
    myProgress: 'تقدمي',
    completionRate: 'معدل الإنجاز',
    avgTaskTime: 'متوسط وقت المهمة',
    tasksThisWeek: 'مهام هذا الأسبوع'
  },
  en: {
    title: 'My Tasks - Employee',
    subtitle: 'Manage personal tasks and projects',
    myTasks: 'My Tasks',
    pendingTasks: 'Pending Tasks',
    completedTasks: 'Completed Tasks',
    overdueTasks: 'Overdue Tasks',
    taskProgress: 'Task Progress',
    searchTasks: 'Search tasks...',
    addTask: 'Add Task',
    filterBy: 'Filter By',
    priority: 'Priority',
    status: 'Status',
    project: 'Project',
    taskList: 'Task List',
    taskName: 'Task Name',
    description: 'Description',
    dueDate: 'Due Date',
    assignedBy: 'Assigned By',
    actions: 'Actions',
    viewTask: 'View Task',
    editTask: 'Edit Task',
    markComplete: 'Mark Complete',
    pending: 'Pending',
    inProgress: 'In Progress',
    completed: 'Completed',
    overdue: 'Overdue',
    high: 'High',
    medium: 'Medium',
    low: 'Low',
    today: 'Today',
    thisWeek: 'This Week',
    nextWeek: 'Next Week',
    refresh: 'Refresh',
    myProgress: 'My Progress',
    completionRate: 'Completion Rate',
    avgTaskTime: 'Avg Task Time',
    tasksThisWeek: 'Tasks This Week'
  }
}

export default function EmployeeTasks({ language }: EmployeeTasksProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedPriority, setSelectedPriority] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('')
  const [refreshing, setRefreshing] = useState(false)
  const t = translations[language]
  const isRTL = language === 'ar'

  // Employee-specific task metrics
  const [taskMetrics, setTaskMetrics] = useState({
    pendingTasks: 8,
    completedTasks: 24,
    overdueTasks: 2,
    completionRate: 85.7,
    avgTaskTime: 3.2,
    tasksThisWeek: 12
  })

  // Sample task data for employee
  const [tasks] = useState([
    {
      id: 1,
      name: 'تطوير واجهة المستخدم الجديدة',
      nameEn: 'Develop New User Interface',
      description: 'تصميم وتطوير واجهة المستخدم للصفحة الرئيسية',
      descriptionEn: 'Design and develop user interface for homepage',
      dueDate: '2024-02-05',
      priority: 'high',
      status: 'inProgress',
      project: 'تطوير الموقع',
      projectEn: 'Website Development',
      assignedBy: 'أحمد محمد',
      assignedByEn: 'Ahmed Mohammed',
      progress: 65,
      estimatedHours: 16,
      spentHours: 10
    },
    {
      id: 2,
      name: 'مراجعة كود التطبيق',
      nameEn: 'Code Review',
      description: 'مراجعة وتحسين كود التطبيق الجديد',
      descriptionEn: 'Review and improve new application code',
      dueDate: '2024-02-03',
      priority: 'medium',
      status: 'pending',
      project: 'تطوير التطبيق',
      projectEn: 'App Development',
      assignedBy: 'فاطمة علي',
      assignedByEn: 'Fatima Ali',
      progress: 0,
      estimatedHours: 8,
      spentHours: 0
    },
    {
      id: 3,
      name: 'إعداد قاعدة البيانات',
      nameEn: 'Database Setup',
      description: 'إعداد وتكوين قاعدة البيانات الجديدة',
      descriptionEn: 'Setup and configure new database',
      dueDate: '2024-01-30',
      priority: 'high',
      status: 'overdue',
      project: 'البنية التحتية',
      projectEn: 'Infrastructure',
      assignedBy: 'عمر سالم',
      assignedByEn: 'Omar Salem',
      progress: 30,
      estimatedHours: 12,
      spentHours: 8
    },
    {
      id: 4,
      name: 'كتابة الوثائق التقنية',
      nameEn: 'Technical Documentation',
      description: 'كتابة الوثائق التقنية للمشروع',
      descriptionEn: 'Write technical documentation for project',
      dueDate: '2024-02-10',
      priority: 'low',
      status: 'pending',
      project: 'التوثيق',
      projectEn: 'Documentation',
      assignedBy: 'نورا أحمد',
      assignedByEn: 'Nora Ahmed',
      progress: 0,
      estimatedHours: 6,
      spentHours: 0
    },
    {
      id: 5,
      name: 'اختبار الوحدة',
      nameEn: 'Unit Testing',
      description: 'كتابة وتنفيذ اختبارات الوحدة',
      descriptionEn: 'Write and execute unit tests',
      dueDate: '2024-01-28',
      priority: 'medium',
      status: 'completed',
      project: 'ضمان الجودة',
      projectEn: 'Quality Assurance',
      assignedBy: 'سعد محمد',
      assignedByEn: 'Saad Mohammed',
      progress: 100,
      estimatedHours: 10,
      spentHours: 9
    }
  ])

  const handleRefresh = async () => {
    setRefreshing(true)
    await new Promise(resolve => setTimeout(resolve, 1000))
    setRefreshing(false)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-500/20 text-green-400 border-green-500/30'
      case 'inProgress':
        return 'bg-blue-500/20 text-blue-400 border-blue-500/30'
      case 'pending':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
      case 'overdue':
        return 'bg-red-500/20 text-red-400 border-red-500/30'
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-500/20 text-red-400 border-red-500/30'
      case 'medium':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
      case 'low':
        return 'bg-green-500/20 text-green-400 border-green-500/30'
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30'
    }
  }

  const getProgressColor = (progress: number) => {
    if (progress >= 90) return 'bg-green-500'
    if (progress >= 70) return 'bg-blue-500'
    if (progress >= 50) return 'bg-yellow-500'
    return 'bg-red-500'
  }

  const filteredTasks = tasks.filter(task => {
    const matchesSearch = task.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         task.nameEn.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         task.project.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesPriority = !selectedPriority || task.priority === selectedPriority
    const matchesStatus = !selectedStatus || task.status === selectedStatus
    
    return matchesSearch && matchesPriority && matchesStatus
  })

  const taskMetricsCards = [
    {
      title: t.pendingTasks,
      value: taskMetrics.pendingTasks.toString(),
      change: '+2',
      trend: 'up',
      icon: Clock,
      color: 'from-yellow-500 to-yellow-600'
    },
    {
      title: t.completedTasks,
      value: taskMetrics.completedTasks.toString(),
      change: '+5',
      trend: 'up',
      icon: CheckCircle,
      color: 'from-green-500 to-green-600'
    },
    {
      title: t.overdueTasks,
      value: taskMetrics.overdueTasks.toString(),
      change: '-1',
      trend: 'down',
      icon: AlertTriangle,
      color: 'from-red-500 to-red-600'
    },
    {
      title: t.completionRate,
      value: `${taskMetrics.completionRate}%`,
      change: '+3.2%',
      trend: 'up',
      icon: Target,
      color: 'from-blue-500 to-blue-600'
    }
  ]

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">{t.title}</h1>
          <p className="text-white/70">{t.subtitle}</p>
        </div>
        
        <div className="flex items-center gap-3">
          <Button
            onClick={handleRefresh}
            disabled={refreshing}
            className="glass-button"
          >
            <Clock className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            {t.refresh}
          </Button>
          <Button className="glass-button">
            <Plus className="h-4 w-4 mr-2" />
            {t.addTask}
          </Button>
        </div>
      </div>

      {/* Task Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {taskMetricsCards.map((card, index) => (
          <Card key={index} className="glass-card border-white/20 hover:border-white/40 transition-all">
            <CardContent className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <p className="text-white/70 text-sm mb-1">{card.title}</p>
                  <p className="text-2xl font-bold text-white">{card.value}</p>
                </div>
                <div className={`p-3 rounded-xl bg-gradient-to-r ${card.color} shadow-lg`}>
                  <card.icon className="h-6 w-6 text-white" />
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <span className={`text-sm font-medium ${
                  card.trend === 'up' ? 'text-green-400' : 
                  card.trend === 'down' ? 'text-red-400' : 'text-blue-400'
                }`}>
                  {card.change}
                </span>
                <span className="text-xs text-white/60">vs last week</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Search and Filters */}
      <Card className="glass-card border-white/20">
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/60" />
              <Input
                placeholder={t.searchTasks}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 glass-input"
              />
            </div>
            
            <select
              value={selectedPriority}
              onChange={(e) => setSelectedPriority(e.target.value)}
              className="glass-input"
            >
              <option value="">{t.priority}</option>
              <option value="high">{t.high}</option>
              <option value="medium">{t.medium}</option>
              <option value="low">{t.low}</option>
            </select>
            
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="glass-input"
            >
              <option value="">{t.status}</option>
              <option value="pending">{t.pending}</option>
              <option value="inProgress">{t.inProgress}</option>
              <option value="completed">{t.completed}</option>
              <option value="overdue">{t.overdue}</option>
            </select>
            
            <Button variant="outline" className="glass-button">
              <Filter className="h-4 w-4 mr-2" />
              {t.filterBy}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Task List */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white text-xl">{t.taskList}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredTasks.map((task) => (
              <div key={task.id} className="p-6 glass-card border-white/10 hover:border-white/30 transition-all">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-white font-semibold text-lg">
                        {language === 'ar' ? task.name : task.nameEn}
                      </h3>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getPriorityColor(task.priority)}`}>
                        {t[task.priority as keyof typeof t]}
                      </span>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(task.status)}`}>
                        {t[task.status as keyof typeof t]}
                      </span>
                    </div>
                    <p className="text-white/70 mb-3">
                      {language === 'ar' ? task.description : task.descriptionEn}
                    </p>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-white/60">{t.project}: </span>
                        <span className="text-white">{language === 'ar' ? task.project : task.projectEn}</span>
                      </div>
                      <div>
                        <span className="text-white/60">{t.assignedBy}: </span>
                        <span className="text-white">{language === 'ar' ? task.assignedBy : task.assignedByEn}</span>
                      </div>
                      <div>
                        <span className="text-white/60">{t.dueDate}: </span>
                        <span className="text-white">{task.dueDate}</span>
                      </div>
                      <div>
                        <span className="text-white/60">الوقت: </span>
                        <span className="text-white">{task.spentHours}/{task.estimatedHours}h</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2 ml-4">
                    <Button size="sm" variant="outline" className="glass-button p-2">
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button size="sm" variant="outline" className="glass-button p-2">
                      <Edit className="h-4 w-4" />
                    </Button>
                    {task.status !== 'completed' && (
                      <Button size="sm" className="glass-button p-2">
                        <CheckSquare className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2">
                      <span className="text-white/60 text-sm">التقدم:</span>
                      <div className="w-32 bg-white/20 rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full ${getProgressColor(task.progress)}`}
                          style={{ width: `${task.progress}%` }}
                        ></div>
                      </div>
                      <span className="text-white text-sm font-medium">{task.progress}%</span>
                    </div>
                  </div>
                  
                  <div className="text-white/60 text-sm">
                    {task.status === 'overdue' && (
                      <span className="text-red-400 flex items-center gap-1">
                        <AlertTriangle className="h-4 w-4" />
                        متأخر
                      </span>
                    )}
                    {task.status === 'completed' && (
                      <span className="text-green-400 flex items-center gap-1">
                        <CheckCircle className="h-4 w-4" />
                        مكتمل
                      </span>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
