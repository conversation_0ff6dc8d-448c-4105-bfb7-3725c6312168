/**
 * Advanced Features Demo Page
 * Demonstrates search, export, and notification features
 */

import React, { useState, useEffect, memo } from 'react'
import {
  Search,
  Download,
  Bell,
  Users,
  FileText,
  BarChart3,
  Filter,
  Refresh<PERSON>w,
  <PERSON>ting<PERSON>,
  Zap
} from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import ExportDialog from '../../components/export/ExportDialog'
import useAdvancedFeatures from '../../hooks/useAdvancedFeatures'
import { ExportColumn } from '../../services/unifiedExport'

interface AdvancedFeaturesDemoProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    title: 'عرض الميزات المتقدمة',
    description: 'استكشف البحث المتقدم، التصدير، والإشعارات الفورية',
    searchFeatures: 'ميزات البحث',
    exportFeatures: 'ميزات التصدير',
    notificationFeatures: 'ميزات الإشعارات',
    globalSearch: 'البحث العام',
    searchDescription: 'ابحث في جميع أنحاء النظام مع اقتراحات فورية',
    trySearch: 'جرب البحث',
    dataExport: 'تصدير البيانات',
    exportDescription: 'صدر البيانات بتنسيقات متعددة مع خيارات متقدمة',
    exportSample: 'تصدير عينة',
    realTimeNotifications: 'الإشعارات الفورية',
    notificationDescription: 'تلقي إشعارات فورية مع إدارة متقدمة',
    testNotification: 'اختبار الإشعار',
    keyboardShortcuts: 'اختصارات لوحة المفاتيح',
    searchShortcut: 'Ctrl+K للبحث',
    exportShortcut: 'Ctrl+E للتصدير',
    notificationShortcut: 'Ctrl+N للإشعارات',
    escapeShortcut: 'Escape للإغلاق',
    features: 'الميزات',
    instantSearch: 'بحث فوري',
    smartSuggestions: 'اقتراحات ذكية',
    searchHistory: 'سجل البحث',
    multiFormat: 'تنسيقات متعددة',
    customColumns: 'أعمدة مخصصة',
    templates: 'قوالب',
    realTime: 'فوري',
    actionable: 'قابل للتنفيذ',
    persistent: 'دائم',
    connectionStatus: 'حالة الاتصال',
    connected: 'متصل',
    disconnected: 'منقطع',
    sampleData: 'بيانات تجريبية',
    employee: 'موظف',
    department: 'قسم',
    project: 'مشروع',
    document: 'مستند'
  },
  en: {
    title: 'Advanced Features Demo',
    description: 'Explore advanced search, export, and real-time notifications',
    searchFeatures: 'Search Features',
    exportFeatures: 'Export Features',
    notificationFeatures: 'Notification Features',
    globalSearch: 'Global Search',
    searchDescription: 'Search across the entire system with instant suggestions',
    trySearch: 'Try Search',
    dataExport: 'Data Export',
    exportDescription: 'Export data in multiple formats with advanced options',
    exportSample: 'Export Sample',
    realTimeNotifications: 'Real-time Notifications',
    notificationDescription: 'Receive instant notifications with advanced management',
    testNotification: 'Test Notification',
    keyboardShortcuts: 'Keyboard Shortcuts',
    searchShortcut: 'Ctrl+K for Search',
    exportShortcut: 'Ctrl+E for Export',
    notificationShortcut: 'Ctrl+N for Notifications',
    escapeShortcut: 'Escape to Close',
    features: 'Features',
    instantSearch: 'Instant Search',
    smartSuggestions: 'Smart Suggestions',
    searchHistory: 'Search History',
    multiFormat: 'Multi-format',
    customColumns: 'Custom Columns',
    templates: 'Templates',
    realTime: 'Real-time',
    actionable: 'Actionable',
    persistent: 'Persistent',
    connectionStatus: 'Connection Status',
    connected: 'Connected',
    disconnected: 'Disconnected',
    sampleData: 'Sample Data',
    employee: 'Employee',
    department: 'Department',
    project: 'Project',
    document: 'Document'
  }
}

// Mock sample data removed - Export demo should use real data from backend

const AdvancedFeaturesDemo: React.FC<AdvancedFeaturesDemoProps> = memo(({ language }) => {
  const [showExportDialog, setShowExportDialog] = useState(false)
  const t = translations[language]
  const isRTL = language === 'ar'

  // Advanced features hook
  const { search, export: exportFeature, notifications, utils } = useAdvancedFeatures({
    module: 'demo',
    enableSearch: true,
    enableExport: true,
    enableNotifications: true
  })

  // Test notification function
  const sendTestNotification = (): void => {
    // Simulate a notification
    const notification = {
      id: Date.now().toString(),
      type: 'info' as const,
      title: language === 'ar' ? 'إشعار تجريبي' : 'Test Notification',
      message: language === 'ar'
        ? 'هذا إشعار تجريبي لاختبار النظام'
        : 'This is a test notification to demonstrate the system',
      timestamp: Date.now(),
      actions: [
        {
          label: language === 'ar' ? 'عرض' : 'View',
          action: 'view'
        }
      ]
    }

    // In a real app, this would be sent via WebSocket
    console.log('Test notification:', notification)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-white mb-4 flex items-center justify-center gap-3">
            <Zap className="h-10 w-10 text-yellow-400" />
            {t.title}
          </h1>
          <p className="text-xl text-white/80 max-w-2xl mx-auto">
            {t.description}
          </p>
        </div>

        {/* Connection Status */}
        <div className="mb-8 flex justify-center">
          <div className={`flex items-center gap-2 px-4 py-2 rounded-full ${
            notifications.connectionStatus === 'connected'
              ? 'bg-green-500/20 text-green-400'
              : 'bg-red-500/20 text-red-400'
          }`}>
            <div className={`w-2 h-2 rounded-full ${
              notifications.connectionStatus === 'connected' ? 'bg-green-400' : 'bg-red-400'
            } animate-pulse`} />
            <span className="text-sm font-medium">
              {t.connectionStatus}: {notifications.connectionStatus === 'connected' ? t.connected : t.disconnected}
            </span>
          </div>
        </div>

        {/* Feature Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
          {/* Search Features */}
          <Card className="bg-white/10 backdrop-blur-xl border-white/20 text-white">
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <Search className="h-6 w-6 text-blue-400" />
                {t.searchFeatures}
              </CardTitle>
              <CardDescription className="text-white/70">
                {t.searchDescription}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <h4 className="font-medium text-white">{t.features}:</h4>
                <div className="flex flex-wrap gap-2">
                  <Badge variant="secondary" className="bg-blue-500/20 text-blue-400">
                    {t.instantSearch}
                  </Badge>
                  <Badge variant="secondary" className="bg-blue-500/20 text-blue-400">
                    {t.smartSuggestions}
                  </Badge>
                  <Badge variant="secondary" className="bg-blue-500/20 text-blue-400">
                    {t.searchHistory}
                  </Badge>
                </div>
              </div>
              <Button
                onClick={search.open}
                className="w-full bg-blue-500 hover:bg-blue-600"
              >
                <Search className="h-4 w-4 mr-2" />
                {t.trySearch}
              </Button>
            </CardContent>
          </Card>

          {/* Export Features */}
          <Card className="bg-white/10 backdrop-blur-xl border-white/20 text-white">
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <Download className="h-6 w-6 text-green-400" />
                {t.exportFeatures}
              </CardTitle>
              <CardDescription className="text-white/70">
                {t.exportDescription}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <h4 className="font-medium text-white">{t.features}:</h4>
                <div className="flex flex-wrap gap-2">
                  <Badge variant="secondary" className="bg-green-500/20 text-green-400">
                    {t.multiFormat}
                  </Badge>
                  <Badge variant="secondary" className="bg-green-500/20 text-green-400">
                    {t.customColumns}
                  </Badge>
                  <Badge variant="secondary" className="bg-green-500/20 text-green-400">
                    {t.templates}
                  </Badge>
                </div>
              </div>
              <Button
                onClick={() => setShowExportDialog(true)}
                className="w-full bg-green-500 hover:bg-green-600"
              >
                <Download className="h-4 w-4 mr-2" />
                {t.exportSample}
              </Button>
            </CardContent>
          </Card>

          {/* Notification Features */}
          <Card className="bg-white/10 backdrop-blur-xl border-white/20 text-white">
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <Bell className="h-6 w-6 text-purple-400" />
                {t.notificationFeatures}
              </CardTitle>
              <CardDescription className="text-white/70">
                {t.notificationDescription}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <h4 className="font-medium text-white">{t.features}:</h4>
                <div className="flex flex-wrap gap-2">
                  <Badge variant="secondary" className="bg-purple-500/20 text-purple-400">
                    {t.realTime}
                  </Badge>
                  <Badge variant="secondary" className="bg-purple-500/20 text-purple-400">
                    {t.actionable}
                  </Badge>
                  <Badge variant="secondary" className="bg-purple-500/20 text-purple-400">
                    {t.persistent}
                  </Badge>
                </div>
              </div>
              <div className="space-y-2">
                <Button
                  onClick={notifications.open}
                  className="w-full bg-purple-500 hover:bg-purple-600"
                >
                  <Bell className="h-4 w-4 mr-2" />
                  Open Notifications
                  {notifications.unreadCount > 0 && (
                    <Badge className="ml-2 bg-red-500">
                      {notifications.unreadCount}
                    </Badge>
                  )}
                </Button>
                <Button
                  onClick={sendTestNotification}
                  variant="outline"
                  className="w-full bg-white/5 border-white/20 text-white hover:bg-white/10"
                >
                  {t.testNotification}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Keyboard Shortcuts */}
        <Card className="bg-white/10 backdrop-blur-xl border-white/20 text-white">
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <Settings className="h-6 w-6 text-yellow-400" />
              {t.keyboardShortcuts}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="text-center p-3 rounded-lg bg-white/5">
                <kbd className="px-2 py-1 bg-white/10 rounded text-sm">Ctrl+K</kbd>
                <p className="text-sm mt-2 text-white/70">{t.searchShortcut}</p>
              </div>
              <div className="text-center p-3 rounded-lg bg-white/5">
                <kbd className="px-2 py-1 bg-white/10 rounded text-sm">Ctrl+E</kbd>
                <p className="text-sm mt-2 text-white/70">{t.exportShortcut}</p>
              </div>
              <div className="text-center p-3 rounded-lg bg-white/5">
                <kbd className="px-2 py-1 bg-white/10 rounded text-sm">Ctrl+N</kbd>
                <p className="text-sm mt-2 text-white/70">{t.notificationShortcut}</p>
              </div>
              <div className="text-center p-3 rounded-lg bg-white/5">
                <kbd className="px-2 py-1 bg-white/10 rounded text-sm">Escape</kbd>
                <p className="text-sm mt-2 text-white/70">{t.escapeShortcut}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Export Dialog */}
        <ExportDialog
          isOpen={showExportDialog}
          onClose={() => setShowExportDialog(false)}
          data={[]}
          defaultColumns={[]}
          module="employees"
          language={language}
          title={t.exportSample}
        />
      </div>
    </div>
  )
})

AdvancedFeaturesDemo.displayName = 'AdvancedFeaturesDemo'

export default AdvancedFeaturesDemo
