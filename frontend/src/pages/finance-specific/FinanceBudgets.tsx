import { useState, useEffect } from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  DollarSign,
  TrendingUp,
  TrendingDown,
  Plus,
  Search,
  Filter,
  Download,
  Calculator,
  PieChart,
  BarChart3,
  AlertTriangle,
  CheckCircle,
  Clock,
  Target,
  Edit,
  Eye,
  MoreHorizontal
} from 'lucide-react'

interface FinanceBudgetsProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    title: 'إدارة الميزانيات - المالية',
    subtitle: 'إدارة شاملة للميزانيات والتخطيط المالي',
    totalBudget: 'إجمالي الميزانية',
    allocatedBudget: 'الميزانية المخصصة',
    remainingBudget: 'الميزانية المتبقية',
    budgetUtilization: 'استخدام الميزانية',
    searchBudgets: 'البحث في الميزانيات...',
    createBudget: 'إنشاء ميزانية',
    exportData: 'تصدير البيانات',
    filterBy: 'تصفية حسب',
    department: 'القسم',
    period: 'الفترة',
    status: 'الحالة',
    budgetList: 'قائمة الميزانيات',
    budgetName: 'اسم الميزانية',
    allocated: 'المخصص',
    spent: 'المنفق',
    remaining: 'المتبقي',
    utilization: 'الاستخدام',
    actions: 'الإجراءات',
    viewDetails: 'عرض التفاصيل',
    editBudget: 'تعديل الميزانية',
    active: 'نشط',
    completed: 'مكتمل',
    overBudget: 'تجاوز الميزانية',
    onTrack: 'على المسار الصحيح',
    atRisk: 'في خطر',
    thisQuarter: 'هذا الربع',
    refresh: 'تحديث',
    budgetAlerts: 'تنبيهات الميزانية',
    budgetAnalysis: 'تحليل الميزانية'
  },
  en: {
    title: 'Budget Management - Finance',
    subtitle: 'Comprehensive budget management and financial planning',
    totalBudget: 'Total Budget',
    allocatedBudget: 'Allocated Budget',
    remainingBudget: 'Remaining Budget',
    budgetUtilization: 'Budget Utilization',
    searchBudgets: 'Search budgets...',
    createBudget: 'Create Budget',
    exportData: 'Export Data',
    filterBy: 'Filter By',
    department: 'Department',
    period: 'Period',
    status: 'Status',
    budgetList: 'Budget List',
    budgetName: 'Budget Name',
    allocated: 'Allocated',
    spent: 'Spent',
    remaining: 'Remaining',
    utilization: 'Utilization',
    actions: 'Actions',
    viewDetails: 'View Details',
    editBudget: 'Edit Budget',
    active: 'Active',
    completed: 'Completed',
    overBudget: 'Over Budget',
    onTrack: 'On Track',
    atRisk: 'At Risk',
    thisQuarter: 'This Quarter',
    refresh: 'Refresh',
    budgetAlerts: 'Budget Alerts',
    budgetAnalysis: 'Budget Analysis'
  }
}

export default function FinanceBudgets({ language }: FinanceBudgetsProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedDepartment, setSelectedDepartment] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('')
  const [refreshing, setRefreshing] = useState(false)
  const t = translations[language]
  const isRTL = language === 'ar'

  // Finance-specific budget metrics
  const [budgetMetrics, setBudgetMetrics] = useState({
    totalBudget: 5000000,
    allocatedBudget: 4200000,
    remainingBudget: 800000,
    budgetUtilization: 84.0
  })

  // Sample budget data with finance-specific information
  const [budgets] = useState([
    {
      id: 1,
      name: 'ميزانية تقنية المعلومات',
      nameEn: 'IT Budget',
      department: 'تقنية المعلومات',
      departmentEn: 'Information Technology',
      allocated: 800000,
      spent: 680000,
      remaining: 120000,
      utilization: 85.0,
      status: 'active',
      period: 'Q1 2024',
      category: 'operational'
    },
    {
      id: 2,
      name: 'ميزانية التسويق',
      nameEn: 'Marketing Budget',
      department: 'التسويق',
      departmentEn: 'Marketing',
      allocated: 600000,
      spent: 520000,
      remaining: 80000,
      utilization: 86.7,
      status: 'active',
      period: 'Q1 2024',
      category: 'marketing'
    },
    {
      id: 3,
      name: 'ميزانية الموارد البشرية',
      nameEn: 'HR Budget',
      department: 'الموارد البشرية',
      departmentEn: 'Human Resources',
      allocated: 450000,
      spent: 380000,
      remaining: 70000,
      utilization: 84.4,
      status: 'active',
      period: 'Q1 2024',
      category: 'operational'
    },
    {
      id: 4,
      name: 'ميزانية العمليات',
      nameEn: 'Operations Budget',
      department: 'العمليات',
      departmentEn: 'Operations',
      allocated: 1200000,
      spent: 1150000,
      remaining: 50000,
      utilization: 95.8,
      status: 'atRisk',
      period: 'Q1 2024',
      category: 'operational'
    }
  ])

  const handleRefresh = async () => {
    setRefreshing(true)
    await new Promise(resolve => setTimeout(resolve, 1000))
    setRefreshing(false)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-500/20 text-green-400 border-green-500/30'
      case 'completed':
        return 'bg-blue-500/20 text-blue-400 border-blue-500/30'
      case 'atRisk':
        return 'bg-red-500/20 text-red-400 border-red-500/30'
      case 'overBudget':
        return 'bg-red-600/20 text-red-300 border-red-600/30'
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30'
    }
  }

  const getUtilizationColor = (utilization: number) => {
    if (utilization >= 95) return 'text-red-400'
    if (utilization >= 85) return 'text-yellow-400'
    return 'text-green-400'
  }

  const getUtilizationBgColor = (utilization: number) => {
    if (utilization >= 95) return 'bg-red-500'
    if (utilization >= 85) return 'bg-yellow-500'
    return 'bg-green-500'
  }

  const filteredBudgets = budgets.filter(budget => {
    const matchesSearch = budget.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         budget.nameEn.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         budget.department.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesDepartment = !selectedDepartment || budget.department === selectedDepartment
    const matchesStatus = !selectedStatus || budget.status === selectedStatus
    
    return matchesSearch && matchesDepartment && matchesStatus
  })

  const budgetMetricsCards = [
    {
      title: t.totalBudget,
      value: new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR'
      }).format(budgetMetrics.totalBudget),
      change: '+8.5%',
      trend: 'up',
      icon: DollarSign,
      color: 'from-blue-500 to-blue-600'
    },
    {
      title: t.allocatedBudget,
      value: new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR'
      }).format(budgetMetrics.allocatedBudget),
      change: '+12.3%',
      trend: 'up',
      icon: Target,
      color: 'from-green-500 to-green-600'
    },
    {
      title: t.remainingBudget,
      value: new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR'
      }).format(budgetMetrics.remainingBudget),
      change: '-15.2%',
      trend: 'down',
      icon: Calculator,
      color: 'from-yellow-500 to-yellow-600'
    },
    {
      title: t.budgetUtilization,
      value: `${budgetMetrics.budgetUtilization}%`,
      change: t.onTrack,
      trend: 'stable',
      icon: BarChart3,
      color: 'from-purple-500 to-purple-600'
    }
  ]

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">{t.title}</h1>
          <p className="text-white/70">{t.subtitle}</p>
        </div>
        
        <div className="flex items-center gap-3">
          <Button
            onClick={handleRefresh}
            disabled={refreshing}
            className="glass-button"
          >
            <Clock className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            {t.refresh}
          </Button>
          <Button className="glass-button">
            <Plus className="h-4 w-4 mr-2" />
            {t.createBudget}
          </Button>
          <Button variant="outline" className="glass-button">
            <Download className="h-4 w-4 mr-2" />
            {t.exportData}
          </Button>
        </div>
      </div>

      {/* Budget Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {budgetMetricsCards.map((card, index) => (
          <Card key={index} className="glass-card border-white/20 hover:border-white/40 transition-all">
            <CardContent className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <p className="text-white/70 text-sm mb-1">{card.title}</p>
                  <p className="text-2xl font-bold text-white">{card.value}</p>
                </div>
                <div className={`p-3 rounded-xl bg-gradient-to-r ${card.color} shadow-lg`}>
                  <card.icon className="h-6 w-6 text-white" />
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <span className={`text-sm font-medium ${
                  card.trend === 'up' ? 'text-green-400' : 
                  card.trend === 'down' ? 'text-red-400' : 'text-blue-400'
                }`}>
                  {card.change}
                </span>
                <span className="text-xs text-white/60">vs last quarter</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Search and Filters */}
      <Card className="glass-card border-white/20">
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/60" />
              <Input
                placeholder={t.searchBudgets}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 glass-input"
              />
            </div>
            
            <select
              value={selectedDepartment}
              onChange={(e) => setSelectedDepartment(e.target.value)}
              className="glass-input"
            >
              <option value="">{t.department}</option>
              <option value="تقنية المعلومات">تقنية المعلومات</option>
              <option value="التسويق">التسويق</option>
              <option value="الموارد البشرية">الموارد البشرية</option>
              <option value="العمليات">العمليات</option>
            </select>
            
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="glass-input"
            >
              <option value="">{t.status}</option>
              <option value="active">{t.active}</option>
              <option value="completed">{t.completed}</option>
              <option value="atRisk">{t.atRisk}</option>
            </select>
            
            <Button variant="outline" className="glass-button">
              <Filter className="h-4 w-4 mr-2" />
              {t.filterBy}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Budget List */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white text-xl">{t.budgetList}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-white/20">
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.budgetName}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.department}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.allocated}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.spent}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.remaining}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.utilization}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.status}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.actions}</th>
                </tr>
              </thead>
              <tbody>
                {filteredBudgets.map((budget) => (
                  <tr key={budget.id} className="border-b border-white/10 hover:bg-white/5 transition-colors">
                    <td className="py-4 px-4">
                      <div>
                        <p className="text-white font-medium">
                          {language === 'ar' ? budget.name : budget.nameEn}
                        </p>
                        <p className="text-white/60 text-sm">{budget.period}</p>
                      </div>
                    </td>
                    <td className="py-4 px-4 text-white">
                      {language === 'ar' ? budget.department : budget.departmentEn}
                    </td>
                    <td className="py-4 px-4 text-white font-medium">
                      {new Intl.NumberFormat('ar-SA', {
                        style: 'currency',
                        currency: 'SAR'
                      }).format(budget.allocated)}
                    </td>
                    <td className="py-4 px-4 text-white">
                      {new Intl.NumberFormat('ar-SA', {
                        style: 'currency',
                        currency: 'SAR'
                      }).format(budget.spent)}
                    </td>
                    <td className="py-4 px-4 text-white">
                      {new Intl.NumberFormat('ar-SA', {
                        style: 'currency',
                        currency: 'SAR'
                      }).format(budget.remaining)}
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex items-center gap-2">
                        <div className="w-16 bg-white/20 rounded-full h-2">
                          <div 
                            className={`h-2 rounded-full ${getUtilizationBgColor(budget.utilization)}`}
                            style={{ width: `${Math.min(budget.utilization, 100)}%` }}
                          ></div>
                        </div>
                        <span className={`text-sm font-medium ${getUtilizationColor(budget.utilization)}`}>
                          {budget.utilization.toFixed(1)}%
                        </span>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(budget.status)}`}>
                        {t[budget.status as keyof typeof t]}
                      </span>
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex items-center gap-2">
                        <Button size="sm" variant="outline" className="glass-button p-2">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="outline" className="glass-button p-2">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="outline" className="glass-button p-2">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
