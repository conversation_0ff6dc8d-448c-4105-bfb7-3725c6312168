import { useState, useEffect } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  DollarSign,
  CreditCard,
  Search,
  Filter,
  Download,
  Eye,
  Edit,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  Clock,
  Receipt,
  FileText,
  Calculator
} from 'lucide-react'

interface FinanceCustomersProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    title: 'العملاء - منظور مالي',
    subtitle: 'إدارة العملاء من الناحية المالية: الفواتير والمدفوعات والائتمان',
    totalRevenue: 'إجمالي الإيرادات',
    outstandingInvoices: 'الفواتير المعلقة',
    creditLimits: 'حدود الائتمان',
    overduePayments: 'المدفوعات المتأخرة',
    searchCustomers: 'البحث في العملاء...',
    addCustomer: 'إضافة عميل',
    exportData: 'تصدير البيانات',
    customerName: 'اسم العميل',
    creditLimit: 'حد الائتمان',
    currentBalance: 'الرصيد الحالي',
    lastPayment: 'آخر دفعة',
    paymentTerms: 'شروط الدفع',
    creditStatus: 'حالة الائتمان',
    actions: 'الإجراءات',
    viewFinancials: 'عرض الماليات',
    sendInvoice: 'إرسال فاتورة',
    good: 'جيد',
    warning: 'تحذير',
    critical: 'حرج',
    blocked: 'محظور',
    net30: 'صافي 30 يوم',
    net60: 'صافي 60 يوم',
    prepaid: 'مدفوع مقدماً',
    cod: 'الدفع عند التسليم',
    refresh: 'تحديث',
    financialOverview: 'نظرة مالية عامة',
    creditManagement: 'إدارة الائتمان',
    paymentHistory: 'تاريخ المدفوعات',
    invoiceManagement: 'إدارة الفواتير',
    filterBy: 'تصفية حسب'
  },
  en: {
    title: 'Customers - Financial Perspective',
    subtitle: 'Customer management from financial perspective: invoicing, payments, and credit',
    totalRevenue: 'Total Revenue',
    outstandingInvoices: 'Outstanding Invoices',
    creditLimits: 'Credit Limits',
    overduePayments: 'Overdue Payments',
    searchCustomers: 'Search customers...',
    addCustomer: 'Add Customer',
    exportData: 'Export Data',
    customerName: 'Customer Name',
    creditLimit: 'Credit Limit',
    currentBalance: 'Current Balance',
    lastPayment: 'Last Payment',
    paymentTerms: 'Payment Terms',
    creditStatus: 'Credit Status',
    actions: 'Actions',
    viewFinancials: 'View Financials',
    sendInvoice: 'Send Invoice',
    good: 'Good',
    warning: 'Warning',
    critical: 'Critical',
    blocked: 'Blocked',
    net30: 'Net 30',
    net60: 'Net 60',
    prepaid: 'Prepaid',
    cod: 'COD',
    refresh: 'Refresh',
    financialOverview: 'Financial Overview',
    creditManagement: 'Credit Management',
    paymentHistory: 'Payment History',
    invoiceManagement: 'Invoice Management',
    filterBy: 'Filter By'
  }
}

export default function FinanceCustomers({ language }: FinanceCustomersProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('')
  const [selectedTerms, setSelectedTerms] = useState('')
  const [refreshing, setRefreshing] = useState(false)
  const t = translations[language]
  const isRTL = language === 'ar'

  // Finance-specific customer metrics
  const [financeMetrics, setFinanceMetrics] = useState({
    totalRevenue: 2850000,
    outstandingInvoices: 485000,
    creditLimits: 1200000,
    overduePayments: 125000
  })

  // Sample finance customer data
  const [customers] = useState([
    {
      id: 1,
      name: 'شركة التقنية المتقدمة',
      nameEn: 'Advanced Technology Company',
      creditLimit: 500000,
      currentBalance: 125000,
      availableCredit: 375000,
      lastPayment: '2024-01-20',
      lastPaymentAmount: 85000,
      paymentTerms: 'net30',
      paymentTermsAr: 'صافي 30 يوم',
      creditStatus: 'good',
      totalRevenue: 850000,
      outstandingAmount: 125000,
      overdueAmount: 0,
      invoiceCount: 12,
      avgPaymentDays: 28
    },
    {
      id: 2,
      name: 'مؤسسة النور للتجارة',
      nameEn: 'Al-Noor Trading Est.',
      creditLimit: 300000,
      currentBalance: 285000,
      availableCredit: 15000,
      lastPayment: '2024-01-15',
      lastPaymentAmount: 45000,
      paymentTerms: 'net60',
      paymentTermsAr: 'صافي 60 يوم',
      creditStatus: 'warning',
      totalRevenue: 620000,
      outstandingAmount: 285000,
      overdueAmount: 85000,
      invoiceCount: 18,
      avgPaymentDays: 65
    },
    {
      id: 3,
      name: 'شركة البناء الحديث',
      nameEn: 'Modern Construction Co.',
      creditLimit: 800000,
      currentBalance: 750000,
      availableCredit: 50000,
      lastPayment: '2023-12-28',
      lastPaymentAmount: 120000,
      paymentTerms: 'net30',
      paymentTermsAr: 'صافي 30 يوم',
      creditStatus: 'critical',
      totalRevenue: 1200000,
      outstandingAmount: 750000,
      overdueAmount: 320000,
      invoiceCount: 25,
      avgPaymentDays: 85
    }
  ])

  const handleRefresh = async () => {
    setRefreshing(true)
    await new Promise(resolve => setTimeout(resolve, 1000))
    setRefreshing(false)
  }

  const getCreditStatusColor = (status: string) => {
    switch (status) {
      case 'good':
        return 'bg-green-500/20 text-green-400 border-green-500/30'
      case 'warning':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
      case 'critical':
        return 'bg-red-500/20 text-red-400 border-red-500/30'
      case 'blocked':
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30'
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30'
    }
  }

  const getCreditStatusIcon = (status: string) => {
    switch (status) {
      case 'good':
        return <CheckCircle className="h-4 w-4" />
      case 'warning':
        return <AlertTriangle className="h-4 w-4" />
      case 'critical':
        return <AlertTriangle className="h-4 w-4" />
      case 'blocked':
        return <Clock className="h-4 w-4" />
      default:
        return <Clock className="h-4 w-4" />
    }
  }

  const getCreditUtilization = (current: number, limit: number) => {
    return Math.round((current / limit) * 100)
  }

  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.nameEn.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = !selectedStatus || customer.creditStatus === selectedStatus
    const matchesTerms = !selectedTerms || customer.paymentTerms === selectedTerms

    return matchesSearch && matchesStatus && matchesTerms
  })

  const financeMetricsCards = [
    {
      title: t.totalRevenue,
      value: new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR',
        notation: 'compact'
      }).format(financeMetrics.totalRevenue),
      change: '+18.5%',
      trend: 'up',
      icon: DollarSign,
      color: 'from-green-500 to-green-600'
    },
    {
      title: t.outstandingInvoices,
      value: new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR',
        notation: 'compact'
      }).format(financeMetrics.outstandingInvoices),
      change: '+12.3%',
      trend: 'up',
      icon: FileText,
      color: 'from-blue-500 to-blue-600'
    },
    {
      title: t.creditLimits,
      value: new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR',
        notation: 'compact'
      }).format(financeMetrics.creditLimits),
      change: '+5.2%',
      trend: 'up',
      icon: CreditCard,
      color: 'from-purple-500 to-purple-600'
    },
    {
      title: t.overduePayments,
      value: new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR',
        notation: 'compact'
      }).format(financeMetrics.overduePayments),
      change: '-8.7%',
      trend: 'down',
      icon: AlertTriangle,
      color: 'from-red-500 to-red-600'
    }
  ]

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">{t.title}</h1>
          <p className="text-white/70">{t.subtitle}</p>
        </div>

        <div className="flex items-center gap-3">
          <Button
            onClick={handleRefresh}
            disabled={refreshing}
            className="text-white/80 hover:text-white hover:bg-white/15 rounded-xl p-3 backdrop-blur-10"
          >
            <Calculator className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            {t.refresh}
          </Button>
          <Button className="text-white/80 hover:text-white hover:bg-white/15 rounded-xl p-3 backdrop-blur-10">
            <DollarSign className="h-4 w-4 mr-2" />
            {t.addCustomer}
          </Button>
          <Button variant="outline" className="text-white/80 hover:text-white hover:bg-white/15 rounded-xl p-3 backdrop-blur-10">
            <Download className="h-4 w-4 mr-2" />
            {t.exportData}
          </Button>
        </div>
      </div>

      {/* Finance Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {financeMetricsCards.map((card, index) => (
          <Card key={index} className="glass-card border-white/20 hover:border-white/40 transition-all">
            <CardContent className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <p className="text-white/70 text-sm mb-1">{card.title}</p>
                  <p className="text-2xl font-bold text-white">{card.value}</p>
                </div>
                <div className={`p-3 rounded-xl bg-gradient-to-r ${card.color} shadow-lg`}>
                  <card.icon className="h-6 w-6 text-white" />
                </div>
              </div>

              <div className="flex items-center gap-2">
                <span className={`text-sm font-medium ${
                  card.trend === 'up' ? 'text-green-400' :
                  card.trend === 'down' ? 'text-red-400' : 'text-blue-400'
                }`}>
                  {card.change}
                </span>
                <span className="text-xs text-white/60">vs last month</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Search and Filters */}
      <Card className="glass-card border-white/20">
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/60" />
              <Input
                placeholder={t.searchCustomers}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-white/10 border-white/20 text-white placeholder:text-white/60"
              />
            </div>

            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="bg-white/10 border-white/20 text-white rounded-lg px-3 py-2"
            >
              <option value="">{t.creditStatus}</option>
              <option value="good">{t.good}</option>
              <option value="warning">{t.warning}</option>
              <option value="critical">{t.critical}</option>
              <option value="blocked">{t.blocked}</option>
            </select>

            <select
              value={selectedTerms}
              onChange={(e) => setSelectedTerms(e.target.value)}
              className="bg-white/10 border-white/20 text-white rounded-lg px-3 py-2"
            >
              <option value="">{t.paymentTerms}</option>
              <option value="net30">{t.net30}</option>
              <option value="net60">{t.net60}</option>
              <option value="prepaid">{t.prepaid}</option>
              <option value="cod">{t.cod}</option>
            </select>

            <Button variant="outline" className="text-white/80 hover:text-white hover:bg-white/15 rounded-xl p-3 backdrop-blur-10">
              <Filter className="h-4 w-4 mr-2" />
              {t.filterBy}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Customer Financial List */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white text-xl">{t.creditManagement}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredCustomers.map((customer) => {
              const creditUtilization = getCreditUtilization(customer.currentBalance, customer.creditLimit)
              return (
                <div key={customer.id} className="p-6 glass-card border-white/10 hover:border-white/30 transition-all">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="text-white font-semibold text-lg">
                          {language === 'ar' ? customer.name : customer.nameEn}
                        </h3>
                        <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${getCreditStatusColor(customer.creditStatus)}`}>
                          {getCreditStatusIcon(customer.creditStatus)}
                          {t[customer.creditStatus as keyof typeof t]}
                        </span>
                      </div>

                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-4">
                        <div>
                          <span className="text-white/60">{t.creditLimit}: </span>
                          <span className="text-white font-medium">
                            {new Intl.NumberFormat('ar-SA', {
                              style: 'currency',
                              currency: 'SAR'
                            }).format(customer.creditLimit)}
                          </span>
                        </div>
                        <div>
                          <span className="text-white/60">{t.currentBalance}: </span>
                          <span className="text-white font-medium">
                            {new Intl.NumberFormat('ar-SA', {
                              style: 'currency',
                              currency: 'SAR'
                            }).format(customer.currentBalance)}
                          </span>
                        </div>
                        <div>
                          <span className="text-white/60">{t.lastPayment}: </span>
                          <span className="text-white">{customer.lastPayment}</span>
                        </div>
                        <div>
                          <span className="text-white/60">{t.paymentTerms}: </span>
                          <span className="text-white">
                            {language === 'ar' ? customer.paymentTermsAr : t[customer.paymentTerms as keyof typeof t]}
                          </span>
                        </div>
                      </div>

                      <div className="flex items-center gap-6 text-sm mb-4">
                        <div>
                          <span className="text-white/60">Total Revenue: </span>
                          <span className="text-green-400 font-medium">
                            {new Intl.NumberFormat('ar-SA', {
                              style: 'currency',
                              currency: 'SAR'
                            }).format(customer.totalRevenue)}
                          </span>
                        </div>
                        <div>
                          <span className="text-white/60">Outstanding: </span>
                          <span className="text-yellow-400 font-medium">
                            {new Intl.NumberFormat('ar-SA', {
                              style: 'currency',
                              currency: 'SAR'
                            }).format(customer.outstandingAmount)}
                          </span>
                        </div>
                        {customer.overdueAmount > 0 && (
                          <div>
                            <span className="text-white/60">Overdue: </span>
                            <span className="text-red-400 font-medium">
                              {new Intl.NumberFormat('ar-SA', {
                                style: 'currency',
                                currency: 'SAR'
                              }).format(customer.overdueAmount)}
                            </span>
                          </div>
                        )}
                      </div>

                      <div className="flex items-center gap-4">
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-1">
                            <span className="text-white/60 text-sm">Credit Utilization</span>
                            <span className="text-white text-sm font-medium">{creditUtilization}%</span>
                          </div>
                          <div className="w-full bg-white/20 rounded-full h-2">
                            <div
                              className={`h-2 rounded-full ${
                                creditUtilization > 90 ? 'bg-red-500' :
                                creditUtilization > 70 ? 'bg-yellow-500' : 'bg-green-500'
                              }`}
                              style={{ width: `${Math.min(creditUtilization, 100)}%` }}
                            ></div>
                          </div>
                        </div>
                        <div className="text-sm">
                          <span className="text-white/60">Avg Payment: </span>
                          <span className="text-white">{customer.avgPaymentDays} days</span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-2 ml-4">
                      <Button size="sm" variant="outline" className="text-white/80 hover:text-white hover:bg-white/15 rounded-lg p-2 backdrop-blur-10">
                        <Receipt className="h-4 w-4" />
                      </Button>
                      <Button size="sm" variant="outline" className="text-white/80 hover:text-white hover:bg-white/15 rounded-lg p-2 backdrop-blur-10">
                        <FileText className="h-4 w-4" />
                      </Button>
                      <Button size="sm" variant="outline" className="text-white/80 hover:text-white hover:bg-white/15 rounded-lg p-2 backdrop-blur-10">
                        <Eye className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  <div className="flex items-center justify-between pt-4 border-t border-white/10">
                    <div className="flex items-center gap-4">
                      <div className="text-sm">
                        <span className="text-white/60">Available Credit: </span>
                        <span className="text-green-400 font-medium">
                          {new Intl.NumberFormat('ar-SA', {
                            style: 'currency',
                            currency: 'SAR'
                          }).format(customer.availableCredit)}
                        </span>
                      </div>
                      <div className="text-sm">
                        <span className="text-white/60">Invoices: </span>
                        <span className="text-white">{customer.invoiceCount}</span>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <Button size="sm" className="text-white/80 hover:text-white hover:bg-white/15 rounded-lg p-2 backdrop-blur-10">
                        <Receipt className="h-4 w-4 mr-1" />
                        {t.sendInvoice}
                      </Button>
                      <Button size="sm" variant="outline" className="text-white/80 hover:text-white hover:bg-white/15 rounded-lg p-2 backdrop-blur-10">
                        <Calculator className="h-4 w-4 mr-1" />
                        {t.viewFinancials}
                      </Button>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
