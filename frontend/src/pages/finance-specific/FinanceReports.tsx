import { useState, useEffect } from 'react'
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  FileText,
  DollarSign,
  TrendingUp,
  TrendingDown,
  BarChart3,
  <PERSON><PERSON><PERSON>,
  Calculator,
  Target,
  Download,
  Eye,
  Calendar,
  Clock,
  CreditCard,
  Wallet,
  Receipt,
  AlertTriangle
} from 'lucide-react'

interface FinanceReportsProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    title: 'التقارير المالية - المالية',
    subtitle: 'تقارير شاملة للأداء المالي والميزانيات',
    financialReports: 'التقارير المالية',
    budgetReports: 'تقارير الميزانية',
    expenseReports: 'تقارير المصروفات',
    revenueReports: 'تقارير الإيرادات',
    generateReport: 'إنشاء تقرير',
    downloadReport: 'تحميل التقرير',
    viewReport: 'عرض التقرير',
    monthlyReport: 'التقرير الشهري',
    quarterlyReport: 'التقرير الربعي',
    annualReport: 'التقرير السنوي',
    profitLoss: 'الأرباح والخسائر',
    cashFlow: 'التدفق النقدي',
    balanceSheet: 'الميزانية العمومية',
    budgetAnalysis: 'تحليل الميزانية',
    expenseAnalysis: 'تحليل المصروفات',
    revenueAnalysis: 'تحليل الإيرادات',
    costAnalysis: 'تحليل التكاليف',
    financialKPIs: 'مؤشرات الأداء المالي',
    quickReports: 'التقارير السريعة',
    customReports: 'التقارير المخصصة',
    recentReports: 'التقارير الحديثة',
    reportTemplates: 'قوالب التقارير',
    lastGenerated: 'آخر إنشاء',
    reportSize: 'حجم التقرير',
    status: 'الحالة',
    ready: 'جاهز',
    processing: 'قيد المعالجة',
    failed: 'فشل',
    refresh: 'تحديث',
    amount: 'المبلغ',
    variance: 'التباين',
    trend: 'الاتجاه'
  },
  en: {
    title: 'Financial Reports - Finance',
    subtitle: 'Comprehensive financial performance and budget reports',
    financialReports: 'Financial Reports',
    budgetReports: 'Budget Reports',
    expenseReports: 'Expense Reports',
    revenueReports: 'Revenue Reports',
    generateReport: 'Generate Report',
    downloadReport: 'Download Report',
    viewReport: 'View Report',
    monthlyReport: 'Monthly Report',
    quarterlyReport: 'Quarterly Report',
    annualReport: 'Annual Report',
    profitLoss: 'Profit & Loss',
    cashFlow: 'Cash Flow',
    balanceSheet: 'Balance Sheet',
    budgetAnalysis: 'Budget Analysis',
    expenseAnalysis: 'Expense Analysis',
    revenueAnalysis: 'Revenue Analysis',
    costAnalysis: 'Cost Analysis',
    financialKPIs: 'Financial KPIs',
    quickReports: 'Quick Reports',
    customReports: 'Custom Reports',
    recentReports: 'Recent Reports',
    reportTemplates: 'Report Templates',
    lastGenerated: 'Last Generated',
    reportSize: 'Report Size',
    status: 'Status',
    ready: 'Ready',
    processing: 'Processing',
    failed: 'Failed',
    refresh: 'Refresh',
    amount: 'Amount',
    variance: 'Variance',
    trend: 'Trend'
  }
}

export default function FinanceReports({ language }: FinanceReportsProps) {
  const [refreshing, setRefreshing] = useState(false)
  const t = translations[language]
  const isRTL = language === 'ar'

  // Sample finance reports data
  const [quickReports] = useState([
    {
      id: 1,
      title: 'تقرير الأرباح والخسائر',
      titleEn: 'Profit & Loss Report',
      description: 'تقرير شامل عن الأرباح والخسائر الشهرية',
      descriptionEn: 'Comprehensive monthly profit and loss report',
      icon: TrendingUp,
      color: 'from-green-500 to-green-600',
      type: 'profitloss',
      amount: 850000,
      variance: '+12.5%'
    },
    {
      id: 2,
      title: 'تحليل التدفق النقدي',
      titleEn: 'Cash Flow Analysis',
      description: 'تحليل مفصل للتدفقات النقدية الداخلة والخارجة',
      descriptionEn: 'Detailed analysis of cash inflows and outflows',
      icon: Wallet,
      color: 'from-blue-500 to-blue-600',
      type: 'cashflow',
      amount: 1250000,
      variance: '+8.3%'
    },
    {
      id: 3,
      title: 'تقرير الميزانية العمومية',
      titleEn: 'Balance Sheet Report',
      description: 'الميزانية العمومية والمركز المالي للشركة',
      descriptionEn: 'Company balance sheet and financial position',
      icon: Calculator,
      color: 'from-purple-500 to-purple-600',
      type: 'balancesheet',
      amount: 5200000,
      variance: '+5.7%'
    },
    {
      id: 4,
      title: 'تحليل المصروفات',
      titleEn: 'Expense Analysis',
      description: 'تحليل شامل للمصروفات حسب الفئات',
      descriptionEn: 'Comprehensive expense analysis by categories',
      icon: Receipt,
      color: 'from-red-500 to-red-600',
      type: 'expenses',
      amount: 1920000,
      variance: '+15.2%'
    },
    {
      id: 5,
      title: 'تحليل الإيرادات',
      titleEn: 'Revenue Analysis',
      description: 'تحليل مصادر الإيرادات والنمو',
      descriptionEn: 'Revenue sources and growth analysis',
      icon: DollarSign,
      color: 'from-cyan-500 to-cyan-600',
      type: 'revenue',
      amount: 2850000,
      variance: '+18.3%'
    },
    {
      id: 6,
      title: 'تحليل الميزانية',
      titleEn: 'Budget Analysis',
      description: 'مقارنة الميزانية المخططة مع الفعلية',
      descriptionEn: 'Planned vs actual budget comparison',
      icon: Target,
      color: 'from-yellow-500 to-yellow-600',
      type: 'budget',
      amount: 2500000,
      variance: '-3.2%'
    }
  ])

  const [recentReports] = useState([
    {
      id: 1,
      name: 'تقرير الأرباح والخسائر - يناير 2024',
      nameEn: 'P&L Report - January 2024',
      type: 'profitloss',
      lastGenerated: '2024-01-28',
      size: '3.2 MB',
      status: 'ready',
      downloads: 67,
      amount: 850000
    },
    {
      id: 2,
      name: 'تحليل التدفق النقدي - Q4 2023',
      nameEn: 'Cash Flow Analysis - Q4 2023',
      type: 'cashflow',
      lastGenerated: '2024-01-15',
      size: '2.8 MB',
      status: 'ready',
      downloads: 45,
      amount: 1250000
    },
    {
      id: 3,
      name: 'الميزانية العمومية - ديسمبر 2023',
      nameEn: 'Balance Sheet - December 2023',
      type: 'balancesheet',
      lastGenerated: '2024-01-10',
      size: '4.1 MB',
      status: 'processing',
      downloads: 0,
      amount: 5200000
    },
    {
      id: 4,
      name: 'تحليل المصروفات - 2023',
      nameEn: 'Expense Analysis - 2023',
      type: 'expenses',
      lastGenerated: '2024-01-05',
      size: '2.4 MB',
      status: 'ready',
      downloads: 89,
      amount: 1920000
    }
  ])

  const handleRefresh = async () => {
    setRefreshing(true)
    await new Promise(resolve => setTimeout(resolve, 1000))
    setRefreshing(false)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ready':
        return 'bg-green-500/20 text-green-400 border-green-500/30'
      case 'processing':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
      case 'failed':
        return 'bg-red-500/20 text-red-400 border-red-500/30'
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30'
    }
  }

  const getVarianceColor = (variance: string) => {
    if (variance.startsWith('+')) return 'text-green-400'
    if (variance.startsWith('-')) return 'text-red-400'
    return 'text-blue-400'
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'profitloss':
        return <TrendingUp className="h-4 w-4" />
      case 'cashflow':
        return <Wallet className="h-4 w-4" />
      case 'balancesheet':
        return <Calculator className="h-4 w-4" />
      case 'expenses':
        return <Receipt className="h-4 w-4" />
      case 'revenue':
        return <DollarSign className="h-4 w-4" />
      case 'budget':
        return <Target className="h-4 w-4" />
      default:
        return <FileText className="h-4 w-4" />
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">{t.title}</h1>
          <p className="text-white/70">{t.subtitle}</p>
        </div>
        
        <div className="flex items-center gap-3">
          <Button
            onClick={handleRefresh}
            disabled={refreshing}
            className="glass-button"
          >
            <BarChart3 className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            {t.refresh}
          </Button>
          <Button className="glass-button">
            <FileText className="h-4 w-4 mr-2" />
            {t.generateReport}
          </Button>
        </div>
      </div>

      {/* Quick Reports */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white text-xl">{t.quickReports}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {quickReports.map((report) => (
              <div
                key={report.id}
                className="group p-6 glass-card border-white/10 hover:border-white/30 cursor-pointer transition-all duration-300 hover:scale-105"
              >
                <div className="flex items-start gap-4 mb-4">
                  <div className={`p-3 rounded-xl bg-gradient-to-r ${report.color} shadow-lg group-hover:shadow-xl transition-all`}>
                    <report.icon className="h-6 w-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-white font-semibold mb-1">
                      {language === 'ar' ? report.title : report.titleEn}
                    </h3>
                    <p className="text-white/70 text-sm mb-2">
                      {language === 'ar' ? report.description : report.descriptionEn}
                    </p>
                    <div className="flex items-center justify-between">
                      <span className="text-white font-bold">
                        {new Intl.NumberFormat('ar-SA', {
                          style: 'currency',
                          currency: 'SAR'
                        }).format(report.amount)}
                      </span>
                      <span className={`text-sm font-medium ${getVarianceColor(report.variance)}`}>
                        {report.variance}
                      </span>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <Button size="sm" className="glass-button flex-1">
                    <Eye className="h-4 w-4 mr-2" />
                    {t.generateReport}
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recent Reports */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white text-xl">{t.recentReports}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-white/20">
                  <th className="text-left py-3 px-4 text-white/80 font-medium">اسم التقرير</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">النوع</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.amount}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.lastGenerated}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.reportSize}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.status}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">التحميلات</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {recentReports.map((report) => (
                  <tr key={report.id} className="border-b border-white/10 hover:bg-white/5 transition-colors">
                    <td className="py-4 px-4">
                      <div className="flex items-center gap-3">
                        <div className="p-2 rounded-lg bg-white/10">
                          {getTypeIcon(report.type)}
                        </div>
                        <div>
                          <p className="text-white font-medium">
                            {language === 'ar' ? report.name : report.nameEn}
                          </p>
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <span className="inline-flex items-center gap-2 text-white/80">
                        {getTypeIcon(report.type)}
                        {report.type}
                      </span>
                    </td>
                    <td className="py-4 px-4 text-white font-medium">
                      {new Intl.NumberFormat('ar-SA', {
                        style: 'currency',
                        currency: 'SAR'
                      }).format(report.amount)}
                    </td>
                    <td className="py-4 px-4 text-white">
                      {report.lastGenerated}
                    </td>
                    <td className="py-4 px-4 text-white">
                      {report.size}
                    </td>
                    <td className="py-4 px-4">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(report.status)}`}>
                        {t[report.status as keyof typeof t]}
                      </span>
                    </td>
                    <td className="py-4 px-4 text-white">
                      {report.downloads} مرة
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex items-center gap-2">
                        {report.status === 'ready' && (
                          <>
                            <Button size="sm" variant="outline" className="glass-button p-2">
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button size="sm" variant="outline" className="glass-button p-2">
                              <Download className="h-4 w-4" />
                            </Button>
                          </>
                        )}
                        {report.status === 'processing' && (
                          <div className="flex items-center gap-2 text-yellow-400">
                            <Clock className="h-4 w-4 animate-spin" />
                            <span className="text-sm">معالجة...</span>
                          </div>
                        )}
                        {report.status === 'failed' && (
                          <div className="flex items-center gap-2 text-red-400">
                            <AlertTriangle className="h-4 w-4" />
                            <span className="text-sm">فشل</span>
                          </div>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
