/**
 * Aging Reports Page - AP and AR Aging Analysis
 * Provides aging analysis for both accounts payable and receivable
 */

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Calendar,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  DollarSign,
  Users,
  Building,
  FileText,
  Download
} from 'lucide-react'

interface AgingReportsProps {
  language: 'ar' | 'en'
}

interface AgingData {
  vendor_id?: number
  customer_id?: number
  vendor_name?: string
  customer_name?: string
  current: number
  '1-30_days': number
  '31-60_days': number
  '60+_days': number
  total: number
}

const translations = {
  ar: {
    agingReports: 'تقارير الأعمار',
    accountsPayable: 'الحسابات الدائنة',
    accountsReceivable: 'الحسابات المدينة',
    vendorAging: 'أعمار الموردين',
    customerAging: 'أعمار العملاء',
    current: 'الحالي',
    days1to30: '1-30 يوم',
    days31to60: '31-60 يوم',
    days60Plus: '60+ يوم',
    total: 'الإجمالي',
    vendor: 'المورد',
    customer: 'العميل',
    amount: 'المبلغ',
    exportReport: 'تصدير التقرير',
    summary: 'الملخص',
    totalOutstanding: 'إجمالي المستحق',
    overdueAmount: 'المبلغ المتأخر',
    averageDaysOutstanding: 'متوسط الأيام المستحقة',
    noDataFound: 'لم يتم العثور على بيانات',
    refreshData: 'تحديث البيانات',
    asOfDate: 'كما في تاريخ',
    payableAging: 'أعمار المدفوعات',
    receivableAging: 'أعمار المقبوضات'
  },
  en: {
    agingReports: 'Aging Reports',
    accountsPayable: 'Accounts Payable',
    accountsReceivable: 'Accounts Receivable',
    vendorAging: 'Vendor Aging',
    customerAging: 'Customer Aging',
    current: 'Current',
    days1to30: '1-30 Days',
    days31to60: '31-60 Days',
    days60Plus: '60+ Days',
    total: 'Total',
    vendor: 'Vendor',
    customer: 'Customer',
    amount: 'Amount',
    exportReport: 'Export Report',
    summary: 'Summary',
    totalOutstanding: 'Total Outstanding',
    overdueAmount: 'Overdue Amount',
    averageDaysOutstanding: 'Average Days Outstanding',
    noDataFound: 'No data found',
    refreshData: 'Refresh Data',
    asOfDate: 'As of Date',
    payableAging: 'Payable Aging',
    receivableAging: 'Receivable Aging'
  }
}

// @ts-ignore
const AgingReports: (React as any).FC<AgingReportsProps> = ({ language }) => {
  // @ts-ignore
  const t = translations[language]
  // @ts-ignore
  const [vendorAging, setVendorAging] = useState<AgingData[]>([])
  // @ts-ignore
  const [customerAging, setCustomerAging] = useState<AgingData[]>([])
  const [loading, setLoading] = useState(true as any)
  // @ts-ignore
  const [activeTab, setActiveTab] = useState<'payable' | 'receivable'>('payable')

  // @ts-ignore
  useEffect(( as any) => {
    // @ts-ignore
    fetchAgingData( as any)
  }, [])

  // @ts-ignore
  const fetchAgingData = async () => {
    // @ts-ignore
    try {
      setLoading(true as any)
      // @ts-ignore
      const token = (localStorage as any).getItem('token' as any)
      const headers = {
        // @ts-ignore
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      // @ts-ignore
      }

      // Fetch vendor aging
      const vendorResponse = await fetch('/api/vendors/aging_report/', { headers } as any)
      if ((vendorResponse as any).ok) {
        // @ts-ignore
        const vendorData = await (vendorResponse as any).json( as any)
        setVendorAging(vendorData as any)
      // @ts-ignore
      }

      // Fetch customer aging
      const customerResponse = await fetch('/api/customer-invoices/aging_report/', { headers } as any)
      if ((customerResponse as any).ok) {
        // @ts-ignore
        const customerData = await (customerResponse as any).json( as any)
        setCustomerAging(customerData as any)
      // @ts-ignore
      }

    // @ts-ignore
    } catch (error) {
      (console as any).error('Error fetching aging data:', error as any)
    } finally {
      setLoading(false as any)
    }
  // @ts-ignore
  }

  // @ts-ignore
  const formatCurrency = (amount: number): string => {
    // @ts-ignore
    return new (Intl as any).NumberFormat('ar-SA', {
      // @ts-ignore
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 2,
    // @ts-ignore
    } as any).format(amount as any)
  // @ts-ignore
  }

  // @ts-ignore
  const calculateTotals = (data: AgingData[]): void => {
    // @ts-ignore
    return (data as any).reduce((acc, item as any) => ({
      // @ts-ignore
      current: (acc as any).current + (item as any).current,
      days1to30: (acc as any).days1to30 + item['1-30_days'],
      days31to60: (acc as any).days31to60 + item['31-60_days'],
      days60Plus: (acc as any).days60Plus + item['60+_days'],
      total: (acc as any).total + (item as any).total
    // @ts-ignore
    }), {
      // @ts-ignore
      current: 0,
      days1to30: 0,
      days31to60: 0,
      days60Plus: 0,
      total: 0
    // @ts-ignore
    })
  // @ts-ignore
  }

  // @ts-ignore
  const getAgingColor = (days: string): void => {
    // @ts-ignore
    switch (days) {
      // @ts-ignore
      case 'current':
        return 'text-green-600'
      case '1-30':
        return 'text-yellow-600'
      case '31-60':
        return 'text-orange-600'
      case '60+':
        return 'text-red-600'
      default:
        return 'text-gray-600'
    // @ts-ignore
    }
  // @ts-ignore
  }

  const vendorTotals = calculateTotals(vendorAging as any)
  const customerTotals = calculateTotals(customerAging as any)

  if (loading) {
    // @ts-ignore
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  // @ts-ignore
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">{(t as any).agingReports}</h1>
          <p className="text-gray-600 mt-1">
            {language === 'ar' ? 'تحليل أعمار الحسابات المدينة والدائنة' : 'Accounts Payable & Receivable Aging Analysis'}
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={fetchAgingData}>
            <TrendingUp className="h-4 w-4 mr-2" />
            {(t as any).refreshData}
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            {(t as any).exportReport}
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{(t as any).accountsPayable}</p>
                <p className="text-2xl font-bold mt-1 text-red-600">{formatCurrency((vendorTotals as any as any).total)}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{(t as any).accountsReceivable}</p>
                <p className="text-2xl font-bold mt-1 text-green-600">{formatCurrency((customerTotals as any as any).total)}</p>
              </div>
              <TrendingDown className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">AP {(t as any).overdueAmount}</p>
                <p className="text-2xl font-bold mt-1 text-red-600">
                  {formatCurrency((vendorTotals as any as any).days31to60 + (vendorTotals as any).days60Plus)}
                </p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">AR {(t as any).overdueAmount}</p>
                <p className="text-2xl font-bold mt-1 text-orange-600">
                  {formatCurrency((customerTotals as any as any).days31to60 + (customerTotals as any).days60Plus)}
                </p>
              </div>
              <AlertTriangle className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
        <button
          onClick={() => setActiveTab('payable' as any)}
          className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'payable'
              ? 'bg-white text-blue-600 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          <Building className="h-4 w-4 inline mr-2" />
          {(t as any).payableAging}
        </button>
        <button
          onClick={() => setActiveTab('receivable' as any)}
          className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'receivable'
              ? 'bg-white text-blue-600 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          <Users className="h-4 w-4 inline mr-2" />
          {(t as any).receivableAging}
        </button>
      </div>

      {/* Aging Table */}
      <Card>
        <CardHeader>
          <CardTitle>
            {activeTab === 'payable' ? (t as any).vendorAging : (t as any).customerAging}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-4 font-medium text-gray-900">
                    {activeTab === 'payable' ? (t as any).vendor : (t as any).customer}
                  </th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900 text-green-600">
                    {(t as any).current}
                  </th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900 text-yellow-600">
                    {(t as any).days1to30}
                  </th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900 text-orange-600">
                    {(t as any).days31to60}
                  </th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900 text-red-600">
                    {(t as any).days60Plus}
                  </th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900">
                    {(t as any).total}
                  </th>
                </tr>
              </thead>
              <tbody>
                // @ts-ignore
                {(activeTab === 'payable' ? vendorAging : customerAging).map((item, index as any) => (
                  <tr key={index} className="border-b hover:bg-gray-50">
                    <td className="py-3 px-4">
                      <span className="font-medium">
                        {activeTab === 'payable' ? (item as any).vendor_name : (item as any).customer_name}
                      </span>
                    </td>
                    <td className="py-3 px-4 text-right font-mono text-green-600">
                      {formatCurrency((item as any as any).current)}
                    </td>
                    <td className="py-3 px-4 text-right font-mono text-yellow-600">
                      {formatCurrency(item['1-30_days'] as any)}
                    </td>
                    <td className="py-3 px-4 text-right font-mono text-orange-600">
                      {formatCurrency(item['31-60_days'] as any)}
                    </td>
                    <td className="py-3 px-4 text-right font-mono text-red-600">
                      {formatCurrency(item['60+_days'] as any)}
                    </td>
                    <td className="py-3 px-4 text-right font-mono font-bold">
                      {formatCurrency((item as any as any).total)}
                    </td>
                  </tr>
                ))}
                
                {/* Totals Row */}
                <tr className="border-t-2 border-gray-300 bg-gray-50 font-bold">
                  <td className="py-3 px-4 font-bold">{(t as any).total}</td>
                  <td className="py-3 px-4 text-right font-mono text-green-600">
                    {formatCurrency(activeTab === 'payable' ? (vendorTotals as any as any).current : (customerTotals as any).current)}
                  </td>
                  <td className="py-3 px-4 text-right font-mono text-yellow-600">
                    {formatCurrency(activeTab === 'payable' ? (vendorTotals as any as any).days1to30 : (customerTotals as any).days1to30)}
                  </td>
                  <td className="py-3 px-4 text-right font-mono text-orange-600">
                    {formatCurrency(activeTab === 'payable' ? (vendorTotals as any as any).days31to60 : (customerTotals as any).days31to60)}
                  </td>
                  <td className="py-3 px-4 text-right font-mono text-red-600">
                    {formatCurrency(activeTab === 'payable' ? (vendorTotals as any as any).days60Plus : (customerTotals as any).days60Plus)}
                  </td>
                  <td className="py-3 px-4 text-right font-mono font-bold text-lg">
                    {formatCurrency(activeTab === 'payable' ? (vendorTotals as any as any).total : (customerTotals as any).total)}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          
          {((activeTab === 'payable' ? vendorAging : customerAging).length === 0) && (
            <div className="text-center py-8">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">{(t as any).noDataFound}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* As of Date */}
      <div className="text-center text-sm text-gray-500">
        <Calendar className="h-4 w-4 inline mr-1" />
        // @ts-ignore
        {(t as any).asOfDate}: {new Date( as any).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US' as any)}
      </div>
    </div>
  )
// @ts-ignore
}

// @ts-ignore
export default AgingReports