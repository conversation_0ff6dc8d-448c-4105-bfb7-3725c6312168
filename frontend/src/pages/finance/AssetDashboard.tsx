import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  CardContent, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Input } from '@/components/ui/input';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  Package, 
  TrendingUp, 
  TrendingDown, 
  AlertTriangle,
  Calendar,
  DollarSign,
  Settings,
  Plus,
  Search,
  Filter,
  BarChart3
} from 'lucide-react';
// Language is passed as prop, not from context

interface DashboardStats {
  total_assets: number;
  active_assets: number;
  status_breakdown: Array<{status: string; count: number}>;
  condition_breakdown: Array<{condition: string; count: number}>;
  financial_summary: {
    total_purchase_value: number;
    total_current_value: number;
    total_depreciation: number;
  };
  alerts: {
    maintenance_due: number;
    warranty_expiring: number;
  };
}

interface Asset {
  id: number;
  asset_id: string;
  name: string;
  category_name: string;
  status: string;
  condition: string;
  purchase_price: string;
  current_book_value: number;
  assigned_to_name: string;
  location: string;
  is_maintenance_due: boolean;
  is_under_warranty: boolean;
  days_until_warranty_expiry: number;
}

interface AssetDashboardProps {
  language: 'ar' | 'en';
}

const AssetDashboard: React.FC<AssetDashboardProps> = ({ language }) => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [recentAssets, setRecentAssets] = useState<Asset[]>([]);
  const [maintenanceDue, setMaintenanceDue] = useState<Asset[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
    fetchRecentAssets();
    fetchMaintenanceDue();
  }, []);

  const fetchDashboardData = async () => {
    try {
      const response = await fetch('/api/assets/dashboard_stats/');
      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
    }
  };

  const fetchRecentAssets = async () => {
    try {
      const response = await fetch('/api/assets/?ordering=-created_at&page_size=5');
      if (response.ok) {
        const data = await response.json();
        setRecentAssets(data.results || data);
      }
    } catch (error) {
      console.error('Error fetching recent assets:', error);
    }
  };

  const fetchMaintenanceDue = async () => {
    try {
      const response = await fetch('/api/assets/maintenance_due/?days=30');
      if (response.ok) {
        const data = await response.json();
        setMaintenanceDue(data);
      }
    } catch (error) {
      console.error('Error fetching maintenance due:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string): void => {
    const colors = {
      'AVAILABLE': 'bg-green-500',
      'IN_USE': 'bg-blue-500',
      'MAINTENANCE': 'bg-yellow-500',
      'RETIRED': 'bg-gray-500',
      'LOST': 'bg-red-500',
      'DAMAGED': 'bg-red-600',
      'DISPOSED': 'bg-gray-600'
    };
    return colors[status as keyof typeof colors] || 'bg-gray-400';
  };

  const getConditionColor = (condition: string): void => {
    const colors = {
      'EXCELLENT': 'text-green-600',
      'GOOD': 'text-blue-600',
      'FAIR': 'text-yellow-600',
      'POOR': 'text-orange-600',
      'DAMAGED': 'text-red-600'
    };
    return colors[condition as keyof typeof colors] || 'text-gray-600';
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">
          {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">
            {language === 'ar' ? 'لوحة إدارة الأصول' : 'Asset Management Dashboard'}
          </h1>
          <p className="text-muted-foreground">
            {language === 'ar' 
              ? 'نظرة عامة على الأصول والصيانة والاستهلاك'
              : 'Overview of assets, maintenance, and depreciation'
            }
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <BarChart3 className="h-4 w-4 mr-2" />
            {language === 'ar' ? 'التقارير' : 'Reports'}
          </Button>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            {language === 'ar' ? 'إضافة أصل' : 'Add Asset'}
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'إجمالي الأصول' : 'Total Assets'}
              </CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total_assets}</div>
              <p className="text-xs text-muted-foreground">
                {stats.active_assets} {language === 'ar' ? 'نشط' : 'active'}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'القيمة الإجمالية' : 'Total Value'}
              </CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(stats.financial_summary.total_purchase_value)}
              </div>
              <p className="text-xs text-muted-foreground">
                {language === 'ar' ? 'قيمة الشراء' : 'Purchase value'}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'الاستهلاك المتراكم' : 'Total Depreciation'}
              </CardTitle>
              <TrendingDown className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(stats.financial_summary.total_depreciation)}
              </div>
              <div className="flex items-center text-xs text-muted-foreground">
                <TrendingDown className="h-3 w-3 mr-1" />
                {language === 'ar' ? 'انخفاض القيمة' : 'Value decline'}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'تنبيهات الصيانة' : 'Maintenance Alerts'}
              </CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">
                {stats.alerts.maintenance_due}
              </div>
              <p className="text-xs text-muted-foreground">
                {language === 'ar' ? 'تحتاج صيانة' : 'Need maintenance'}
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Charts and Breakdowns */}
      {stats && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Status Breakdown */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                {language === 'ar' ? 'توزيع حالة الأصول' : 'Asset Status Breakdown'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {stats.status_breakdown.map((item, index) => {
                  const percentage = (item.count / stats.total_assets) * 100;
                  return (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className={`w-3 h-3 rounded-full ${getStatusColor(item.status)}`} />
                        <span className="text-sm font-medium">
                          {language === 'ar' 
                            ? item.status === 'AVAILABLE' ? 'متاح' 
                              : item.status === 'IN_USE' ? 'قيد الاستخدام'
                              : item.status === 'MAINTENANCE' ? 'تحت الصيانة'
                              : item.status
                            : item.status.replace('_', ' ')
                          }
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Progress value={percentage} className="w-20" />
                        <span className="text-sm text-muted-foreground w-8">
                          {item.count}
                        </span>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>

          {/* Condition Breakdown */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                {language === 'ar' ? 'توزيع حالة الأصول' : 'Asset Condition Breakdown'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {stats.condition_breakdown.map((item, index) => {
                  const percentage = (item.count / stats.total_assets) * 100;
                  return (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className={`text-sm font-medium ${getConditionColor(item.condition)}`}>
                          {language === 'ar' 
                            ? item.condition === 'EXCELLENT' ? 'ممتاز' 
                              : item.condition === 'GOOD' ? 'جيد'
                              : item.condition === 'FAIR' ? 'مقبول'
                              : item.condition === 'POOR' ? 'ضعيف'
                              : item.condition === 'DAMAGED' ? 'تالف'
                              : item.condition
                            : item.condition
                          }
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Progress value={percentage} className="w-20" />
                        <span className="text-sm text-muted-foreground w-8">
                          {item.count}
                        </span>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Recent Assets and Maintenance Due */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Assets */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              {language === 'ar' ? 'الأصول المضافة حديثاً' : 'Recently Added Assets'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {recentAssets.map((asset) => (
                <div key={asset.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex-1">
                    <div className="font-medium">{asset.name}</div>
                    <div className="text-sm text-muted-foreground">
                      {asset.asset_id} • {asset.category_name}
                    </div>
                  </div>
                  <div className="text-right">
                    <Badge variant={asset.status === 'AVAILABLE' ? 'default' : 'secondary'}>
                      {asset.status}
                    </Badge>
                    <div className="text-sm text-muted-foreground mt-1">
                      {formatCurrency(parseFloat(asset.purchase_price))}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Maintenance Due */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              {language === 'ar' ? 'الصيانة المستحقة' : 'Maintenance Due'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {maintenanceDue.length > 0 ? (
                maintenanceDue.map((asset) => (
                  <div key={asset.id} className="flex items-center justify-between p-3 border rounded-lg border-orange-200 bg-orange-50">
                    <div className="flex-1">
                      <div className="font-medium">{asset.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {asset.asset_id} • {asset.location}
                      </div>
                    </div>
                    <div className="text-right">
                      <Badge variant="destructive">
                        {language === 'ar' ? 'مستحقة' : 'Due'}
                      </Badge>
                      <div className="text-sm text-muted-foreground mt-1">
                        {asset.assigned_to_name || (language === 'ar' ? 'غير مخصص' : 'Unassigned')}
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center text-muted-foreground py-4">
                  {language === 'ar' ? 'لا توجد صيانة مستحقة' : 'No maintenance due'}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>
            {language === 'ar' ? 'إجراءات سريعة' : 'Quick Actions'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Button variant="outline" className="h-20 flex-col">
              <Plus className="h-6 w-6 mb-2" />
              <span>{language === 'ar' ? 'إضافة أصل جديد' : 'Add New Asset'}</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <Search className="h-6 w-6 mb-2" />
              <span>{language === 'ar' ? 'البحث في الأصول' : 'Search Assets'}</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <Calendar className="h-6 w-6 mb-2" />
              <span>{language === 'ar' ? 'جدولة الصيانة' : 'Schedule Maintenance'}</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <BarChart3 className="h-6 w-6 mb-2" />
              <span>{language === 'ar' ? 'تقرير الاستهلاك' : 'Depreciation Report'}</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AssetDashboard;
