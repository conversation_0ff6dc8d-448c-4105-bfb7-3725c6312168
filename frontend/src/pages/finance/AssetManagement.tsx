import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  CardContent, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { 
  Package, 
  Plus, 
  Edit, 
  Trash2, 
  Search,
  Filter,
  Download,
  Upload,
  Calendar,
  DollarSign,
  MapPin,
  User,
  Settings,
  TrendingDown
} from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';

interface Asset {
  id: number;
  asset_id: string;
  name: string;
  name_ar: string;
  category: number;
  category_name: string;
  serial_number: string;
  model: string;
  manufacturer: string;
  purchase_date: string;
  purchase_price: string;
  current_book_value: number;
  accumulated_depreciation: number;
  depreciation_method: string;
  useful_life_years: number;
  status: string;
  condition: string;
  assigned_to: number | null;
  assigned_to_name: string;
  department: number | null;
  department_name: string;
  location: string;
  warranty_expiry: string | null;
  is_under_warranty: boolean;
  is_maintenance_due: boolean;
  currency_code: string;
  currency_symbol: string;
  tags: string;
  created_at: string;
}

interface AssetCategory {
  id: number;
  name: string;
  name_ar: string;
}

interface Department {
  id: number;
  name: string;
}

interface Employee {
  id: number;
  user: {
    first_name: string;
    last_name: string;
  };
}

// @ts-ignore
const AssetManagement: (React as any).FC = () => {
  // @ts-ignore
  const { language } = useLanguage( as any);
  const [assets, setAssets] = useState<Asset[]>([]);
  const [categories, setCategories] = useState<AssetCategory[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [loading, setLoading] = useState(true as any);
  const [searchTerm, setSearchTerm] = useState('' as any);
  const [statusFilter, setStatusFilter] = useState('' as any);
  const [categoryFilter, setCategoryFilter] = useState('' as any);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false as any);
  const [selectedAsset, setSelectedAsset] = useState<Asset | null>(null);

  // Form state
  const [assetForm, setAssetForm] = useState({
    name: '',
    name_ar: '',
    category: '',
    serial_number: '',
    model: '',
    manufacturer: '',
    purchase_date: '',
    purchase_price: '',
    salvage_value: '',
    useful_life_years: 5,
    depreciation_method: 'STRAIGHT_LINE',
    assigned_to: '',
    department: '',
    location: '',
    warranty_expiry: '',
    condition: 'GOOD',
    status: 'AVAILABLE',
    tags: '',
    notes: ''
  } as any);

  // @ts-ignore
  useEffect(( as any) => {
    // @ts-ignore
    fetchAssets( as any);
    // @ts-ignore
    fetchCategories( as any);
    // @ts-ignore
    fetchDepartments( as any);
    // @ts-ignore
    fetchEmployees( as any);
  // @ts-ignore
  }, []);

  const fetchAssets: any = async () => {
    try {
      // @ts-ignore
      const params = new URLSearchParams( as any);
      if (searchTerm) (params as any).append('search', searchTerm as any);
      if (statusFilter && statusFilter !== '__all__') (params as any).append('status', statusFilter as any);
      if (categoryFilter && categoryFilter !== '__all__') (params as any).append('category', categoryFilter as any);
      
      // @ts-ignore
      const response: any = await fetch(`/api/assets/?${(params as any as any).toString( as any)}`);
      if ((response as any).ok) {
        // @ts-ignore
        const data: any = await (response as any).json( as any);
        setAssets((data as any as any).results || data);
      }
    } catch (error) {
      (console as any).error('Error fetching assets:', error as any);
    } finally {
      setLoading(false as any);
    }
  };

  const fetchCategories: any = async () => {
    try {
      const response = await fetch('/api/asset-categories/' as any);
      if ((response as any).ok) {
        // @ts-ignore
        const data: any = await (response as any).json( as any);
        setCategories((data as any as any).results || data);
      }
    } catch (error) {
      (console as any).error('Error fetching categories:', error as any);
    }
  };

  const fetchDepartments: any = async () => {
    try {
      const response = await fetch('/api/departments/' as any);
      if ((response as any).ok) {
        // @ts-ignore
        const data: any = await (response as any).json( as any);
        setDepartments((data as any as any).results || data);
      }
    } catch (error) {
      (console as any).error('Error fetching departments:', error as any);
    }
  };

  const fetchEmployees: any = async () => {
    try {
      const response = await fetch('/api/employees/' as any);
      if ((response as any).ok) {
        // @ts-ignore
        const data: any = await (response as any).json( as any);
        setEmployees((data as any as any).results || data);
      }
    } catch (error) {
      (console as any).error('Error fetching employees:', error as any);
    }
  };

  const handleAddAsset: any = async () => {
    try {
      const response = await fetch('/api/assets/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: (JSON as any as any).stringify({
          ...assetForm,
          category: parseInt((assetForm as any as any).category),
          assigned_to: (assetForm as any).assigned_to ? parseInt((assetForm as any as any).assigned_to) : null,
          department: (assetForm as any).department ? parseInt((assetForm as any as any).department) : null,
          // @ts-ignore
          useful_life_years: parseInt((assetForm as any as any).useful_life_years.toString( as any)),
        }),
      });

      if ((response as any).ok) {
        // @ts-ignore
        fetchAssets( as any);
        setIsAddDialogOpen(false as any);
        // @ts-ignore
        resetForm( as any);
      }
    } catch (error) {
      (console as any).error('Error adding asset:', error as any);
    }
  };

  const resetForm: any = (): void => {
    setAssetForm({
      name: '',
      name_ar: '',
      category: '',
      serial_number: '',
      model: '',
      manufacturer: '',
      purchase_date: '',
      purchase_price: '',
      salvage_value: '',
      useful_life_years: 5,
      depreciation_method: 'STRAIGHT_LINE',
      assigned_to: '',
      department: '',
      location: '',
      warranty_expiry: '',
      condition: 'GOOD',
      status: 'AVAILABLE',
      tags: '',
      notes: ''
    } as any);
  };

  const getStatusColor: any = (status: string): void => {
    const colors = {
      'AVAILABLE': 'bg-green-100 text-green-800',
      'IN_USE': 'bg-blue-100 text-blue-800',
      'MAINTENANCE': 'bg-yellow-100 text-yellow-800',
      'RETIRED': 'bg-gray-100 text-gray-800',
      'LOST': 'bg-red-100 text-red-800',
      'DAMAGED': 'bg-red-100 text-red-800',
      'DISPOSED': 'bg-gray-100 text-gray-800'
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const getConditionColor: any = (condition: string): void => {
    const colors = {
      'EXCELLENT': 'text-green-600',
      'GOOD': 'text-blue-600',
      'FAIR': 'text-yellow-600',
      'POOR': 'text-orange-600',
      'DAMAGED': 'text-red-600'
    };
    return colors[condition as keyof typeof colors] || 'text-gray-600';
  };

  const formatCurrency: any = (amount: number, symbol: string = '﷼'): string => {
    // @ts-ignore
    return `${(amount as any).toLocaleString( as any)} ${symbol}`;
  };

  const filteredAssets: any = (assets as any).filter(asset => {
    const matchesSearch = !searchTerm || 
      // @ts-ignore
      (asset as any as any).name.toLowerCase( as any).includes((searchTerm as any as any).toLowerCase( as any)) ||
      // @ts-ignore
      (asset as any).asset_id.toLowerCase( as any).includes((searchTerm as any as any).toLowerCase( as any)) ||
      // @ts-ignore
      (asset as any).serial_number.toLowerCase( as any).includes((searchTerm as any as any).toLowerCase( as any));
    
    const matchesStatus: any = !statusFilter || statusFilter === '__all__' || (asset as any).status === statusFilter;
    // @ts-ignore
    const matchesCategory: any = !categoryFilter || categoryFilter === '__all__' || (asset as any).category.toString( as any) === categoryFilter;
    
    return matchesSearch && matchesStatus && matchesCategory;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">
          {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">
            {language === 'ar' ? 'إدارة الأصول' : 'Asset Management'}
          </h1>
          <p className="text-muted-foreground">
            {language === 'ar' 
              ? 'إدارة وتتبع أصول الشركة والاستهلاك والصيانة'
              : 'Manage and track company assets, depreciation, and maintenance'
            }
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            {language === 'ar' ? 'تصدير' : 'Export'}
          </Button>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                {language === 'ar' ? 'إضافة أصل' : 'Add Asset'}
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>
                  {language === 'ar' ? 'إضافة أصل جديد' : 'Add New Asset'}
                </DialogTitle>
              </DialogHeader>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label>{language === 'ar' ? 'اسم الأصل' : 'Asset Name'}</Label>
                  <Input
                    value={(assetForm as any).name}
                    onChange={(e: any) => setAssetForm({...assetForm, name: (e as any as any).target.value})}
                    placeholder={language === 'ar' ? 'أدخل اسم الأصل' : 'Enter asset name'}
                  />
                </div>
                <div>
                  <Label>{language === 'ar' ? 'الاسم بالعربية' : 'Arabic Name'}</Label>
                  <Input
                    value={(assetForm as any).name_ar}
                    onChange={(e: any) => setAssetForm({...assetForm, name_ar: (e as any as any).target.value})}
                    placeholder={language === 'ar' ? 'أدخل الاسم بالعربية' : 'Enter Arabic name'}
                  />
                </div>
                <div>
                  <Label>{language === 'ar' ? 'الفئة' : 'Category'}</Label>
                  <Select value={(assetForm as any).category} onValueChange={(value) => setAssetForm({...assetForm, category: value} as any)}>
                    <SelectTrigger>
                      <SelectValue placeholder={language === 'ar' ? 'اختر الفئة' : 'Select category'} />
                    </SelectTrigger>
                    <SelectContent>
                      {(categories as any).map(category => (
                        // @ts-ignore
                        <SelectItem key={(category as any as any).id} value={(category as any).id.toString( as any)}>
                          {language === 'ar' ? (category as any).name_ar : (category as any).name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label>{language === 'ar' ? 'الرقم التسلسلي' : 'Serial Number'}</Label>
                  <Input
                    value={(assetForm as any).serial_number}
                    onChange={(e: any) => setAssetForm({...assetForm, serial_number: (e as any as any).target.value})}
                    placeholder={language === 'ar' ? 'أدخل الرقم التسلسلي' : 'Enter serial number'}
                  />
                </div>
                <div>
                  <Label>{language === 'ar' ? 'الطراز' : 'Model'}</Label>
                  <Input
                    value={(assetForm as any).model}
                    onChange={(e: any) => setAssetForm({...assetForm, model: (e as any as any).target.value})}
                    placeholder={language === 'ar' ? 'أدخل الطراز' : 'Enter model'}
                  />
                </div>
                <div>
                  <Label>{language === 'ar' ? 'الشركة المصنعة' : 'Manufacturer'}</Label>
                  <Input
                    value={(assetForm as any).manufacturer}
                    onChange={(e: any) => setAssetForm({...assetForm, manufacturer: (e as any as any).target.value})}
                    placeholder={language === 'ar' ? 'أدخل الشركة المصنعة' : 'Enter manufacturer'}
                  />
                </div>
                <div>
                  <Label>{language === 'ar' ? 'تاريخ الشراء' : 'Purchase Date'}</Label>
                  <Input
                    type="date"
                    value={(assetForm as any).purchase_date}
                    onChange={(e: any) => setAssetForm({...assetForm, purchase_date: (e as any as any).target.value})}
                  />
                </div>
                <div>
                  <Label>{language === 'ar' ? 'سعر الشراء' : 'Purchase Price'}</Label>
                  <Input
                    type="number"
                    step="(0 as any).01"
                    value={(assetForm as any).purchase_price}
                    onChange={(e: any) => setAssetForm({...assetForm, purchase_price: (e as any as any).target.value})}
                    placeholder={language === 'ar' ? 'أدخل سعر الشراء' : 'Enter purchase price'}
                  />
                </div>
                <div>
                  <Label>{language === 'ar' ? 'العمر الافتراضي (سنوات)' : 'Useful Life (Years)'}</Label>
                  <Input
                    type="number"
                    min="1"
                    value={(assetForm as any).useful_life_years}
                    onChange={(e: any) => setAssetForm({...assetForm, useful_life_years: parseInt((e as any as any).target.value) || 5})}
                  />
                </div>
                <div>
                  <Label>{language === 'ar' ? 'طريقة الاستهلاك' : 'Depreciation Method'}</Label>
                  <Select value={(assetForm as any).depreciation_method} onValueChange={(value) => setAssetForm({...assetForm, depreciation_method: value} as any)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="STRAIGHT_LINE">
                        {language === 'ar' ? 'القسط الثابت' : 'Straight Line'}
                      </SelectItem>
                      <SelectItem value="DECLINING_BALANCE">
                        {language === 'ar' ? 'الرصيد المتناقص' : 'Declining Balance'}
                      </SelectItem>
                      <SelectItem value="NO_DEPRECIATION">
                        {language === 'ar' ? 'بدون استهلاك' : 'No Depreciation'}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label>{language === 'ar' ? 'المخصص له' : 'Assigned To'}</Label>
                  <Select value={(assetForm as any).assigned_to} onValueChange={(value) => setAssetForm({...assetForm, assigned_to: value} as any)}>
                    <SelectTrigger>
                      <SelectValue placeholder={language === 'ar' ? 'اختر الموظف' : 'Select employee'} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">
                        {language === 'ar' ? 'غير مخصص' : 'Unassigned'}
                      </SelectItem>
                      {(employees as any).map(employee => (
                        // @ts-ignore
                        <SelectItem key={(employee as any as any).id} value={(employee as any).id.toString( as any)}>
                          {(employee as any).user.first_name} {(employee as any).user.last_name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label>{language === 'ar' ? 'القسم' : 'Department'}</Label>
                  <Select value={(assetForm as any).department} onValueChange={(value) => setAssetForm({...assetForm, department: value} as any)}>
                    <SelectTrigger>
                      <SelectValue placeholder={language === 'ar' ? 'اختر القسم' : 'Select department'} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">
                        {language === 'ar' ? 'بدون قسم' : 'No department'}
                      </SelectItem>
                      {(departments as any).map(department => (
                        // @ts-ignore
                        <SelectItem key={(department as any as any).id} value={(department as any).id.toString( as any)}>
                          {(department as any).name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="md:col-span-2">
                  <Label>{language === 'ar' ? 'الموقع' : 'Location'}</Label>
                  <Input
                    value={(assetForm as any).location}
                    onChange={(e: any) => setAssetForm({...assetForm, location: (e as any as any).target.value})}
                    placeholder={language === 'ar' ? 'أدخل الموقع' : 'Enter location'}
                  />
                </div>
              </div>
              <div className="flex justify-end gap-2 mt-4">
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false as any)}>
                  {language === 'ar' ? 'إلغاء' : 'Cancel'}
                </Button>
                <Button onClick={handleAddAsset}>
                  {language === 'ar' ? 'إضافة الأصل' : 'Add Asset'}
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-[200px]">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder={language === 'ar' ? 'البحث في الأصول...' : 'Search assets...'}
                  value={searchTerm}
                  onChange={(e: any) => setSearchTerm((e as any as any).target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder={language === 'ar' ? 'جميع الحالات' : 'All statuses'} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="__all__">
                  {language === 'ar' ? 'جميع الحالات' : 'All statuses'}
                </SelectItem>
                <SelectItem value="AVAILABLE">
                  {language === 'ar' ? 'متاح' : 'Available'}
                </SelectItem>
                <SelectItem value="IN_USE">
                  {language === 'ar' ? 'قيد الاستخدام' : 'In Use'}
                </SelectItem>
                <SelectItem value="MAINTENANCE">
                  {language === 'ar' ? 'تحت الصيانة' : 'Maintenance'}
                </SelectItem>
              </SelectContent>
            </Select>
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder={language === 'ar' ? 'جميع الفئات' : 'All categories'} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="__all__">
                  {language === 'ar' ? 'جميع الفئات' : 'All categories'}
                </SelectItem>
                {(categories as any).map(category => (
                  // @ts-ignore
                  <SelectItem key={(category as any as any).id} value={(category as any).id.toString( as any)}>
                    {language === 'ar' ? (category as any).name_ar : (category as any).name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Assets Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            {language === 'ar' ? 'قائمة الأصول' : 'Assets List'}
            <Badge variant="secondary" className="ml-2">
              {(filteredAssets as any).length}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{language === 'ar' ? 'رقم الأصل' : 'Asset ID'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الاسم' : 'Name'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الفئة' : 'Category'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الحالة' : 'Status'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الحالة الفنية' : 'Condition'}</TableHead>
                  <TableHead>{language === 'ar' ? 'القيمة الحالية' : 'Book Value'}</TableHead>
                  <TableHead>{language === 'ar' ? 'المخصص له' : 'Assigned To'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الإجراءات' : 'Actions'}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                // @ts-ignore
                {(filteredAssets as any).map((asset as any) => (
                  <TableRow key={(asset as any).id}>
                    <TableCell className="font-medium">{(asset as any).asset_id}</TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{(asset as any).name}</div>
                        <div className="text-sm text-muted-foreground">
                          {(asset as any).model} • {(asset as any).manufacturer}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>{(asset as any).category_name}</TableCell>
                    <TableCell>
                      <Badge className={getStatusColor((asset as any as any).status)}>
                        {language === 'ar' 
                          ? (asset as any).status === 'AVAILABLE' ? 'متاح' 
                            : (asset as any).status === 'IN_USE' ? 'قيد الاستخدام'
                            : (asset as any).status === 'MAINTENANCE' ? 'تحت الصيانة'
                            : (asset as any).status
                          : (asset as any).status.replace('_', ' ' as any)
                        }
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <span className={getConditionColor((asset as any as any).condition)}>
                        {language === 'ar' 
                          ? (asset as any).condition === 'EXCELLENT' ? 'ممتاز' 
                            : (asset as any).condition === 'GOOD' ? 'جيد'
                            : (asset as any).condition === 'FAIR' ? 'مقبول'
                            : (asset as any).condition
                          : (asset as any).condition
                        }
                      </span>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">
                          {formatCurrency((asset as any as any).current_book_value, (asset as any).currency_symbol)}
                        </div>
                        <div className="text-sm text-muted-foreground flex items-center">
                          <TrendingDown className="h-3 w-3 mr-1" />
                          {formatCurrency((asset as any as any).accumulated_depreciation, (asset as any).currency_symbol)}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">
                          {(asset as any).assigned_to_name || (language === 'ar' ? 'غير مخصص' : 'Unassigned')}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {(asset as any).location}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <Calendar className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                // @ts-ignore
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
// @ts-ignore
};

export default AssetManagement;
