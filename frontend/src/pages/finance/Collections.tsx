/**
 * Collections Page - AR Collections Management
 * Manages overdue invoices, collection activities, and customer follow-ups
 */

import React, { useState, useEffect } from 'react'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  Search, 
  Plus, 
  Phone, 
  Mail,
  Calendar,
  DollarSign,
  Clock,
  AlertTriangle,
  Users,
  TrendingUp,
  FileText,
  MessageSquare,
  CheckCircle,
  XCircle
} from 'lucide-react'

interface CollectionsProps {
  language: 'ar' | 'en'
}

interface CollectionItem {
  id: number
  invoice_number: string
  customer_id: number
  customer_name: string
  customer_email: string
  customer_phone: string
  total_amount: number
  remaining_balance: number
  days_overdue: number
  due_date: string
  last_contact_date?: string
  last_contact_method?: string
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'
  collection_status: 'NEW' | 'CONTACTED' | 'PROMISED' | 'LEGAL' | 'WRITTEN_OFF'
  next_follow_up_date?: string
  notes?: string
  payment_plan?: boolean
}

interface CollectionActivity {
  id: number
  invoice_id: number
  activity_type: 'CALL' | 'EMAIL' | 'LETTER' | 'MEETING' | 'PAYMENT'
  activity_date: string
  description: string
  result: string
  created_by: string
}

const translations = {
  ar: {
    collections: 'التحصيلات',
    overdueInvoices: 'الفواتير المتأخرة',
    collectionActivities: 'أنشطة التحصيل',
    addActivity: 'إضافة نشاط',
    searchCollections: 'البحث في التحصيلات...',
    invoiceNumber: 'رقم الفاتورة',
    customer: 'العميل',
    amount: 'المبلغ',
    daysOverdue: 'الأيام المتأخرة',
    priority: 'الأولوية',
    status: 'الحالة',
    lastContact: 'آخر اتصال',
    nextFollowUp: 'المتابعة التالية',
    actions: 'الإجراءات',
    // Priority levels
    low: 'منخفض',
    medium: 'متوسط',
    high: 'عالي',
    urgent: 'عاجل',
    // Collection status
    new: 'جديد',
    contacted: 'تم الاتصال',
    promised: 'وعد بالدفع',
    legal: 'إجراء قانوني',
    writtenOff: 'شطب',
    // Activity types
    call: 'مكالمة',
    email: 'بريد إلكتروني',
    letter: 'خطاب',
    meeting: 'اجتماع',
    payment: 'دفعة',
    // Summary cards
    totalOverdue: 'إجمالي المتأخر',
    overdueCount: 'عدد المتأخرة',
    averageDaysOverdue: 'متوسط الأيام المتأخرة',
    collectionRate: 'معدل التحصيل',
    urgentCases: 'الحالات العاجلة',
    thisWeekTarget: 'هدف هذا الأسبوع',
    noCollectionsFound: 'لم يتم العثور على تحصيلات',
    allPriorities: 'جميع الأولويات',
    allStatuses: 'جميع الحالات',
    contactCustomer: 'اتصال بالعميل',
    sendEmail: 'إرسال بريد',
    scheduleFollowUp: 'جدولة متابعة',
    addNote: 'إضافة ملاحظة',
    paymentPlan: 'خطة دفع'
  },
  en: {
    collections: 'Collections',
    overdueInvoices: 'Overdue Invoices',
    collectionActivities: 'Collection Activities',
    addActivity: 'Add Activity',
    searchCollections: 'Search collections...',
    invoiceNumber: 'Invoice Number',
    customer: 'Customer',
    amount: 'Amount',
    daysOverdue: 'Days Overdue',
    priority: 'Priority',
    status: 'Status',
    lastContact: 'Last Contact',
    nextFollowUp: 'Next Follow-up',
    actions: 'Actions',
    // Priority levels
    low: 'Low',
    medium: 'Medium',
    high: 'High',
    urgent: 'Urgent',
    // Collection status
    new: 'New',
    contacted: 'Contacted',
    promised: 'Promised',
    legal: 'Legal',
    writtenOff: 'Written Off',
    // Activity types
    call: 'Call',
    email: 'Email',
    letter: 'Letter',
    meeting: 'Meeting',
    payment: 'Payment',
    // Summary cards
    totalOverdue: 'Total Overdue',
    overdueCount: 'Overdue Count',
    averageDaysOverdue: 'Average Days Overdue',
    collectionRate: 'Collection Rate',
    urgentCases: 'Urgent Cases',
    thisWeekTarget: 'This Week Target',
    noCollectionsFound: 'No collections found',
    allPriorities: 'All Priorities',
    allStatuses: 'All Statuses',
    contactCustomer: 'Contact Customer',
    sendEmail: 'Send Email',
    scheduleFollowUp: 'Schedule Follow-up',
    addNote: 'Add Note',
    paymentPlan: 'Payment Plan'
  }
}

// @ts-ignore
const Collections: (React as any).FC<CollectionsProps> = ({ language }) => {
  // @ts-ignore
  const t = translations[language]
  // @ts-ignore
  const [collections, setCollections] = useState<CollectionItem[]>([])
  // @ts-ignore
  const [activities, setActivities] = useState<CollectionActivity[]>([])
  const [loading, setLoading] = useState(true as any)
  const [searchTerm, setSearchTerm] = useState('' as any)
  // @ts-ignore
  const [priorityFilter, setPriorityFilter] = useState<string>('all')
  // @ts-ignore
  const [statusFilter, setStatusFilter] = useState<string>('all')

  // @ts-ignore
  useEffect(( as any) => {
    // @ts-ignore
    fetchCollections( as any)
    // @ts-ignore
    fetchActivities( as any)
  // @ts-ignore
  }, [])

  // @ts-ignore
  const fetchCollections = async () => {
    // @ts-ignore
    try {
      setLoading(true as any)
      // @ts-ignore
      const token = (localStorage as any).getItem('token' as any)
      const response = await fetch('/api/customer-invoices/overdue/', {
        // @ts-ignore
        headers: {
          // @ts-ignore
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        // @ts-ignore
        },
      // @ts-ignore
      } as any)
      
      if ((response as any).ok) {
        // @ts-ignore
        const data = await (response as any).json( as any)
        setCollections((data as any as any).results || data)
      // @ts-ignore
      }
    // @ts-ignore
    } catch (error) {
      (console as any).error('Error fetching collections:', error as any)
    } finally {
      setLoading(false as any)
    }
  // @ts-ignore
  }

  // @ts-ignore
  const fetchActivities = async () => {
    // @ts-ignore
    try {
      // @ts-ignore
      const token = (localStorage as any).getItem('token' as any)
      const response = await fetch('/api/collection-activities/', {
        // @ts-ignore
        headers: {
          // @ts-ignore
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        // @ts-ignore
        },
      // @ts-ignore
      } as any)
      
      if ((response as any).ok) {
        // @ts-ignore
        const data = await (response as any).json( as any)
        setActivities((data as any as any).results || data)
      // @ts-ignore
      }
    // @ts-ignore
    } catch (error) {
      (console as any).error('Error fetching collection activities:', error as any)
    }
  // @ts-ignore
  }

  // @ts-ignore
  const filteredCollections = (collections as any).filter(item => {
    // @ts-ignore
    const matchesSearch = 
      (item as any as any).invoice_number.toLowerCase( as any).includes((searchTerm as any as any).toLowerCase( as any)) ||
      (item as any).customer_name.toLowerCase( as any).includes((searchTerm as any as any).toLowerCase( as any)) ||
      (item as any).customer_email.toLowerCase( as any).includes((searchTerm as any as any).toLowerCase( as any))
    
    const matchesPriority = priorityFilter === 'all' || (item as any).priority === priorityFilter
    const matchesStatus = statusFilter === 'all' || (item as any).collection_status === statusFilter
    
    return matchesSearch && matchesPriority && matchesStatus
  // @ts-ignore
  })

  // @ts-ignore
  const formatCurrency = (amount: number): string => {
    // @ts-ignore
    return new (Intl as any).NumberFormat('ar-SA', {
      // @ts-ignore
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 2,
    // @ts-ignore
    } as any).format(amount as any)
  // @ts-ignore
  }

  // @ts-ignore
  const formatDate = (dateString: string): string => {
    // @ts-ignore
    return new Date(dateString as any).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US' as any)
  // @ts-ignore
  }

  // @ts-ignore
  const getPriorityBadge = (priority: string): void => {
    // @ts-ignore
    switch (priority) {
      // @ts-ignore
      case 'LOW':
        return <Badge variant="secondary">{(t as any).low}</Badge>
      case 'MEDIUM':
        return <Badge variant="outline">{(t as any).medium}</Badge>
      case 'HIGH':
        return <Badge variant="default" className="bg-orange-600">{(t as any).high}</Badge>
      case 'URGENT':
        return <Badge variant="destructive">{(t as any).urgent}</Badge>
      default:
        return <Badge variant="secondary">{priority}</Badge>
    // @ts-ignore
    }
  // @ts-ignore
  }

  // @ts-ignore
  const getStatusBadge = (status: string): void => {
    // @ts-ignore
    switch (status) {
      // @ts-ignore
      case 'NEW':
        return <Badge variant="secondary">{(t as any).new}</Badge>
      case 'CONTACTED':
        return <Badge variant="outline">{(t as any).contacted}</Badge>
      case 'PROMISED':
        return <Badge variant="default" className="bg-yellow-600">{(t as any).promised}</Badge>
      case 'LEGAL':
        return <Badge variant="destructive">{(t as any).legal}</Badge>
      case 'WRITTEN_OFF':
        return <Badge variant="secondary" className="bg-gray-600">{(t as any).writtenOff}</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    // @ts-ignore
    }
  // @ts-ignore
  }

  // @ts-ignore
  const getDaysOverdueColor = (days: number): void => {
    // @ts-ignore
    if (days <= 30) return 'text-yellow-600'
    // @ts-ignore
    if (days <= 60) return 'text-orange-600'
    return 'text-red-600'
  // @ts-ignore
  }

  // Calculate summary statistics
  // @ts-ignore
  const totalOverdue = (collections as any).reduce((sum, item as any) => sum + (item as any).remaining_balance, 0)
  const overdueCount = (collections as any).length
  // @ts-ignore
  const averageDaysOverdue = (collections as any).length > 0 ? 
    // @ts-ignore
    (collections as any).reduce((sum, item as any) => sum + (item as any).days_overdue, 0) / (collections as any).length : 0
  // @ts-ignore
  const urgentCases = (collections as any).filter(item => (item as any as any).priority === 'URGENT').length

  if (loading) {
    // @ts-ignore
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  // @ts-ignore
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">{(t as any).collections}</h1>
          <p className="text-gray-600 mt-1">
            {language === 'ar' ? 'إدارة التحصيلات والمتابعة مع العملاء' : 'Collections Management & Customer Follow-up'}
          </p>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          {(t as any).addActivity}
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="bg-gradient-to-r from-red-50 to-orange-50 border-red-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{(t as any).totalOverdue}</p>
                <p className="text-2xl font-bold mt-1 text-red-600">{formatCurrency(totalOverdue as any)}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{(t as any).overdueCount}</p>
                <p className="text-2xl font-bold mt-1">{overdueCount}</p>
              </div>
              <FileText className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{(t as any).averageDaysOverdue}</p>
                <p className="text-2xl font-bold mt-1">{(Math as any).round(averageDaysOverdue as any)}</p>
              </div>
              <Clock className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{(t as any).urgentCases}</p>
                <p className="text-2xl font-bold mt-1 text-red-600">{urgentCases}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder={(t as any).searchCollections}
                  value={searchTerm}
                  onChange={(e: any) => setSearchTerm((e as any as any).target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="sm:w-48">
              <select
                value={priorityFilter}
                onChange={(e: any) => setPriorityFilter((e as any as any).target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">{(t as any).allPriorities}</option>
                <option value="LOW">{(t as any).low}</option>
                <option value="MEDIUM">{(t as any).medium}</option>
                <option value="HIGH">{(t as any).high}</option>
                <option value="URGENT">{(t as any).urgent}</option>
              </select>
            </div>
            <div className="sm:w-48">
              <select
                value={statusFilter}
                onChange={(e: any) => setStatusFilter((e as any as any).target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">{(t as any).allStatuses}</option>
                <option value="NEW">{(t as any).new}</option>
                <option value="CONTACTED">{(t as any).contacted}</option>
                <option value="PROMISED">{(t as any).promised}</option>
                <option value="LEGAL">{(t as any).legal}</option>
                <option value="WRITTEN_OFF">{(t as any).writtenOff}</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Collections Table */}
      <Card>
        <CardHeader>
          <CardTitle>{(t as any).overdueInvoices}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-4 font-medium text-gray-900">{(t as any).invoiceNumber}</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">{(t as any).customer}</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900">{(t as any).amount}</th>
                  <th className="text-center py-3 px-4 font-medium text-gray-900">{(t as any).daysOverdue}</th>
                  <th className="text-center py-3 px-4 font-medium text-gray-900">{(t as any).priority}</th>
                  <th className="text-center py-3 px-4 font-medium text-gray-900">{(t as any).status}</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">{(t as any).lastContact}</th>
                  <th className="text-center py-3 px-4 font-medium text-gray-900">{(t as any).actions}</th>
                </tr>
              </thead>
              <tbody>
                // @ts-ignore
                {(filteredCollections as any).map((item as any) => (
                  <tr key={(item as any).id} className="border-b hover:bg-gray-50">
                    <td className="py-3 px-4">
                      <code className="bg-gray-100 px-2 py-1 rounded text-sm">
                        {(item as any).invoice_number}
                      </code>
                    </td>
                    <td className="py-3 px-4">
                      <div>
                        <span className="font-medium">{(item as any).customer_name}</span>
                        <div className="flex items-center gap-2 mt-1">
                          <Mail className="h-3 w-3 text-gray-400" />
                          <span className="text-xs text-gray-500">{(item as any).customer_email}</span>
                        </div>
                        {(item as any).customer_phone && (
                          <div className="flex items-center gap-2">
                            <Phone className="h-3 w-3 text-gray-400" />
                            <span className="text-xs text-gray-500">{(item as any).customer_phone}</span>
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="py-3 px-4 text-right font-mono">
                      {formatCurrency((item as any as any).remaining_balance)}
                    </td>
                    <td className="py-3 px-4 text-center">
                      <span className={`font-bold ${getDaysOverdueColor((item as any as any).days_overdue)}`}>
                        {(item as any).days_overdue}
                      </span>
                    </td>
                    <td className="py-3 px-4 text-center">
                      {getPriorityBadge((item as any as any).priority)}
                    </td>
                    <td className="py-3 px-4 text-center">
                      {getStatusBadge((item as any as any).collection_status)}
                    </td>
                    <td className="py-3 px-4">
                      {(item as any).last_contact_date ? (
                        <div>
                          <span className="text-sm">{formatDate((item as any as any).last_contact_date)}</span>
                          <p className="text-xs text-gray-500">{(item as any).last_contact_method}</p>
                        </div>
                      ) : (
                        <span className="text-gray-400 text-sm">
                          {language === 'ar' ? 'لا يوجد' : 'None'}
                        </span>
                      )}
                    </td>
                    <td className="py-3 px-4 text-center">
                      <div className="flex items-center justify-center gap-1">
                        <Button variant="outline" size="sm" title={(t as any).contactCustomer}>
                          <Phone className="h-3 w-3" />
                        </Button>
                        <Button variant="outline" size="sm" title={(t as any).sendEmail}>
                          <Mail className="h-3 w-3" />
                        </Button>
                        <Button variant="outline" size="sm" title={(t as any).addNote}>
                          <MessageSquare className="h-3 w-3" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                // @ts-ignore
                ))}
              </tbody>
            </table>
          </div>
          
          {(filteredCollections as any).length === 0 && (
            <div className="text-center py-8">
              <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">{(t as any).noCollectionsFound}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Recent Activities */}
      <Card>
        <CardHeader>
          <CardTitle>{(t as any).collectionActivities}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            // @ts-ignore
            {(activities as any).slice(0, 5 as any).map((activity as any) => (
              <div key={(activity as any).id} className="flex items-start gap-4 p-4 bg-gray-50 rounded-lg">
                <div className="flex-shrink-0">
                  {(activity as any).activity_type === 'CALL' && <Phone className="h-5 w-5 text-blue-600" />}
                  {(activity as any).activity_type === 'EMAIL' && <Mail className="h-5 w-5 text-green-600" />}
                  {(activity as any).activity_type === 'LETTER' && <FileText className="h-5 w-5 text-purple-600" />}
                  {(activity as any).activity_type === 'MEETING' && <Users className="h-5 w-5 text-orange-600" />}
                  {(activity as any).activity_type === 'PAYMENT' && <CheckCircle className="h-5 w-5 text-green-600" />}
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium">{(activity as any).description}</h4>
                    <span className="text-sm text-gray-500">{formatDate((activity as any as any).activity_date)}</span>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">{(activity as any).result}</p>
                  <p className="text-xs text-gray-500 mt-1">
                    {language === 'ar' ? 'بواسطة' : 'by'} {(activity as any).created_by}
                  </p>
                </div>
              </div>
            // @ts-ignore
            ))}
            
            {(activities as any).length === 0 && (
              <div className="text-center py-8">
                <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">
                  {language === 'ar' ? 'لا توجد أنشطة تحصيل' : 'No collection activities'}
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
// @ts-ignore
}

// @ts-ignore
export default Collections