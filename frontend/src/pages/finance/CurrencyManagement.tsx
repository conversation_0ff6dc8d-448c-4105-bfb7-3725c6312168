import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  CardContent, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Plus, Edit, Star, Globe, TrendingUp } from 'lucide-react';
// Language prop will be passed from parent component

interface Currency {
  id: number;
  code: string;
  name: string;
  symbol: string;
  decimal_places: number;
  is_base_currency: boolean;
  is_active: boolean;
  symbol_position: 'before' | 'after';
  created_at: string;
  updated_at: string;
}

interface ExchangeRate {
  id: number;
  from_currency: number;
  to_currency: number;
  from_currency_code: string;
  to_currency_code: string;
  from_currency_name: string;
  to_currency_name: string;
  rate: string;
  effective_date: string;
  source: string;
  is_active: boolean;
}

interface CurrencyManagementProps {
  language: string;
}

const CurrencyManagement: React.FC<CurrencyManagementProps> = ({ language }) => {
  const [currencies, setCurrencies] = useState<Currency[]>([]);
  const [exchangeRates, setExchangeRates] = useState<ExchangeRate[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState<boolean>(false);
  const [isRateDialogOpen, setIsRateDialogOpen] = useState<boolean>(false);
  const [selectedCurrency, setSelectedCurrency] = useState<Currency | null>(null);

  // Form states
  const [currencyForm, setCurrencyForm] = useState({
    code: '',
    name: '',
    symbol: '',
    decimal_places: 2,
    symbol_position: 'before' as 'before' | 'after',
    is_active: true
  });

  const [rateForm, setRateForm] = useState({
    from_currency: '',
    to_currency: '',
    rate: '',
    effective_date: new Date().toISOString().split('T')[0],
    source: 'manual'
  });

  useEffect(() => {
    fetchCurrencies();
    fetchExchangeRates();
  }, []);

  const fetchCurrencies: any = async () => {
    try {
      const response = await fetch('/api/currencies/');
      if (response.ok) {
        const data: any = await response.json();
        setCurrencies(data);
      }
    } catch (error) {
      console.error('Error fetching currencies:', error);
    }
  };

  const fetchExchangeRates: any = async () => {
    try {
      const response = await fetch('/api/exchange-rates/');
      if (response.ok) {
        const data: any = await response.json();
        setExchangeRates(data.results || data);
      }
    } catch (error) {
      console.error('Error fetching exchange rates:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddCurrency: any = async () => {
    try {
      const response = await fetch('/api/currencies/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(currencyForm),
      });

      if (response.ok) {
        fetchCurrencies();
        setIsAddDialogOpen(false);
        setCurrencyForm({
          code: '',
          name: '',
          symbol: '',
          decimal_places: 2,
          symbol_position: 'before',
          is_active: true
        });
      }
    } catch (error) {
      console.error('Error adding currency:', error);
    }
  };

  const handleAddExchangeRate: any = async () => {
    try {
      const response = await fetch('/api/exchange-rates/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(rateForm),
      });

      if (response.ok) {
        fetchExchangeRates();
        setIsRateDialogOpen(false);
        setRateForm({
          from_currency: '',
          to_currency: '',
          rate: '',
          effective_date: new Date().toISOString().split('T')[0],
          source: 'manual'
        });
      }
    } catch (error) {
      console.error('Error adding exchange rate:', error);
    }
  };

  const setAsBaseCurrency: any = async (currencyId: number) => {
    try {
      const response = await fetch(`/api/currencies/${currencyId}/set_as_base/`, {
        method: 'POST',
      });

      if (response.ok) {
        fetchCurrencies();
      }
    } catch (error) {
      console.error('Error setting base currency:', error);
    }
  };

  const formatCurrency: any = (amount: string, currency: Currency): string => {
    const numAmount = parseFloat(amount);
    const formatted: any = numAmount.toFixed(currency.decimal_places);
    
    if (currency.symbol_position === 'before') {
      return `${currency.symbol}${formatted}`;
    } else {
      return `${formatted} ${currency.symbol}`;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">
          {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">
            {language === 'ar' ? 'إدارة العملات' : 'Currency Management'}
          </h1>
          <p className="text-muted-foreground">
            {language === 'ar' 
              ? 'إدارة العملات وأسعار الصرف للنظام متعدد العملات'
              : 'Manage currencies and exchange rates for multi-currency system'
            }
          </p>
        </div>
        <div className="flex gap-2">
          <Dialog open={isRateDialogOpen} onOpenChange={setIsRateDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline">
                <TrendingUp className="h-4 w-4 mr-2" />
                {language === 'ar' ? 'إضافة سعر صرف' : 'Add Exchange Rate'}
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>
                  {language === 'ar' ? 'إضافة سعر صرف جديد' : 'Add New Exchange Rate'}
                </DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>{language === 'ar' ? 'من العملة' : 'From Currency'}</Label>
                    <Select 
                      value={rateForm.from_currency} 
                      onValueChange={(value) => setRateForm({...rateForm, from_currency: value})}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder={language === 'ar' ? 'اختر العملة' : 'Select currency'} />
                      </SelectTrigger>
                      <SelectContent>
                        {currencies.filter(c => c.is_active).map(currency => (
                          <SelectItem key={currency.id} value={currency.id.toString()}>
                            {currency.code} - {currency.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label>{language === 'ar' ? 'إلى العملة' : 'To Currency'}</Label>
                    <Select 
                      value={rateForm.to_currency} 
                      onValueChange={(value) => setRateForm({...rateForm, to_currency: value})}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder={language === 'ar' ? 'اختر العملة' : 'Select currency'} />
                      </SelectTrigger>
                      <SelectContent>
                        {currencies.filter(c => c.is_active).map(currency => (
                          <SelectItem key={currency.id} value={currency.id.toString()}>
                            {currency.code} - {currency.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div>
                  <Label>{language === 'ar' ? 'سعر الصرف' : 'Exchange Rate'}</Label>
                  <Input
                    type="number"
                    step="0.000001"
                    value={rateForm.rate}
                    onChange={(e: any) => setRateForm({...rateForm, rate: e.target.value})}
                    placeholder="1.0000"
                  />
                </div>
                <div>
                  <Label>{language === 'ar' ? 'تاريخ السريان' : 'Effective Date'}</Label>
                  <Input
                    type="date"
                    value={rateForm.effective_date}
                    onChange={(e: any) => setRateForm({...rateForm, effective_date: e.target.value})}
                  />
                </div>
                <Button onClick={handleAddExchangeRate} className="w-full">
                  {language === 'ar' ? 'إضافة سعر الصرف' : 'Add Exchange Rate'}
                </Button>
              </div>
            </DialogContent>
          </Dialog>

          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                {language === 'ar' ? 'إضافة عملة' : 'Add Currency'}
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>
                  {language === 'ar' ? 'إضافة عملة جديدة' : 'Add New Currency'}
                </DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label>{language === 'ar' ? 'رمز العملة' : 'Currency Code'}</Label>
                  <Input
                    value={currencyForm.code}
                    onChange={(e: any) => setCurrencyForm({...currencyForm, code: e.target.value.toUpperCase()})}
                    placeholder="USD"
                    maxLength={3}
                  />
                </div>
                <div>
                  <Label>{language === 'ar' ? 'اسم العملة' : 'Currency Name'}</Label>
                  <Input
                    value={currencyForm.name}
                    onChange={(e: any) => setCurrencyForm({...currencyForm, name: e.target.value})}
                    placeholder="US Dollar"
                  />
                </div>
                <div>
                  <Label>{language === 'ar' ? 'رمز العملة' : 'Currency Symbol'}</Label>
                  <Input
                    value={currencyForm.symbol}
                    onChange={(e: any) => setCurrencyForm({...currencyForm, symbol: e.target.value})}
                    placeholder="$"
                  />
                </div>
                <div>
                  <Label>{language === 'ar' ? 'عدد الخانات العشرية' : 'Decimal Places'}</Label>
                  <Input
                    type="number"
                    min="0"
                    max="4"
                    value={currencyForm.decimal_places}
                    onChange={(e: any) => setCurrencyForm({...currencyForm, decimal_places: parseInt(e.target.value)})}
                  />
                </div>
                <div>
                  <Label>{language === 'ar' ? 'موضع الرمز' : 'Symbol Position'}</Label>
                  <Select 
                    value={currencyForm.symbol_position} 
                    onValueChange={(value: 'before' | 'after') => setCurrencyForm({...currencyForm, symbol_position: value})}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="before">
                        {language === 'ar' ? 'قبل المبلغ ($100)' : 'Before amount ($100)'}
                      </SelectItem>
                      <SelectItem value="after">
                        {language === 'ar' ? 'بعد المبلغ (100$)' : 'After amount (100$)'}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={currencyForm.is_active}
                    onCheckedChange={(checked) => setCurrencyForm({...currencyForm, is_active: checked})}
                  />
                  <Label>{language === 'ar' ? 'نشط' : 'Active'}</Label>
                </div>
                <Button onClick={handleAddCurrency} className="w-full">
                  {language === 'ar' ? 'إضافة العملة' : 'Add Currency'}
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Currencies Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            {language === 'ar' ? 'العملات المتاحة' : 'Available Currencies'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{language === 'ar' ? 'الرمز' : 'Code'}</TableHead>
                <TableHead>{language === 'ar' ? 'الاسم' : 'Name'}</TableHead>
                <TableHead>{language === 'ar' ? 'الرمز' : 'Symbol'}</TableHead>
                <TableHead>{language === 'ar' ? 'الخانات العشرية' : 'Decimal Places'}</TableHead>
                <TableHead>{language === 'ar' ? 'الحالة' : 'Status'}</TableHead>
                <TableHead>{language === 'ar' ? 'الإجراءات' : 'Actions'}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {currencies.map((currency) => (
                <TableRow key={currency.id}>
                  <TableCell className="font-medium">
                    <div className="flex items-center gap-2">
                      {currency.code}
                      {currency.is_base_currency && (
                        <Star className="h-4 w-4 text-yellow-500 fill-current" />
                      )}
                    </div>
                  </TableCell>
                  <TableCell>{currency.name}</TableCell>
                  <TableCell>{currency.symbol}</TableCell>
                  <TableCell>{currency.decimal_places}</TableCell>
                  <TableCell>
                    <div className="flex gap-1">
                      {currency.is_base_currency && (
                        <Badge variant="default">
                          {language === 'ar' ? 'العملة الأساسية' : 'Base Currency'}
                        </Badge>
                      )}
                      <Badge variant={currency.is_active ? "default" : "secondary"}>
                        {currency.is_active 
                          ? (language === 'ar' ? 'نشط' : 'Active')
                          : (language === 'ar' ? 'غير نشط' : 'Inactive')
                        }
                      </Badge>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      {!currency.is_base_currency && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setAsBaseCurrency(currency.id)}
                        >
                          <Star className="h-4 w-4" />
                        </Button>
                      )}
                      <Button variant="outline" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Exchange Rates Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            {language === 'ar' ? 'أسعار الصرف الحالية' : 'Current Exchange Rates'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{language === 'ar' ? 'من' : 'From'}</TableHead>
                <TableHead>{language === 'ar' ? 'إلى' : 'To'}</TableHead>
                <TableHead>{language === 'ar' ? 'السعر' : 'Rate'}</TableHead>
                <TableHead>{language === 'ar' ? 'تاريخ السريان' : 'Effective Date'}</TableHead>
                <TableHead>{language === 'ar' ? 'المصدر' : 'Source'}</TableHead>
                <TableHead>{language === 'ar' ? 'الحالة' : 'Status'}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {exchangeRates.slice(0, 10).map((rate) => (
                <TableRow key={rate.id}>
                  <TableCell className="font-medium">{rate.from_currency_code}</TableCell>
                  <TableCell>{rate.to_currency_code}</TableCell>
                  <TableCell>{parseFloat(rate.rate).toFixed(6)}</TableCell>
                  <TableCell>{new Date(rate.effective_date).toLocaleDateString()}</TableCell>
                  <TableCell>
                    <Badge variant="outline">
                      {rate.source === 'manual' 
                        ? (language === 'ar' ? 'يدوي' : 'Manual')
                        : rate.source
                      }
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant={rate.is_active ? "default" : "secondary"}>
                      {rate.is_active 
                        ? (language === 'ar' ? 'نشط' : 'Active')
                        : (language === 'ar' ? 'غير نشط' : 'Inactive')
                      }
                    </Badge>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

export default CurrencyManagement;
