/**
 * Customer Invoices Page - Accounts Receivable Invoice Management
 * Manages customer invoices with billing workflow and payment tracking
 */

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  Search, 
  Plus, 
  FileText, 
  Calendar,
  DollarSign,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Eye,
  Edit,
  Download,
  Send,
  CreditCard,
  Users
} from 'lucide-react'

interface CustomerInvoicesProps {
  language: 'ar' | 'en'
}

interface CustomerInvoice {
  id: number
  invoice_number: string
  customer: number
  customer_name: string
  customer_email: string
  invoice_date: string
  due_date: string
  description: string
  subtotal: number
  tax_amount: number
  discount_amount: number
  total_amount: number
  paid_amount: number
  remaining_balance: number
  status: 'DRAFT' | 'SENT' | 'PAID' | 'PARTIAL' | 'OVERDUE' | 'CANCELLED'
  department_name?: string
  project_name?: string
  created_by_name: string
  is_overdue: boolean
  days_overdue: number
  created_at: string
  sent_date?: string
  payment_terms: string
}

const translations = {
  ar: {
    customerInvoices: 'فواتير العملاء',
    addInvoice: 'إضافة فاتورة',
    searchInvoices: 'البحث في الفواتير...',
    invoiceNumber: 'رقم الفاتورة',
    customer: 'العميل',
    invoiceDate: 'تاريخ الفاتورة',
    dueDate: 'تاريخ الاستحقاق',
    totalAmount: 'المبلغ الإجمالي',
    paidAmount: 'المبلغ المدفوع',
    remainingBalance: 'الرصيد المتبقي',
    status: 'الحالة',
    actions: 'الإجراءات',
    view: 'عرض',
    edit: 'تعديل',
    send: 'إرسال',
    download: 'تحميل',
    // Status translations
    draft: 'مسودة',
    sent: 'مرسل',
    paid: 'مدفوع',
    partial: 'مدفوع جزئياً',
    overdue: 'متأخر',
    cancelled: 'ملغي',
    // Summary cards
    totalInvoices: 'إجمالي الفواتير',
    sentInvoices: 'الفواتير المرسلة',
    overdueInvoices: 'الفواتير المتأخرة',
    totalOutstanding: 'إجمالي المستحق',
    averagePaymentTime: 'متوسط وقت الدفع',
    collectionRate: 'معدل التحصيل',
    noInvoicesFound: 'لم يتم العثور على فواتير',
    allStatuses: 'جميع الحالات',
    daysOverdue: 'يوم متأخر',
    paymentTerms: 'شروط الدفع',
    sentDate: 'تاريخ الإرسال'
  },
  en: {
    customerInvoices: 'Customer Invoices',
    addInvoice: 'Add Invoice',
    searchInvoices: 'Search invoices...',
    invoiceNumber: 'Invoice Number',
    customer: 'Customer',
    invoiceDate: 'Invoice Date',
    dueDate: 'Due Date',
    totalAmount: 'Total Amount',
    paidAmount: 'Paid Amount',
    remainingBalance: 'Remaining Balance',
    status: 'Status',
    actions: 'Actions',
    view: 'View',
    edit: 'Edit',
    send: 'Send',
    download: 'Download',
    // Status translations
    draft: 'Draft',
    sent: 'Sent',
    paid: 'Paid',
    partial: 'Partial',
    overdue: 'Overdue',
    cancelled: 'Cancelled',
    // Summary cards
    totalInvoices: 'Total Invoices',
    sentInvoices: 'Sent Invoices',
    overdueInvoices: 'Overdue Invoices',
    totalOutstanding: 'Total Outstanding',
    averagePaymentTime: 'Average Payment Time',
    collectionRate: 'Collection Rate',
    noInvoicesFound: 'No invoices found',
    allStatuses: 'All Statuses',
    daysOverdue: 'days overdue',
    paymentTerms: 'Payment Terms',
    sentDate: 'Sent Date'
  }
}

const CustomerInvoices: React.FC<CustomerInvoicesProps> = ({ language }) => {
  const t = translations[language]
  const [invoices, setInvoices] = useState<CustomerInvoice[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')

  useEffect(() => {
    fetchInvoices()
  }, [])

  const fetchInvoices = async () => {
    try {
      setLoading(true)
      const token = localStorage.getItem('token')
      const response = await fetch('/api/customer-invoices/', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      })
      
      if (response.ok) {
        const data = await response.json()
        setInvoices(data.results || data)
      }
    } catch (error) {
      console.error('Error fetching customer invoices:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSendInvoice = async (invoiceId: number) => {
    try {
      const token = localStorage.getItem('token')
      const response = await fetch(`/api/customer-invoices/${invoiceId}/send/`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      })
      
      if (response.ok) {
        fetchInvoices() // Refresh the list
      }
    } catch (error) {
      console.error('Error sending invoice:', error)
    }
  }

  const filteredInvoices = invoices.filter(invoice => {
    const matchesSearch = 
      invoice.invoice_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.customer_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.customer_email.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesStatus = statusFilter === 'all' || invoice.status === statusFilter
    
    return matchesSearch && matchesStatus
  })

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 2,
    }).format(amount)
  }

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')
  }

  const getStatusBadge = (status: string, isOverdue: boolean): void => {
    if (isOverdue && status !== 'PAID') {
      return <Badge variant="destructive">{t.overdue}</Badge>
    }
    
    switch (status) {
      case 'DRAFT':
        return <Badge variant="secondary">{t.draft}</Badge>
      case 'SENT':
        return <Badge variant="outline">{t.sent}</Badge>
      case 'PAID':
        return <Badge variant="default" className="bg-green-600">{t.paid}</Badge>
      case 'PARTIAL':
        return <Badge variant="default" className="bg-yellow-600">{t.partial}</Badge>
      case 'OVERDUE':
        return <Badge variant="destructive">{t.overdue}</Badge>
      case 'CANCELLED':
        return <Badge variant="destructive">{t.cancelled}</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const getPaymentProgress = (paidAmount: number, totalAmount: number): void => {
    const percentage = totalAmount > 0 ? (paidAmount / totalAmount) * 100 : 0
    return Math.round(percentage)
  }

  // Calculate summary statistics
  const totalInvoices = invoices.length
  const sentInvoices = invoices.filter(inv => ['SENT', 'PAID', 'PARTIAL', 'OVERDUE'].includes(inv.status)).length
  const overdueInvoices = invoices.filter(inv => inv.is_overdue).length
  const totalOutstanding = invoices
    .filter(inv => inv.status !== 'PAID' && inv.status !== 'CANCELLED')
    .reduce((sum, inv) => sum + inv.remaining_balance, 0)
  
  // Calculate collection rate
  const totalBilled = invoices.reduce((sum, inv) => sum + inv.total_amount, 0)
  const totalCollected = invoices.reduce((sum, inv) => sum + inv.paid_amount, 0)
  const collectionRate = totalBilled > 0 ? (totalCollected / totalBilled) * 100 : 0

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">{t.customerInvoices}</h1>
          <p className="text-gray-600 mt-1">
            {language === 'ar' ? 'إدارة فواتير العملاء والحسابات المدينة' : 'Customer Invoice Management & Accounts Receivable'}
          </p>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          {t.addInvoice}
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{t.totalInvoices}</p>
                <p className="text-2xl font-bold mt-1">{totalInvoices}</p>
              </div>
              <FileText className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{t.sentInvoices}</p>
                <p className="text-2xl font-bold mt-1">{sentInvoices}</p>
              </div>
              <Send className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{t.overdueInvoices}</p>
                <p className="text-2xl font-bold mt-1">{overdueInvoices}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{t.collectionRate}</p>
                <p className="text-2xl font-bold mt-1">{collectionRate.toFixed(1)}%</p>
              </div>
              <CreditCard className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Outstanding Amount Card */}
      <Card className="bg-gradient-to-r from-green-50 to-blue-50 border-green-200">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">{t.totalOutstanding}</h3>
              <p className="text-3xl font-bold text-green-600 mt-2">{formatCurrency(totalOutstanding)}</p>
              <p className="text-sm text-gray-600 mt-1">
                {language === 'ar' ? 'المبلغ المستحق من العملاء' : 'Amount due from customers'}
              </p>
            </div>
            <div className="text-right">
              <div className="bg-white rounded-lg p-4 shadow-sm">
                <p className="text-sm text-gray-600">{t.averagePaymentTime}</p>
                <p className="text-xl font-bold text-blue-600">28 {language === 'ar' ? 'يوم' : 'days'}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder={t.searchInvoices}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="sm:w-48">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">{t.allStatuses}</option>
                <option value="DRAFT">{t.draft}</option>
                <option value="SENT">{t.sent}</option>
                <option value="PAID">{t.paid}</option>
                <option value="PARTIAL">{t.partial}</option>
                <option value="OVERDUE">{t.overdue}</option>
                <option value="CANCELLED">{t.cancelled}</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Invoices Table */}
      <Card>
        <CardHeader>
          <CardTitle>{t.customerInvoices}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-4 font-medium text-gray-900">{t.invoiceNumber}</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">{t.customer}</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">{t.invoiceDate}</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">{t.dueDate}</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900">{t.totalAmount}</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900">{t.remainingBalance}</th>
                  <th className="text-center py-3 px-4 font-medium text-gray-900">{t.status}</th>
                  <th className="text-center py-3 px-4 font-medium text-gray-900">{t.actions}</th>
                </tr>
              </thead>
              <tbody>
                {filteredInvoices.map((invoice) => (
                  <tr key={invoice.id} className="border-b hover:bg-gray-50">
                    <td className="py-3 px-4">
                      <div>
                        <code className="bg-gray-100 px-2 py-1 rounded text-sm">
                          {invoice.invoice_number}
                        </code>
                        {invoice.sent_date && (
                          <p className="text-xs text-gray-500 mt-1">
                            {t.sentDate}: {formatDate(invoice.sent_date)}
                          </p>
                        )}
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div>
                        <span className="font-medium">{invoice.customer_name}</span>
                        <p className="text-sm text-gray-500">{invoice.customer_email}</p>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-gray-400" />
                        {formatDate(invoice.invoice_date)}
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-gray-400" />
                        {formatDate(invoice.due_date)}
                        {invoice.is_overdue && (
                          <span className="text-xs text-red-600">
                            ({invoice.days_overdue} {t.daysOverdue})
                          </span>
                        )}
                      </div>
                    </td>
                    <td className="py-3 px-4 text-right font-mono">
                      {formatCurrency(invoice.total_amount)}
                    </td>
                    <td className="py-3 px-4 text-right">
                      <div>
                        <span className="font-mono">{formatCurrency(invoice.remaining_balance)}</span>
                        {invoice.paid_amount > 0 && (
                          <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                            <div 
                              className="bg-green-600 h-2 rounded-full" 
                              style={{ width: `${getPaymentProgress(invoice.paid_amount, invoice.total_amount)}%` }}
                            ></div>
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="py-3 px-4 text-center">
                      {getStatusBadge(invoice.status, invoice.is_overdue)}
                    </td>
                    <td className="py-3 px-4 text-center">
                      <div className="flex items-center justify-center gap-2">
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                        {invoice.status === 'DRAFT' && (
                          <Button 
                            variant="default" 
                            size="sm"
                            onClick={() => handleSendInvoice(invoice.id)}
                            className="bg-blue-600"
                          >
                            <Send className="h-4 w-4" />
                          </Button>
                        )}
                        {invoice.status === 'SENT' && invoice.remaining_balance > 0 && (
                          <Button variant="default" size="sm" className="bg-green-600">
                            <CreditCard className="h-4 w-4" />
                          </Button>
                        )}
                        <Button variant="outline" size="sm">
                          <Download className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          {filteredInvoices.length === 0 && (
            <div className="text-center py-8">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">{t.noInvoicesFound}</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

export default CustomerInvoices