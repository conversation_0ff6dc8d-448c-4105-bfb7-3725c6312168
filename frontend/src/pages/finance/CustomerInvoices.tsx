/**
 * Customer Invoices Page - Accounts Receivable Invoice Management
 * Manages customer invoices with billing workflow and payment tracking
 */

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  Search, 
  Plus, 
  FileText, 
  Calendar,
  DollarSign,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Eye,
  Edit,
  Download,
  Send,
  CreditCard,
  Users
} from 'lucide-react'

interface CustomerInvoicesProps {
  language: 'ar' | 'en'
}

interface CustomerInvoice {
  id: number
  invoice_number: string
  customer: number
  customer_name: string
  customer_email: string
  invoice_date: string
  due_date: string
  description: string
  subtotal: number
  tax_amount: number
  discount_amount: number
  total_amount: number
  paid_amount: number
  remaining_balance: number
  status: 'DRAFT' | 'SENT' | 'PAID' | 'PARTIAL' | 'OVERDUE' | 'CANCELLED'
  department_name?: string
  project_name?: string
  created_by_name: string
  is_overdue: boolean
  days_overdue: number
  created_at: string
  sent_date?: string
  payment_terms: string
}

const translations = {
  ar: {
    customerInvoices: 'فواتير العملاء',
    addInvoice: 'إضافة فاتورة',
    searchInvoices: 'البحث في الفواتير...',
    invoiceNumber: 'رقم الفاتورة',
    customer: 'العميل',
    invoiceDate: 'تاريخ الفاتورة',
    dueDate: 'تاريخ الاستحقاق',
    totalAmount: 'المبلغ الإجمالي',
    paidAmount: 'المبلغ المدفوع',
    remainingBalance: 'الرصيد المتبقي',
    status: 'الحالة',
    actions: 'الإجراءات',
    view: 'عرض',
    edit: 'تعديل',
    send: 'إرسال',
    download: 'تحميل',
    // Status translations
    draft: 'مسودة',
    sent: 'مرسل',
    paid: 'مدفوع',
    partial: 'مدفوع جزئياً',
    overdue: 'متأخر',
    cancelled: 'ملغي',
    // Summary cards
    totalInvoices: 'إجمالي الفواتير',
    sentInvoices: 'الفواتير المرسلة',
    overdueInvoices: 'الفواتير المتأخرة',
    totalOutstanding: 'إجمالي المستحق',
    averagePaymentTime: 'متوسط وقت الدفع',
    collectionRate: 'معدل التحصيل',
    noInvoicesFound: 'لم يتم العثور على فواتير',
    allStatuses: 'جميع الحالات',
    daysOverdue: 'يوم متأخر',
    paymentTerms: 'شروط الدفع',
    sentDate: 'تاريخ الإرسال'
  },
  en: {
    customerInvoices: 'Customer Invoices',
    addInvoice: 'Add Invoice',
    searchInvoices: 'Search invoices...',
    invoiceNumber: 'Invoice Number',
    customer: 'Customer',
    invoiceDate: 'Invoice Date',
    dueDate: 'Due Date',
    totalAmount: 'Total Amount',
    paidAmount: 'Paid Amount',
    remainingBalance: 'Remaining Balance',
    status: 'Status',
    actions: 'Actions',
    view: 'View',
    edit: 'Edit',
    send: 'Send',
    download: 'Download',
    // Status translations
    draft: 'Draft',
    sent: 'Sent',
    paid: 'Paid',
    partial: 'Partial',
    overdue: 'Overdue',
    cancelled: 'Cancelled',
    // Summary cards
    totalInvoices: 'Total Invoices',
    sentInvoices: 'Sent Invoices',
    overdueInvoices: 'Overdue Invoices',
    totalOutstanding: 'Total Outstanding',
    averagePaymentTime: 'Average Payment Time',
    collectionRate: 'Collection Rate',
    noInvoicesFound: 'No invoices found',
    allStatuses: 'All Statuses',
    daysOverdue: 'days overdue',
    paymentTerms: 'Payment Terms',
    sentDate: 'Sent Date'
  }
}

// @ts-ignore
const CustomerInvoices: (React as any).FC<CustomerInvoicesProps> = ({ language }) => {
  // @ts-ignore
  const t = translations[language]
  // @ts-ignore
  const [invoices, setInvoices] = useState<CustomerInvoice[]>([])
  const [loading, setLoading] = useState(true as any)
  const [searchTerm, setSearchTerm] = useState('' as any)
  // @ts-ignore
  const [statusFilter, setStatusFilter] = useState<string>('all')

  // @ts-ignore
  useEffect(( as any) => {
    // @ts-ignore
    fetchInvoices( as any)
  }, [])

  // @ts-ignore
  const fetchInvoices = async () => {
    // @ts-ignore
    try {
      setLoading(true as any)
      // @ts-ignore
      const token = (localStorage as any).getItem('token' as any)
      const response = await fetch('/api/customer-invoices/', {
        // @ts-ignore
        headers: {
          // @ts-ignore
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        // @ts-ignore
        },
      // @ts-ignore
      } as any)
      
      if ((response as any).ok) {
        // @ts-ignore
        const data = await (response as any).json( as any)
        setInvoices((data as any as any).results || data)
      // @ts-ignore
      }
    // @ts-ignore
    } catch (error) {
      (console as any).error('Error fetching customer invoices:', error as any)
    } finally {
      setLoading(false as any)
    }
  // @ts-ignore
  }

  // @ts-ignore
  const handleSendInvoice = async (invoiceId: number) => {
    // @ts-ignore
    try {
      // @ts-ignore
      const token = (localStorage as any).getItem('token' as any)
      const response = await fetch(`/api/customer-invoices/${invoiceId}/send/`, {
        // @ts-ignore
        method: 'POST',
        headers: {
          // @ts-ignore
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        // @ts-ignore
        },
      // @ts-ignore
      } as any)
      
      if ((response as any).ok) {
        // @ts-ignore
        fetchInvoices( as any) // Refresh the list
      }
    // @ts-ignore
    } catch (error) {
      (console as any).error('Error sending invoice:', error as any)
    }
  // @ts-ignore
  }

  // @ts-ignore
  const filteredInvoices = (invoices as any).filter(invoice => {
    // @ts-ignore
    const matchesSearch = 
      (invoice as any as any).invoice_number.toLowerCase( as any).includes((searchTerm as any as any).toLowerCase( as any)) ||
      (invoice as any).customer_name.toLowerCase( as any).includes((searchTerm as any as any).toLowerCase( as any)) ||
      (invoice as any).description.toLowerCase( as any).includes((searchTerm as any as any).toLowerCase( as any)) ||
      (invoice as any).customer_email.toLowerCase( as any).includes((searchTerm as any as any).toLowerCase( as any))
    
    const matchesStatus = statusFilter === 'all' || (invoice as any).status === statusFilter
    
    return matchesSearch && matchesStatus
  // @ts-ignore
  })

  // @ts-ignore
  const formatCurrency = (amount: number): string => {
    // @ts-ignore
    return new (Intl as any).NumberFormat('ar-SA', {
      // @ts-ignore
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 2,
    // @ts-ignore
    } as any).format(amount as any)
  // @ts-ignore
  }

  // @ts-ignore
  const formatDate = (dateString: string): string => {
    // @ts-ignore
    return new Date(dateString as any).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US' as any)
  // @ts-ignore
  }

  // @ts-ignore
  const getStatusBadge = (status: string, isOverdue: boolean): void => {
    // @ts-ignore
    if (isOverdue && status !== 'PAID') {
      // @ts-ignore
      return <Badge variant="destructive">{(t as any).overdue}</Badge>
    // @ts-ignore
    }
    
    switch (status) {
      // @ts-ignore
      case 'DRAFT':
        return <Badge variant="secondary">{(t as any).draft}</Badge>
      case 'SENT':
        return <Badge variant="outline">{(t as any).sent}</Badge>
      case 'PAID':
        return <Badge variant="default" className="bg-green-600">{(t as any).paid}</Badge>
      case 'PARTIAL':
        return <Badge variant="default" className="bg-yellow-600">{(t as any).partial}</Badge>
      case 'OVERDUE':
        return <Badge variant="destructive">{(t as any).overdue}</Badge>
      case 'CANCELLED':
        return <Badge variant="destructive">{(t as any).cancelled}</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    // @ts-ignore
    }
  // @ts-ignore
  }

  // @ts-ignore
  const getPaymentProgress = (paidAmount: number, totalAmount: number): void => {
    // @ts-ignore
    const percentage = totalAmount > 0 ? (paidAmount / totalAmount) * 100 : 0
    return (Math as any).round(percentage as any)
  // @ts-ignore
  }

  // Calculate summary statistics
  const totalInvoices = (invoices as any).length
  // @ts-ignore
  const sentInvoices = (invoices as any).filter(inv => ['SENT', 'PAID', 'PARTIAL', 'OVERDUE'].includes((inv as any as any).status)).length
  // @ts-ignore
  const overdueInvoices = (invoices as any).filter(inv => (inv as any as any).is_overdue).length
  const totalOutstanding = invoices
    // @ts-ignore
    .filter(inv => (inv as any as any).status !== 'PAID' && (inv as any).status !== 'CANCELLED')
    // @ts-ignore
    .reduce((sum, inv as any) => sum + (inv as any).remaining_balance, 0)
  
  // Calculate collection rate
  // @ts-ignore
  const totalBilled = (invoices as any).reduce((sum, inv as any) => sum + (inv as any).total_amount, 0)
  // @ts-ignore
  const totalCollected = (invoices as any).reduce((sum, inv as any) => sum + (inv as any).paid_amount, 0)
  // @ts-ignore
  const collectionRate = totalBilled > 0 ? (totalCollected / totalBilled) * 100 : 0

  if (loading) {
    // @ts-ignore
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  // @ts-ignore
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">{(t as any).customerInvoices}</h1>
          <p className="text-gray-600 mt-1">
            {language === 'ar' ? 'إدارة فواتير العملاء والحسابات المدينة' : 'Customer Invoice Management & Accounts Receivable'}
          </p>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          {(t as any).addInvoice}
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{(t as any).totalInvoices}</p>
                <p className="text-2xl font-bold mt-1">{totalInvoices}</p>
              </div>
              <FileText className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{(t as any).sentInvoices}</p>
                <p className="text-2xl font-bold mt-1">{sentInvoices}</p>
              </div>
              <Send className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{(t as any).overdueInvoices}</p>
                <p className="text-2xl font-bold mt-1">{overdueInvoices}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{(t as any).collectionRate}</p>
                <p className="text-2xl font-bold mt-1">{(collectionRate as any).toFixed(1 as any)}%</p>
              </div>
              <CreditCard className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Outstanding Amount Card */}
      <Card className="bg-gradient-to-r from-green-50 to-blue-50 border-green-200">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">{(t as any).totalOutstanding}</h3>
              <p className="text-3xl font-bold text-green-600 mt-2">{formatCurrency(totalOutstanding as any)}</p>
              <p className="text-sm text-gray-600 mt-1">
                {language === 'ar' ? 'المبلغ المستحق من العملاء' : 'Amount due from customers'}
              </p>
            </div>
            <div className="text-right">
              <div className="bg-white rounded-lg p-4 shadow-sm">
                <p className="text-sm text-gray-600">{(t as any).averagePaymentTime}</p>
                <p className="text-xl font-bold text-blue-600">28 {language === 'ar' ? 'يوم' : 'days'}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder={(t as any).searchInvoices}
                  value={searchTerm}
                  onChange={(e: any) => setSearchTerm((e as any as any).target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="sm:w-48">
              <select
                value={statusFilter}
                onChange={(e: any) => setStatusFilter((e as any as any).target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">{(t as any).allStatuses}</option>
                <option value="DRAFT">{(t as any).draft}</option>
                <option value="SENT">{(t as any).sent}</option>
                <option value="PAID">{(t as any).paid}</option>
                <option value="PARTIAL">{(t as any).partial}</option>
                <option value="OVERDUE">{(t as any).overdue}</option>
                <option value="CANCELLED">{(t as any).cancelled}</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Invoices Table */}
      <Card>
        <CardHeader>
          <CardTitle>{(t as any).customerInvoices}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-4 font-medium text-gray-900">{(t as any).invoiceNumber}</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">{(t as any).customer}</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">{(t as any).invoiceDate}</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">{(t as any).dueDate}</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900">{(t as any).totalAmount}</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900">{(t as any).remainingBalance}</th>
                  <th className="text-center py-3 px-4 font-medium text-gray-900">{(t as any).status}</th>
                  <th className="text-center py-3 px-4 font-medium text-gray-900">{(t as any).actions}</th>
                </tr>
              </thead>
              <tbody>
                // @ts-ignore
                {(filteredInvoices as any).map((invoice as any) => (
                  <tr key={(invoice as any).id} className="border-b hover:bg-gray-50">
                    <td className="py-3 px-4">
                      <div>
                        <code className="bg-gray-100 px-2 py-1 rounded text-sm">
                          {(invoice as any).invoice_number}
                        </code>
                        {(invoice as any).sent_date && (
                          <p className="text-xs text-gray-500 mt-1">
                            {(t as any).sentDate}: {formatDate((invoice as any as any).sent_date)}
                          </p>
                        )}
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div>
                        <span className="font-medium">{(invoice as any).customer_name}</span>
                        <p className="text-sm text-gray-500">{(invoice as any).customer_email}</p>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-gray-400" />
                        {formatDate((invoice as any as any).invoice_date)}
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-gray-400" />
                        {formatDate((invoice as any as any).due_date)}
                        {(invoice as any).is_overdue && (
                          <span className="text-xs text-red-600">
                            ({(invoice as any).days_overdue} {(t as any).daysOverdue})
                          </span>
                        )}
                      </div>
                    </td>
                    <td className="py-3 px-4 text-right font-mono">
                      {formatCurrency((invoice as any as any).total_amount)}
                    </td>
                    <td className="py-3 px-4 text-right">
                      <div>
                        <span className="font-mono">{formatCurrency((invoice as any as any).remaining_balance)}</span>
                        {(invoice as any).paid_amount > 0 && (
                          <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                            <div 
                              className="bg-green-600 h-2 rounded-full" 
                              style={{ width: `${getPaymentProgress((invoice as any as any).paid_amount, (invoice as any).total_amount)}%` }}
                            ></div>
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="py-3 px-4 text-center">
                      {getStatusBadge((invoice as any as any).status, (invoice as any).is_overdue)}
                    </td>
                    <td className="py-3 px-4 text-center">
                      <div className="flex items-center justify-center gap-2">
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                        {(invoice as any).status === 'DRAFT' && (
                          <Button 
                            variant="default" 
                            size="sm"
                            onClick={() => handleSendInvoice((invoice as any as any).id)}
                            className="bg-blue-600"
                          >
                            <Send className="h-4 w-4" />
                          </Button>
                        )}
                        {(invoice as any).status === 'SENT' && (invoice as any).remaining_balance > 0 && (
                          <Button variant="default" size="sm" className="bg-green-600">
                            <CreditCard className="h-4 w-4" />
                          </Button>
                        )}
                        <Button variant="outline" size="sm">
                          <Download className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                // @ts-ignore
                ))}
              </tbody>
            </table>
          </div>
          
          {(filteredInvoices as any).length === 0 && (
            <div className="text-center py-8">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">{(t as any).noInvoicesFound}</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
// @ts-ignore
}

// @ts-ignore
export default CustomerInvoices