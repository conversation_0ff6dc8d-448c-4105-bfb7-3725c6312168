import React, { useState, useEffect } from 'react';
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  TrendingUp, 
  TrendingDown, 
  RefreshCw, 
  Calculator,
  ArrowRightLeft,
  Upload,
  Download
} from 'lucide-react';
// Language prop will be passed from parent component

interface Currency {
  id: number;
  code: string;
  name: string;
  symbol: string;
  is_base_currency: boolean;
  is_active: boolean;
}

interface ExchangeRate {
  from_currency: string;
  to_currency: string;
  rate: number;
  date: string;
}

interface CurrencyConverter {
  fromCurrency: string;
  toCurrency: string;
  amount: string;
  convertedAmount: string;
}

interface ExchangeRateDashboardProps {
  language: string;
}

// @ts-ignore
const ExchangeRateDashboard: (React as any).FC<ExchangeRateDashboardProps> = ({ language }) => {
  // @ts-ignore
  const [currencies, setCurrencies] = useState<Currency[]>([]);
  // @ts-ignore
  const [currentRates, setCurrentRates] = useState<ExchangeRate[]>([]);
  const [loading, setLoading] = useState(true as any);
  const [isConverterOpen, setIsConverterOpen] = useState(false as any);
  const [isBulkUpdateOpen, setIsBulkUpdateOpen] = useState(false as any);
  
  // @ts-ignore
  const [converter, setConverter] = useState<CurrencyConverter>({
    // @ts-ignore
    fromCurrency: '',
    toCurrency: '',
    amount: '1',
    convertedAmount: ''
  // @ts-ignore
  });

  const [bulkRates, setBulkRates] = useState('' as any);

  // @ts-ignore
  useEffect(( as any) => {
    // @ts-ignore
    fetchCurrencies( as any);
    fetchCurrentRates( as any);
  // @ts-ignore
  }, []);

  // @ts-ignore
  const fetchCurrencies: any = async () => {
    // @ts-ignore
    try {
      // @ts-ignore
      const response = await fetch('/api/currencies/active/' as any);
      if ((response as any).ok) {
        // @ts-ignore
        const data: any = await (response as any).json( as any);
        setCurrencies(data as any);
      // @ts-ignore
      }
    // @ts-ignore
    } catch (error) {
      // @ts-ignore
      (console as any).error('Error fetching currencies:', error as any);
    // @ts-ignore
    }
  // @ts-ignore
  };

  // @ts-ignore
  const fetchCurrentRates: any = async () => {
    // @ts-ignore
    try {
      // @ts-ignore
      const response = await fetch('/api/exchange-rates/current_rates/' as any);
      if ((response as any).ok) {
        // @ts-ignore
        const data: any = await (response as any).json( as any);
        setCurrentRates(data as any);
      // @ts-ignore
      }
    // @ts-ignore
    } catch (error) {
      // @ts-ignore
      (console as any).error('Error fetching current rates:', error as any);
    // @ts-ignore
    } finally {
      // @ts-ignore
      setLoading(false as any);
    // @ts-ignore
    }
  // @ts-ignore
  };

  // @ts-ignore
  const convertCurrency: any = async () => {
    // @ts-ignore
    if (!(converter as any).fromCurrency || !(converter as any).toCurrency || !(converter as any).amount) {
      // @ts-ignore
      return;
    // @ts-ignore
    }

    try {
      // @ts-ignore
      const response: any = await fetch(
        `/api/exchange-rates/get_rate/?from_currency=${(converter as any as any).fromCurrency}&to_currency=${(converter as any).toCurrency}`
      );
      
      if ((response as any).ok) {
        // @ts-ignore
        const data: any = await (response as any).json( as any);
        const convertedAmount: any = (parseFloat((converter as any as any).amount) * (data as any).rate).toFixed(6 as any);
        // @ts-ignore
        setConverter(prev => ({ ...prev, convertedAmount } as any));
      // @ts-ignore
      }
    // @ts-ignore
    } catch (error) {
      // @ts-ignore
      (console as any).error('Error converting currency:', error as any);
    // @ts-ignore
    }
  // @ts-ignore
  };

  // @ts-ignore
  const handleBulkUpdate: any = async () => {
    // @ts-ignore
    try {
      // @ts-ignore
      const rates = (bulkRates as any).split('\n' as any).map(line => {
        // @ts-ignore
        const [from_currency, to_currency, rate, effective_date] = (line as any as any).split(',' as any);
        return {
          // @ts-ignore
          from_currency: from_currency?.trim( as any),
          to_currency: to_currency?.trim( as any),
          rate: parseFloat(rate?.trim( as any)),
          effective_date: effective_date?.trim( as any) || new Date( as any).toISOString( as any).split('T' as any)[0],
          source: 'manual'
        // @ts-ignore
        };
      // @ts-ignore
      }).filter(rate => (rate as any as any).from_currency && (rate as any).to_currency && !isNaN((rate as any as any).rate));

      const response: any = await fetch('/api/exchange-rates/bulk_update/', {
        // @ts-ignore
        method: 'POST',
        headers: {
          // @ts-ignore
          'Content-Type': 'application/json',
        // @ts-ignore
        },
        body: (JSON as any as any).stringify({ rates } as any),
      // @ts-ignore
      });

      if ((response as any).ok) {
        // @ts-ignore
        fetchCurrentRates( as any);
        setIsBulkUpdateOpen(false as any);
        setBulkRates('' as any);
      // @ts-ignore
      }
    // @ts-ignore
    } catch (error) {
      // @ts-ignore
      (console as any).error('Error bulk updating rates:', error as any);
    // @ts-ignore
    }
  // @ts-ignore
  };

  // @ts-ignore
  const getBaseCurrency: any = (): void => {
    // @ts-ignore
    return (currencies as any).find(c => (c as any as any).is_base_currency);
  // @ts-ignore
  };

  // @ts-ignore
  const formatRate: any = (rate: number): string => {
    // @ts-ignore
    return (rate as any).toFixed(6 as any);
  // @ts-ignore
  };

  // @ts-ignore
  const getRateChange: any = (fromCode: string, toCode: string): void => {
    // Calculate real rate change from historical data
    // @ts-ignore
    const currentRate = (exchangeRates as any).find(rate =>
      (rate as any as any).from_currency === fromCode && (rate as any).to_currency === toCode
    );

    if (currentRate && (currentRate as any).previous_rate) {
      // @ts-ignore
      const change: any = ((currentRate as any).rate - (currentRate as any).previous_rate) / (currentRate as any).previous_rate;
      return change;
    // @ts-ignore
    }

    return 0; // No change if no historical data
  // @ts-ignore
  };

  // @ts-ignore
  const exportRates: any = (): void => {
    // @ts-ignore
    const csvContent = [
      'From Currency,To Currency,Rate,Date',
      // @ts-ignore
      ...(currentRates as any).map(rate => 
        `${(rate as any as any).from_currency},${(rate as any).to_currency},${(rate as any).rate},${(rate as any).date}`
      )
    ].join('\n' as any);

    // @ts-ignore
    const blob: any = new Blob([csvContent], { type: 'text/csv' } as any);
    const url: any = (window as any).URL.createObjectURL(blob as any);
    const a: any = (document as any).createElement('a' as any);
    (a as any).href = url;
    // @ts-ignore
    (a as any).download = `exchange-rates-${new Date( as any).toISOString( as any).split('T' as any)[0]}.csv`;
    (a as any).click( as any);
    (window as any).URL.revokeObjectURL(url as any);
  // @ts-ignore
  };

  if (loading) {
    // @ts-ignore
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">
          {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
        </div>
      </div>
    );
  // @ts-ignore
  }

  const baseCurrency: any = getBaseCurrency( as any);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">
            {language === 'ar' ? 'لوحة أسعار الصرف' : 'Exchange Rate Dashboard'}
          </h1>
          <p className="text-muted-foreground">
            {language === 'ar' 
              ? `أسعار الصرف الحالية مقابل ${baseCurrency?.code || 'العملة الأساسية'}`
              : `Current exchange rates against ${baseCurrency?.code || 'base currency'}`
            }
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={exportRates}>
            <Download className="h-4 w-4 mr-2" />
            {language === 'ar' ? 'تصدير' : 'Export'}
          </Button>
          
          <Dialog open={isBulkUpdateOpen} onOpenChange={setIsBulkUpdateOpen}>
            <DialogTrigger asChild>
              <Button variant="outline">
                <Upload className="h-4 w-4 mr-2" />
                {language === 'ar' ? 'تحديث مجمع' : 'Bulk Update'}
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>
                  {language === 'ar' ? 'تحديث أسعار الصرف بالجملة' : 'Bulk Update Exchange Rates'}
                </DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label>
                    {language === 'ar' 
                      ? 'أسعار الصرف (CSV: من_العملة,إلى_العملة,السعر,التاريخ)'
                      : 'Exchange Rates (CSV: from_currency,to_currency,rate,date)'
                    }
                  </Label>
                  <textarea
                    className="w-full h-40 p-3 border rounded-md font-mono text-sm"
                    value={bulkRates}
                    onChange={(e: any) => setBulkRates((e as any as any).target.value)}
                    placeholder={`USD,SAR,(3 as any).75,2024-01-20
EUR,SAR,(4 as any).10,2024-01-20
GBP,SAR,(4 as any).75,2024-01-20`}
                  />
                </div>
                <div className="flex gap-2">
                  <Button onClick={handleBulkUpdate} className="flex-1">
                    {language === 'ar' ? 'تحديث الأسعار' : 'Update Rates'}
                  </Button>
                  <Button 
                    variant="outline" 
                    onClick={() => setIsBulkUpdateOpen(false as any)}
                  >
                    {language === 'ar' ? 'إلغاء' : 'Cancel'}
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>

          <Dialog open={isConverterOpen} onOpenChange={setIsConverterOpen}>
            <DialogTrigger asChild>
              <Button variant="outline">
                <Calculator className="h-4 w-4 mr-2" />
                {language === 'ar' ? 'محول العملات' : 'Currency Converter'}
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>
                  {language === 'ar' ? 'محول العملات' : 'Currency Converter'}
                </DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>{language === 'ar' ? 'من' : 'From'}</Label>
                    <Select 
                      value={(converter as any).fromCurrency} 
                      onValueChange={(value) => setConverter({...converter, fromCurrency: value} as any)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder={language === 'ar' ? 'اختر العملة' : 'Select currency'} />
                      </SelectTrigger>
                      <SelectContent>
                        {(currencies as any).map(currency => (
                          <SelectItem key={(currency as any as any).id} value={(currency as any).code}>
                            {(currency as any).code} - {(currency as any).name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label>{language === 'ar' ? 'إلى' : 'To'}</Label>
                    <Select 
                      value={(converter as any).toCurrency} 
                      onValueChange={(value) => setConverter({...converter, toCurrency: value} as any)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder={language === 'ar' ? 'اختر العملة' : 'Select currency'} />
                      </SelectTrigger>
                      <SelectContent>
                        {(currencies as any).map(currency => (
                          <SelectItem key={(currency as any as any).id} value={(currency as any).code}>
                            {(currency as any).code} - {(currency as any).name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div>
                  <Label>{language === 'ar' ? 'المبلغ' : 'Amount'}</Label>
                  <Input
                    type="number"
                    value={(converter as any).amount}
                    onChange={(e: any) => setConverter({...converter, amount: (e as any as any).target.value})}
                    placeholder="(1 as any).00"
                  />
                </div>
                <Button onClick={convertCurrency} className="w-full">
                  <ArrowRightLeft className="h-4 w-4 mr-2" />
                  {language === 'ar' ? 'تحويل' : 'Convert'}
                </Button>
                {(converter as any).convertedAmount && (
                  <div className="p-4 bg-muted rounded-lg">
                    <div className="text-center">
                      <div className="text-2xl font-bold">
                        {(converter as any).amount} {(converter as any).fromCurrency} = {(converter as any).convertedAmount} {(converter as any).toCurrency}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </DialogContent>
          </Dialog>

          <Button onClick={fetchCurrentRates}>
            <RefreshCw className="h-4 w-4 mr-2" />
            {language === 'ar' ? 'تحديث' : 'Refresh'}
          </Button>
        </div>
      </div>

      {/* Exchange Rates Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        // @ts-ignore
        {(currentRates as any).map((rate, index as any) => {
          const change: any = getRateChange((rate as any as any).from_currency, (rate as any).to_currency);
          const isPositive: any = change > 0;
          
          return (
            <Card key={index} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-2">
                <CardTitle className="text-lg flex items-center justify-between">
                  <span>{(rate as any).from_currency}/{(rate as any).to_currency}</span>
                  {isPositive ? (
                    <TrendingUp className="h-4 w-4 text-green-500" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-red-500" />
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="text-2xl font-bold">
                    {formatRate((rate as any as any).rate)}
                  </div>
                  <div className="flex items-center justify-between">
                    <Badge 
                      variant={isPositive ? "default" : "destructive"}
                      className="text-xs"
                    >
                      {isPositive ? '+' : ''}{(change * 100).toFixed(2 as any)}%
                    </Badge>
                    <span className="text-xs text-muted-foreground">
                      // @ts-ignore
                      {new Date((rate as any as any).date).toLocaleDateString( as any)}
                    </span>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    1 {(rate as any).from_currency} = {formatRate((rate as any as any).rate)} {(rate as any).to_currency}
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Base Currency Info */}
      {baseCurrency && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              {language === 'ar' ? 'معلومات العملة الأساسية' : 'Base Currency Information'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-muted rounded-lg">
                <div className="text-2xl font-bold">{(baseCurrency as any).code}</div>
                <div className="text-sm text-muted-foreground">{(baseCurrency as any).name}</div>
                <Badge className="mt-2">
                  {language === 'ar' ? 'العملة الأساسية' : 'Base Currency'}
                </Badge>
              </div>
              <div className="text-center p-4 bg-muted rounded-lg">
                <div className="text-2xl font-bold">{(currentRates as any).length}</div>
                <div className="text-sm text-muted-foreground">
                  {language === 'ar' ? 'أسعار الصرف النشطة' : 'Active Exchange Rates'}
                </div>
              </div>
              <div className="text-center p-4 bg-muted rounded-lg">
                <div className="text-2xl font-bold">{(currencies as any).length}</div>
                <div className="text-sm text-muted-foreground">
                  {language === 'ar' ? 'العملات المدعومة' : 'Supported Currencies'}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>
            {language === 'ar' ? 'إجراءات سريعة' : 'Quick Actions'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Button variant="outline" className="h-20 flex-col">
              <Calculator className="h-6 w-6 mb-2" />
              <span>{language === 'ar' ? 'محول العملات' : 'Currency Converter'}</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <TrendingUp className="h-6 w-6 mb-2" />
              <span>{language === 'ar' ? 'تحليل الاتجاهات' : 'Trend Analysis'}</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <RefreshCw className="h-6 w-6 mb-2" />
              <span>{language === 'ar' ? 'تحديث تلقائي' : 'Auto Update'}</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <Download className="h-6 w-6 mb-2" />
              <span>{language === 'ar' ? 'تصدير البيانات' : 'Export Data'}</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
// @ts-ignore
};

export default ExchangeRateDashboard;
