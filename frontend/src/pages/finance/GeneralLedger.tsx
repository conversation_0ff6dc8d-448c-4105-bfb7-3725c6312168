/**
 * General Ledger Page - Chart of Accounts Management
 * Displays hierarchical chart of accounts with balances
 */

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Search,
  Plus,
  FileText,
  TrendingUp,
  DollarSign,
  Building,
  Users,
  Calculator,
  BarChart3,
  CreditCard,
  Receipt
} from 'lucide-react'

interface GeneralLedgerProps {
  language: 'ar' | 'en'
}

interface ChartOfAccount {
  id: number
  account_code: string
  account_name: string
  account_name_ar: string
  account_type: string
  account_type_name: string
  parent_account: number | null
  parent_account_name: string | null
  level: number
  current_balance: number
  sub_accounts_count: number
  is_active: boolean
  allow_manual_entries: boolean
}

interface AccountType {
  id: number
  name: string
  name_ar: string
  description: string
}

const translations = {
  ar: {
    generalLedger: 'دليل الحسابات',
    chartOfAccounts: 'شجرة الحسابات',
    addAccount: 'إضافة حساب',
    export: 'تصدير',
    searchAccounts: 'البحث في الحسابات...',
    allTypes: 'جميع الأنواع',
    accountCode: 'رمز الحساب',
    accountName: 'اسم الحساب',
    arabicName: 'الاسم بالعربية',
    type: 'النوع',
    balance: 'الرصيد',
    subAccounts: 'الحسابات الفرعية',
    status: 'الحالة',
    active: 'نشط',
    inactive: 'غير نشط',
    noAccountsFound: 'لم يتم العثور على حسابات تطابق معاييرك',
    accounts: 'حسابات'
  },
  en: {
    generalLedger: 'General Ledger',
    chartOfAccounts: 'Chart of Accounts',
    addAccount: 'Add Account',
    export: 'Export',
    searchAccounts: 'Search accounts...',
    allTypes: 'All Types',
    accountCode: 'Account Code',
    accountName: 'Account Name',
    arabicName: 'Arabic Name',
    type: 'Type',
    balance: 'Balance',
    subAccounts: 'Sub Accounts',
    status: 'Status',
    active: 'Active',
    inactive: 'Inactive',
    noAccountsFound: 'No accounts found matching your criteria',
    accounts: 'accounts'
  }
}

const GeneralLedger: React.FC<GeneralLedgerProps> = ({ language }) => {
  const t = translations[language]
  const [accounts, setAccounts] = useState<ChartOfAccount[]>([])
  const [accountTypes, setAccountTypes] = useState<AccountType[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedType, setSelectedType] = useState<string>('all')

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      setLoading(true)

      const token = localStorage.getItem('token')
      const headers = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      }

      // Fetch Chart of Accounts
      const accountsResponse = await fetch('/api/chart-of-accounts/', { headers })

      if (accountsResponse.ok) {
        const accountsData = await accountsResponse.json()
        setAccounts(accountsData.results || accountsData)
      } else {
        console.error('Failed to fetch accounts:', accountsResponse.status)
      }

      // Fetch Account Types
      const typesResponse = await fetch('/api/account-types/', { headers })

      if (typesResponse.ok) {
        const typesData = await typesResponse.json()
        setAccountTypes(typesData.results || typesData)
      } else {
        console.error('Failed to fetch account types:', typesResponse.status)
      }

    } catch (error) {
      console.error('Error fetching data:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredAccounts = accounts.filter(account => {
    const matchesSearch = account.account_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         account.account_name_ar.includes(searchTerm) ||
                         account.account_code.includes(searchTerm)
    const matchesType = selectedType === 'all' || account.account_type === selectedType
    return matchesSearch && matchesType
  })

  const getAccountTypeIcon = (type: string): void => {
    switch (type) {
      case 'ASSET': return <Building className="h-4 w-4" />
      case 'LIABILITY': return <Users className="h-4 w-4" />
      case 'EQUITY': return <TrendingUp className="h-4 w-4" />
      case 'REVENUE': return <DollarSign className="h-4 w-4" />
      case 'EXPENSE': return <Calculator className="h-4 w-4" />
      case 'COST_OF_GOODS_SOLD': return <FileText className="h-4 w-4" />
      default: return <FileText className="h-4 w-4" />
    }
  }

  const getAccountTypeColor = (type: string): void => {
    switch (type) {
      case 'ASSET': return 'bg-green-100 text-green-800'
      case 'LIABILITY': return 'bg-red-100 text-red-800'
      case 'EQUITY': return 'bg-blue-100 text-blue-800'
      case 'REVENUE': return 'bg-purple-100 text-purple-800'
      case 'EXPENSE': return 'bg-orange-100 text-orange-800'
      case 'COST_OF_GOODS_SOLD': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 2,
    }).format(amount)
  }

  const getIndentation = (level: number): void => {
    return `${level * 20}px`
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            {language === 'ar' ? t.generalLedger : t.generalLedger}
          </h1>
          <p className="text-gray-600 mt-1">
            {language === 'ar' ? 'نظام دليل الحسابات المالي' : 'Financial Chart of Accounts System'}
          </p>
        </div>
        <div className="flex gap-2">
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            {t.addAccount}
          </Button>
          <Button variant="outline">
            <FileText className="h-4 w-4 mr-2" />
            {t.export}
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {accountTypes.map((type) => {
          const typeAccounts = accounts.filter(acc => acc.account_type === type.name)
          const totalBalance = typeAccounts.reduce((sum, acc) => sum + acc.current_balance, 0)
          
          return (
            <Card key={type.id}>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{type.description}</p>
                    <p className="text-xs text-gray-500">{type.name_ar}</p>
                    <p className="text-2xl font-bold mt-1">{formatCurrency(totalBalance)}</p>
                  </div>
                  <div className={`p-2 rounded-full ${getAccountTypeColor(type.name)}`}>
                    {getAccountTypeIcon(type.name)}
                  </div>
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  {typeAccounts.length} {t.accounts}
                </p>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder={t.searchAccounts}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="sm:w-48">
              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">{t.allTypes}</option>
                {accountTypes.map((type) => (
                  <option key={type.id} value={type.name}>
                    {type.description}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Chart of Accounts Table */}
      <Card>
        <CardHeader>
          <CardTitle>{t.chartOfAccounts}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-4 font-medium text-gray-900">{t.accountCode}</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">{t.accountName}</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">{t.arabicName}</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">{t.type}</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900">{t.balance}</th>
                  <th className="text-center py-3 px-4 font-medium text-gray-900">{t.subAccounts}</th>
                  <th className="text-center py-3 px-4 font-medium text-gray-900">{t.status}</th>
                </tr>
              </thead>
              <tbody>
                {filteredAccounts.map((account) => (
                  <tr key={account.id} className="border-b hover:bg-gray-50">
                    <td className="py-3 px-4">
                      <code className="bg-gray-100 px-2 py-1 rounded text-sm">
                        {account.account_code}
                      </code>
                    </td>
                    <td className="py-3 px-4">
                      <div style={{ paddingLeft: getIndentation(account.level) }}>
                        <span className="font-medium">{account.account_name}</span>
                      </div>
                    </td>
                    <td className="py-3 px-4 text-right">
                      <span className="text-gray-600">{account.account_name_ar}</span>
                    </td>
                    <td className="py-3 px-4">
                      <Badge className={getAccountTypeColor(account.account_type)}>
                        <span className="flex items-center gap-1">
                          {getAccountTypeIcon(account.account_type)}
                          {account.account_type_name}
                        </span>
                      </Badge>
                    </td>
                    <td className="py-3 px-4 text-right font-mono">
                      {formatCurrency(account.current_balance)}
                    </td>
                    <td className="py-3 px-4 text-center">
                      {account.sub_accounts_count > 0 && (
                        <Badge variant="outline">
                          {account.sub_accounts_count}
                        </Badge>
                      )}
                    </td>
                    <td className="py-3 px-4 text-center">
                      <Badge variant={account.is_active ? "default" : "secondary"}>
                        {account.is_active ? t.active : t.inactive}
                      </Badge>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          {filteredAccounts.length === 0 && (
            <div className="text-center py-8">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">{t.noAccountsFound}</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

export default GeneralLedger