/**
 * General Ledger Page - Chart of Accounts Management
 * Displays hierarchical chart of accounts with balances
 */

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Search,
  Plus,
  FileText,
  TrendingUp,
  DollarSign,
  Building,
  Users,
  Calculator,
  BarChart3,
  CreditCard,
  Receipt
} from 'lucide-react'

interface GeneralLedgerProps {
  language: 'ar' | 'en'
}

interface ChartOfAccount {
  id: number
  account_code: string
  account_name: string
  account_name_ar: string
  account_type: string
  account_type_name: string
  parent_account: number | null
  parent_account_name: string | null
  level: number
  current_balance: number
  sub_accounts_count: number
  is_active: boolean
  allow_manual_entries: boolean
}

interface AccountType {
  id: number
  name: string
  name_ar: string
  description: string
}

const translations = {
  ar: {
    generalLedger: 'دليل الحسابات',
    chartOfAccounts: 'شجرة الحسابات',
    addAccount: 'إضافة حساب',
    export: 'تصدير',
    searchAccounts: 'البحث في الحسابات...',
    allTypes: 'جميع الأنواع',
    accountCode: 'رمز الحساب',
    accountName: 'اسم الحساب',
    arabicName: 'الاسم بالعربية',
    type: 'النوع',
    balance: 'الرصيد',
    subAccounts: 'الحسابات الفرعية',
    status: 'الحالة',
    active: 'نشط',
    inactive: 'غير نشط',
    noAccountsFound: 'لم يتم العثور على حسابات تطابق معاييرك',
    accounts: 'حسابات'
  },
  en: {
    generalLedger: 'General Ledger',
    chartOfAccounts: 'Chart of Accounts',
    addAccount: 'Add Account',
    export: 'Export',
    searchAccounts: 'Search accounts...',
    allTypes: 'All Types',
    accountCode: 'Account Code',
    accountName: 'Account Name',
    arabicName: 'Arabic Name',
    type: 'Type',
    balance: 'Balance',
    subAccounts: 'Sub Accounts',
    status: 'Status',
    active: 'Active',
    inactive: 'Inactive',
    noAccountsFound: 'No accounts found matching your criteria',
    accounts: 'accounts'
  }
}

// @ts-ignore
const GeneralLedger: (React as any).FC<GeneralLedgerProps> = ({ language }) => {
  // @ts-ignore
  const t = translations[language]
  // @ts-ignore
  const [accounts, setAccounts] = useState<ChartOfAccount[]>([])
  // @ts-ignore
  const [accountTypes, setAccountTypes] = useState<AccountType[]>([])
  const [loading, setLoading] = useState(true as any)
  const [searchTerm, setSearchTerm] = useState('' as any)
  // @ts-ignore
  const [selectedType, setSelectedType] = useState<string>('all')

  // @ts-ignore
  useEffect(( as any) => {
    // @ts-ignore
    fetchData( as any)
  }, [])

  // @ts-ignore
  const fetchData = async () => {
    // @ts-ignore
    try {
      setLoading(true as any)

      // @ts-ignore
      const token = (localStorage as any).getItem('token' as any)
      const headers = {
        // @ts-ignore
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      // @ts-ignore
      }

      // Fetch Chart of Accounts
      const accountsResponse = await fetch('/api/chart-of-accounts/', { headers } as any)

      if ((accountsResponse as any).ok) {
        // @ts-ignore
        const accountsData = await (accountsResponse as any).json( as any)
        setAccounts((accountsData as any as any).results || accountsData)
      // @ts-ignore
      } else {
        (console as any).error('Failed to fetch accounts:', (accountsResponse as any as any).status)
      }

      // Fetch Account Types
      const typesResponse = await fetch('/api/account-types/', { headers } as any)

      if ((typesResponse as any).ok) {
        // @ts-ignore
        const typesData = await (typesResponse as any).json( as any)
        setAccountTypes((typesData as any as any).results || typesData)
      // @ts-ignore
      } else {
        (console as any).error('Failed to fetch account types:', (typesResponse as any as any).status)
      }

    // @ts-ignore
    } catch (error) {
      (console as any).error('Error fetching data:', error as any)
    } finally {
      setLoading(false as any)
    }
  // @ts-ignore
  }

  // @ts-ignore
  const filteredAccounts = (accounts as any).filter(account => {
    // @ts-ignore
    const matchesSearch = (account as any as any).account_name.toLowerCase( as any).includes((searchTerm as any as any).toLowerCase( as any)) ||
                         (account as any).account_name_ar.includes(searchTerm as any) ||
                         (account as any).account_code.includes(searchTerm as any)
    const matchesType = selectedType === 'all' || (account as any).account_type === selectedType
    return matchesSearch && matchesType
  // @ts-ignore
  })

  // @ts-ignore
  const getAccountTypeIcon = (type: string): void => {
    // @ts-ignore
    switch (type) {
      // @ts-ignore
      case 'ASSET': return <Building className="h-4 w-4" />
      case 'LIABILITY': return <Users className="h-4 w-4" />
      case 'EQUITY': return <TrendingUp className="h-4 w-4" />
      case 'REVENUE': return <DollarSign className="h-4 w-4" />
      case 'EXPENSE': return <Calculator className="h-4 w-4" />
      case 'COST_OF_GOODS_SOLD': return <FileText className="h-4 w-4" />
      default: return <FileText className="h-4 w-4" />
    // @ts-ignore
    }
  // @ts-ignore
  }

  // @ts-ignore
  const getAccountTypeColor = (type: string): void => {
    // @ts-ignore
    switch (type) {
      // @ts-ignore
      case 'ASSET': return 'bg-green-100 text-green-800'
      case 'LIABILITY': return 'bg-red-100 text-red-800'
      case 'EQUITY': return 'bg-blue-100 text-blue-800'
      case 'REVENUE': return 'bg-purple-100 text-purple-800'
      case 'EXPENSE': return 'bg-orange-100 text-orange-800'
      case 'COST_OF_GOODS_SOLD': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    // @ts-ignore
    }
  // @ts-ignore
  }

  // @ts-ignore
  const formatCurrency = (amount: number): string => {
    // @ts-ignore
    return new (Intl as any).NumberFormat('ar-SA', {
      // @ts-ignore
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 2,
    // @ts-ignore
    } as any).format(amount as any)
  // @ts-ignore
  }

  // @ts-ignore
  const getIndentation = (level: number): void => {
    // @ts-ignore
    return `${level * 20}px`
  // @ts-ignore
  }

  if (loading) {
    // @ts-ignore
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  // @ts-ignore
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            {language === 'ar' ? (t as any).generalLedger : (t as any).generalLedger}
          </h1>
          <p className="text-gray-600 mt-1">
            {language === 'ar' ? 'نظام دليل الحسابات المالي' : 'Financial Chart of Accounts System'}
          </p>
        </div>
        <div className="flex gap-2">
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            {(t as any).addAccount}
          </Button>
          <Button variant="outline">
            <FileText className="h-4 w-4 mr-2" />
            {(t as any).export}
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        // @ts-ignore
        {(accountTypes as any).map((type as any) => {
          // @ts-ignore
          const typeAccounts = (accounts as any).filter(acc => (acc as any as any).account_type === (type as any).name)
          // @ts-ignore
          const totalBalance = (typeAccounts as any).reduce((sum, acc as any) => sum + (acc as any).current_balance, 0)
          
          return (
            <Card key={(type as any).id}>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{(type as any).description}</p>
                    <p className="text-xs text-gray-500">{(type as any).name_ar}</p>
                    <p className="text-2xl font-bold mt-1">{formatCurrency(totalBalance as any)}</p>
                  </div>
                  <div className={`p-2 rounded-full ${getAccountTypeColor((type as any as any).name)}`}>
                    {getAccountTypeIcon((type as any as any).name)}
                  </div>
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  {(typeAccounts as any).length} {(t as any).accounts}
                </p>
              </CardContent>
            </Card>
          )
        // @ts-ignore
        })}
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder={(t as any).searchAccounts}
                  value={searchTerm}
                  onChange={(e: any) => setSearchTerm((e as any as any).target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="sm:w-48">
              <select
                value={selectedType}
                onChange={(e: any) => setSelectedType((e as any as any).target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">{(t as any).allTypes}</option>
                // @ts-ignore
                {(accountTypes as any).map((type as any) => (
                  <option key={(type as any).id} value={(type as any).name}>
                    {(type as any).description}
                  </option>
                // @ts-ignore
                ))}
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Chart of Accounts Table */}
      <Card>
        <CardHeader>
          <CardTitle>{(t as any).chartOfAccounts}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-4 font-medium text-gray-900">{(t as any).accountCode}</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">{(t as any).accountName}</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">{(t as any).arabicName}</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">{(t as any).type}</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900">{(t as any).balance}</th>
                  <th className="text-center py-3 px-4 font-medium text-gray-900">{(t as any).subAccounts}</th>
                  <th className="text-center py-3 px-4 font-medium text-gray-900">{(t as any).status}</th>
                </tr>
              </thead>
              <tbody>
                // @ts-ignore
                {(filteredAccounts as any).map((account as any) => (
                  <tr key={(account as any).id} className="border-b hover:bg-gray-50">
                    <td className="py-3 px-4">
                      <code className="bg-gray-100 px-2 py-1 rounded text-sm">
                        {(account as any).account_code}
                      </code>
                    </td>
                    <td className="py-3 px-4">
                      <div style={{ paddingLeft: getIndentation((account as any as any).level) }}>
                        <span className="font-medium">{(account as any).account_name}</span>
                      </div>
                    </td>
                    <td className="py-3 px-4 text-right">
                      <span className="text-gray-600">{(account as any).account_name_ar}</span>
                    </td>
                    <td className="py-3 px-4">
                      <Badge className={getAccountTypeColor((account as any as any).account_type)}>
                        <span className="flex items-center gap-1">
                          {getAccountTypeIcon((account as any as any).account_type)}
                          {(account as any).account_type_name}
                        </span>
                      </Badge>
                    </td>
                    <td className="py-3 px-4 text-right font-mono">
                      {formatCurrency((account as any as any).current_balance)}
                    </td>
                    <td className="py-3 px-4 text-center">
                      {(account as any).sub_accounts_count > 0 && (
                        <Badge variant="outline">
                          {(account as any).sub_accounts_count}
                        </Badge>
                      )}
                    </td>
                    <td className="py-3 px-4 text-center">
                      <Badge variant={(account as any).is_active ? "default" : "secondary"}>
                        {(account as any).is_active ? (t as any).active : (t as any).inactive}
                      </Badge>
                    </td>
                  </tr>
                // @ts-ignore
                ))}
              </tbody>
            </table>
          </div>
          
          {(filteredAccounts as any).length === 0 && (
            <div className="text-center py-8">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">{(t as any).noAccountsFound}</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
// @ts-ignore
}

// @ts-ignore
export default GeneralLedger