import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON>ontent, 
  <PERSON><PERSON><PERSON><PERSON>, 
  CardTitle 
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { 
  Calendar, 
  Plus, 
  Edit, 
  CheckCircle, 
  Clock,
  AlertTriangle,
  Wrench,
  User,
  DollarSign,
  Search,
  Filter
} from 'lucide-react';
// Language is passed as prop, not from context

interface MaintenanceRecord {
  id: number;
  asset: number;
  asset_name: string;
  asset_id: string;
  maintenance_type: string;
  title: string;
  description: string;
  scheduled_date: string;
  scheduled_time: string | null;
  actual_start_date: string | null;
  actual_end_date: string | null;
  performed_by: number | null;
  performed_by_name: string;
  external_vendor: string;
  status: string;
  priority: string;
  estimated_cost: string | null;
  actual_cost: string | null;
  cost_variance: number | null;
  currency_code: string;
  work_performed: string;
  parts_used: string;
  issues_found: string;
  recommendations: string;
  next_maintenance_date: string | null;
  is_overdue: boolean;
  created_at: string;
}

interface Asset {
  id: number;
  asset_id: string;
  name: string;
}

interface Employee {
  id: number;
  user: {
    first_name: string;
    last_name: string;
  };
}

interface MaintenanceManagementProps {
  language: 'ar' | 'en';
}

const MaintenanceManagement: React.FC<MaintenanceManagementProps> = ({ language }) => {
  const [maintenanceRecords, setMaintenanceRecords] = useState<MaintenanceRecord[]>([]);
  const [assets, setAssets] = useState<Asset[]>([]);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [priorityFilter, setPriorityFilter] = useState('');
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [selectedMaintenance, setSelectedMaintenance] = useState<MaintenanceRecord | null>(null);
  const [isCompleteDialogOpen, setIsCompleteDialogOpen] = useState(false);

  // Form state
  const [maintenanceForm, setMaintenanceForm] = useState({
    asset: '',
    maintenance_type: 'PREVENTIVE',
    title: '',
    description: '',
    scheduled_date: '',
    scheduled_time: '',
    priority: 'MEDIUM',
    estimated_cost: '',
    performed_by: '',
    external_vendor: ''
  });

  // Completion form state
  const [completionForm, setCompletionForm] = useState({
    work_performed: '',
    parts_used: '',
    issues_found: '',
    recommendations: '',
    actual_cost: '',
    next_maintenance_date: ''
  });

  useEffect(() => {
    fetchMaintenanceRecords();
    fetchAssets();
    fetchEmployees();
  }, []);

  const fetchMaintenanceRecords = async () => {
    try {
      const params = new URLSearchParams();
      if (searchTerm) params.append('search', searchTerm);
      if (statusFilter) params.append('status', statusFilter);
      if (priorityFilter) params.append('priority', priorityFilter);
      
      const response = await fetch(`/api/asset-maintenance/?${params.toString()}`);
      if (response.ok) {
        const data = await response.json();
        setMaintenanceRecords(data.results || data);
      }
    } catch (error) {
      console.error('Error fetching maintenance records:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchAssets = async () => {
    try {
      const response = await fetch('/api/assets/?is_active=true');
      if (response.ok) {
        const data = await response.json();
        setAssets(data.results || data);
      }
    } catch (error) {
      console.error('Error fetching assets:', error);
    }
  };

  const fetchEmployees = async () => {
    try {
      const response = await fetch('/api/employees/');
      if (response.ok) {
        const data = await response.json();
        setEmployees(data.results || data);
      }
    } catch (error) {
      console.error('Error fetching employees:', error);
    }
  };

  const handleAddMaintenance = async () => {
    try {
      const response = await fetch('/api/asset-maintenance/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...maintenanceForm,
          asset: parseInt(maintenanceForm.asset),
          performed_by: maintenanceForm.performed_by && maintenanceForm.performed_by !== '__not_specified__' ? parseInt(maintenanceForm.performed_by) : null,
        }),
      });

      if (response.ok) {
        fetchMaintenanceRecords();
        setIsAddDialogOpen(false);
        resetForm();
      }
    } catch (error) {
      console.error('Error adding maintenance:', error);
    }
  };

  const handleCompleteMaintenance = async () => {
    if (!selectedMaintenance) return;

    try {
      const response = await fetch(`/api/asset-maintenance/${selectedMaintenance.id}/complete/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(completionForm),
      });

      if (response.ok) {
        fetchMaintenanceRecords();
        setIsCompleteDialogOpen(false);
        setSelectedMaintenance(null);
        resetCompletionForm();
      }
    } catch (error) {
      console.error('Error completing maintenance:', error);
    }
  };

  const resetForm = (): void => {
    setMaintenanceForm({
      asset: '',
      maintenance_type: 'PREVENTIVE',
      title: '',
      description: '',
      scheduled_date: '',
      scheduled_time: '',
      priority: 'MEDIUM',
      estimated_cost: '',
      performed_by: '',
      external_vendor: ''
    });
  };

  const resetCompletionForm = (): void => {
    setCompletionForm({
      work_performed: '',
      parts_used: '',
      issues_found: '',
      recommendations: '',
      actual_cost: '',
      next_maintenance_date: ''
    });
  };

  const getStatusColor = (status: string): void => {
    const colors = {
      'SCHEDULED': 'bg-blue-100 text-blue-800',
      'IN_PROGRESS': 'bg-yellow-100 text-yellow-800',
      'COMPLETED': 'bg-green-100 text-green-800',
      'CANCELLED': 'bg-gray-100 text-gray-800',
      'OVERDUE': 'bg-red-100 text-red-800'
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const getPriorityColor = (priority: string): void => {
    const colors = {
      'LOW': 'text-green-600',
      'MEDIUM': 'text-yellow-600',
      'HIGH': 'text-orange-600',
      'CRITICAL': 'text-red-600'
    };
    return colors[priority as keyof typeof colors] || 'text-gray-600';
  };

  const formatCurrency = (amount: string | null, currency: string = 'SAR'): string => {
    if (!amount) return '-';
    return `${parseFloat(amount).toLocaleString()} ${currency}`;
  };

  const filteredRecords = maintenanceRecords.filter(record => {
    const matchesSearch = !searchTerm || 
      record.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.asset_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.asset_id.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = !statusFilter || record.status === statusFilter;
    const matchesPriority = !priorityFilter || record.priority === priorityFilter;
    
    return matchesSearch && matchesStatus && matchesPriority;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">
          {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">
            {language === 'ar' ? 'إدارة الصيانة' : 'Maintenance Management'}
          </h1>
          <p className="text-muted-foreground">
            {language === 'ar' 
              ? 'جدولة وتتبع أعمال الصيانة للأصول'
              : 'Schedule and track asset maintenance activities'
            }
          </p>
        </div>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              {language === 'ar' ? 'جدولة صيانة' : 'Schedule Maintenance'}
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>
                {language === 'ar' ? 'جدولة صيانة جديدة' : 'Schedule New Maintenance'}
              </DialogTitle>
            </DialogHeader>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label>{language === 'ar' ? 'الأصل' : 'Asset'}</Label>
                <Select value={maintenanceForm.asset} onValueChange={(value) => setMaintenanceForm({...maintenanceForm, asset: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder={language === 'ar' ? 'اختر الأصل' : 'Select asset'} />
                  </SelectTrigger>
                  <SelectContent>
                    {assets.map(asset => (
                      <SelectItem key={asset.id} value={asset.id.toString()}>
                        {asset.asset_id} - {asset.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label>{language === 'ar' ? 'نوع الصيانة' : 'Maintenance Type'}</Label>
                <Select value={maintenanceForm.maintenance_type} onValueChange={(value) => setMaintenanceForm({...maintenanceForm, maintenance_type: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="PREVENTIVE">
                      {language === 'ar' ? 'صيانة وقائية' : 'Preventive'}
                    </SelectItem>
                    <SelectItem value="CORRECTIVE">
                      {language === 'ar' ? 'صيانة تصحيحية' : 'Corrective'}
                    </SelectItem>
                    <SelectItem value="EMERGENCY">
                      {language === 'ar' ? 'إصلاح طارئ' : 'Emergency'}
                    </SelectItem>
                    <SelectItem value="INSPECTION">
                      {language === 'ar' ? 'فحص' : 'Inspection'}
                    </SelectItem>
                    <SelectItem value="CALIBRATION">
                      {language === 'ar' ? 'معايرة' : 'Calibration'}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="md:col-span-2">
                <Label>{language === 'ar' ? 'العنوان' : 'Title'}</Label>
                <Input
                  value={maintenanceForm.title}
                  onChange={(e) => setMaintenanceForm({...maintenanceForm, title: e.target.value})}
                  placeholder={language === 'ar' ? 'أدخل عنوان الصيانة' : 'Enter maintenance title'}
                />
              </div>
              <div className="md:col-span-2">
                <Label>{language === 'ar' ? 'الوصف' : 'Description'}</Label>
                <Textarea
                  value={maintenanceForm.description}
                  onChange={(e) => setMaintenanceForm({...maintenanceForm, description: e.target.value})}
                  placeholder={language === 'ar' ? 'أدخل وصف الصيانة' : 'Enter maintenance description'}
                  rows={3}
                />
              </div>
              <div>
                <Label>{language === 'ar' ? 'تاريخ الجدولة' : 'Scheduled Date'}</Label>
                <Input
                  type="date"
                  value={maintenanceForm.scheduled_date}
                  onChange={(e) => setMaintenanceForm({...maintenanceForm, scheduled_date: e.target.value})}
                />
              </div>
              <div>
                <Label>{language === 'ar' ? 'وقت الجدولة' : 'Scheduled Time'}</Label>
                <Input
                  type="time"
                  value={maintenanceForm.scheduled_time}
                  onChange={(e) => setMaintenanceForm({...maintenanceForm, scheduled_time: e.target.value})}
                />
              </div>
              <div>
                <Label>{language === 'ar' ? 'الأولوية' : 'Priority'}</Label>
                <Select value={maintenanceForm.priority} onValueChange={(value) => setMaintenanceForm({...maintenanceForm, priority: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="LOW">
                      {language === 'ar' ? 'منخفضة' : 'Low'}
                    </SelectItem>
                    <SelectItem value="MEDIUM">
                      {language === 'ar' ? 'متوسطة' : 'Medium'}
                    </SelectItem>
                    <SelectItem value="HIGH">
                      {language === 'ar' ? 'عالية' : 'High'}
                    </SelectItem>
                    <SelectItem value="CRITICAL">
                      {language === 'ar' ? 'حرجة' : 'Critical'}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label>{language === 'ar' ? 'التكلفة المقدرة' : 'Estimated Cost'}</Label>
                <Input
                  type="number"
                  step="0.01"
                  value={maintenanceForm.estimated_cost}
                  onChange={(e) => setMaintenanceForm({...maintenanceForm, estimated_cost: e.target.value})}
                  placeholder={language === 'ar' ? 'أدخل التكلفة المقدرة' : 'Enter estimated cost'}
                />
              </div>
              <div>
                <Label>{language === 'ar' ? 'المنفذ' : 'Performed By'}</Label>
                <Select value={maintenanceForm.performed_by} onValueChange={(value) => setMaintenanceForm({...maintenanceForm, performed_by: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder={language === 'ar' ? 'اختر الموظف' : 'Select employee'} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="__not_specified__">
                      {language === 'ar' ? 'غير محدد' : 'Not specified'}
                    </SelectItem>
                    {employees.map(employee => (
                      <SelectItem key={employee.id} value={employee.id.toString()}>
                        {employee.user.first_name} {employee.user.last_name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label>{language === 'ar' ? 'مقاول خارجي' : 'External Vendor'}</Label>
                <Input
                  value={maintenanceForm.external_vendor}
                  onChange={(e) => setMaintenanceForm({...maintenanceForm, external_vendor: e.target.value})}
                  placeholder={language === 'ar' ? 'اسم المقاول الخارجي' : 'External vendor name'}
                />
              </div>
            </div>
            <div className="flex justify-end gap-2 mt-4">
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                {language === 'ar' ? 'إلغاء' : 'Cancel'}
              </Button>
              <Button onClick={handleAddMaintenance}>
                {language === 'ar' ? 'جدولة الصيانة' : 'Schedule Maintenance'}
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-[200px]">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder={language === 'ar' ? 'البحث في الصيانة...' : 'Search maintenance...'}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder={language === 'ar' ? 'جميع الحالات' : 'All statuses'} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">
                  {language === 'ar' ? 'جميع الحالات' : 'All statuses'}
                </SelectItem>
                <SelectItem value="SCHEDULED">
                  {language === 'ar' ? 'مجدولة' : 'Scheduled'}
                </SelectItem>
                <SelectItem value="IN_PROGRESS">
                  {language === 'ar' ? 'قيد التنفيذ' : 'In Progress'}
                </SelectItem>
                <SelectItem value="COMPLETED">
                  {language === 'ar' ? 'مكتملة' : 'Completed'}
                </SelectItem>
                <SelectItem value="OVERDUE">
                  {language === 'ar' ? 'متأخرة' : 'Overdue'}
                </SelectItem>
              </SelectContent>
            </Select>
            <Select value={priorityFilter} onValueChange={setPriorityFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder={language === 'ar' ? 'جميع الأولويات' : 'All priorities'} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">
                  {language === 'ar' ? 'جميع الأولويات' : 'All priorities'}
                </SelectItem>
                <SelectItem value="LOW">
                  {language === 'ar' ? 'منخفضة' : 'Low'}
                </SelectItem>
                <SelectItem value="MEDIUM">
                  {language === 'ar' ? 'متوسطة' : 'Medium'}
                </SelectItem>
                <SelectItem value="HIGH">
                  {language === 'ar' ? 'عالية' : 'High'}
                </SelectItem>
                <SelectItem value="CRITICAL">
                  {language === 'ar' ? 'حرجة' : 'Critical'}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Maintenance Records Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Wrench className="h-5 w-5" />
            {language === 'ar' ? 'سجلات الصيانة' : 'Maintenance Records'}
            <Badge variant="secondary" className="ml-2">
              {filteredRecords.length}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{language === 'ar' ? 'الأصل' : 'Asset'}</TableHead>
                  <TableHead>{language === 'ar' ? 'العنوان' : 'Title'}</TableHead>
                  <TableHead>{language === 'ar' ? 'النوع' : 'Type'}</TableHead>
                  <TableHead>{language === 'ar' ? 'التاريخ المجدول' : 'Scheduled Date'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الحالة' : 'Status'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الأولوية' : 'Priority'}</TableHead>
                  <TableHead>{language === 'ar' ? 'التكلفة' : 'Cost'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الإجراءات' : 'Actions'}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredRecords.map((record) => (
                  <TableRow key={record.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{record.asset_name}</div>
                        <div className="text-sm text-muted-foreground">{record.asset_id}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{record.title}</div>
                        <div className="text-sm text-muted-foreground">
                          {record.performed_by_name || record.external_vendor || (language === 'ar' ? 'غير محدد' : 'Not assigned')}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {language === 'ar' 
                          ? record.maintenance_type === 'PREVENTIVE' ? 'وقائية' 
                            : record.maintenance_type === 'CORRECTIVE' ? 'تصحيحية'
                            : record.maintenance_type === 'EMERGENCY' ? 'طارئة'
                            : record.maintenance_type
                          : record.maintenance_type.replace('_', ' ')
                        }
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <span>{new Date(record.scheduled_date).toLocaleDateString()}</span>
                        {record.is_overdue && (
                          <AlertTriangle className="h-4 w-4 text-red-500" />
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(record.status)}>
                        {language === 'ar' 
                          ? record.status === 'SCHEDULED' ? 'مجدولة' 
                            : record.status === 'IN_PROGRESS' ? 'قيد التنفيذ'
                            : record.status === 'COMPLETED' ? 'مكتملة'
                            : record.status === 'OVERDUE' ? 'متأخرة'
                            : record.status
                          : record.status.replace('_', ' ')
                        }
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <span className={getPriorityColor(record.priority)}>
                        {language === 'ar' 
                          ? record.priority === 'LOW' ? 'منخفضة' 
                            : record.priority === 'MEDIUM' ? 'متوسطة'
                            : record.priority === 'HIGH' ? 'عالية'
                            : record.priority === 'CRITICAL' ? 'حرجة'
                            : record.priority
                          : record.priority
                        }
                      </span>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">
                          {formatCurrency(record.estimated_cost, record.currency_code)}
                        </div>
                        {record.actual_cost && (
                          <div className="text-sm text-muted-foreground">
                            {language === 'ar' ? 'فعلي: ' : 'Actual: '}
                            {formatCurrency(record.actual_cost, record.currency_code)}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                        {record.status === 'IN_PROGRESS' && (
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => {
                              setSelectedMaintenance(record);
                              setIsCompleteDialogOpen(true);
                            }}
                          >
                            <CheckCircle className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Complete Maintenance Dialog */}
      <Dialog open={isCompleteDialogOpen} onOpenChange={setIsCompleteDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              {language === 'ar' ? 'إكمال الصيانة' : 'Complete Maintenance'}
            </DialogTitle>
          </DialogHeader>
          {selectedMaintenance && (
            <div className="space-y-4">
              <div className="p-4 bg-muted rounded-lg">
                <div className="font-medium">{selectedMaintenance.title}</div>
                <div className="text-sm text-muted-foreground">
                  {selectedMaintenance.asset_name} ({selectedMaintenance.asset_id})
                </div>
              </div>
              
              <div>
                <Label>{language === 'ar' ? 'العمل المنجز' : 'Work Performed'}</Label>
                <Textarea
                  value={completionForm.work_performed}
                  onChange={(e) => setCompletionForm({...completionForm, work_performed: e.target.value})}
                  placeholder={language === 'ar' ? 'وصف العمل المنجز' : 'Describe work performed'}
                  rows={3}
                />
              </div>
              
              <div>
                <Label>{language === 'ar' ? 'القطع المستخدمة' : 'Parts Used'}</Label>
                <Textarea
                  value={completionForm.parts_used}
                  onChange={(e) => setCompletionForm({...completionForm, parts_used: e.target.value})}
                  placeholder={language === 'ar' ? 'قائمة القطع المستخدمة' : 'List of parts used'}
                  rows={2}
                />
              </div>
              
              <div>
                <Label>{language === 'ar' ? 'المشاكل المكتشفة' : 'Issues Found'}</Label>
                <Textarea
                  value={completionForm.issues_found}
                  onChange={(e) => setCompletionForm({...completionForm, issues_found: e.target.value})}
                  placeholder={language === 'ar' ? 'المشاكل التي تم اكتشافها' : 'Issues discovered during maintenance'}
                  rows={2}
                />
              </div>
              
              <div>
                <Label>{language === 'ar' ? 'التوصيات' : 'Recommendations'}</Label>
                <Textarea
                  value={completionForm.recommendations}
                  onChange={(e) => setCompletionForm({...completionForm, recommendations: e.target.value})}
                  placeholder={language === 'ar' ? 'توصيات للصيانة المستقبلية' : 'Recommendations for future maintenance'}
                  rows={2}
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>{language === 'ar' ? 'التكلفة الفعلية' : 'Actual Cost'}</Label>
                  <Input
                    type="number"
                    step="0.01"
                    value={completionForm.actual_cost}
                    onChange={(e) => setCompletionForm({...completionForm, actual_cost: e.target.value})}
                    placeholder={language === 'ar' ? 'التكلفة الفعلية' : 'Actual cost'}
                  />
                </div>
                <div>
                  <Label>{language === 'ar' ? 'تاريخ الصيانة القادمة' : 'Next Maintenance Date'}</Label>
                  <Input
                    type="date"
                    value={completionForm.next_maintenance_date}
                    onChange={(e) => setCompletionForm({...completionForm, next_maintenance_date: e.target.value})}
                  />
                </div>
              </div>
              
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setIsCompleteDialogOpen(false)}>
                  {language === 'ar' ? 'إلغاء' : 'Cancel'}
                </Button>
                <Button onClick={handleCompleteMaintenance}>
                  {language === 'ar' ? 'إكمال الصيانة' : 'Complete Maintenance'}
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default MaintenanceManagement;
