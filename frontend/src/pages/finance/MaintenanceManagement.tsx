import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON>ontent, 
  <PERSON><PERSON><PERSON><PERSON>, 
  CardTitle 
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { 
  Calendar, 
  Plus, 
  Edit, 
  CheckCircle, 
  Clock,
  AlertTriangle,
  Wrench,
  User,
  DollarSign,
  Search,
  Filter
} from 'lucide-react';
// Language is passed as prop, not from context

interface MaintenanceRecord {
  id: number;
  asset: number;
  asset_name: string;
  asset_id: string;
  maintenance_type: string;
  title: string;
  description: string;
  scheduled_date: string;
  scheduled_time: string | null;
  actual_start_date: string | null;
  actual_end_date: string | null;
  performed_by: number | null;
  performed_by_name: string;
  external_vendor: string;
  status: string;
  priority: string;
  estimated_cost: string | null;
  actual_cost: string | null;
  cost_variance: number | null;
  currency_code: string;
  work_performed: string;
  parts_used: string;
  issues_found: string;
  recommendations: string;
  next_maintenance_date: string | null;
  is_overdue: boolean;
  created_at: string;
}

interface Asset {
  id: number;
  asset_id: string;
  name: string;
}

interface Employee {
  id: number;
  user: {
    first_name: string;
    last_name: string;
  };
}

interface MaintenanceManagementProps {
  language: 'ar' | 'en';
}

// @ts-ignore
const MaintenanceManagement: (React as any).FC<MaintenanceManagementProps> = ({ language }) => {
  // @ts-ignore
  const [maintenanceRecords, setMaintenanceRecords] = useState<MaintenanceRecord[]>([]);
  // @ts-ignore
  const [assets, setAssets] = useState<Asset[]>([]);
  // @ts-ignore
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [loading, setLoading] = useState(true as any);
  const [searchTerm, setSearchTerm] = useState('' as any);
  const [statusFilter, setStatusFilter] = useState('' as any);
  const [priorityFilter, setPriorityFilter] = useState('' as any);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false as any);
  // @ts-ignore
  const [selectedMaintenance, setSelectedMaintenance] = useState<MaintenanceRecord | null>(null);
  const [isCompleteDialogOpen, setIsCompleteDialogOpen] = useState(false as any);

  // Form state
  const [maintenanceForm, setMaintenanceForm] = useState({
    // @ts-ignore
    asset: '',
    maintenance_type: 'PREVENTIVE',
    title: '',
    description: '',
    scheduled_date: '',
    scheduled_time: '',
    priority: 'MEDIUM',
    estimated_cost: '',
    performed_by: '',
    external_vendor: ''
  // @ts-ignore
  } as any);

  // Completion form state
  const [completionForm, setCompletionForm] = useState({
    // @ts-ignore
    work_performed: '',
    parts_used: '',
    issues_found: '',
    recommendations: '',
    actual_cost: '',
    next_maintenance_date: ''
  // @ts-ignore
  } as any);

  // @ts-ignore
  useEffect(( as any) => {
    // @ts-ignore
    fetchMaintenanceRecords( as any);
    fetchAssets( as any);
    fetchEmployees( as any);
  // @ts-ignore
  }, []);

  // @ts-ignore
  const fetchMaintenanceRecords: any = async () => {
    // @ts-ignore
    try {
      // @ts-ignore
      const params = new URLSearchParams( as any);
      if (searchTerm) (params as any).append('search', searchTerm as any);
      if (statusFilter) (params as any).append('status', statusFilter as any);
      if (priorityFilter) (params as any).append('priority', priorityFilter as any);
      
      // @ts-ignore
      const response: any = await fetch(`/api/asset-maintenance/?${(params as any as any).toString( as any)}`);
      if ((response as any).ok) {
        // @ts-ignore
        const data: any = await (response as any).json( as any);
        setMaintenanceRecords((data as any as any).results || data);
      // @ts-ignore
      }
    // @ts-ignore
    } catch (error) {
      // @ts-ignore
      (console as any).error('Error fetching maintenance records:', error as any);
    // @ts-ignore
    } finally {
      // @ts-ignore
      setLoading(false as any);
    // @ts-ignore
    }
  // @ts-ignore
  };

  // @ts-ignore
  const fetchAssets: any = async () => {
    // @ts-ignore
    try {
      // @ts-ignore
      const response = await fetch('/api/assets/?is_active=true' as any);
      if ((response as any).ok) {
        // @ts-ignore
        const data: any = await (response as any).json( as any);
        setAssets((data as any as any).results || data);
      // @ts-ignore
      }
    // @ts-ignore
    } catch (error) {
      // @ts-ignore
      (console as any).error('Error fetching assets:', error as any);
    // @ts-ignore
    }
  // @ts-ignore
  };

  // @ts-ignore
  const fetchEmployees: any = async () => {
    // @ts-ignore
    try {
      // @ts-ignore
      const response = await fetch('/api/employees/' as any);
      if ((response as any).ok) {
        // @ts-ignore
        const data: any = await (response as any).json( as any);
        setEmployees((data as any as any).results || data);
      // @ts-ignore
      }
    // @ts-ignore
    } catch (error) {
      // @ts-ignore
      (console as any).error('Error fetching employees:', error as any);
    // @ts-ignore
    }
  // @ts-ignore
  };

  // @ts-ignore
  const handleAddMaintenance: any = async () => {
    // @ts-ignore
    try {
      // @ts-ignore
      const response = await fetch('/api/asset-maintenance/', {
        // @ts-ignore
        method: 'POST',
        headers: {
          // @ts-ignore
          'Content-Type': 'application/json',
        // @ts-ignore
        },
        body: (JSON as any as any).stringify({
          ...maintenanceForm,
          // @ts-ignore
          asset: parseInt((maintenanceForm as any as any).asset),
          performed_by: (maintenanceForm as any).performed_by && (maintenanceForm as any).performed_by !== '__not_specified__' ? parseInt((maintenanceForm as any as any).performed_by) : null,
        // @ts-ignore
        }),
      // @ts-ignore
      });

      if ((response as any).ok) {
        // @ts-ignore
        fetchMaintenanceRecords( as any);
        setIsAddDialogOpen(false as any);
        resetForm( as any);
      // @ts-ignore
      }
    // @ts-ignore
    } catch (error) {
      // @ts-ignore
      (console as any).error('Error adding maintenance:', error as any);
    // @ts-ignore
    }
  // @ts-ignore
  };

  // @ts-ignore
  const handleCompleteMaintenance: any = async () => {
    // @ts-ignore
    if (!selectedMaintenance) return;

    try {
      // @ts-ignore
      const response: any = await fetch(`/api/asset-maintenance/${(selectedMaintenance as any as any).id}/complete/`, {
        // @ts-ignore
        method: 'POST',
        headers: {
          // @ts-ignore
          'Content-Type': 'application/json',
        // @ts-ignore
        },
        body: (JSON as any).stringify(completionForm as any),
      // @ts-ignore
      });

      if ((response as any).ok) {
        // @ts-ignore
        fetchMaintenanceRecords( as any);
        setIsCompleteDialogOpen(false as any);
        setSelectedMaintenance(null as any);
        resetCompletionForm( as any);
      // @ts-ignore
      }
    // @ts-ignore
    } catch (error) {
      // @ts-ignore
      (console as any).error('Error completing maintenance:', error as any);
    // @ts-ignore
    }
  // @ts-ignore
  };

  // @ts-ignore
  const resetForm: any = (): void => {
    setMaintenanceForm({
      asset: '',
      maintenance_type: 'PREVENTIVE',
      title: '',
      description: '',
      scheduled_date: '',
      scheduled_time: '',
      priority: 'MEDIUM',
      estimated_cost: '',
      performed_by: '',
      external_vendor: ''
    // @ts-ignore
    } as any);
  // @ts-ignore
  };

  // @ts-ignore
  const resetCompletionForm: any = (): void => {
    setCompletionForm({
      work_performed: '',
      parts_used: '',
      issues_found: '',
      recommendations: '',
      actual_cost: '',
      next_maintenance_date: ''
    // @ts-ignore
    } as any);
  // @ts-ignore
  };

  // @ts-ignore
  const getStatusColor: any = (status: string): void => {
    // @ts-ignore
    const colors = {
      // @ts-ignore
      'SCHEDULED': 'bg-blue-100 text-blue-800',
      'IN_PROGRESS': 'bg-yellow-100 text-yellow-800',
      'COMPLETED': 'bg-green-100 text-green-800',
      'CANCELLED': 'bg-gray-100 text-gray-800',
      'OVERDUE': 'bg-red-100 text-red-800'
    // @ts-ignore
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  // @ts-ignore
  };

  // @ts-ignore
  const getPriorityColor: any = (priority: string): void => {
    // @ts-ignore
    const colors = {
      // @ts-ignore
      'LOW': 'text-green-600',
      'MEDIUM': 'text-yellow-600',
      'HIGH': 'text-orange-600',
      'CRITICAL': 'text-red-600'
    // @ts-ignore
    };
    return colors[priority as keyof typeof colors] || 'text-gray-600';
  // @ts-ignore
  };

  // @ts-ignore
  const formatCurrency: any = (amount: string | null, currency: string = 'SAR'): string => {
    // @ts-ignore
    if (!amount) return '-';
    // @ts-ignore
    return `${parseFloat(amount as any).toLocaleString( as any)} ${currency}`;
  // @ts-ignore
  };

  // @ts-ignore
  const filteredRecords: any = (maintenanceRecords as any).filter(record => {
    // @ts-ignore
    const matchesSearch = !searchTerm || 
      (record as any as any).title.toLowerCase( as any).includes((searchTerm as any as any).toLowerCase( as any)) ||
      (record as any).asset_name.toLowerCase( as any).includes((searchTerm as any as any).toLowerCase( as any)) ||
      (record as any).asset_id.toLowerCase( as any).includes((searchTerm as any as any).toLowerCase( as any));
    
    const matchesStatus: any = !statusFilter || (record as any).status === statusFilter;
    const matchesPriority: any = !priorityFilter || (record as any).priority === priorityFilter;
    
    return matchesSearch && matchesStatus && matchesPriority;
  // @ts-ignore
  });

  if (loading) {
    // @ts-ignore
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">
          {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
        </div>
      </div>
    );
  // @ts-ignore
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">
            {language === 'ar' ? 'إدارة الصيانة' : 'Maintenance Management'}
          </h1>
          <p className="text-muted-foreground">
            {language === 'ar' 
              ? 'جدولة وتتبع أعمال الصيانة للأصول'
              : 'Schedule and track asset maintenance activities'
            }
          </p>
        </div>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              {language === 'ar' ? 'جدولة صيانة' : 'Schedule Maintenance'}
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>
                {language === 'ar' ? 'جدولة صيانة جديدة' : 'Schedule New Maintenance'}
              </DialogTitle>
            </DialogHeader>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label>{language === 'ar' ? 'الأصل' : 'Asset'}</Label>
                <Select value={(maintenanceForm as any).asset} onValueChange={(value) => setMaintenanceForm({...maintenanceForm, asset: value} as any)}>
                  <SelectTrigger>
                    <SelectValue placeholder={language === 'ar' ? 'اختر الأصل' : 'Select asset'} />
                  </SelectTrigger>
                  <SelectContent>
                    {(assets as any).map(asset => (
                      // @ts-ignore
                      <SelectItem key={(asset as any as any).id} value={(asset as any).id.toString( as any)}>
                        {(asset as any).asset_id} - {(asset as any).name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label>{language === 'ar' ? 'نوع الصيانة' : 'Maintenance Type'}</Label>
                <Select value={(maintenanceForm as any).maintenance_type} onValueChange={(value) => setMaintenanceForm({...maintenanceForm, maintenance_type: value} as any)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="PREVENTIVE">
                      {language === 'ar' ? 'صيانة وقائية' : 'Preventive'}
                    </SelectItem>
                    <SelectItem value="CORRECTIVE">
                      {language === 'ar' ? 'صيانة تصحيحية' : 'Corrective'}
                    </SelectItem>
                    <SelectItem value="EMERGENCY">
                      {language === 'ar' ? 'إصلاح طارئ' : 'Emergency'}
                    </SelectItem>
                    <SelectItem value="INSPECTION">
                      {language === 'ar' ? 'فحص' : 'Inspection'}
                    </SelectItem>
                    <SelectItem value="CALIBRATION">
                      {language === 'ar' ? 'معايرة' : 'Calibration'}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="md:col-span-2">
                <Label>{language === 'ar' ? 'العنوان' : 'Title'}</Label>
                <Input
                  value={(maintenanceForm as any).title}
                  onChange={(e: any) => setMaintenanceForm({...maintenanceForm, title: (e as any as any).target.value})}
                  placeholder={language === 'ar' ? 'أدخل عنوان الصيانة' : 'Enter maintenance title'}
                />
              </div>
              <div className="md:col-span-2">
                <Label>{language === 'ar' ? 'الوصف' : 'Description'}</Label>
                <Textarea
                  value={(maintenanceForm as any).description}
                  onChange={(e: any) => setMaintenanceForm({...maintenanceForm, description: (e as any as any).target.value})}
                  placeholder={language === 'ar' ? 'أدخل وصف الصيانة' : 'Enter maintenance description'}
                  rows={3}
                />
              </div>
              <div>
                <Label>{language === 'ar' ? 'تاريخ الجدولة' : 'Scheduled Date'}</Label>
                <Input
                  type="date"
                  value={(maintenanceForm as any).scheduled_date}
                  onChange={(e: any) => setMaintenanceForm({...maintenanceForm, scheduled_date: (e as any as any).target.value})}
                />
              </div>
              <div>
                <Label>{language === 'ar' ? 'وقت الجدولة' : 'Scheduled Time'}</Label>
                <Input
                  type="time"
                  value={(maintenanceForm as any).scheduled_time}
                  onChange={(e: any) => setMaintenanceForm({...maintenanceForm, scheduled_time: (e as any as any).target.value})}
                />
              </div>
              <div>
                <Label>{language === 'ar' ? 'الأولوية' : 'Priority'}</Label>
                <Select value={(maintenanceForm as any).priority} onValueChange={(value) => setMaintenanceForm({...maintenanceForm, priority: value} as any)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="LOW">
                      {language === 'ar' ? 'منخفضة' : 'Low'}
                    </SelectItem>
                    <SelectItem value="MEDIUM">
                      {language === 'ar' ? 'متوسطة' : 'Medium'}
                    </SelectItem>
                    <SelectItem value="HIGH">
                      {language === 'ar' ? 'عالية' : 'High'}
                    </SelectItem>
                    <SelectItem value="CRITICAL">
                      {language === 'ar' ? 'حرجة' : 'Critical'}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label>{language === 'ar' ? 'التكلفة المقدرة' : 'Estimated Cost'}</Label>
                <Input
                  type="number"
                  step="(0 as any).01"
                  value={(maintenanceForm as any).estimated_cost}
                  onChange={(e: any) => setMaintenanceForm({...maintenanceForm, estimated_cost: (e as any as any).target.value})}
                  placeholder={language === 'ar' ? 'أدخل التكلفة المقدرة' : 'Enter estimated cost'}
                />
              </div>
              <div>
                <Label>{language === 'ar' ? 'المنفذ' : 'Performed By'}</Label>
                <Select value={(maintenanceForm as any).performed_by} onValueChange={(value) => setMaintenanceForm({...maintenanceForm, performed_by: value} as any)}>
                  <SelectTrigger>
                    <SelectValue placeholder={language === 'ar' ? 'اختر الموظف' : 'Select employee'} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="__not_specified__">
                      {language === 'ar' ? 'غير محدد' : 'Not specified'}
                    </SelectItem>
                    {(employees as any).map(employee => (
                      // @ts-ignore
                      <SelectItem key={(employee as any as any).id} value={(employee as any).id.toString( as any)}>
                        {(employee as any).user.first_name} {(employee as any).user.last_name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label>{language === 'ar' ? 'مقاول خارجي' : 'External Vendor'}</Label>
                <Input
                  value={(maintenanceForm as any).external_vendor}
                  onChange={(e: any) => setMaintenanceForm({...maintenanceForm, external_vendor: (e as any as any).target.value})}
                  placeholder={language === 'ar' ? 'اسم المقاول الخارجي' : 'External vendor name'}
                />
              </div>
            </div>
            <div className="flex justify-end gap-2 mt-4">
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false as any)}>
                {language === 'ar' ? 'إلغاء' : 'Cancel'}
              </Button>
              <Button onClick={handleAddMaintenance}>
                {language === 'ar' ? 'جدولة الصيانة' : 'Schedule Maintenance'}
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-[200px]">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder={language === 'ar' ? 'البحث في الصيانة...' : 'Search maintenance...'}
                  value={searchTerm}
                  onChange={(e: any) => setSearchTerm((e as any as any).target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder={language === 'ar' ? 'جميع الحالات' : 'All statuses'} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">
                  {language === 'ar' ? 'جميع الحالات' : 'All statuses'}
                </SelectItem>
                <SelectItem value="SCHEDULED">
                  {language === 'ar' ? 'مجدولة' : 'Scheduled'}
                </SelectItem>
                <SelectItem value="IN_PROGRESS">
                  {language === 'ar' ? 'قيد التنفيذ' : 'In Progress'}
                </SelectItem>
                <SelectItem value="COMPLETED">
                  {language === 'ar' ? 'مكتملة' : 'Completed'}
                </SelectItem>
                <SelectItem value="OVERDUE">
                  {language === 'ar' ? 'متأخرة' : 'Overdue'}
                </SelectItem>
              </SelectContent>
            </Select>
            <Select value={priorityFilter} onValueChange={setPriorityFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder={language === 'ar' ? 'جميع الأولويات' : 'All priorities'} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">
                  {language === 'ar' ? 'جميع الأولويات' : 'All priorities'}
                </SelectItem>
                <SelectItem value="LOW">
                  {language === 'ar' ? 'منخفضة' : 'Low'}
                </SelectItem>
                <SelectItem value="MEDIUM">
                  {language === 'ar' ? 'متوسطة' : 'Medium'}
                </SelectItem>
                <SelectItem value="HIGH">
                  {language === 'ar' ? 'عالية' : 'High'}
                </SelectItem>
                <SelectItem value="CRITICAL">
                  {language === 'ar' ? 'حرجة' : 'Critical'}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Maintenance Records Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Wrench className="h-5 w-5" />
            {language === 'ar' ? 'سجلات الصيانة' : 'Maintenance Records'}
            <Badge variant="secondary" className="ml-2">
              {(filteredRecords as any).length}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{language === 'ar' ? 'الأصل' : 'Asset'}</TableHead>
                  <TableHead>{language === 'ar' ? 'العنوان' : 'Title'}</TableHead>
                  <TableHead>{language === 'ar' ? 'النوع' : 'Type'}</TableHead>
                  <TableHead>{language === 'ar' ? 'التاريخ المجدول' : 'Scheduled Date'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الحالة' : 'Status'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الأولوية' : 'Priority'}</TableHead>
                  <TableHead>{language === 'ar' ? 'التكلفة' : 'Cost'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الإجراءات' : 'Actions'}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                // @ts-ignore
                {(filteredRecords as any).map((record as any) => (
                  <TableRow key={(record as any).id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{(record as any).asset_name}</div>
                        <div className="text-sm text-muted-foreground">{(record as any).asset_id}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{(record as any).title}</div>
                        <div className="text-sm text-muted-foreground">
                          {(record as any).performed_by_name || (record as any).external_vendor || (language === 'ar' ? 'غير محدد' : 'Not assigned')}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {language === 'ar' 
                          ? (record as any).maintenance_type === 'PREVENTIVE' ? 'وقائية' 
                            : (record as any).maintenance_type === 'CORRECTIVE' ? 'تصحيحية'
                            : (record as any).maintenance_type === 'EMERGENCY' ? 'طارئة'
                            : (record as any).maintenance_type
                          : (record as any).maintenance_type.replace('_', ' ' as any)
                        }
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        // @ts-ignore
                        <span>{new Date((record as any as any).scheduled_date).toLocaleDateString( as any)}</span>
                        {(record as any).is_overdue && (
                          <AlertTriangle className="h-4 w-4 text-red-500" />
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={getStatusColor((record as any as any).status)}>
                        {language === 'ar' 
                          ? (record as any).status === 'SCHEDULED' ? 'مجدولة' 
                            : (record as any).status === 'IN_PROGRESS' ? 'قيد التنفيذ'
                            : (record as any).status === 'COMPLETED' ? 'مكتملة'
                            : (record as any).status === 'OVERDUE' ? 'متأخرة'
                            : (record as any).status
                          : (record as any).status.replace('_', ' ' as any)
                        }
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <span className={getPriorityColor((record as any as any).priority)}>
                        {language === 'ar' 
                          ? (record as any).priority === 'LOW' ? 'منخفضة' 
                            : (record as any).priority === 'MEDIUM' ? 'متوسطة'
                            : (record as any).priority === 'HIGH' ? 'عالية'
                            : (record as any).priority === 'CRITICAL' ? 'حرجة'
                            : (record as any).priority
                          : (record as any).priority
                        }
                      </span>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">
                          {formatCurrency((record as any as any).estimated_cost, (record as any).currency_code)}
                        </div>
                        {(record as any).actual_cost && (
                          <div className="text-sm text-muted-foreground">
                            {language === 'ar' ? 'فعلي: ' : 'Actual: '}
                            {formatCurrency((record as any as any).actual_cost, (record as any).currency_code)}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                        {(record as any).status === 'IN_PROGRESS' && (
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => {
                              setSelectedMaintenance(record as any);
                              setIsCompleteDialogOpen(true as any);
                            }}
                          >
                            <CheckCircle className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                // @ts-ignore
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Complete Maintenance Dialog */}
      <Dialog open={isCompleteDialogOpen} onOpenChange={setIsCompleteDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              {language === 'ar' ? 'إكمال الصيانة' : 'Complete Maintenance'}
            </DialogTitle>
          </DialogHeader>
          {selectedMaintenance && (
            <div className="space-y-4">
              <div className="p-4 bg-muted rounded-lg">
                <div className="font-medium">{(selectedMaintenance as any).title}</div>
                <div className="text-sm text-muted-foreground">
                  {(selectedMaintenance as any).asset_name} ({(selectedMaintenance as any).asset_id})
                </div>
              </div>
              
              <div>
                <Label>{language === 'ar' ? 'العمل المنجز' : 'Work Performed'}</Label>
                <Textarea
                  value={(completionForm as any).work_performed}
                  onChange={(e: any) => setCompletionForm({...completionForm, work_performed: (e as any as any).target.value})}
                  placeholder={language === 'ar' ? 'وصف العمل المنجز' : 'Describe work performed'}
                  rows={3}
                />
              </div>
              
              <div>
                <Label>{language === 'ar' ? 'القطع المستخدمة' : 'Parts Used'}</Label>
                <Textarea
                  value={(completionForm as any).parts_used}
                  onChange={(e: any) => setCompletionForm({...completionForm, parts_used: (e as any as any).target.value})}
                  placeholder={language === 'ar' ? 'قائمة القطع المستخدمة' : 'List of parts used'}
                  rows={2}
                />
              </div>
              
              <div>
                <Label>{language === 'ar' ? 'المشاكل المكتشفة' : 'Issues Found'}</Label>
                <Textarea
                  value={(completionForm as any).issues_found}
                  onChange={(e: any) => setCompletionForm({...completionForm, issues_found: (e as any as any).target.value})}
                  placeholder={language === 'ar' ? 'المشاكل التي تم اكتشافها' : 'Issues discovered during maintenance'}
                  rows={2}
                />
              </div>
              
              <div>
                <Label>{language === 'ar' ? 'التوصيات' : 'Recommendations'}</Label>
                <Textarea
                  value={(completionForm as any).recommendations}
                  onChange={(e: any) => setCompletionForm({...completionForm, recommendations: (e as any as any).target.value})}
                  placeholder={language === 'ar' ? 'توصيات للصيانة المستقبلية' : 'Recommendations for future maintenance'}
                  rows={2}
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>{language === 'ar' ? 'التكلفة الفعلية' : 'Actual Cost'}</Label>
                  <Input
                    type="number"
                    step="(0 as any).01"
                    value={(completionForm as any).actual_cost}
                    onChange={(e: any) => setCompletionForm({...completionForm, actual_cost: (e as any as any).target.value})}
                    placeholder={language === 'ar' ? 'التكلفة الفعلية' : 'Actual cost'}
                  />
                </div>
                <div>
                  <Label>{language === 'ar' ? 'تاريخ الصيانة القادمة' : 'Next Maintenance Date'}</Label>
                  <Input
                    type="date"
                    value={(completionForm as any).next_maintenance_date}
                    onChange={(e: any) => setCompletionForm({...completionForm, next_maintenance_date: (e as any as any).target.value})}
                  />
                </div>
              </div>
              
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setIsCompleteDialogOpen(false as any)}>
                  {language === 'ar' ? 'إلغاء' : 'Cancel'}
                </Button>
                <Button onClick={handleCompleteMaintenance}>
                  {language === 'ar' ? 'إكمال الصيانة' : 'Complete Maintenance'}
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
// @ts-ignore
};

export default MaintenanceManagement;
