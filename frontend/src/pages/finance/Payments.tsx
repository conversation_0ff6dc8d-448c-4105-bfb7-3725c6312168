/**
 * Payments Page - Payment Management for AP and AR
 * Manages both vendor payments and customer payments
 */

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  Search, 
  Plus, 
  DollarSign, 
  Calendar,
  CreditCard,
  Building,
  Users,
  ArrowUpRight,
  ArrowDownLeft,
  Banknote,
  Smartphone,
  CheckCircle
} from 'lucide-react'

interface PaymentsProps {
  language: 'ar' | 'en'
}

interface Payment {
  id: number
  payment_number: string
  payment_type: 'VENDOR_PAYMENT' | 'CUSTOMER_PAYMENT'
  payment_method: 'CASH' | 'CHECK' | 'BANK_TRANSFER' | 'CREDIT_CARD' | 'ONLINE'
  payment_date: string
  amount: number
  currency: string
  exchange_rate: number
  vendor_invoice?: number
  vendor_invoice_number?: string
  customer_invoice?: number
  customer_invoice_number?: string
  bank_account?: string
  check_number?: string
  reference_number?: string
  description?: string
  created_by_name: string
  created_at: string
}

const translations = {
  ar: {
    payments: 'المدفوعات',
    addPayment: 'إضافة دفعة',
    searchPayments: 'البحث في المدفوعات...',
    paymentNumber: 'رقم الدفعة',
    paymentType: 'نوع الدفعة',
    paymentMethod: 'طريقة الدفع',
    paymentDate: 'تاريخ الدفع',
    amount: 'المبلغ',
    reference: 'المرجع',
    description: 'الوصف',
    createdBy: 'أنشأ بواسطة',
    // Payment types
    vendorPayment: 'دفعة مورد',
    customerPayment: 'دفعة عميل',
    // Payment methods
    cash: 'نقد',
    check: 'شيك',
    bankTransfer: 'تحويل بنكي',
    creditCard: 'بطاقة ائتمان',
    online: 'دفع إلكتروني',
    // Summary cards
    totalPayments: 'إجمالي المدفوعات',
    vendorPayments: 'مدفوعات الموردين',
    customerPayments: 'مدفوعات العملاء',
    netCashFlow: 'صافي التدفق النقدي',
    noPaymentsFound: 'لم يتم العثور على مدفوعات',
    allTypes: 'جميع الأنواع',
    allMethods: 'جميع الطرق',
    todaysPayments: 'مدفوعات اليوم',
    thisWeek: 'هذا الأسبوع',
    thisMonth: 'هذا الشهر'
  },
  en: {
    payments: 'Payments',
    addPayment: 'Add Payment',
    searchPayments: 'Search payments...',
    paymentNumber: 'Payment Number',
    paymentType: 'Payment Type',
    paymentMethod: 'Payment Method',
    paymentDate: 'Payment Date',
    amount: 'Amount',
    reference: 'Reference',
    description: 'Description',
    createdBy: 'Created By',
    // Payment types
    vendorPayment: 'Vendor Payment',
    customerPayment: 'Customer Payment',
    // Payment methods
    cash: 'Cash',
    check: 'Check',
    bankTransfer: 'Bank Transfer',
    creditCard: 'Credit Card',
    online: 'Online Payment',
    // Summary cards
    totalPayments: 'Total Payments',
    vendorPayments: 'Vendor Payments',
    customerPayments: 'Customer Payments',
    netCashFlow: 'Net Cash Flow',
    noPaymentsFound: 'No payments found',
    allTypes: 'All Types',
    allMethods: 'All Methods',
    todaysPayments: "Today's Payments",
    thisWeek: 'This Week',
    thisMonth: 'This Month'
  }
}

// @ts-ignore
const Payments: (React as any).FC<PaymentsProps> = ({ language }) => {
  // @ts-ignore
  const t = translations[language]
  // @ts-ignore
  const [payments, setPayments] = useState<Payment[]>([])
  const [loading, setLoading] = useState(true as any)
  const [searchTerm, setSearchTerm] = useState('' as any)
  // @ts-ignore
  const [typeFilter, setTypeFilter] = useState<string>('all')
  // @ts-ignore
  const [methodFilter, setMethodFilter] = useState<string>('all')

  // @ts-ignore
  useEffect(( as any) => {
    // @ts-ignore
    fetchPayments( as any)
  }, [])

  // @ts-ignore
  const fetchPayments = async () => {
    // @ts-ignore
    try {
      setLoading(true as any)
      // @ts-ignore
      const token = (localStorage as any).getItem('token' as any)
      const response = await fetch('/api/payments/', {
        // @ts-ignore
        headers: {
          // @ts-ignore
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        // @ts-ignore
        },
      // @ts-ignore
      } as any)
      
      if ((response as any).ok) {
        // @ts-ignore
        const data = await (response as any).json( as any)
        setPayments((data as any as any).results || data)
      // @ts-ignore
      }
    // @ts-ignore
    } catch (error) {
      (console as any).error('Error fetching payments:', error as any)
    } finally {
      setLoading(false as any)
    }
  // @ts-ignore
  }

  // @ts-ignore
  const filteredPayments = (payments as any).filter(payment => {
    // @ts-ignore
    const matchesSearch = 
      (payment as any as any).payment_number.toLowerCase( as any).includes((searchTerm as any as any).toLowerCase( as any)) ||
      (payment as any).reference_number?.toLowerCase( as any).includes((searchTerm as any as any).toLowerCase( as any)) ||
      (payment as any).description?.toLowerCase( as any).includes((searchTerm as any as any).toLowerCase( as any)) ||
      (payment as any).created_by_name.toLowerCase( as any).includes((searchTerm as any as any).toLowerCase( as any))
    
    const matchesType = typeFilter === 'all' || (payment as any).payment_type === typeFilter
    const matchesMethod = methodFilter === 'all' || (payment as any).payment_method === methodFilter
    
    return matchesSearch && matchesType && matchesMethod
  // @ts-ignore
  })

  // @ts-ignore
  const formatCurrency = (amount: number): string => {
    // @ts-ignore
    return new (Intl as any).NumberFormat('ar-SA', {
      // @ts-ignore
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 2,
    // @ts-ignore
    } as any).format(amount as any)
  // @ts-ignore
  }

  // @ts-ignore
  const formatDate = (dateString: string): string => {
    // @ts-ignore
    return new Date(dateString as any).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US' as any)
  // @ts-ignore
  }

  // @ts-ignore
  const getPaymentTypeIcon = (type: string): void => {
    // @ts-ignore
    return type === 'VENDOR_PAYMENT' ? 
      <ArrowUpRight className="h-4 w-4 text-red-600" /> : 
      <ArrowDownLeft className="h-4 w-4 text-green-600" />
  // @ts-ignore
  }

  // @ts-ignore
  const getPaymentMethodIcon = (method: string): void => {
    // @ts-ignore
    switch (method) {
      // @ts-ignore
      case 'CASH':
        return <Banknote className="h-4 w-4 text-green-600" />
      case 'CHECK':
        return <CheckCircle className="h-4 w-4 text-blue-600" />
      case 'BANK_TRANSFER':
        return <Building className="h-4 w-4 text-purple-600" />
      case 'CREDIT_CARD':
        return <CreditCard className="h-4 w-4 text-orange-600" />
      case 'ONLINE':
        return <Smartphone className="h-4 w-4 text-indigo-600" />
      default:
        return <DollarSign className="h-4 w-4 text-gray-600" />
    // @ts-ignore
    }
  // @ts-ignore
  }

  // @ts-ignore
  const getPaymentTypeBadge = (type: string): void => {
    // @ts-ignore
    return type === 'VENDOR_PAYMENT' ? 
      <Badge variant="outline" className="text-red-600 border-red-600">{(t as any).vendorPayment}</Badge> :
      <Badge variant="outline" className="text-green-600 border-green-600">{(t as any).customerPayment}</Badge>
  // @ts-ignore
  }

  // @ts-ignore
  const getPaymentMethodText = (method: string): void => {
    // @ts-ignore
    switch (method) {
      // @ts-ignore
      case 'CASH': return (t as any).cash
      case 'CHECK': return (t as any).check
      case 'BANK_TRANSFER': return (t as any).bankTransfer
      case 'CREDIT_CARD': return (t as any).creditCard
      case 'ONLINE': return (t as any).online
      default: return method
    // @ts-ignore
    }
  // @ts-ignore
  }

  // Calculate summary statistics
  const totalPayments = (payments as any).length
  const vendorPayments = payments
    // @ts-ignore
    .filter(p => (p as any as any).payment_type === 'VENDOR_PAYMENT')
    // @ts-ignore
    .reduce((sum, p as any) => sum + (p as any).amount, 0)
  const customerPayments = payments
    // @ts-ignore
    .filter(p => (p as any as any).payment_type === 'CUSTOMER_PAYMENT')
    // @ts-ignore
    .reduce((sum, p as any) => sum + (p as any).amount, 0)
  const netCashFlow = customerPayments - vendorPayments

  // Today's payments
  const today = new Date( as any).toISOString( as any).split('T' as any)[0]
  const todaysPayments = payments
    // @ts-ignore
    .filter(p => (p as any as any).payment_date === today)
    // @ts-ignore
    .reduce((sum, p as any) => sum + (p as any).amount, 0)

  if (loading) {
    // @ts-ignore
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  // @ts-ignore
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">{(t as any).payments}</h1>
          <p className="text-gray-600 mt-1">
            {language === 'ar' ? 'إدارة المدفوعات والتدفق النقدي' : 'Payment Management & Cash Flow'}
          </p>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          {(t as any).addPayment}
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{(t as any).totalPayments}</p>
                <p className="text-2xl font-bold mt-1">{totalPayments}</p>
              </div>
              <DollarSign className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{(t as any).vendorPayments}</p>
                <p className="text-2xl font-bold mt-1 text-red-600">{formatCurrency(vendorPayments as any)}</p>
              </div>
              <ArrowUpRight className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{(t as any).customerPayments}</p>
                <p className="text-2xl font-bold mt-1 text-green-600">{formatCurrency(customerPayments as any)}</p>
              </div>
              <ArrowDownLeft className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{(t as any).netCashFlow}</p>
                <p className={`text-2xl font-bold mt-1 ${netCashFlow >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {formatCurrency(netCashFlow as any)}
                </p>
              </div>
              <div className={`h-8 w-8 ${netCashFlow >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {netCashFlow >= 0 ? <ArrowDownLeft /> : <ArrowUpRight />}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder={(t as any).searchPayments}
                  value={searchTerm}
                  onChange={(e: any) => setSearchTerm((e as any as any).target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="sm:w-48">
              <select
                value={typeFilter}
                onChange={(e: any) => setTypeFilter((e as any as any).target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">{(t as any).allTypes}</option>
                <option value="VENDOR_PAYMENT">{(t as any).vendorPayment}</option>
                <option value="CUSTOMER_PAYMENT">{(t as any).customerPayment}</option>
              </select>
            </div>
            <div className="sm:w-48">
              <select
                value={methodFilter}
                onChange={(e: any) => setMethodFilter((e as any as any).target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">{(t as any).allMethods}</option>
                <option value="CASH">{(t as any).cash}</option>
                <option value="CHECK">{(t as any).check}</option>
                <option value="BANK_TRANSFER">{(t as any).bankTransfer}</option>
                <option value="CREDIT_CARD">{(t as any).creditCard}</option>
                <option value="ONLINE">{(t as any).online}</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Payments Table */}
      <Card>
        <CardHeader>
          <CardTitle>{(t as any).payments}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-4 font-medium text-gray-900">{(t as any).paymentNumber}</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">{(t as any).paymentType}</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">{(t as any).paymentMethod}</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">{(t as any).paymentDate}</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900">{(t as any).amount}</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">{(t as any).reference}</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">{(t as any).createdBy}</th>
                </tr>
              </thead>
              <tbody>
                // @ts-ignore
                {(filteredPayments as any).map((payment as any) => (
                  <tr key={(payment as any).id} className="border-b hover:bg-gray-50">
                    <td className="py-3 px-4">
                      <code className="bg-gray-100 px-2 py-1 rounded text-sm">
                        {(payment as any).payment_number}
                      </code>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center gap-2">
                        {getPaymentTypeIcon((payment as any as any).payment_type)}
                        {getPaymentTypeBadge((payment as any as any).payment_type)}
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center gap-2">
                        {getPaymentMethodIcon((payment as any as any).payment_method)}
                        {getPaymentMethodText((payment as any as any).payment_method)}
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-gray-400" />
                        {formatDate((payment as any as any).payment_date)}
                      </div>
                    </td>
                    <td className="py-3 px-4 text-right font-mono">
                      <span className={(payment as any).payment_type === 'VENDOR_PAYMENT' ? 'text-red-600' : 'text-green-600'}>
                        {(payment as any).payment_type === 'VENDOR_PAYMENT' ? '-' : '+'}
                        {formatCurrency((payment as any as any).amount)}
                      </span>
                    </td>
                    <td className="py-3 px-4">
                      <div>
                        {(payment as any).reference_number && (
                          <p className="text-sm font-medium">{(payment as any).reference_number}</p>
                        )}
                        {(payment as any).vendor_invoice_number && (
                          <p className="text-xs text-gray-500">Invoice: {(payment as any).vendor_invoice_number}</p>
                        )}
                        {(payment as any).customer_invoice_number && (
                          <p className="text-xs text-gray-500">Invoice: {(payment as any).customer_invoice_number}</p>
                        )}
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4 text-gray-400" />
                        {(payment as any).created_by_name}
                      </div>
                    </td>
                  </tr>
                // @ts-ignore
                ))}
              </tbody>
            </table>
          </div>
          
          {(filteredPayments as any).length === 0 && (
            <div className="text-center py-8">
              <DollarSign className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">{(t as any).noPaymentsFound}</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
// @ts-ignore
}

// @ts-ignore
export default Payments