/**
 * Vendors Page - Accounts Payable Vendor Management
 * Manages vendor information for AP processing
 */

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  Search, 
  Plus, 
  Users, 
  Mail,
  Phone,
  Building,
  CreditCard,
  CheckCircle,
  XCircle
} from 'lucide-react'

interface VendorsProps {
  language: 'ar' | 'en'
}

interface Vendor {
  id: number
  vendor_code: string
  company_name: string
  company_name_ar: string
  contact_person: string
  email: string
  phone: string
  address: string
  tax_id: string
  payment_terms: string
  credit_limit: number
  currency: string
  is_active: boolean
  is_approved: boolean
  outstanding_balance: number
  invoices_count: number
}

const translations = {
  ar: {
    vendors: 'الموردين',
    addVendor: 'إضافة مورد',
    searchVendors: 'البحث في الموردين...',
    vendorCode: 'رمز المورد',
    companyName: 'اسم الشركة',
    contactPerson: 'الشخص المسؤول',
    email: 'البريد الإلكتروني',
    phone: 'الهاتف',
    paymentTerms: 'شروط الدفع',
    creditLimit: 'حد الائتمان',
    outstandingBalance: 'الرصيد المستحق',
    status: 'الحالة',
    active: 'نشط',
    inactive: 'غير نشط',
    approved: 'معتمد',
    pending: 'في الانتظار',
    noVendorsFound: 'لم يتم العثور على موردين',
    totalVendors: 'إجمالي الموردين',
    activeVendors: 'الموردين النشطين',
    totalOutstanding: 'إجمالي المستحق',
    averageCredit: 'متوسط حد الائتمان'
  },
  en: {
    vendors: 'Vendors',
    addVendor: 'Add Vendor',
    searchVendors: 'Search vendors...',
    vendorCode: 'Vendor Code',
    companyName: 'Company Name',
    contactPerson: 'Contact Person',
    email: 'Email',
    phone: 'Phone',
    paymentTerms: 'Payment Terms',
    creditLimit: 'Credit Limit',
    outstandingBalance: 'Outstanding Balance',
    status: 'Status',
    active: 'Active',
    inactive: 'Inactive',
    approved: 'Approved',
    pending: 'Pending',
    noVendorsFound: 'No vendors found',
    totalVendors: 'Total Vendors',
    activeVendors: 'Active Vendors',
    totalOutstanding: 'Total Outstanding',
    averageCredit: 'Average Credit Limit'
  }
}

const Vendors: React.FC<VendorsProps> = ({ language }) => {
  const t = translations[language]
  const [vendors, setVendors] = useState<Vendor[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')

  useEffect(() => {
    fetchVendors()
  }, [])

  const fetchVendors = async () => {
    try {
      setLoading(true)
      const token = localStorage.getItem('token')
      const response = await fetch('/api/vendors/', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      })
      
      if (response.ok) {
        const data = await response.json()
        setVendors(data.results || data)
      }
    } catch (error) {
      console.error('Error fetching vendors:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredVendors = vendors.filter(vendor =>
    vendor.company_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    vendor.vendor_code.toLowerCase().includes(searchTerm.toLowerCase()) ||
    vendor.contact_person.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 2,
    }).format(amount)
  }

  // Calculate summary statistics
  const totalVendors = vendors.length
  const activeVendors = vendors.filter(v => v.is_active).length
  const totalOutstanding = vendors.reduce((sum, v) => sum + v.outstanding_balance, 0)
  const averageCredit = vendors.length > 0 ? vendors.reduce((sum, v) => sum + v.credit_limit, 0) / vendors.length : 0

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">{t.vendors}</h1>
          <p className="text-gray-600 mt-1">
            {language === 'ar' ? 'إدارة الموردين والحسابات الدائنة' : 'Vendor Management & Accounts Payable'}
          </p>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          {t.addVendor}
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{t.totalVendors}</p>
                <p className="text-2xl font-bold mt-1">{totalVendors}</p>
              </div>
              <Users className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{t.activeVendors}</p>
                <p className="text-2xl font-bold mt-1">{activeVendors}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{t.totalOutstanding}</p>
                <p className="text-2xl font-bold mt-1">{formatCurrency(totalOutstanding)}</p>
              </div>
              <CreditCard className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{t.averageCredit}</p>
                <p className="text-2xl font-bold mt-1">{formatCurrency(averageCredit)}</p>
              </div>
              <Building className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search */}
      <Card>
        <CardContent className="p-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder={t.searchVendors}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      {/* Vendors Table */}
      <Card>
        <CardHeader>
          <CardTitle>{t.vendors}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-4 font-medium text-gray-900">{t.vendorCode}</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">{t.companyName}</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">{t.contactPerson}</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">{t.email}</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">{t.paymentTerms}</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900">{t.outstandingBalance}</th>
                  <th className="text-center py-3 px-4 font-medium text-gray-900">{t.status}</th>
                </tr>
              </thead>
              <tbody>
                {filteredVendors.map((vendor) => (
                  <tr key={vendor.id} className="border-b hover:bg-gray-50">
                    <td className="py-3 px-4">
                      <code className="bg-gray-100 px-2 py-1 rounded text-sm">
                        {vendor.vendor_code}
                      </code>
                    </td>
                    <td className="py-3 px-4">
                      <div>
                        <span className="font-medium">{vendor.company_name}</span>
                        {vendor.company_name_ar && (
                          <p className="text-sm text-gray-500">{vendor.company_name_ar}</p>
                        )}
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4 text-gray-400" />
                        {vendor.contact_person}
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4 text-gray-400" />
                        {vendor.email}
                      </div>
                    </td>
                    <td className="py-3 px-4">{vendor.payment_terms}</td>
                    <td className="py-3 px-4 text-right font-mono">
                      {formatCurrency(vendor.outstanding_balance)}
                    </td>
                    <td className="py-3 px-4 text-center">
                      <div className="flex flex-col gap-1">
                        <Badge variant={vendor.is_active ? "default" : "secondary"}>
                          {vendor.is_active ? t.active : t.inactive}
                        </Badge>
                        <Badge variant={vendor.is_approved ? "default" : "outline"}>
                          {vendor.is_approved ? t.approved : t.pending}
                        </Badge>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          {filteredVendors.length === 0 && (
            <div className="text-center py-8">
              <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">{t.noVendorsFound}</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

export default Vendors