/**
 * Profit & Loss Report (Income Statement)
 * Comprehensive P&L statement with period comparison and drill-down capabilities
 */

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  Calendar,
  Download,
  Printer,
  TrendingUp,
  TrendingDown,
  DollarSign,
  BarChart3,
  FileText,
  Filter,
  RefreshCw
} from 'lucide-react'

interface ProfitLossReportProps {
  language: 'ar' | 'en'
}

interface PLLineItem {
  account_code: string
  account_name: string
  current_period: number
  previous_period: number
  variance: number
  variance_percent: number
  account_type: 'REVENUE' | 'EXPENSE' | 'COGS'
  is_subtotal?: boolean
  is_total?: boolean
  level: number
}

interface PLReport {
  company_name: string
  report_period: string
  comparison_period: string
  currency: string
  revenue: PLLineItem[]
  cost_of_goods_sold: PLLineItem[]
  gross_profit: PLLineItem
  operating_expenses: PLLineItem[]
  operating_income: PLLineItem
  other_income: PLLineItem[]
  other_expenses: PLLineItem[]
  net_income_before_tax: PLLineItem
  tax_expense: PLLineItem
  net_income: PLLineItem
  generated_at: string
}

const translations = {
  ar: {
    profitLossReport: 'قائمة الدخل',
    incomeStatement: 'بيان الأرباح والخسائر',
    forPeriod: 'للفترة',
    comparedTo: 'مقارنة بـ',
    revenue: 'الإيرادات',
    totalRevenue: 'إجمالي الإيرادات',
    costOfGoodsSold: 'تكلفة البضاعة المباعة',
    grossProfit: 'إجمالي الربح',
    operatingExpenses: 'المصروفات التشغيلية',
    operatingIncome: 'الدخل التشغيلي',
    otherIncome: 'إيرادات أخرى',
    otherExpenses: 'مصروفات أخرى',
    netIncomeBeforeTax: 'صافي الدخل قبل الضريبة',
    taxExpense: 'مصروف الضريبة',
    netIncome: 'صافي الدخل',
    currentPeriod: 'الفترة الحالية',
    previousPeriod: 'الفترة السابقة',
    variance: 'التغيير',
    variancePercent: 'نسبة التغيير',
    accountCode: 'رمز الحساب',
    accountName: 'اسم الحساب',
    amount: 'المبلغ',
    // Actions
    downloadPDF: 'تحميل PDF',
    print: 'طباعة',
    export: 'تصدير',
    refresh: 'تحديث',
    selectPeriod: 'اختيار الفترة',
    // Periods
    thisMonth: 'هذا الشهر',
    lastMonth: 'الشهر الماضي',
    thisQuarter: 'هذا الربع',
    lastQuarter: 'الربع الماضي',
    thisYear: 'هذا العام',
    lastYear: 'العام الماضي',
    custom: 'مخصص',
    // Status
    loading: 'جاري التحميل...',
    noData: 'لا توجد بيانات',
    generatedAt: 'تم إنشاؤه في',
    // Metrics
    grossProfitMargin: 'هامش الربح الإجمالي',
    operatingMargin: 'هامش التشغيل',
    netProfitMargin: 'هامش الربح الصافي',
    revenueGrowth: 'نمو الإيرادات',
    // Formatting
    increase: 'زيادة',
    decrease: 'انخفاض',
    favorable: 'إيجابي',
    unfavorable: 'سلبي'
  },
  en: {
    profitLossReport: 'Profit & Loss Report',
    incomeStatement: 'Income Statement',
    forPeriod: 'For Period',
    comparedTo: 'Compared to',
    revenue: 'Revenue',
    totalRevenue: 'Total Revenue',
    costOfGoodsSold: 'Cost of Goods Sold',
    grossProfit: 'Gross Profit',
    operatingExpenses: 'Operating Expenses',
    operatingIncome: 'Operating Income',
    otherIncome: 'Other Income',
    otherExpenses: 'Other Expenses',
    netIncomeBeforeTax: 'Net Income Before Tax',
    taxExpense: 'Tax Expense',
    netIncome: 'Net Income',
    currentPeriod: 'Current Period',
    previousPeriod: 'Previous Period',
    variance: 'Variance',
    variancePercent: 'Variance %',
    accountCode: 'Account Code',
    accountName: 'Account Name',
    amount: 'Amount',
    // Actions
    downloadPDF: 'Download PDF',
    print: 'Print',
    export: 'Export',
    refresh: 'Refresh',
    selectPeriod: 'Select Period',
    // Periods
    thisMonth: 'This Month',
    lastMonth: 'Last Month',
    thisQuarter: 'This Quarter',
    lastQuarter: 'Last Quarter',
    thisYear: 'This Year',
    lastYear: 'Last Year',
    custom: 'Custom',
    // Status
    loading: 'Loading...',
    noData: 'No data available',
    generatedAt: 'Generated at',
    // Metrics
    grossProfitMargin: 'Gross Profit Margin',
    operatingMargin: 'Operating Margin',
    netProfitMargin: 'Net Profit Margin',
    revenueGrowth: 'Revenue Growth',
    // Formatting
    increase: 'Increase',
    decrease: 'Decrease',
    favorable: 'Favorable',
    unfavorable: 'Unfavorable'
  }
}

// @ts-ignore
const ProfitLossReport: (React as any).FC<ProfitLossReportProps> = ({ language }) => {
  // @ts-ignore
  const t = translations[language]
  // @ts-ignore
  const [report, setReport] = useState<PLReport | null>(null)
  const [loading, setLoading] = useState(true as any)
  const [selectedPeriod, setSelectedPeriod] = useState('thisMonth' as any)
  const [comparisonPeriod, setComparisonPeriod] = useState('lastMonth' as any)

  // @ts-ignore
  useEffect(( as any) => {
    // @ts-ignore
    fetchReport( as any)
  }, [selectedPeriod, comparisonPeriod])

  // @ts-ignore
  const fetchReport = async () => {
    // @ts-ignore
    try {
      setLoading(true as any)
      // @ts-ignore
      const token = (localStorage as any).getItem('token' as any)
      const response = await fetch(`/api/financial-reports/profit-loss/?period=${selectedPeriod}&comparison=${comparisonPeriod}`, {
        // @ts-ignore
        headers: {
          // @ts-ignore
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        // @ts-ignore
        },
      // @ts-ignore
      } as any)
      
      if ((response as any).ok) {
        // @ts-ignore
        const data = await (response as any).json( as any)
        setReport(data as any)
      // @ts-ignore
      }
    // @ts-ignore
    } catch (error) {
      (console as any).error('Error fetching P&L report:', error as any)
    } finally {
      setLoading(false as any)
    }
  // @ts-ignore
  }

  // @ts-ignore
  const formatCurrency = (amount: number): string => {
    // @ts-ignore
    return new (Intl as any).NumberFormat('ar-SA', {
      // @ts-ignore
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 2,
    // @ts-ignore
    } as any).format(amount as any)
  // @ts-ignore
  }

  // @ts-ignore
  const formatPercent = (percent: number): string => {
    // @ts-ignore
    return new (Intl as any).NumberFormat('ar-SA', {
      // @ts-ignore
      style: 'percent',
      minimumFractionDigits: 1,
      maximumFractionDigits: 1,
    // @ts-ignore
    } as any).format(percent / 100 as any)
  // @ts-ignore
  }

  // @ts-ignore
  const getVarianceColor = (variance: number, isRevenue: boolean = false): void => {
    // @ts-ignore
    if (variance === 0) return 'text-gray-500'
    
    // For revenue, positive variance is good (green)
    // For expenses, negative variance is good (green)
    // @ts-ignore
    const isPositive = variance > 0
    const isFavorable = isRevenue ? isPositive : !isPositive
    
    return isFavorable ? 'text-green-600' : 'text-red-600'
  // @ts-ignore
  }

  // @ts-ignore
  const getVarianceIcon = (variance: number): void => {
    // @ts-ignore
    if (variance === 0) return null
    // @ts-ignore
    return variance > 0 ? 
      <TrendingUp className="h-4 w-4 inline ml-1" /> : 
      <TrendingDown className="h-4 w-4 inline ml-1" />
  // @ts-ignore
  }

  // @ts-ignore
  const renderLineItem = (item: PLLineItem, isRevenue: boolean = false): void => {
    // @ts-ignore
    const indentClass = `pl-${(item as any).level * 4}`
    const fontWeight = (item as any).is_total ? 'font-bold' : (item as any).is_subtotal ? 'font-semibold' : 'font-normal'
    const borderClass = (item as any).is_total ? 'border-t-2 border-b-2 border-gray-800' : 
                       (item as any).is_subtotal ? 'border-t border-gray-400' : ''

    return (
      <tr key={(item as any).account_code} className={`${borderClass} hover:bg-gray-50`}>
        <td className={`py-2 px-4 ${indentClass} ${fontWeight}`}>
          {(item as any).account_code && (
            <span className="text-gray-500 text-sm mr-2">{(item as any).account_code}</span>
          )}
          {(item as any).account_name}
        </td>
        <td className={`py-2 px-4 text-right font-mono ${fontWeight}`}>
          {formatCurrency((item as any as any).current_period)}
        </td>
        <td className={`py-2 px-4 text-right font-mono ${fontWeight}`}>
          {formatCurrency((item as any as any).previous_period)}
        </td>
        <td className={`py-2 px-4 text-right font-mono ${fontWeight} ${getVarianceColor((item as any as any).variance, isRevenue)}`}>
          {formatCurrency((Math as any as any).abs((item as any as any).variance))}
          {getVarianceIcon((item as any as any).variance)}
        </td>
        <td className={`py-2 px-4 text-right ${fontWeight} ${getVarianceColor((item as any as any).variance, isRevenue)}`}>
          {(item as any).variance_percent !== 0 && formatPercent((Math as any as any).abs((item as any as any).variance_percent))}
        </td>
      </tr>
    )
  // @ts-ignore
  }

  if (loading) {
    // @ts-ignore
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  // @ts-ignore
  }

  if (!report) {
    // @ts-ignore
    return (
      <div className="text-center py-8">
        <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-500">{(t as any).noData}</p>
      </div>
    )
  // @ts-ignore
  }

  // Calculate key ratios
  // @ts-ignore
  const totalRevenue = (report as any).revenue.reduce((sum, item as any) => sum + (item as any).current_period, 0)
  // @ts-ignore
  const grossProfitMargin = totalRevenue > 0 ? ((report as any).gross_profit.current_period / totalRevenue) * 100 : 0
  // @ts-ignore
  const operatingMargin = totalRevenue > 0 ? ((report as any).operating_income.current_period / totalRevenue) * 100 : 0
  // @ts-ignore
  const netProfitMargin = totalRevenue > 0 ? ((report as any).net_income.current_period / totalRevenue) * 100 : 0

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">{(t as any).profitLossReport}</h1>
          <p className="text-gray-600 mt-1">{(t as any).incomeStatement}</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={fetchReport}>
            <RefreshCw className="h-4 w-4 mr-2" />
            {(t as any).refresh}
          </Button>
          <Button variant="outline">
            <Printer className="h-4 w-4 mr-2" />
            {(t as any).print}
          </Button>
          <Button>
            <Download className="h-4 w-4 mr-2" />
            {(t as any).downloadPDF}
          </Button>
        </div>
      </div>

      {/* Period Selection */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4 items-center">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-gray-500" />
              <span className="font-medium">{(t as any).selectPeriod}:</span>
            </div>
            <div className="flex gap-2">
              <select
                value={selectedPeriod}
                onChange={(e: any) => setSelectedPeriod((e as any as any).target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="thisMonth">{(t as any).thisMonth}</option>
                <option value="lastMonth">{(t as any).lastMonth}</option>
                <option value="thisQuarter">{(t as any).thisQuarter}</option>
                <option value="lastQuarter">{(t as any).lastQuarter}</option>
                <option value="thisYear">{(t as any).thisYear}</option>
                <option value="lastYear">{(t as any).lastYear}</option>
              </select>
              <span className="self-center text-gray-500">{(t as any).comparedTo}</span>
              <select
                value={comparisonPeriod}
                onChange={(e: any) => setComparisonPeriod((e as any as any).target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="lastMonth">{(t as any).lastMonth}</option>
                <option value="lastQuarter">{(t as any).lastQuarter}</option>
                <option value="lastYear">{(t as any).lastYear}</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{(t as any).grossProfitMargin}</p>
                <p className="text-2xl font-bold mt-1">{formatPercent(grossProfitMargin as any)}</p>
              </div>
              <BarChart3 className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{(t as any).operatingMargin}</p>
                <p className="text-2xl font-bold mt-1">{formatPercent(operatingMargin as any)}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{(t as any).netProfitMargin}</p>
                <p className="text-2xl font-bold mt-1">{formatPercent(netProfitMargin as any)}</p>
              </div>
              <DollarSign className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{(t as any).revenueGrowth}</p>
                <p className="text-2xl font-bold mt-1">
                  {(report as any).revenue.length > 0 && formatPercent((report as any as any).revenue[0].variance_percent)}
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* P&L Statement */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>{(report as any).company_name}</CardTitle>
              <p className="text-sm text-gray-600 mt-1">
                {(t as any).incomeStatement} - {(report as any).report_period}
              </p>
            </div>
            <div className="text-right text-sm text-gray-500">
              {(t as any).generatedAt}: {new Date((report as any as any).generated_at).toLocaleString(language === 'ar' ? 'ar-SA' : 'en-US' as any)}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b-2 border-gray-800">
                  <th className="text-left py-3 px-4 font-bold">{(t as any).accountName}</th>
                  <th className="text-right py-3 px-4 font-bold">{(t as any).currentPeriod}</th>
                  <th className="text-right py-3 px-4 font-bold">{(t as any).previousPeriod}</th>
                  <th className="text-right py-3 px-4 font-bold">{(t as any).variance}</th>
                  <th className="text-right py-3 px-4 font-bold">{(t as any).variancePercent}</th>
                </tr>
              </thead>
              <tbody>
                {/* Revenue Section */}
                <tr className="bg-blue-50">
                  <td colSpan={5} className="py-2 px-4 font-bold text-blue-800 uppercase">
                    {(t as any).revenue}
                  </td>
                </tr>
                {(report as any).revenue.map(item => renderLineItem(item, true as any))}
                
                {/* Cost of Goods Sold */}
                <tr className="bg-red-50">
                  <td colSpan={5} className="py-2 px-4 font-bold text-red-800 uppercase">
                    {(t as any).costOfGoodsSold}
                  </td>
                </tr>
                {(report as any).cost_of_goods_sold.map(item => renderLineItem(item as any))}
                
                {/* Gross Profit */}
                {renderLineItem((report as any as any).gross_profit, true)}
                
                {/* Operating Expenses */}
                <tr className="bg-orange-50">
                  <td colSpan={5} className="py-2 px-4 font-bold text-orange-800 uppercase">
                    {(t as any).operatingExpenses}
                  </td>
                </tr>
                {(report as any).operating_expenses.map(item => renderLineItem(item as any))}
                
                {/* Operating Income */}
                {renderLineItem((report as any as any).operating_income, true)}
                
                {/* Other Income */}
                {(report as any).other_income.length > 0 && (
                  <>
                    <tr className="bg-green-50">
                      <td colSpan={5} className="py-2 px-4 font-bold text-green-800 uppercase">
                        {(t as any).otherIncome}
                      </td>
                    </tr>
                    {(report as any).other_income.map(item => renderLineItem(item, true as any))}
                  </>
                )}
                
                {/* Other Expenses */}
                {(report as any).other_expenses.length > 0 && (
                  <>
                    <tr className="bg-gray-50">
                      <td colSpan={5} className="py-2 px-4 font-bold text-gray-800 uppercase">
                        {(t as any).otherExpenses}
                      </td>
                    </tr>
                    {(report as any).other_expenses.map(item => renderLineItem(item as any))}
                  </>
                )}
                
                {/* Net Income Before Tax */}
                {renderLineItem((report as any as any).net_income_before_tax, true)}
                
                {/* Tax Expense */}
                {renderLineItem((report as any as any).tax_expense)}
                
                {/* Net Income */}
                {renderLineItem((report as any as any).net_income, true)}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
// @ts-ignore
}

// @ts-ignore
export default ProfitLossReport