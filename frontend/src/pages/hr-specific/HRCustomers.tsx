import { useState, useEffect } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Users,
  UserCheck,
  Search,
  Filter,
  Download,
  Eye,
  Edit,
  Phone,
  Mail,
  Calendar,
  Clock,
  FileText,
  AlertTriangle,
  CheckCircle,
  Building
} from 'lucide-react'

interface HRCustomersProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    title: 'العملاء - منظور الموارد البشرية',
    subtitle: 'إدارة العملاء من ناحية التوظيف والعقود والعلاقات المؤسسية',
    corporateClients: 'العملاء المؤسسيون',
    activeContracts: 'العقود النشطة',
    hrRequests: 'طلبات الموارد البشرية',
    complianceIssues: 'قضايا الامتثال',
    searchClients: 'البحث في العملاء...',
    addClient: 'إضافة عميل',
    exportData: 'تصدير البيانات',
    clientName: 'اسم العميل',
    contractType: 'نوع العقد',
    employees: 'الموظفون',
    contractStart: 'بداية العقد',
    contractEnd: 'نهاية العقد',
    hrContact: 'جهة الاتصال HR',
    complianceStatus: 'حالة الامتثال',
    actions: 'الإجراءات',
    viewContract: 'عرض العقد',
    manageEmployees: 'إدارة الموظفين',
    compliant: 'ملتزم',
    nonCompliant: 'غير ملتزم',
    pending: 'معلق',
    active: 'نشط',
    expired: 'منتهي',
    renewal: 'تجديد',
    staffing: 'توظيف',
    consulting: 'استشارات',
    outsourcing: 'استعانة خارجية',
    refresh: 'تحديث',
    contractManagement: 'إدارة العقود',
    employeeAllocation: 'توزيع الموظفين',
    filterBy: 'تصفية حسب'
  },
  en: {
    title: 'Clients - HR Perspective',
    subtitle: 'Client management from HR perspective: staffing, contracts, and institutional relations',
    corporateClients: 'Corporate Clients',
    activeContracts: 'Active Contracts',
    hrRequests: 'HR Requests',
    complianceIssues: 'Compliance Issues',
    searchClients: 'Search clients...',
    addClient: 'Add Client',
    exportData: 'Export Data',
    clientName: 'Client Name',
    contractType: 'Contract Type',
    employees: 'Employees',
    contractStart: 'Contract Start',
    contractEnd: 'Contract End',
    hrContact: 'HR Contact',
    complianceStatus: 'Compliance Status',
    actions: 'Actions',
    viewContract: 'View Contract',
    manageEmployees: 'Manage Employees',
    compliant: 'Compliant',
    nonCompliant: 'Non-Compliant',
    pending: 'Pending',
    active: 'Active',
    expired: 'Expired',
    renewal: 'Renewal',
    staffing: 'Staffing',
    consulting: 'Consulting',
    outsourcing: 'Outsourcing',
    refresh: 'Refresh',
    contractManagement: 'Contract Management',
    employeeAllocation: 'Employee Allocation',
    filterBy: 'Filter By'
  }
}

export default function HRCustomers({ language }: HRCustomersProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedType, setSelectedType] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('')
  const [refreshing, setRefreshing] = useState(false)
  const t = translations[language]
  const isRTL = language === 'ar'

  // HR-specific client metrics
  const [hrMetrics, setHrMetrics] = useState({
    corporateClients: 28,
    activeContracts: 45,
    hrRequests: 12,
    complianceIssues: 3
  })

  // Sample HR client data
  const [clients] = useState([
    {
      id: 1,
      name: 'شركة التقنية المتقدمة',
      nameEn: 'Advanced Technology Company',
      contractType: 'staffing',
      contractTypeAr: 'توظيف',
      employees: 25,
      contractStart: '2023-01-15',
      contractEnd: '2024-12-31',
      hrContact: 'سارة أحمد',
      hrContactEn: 'Sarah Ahmed',
      hrContactEmail: '<EMAIL>',
      hrContactPhone: '+966501234567',
      complianceStatus: 'compliant',
      contractValue: 850000,
      status: 'active'
    },
    {
      id: 2,
      name: 'مؤسسة النور للتجارة',
      nameEn: 'Al-Noor Trading Est.',
      contractType: 'consulting',
      contractTypeAr: 'استشارات',
      employees: 8,
      contractStart: '2023-06-01',
      contractEnd: '2024-05-31',
      hrContact: 'محمد علي',
      hrContactEn: 'Mohammed Ali',
      hrContactEmail: '<EMAIL>',
      hrContactPhone: '+966507654321',
      complianceStatus: 'pending',
      contractValue: 320000,
      status: 'active'
    },
    {
      id: 3,
      name: 'شركة البناء الحديث',
      nameEn: 'Modern Construction Co.',
      contractType: 'outsourcing',
      contractTypeAr: 'استعانة خارجية',
      employees: 45,
      contractStart: '2022-03-01',
      contractEnd: '2024-02-29',
      hrContact: 'فاطمة سالم',
      hrContactEn: 'Fatima Salem',
      hrContactEmail: '<EMAIL>',
      hrContactPhone: '+966509876543',
      complianceStatus: 'nonCompliant',
      contractValue: 1200000,
      status: 'renewal'
    }
  ])

  const handleRefresh = async () => {
    setRefreshing(true)
    await new Promise(resolve => setTimeout(resolve, 1000))
    setRefreshing(false)
  }

  const getComplianceColor = (status: string) => {
    switch (status) {
      case 'compliant':
        return 'bg-green-500/20 text-green-400 border-green-500/30'
      case 'nonCompliant':
        return 'bg-red-500/20 text-red-400 border-red-500/30'
      case 'pending':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-500/20 text-green-400 border-green-500/30'
      case 'expired':
        return 'bg-red-500/20 text-red-400 border-red-500/30'
      case 'renewal':
        return 'bg-orange-500/20 text-orange-400 border-orange-500/30'
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30'
    }
  }

  const getComplianceIcon = (status: string) => {
    switch (status) {
      case 'compliant':
        return <CheckCircle className="h-4 w-4" />
      case 'nonCompliant':
        return <AlertTriangle className="h-4 w-4" />
      case 'pending':
        return <Clock className="h-4 w-4" />
      default:
        return <Clock className="h-4 w-4" />
    }
  }

  const filteredClients = clients.filter(client => {
    const matchesSearch = client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         client.nameEn.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         client.hrContact.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = !selectedType || client.contractType === selectedType
    const matchesStatus = !selectedStatus || client.complianceStatus === selectedStatus

    return matchesSearch && matchesType && matchesStatus
  })

  const hrMetricsCards = [
    {
      title: t.corporateClients,
      value: hrMetrics.corporateClients.toString(),
      change: '+3',
      trend: 'up',
      icon: Building,
      color: 'from-blue-500 to-blue-600'
    },
    {
      title: t.activeContracts,
      value: hrMetrics.activeContracts.toString(),
      change: '+5',
      trend: 'up',
      icon: FileText,
      color: 'from-green-500 to-green-600'
    },
    {
      title: t.hrRequests,
      value: hrMetrics.hrRequests.toString(),
      change: '+2',
      trend: 'up',
      icon: UserCheck,
      color: 'from-purple-500 to-purple-600'
    },
    {
      title: t.complianceIssues,
      value: hrMetrics.complianceIssues.toString(),
      change: '-1',
      trend: 'down',
      icon: AlertTriangle,
      color: 'from-red-500 to-red-600'
    }
  ]

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">{t.title}</h1>
          <p className="text-white/70">{t.subtitle}</p>
        </div>

        <div className="flex items-center gap-3">
          <Button
            onClick={handleRefresh}
            disabled={refreshing}
            className="text-white/80 hover:text-white hover:bg-white/15 rounded-xl p-3 backdrop-blur-10"
          >
            <Calendar className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            {t.refresh}
          </Button>
          <Button className="text-white/80 hover:text-white hover:bg-white/15 rounded-xl p-3 backdrop-blur-10">
            <Building className="h-4 w-4 mr-2" />
            {t.addClient}
          </Button>
          <Button variant="outline" className="text-white/80 hover:text-white hover:bg-white/15 rounded-xl p-3 backdrop-blur-10">
            <Download className="h-4 w-4 mr-2" />
            {t.exportData}
          </Button>
        </div>
      </div>

      {/* HR Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {hrMetricsCards.map((card, index) => (
          <Card key={index} className="glass-card border-white/20 hover:border-white/40 transition-all">
            <CardContent className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <p className="text-white/70 text-sm mb-1">{card.title}</p>
                  <p className="text-2xl font-bold text-white">{card.value}</p>
                </div>
                <div className={`p-3 rounded-xl bg-gradient-to-r ${card.color} shadow-lg`}>
                  <card.icon className="h-6 w-6 text-white" />
                </div>
              </div>

              <div className="flex items-center gap-2">
                <span className={`text-sm font-medium ${
                  card.trend === 'up' ? 'text-green-400' :
                  card.trend === 'down' ? 'text-red-400' : 'text-blue-400'
                }`}>
                  {card.change}
                </span>
                <span className="text-xs text-white/60">this month</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Search and Filters */}
      <Card className="glass-card border-white/20">
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/60" />
              <Input
                placeholder={t.searchClients}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-white/10 border-white/20 text-white placeholder:text-white/60"
              />
            </div>

            <select
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value)}
              className="bg-white/10 border-white/20 text-white rounded-lg px-3 py-2"
            >
              <option value="">{t.contractType}</option>
              <option value="staffing">{t.staffing}</option>
              <option value="consulting">{t.consulting}</option>
              <option value="outsourcing">{t.outsourcing}</option>
            </select>

            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="bg-white/10 border-white/20 text-white rounded-lg px-3 py-2"
            >
              <option value="">{t.complianceStatus}</option>
              <option value="compliant">{t.compliant}</option>
              <option value="nonCompliant">{t.nonCompliant}</option>
              <option value="pending">{t.pending}</option>
            </select>

            <Button variant="outline" className="text-white/80 hover:text-white hover:bg-white/15 rounded-xl p-3 backdrop-blur-10">
              <Filter className="h-4 w-4 mr-2" />
              {t.filterBy}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Client List */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white text-xl">{t.contractManagement}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredClients.map((client) => (
              <div key={client.id} className="p-6 glass-card border-white/10 hover:border-white/30 transition-all">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-white font-semibold text-lg">
                        {language === 'ar' ? client.name : client.nameEn}
                      </h3>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(client.status)}`}>
                        {t[client.status as keyof typeof t]}
                      </span>
                      <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${getComplianceColor(client.complianceStatus)}`}>
                        {getComplianceIcon(client.complianceStatus)}
                        {t[client.complianceStatus as keyof typeof t]}
                      </span>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-4">
                      <div>
                        <span className="text-white/60">{t.contractType}: </span>
                        <span className="text-white">
                          {language === 'ar' ? client.contractTypeAr : t[client.contractType as keyof typeof t]}
                        </span>
                      </div>
                      <div>
                        <span className="text-white/60">{t.employees}: </span>
                        <span className="text-white">{client.employees}</span>
                      </div>
                      <div>
                        <span className="text-white/60">{t.contractStart}: </span>
                        <span className="text-white">{client.contractStart}</span>
                      </div>
                      <div>
                        <span className="text-white/60">{t.contractEnd}: </span>
                        <span className="text-white">{client.contractEnd}</span>
                      </div>
                    </div>

                    <div className="flex items-center gap-6 text-sm">
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4 text-white/60" />
                        <span className="text-white/60">{t.hrContact}: </span>
                        <span className="text-white">
                          {language === 'ar' ? client.hrContact : client.hrContactEn}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4 text-white/60" />
                        <span className="text-white">{client.hrContactEmail}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Phone className="h-4 w-4 text-white/60" />
                        <span className="text-white">{client.hrContactPhone}</span>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-2 ml-4">
                    <Button size="sm" variant="outline" className="text-white/80 hover:text-white hover:bg-white/15 rounded-lg p-2 backdrop-blur-10">
                      <FileText className="h-4 w-4" />
                    </Button>
                    <Button size="sm" variant="outline" className="text-white/80 hover:text-white hover:bg-white/15 rounded-lg p-2 backdrop-blur-10">
                      <Users className="h-4 w-4" />
                    </Button>
                    <Button size="sm" variant="outline" className="text-white/80 hover:text-white hover:bg-white/15 rounded-lg p-2 backdrop-blur-10">
                      <Eye className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <div className="flex items-center justify-between pt-4 border-t border-white/10">
                  <div className="flex items-center gap-4">
                    <div>
                      <span className="text-white/60 text-sm">Contract Value: </span>
                      <span className="text-white font-medium">
                        {new Intl.NumberFormat('ar-SA', {
                          style: 'currency',
                          currency: 'SAR'
                        }).format(client.contractValue)}
                      </span>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <Button size="sm" className="text-white/80 hover:text-white hover:bg-white/15 rounded-lg p-2 backdrop-blur-10">
                      <UserCheck className="h-4 w-4 mr-1" />
                      {t.manageEmployees}
                    </Button>
                    <Button size="sm" variant="outline" className="text-white/80 hover:text-white hover:bg-white/15 rounded-lg p-2 backdrop-blur-10">
                      <FileText className="h-4 w-4 mr-1" />
                      {t.viewContract}
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
