import { useState, useEffect } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Building,
  Users,
  TrendingUp,
  UserCheck,
  UserX,
  Plus,
  Search,
  Filter,
  Download,
  Eye,
  Edit,
  MoreHorizontal,
  Award,
  Clock,
  Target,
  BarChart3
} from 'lucide-react'

interface HRDepartmentsProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    title: 'إدارة الأقسام - الموارد البشرية',
    subtitle: 'إدارة الأقسام من منظور الموارد البشرية',
    totalDepartments: 'إجمالي الأقسام',
    totalEmployees: 'إجمالي الموظفين',
    avgSalary: 'متوسط الراتب',
    turnoverRate: 'معدل دوران الموظفين',
    searchDepartments: 'البحث في الأقسام...',
    addDepartment: 'إضافة قسم',
    exportData: 'تصدير البيانات',
    hrAnalytics: 'تحليلات الموارد البشرية',
    departmentList: 'قائمة الأقسام',
    departmentName: 'اسم القسم',
    manager: 'المدير',
    employeeCount: 'عدد الموظفين',
    avgPerformance: 'متوسط الأداء',
    budget: 'الميزانية',
    vacancies: 'الوظائف الشاغرة',
    actions: 'الإجراءات',
    viewDetails: 'عرض التفاصيل',
    editDepartment: 'تعديل القسم',
    hrMetrics: 'مقاييس الموارد البشرية',
    excellent: 'ممتاز',
    good: 'جيد',
    average: 'متوسط',
    needsImprovement: 'يحتاج تحسين',
    refresh: 'تحديث',
    thisQuarter: 'هذا الربع',
    employeeDistribution: 'توزيع الموظفين',
    performanceOverview: 'نظرة عامة على الأداء'
  },
  en: {
    title: 'Department Management - HR',
    subtitle: 'Manage departments from HR perspective',
    totalDepartments: 'Total Departments',
    totalEmployees: 'Total Employees',
    avgSalary: 'Average Salary',
    turnoverRate: 'Turnover Rate',
    searchDepartments: 'Search departments...',
    addDepartment: 'Add Department',
    exportData: 'Export Data',
    hrAnalytics: 'HR Analytics',
    departmentList: 'Department List',
    departmentName: 'Department Name',
    manager: 'Manager',
    employeeCount: 'Employee Count',
    avgPerformance: 'Avg Performance',
    budget: 'Budget',
    vacancies: 'Vacancies',
    actions: 'Actions',
    viewDetails: 'View Details',
    editDepartment: 'Edit Department',
    hrMetrics: 'HR Metrics',
    excellent: 'Excellent',
    good: 'Good',
    average: 'Average',
    needsImprovement: 'Needs Improvement',
    refresh: 'Refresh',
    thisQuarter: 'This Quarter',
    employeeDistribution: 'Employee Distribution',
    performanceOverview: 'Performance Overview'
  }
}

export default function HRDepartments({ language }: HRDepartmentsProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [refreshing, setRefreshing] = useState(false)
  const t = translations[language]
  const isRTL = language === 'ar'

  // HR-specific department metrics
  const [hrMetrics, setHrMetrics] = useState({
    totalDepartments: 8,
    totalEmployees: 156,
    avgSalary: 9500,
    turnoverRate: 12.5
  })

  // Sample department data with HR-specific information
  const [departments] = useState([
    {
      id: 1,
      name: 'تقنية المعلومات',
      nameEn: 'Information Technology',
      manager: 'أحمد محمد علي',
      managerEn: 'Ahmed Mohammed Ali',
      employeeCount: 25,
      avgPerformance: 4.2,
      budget: 2500000,
      vacancies: 3,
      avgSalary: 12000,
      turnoverRate: 8.5,
      performanceRating: 'excellent'
    },
    {
      id: 2,
      name: 'الموارد البشرية',
      nameEn: 'Human Resources',
      manager: 'فاطمة سالم أحمد',
      managerEn: 'Fatima Salem Ahmed',
      employeeCount: 12,
      avgPerformance: 4.5,
      budget: 1200000,
      vacancies: 1,
      avgSalary: 9500,
      turnoverRate: 5.2,
      performanceRating: 'excellent'
    },
    {
      id: 3,
      name: 'المالية',
      nameEn: 'Finance',
      manager: 'عمر خالد محمد',
      managerEn: 'Omar Khalid Mohammed',
      employeeCount: 18,
      avgPerformance: 4.0,
      budget: 1800000,
      vacancies: 2,
      avgSalary: 10500,
      turnoverRate: 10.1,
      performanceRating: 'good'
    },
    {
      id: 4,
      name: 'التسويق',
      nameEn: 'Marketing',
      manager: 'نورا عبدالله سعد',
      managerEn: 'Nora Abdullah Saad',
      employeeCount: 15,
      avgPerformance: 3.8,
      budget: 1500000,
      vacancies: 4,
      avgSalary: 8500,
      turnoverRate: 15.3,
      performanceRating: 'good'
    },
    {
      id: 5,
      name: 'المبيعات',
      nameEn: 'Sales',
      manager: 'سعد أحمد محمد',
      managerEn: 'Saad Ahmed Mohammed',
      employeeCount: 22,
      avgPerformance: 3.9,
      budget: 2200000,
      vacancies: 5,
      avgSalary: 9000,
      turnoverRate: 18.7,
      performanceRating: 'average'
    }
  ])

  const handleRefresh = async () => {
    setRefreshing(true)
    await new Promise(resolve => setTimeout(resolve, 1000))
    setRefreshing(false)
  }

  const getPerformanceColor = (rating: string) => {
    switch (rating) {
      case 'excellent':
        return 'text-green-400'
      case 'good':
        return 'text-blue-400'
      case 'average':
        return 'text-yellow-400'
      case 'needsImprovement':
        return 'text-red-400'
      default:
        return 'text-gray-400'
    }
  }

  const getPerformanceIcon = (rating: string) => {
    switch (rating) {
      case 'excellent':
        return <Award className="h-4 w-4" />
      case 'good':
        return <TrendingUp className="h-4 w-4" />
      case 'average':
        return <Target className="h-4 w-4" />
      case 'needsImprovement':
        return <Clock className="h-4 w-4" />
      default:
        return <BarChart3 className="h-4 w-4" />
    }
  }

  const filteredDepartments = departments.filter(dept => {
    const matchesSearch = dept.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         dept.nameEn.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         dept.manager.toLowerCase().includes(searchTerm.toLowerCase())
    return matchesSearch
  })

  const hrMetricsCards = [
    {
      title: t.totalDepartments,
      value: hrMetrics.totalDepartments.toString(),
      change: '+1',
      trend: 'up',
      icon: Building,
      color: 'from-blue-500 to-blue-600'
    },
    {
      title: t.totalEmployees,
      value: hrMetrics.totalEmployees.toString(),
      change: '+12',
      trend: 'up',
      icon: Users,
      color: 'from-green-500 to-green-600'
    },
    {
      title: t.avgSalary,
      value: new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR'
      }).format(hrMetrics.avgSalary),
      change: '+5.2%',
      trend: 'up',
      icon: TrendingUp,
      color: 'from-purple-500 to-purple-600'
    },
    {
      title: t.turnoverRate,
      value: `${hrMetrics.turnoverRate}%`,
      change: '-2.1%',
      trend: 'down',
      icon: UserX,
      color: 'from-orange-500 to-orange-600'
    }
  ]

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">{t.title}</h1>
          <p className="text-white/70">{t.subtitle}</p>
        </div>
        
        <div className="flex items-center gap-3">
          <Button
            onClick={handleRefresh}
            disabled={refreshing}
            className="glass-button"
          >
            <Clock className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            {t.refresh}
          </Button>
          <Button className="glass-button">
            <Plus className="h-4 w-4 mr-2" />
            {t.addDepartment}
          </Button>
          <Button variant="outline" className="glass-button">
            <Download className="h-4 w-4 mr-2" />
            {t.exportData}
          </Button>
        </div>
      </div>

      {/* HR Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {hrMetricsCards.map((card, index) => (
          <Card key={index} className="glass-card border-white/20 hover:border-white/40 transition-all">
            <CardContent className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <p className="text-white/70 text-sm mb-1">{card.title}</p>
                  <p className="text-2xl font-bold text-white">{card.value}</p>
                </div>
                <div className={`p-3 rounded-xl bg-gradient-to-r ${card.color} shadow-lg`}>
                  <card.icon className="h-6 w-6 text-white" />
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <span className={`text-sm font-medium ${
                  card.trend === 'up' ? 'text-green-400' : 
                  card.trend === 'down' ? 'text-red-400' : 'text-blue-400'
                }`}>
                  {card.change}
                </span>
                <span className="text-xs text-white/60">vs last quarter</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Search */}
      <Card className="glass-card border-white/20">
        <CardContent className="p-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/60" />
            <Input
              placeholder={t.searchDepartments}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 glass-input"
            />
          </div>
        </CardContent>
      </Card>

      {/* Department List */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white text-xl">{t.departmentList}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-white/20">
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.departmentName}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.manager}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.employeeCount}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.avgPerformance}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.avgSalary}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.vacancies}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.turnoverRate}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.actions}</th>
                </tr>
              </thead>
              <tbody>
                {filteredDepartments.map((dept) => (
                  <tr key={dept.id} className="border-b border-white/10 hover:bg-white/5 transition-colors">
                    <td className="py-4 px-4">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-white font-bold">
                          {(language === 'ar' ? dept.name : dept.nameEn).charAt(0)}
                        </div>
                        <div>
                          <p className="text-white font-medium">
                            {language === 'ar' ? dept.name : dept.nameEn}
                          </p>
                          <p className="text-white/60 text-sm">{dept.employeeCount} موظف</p>
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-4 text-white">
                      {language === 'ar' ? dept.manager : dept.managerEn}
                    </td>
                    <td className="py-4 px-4 text-white font-medium">
                      {dept.employeeCount}
                    </td>
                    <td className="py-4 px-4">
                      <div className={`flex items-center gap-2 ${getPerformanceColor(dept.performanceRating)}`}>
                        {getPerformanceIcon(dept.performanceRating)}
                        <span className="text-sm font-medium">
                          {dept.avgPerformance}/5.0
                        </span>
                      </div>
                    </td>
                    <td className="py-4 px-4 text-white">
                      {new Intl.NumberFormat('ar-SA', {
                        style: 'currency',
                        currency: 'SAR'
                      }).format(dept.avgSalary)}
                    </td>
                    <td className="py-4 px-4">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        dept.vacancies > 0 ? 'bg-yellow-500/20 text-yellow-400' : 'bg-green-500/20 text-green-400'
                      }`}>
                        {dept.vacancies} وظائف شاغرة
                      </span>
                    </td>
                    <td className="py-4 px-4">
                      <span className={`text-sm font-medium ${
                        dept.turnoverRate > 15 ? 'text-red-400' : 
                        dept.turnoverRate > 10 ? 'text-yellow-400' : 'text-green-400'
                      }`}>
                        {dept.turnoverRate}%
                      </span>
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex items-center gap-2">
                        <Button size="sm" variant="outline" className="glass-button p-2">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="outline" className="glass-button p-2">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="outline" className="glass-button p-2">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
