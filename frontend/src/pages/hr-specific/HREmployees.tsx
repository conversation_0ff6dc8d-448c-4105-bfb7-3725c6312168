import { useState, useEffect } from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Users,
  Search,
  Plus,
  Filter,
  Download,
  UserCheck,
  UserX,
  Calendar,
  Clock,
  TrendingUp,
  Award,
  AlertTriangle,
  CheckCircle,
  Eye,
  Edit,
  MoreHorizontal
} from 'lucide-react'

interface HREmployeesProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    title: 'إدارة الموظفين - الموارد البشرية',
    subtitle: 'إدارة شاملة لبيانات الموظفين والموارد البشرية',
    totalEmployees: 'إجمالي الموظفين',
    activeEmployees: 'الموظفون النشطون',
    onLeave: 'في إجازة',
    newHires: 'التوظيفات الجديدة',
    searchEmployees: 'البحث في الموظفين...',
    addEmployee: 'إضافة موظف',
    exportData: 'تصدير البيانات',
    filterBy: 'تصفية حسب',
    department: 'القسم',
    position: 'المنصب',
    status: 'الحالة',
    employeeList: 'قائمة الموظفين',
    name: 'الاسم',
    email: 'البريد الإلكتروني',
    phone: 'الهاتف',
    joinDate: 'تاريخ الانضمام',
    salary: 'الراتب',
    performance: 'الأداء',
    actions: 'الإجراءات',
    viewProfile: 'عرض الملف الشخصي',
    editEmployee: 'تعديل الموظف',
    hrActions: 'إجراءات الموارد البشرية',
    active: 'نشط',
    inactive: 'غير نشط',
    onLeaveStatus: 'في إجازة',
    excellent: 'ممتاز',
    good: 'جيد',
    average: 'متوسط',
    needsImprovement: 'يحتاج تحسين',
    thisMonth: 'هذا الشهر',
    refresh: 'تحديث'
  },
  en: {
    title: 'Employee Management - HR',
    subtitle: 'Comprehensive employee data and HR management',
    totalEmployees: 'Total Employees',
    activeEmployees: 'Active Employees',
    onLeave: 'On Leave',
    newHires: 'New Hires',
    searchEmployees: 'Search employees...',
    addEmployee: 'Add Employee',
    exportData: 'Export Data',
    filterBy: 'Filter By',
    department: 'Department',
    position: 'Position',
    status: 'Status',
    employeeList: 'Employee List',
    name: 'Name',
    email: 'Email',
    phone: 'Phone',
    joinDate: 'Join Date',
    salary: 'Salary',
    performance: 'Performance',
    actions: 'Actions',
    viewProfile: 'View Profile',
    editEmployee: 'Edit Employee',
    hrActions: 'HR Actions',
    active: 'Active',
    inactive: 'Inactive',
    onLeaveStatus: 'On Leave',
    excellent: 'Excellent',
    good: 'Good',
    average: 'Average',
    needsImprovement: 'Needs Improvement',
    thisMonth: 'This Month',
    refresh: 'Refresh'
  }
}

export default function HREmployees({ language }: HREmployeesProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedDepartment, setSelectedDepartment] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('')
  const [refreshing, setRefreshing] = useState(false)
  const t = translations[language]
  const isRTL = language === 'ar'

  // HR-specific employee metrics
  const [hrMetrics, setHrMetrics] = useState({
    totalEmployees: 156,
    activeEmployees: 142,
    onLeave: 8,
    newHires: 6
  })

  // Sample employee data with HR-specific information
  const [employees] = useState([
    {
      id: 1,
      name: 'أحمد محمد علي',
      nameEn: 'Ahmed Mohammed Ali',
      email: '<EMAIL>',
      phone: '+966501234567',
      department: 'تقنية المعلومات',
      departmentEn: 'Information Technology',
      position: 'مطور برمجيات أول',
      positionEn: 'Senior Software Developer',
      joinDate: '2023-01-15',
      salary: 12000,
      status: 'active',
      performance: 'excellent',
      avatar: 'A'
    },
    {
      id: 2,
      name: 'فاطمة سالم أحمد',
      nameEn: 'Fatima Salem Ahmed',
      email: '<EMAIL>',
      phone: '+966507654321',
      department: 'الموارد البشرية',
      departmentEn: 'Human Resources',
      position: 'أخصائي موارد بشرية',
      positionEn: 'HR Specialist',
      joinDate: '2022-08-20',
      salary: 9500,
      status: 'active',
      performance: 'good',
      avatar: 'F'
    },
    {
      id: 3,
      name: 'عمر خالد محمد',
      nameEn: 'Omar Khalid Mohammed',
      email: '<EMAIL>',
      phone: '+************',
      department: 'المالية',
      departmentEn: 'Finance',
      position: 'محاسب مالي',
      positionEn: 'Financial Accountant',
      joinDate: '2023-03-10',
      salary: 8500,
      status: 'onLeave',
      performance: 'good',
      avatar: 'O'
    },
    {
      id: 4,
      name: 'نورا عبدالله سعد',
      nameEn: 'Nora Abdullah Saad',
      email: '<EMAIL>',
      phone: '+************',
      department: 'التسويق',
      departmentEn: 'Marketing',
      position: 'مدير تسويق',
      positionEn: 'Marketing Manager',
      joinDate: '2021-11-05',
      salary: 11000,
      status: 'active',
      performance: 'excellent',
      avatar: 'N'
    }
  ])

  const handleRefresh = async () => {
    setRefreshing(true)
    await new Promise(resolve => setTimeout(resolve, 1000))
    setRefreshing(false)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-500/20 text-green-400 border-green-500/30'
      case 'inactive':
        return 'bg-red-500/20 text-red-400 border-red-500/30'
      case 'onLeave':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30'
    }
  }

  const getPerformanceColor = (performance: string) => {
    switch (performance) {
      case 'excellent':
        return 'text-green-400'
      case 'good':
        return 'text-blue-400'
      case 'average':
        return 'text-yellow-400'
      case 'needsImprovement':
        return 'text-red-400'
      default:
        return 'text-gray-400'
    }
  }

  const getPerformanceIcon = (performance: string) => {
    switch (performance) {
      case 'excellent':
        return <Award className="h-4 w-4" />
      case 'good':
        return <CheckCircle className="h-4 w-4" />
      case 'average':
        return <Clock className="h-4 w-4" />
      case 'needsImprovement':
        return <AlertTriangle className="h-4 w-4" />
      default:
        return <Clock className="h-4 w-4" />
    }
  }

  const filteredEmployees = employees.filter(employee => {
    const matchesSearch = employee.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         employee.nameEn.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         employee.email.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesDepartment = !selectedDepartment || employee.department === selectedDepartment
    const matchesStatus = !selectedStatus || employee.status === selectedStatus
    
    return matchesSearch && matchesDepartment && matchesStatus
  })

  const hrMetricsCards = [
    {
      title: t.totalEmployees,
      value: hrMetrics.totalEmployees.toString(),
      change: '+12',
      trend: 'up',
      icon: Users,
      color: 'from-blue-500 to-blue-600'
    },
    {
      title: t.activeEmployees,
      value: hrMetrics.activeEmployees.toString(),
      change: '+8',
      trend: 'up',
      icon: UserCheck,
      color: 'from-green-500 to-green-600'
    },
    {
      title: t.onLeave,
      value: hrMetrics.onLeave.toString(),
      change: '-2',
      trend: 'down',
      icon: Calendar,
      color: 'from-yellow-500 to-yellow-600'
    },
    {
      title: t.newHires,
      value: hrMetrics.newHires.toString(),
      change: t.thisMonth,
      trend: 'stable',
      icon: Plus,
      color: 'from-purple-500 to-purple-600'
    }
  ]

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">{t.title}</h1>
          <p className="text-white/70">{t.subtitle}</p>
        </div>
        
        <div className="flex items-center gap-3">
          <Button
            onClick={handleRefresh}
            disabled={refreshing}
            className="glass-button"
          >
            <Clock className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            {t.refresh}
          </Button>
          <Button className="glass-button">
            <Plus className="h-4 w-4 mr-2" />
            {t.addEmployee}
          </Button>
          <Button variant="outline" className="glass-button">
            <Download className="h-4 w-4 mr-2" />
            {t.exportData}
          </Button>
        </div>
      </div>

      {/* HR Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {hrMetricsCards.map((card, index) => (
          <Card key={index} className="glass-card border-white/20 hover:border-white/40 transition-all">
            <CardContent className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <p className="text-white/70 text-sm mb-1">{card.title}</p>
                  <p className="text-2xl font-bold text-white">{card.value}</p>
                </div>
                <div className={`p-3 rounded-xl bg-gradient-to-r ${card.color} shadow-lg`}>
                  <card.icon className="h-6 w-6 text-white" />
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <span className={`text-sm font-medium ${
                  card.trend === 'up' ? 'text-green-400' : 
                  card.trend === 'down' ? 'text-red-400' : 'text-blue-400'
                }`}>
                  {card.change}
                </span>
                <span className="text-xs text-white/60">vs last month</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Search and Filters */}
      <Card className="glass-card border-white/20">
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/60" />
              <Input
                placeholder={t.searchEmployees}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 glass-input"
              />
            </div>
            
            <select
              value={selectedDepartment}
              onChange={(e) => setSelectedDepartment(e.target.value)}
              className="glass-input"
            >
              <option value="">{t.department}</option>
              <option value="تقنية المعلومات">تقنية المعلومات</option>
              <option value="الموارد البشرية">الموارد البشرية</option>
              <option value="المالية">المالية</option>
              <option value="التسويق">التسويق</option>
            </select>
            
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="glass-input"
            >
              <option value="">{t.status}</option>
              <option value="active">{t.active}</option>
              <option value="inactive">{t.inactive}</option>
              <option value="onLeave">{t.onLeaveStatus}</option>
            </select>
            
            <Button variant="outline" className="glass-button">
              <Filter className="h-4 w-4 mr-2" />
              {t.filterBy}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Employee List */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white text-xl">{t.employeeList}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-white/20">
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.name}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.department}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.position}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.status}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.performance}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.salary}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.actions}</th>
                </tr>
              </thead>
              <tbody>
                {filteredEmployees.map((employee) => (
                  <tr key={employee.id} className="border-b border-white/10 hover:bg-white/5 transition-colors">
                    <td className="py-4 px-4">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-white font-bold">
                          {employee.avatar}
                        </div>
                        <div>
                          <p className="text-white font-medium">
                            {language === 'ar' ? employee.name : employee.nameEn}
                          </p>
                          <p className="text-white/60 text-sm">{employee.email}</p>
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-4 text-white">
                      {language === 'ar' ? employee.department : employee.departmentEn}
                    </td>
                    <td className="py-4 px-4 text-white">
                      {language === 'ar' ? employee.position : employee.positionEn}
                    </td>
                    <td className="py-4 px-4">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(employee.status)}`}>
                        {t[employee.status as keyof typeof t]}
                      </span>
                    </td>
                    <td className="py-4 px-4">
                      <div className={`flex items-center gap-2 ${getPerformanceColor(employee.performance)}`}>
                        {getPerformanceIcon(employee.performance)}
                        <span className="text-sm font-medium">
                          {t[employee.performance as keyof typeof t]}
                        </span>
                      </div>
                    </td>
                    <td className="py-4 px-4 text-white font-medium">
                      {new Intl.NumberFormat('ar-SA', {
                        style: 'currency',
                        currency: 'SAR'
                      }).format(employee.salary)}
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex items-center gap-2">
                        <Button size="sm" variant="outline" className="glass-button p-2">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="outline" className="glass-button p-2">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="outline" className="glass-button p-2">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
