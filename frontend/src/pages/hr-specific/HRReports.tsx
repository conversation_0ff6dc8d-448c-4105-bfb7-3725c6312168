import { useState, useEffect } from 'react'
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  FileText,
  Users,
  TrendingUp,
  Calendar,
  Clock,
  Download,
  Eye,
  BarChart3,
  <PERSON>Chart,
  Target,
  Award,
  DollarSign,
  UserCheck,
  UserX,
  AlertTriangle
} from 'lucide-react'

interface HRReportsProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    title: 'التقارير - الموارد البشرية',
    subtitle: 'تقارير شاملة للموارد البشرية والموظفين',
    hrReports: 'تقارير الموارد البشرية',
    employeeReports: 'تقارير الموظفين',
    performanceReports: 'تقارير الأداء',
    attendanceReports: 'تقارير الحضور',
    generateReport: 'إنشاء تقرير',
    downloadReport: 'تحميل التقرير',
    viewReport: 'عرض التقرير',
    monthlyReport: 'التقرير الشهري',
    quarterlyReport: 'التقرير الربعي',
    annualReport: 'التقرير السنوي',
    employeeTurnover: 'دوران الموظفين',
    salaryAnalysis: 'تحليل الرواتب',
    performanceAnalysis: 'تحليل الأداء',
    attendanceAnalysis: 'تحليل الحضور',
    recruitmentReport: 'تقرير التوظيف',
    trainingReport: 'تقرير التدريب',
    leaveReport: 'تقرير الإجازات',
    departmentAnalysis: 'تحليل الأقسام',
    quickReports: 'التقارير السريعة',
    customReports: 'التقارير المخصصة',
    recentReports: 'التقارير الحديثة',
    reportTemplates: 'قوالب التقارير',
    lastGenerated: 'آخر إنشاء',
    reportSize: 'حجم التقرير',
    status: 'الحالة',
    ready: 'جاهز',
    processing: 'قيد المعالجة',
    failed: 'فشل',
    refresh: 'تحديث'
  },
  en: {
    title: 'Reports - HR',
    subtitle: 'Comprehensive HR and employee reports',
    hrReports: 'HR Reports',
    employeeReports: 'Employee Reports',
    performanceReports: 'Performance Reports',
    attendanceReports: 'Attendance Reports',
    generateReport: 'Generate Report',
    downloadReport: 'Download Report',
    viewReport: 'View Report',
    monthlyReport: 'Monthly Report',
    quarterlyReport: 'Quarterly Report',
    annualReport: 'Annual Report',
    employeeTurnover: 'Employee Turnover',
    salaryAnalysis: 'Salary Analysis',
    performanceAnalysis: 'Performance Analysis',
    attendanceAnalysis: 'Attendance Analysis',
    recruitmentReport: 'Recruitment Report',
    trainingReport: 'Training Report',
    leaveReport: 'Leave Report',
    departmentAnalysis: 'Department Analysis',
    quickReports: 'Quick Reports',
    customReports: 'Custom Reports',
    recentReports: 'Recent Reports',
    reportTemplates: 'Report Templates',
    lastGenerated: 'Last Generated',
    reportSize: 'Report Size',
    status: 'Status',
    ready: 'Ready',
    processing: 'Processing',
    failed: 'Failed',
    refresh: 'Refresh'
  }
}

export default function HRReports({ language }: HRReportsProps) {
  const [refreshing, setRefreshing] = useState(false)
  const t = translations[language]
  const isRTL = language === 'ar'

  // Sample HR reports data
  const [quickReports] = useState([
    {
      id: 1,
      title: 'تقرير الموظفين الشهري',
      titleEn: 'Monthly Employee Report',
      description: 'تقرير شامل عن حالة الموظفين هذا الشهر',
      descriptionEn: 'Comprehensive report on employee status this month',
      icon: Users,
      color: 'from-blue-500 to-blue-600',
      type: 'employee'
    },
    {
      id: 2,
      title: 'تحليل الأداء الربعي',
      titleEn: 'Quarterly Performance Analysis',
      description: 'تحليل أداء الموظفين للربع الحالي',
      descriptionEn: 'Employee performance analysis for current quarter',
      icon: TrendingUp,
      color: 'from-green-500 to-green-600',
      type: 'performance'
    },
    {
      id: 3,
      title: 'تقرير الحضور والغياب',
      titleEn: 'Attendance Report',
      description: 'تقرير مفصل عن حضور وغياب الموظفين',
      descriptionEn: 'Detailed report on employee attendance',
      icon: Clock,
      color: 'from-purple-500 to-purple-600',
      type: 'attendance'
    },
    {
      id: 4,
      title: 'تحليل الرواتب',
      titleEn: 'Salary Analysis',
      description: 'تحليل شامل لهيكل الرواتب والمكافآت',
      descriptionEn: 'Comprehensive salary and benefits analysis',
      icon: DollarSign,
      color: 'from-yellow-500 to-yellow-600',
      type: 'salary'
    },
    {
      id: 5,
      title: 'تقرير التوظيف',
      titleEn: 'Recruitment Report',
      description: 'تقرير عن عمليات التوظيف والتعيين',
      descriptionEn: 'Report on recruitment and hiring processes',
      icon: UserCheck,
      color: 'from-cyan-500 to-cyan-600',
      type: 'recruitment'
    },
    {
      id: 6,
      title: 'تحليل دوران الموظفين',
      titleEn: 'Employee Turnover Analysis',
      description: 'تحليل معدلات ترك الموظفين وأسبابها',
      descriptionEn: 'Analysis of employee turnover rates and reasons',
      icon: UserX,
      color: 'from-red-500 to-red-600',
      type: 'turnover'
    }
  ])

  const [recentReports] = useState([
    {
      id: 1,
      name: 'تقرير الموظفين - يناير 2024',
      nameEn: 'Employee Report - January 2024',
      type: 'employee',
      lastGenerated: '2024-01-28',
      size: '2.4 MB',
      status: 'ready',
      downloads: 45
    },
    {
      id: 2,
      name: 'تحليل الأداء - Q4 2023',
      nameEn: 'Performance Analysis - Q4 2023',
      type: 'performance',
      lastGenerated: '2024-01-15',
      size: '1.8 MB',
      status: 'ready',
      downloads: 32
    },
    {
      id: 3,
      name: 'تقرير الحضور - ديسمبر 2023',
      nameEn: 'Attendance Report - December 2023',
      type: 'attendance',
      lastGenerated: '2024-01-10',
      size: '3.1 MB',
      status: 'processing',
      downloads: 0
    },
    {
      id: 4,
      name: 'تحليل الرواتب - 2023',
      nameEn: 'Salary Analysis - 2023',
      type: 'salary',
      lastGenerated: '2024-01-05',
      size: '1.2 MB',
      status: 'ready',
      downloads: 67
    }
  ])

  const handleRefresh = async () => {
    setRefreshing(true)
    await new Promise(resolve => setTimeout(resolve, 1000))
    setRefreshing(false)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ready':
        return 'bg-green-500/20 text-green-400 border-green-500/30'
      case 'processing':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
      case 'failed':
        return 'bg-red-500/20 text-red-400 border-red-500/30'
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30'
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'employee':
        return <Users className="h-4 w-4" />
      case 'performance':
        return <TrendingUp className="h-4 w-4" />
      case 'attendance':
        return <Clock className="h-4 w-4" />
      case 'salary':
        return <DollarSign className="h-4 w-4" />
      case 'recruitment':
        return <UserCheck className="h-4 w-4" />
      case 'turnover':
        return <UserX className="h-4 w-4" />
      default:
        return <FileText className="h-4 w-4" />
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">{t.title}</h1>
          <p className="text-white/70">{t.subtitle}</p>
        </div>
        
        <div className="flex items-center gap-3">
          <Button
            onClick={handleRefresh}
            disabled={refreshing}
            className="glass-button"
          >
            <BarChart3 className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            {t.refresh}
          </Button>
          <Button className="glass-button">
            <FileText className="h-4 w-4 mr-2" />
            {t.generateReport}
          </Button>
        </div>
      </div>

      {/* Quick Reports */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white text-xl">{t.quickReports}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {quickReports.map((report) => (
              <div
                key={report.id}
                className="group p-6 glass-card border-white/10 hover:border-white/30 cursor-pointer transition-all duration-300 hover:scale-105"
              >
                <div className="flex items-start gap-4 mb-4">
                  <div className={`p-3 rounded-xl bg-gradient-to-r ${report.color} shadow-lg group-hover:shadow-xl transition-all`}>
                    <report.icon className="h-6 w-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-white font-semibold mb-1">
                      {language === 'ar' ? report.title : report.titleEn}
                    </h3>
                    <p className="text-white/70 text-sm">
                      {language === 'ar' ? report.description : report.descriptionEn}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <Button size="sm" className="glass-button flex-1">
                    <Eye className="h-4 w-4 mr-2" />
                    {t.generateReport}
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recent Reports */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white text-xl">{t.recentReports}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-white/20">
                  <th className="text-left py-3 px-4 text-white/80 font-medium">اسم التقرير</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">النوع</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.lastGenerated}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.reportSize}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.status}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">التحميلات</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {recentReports.map((report) => (
                  <tr key={report.id} className="border-b border-white/10 hover:bg-white/5 transition-colors">
                    <td className="py-4 px-4">
                      <div className="flex items-center gap-3">
                        <div className="p-2 rounded-lg bg-white/10">
                          {getTypeIcon(report.type)}
                        </div>
                        <div>
                          <p className="text-white font-medium">
                            {language === 'ar' ? report.name : report.nameEn}
                          </p>
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <span className="inline-flex items-center gap-2 text-white/80">
                        {getTypeIcon(report.type)}
                        {report.type}
                      </span>
                    </td>
                    <td className="py-4 px-4 text-white">
                      {report.lastGenerated}
                    </td>
                    <td className="py-4 px-4 text-white">
                      {report.size}
                    </td>
                    <td className="py-4 px-4">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(report.status)}`}>
                        {t[report.status as keyof typeof t]}
                      </span>
                    </td>
                    <td className="py-4 px-4 text-white">
                      {report.downloads} مرة
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex items-center gap-2">
                        {report.status === 'ready' && (
                          <>
                            <Button size="sm" variant="outline" className="glass-button p-2">
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button size="sm" variant="outline" className="glass-button p-2">
                              <Download className="h-4 w-4" />
                            </Button>
                          </>
                        )}
                        {report.status === 'processing' && (
                          <div className="flex items-center gap-2 text-yellow-400">
                            <Clock className="h-4 w-4 animate-spin" />
                            <span className="text-sm">معالجة...</span>
                          </div>
                        )}
                        {report.status === 'failed' && (
                          <div className="flex items-center gap-2 text-red-400">
                            <AlertTriangle className="h-4 w-4" />
                            <span className="text-sm">فشل</span>
                          </div>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
