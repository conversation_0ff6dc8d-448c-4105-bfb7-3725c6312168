import { useState } from 'react'
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Clock,
  Calendar,
  Search,
  Filter,
  CheckCircle,
  XCircle,
  Alert<PERSON>riangle,
  User,
  Timer,
  TrendingUp,
  Download
} from 'lucide-react'

interface AttendanceProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    attendance: 'الحضور والانصراف',
    todayAttendance: 'حضور اليوم',
    attendanceReport: 'تقرير الحضور',
    checkIn: 'تسجيل الحضور',
    checkOut: 'تسجيل الانصراف',
    present: 'حاضر',
    absent: 'غائب',
    late: 'متأخر',
    overtime: 'وقت إضافي',
    search: 'بحث',
    filter: 'تصفية',
    export: 'تصدير',
    employee: 'الموظف',
    date: 'التاريخ',
    checkInTime: 'وقت الحضور',
    checkOutTime: 'وقت الانصراف',
    totalHours: 'إجمالي الساعات',
    overtimeHours: 'ساعات إضافية',
    status: 'الحالة',
    department: 'القسم',
    workingHours: 'ساعات العمل',
    breakTime: 'وقت الاستراحة',
    totalEmployees: 'إجمالي الموظفين',
    presentToday: 'الحاضرون اليوم',
    absentToday: 'الغائبون اليوم',
    lateToday: 'المتأخرون اليوم',
    averageWorkingHours: 'متوسط ساعات العمل',
    thisMonth: 'هذا الشهر'
  },
  en: {
    attendance: 'Attendance',
    todayAttendance: "Today's Attendance",
    attendanceReport: 'Attendance Report',
    checkIn: 'Check In',
    checkOut: 'Check Out',
    present: 'Present',
    absent: 'Absent',
    late: 'Late',
    overtime: 'Overtime',
    search: 'Search',
    filter: 'Filter',
    export: 'Export',
    employee: 'Employee',
    date: 'Date',
    checkInTime: 'Check In Time',
    checkOutTime: 'Check Out Time',
    totalHours: 'Total Hours',
    overtimeHours: 'Overtime Hours',
    status: 'Status',
    department: 'Department',
    workingHours: 'Working Hours',
    breakTime: 'Break Time',
    totalEmployees: 'Total Employees',
    presentToday: 'Present Today',
    absentToday: 'Absent Today',
    lateToday: 'Late Today',
    averageWorkingHours: 'Average Working Hours',
    thisMonth: 'This Month'
  }
}

export default function Attendance({ language }: AttendanceProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0])
  const t = translations[language]
  const isRTL = language === 'ar'

  // Mock data
  const attendanceStats = {
    totalEmployees: 150,
    presentToday: 142,
    absentToday: 8,
    lateToday: 12,
    averageWorkingHours: 8.2
  }

  const attendanceRecords = [
    {
      id: 1,
      employee: 'أحمد محمد',
      department: 'تقنية المعلومات',
      date: '2024-01-15',
      checkIn: '08:00',
      checkOut: '17:30',
      totalHours: 8.5,
      overtimeHours: 0.5,
      status: 'present'
    },
    {
      id: 2,
      employee: 'فاطمة علي',
      department: 'الموارد البشرية',
      date: '2024-01-15',
      checkIn: '08:15',
      checkOut: '17:00',
      totalHours: 7.75,
      overtimeHours: 0,
      status: 'late'
    },
    {
      id: 3,
      employee: 'محمد حسن',
      department: 'المالية',
      date: '2024-01-15',
      checkIn: '-',
      checkOut: '-',
      totalHours: 0,
      overtimeHours: 0,
      status: 'absent'
    },
    {
      id: 4,
      employee: 'سارة أحمد',
      department: 'التسويق',
      date: '2024-01-15',
      checkIn: '07:45',
      checkOut: '18:00',
      totalHours: 9.25,
      overtimeHours: 1.25,
      status: 'overtime'
    }
  ]

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'present':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'absent':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'late':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      case 'overtime':
        return <TrendingUp className="h-4 w-4 text-blue-500" />
      default:
        return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'present':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'absent':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'late':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'overtime':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">{t.attendance}</h1>
          <p className="text-white/70">متابعة حضور وانصراف الموظفين</p>
        </div>
        <div className="flex gap-2">
          <Button className="glass-button">
            <Clock className="h-4 w-4 mr-2" />
            {t.checkIn}
          </Button>
          <Button variant="outline" className="glass-button">
            <Download className="h-4 w-4 mr-2" />
            {t.export}
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.totalEmployees}</p>
                <p className="text-2xl font-bold text-white">{attendanceStats.totalEmployees}</p>
              </div>
              <User className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.presentToday}</p>
                <p className="text-2xl font-bold text-green-400">{attendanceStats.presentToday}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.absentToday}</p>
                <p className="text-2xl font-bold text-red-400">{attendanceStats.absentToday}</p>
              </div>
              <XCircle className="h-8 w-8 text-red-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.lateToday}</p>
                <p className="text-2xl font-bold text-yellow-400">{attendanceStats.lateToday}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-yellow-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.averageWorkingHours}</p>
                <p className="text-2xl font-bold text-blue-400">{attendanceStats.averageWorkingHours}h</p>
              </div>
              <Timer className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Attendance Records */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <CardTitle className="text-white text-xl">{t.attendanceReport}</CardTitle>
              <CardDescription className="text-white/70">
                تفاصيل حضور وانصراف الموظفين
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <Input
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                className="glass-input"
              />
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 h-4 w-4" />
                <Input
                  placeholder={t.search}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 glass-input"
                />
              </div>
              <Button variant="outline" className="glass-button">
                <Filter className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-white/20">
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.employee}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.department}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.checkInTime}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.checkOutTime}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.totalHours}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.overtimeHours}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.status}</th>
                </tr>
              </thead>
              <tbody>
                {attendanceRecords.map((record) => (
                  <tr key={record.id} className="border-b border-white/10 hover:bg-white/5">
                    <td className="py-3 px-4">
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4 text-blue-400" />
                        <span className="text-white">{record.employee}</span>
                      </div>
                    </td>
                    <td className="py-3 px-4 text-white/80">{record.department}</td>
                    <td className="py-3 px-4 text-white/80">{record.checkIn}</td>
                    <td className="py-3 px-4 text-white/80">{record.checkOut}</td>
                    <td className="py-3 px-4 text-white/80">{record.totalHours}h</td>
                    <td className="py-3 px-4 text-white/80">{record.overtimeHours}h</td>
                    <td className="py-3 px-4">
                      <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(record.status)}`}>
                        {getStatusIcon(record.status)}
                        {t[record.status as keyof typeof t]}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
