import { useState } from 'react'
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Calendar,
  Plus,
  Search,
  Filter,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  User,
  CalendarDays
} from 'lucide-react'

interface LeaveManagementProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    leaveManagement: 'إدارة الإجازات',
    requestLeave: 'طلب إجازة',
    leaveRequests: 'طلبات الإجازات',
    leaveBalance: 'رصيد الإجازات',
    leaveTypes: 'أنواع الإجازات',
    pendingRequests: 'الطلبات المعلقة',
    approvedRequests: 'الطلبات المعتمدة',
    rejectedRequests: 'الطلبات المرفوضة',
    search: 'بحث',
    filter: 'تصفية',
    addNew: 'إضافة جديد',
    employee: 'الموظف',
    leaveType: 'نوع الإجازة',
    startDate: 'تاريخ البداية',
    endDate: 'تاريخ النهاية',
    duration: 'المدة',
    status: 'الحالة',
    reason: 'السبب',
    actions: 'الإجراءات',
    approve: 'موافقة',
    reject: 'رفض',
    pending: 'معلق',
    approved: 'معتمد',
    rejected: 'مرفوض',
    days: 'أيام',
    annualLeave: 'إجازة سنوية',
    sickLeave: 'إجازة مرضية',
    personalLeave: 'إجازة شخصية',
    maternityLeave: 'إجازة أمومة',
    totalDays: 'إجمالي الأيام',
    usedDays: 'الأيام المستخدمة',
    remainingDays: 'الأيام المتبقية'
  },
  en: {
    leaveManagement: 'Leave Management',
    requestLeave: 'Request Leave',
    leaveRequests: 'Leave Requests',
    leaveBalance: 'Leave Balance',
    leaveTypes: 'Leave Types',
    pendingRequests: 'Pending Requests',
    approvedRequests: 'Approved Requests',
    rejectedRequests: 'Rejected Requests',
    search: 'Search',
    filter: 'Filter',
    addNew: 'Add New',
    employee: 'Employee',
    leaveType: 'Leave Type',
    startDate: 'Start Date',
    endDate: 'End Date',
    duration: 'Duration',
    status: 'Status',
    reason: 'Reason',
    actions: 'Actions',
    approve: 'Approve',
    reject: 'Reject',
    pending: 'Pending',
    approved: 'Approved',
    rejected: 'Rejected',
    days: 'Days',
    annualLeave: 'Annual Leave',
    sickLeave: 'Sick Leave',
    personalLeave: 'Personal Leave',
    maternityLeave: 'Maternity Leave',
    totalDays: 'Total Days',
    usedDays: 'Used Days',
    remainingDays: 'Remaining Days'
  }
}

export default function LeaveManagement({ language }: LeaveManagementProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedFilter, setSelectedFilter] = useState('all')
  const t = translations[language]
  const isRTL = language === 'ar'

  // Mock data
  const leaveRequests = [
    {
      id: 1,
      employee: 'أحمد محمد',
      leaveType: t.annualLeave,
      startDate: '2024-01-15',
      endDate: '2024-01-20',
      duration: 5,
      status: 'pending',
      reason: 'عطلة عائلية'
    },
    {
      id: 2,
      employee: 'فاطمة علي',
      leaveType: t.sickLeave,
      startDate: '2024-01-10',
      endDate: '2024-01-12',
      duration: 3,
      status: 'approved',
      reason: 'إجازة مرضية'
    },
    {
      id: 3,
      employee: 'محمد حسن',
      leaveType: t.personalLeave,
      startDate: '2024-01-08',
      endDate: '2024-01-09',
      duration: 2,
      status: 'rejected',
      reason: 'ظروف شخصية'
    }
  ]

  const leaveBalance = [
    { type: t.annualLeave, total: 30, used: 10, remaining: 20 },
    { type: t.sickLeave, total: 15, used: 3, remaining: 12 },
    { type: t.personalLeave, total: 10, used: 2, remaining: 8 }
  ]

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <AlertCircle className="h-4 w-4 text-yellow-500" />
      case 'approved':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'rejected':
        return <XCircle className="h-4 w-4 text-red-500" />
      default:
        return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'approved':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'rejected':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">{t.leaveManagement}</h1>
          <p className="text-white/70">إدارة طلبات الإجازات ومتابعة الأرصدة</p>
        </div>
        <Button className="glass-button">
          <Plus className="h-4 w-4 mr-2" />
          {t.requestLeave}
        </Button>
      </div>

      {/* Leave Balance Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {leaveBalance.map((balance, index) => (
          <Card key={index} className="glass-card border-white/20">
            <CardHeader className="pb-3">
              <CardTitle className="text-white text-lg flex items-center gap-2">
                <Calendar className="h-5 w-5 text-blue-400" />
                {balance.type}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="text-white/70">{t.totalDays}</span>
                  <span className="text-white font-medium">{balance.total}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-white/70">{t.usedDays}</span>
                  <span className="text-orange-400 font-medium">{balance.used}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-white/70">{t.remainingDays}</span>
                  <span className="text-green-400 font-medium">{balance.remaining}</span>
                </div>
                <div className="w-full bg-white/20 rounded-full h-2">
                  <div 
                    className="bg-gradient-to-r from-blue-500 to-green-500 h-2 rounded-full"
                    style={{ width: `${(balance.remaining / balance.total) * 100}%` }}
                  ></div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Leave Requests */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <CardTitle className="text-white text-xl">{t.leaveRequests}</CardTitle>
              <CardDescription className="text-white/70">
                إدارة ومراجعة طلبات الإجازات
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 h-4 w-4" />
                <Input
                  placeholder={t.search}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 glass-input"
                />
              </div>
              <Button variant="outline" className="glass-button">
                <Filter className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-white/20">
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.employee}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.leaveType}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.startDate}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.endDate}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.duration}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.status}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.actions}</th>
                </tr>
              </thead>
              <tbody>
                {leaveRequests.map((request) => (
                  <tr key={request.id} className="border-b border-white/10 hover:bg-white/5">
                    <td className="py-3 px-4">
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4 text-blue-400" />
                        <span className="text-white">{request.employee}</span>
                      </div>
                    </td>
                    <td className="py-3 px-4 text-white/80">{request.leaveType}</td>
                    <td className="py-3 px-4 text-white/80">{request.startDate}</td>
                    <td className="py-3 px-4 text-white/80">{request.endDate}</td>
                    <td className="py-3 px-4 text-white/80">{request.duration} {t.days}</td>
                    <td className="py-3 px-4">
                      <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(request.status)}`}>
                        {getStatusIcon(request.status)}
                        {t[request.status as keyof typeof t]}
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex gap-2">
                        {request.status === 'pending' && (
                          <>
                            <Button size="sm" className="glass-button text-green-400 hover:bg-green-500/20">
                              <CheckCircle className="h-3 w-3" />
                            </Button>
                            <Button size="sm" className="glass-button text-red-400 hover:bg-red-500/20">
                              <XCircle className="h-3 w-3" />
                            </Button>
                          </>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
