import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  DollarSign,
  Calculator,
  Download,
  Search,
  Filter,
  User,
  Calendar,
  TrendingUp,
  FileText,
  CheckCircle,
  Clock
} from 'lucide-react'

interface PayrollProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    payroll: 'كشوف المرتبات',
    generatePayroll: 'إنشاء كشف مرتبات',
    payrollSummary: 'ملخص كشوف المرتبات',
    employeePayroll: 'مرتبات الموظفين',
    search: 'بحث',
    filter: 'تصفية',
    export: 'تصدير',
    employee: 'الموظف',
    department: 'القسم',
    basicSalary: 'الراتب الأساسي',
    allowances: 'البدلات',
    deductions: 'الخصومات',
    netSalary: 'صافي الراتب',
    payPeriod: 'فترة الدفع',
    status: 'الحالة',
    actions: 'الإجراءات',
    processed: 'معالج',
    pending: 'معلق',
    paid: 'مدفوع',
    viewPayslip: 'عرض كشف الراتب',
    sendPayslip: 'إرسال كشف الراتب',
    totalPayroll: 'إجمالي كشوف المرتبات',
    totalEmployees: 'إجمالي الموظفين',
    averageSalary: 'متوسط الراتب',
    totalDeductions: 'إجمالي الخصومات',
    overtimePay: 'أجر الوقت الإضافي',
    bonus: 'المكافآت',
    tax: 'الضرائب',
    insurance: 'التأمين',
    thisMonth: 'هذا الشهر',
    lastMonth: 'الشهر الماضي',
    payrollHistory: 'تاريخ كشوف المرتبات'
  },
  en: {
    payroll: 'Payroll',
    generatePayroll: 'Generate Payroll',
    payrollSummary: 'Payroll Summary',
    employeePayroll: 'Employee Payroll',
    search: 'Search',
    filter: 'Filter',
    export: 'Export',
    employee: 'Employee',
    department: 'Department',
    basicSalary: 'Basic Salary',
    allowances: 'Allowances',
    deductions: 'Deductions',
    netSalary: 'Net Salary',
    payPeriod: 'Pay Period',
    status: 'Status',
    actions: 'Actions',
    processed: 'Processed',
    pending: 'Pending',
    paid: 'Paid',
    viewPayslip: 'View Payslip',
    sendPayslip: 'Send Payslip',
    totalPayroll: 'Total Payroll',
    totalEmployees: 'Total Employees',
    averageSalary: 'Average Salary',
    totalDeductions: 'Total Deductions',
    overtimePay: 'Overtime Pay',
    bonus: 'Bonus',
    tax: 'Tax',
    insurance: 'Insurance',
    thisMonth: 'This Month',
    lastMonth: 'Last Month',
    payrollHistory: 'Payroll History'
  }
}

export default function Payroll({ language }: PayrollProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedPeriod, setSelectedPeriod] = useState('2024-01')
  const t = translations[language]
  const isRTL = language === 'ar'

  // Mock data
  const payrollStats = {
    totalPayroll: 750000,
    totalEmployees: 150,
    averageSalary: 5000,
    totalDeductions: 125000
  }

  const payrollRecords = [
    {
      id: 1,
      employee: 'أحمد محمد',
      department: 'تقنية المعلومات',
      basicSalary: 8000,
      allowances: 1500,
      deductions: 800,
      netSalary: 8700,
      payPeriod: '2024-01',
      status: 'paid'
    },
    {
      id: 2,
      employee: 'فاطمة علي',
      department: 'الموارد البشرية',
      basicSalary: 6500,
      allowances: 1000,
      deductions: 650,
      netSalary: 6850,
      payPeriod: '2024-01',
      status: 'paid'
    },
    {
      id: 3,
      employee: 'محمد حسن',
      department: 'المالية',
      basicSalary: 7000,
      allowances: 1200,
      deductions: 700,
      netSalary: 7500,
      payPeriod: '2024-01',
      status: 'processed'
    },
    {
      id: 4,
      employee: 'سارة أحمد',
      department: 'التسويق',
      basicSalary: 5500,
      allowances: 800,
      deductions: 550,
      netSalary: 5750,
      payPeriod: '2024-01',
      status: 'pending'
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'processed':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'paid':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'processed':
        return <FileText className="h-4 w-4 text-blue-500" />
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />
      default:
        return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount)
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">{t.payroll}</h1>
          <p className="text-white/70">إدارة كشوف المرتبات والأجور</p>
        </div>
        <div className="flex gap-2">
          <Button className="glass-button">
            <Calculator className="h-4 w-4 mr-2" />
            {t.generatePayroll}
          </Button>
          <Button variant="outline" className="glass-button">
            <Download className="h-4 w-4 mr-2" />
            {t.export}
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.totalPayroll}</p>
                <p className="text-2xl font-bold text-white">{formatCurrency(payrollStats.totalPayroll)}</p>
              </div>
              <DollarSign className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.totalEmployees}</p>
                <p className="text-2xl font-bold text-blue-400">{payrollStats.totalEmployees}</p>
              </div>
              <User className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.averageSalary}</p>
                <p className="text-2xl font-bold text-purple-400">{formatCurrency(payrollStats.averageSalary)}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-purple-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.totalDeductions}</p>
                <p className="text-2xl font-bold text-orange-400">{formatCurrency(payrollStats.totalDeductions)}</p>
              </div>
              <Calculator className="h-8 w-8 text-orange-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Payroll Records */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <CardTitle className="text-white text-xl">{t.employeePayroll}</CardTitle>
              <CardDescription className="text-white/70">
                تفاصيل كشوف مرتبات الموظفين
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <Input
                type="month"
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(e.target.value)}
                className="glass-input"
              />
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 h-4 w-4" />
                <Input
                  placeholder={t.search}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 glass-input"
                />
              </div>
              <Button variant="outline" className="glass-button">
                <Filter className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-white/20">
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.employee}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.department}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.basicSalary}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.allowances}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.deductions}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.netSalary}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.status}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.actions}</th>
                </tr>
              </thead>
              <tbody>
                {payrollRecords.map((record) => (
                  <tr key={record.id} className="border-b border-white/10 hover:bg-white/5">
                    <td className="py-3 px-4">
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4 text-blue-400" />
                        <span className="text-white">{record.employee}</span>
                      </div>
                    </td>
                    <td className="py-3 px-4 text-white/80">{record.department}</td>
                    <td className="py-3 px-4 text-white/80">{formatCurrency(record.basicSalary)}</td>
                    <td className="py-3 px-4 text-green-400">{formatCurrency(record.allowances)}</td>
                    <td className="py-3 px-4 text-red-400">{formatCurrency(record.deductions)}</td>
                    <td className="py-3 px-4 text-white font-medium">{formatCurrency(record.netSalary)}</td>
                    <td className="py-3 px-4">
                      <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(record.status)}`}>
                        {getStatusIcon(record.status)}
                        {t[record.status as keyof typeof t]}
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex gap-2">
                        <Button size="sm" className="glass-button">
                          <FileText className="h-3 w-3 mr-1" />
                          {t.viewPayslip}
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
