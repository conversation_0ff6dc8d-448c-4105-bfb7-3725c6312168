import { useState } from 'react'
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  TrendingUp,
  Star,
  Target,
  Award,
  Search,
  Filter,
  Plus,
  User,
  Calendar,
  BarChart3,
  CheckCircle
} from 'lucide-react'

interface PerformanceProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    performance: 'تقييم الأداء',
    performanceReviews: 'مراجعات الأداء',
    goals: 'الأهداف',
    achievements: 'الإنجازات',
    createReview: 'إنشاء مراجعة',
    search: 'بحث',
    filter: 'تصفية',
    employee: 'الموظف',
    department: 'القسم',
    reviewPeriod: 'فترة المراجعة',
    overallRating: 'التقييم العام',
    status: 'الحالة',
    actions: 'الإجراءات',
    pending: 'معلق',
    completed: 'مكتمل',
    inProgress: 'قيد التنفيذ',
    excellent: 'ممتاز',
    good: 'جيد',
    average: 'متوسط',
    needsImprovement: 'يحتاج تحسين',
    viewDetails: 'عرض التفاصيل',
    edit: 'تعديل',
    totalReviews: 'إجمالي المراجعات',
    completedReviews: 'المراجعات المكتملة',
    pendingReviews: 'المراجعات المعلقة',
    averageRating: 'متوسط التقييم',
    goalTitle: 'عنوان الهدف',
    progress: 'التقدم',
    dueDate: 'تاريخ الاستحقاق',
    achievementTitle: 'عنوان الإنجاز',
    achievementDate: 'تاريخ الإنجاز'
  },
  en: {
    performance: 'Performance',
    performanceReviews: 'Performance Reviews',
    goals: 'Goals',
    achievements: 'Achievements',
    createReview: 'Create Review',
    search: 'Search',
    filter: 'Filter',
    employee: 'Employee',
    department: 'Department',
    reviewPeriod: 'Review Period',
    overallRating: 'Overall Rating',
    status: 'Status',
    actions: 'Actions',
    pending: 'Pending',
    completed: 'Completed',
    inProgress: 'In Progress',
    excellent: 'Excellent',
    good: 'Good',
    average: 'Average',
    needsImprovement: 'Needs Improvement',
    viewDetails: 'View Details',
    edit: 'Edit',
    totalReviews: 'Total Reviews',
    completedReviews: 'Completed Reviews',
    pendingReviews: 'Pending Reviews',
    averageRating: 'Average Rating',
    goalTitle: 'Goal Title',
    progress: 'Progress',
    dueDate: 'Due Date',
    achievementTitle: 'Achievement Title',
    achievementDate: 'Achievement Date'
  }
}

export default function Performance({ language }: PerformanceProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [activeTab, setActiveTab] = useState('reviews')
  const t = translations[language]
  const isRTL = language === 'ar'

  // Mock data
  const performanceStats = {
    totalReviews: 45,
    completedReviews: 38,
    pendingReviews: 7,
    averageRating: 4.2
  }

  const performanceReviews = [
    {
      id: 1,
      employee: 'أحمد محمد',
      department: 'تقنية المعلومات',
      reviewPeriod: 'Q4 2023',
      overallRating: 4.5,
      status: 'completed'
    },
    {
      id: 2,
      employee: 'فاطمة علي',
      department: 'الموارد البشرية',
      reviewPeriod: 'Q4 2023',
      overallRating: 4.0,
      status: 'completed'
    },
    {
      id: 3,
      employee: 'محمد حسن',
      department: 'المالية',
      reviewPeriod: 'Q4 2023',
      overallRating: 0,
      status: 'pending'
    }
  ]

  const goals = [
    {
      id: 1,
      employee: 'أحمد محمد',
      title: 'تطوير نظام إدارة المشاريع',
      progress: 85,
      dueDate: '2024-03-31',
      status: 'inProgress'
    },
    {
      id: 2,
      employee: 'فاطمة علي',
      title: 'تحسين عملية التوظيف',
      progress: 100,
      dueDate: '2024-02-28',
      status: 'completed'
    }
  ]

  const achievements = [
    {
      id: 1,
      employee: 'أحمد محمد',
      title: 'إكمال شهادة AWS',
      date: '2024-01-15',
      type: 'certification'
    },
    {
      id: 2,
      employee: 'فاطمة علي',
      title: 'تقليل وقت التوظيف بنسبة 30%',
      date: '2024-01-10',
      type: 'improvement'
    }
  ]

  const getRatingColor = (rating: number) => {
    if (rating >= 4.5) return 'text-green-400'
    if (rating >= 4.0) return 'text-blue-400'
    if (rating >= 3.0) return 'text-yellow-400'
    return 'text-red-400'
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'inProgress':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">{t.performance}</h1>
          <p className="text-white/70">إدارة تقييم الأداء والأهداف</p>
        </div>
        <Button className="glass-button">
          <Plus className="h-4 w-4 mr-2" />
          {t.createReview}
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.totalReviews}</p>
                <p className="text-2xl font-bold text-white">{performanceStats.totalReviews}</p>
              </div>
              <BarChart3 className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.completedReviews}</p>
                <p className="text-2xl font-bold text-green-400">{performanceStats.completedReviews}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.pendingReviews}</p>
                <p className="text-2xl font-bold text-yellow-400">{performanceStats.pendingReviews}</p>
              </div>
              <Calendar className="h-8 w-8 text-yellow-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.averageRating}</p>
                <p className="text-2xl font-bold text-blue-400">{performanceStats.averageRating}/5</p>
              </div>
              <Star className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-white/10 p-1 rounded-lg">
        <button
          onClick={() => setActiveTab('reviews')}
          className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all ${
            activeTab === 'reviews'
              ? 'bg-white/20 text-white shadow-lg'
              : 'text-white/70 hover:text-white hover:bg-white/10'
          }`}
        >
          {t.performanceReviews}
        </button>
        <button
          onClick={() => setActiveTab('goals')}
          className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all ${
            activeTab === 'goals'
              ? 'bg-white/20 text-white shadow-lg'
              : 'text-white/70 hover:text-white hover:bg-white/10'
          }`}
        >
          {t.goals}
        </button>
        <button
          onClick={() => setActiveTab('achievements')}
          className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all ${
            activeTab === 'achievements'
              ? 'bg-white/20 text-white shadow-lg'
              : 'text-white/70 hover:text-white hover:bg-white/10'
          }`}
        >
          {t.achievements}
        </button>
      </div>

      {/* Content based on active tab */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <CardTitle className="text-white text-xl">
                {activeTab === 'reviews' && t.performanceReviews}
                {activeTab === 'goals' && t.goals}
                {activeTab === 'achievements' && t.achievements}
              </CardTitle>
              <CardDescription className="text-white/70">
                {activeTab === 'reviews' && 'إدارة مراجعات الأداء'}
                {activeTab === 'goals' && 'متابعة الأهداف والتقدم'}
                {activeTab === 'achievements' && 'عرض الإنجازات والجوائز'}
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 h-4 w-4" />
                <Input
                  placeholder={t.search}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 glass-input"
                />
              </div>
              <Button variant="outline" className="glass-button">
                <Filter className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            {activeTab === 'reviews' && (
              <table className="w-full">
                <thead>
                  <tr className="border-b border-white/20">
                    <th className="text-left py-3 px-4 text-white/80 font-medium">{t.employee}</th>
                    <th className="text-left py-3 px-4 text-white/80 font-medium">{t.department}</th>
                    <th className="text-left py-3 px-4 text-white/80 font-medium">{t.reviewPeriod}</th>
                    <th className="text-left py-3 px-4 text-white/80 font-medium">{t.overallRating}</th>
                    <th className="text-left py-3 px-4 text-white/80 font-medium">{t.status}</th>
                    <th className="text-left py-3 px-4 text-white/80 font-medium">{t.actions}</th>
                  </tr>
                </thead>
                <tbody>
                  {performanceReviews.map((review) => (
                    <tr key={review.id} className="border-b border-white/10 hover:bg-white/5">
                      <td className="py-3 px-4">
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4 text-blue-400" />
                          <span className="text-white">{review.employee}</span>
                        </div>
                      </td>
                      <td className="py-3 px-4 text-white/80">{review.department}</td>
                      <td className="py-3 px-4 text-white/80">{review.reviewPeriod}</td>
                      <td className="py-3 px-4">
                        <div className="flex items-center gap-1">
                          <Star className={`h-4 w-4 ${getRatingColor(review.overallRating)}`} />
                          <span className={`font-medium ${getRatingColor(review.overallRating)}`}>
                            {review.overallRating > 0 ? review.overallRating : '-'}
                          </span>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(review.status)}`}>
                          {t[review.status as keyof typeof t]}
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex gap-2">
                          <Button size="sm" className="glass-button">
                            {t.viewDetails}
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            )}

            {activeTab === 'goals' && (
              <table className="w-full">
                <thead>
                  <tr className="border-b border-white/20">
                    <th className="text-left py-3 px-4 text-white/80 font-medium">{t.employee}</th>
                    <th className="text-left py-3 px-4 text-white/80 font-medium">{t.goalTitle}</th>
                    <th className="text-left py-3 px-4 text-white/80 font-medium">{t.progress}</th>
                    <th className="text-left py-3 px-4 text-white/80 font-medium">{t.dueDate}</th>
                    <th className="text-left py-3 px-4 text-white/80 font-medium">{t.status}</th>
                  </tr>
                </thead>
                <tbody>
                  {goals.map((goal) => (
                    <tr key={goal.id} className="border-b border-white/10 hover:bg-white/5">
                      <td className="py-3 px-4">
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4 text-blue-400" />
                          <span className="text-white">{goal.employee}</span>
                        </div>
                      </td>
                      <td className="py-3 px-4 text-white/80">{goal.title}</td>
                      <td className="py-3 px-4">
                        <div className="flex items-center gap-2">
                          <div className="w-20 bg-white/20 rounded-full h-2">
                            <div 
                              className="bg-gradient-to-r from-blue-500 to-green-500 h-2 rounded-full"
                              style={{ width: `${goal.progress}%` }}
                            ></div>
                          </div>
                          <span className="text-white text-sm">{goal.progress}%</span>
                        </div>
                      </td>
                      <td className="py-3 px-4 text-white/80">{goal.dueDate}</td>
                      <td className="py-3 px-4">
                        <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(goal.status)}`}>
                          {t[goal.status as keyof typeof t]}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            )}

            {activeTab === 'achievements' && (
              <table className="w-full">
                <thead>
                  <tr className="border-b border-white/20">
                    <th className="text-left py-3 px-4 text-white/80 font-medium">{t.employee}</th>
                    <th className="text-left py-3 px-4 text-white/80 font-medium">{t.achievementTitle}</th>
                    <th className="text-left py-3 px-4 text-white/80 font-medium">{t.achievementDate}</th>
                  </tr>
                </thead>
                <tbody>
                  {achievements.map((achievement) => (
                    <tr key={achievement.id} className="border-b border-white/10 hover:bg-white/5">
                      <td className="py-3 px-4">
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4 text-blue-400" />
                          <span className="text-white">{achievement.employee}</span>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex items-center gap-2">
                          <Award className="h-4 w-4 text-yellow-400" />
                          <span className="text-white">{achievement.title}</span>
                        </div>
                      </td>
                      <td className="py-3 px-4 text-white/80">{achievement.date}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
