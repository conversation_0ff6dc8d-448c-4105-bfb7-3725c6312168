import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  CardContent, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { 
  Key, 
  Plus, 
  RefreshCw, 
  Trash2,
  Copy,
  Eye,
  EyeOff,
  Shield,
  Activity,
  Clock,
  AlertTriangle,
  CheckCircle,
  Search
} from 'lucide-react';
// Language is passed as prop, not from context

interface APIKey {
  id: number;
  name: string;
  description: string;
  key_type: string;
  key_id: string;
  key_prefix: string;
  scopes: string[];
  allowed_ips: string[];
  rate_limit: number;
  status: string;
  expires_at: string | null;
  last_used_at: string | null;
  usage_count: number;
  is_valid_status: boolean;
  days_until_expiry: number | null;
  created_by_name: string;
  created_at: string;
}

interface APIStats {
  total_keys: number;
  active_keys: number;
  total_usage: number;
  average_usage: number;
  recently_used_keys: number;
}

interface APIManagementProps {
  language?: 'ar' | 'en';
}

// @ts-ignore
const APIManagement: (React as any).FC<APIManagementProps> = ({ language = 'en' }) => {
  // @ts-ignore
  const [apiKeys, setApiKeys] = useState<APIKey[]>([]);
  // @ts-ignore
  const [apiStats, setApiStats] = useState<APIStats | null>(null);
  const [loading, setLoading] = useState(true as any);
  const [searchTerm, setSearchTerm] = useState('' as any);
  const [typeFilter, setTypeFilter] = useState('' as any);
  const [statusFilter, setStatusFilter] = useState('' as any);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false as any);
  // @ts-ignore
  const [selectedKey, setSelectedKey] = useState<APIKey | null>(null);
  // @ts-ignore
  const [showSecret, setShowSecret] = useState<{[key: number]: boolean}>({});
  // @ts-ignore
  const [newSecret, setNewSecret] = useState<string>('');

  // Form state
  const [keyForm, setKeyForm] = useState({
    // @ts-ignore
    name: '',
    description: '',
    key_type: 'INTERNAL',
    scopes: [] as string[],
    allowed_ips: [] as string[],
    rate_limit: 1000,
    expires_at: ''
  // @ts-ignore
  } as any);

  // @ts-ignore
  useEffect(( as any) => {
    // @ts-ignore
    fetchAPIKeys( as any);
    fetchAPIStats( as any);
  // @ts-ignore
  }, []);

  // @ts-ignore
  const fetchAPIKeys: any = async () => {
    // @ts-ignore
    try {
      // @ts-ignore
      const params = new URLSearchParams( as any);
      if (searchTerm) (params as any).append('search', searchTerm as any);
      if (typeFilter && typeFilter !== '__all__') (params as any).append('key_type', typeFilter as any);
      if (statusFilter && statusFilter !== '__all__') (params as any).append('status', statusFilter as any);
      
      // @ts-ignore
      const response: any = await fetch(`/api/api-keys/?${(params as any as any).toString( as any)}`);
      if ((response as any).ok) {
        // @ts-ignore
        const data: any = await (response as any).json( as any);
        setApiKeys((data as any as any).results || data);
      // @ts-ignore
      }
    // @ts-ignore
    } catch (error) {
      // @ts-ignore
      (console as any).error('Error fetching API keys:', error as any);
    // @ts-ignore
    } finally {
      // @ts-ignore
      setLoading(false as any);
    // @ts-ignore
    }
  // @ts-ignore
  };

  // @ts-ignore
  const fetchAPIStats: any = async () => {
    // @ts-ignore
    try {
      // @ts-ignore
      const response = await fetch('/api/api-keys/usage_stats/' as any);
      if ((response as any).ok) {
        // @ts-ignore
        const data: any = await (response as any).json( as any);
        setApiStats(data as any);
      // @ts-ignore
      }
    // @ts-ignore
    } catch (error) {
      // @ts-ignore
      (console as any).error('Error fetching API stats:', error as any);
    // @ts-ignore
    }
  // @ts-ignore
  };

  // @ts-ignore
  const handleCreateKey: any = async () => {
    // @ts-ignore
    try {
      // @ts-ignore
      const response = await fetch('/api/api-keys/', {
        // @ts-ignore
        method: 'POST',
        headers: {
          // @ts-ignore
          'Content-Type': 'application/json',
        // @ts-ignore
        },
        body: (JSON as any as any).stringify({
          ...keyForm,
          // @ts-ignore
          expires_at: (keyForm as any as any).expires_at || null,
        // @ts-ignore
        }),
      // @ts-ignore
      });

      if ((response as any).ok) {
        // @ts-ignore
        const data: any = await (response as any).json( as any);
        setNewSecret((data as any as any).key_secret_plain);
        fetchAPIKeys( as any);
        fetchAPIStats( as any);
        setIsAddDialogOpen(false as any);
        resetForm( as any);
      // @ts-ignore
      }
    // @ts-ignore
    } catch (error) {
      // @ts-ignore
      (console as any).error('Error creating API key:', error as any);
    // @ts-ignore
    }
  // @ts-ignore
  };

  // @ts-ignore
  const handleRegenerateKey: any = async (keyId: number) => {
    // @ts-ignore
    try {
      // @ts-ignore
      const response = await fetch(`/api/api-keys/${keyId}/regenerate/`, {
        // @ts-ignore
        method: 'POST',
      // @ts-ignore
      } as any);

      if ((response as any).ok) {
        // @ts-ignore
        const data: any = await (response as any).json( as any);
        setNewSecret((data as any as any).key_secret_plain);
        fetchAPIKeys( as any);
        alert(language === 'ar' ? 'تم تجديد المفتاح بنجاح' : 'API key regenerated successfully' as any);
      // @ts-ignore
      }
    // @ts-ignore
    } catch (error) {
      // @ts-ignore
      (console as any).error('Error regenerating API key:', error as any);
    // @ts-ignore
    }
  // @ts-ignore
  };

  // @ts-ignore
  const handleRevokeKey: any = async (keyId: number) => {
    // @ts-ignore
    if (!confirm(language === 'ar' ? 'هل أنت متأكد من إلغاء هذا المفتاح؟' : 'Are you sure you want to revoke this API key?' as any)) {
      // @ts-ignore
      return;
    // @ts-ignore
    }

    try {
      // @ts-ignore
      const response: any = await fetch(`/api/api-keys/${keyId}/revoke/`, {
        // @ts-ignore
        method: 'POST',
      // @ts-ignore
      } as any);

      if ((response as any).ok) {
        // @ts-ignore
        fetchAPIKeys( as any);
        fetchAPIStats( as any);
        alert(language === 'ar' ? 'تم إلغاء المفتاح بنجاح' : 'API key revoked successfully' as any);
      // @ts-ignore
      }
    // @ts-ignore
    } catch (error) {
      // @ts-ignore
      (console as any).error('Error revoking API key:', error as any);
    // @ts-ignore
    }
  // @ts-ignore
  };

  // @ts-ignore
  const resetForm: any = (): void => {
    setKeyForm({
      name: '',
      description: '',
      key_type: 'INTERNAL',
      scopes: [],
      allowed_ips: [],
      rate_limit: 1000,
      expires_at: ''
    // @ts-ignore
    } as any);
  // @ts-ignore
  };

  // @ts-ignore
  const copyToClipboard: any = (text: string): void => {
    // @ts-ignore
    (navigator as any).clipboard.writeText(text as any);
    alert(language === 'ar' ? 'تم النسخ إلى الحافظة' : 'Copied to clipboard' as any);
  // @ts-ignore
  };

  // @ts-ignore
  const getStatusColor: any = (status: string, isValid: boolean): void => {
    // @ts-ignore
    if (!isValid) return 'bg-red-100 text-red-800';
    
    const colors: any = {
      // @ts-ignore
      'ACTIVE': 'bg-green-100 text-green-800',
      'INACTIVE': 'bg-gray-100 text-gray-800',
      'EXPIRED': 'bg-red-100 text-red-800',
      'REVOKED': 'bg-red-100 text-red-800'
    // @ts-ignore
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  // @ts-ignore
  };

  // @ts-ignore
  const getTypeIcon: any = (type: string): void => {
    // @ts-ignore
    switch (type) {
      // @ts-ignore
      case 'INTERNAL':
        return <Shield className="h-4 w-4 text-blue-600" />;
      case 'EXTERNAL':
        return <Key className="h-4 w-4 text-green-600" />;
      case 'WEBHOOK':
        return <Activity className="h-4 w-4 text-purple-600" />;
      case 'INTEGRATION':
        return <RefreshCw className="h-4 w-4 text-orange-600" />;
      default:
        return <Key className="h-4 w-4 text-gray-600" />;
    // @ts-ignore
    }
  // @ts-ignore
  };

  // @ts-ignore
  const formatDate: any = (dateString: string | null): string => {
    // @ts-ignore
    if (!dateString) return '-';
    return new Date(dateString as any).toLocaleDateString( as any);
  // @ts-ignore
  };

  // @ts-ignore
  const filteredKeys: any = (apiKeys as any).filter(key => {
    // @ts-ignore
    const matchesSearch = !searchTerm || 
      (key as any as any).name.toLowerCase( as any).includes((searchTerm as any as any).toLowerCase( as any)) ||
      (key as any).key_id.toLowerCase( as any).includes((searchTerm as any as any).toLowerCase( as any));
    
    const matchesType: any = !typeFilter || typeFilter === '__all__' || (key as any).key_type === typeFilter;
    const matchesStatus: any = !statusFilter || statusFilter === '__all__' || (key as any).status === statusFilter;
    
    return matchesSearch && matchesType && matchesStatus;
  // @ts-ignore
  });

  if (loading) {
    // @ts-ignore
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">
          {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
        </div>
      </div>
    );
  // @ts-ignore
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">
            {language === 'ar' ? 'إدارة مفاتيح API' : 'API Key Management'}
          </h1>
          <p className="text-muted-foreground">
            {language === 'ar' 
              ? 'إنشاء وإدارة مفاتيح API للتكامل مع الخدمات الخارجية'
              : 'Create and manage API keys for external service integrations'
            }
          </p>
        </div>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              {language === 'ar' ? 'إنشاء مفتاح' : 'Create Key'}
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>
                {language === 'ar' ? 'إنشاء مفتاح API جديد' : 'Create New API Key'}
              </DialogTitle>
            </DialogHeader>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label>{language === 'ar' ? 'اسم المفتاح' : 'Key Name'}</Label>
                <Input
                  value={(keyForm as any).name}
                  onChange={(e: any) => setKeyForm({...keyForm, name: (e as any as any).target.value})}
                  placeholder={language === 'ar' ? 'أدخل اسم المفتاح' : 'Enter key name'}
                />
              </div>
              <div>
                <Label>{language === 'ar' ? 'نوع المفتاح' : 'Key Type'}</Label>
                <Select value={(keyForm as any).key_type} onValueChange={(value) => setKeyForm({...keyForm, key_type: value} as any)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="INTERNAL">
                      {language === 'ar' ? 'داخلي' : 'Internal'}
                    </SelectItem>
                    <SelectItem value="EXTERNAL">
                      {language === 'ar' ? 'خارجي' : 'External'}
                    </SelectItem>
                    <SelectItem value="WEBHOOK">
                      {language === 'ar' ? 'ويب هوك' : 'Webhook'}
                    </SelectItem>
                    <SelectItem value="INTEGRATION">
                      {language === 'ar' ? 'تكامل' : 'Integration'}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="md:col-span-2">
                <Label>{language === 'ar' ? 'الوصف' : 'Description'}</Label>
                <Textarea
                  value={(keyForm as any).description}
                  onChange={(e: any) => setKeyForm({...keyForm, description: (e as any as any).target.value})}
                  placeholder={language === 'ar' ? 'أدخل وصف المفتاح' : 'Enter key description'}
                  rows={3}
                />
              </div>
              <div>
                <Label>{language === 'ar' ? 'حد المعدل (في الساعة)' : 'Rate Limit (per hour)'}</Label>
                <Input
                  type="number"
                  value={(keyForm as any).rate_limit}
                  onChange={(e: any) => setKeyForm({...keyForm, rate_limit: parseInt((e as any as any).target.value) || 1000})}
                />
              </div>
              <div>
                <Label>{language === 'ar' ? 'تاريخ الانتهاء' : 'Expiry Date'}</Label>
                <Input
                  type="datetime-local"
                  value={(keyForm as any).expires_at}
                  onChange={(e: any) => setKeyForm({...keyForm, expires_at: (e as any as any).target.value})}
                />
              </div>
            </div>
            <div className="flex justify-end gap-2 mt-4">
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false as any)}>
                {language === 'ar' ? 'إلغاء' : 'Cancel'}
              </Button>
              <Button onClick={handleCreateKey}>
                {language === 'ar' ? 'إنشاء المفتاح' : 'Create Key'}
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Statistics */}
      {apiStats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'إجمالي المفاتيح' : 'Total Keys'}
              </CardTitle>
              <Key className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{(apiStats as any).total_keys}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'المفاتيح النشطة' : 'Active Keys'}
              </CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{(apiStats as any).active_keys}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'إجمالي الاستخدام' : 'Total Usage'}
              </CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              // @ts-ignore
              <div className="text-2xl font-bold">{(apiStats as any).total_usage.toLocaleString( as any)}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'متوسط الاستخدام' : 'Average Usage'}
              </CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{(apiStats as any).average_usage.toFixed(0 as any)}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'مستخدم حديثاً' : 'Recently Used'}
              </CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{(apiStats as any).recently_used_keys}</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-[200px]">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder={language === 'ar' ? 'البحث في المفاتيح...' : 'Search API keys...'}
                  value={searchTerm}
                  onChange={(e: any) => setSearchTerm((e as any as any).target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder={language === 'ar' ? 'جميع الأنواع' : 'All types'} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="__all__">
                  {language === 'ar' ? 'جميع الأنواع' : 'All types'}
                </SelectItem>
                <SelectItem value="INTERNAL">
                  {language === 'ar' ? 'داخلي' : 'Internal'}
                </SelectItem>
                <SelectItem value="EXTERNAL">
                  {language === 'ar' ? 'خارجي' : 'External'}
                </SelectItem>
                <SelectItem value="WEBHOOK">
                  {language === 'ar' ? 'ويب هوك' : 'Webhook'}
                </SelectItem>
                <SelectItem value="INTEGRATION">
                  {language === 'ar' ? 'تكامل' : 'Integration'}
                </SelectItem>
              </SelectContent>
            </Select>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder={language === 'ar' ? 'جميع الحالات' : 'All statuses'} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="__all__">
                  {language === 'ar' ? 'جميع الحالات' : 'All statuses'}
                </SelectItem>
                <SelectItem value="ACTIVE">
                  {language === 'ar' ? 'نشط' : 'Active'}
                </SelectItem>
                <SelectItem value="INACTIVE">
                  {language === 'ar' ? 'غير نشط' : 'Inactive'}
                </SelectItem>
                <SelectItem value="EXPIRED">
                  {language === 'ar' ? 'منتهي الصلاحية' : 'Expired'}
                </SelectItem>
                <SelectItem value="REVOKED">
                  {language === 'ar' ? 'ملغي' : 'Revoked'}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* API Keys Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Key className="h-5 w-5" />
            {language === 'ar' ? 'مفاتيح API' : 'API Keys'}
            <Badge variant="secondary" className="ml-2">
              {(filteredKeys as any).length}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{language === 'ar' ? 'اسم المفتاح' : 'Key Name'}</TableHead>
                  <TableHead>{language === 'ar' ? 'النوع' : 'Type'}</TableHead>
                  <TableHead>{language === 'ar' ? 'معرف المفتاح' : 'Key ID'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الحالة' : 'Status'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الاستخدام' : 'Usage'}</TableHead>
                  <TableHead>{language === 'ar' ? 'انتهاء الصلاحية' : 'Expires'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الإجراءات' : 'Actions'}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                // @ts-ignore
                {(filteredKeys as any).map((key as any) => (
                  <TableRow key={(key as any).id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{(key as any).name}</div>
                        <div className="text-sm text-muted-foreground">
                          {(key as any).description}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getTypeIcon((key as any as any).key_type)}
                        <span className="text-sm">
                          {language === 'ar' 
                            ? (key as any).key_type === 'INTERNAL' ? 'داخلي' 
                              : (key as any).key_type === 'EXTERNAL' ? 'خارجي'
                              : (key as any).key_type === 'WEBHOOK' ? 'ويب هوك'
                              : (key as any).key_type === 'INTEGRATION' ? 'تكامل'
                              : (key as any).key_type
                            : (key as any).key_type
                          }
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <code className="text-sm bg-muted px-2 py-1 rounded">
                          {(key as any).key_id}
                        </code>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => copyToClipboard((key as any as any).key_id)}
                        >
                          <Copy className="h-3 w-3" />
                        </Button>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={getStatusColor((key as any as any).status, (key as any).is_valid_status)}>
                        {language === 'ar' 
                          ? (key as any).status === 'ACTIVE' ? 'نشط' 
                            : (key as any).status === 'INACTIVE' ? 'غير نشط'
                            : (key as any).status === 'EXPIRED' ? 'منتهي'
                            : (key as any).status === 'REVOKED' ? 'ملغي'
                            : (key as any).status
                          : (key as any).status
                        }
                      </Badge>
                      {(key as any).days_until_expiry !== null && (key as any).days_until_expiry <= 30 && (
                        <div className="flex items-center gap-1 mt-1">
                          <AlertTriangle className="h-3 w-3 text-orange-500" />
                          <span className="text-xs text-orange-600">
                            {(key as any).days_until_expiry} {language === 'ar' ? 'أيام متبقية' : 'days left'}
                          </span>
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      <div>
                        // @ts-ignore
                        <div className="font-medium">{(key as any).usage_count.toLocaleString( as any)}</div>
                        <div className="text-sm text-muted-foreground">
                          // @ts-ignore
                          {language === 'ar' ? 'حد:' : 'Limit:'} {(key as any).rate_limit.toLocaleString( as any)}/h
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="text-sm">
                          {formatDate((key as any as any).expires_at)}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {language === 'ar' ? 'آخر استخدام:' : 'Last used:'} {formatDate((key as any as any).last_used_at)}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleRegenerateKey((key as any as any).id)}
                          disabled={(key as any).status === 'REVOKED'}
                        >
                          <RefreshCw className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleRevokeKey((key as any as any).id)}
                          disabled={(key as any).status === 'REVOKED'}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                // @ts-ignore
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* New Secret Dialog */}
      {newSecret && (
        <Dialog open={!!newSecret} onOpenChange={() => setNewSecret('' as any)}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>
                {language === 'ar' ? 'مفتاح API الجديد' : 'New API Key Secret'}
              </DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <AlertTriangle className="h-4 w-4 text-yellow-600" />
                  <span className="font-medium text-yellow-800">
                    {language === 'ar' ? 'تحذير مهم' : 'Important Warning'}
                  </span>
                </div>
                <p className="text-sm text-yellow-700">
                  {language === 'ar' 
                    ? 'هذا هو المفتاح السري الوحيد الذي ستراه. احفظه في مكان آمن.'
                    : 'This is the only time you will see this secret. Save it securely.'
                  }
                </p>
              </div>
              
              <div>
                <Label>{language === 'ar' ? 'المفتاح السري' : 'API Secret'}</Label>
                <div className="flex items-center gap-2">
                  <Input
                    value={newSecret}
                    readOnly
                    className="font-mono"
                  />
                  <Button
                    variant="outline"
                    onClick={() => copyToClipboard(newSecret as any)}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              
              <div className="flex justify-end">
                <Button onClick={() => setNewSecret('' as any)}>
                  {language === 'ar' ? 'فهمت' : 'Got it'}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
// @ts-ignore
};

export default APIManagement;
