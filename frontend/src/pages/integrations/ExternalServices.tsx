import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  <PERSON>Content, 
  Card<PERSON>eader, 
  CardTitle 
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { 
  Globe, 
  Plus, 
  Activity, 
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  Settings,
  Play,
  Search,
  CreditCard,
  Truck,
  Mail,
  MessageSquare,
  Cloud,
  BarChart3
} from 'lucide-react';
// Language is passed as prop, not from context

interface ExternalService {
  id: number;
  name: string;
  service_type: string;
  description: string;
  base_url: string;
  api_version: string;
  authentication_type: string;
  status: string;
  is_healthy: boolean;
  success_rate: number;
  total_requests: number;
  successful_requests: number;
  failed_requests: number;
  last_request_at: string | null;
  health_status: string;
  created_by_name: string;
  created_at: string;
}

interface ServiceStats {
  total_services: number;
  active_services: number;
  healthy_services: number;
  total_requests: number;
  average_success_rate: number;
  service_type_breakdown: Array<{
    service_type: string;
    count: number;
    healthy_count: number;
  }>;
}

interface ExternalServicesProps {
  language?: 'ar' | 'en';
}

// @ts-ignore
const ExternalServices: (React as any).FC<ExternalServicesProps> = ({ language = 'en' }) => {
  // @ts-ignore
  const [services, setServices] = useState<ExternalService[]>([]);
  // @ts-ignore
  const [serviceStats, setServiceStats] = useState<ServiceStats | null>(null);
  const [loading, setLoading] = useState(true as any);
  const [searchTerm, setSearchTerm] = useState('' as any);
  const [typeFilter, setTypeFilter] = useState('' as any);
  const [statusFilter, setStatusFilter] = useState('' as any);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false as any);
  // @ts-ignore
  const [testingService, setTestingService] = useState<number | null>(null);

  // Form state
  const [serviceForm, setServiceForm] = useState({
    // @ts-ignore
    name: '',
    service_type: 'CUSTOM',
    description: '',
    base_url: '',
    api_version: '',
    authentication_type: 'API_KEY',
    timeout: 30,
    health_check_url: ''
  // @ts-ignore
  } as any);

  // @ts-ignore
  useEffect(( as any) => {
    // @ts-ignore
    fetchServices( as any);
    fetchServiceStats( as any);
  // @ts-ignore
  }, []);

  // @ts-ignore
  const fetchServices: any = async () => {
    // @ts-ignore
    try {
      // @ts-ignore
      const params = new URLSearchParams( as any);
      if (searchTerm) (params as any).append('search', searchTerm as any);
      if (typeFilter && typeFilter !== '__all__') (params as any).append('service_type', typeFilter as any);
      if (statusFilter && statusFilter !== '__all__') (params as any).append('status', statusFilter as any);
      
      // @ts-ignore
      const response: any = await fetch(`/api/external-services/?${(params as any as any).toString( as any)}`);
      if ((response as any).ok) {
        // @ts-ignore
        const data: any = await (response as any).json( as any);
        setServices((data as any as any).results || data);
      // @ts-ignore
      }
    // @ts-ignore
    } catch (error) {
      // @ts-ignore
      (console as any).error('Error fetching services:', error as any);
    // @ts-ignore
    } finally {
      // @ts-ignore
      setLoading(false as any);
    // @ts-ignore
    }
  // @ts-ignore
  };

  // @ts-ignore
  const fetchServiceStats: any = async () => {
    // @ts-ignore
    try {
      // @ts-ignore
      const response = await fetch('/api/external-services/service_stats/' as any);
      if ((response as any).ok) {
        // @ts-ignore
        const data: any = await (response as any).json( as any);
        setServiceStats(data as any);
      // @ts-ignore
      }
    // @ts-ignore
    } catch (error) {
      // @ts-ignore
      (console as any).error('Error fetching service stats:', error as any);
    // @ts-ignore
    }
  // @ts-ignore
  };

  // @ts-ignore
  const handleCreateService: any = async () => {
    // @ts-ignore
    try {
      // @ts-ignore
      const response = await fetch('/api/external-services/', {
        // @ts-ignore
        method: 'POST',
        headers: {
          // @ts-ignore
          'Content-Type': 'application/json',
        // @ts-ignore
        },
        body: (JSON as any as any).stringify(serviceForm as any),
      // @ts-ignore
      });

      if ((response as any).ok) {
        // @ts-ignore
        fetchServices( as any);
        fetchServiceStats( as any);
        setIsAddDialogOpen(false as any);
        resetForm( as any);
      // @ts-ignore
      }
    // @ts-ignore
    } catch (error) {
      // @ts-ignore
      (console as any).error('Error creating service:', error as any);
    // @ts-ignore
    }
  // @ts-ignore
  };

  // @ts-ignore
  const handleHealthCheck: any = async (serviceId: number) => {
    // @ts-ignore
    setTestingService(serviceId as any);
    try {
      // @ts-ignore
      const response: any = await fetch(`/api/external-services/${serviceId}/health_check/`, {
        // @ts-ignore
        method: 'POST',
      // @ts-ignore
      } as any);

      if ((response as any).ok) {
        // @ts-ignore
        const data: any = await (response as any).json( as any);
        fetchServices( as any); // Refresh to get updated health status
        alert(
          language === 'ar' 
            ? `فحص الصحة: ${(data as any as any).is_healthy ? 'صحي' : 'غير صحي'} (${(data as any).response_time_ms}ms)`
            : `Health check: ${(data as any).is_healthy ? 'Healthy' : 'Unhealthy'} (${(data as any).response_time_ms}ms)`
        );
      // @ts-ignore
      }
    // @ts-ignore
    } catch (error) {
      // @ts-ignore
      (console as any).error('Error performing health check:', error as any);
    // @ts-ignore
    } finally {
      // @ts-ignore
      setTestingService(null as any);
    // @ts-ignore
    }
  // @ts-ignore
  };

  // @ts-ignore
  const handleTestConnection: any = async (serviceId: number) => {
    // @ts-ignore
    setTestingService(serviceId as any);
    try {
      // @ts-ignore
      const response: any = await fetch(`/api/external-services/${serviceId}/test_connection/`, {
        // @ts-ignore
        method: 'POST',
        headers: {
          // @ts-ignore
          'Content-Type': 'application/json',
        // @ts-ignore
        },
        // @ts-ignore
        body: (JSON as any as any).stringify({ endpoint: '' } as any),
      // @ts-ignore
      });

      if ((response as any).ok) {
        // @ts-ignore
        const data: any = await (response as any).json( as any);
        alert(
          language === 'ar' 
            ? `اختبار الاتصال: ${(data as any as any).success ? 'نجح' : 'فشل'} (${(data as any).response_time_ms || 0}ms)`
            : `Connection test: ${(data as any).success ? 'Success' : 'Failed'} (${(data as any).response_time_ms || 0}ms)`
        );
      // @ts-ignore
      }
    // @ts-ignore
    } catch (error) {
      // @ts-ignore
      (console as any).error('Error testing connection:', error as any);
    // @ts-ignore
    } finally {
      // @ts-ignore
      setTestingService(null as any);
    // @ts-ignore
    }
  // @ts-ignore
  };

  // @ts-ignore
  const resetForm: any = (): void => {
    setServiceForm({
      name: '',
      service_type: 'CUSTOM',
      description: '',
      base_url: '',
      api_version: '',
      authentication_type: 'API_KEY',
      timeout: 30,
      health_check_url: ''
    // @ts-ignore
    } as any);
  // @ts-ignore
  };

  // @ts-ignore
  const getServiceTypeIcon: any = (type: string): void => {
    // @ts-ignore
    switch (type) {
      // @ts-ignore
      case 'PAYMENT':
        return <CreditCard className="h-4 w-4 text-green-600" />;
      case 'SHIPPING':
        return <Truck className="h-4 w-4 text-blue-600" />;
      case 'EMAIL':
        return <Mail className="h-4 w-4 text-purple-600" />;
      case 'SMS':
        return <MessageSquare className="h-4 w-4 text-orange-600" />;
      case 'STORAGE':
        return <Cloud className="h-4 w-4 text-cyan-600" />;
      case 'ANALYTICS':
        return <BarChart3 className="h-4 w-4 text-indigo-600" />;
      default:
        return <Globe className="h-4 w-4 text-gray-600" />;
    // @ts-ignore
    }
  // @ts-ignore
  };

  // @ts-ignore
  const getStatusColor: any = (status: string): void => {
    // @ts-ignore
    const colors = {
      // @ts-ignore
      'ACTIVE': 'bg-green-100 text-green-800',
      'INACTIVE': 'bg-gray-100 text-gray-800',
      'TESTING': 'bg-blue-100 text-blue-800',
      'ERROR': 'bg-red-100 text-red-800',
      'MAINTENANCE': 'bg-yellow-100 text-yellow-800'
    // @ts-ignore
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  // @ts-ignore
  };

  // @ts-ignore
  const getHealthIcon: any = (healthStatus: string, isHealthy: boolean): void => {
    // @ts-ignore
    if (healthStatus === 'UNKNOWN') {
      // @ts-ignore
      return <Clock className="h-4 w-4 text-gray-500" />;
    // @ts-ignore
    } else if (healthStatus === 'STALE') {
      // @ts-ignore
      return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
    // @ts-ignore
    } else if (isHealthy) {
      // @ts-ignore
      return <CheckCircle className="h-4 w-4 text-green-500" />;
    // @ts-ignore
    } else {
      // @ts-ignore
      return <XCircle className="h-4 w-4 text-red-500" />;
    // @ts-ignore
    }
  // @ts-ignore
  };

  // @ts-ignore
  const formatDate: any = (dateString: string | null): string => {
    // @ts-ignore
    if (!dateString) return '-';
    return new Date(dateString as any).toLocaleDateString( as any);
  // @ts-ignore
  };

  // @ts-ignore
  const filteredServices: any = (services as any).filter(service => {
    // @ts-ignore
    const matchesSearch = !searchTerm ||
      (service as any as any).name.toLowerCase( as any).includes((searchTerm as any as any).toLowerCase( as any)) ||
      (service as any).base_url.toLowerCase( as any).includes((searchTerm as any as any).toLowerCase( as any));

    const matchesType: any = !typeFilter || typeFilter === '__all__' || (service as any).service_type === typeFilter;
    const matchesStatus: any = !statusFilter || statusFilter === '__all__' || (service as any).status === statusFilter;
    
    return matchesSearch && matchesType && matchesStatus;
  // @ts-ignore
  });

  if (loading) {
    // @ts-ignore
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">
          {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
        </div>
      </div>
    );
  // @ts-ignore
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">
            {language === 'ar' ? 'الخدمات الخارجية' : 'External Services'}
          </h1>
          <p className="text-muted-foreground">
            {language === 'ar' 
              ? 'إدارة ومراقبة التكامل مع الخدمات الخارجية'
              : 'Manage and monitor external service integrations'
            }
          </p>
        </div>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              {language === 'ar' ? 'إضافة خدمة' : 'Add Service'}
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>
                {language === 'ar' ? 'إضافة خدمة خارجية جديدة' : 'Add New External Service'}
              </DialogTitle>
            </DialogHeader>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label>{language === 'ar' ? 'اسم الخدمة' : 'Service Name'}</Label>
                <Input
                  value={(serviceForm as any).name}
                  onChange={(e: any) => setServiceForm({...serviceForm, name: (e as any as any).target.value})}
                  placeholder={language === 'ar' ? 'أدخل اسم الخدمة' : 'Enter service name'}
                />
              </div>
              <div>
                <Label>{language === 'ar' ? 'نوع الخدمة' : 'Service Type'}</Label>
                <Select value={(serviceForm as any).service_type} onValueChange={(value) => setServiceForm({...serviceForm, service_type: value} as any)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="PAYMENT">
                      {language === 'ar' ? 'دفع' : 'Payment'}
                    </SelectItem>
                    <SelectItem value="SHIPPING">
                      {language === 'ar' ? 'شحن' : 'Shipping'}
                    </SelectItem>
                    <SelectItem value="EMAIL">
                      {language === 'ar' ? 'بريد إلكتروني' : 'Email'}
                    </SelectItem>
                    <SelectItem value="SMS">
                      {language === 'ar' ? 'رسائل نصية' : 'SMS'}
                    </SelectItem>
                    <SelectItem value="STORAGE">
                      {language === 'ar' ? 'تخزين' : 'Storage'}
                    </SelectItem>
                    <SelectItem value="ANALYTICS">
                      {language === 'ar' ? 'تحليلات' : 'Analytics'}
                    </SelectItem>
                    <SelectItem value="CUSTOM">
                      {language === 'ar' ? 'مخصص' : 'Custom'}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="md:col-span-2">
                <Label>{language === 'ar' ? 'الوصف' : 'Description'}</Label>
                <Textarea
                  value={(serviceForm as any).description}
                  onChange={(e: any) => setServiceForm({...serviceForm, description: (e as any as any).target.value})}
                  placeholder={language === 'ar' ? 'أدخل وصف الخدمة' : 'Enter service description'}
                  rows={3}
                />
              </div>
              <div>
                <Label>{language === 'ar' ? 'الرابط الأساسي' : 'Base URL'}</Label>
                <Input
                  value={(serviceForm as any).base_url}
                  onChange={(e: any) => setServiceForm({...serviceForm, base_url: (e as any as any).target.value})}
                  placeholder="https://(api as any).example.com"
                />
              </div>
              <div>
                <Label>{language === 'ar' ? 'إصدار API' : 'API Version'}</Label>
                <Input
                  value={(serviceForm as any).api_version}
                  onChange={(e: any) => setServiceForm({...serviceForm, api_version: (e as any as any).target.value})}
                  placeholder="v1, v2, etc."
                />
              </div>
              <div>
                <Label>{language === 'ar' ? 'نوع المصادقة' : 'Authentication Type'}</Label>
                <Select value={(serviceForm as any).authentication_type} onValueChange={(value) => setServiceForm({...serviceForm, authentication_type: value} as any)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="API_KEY">API Key</SelectItem>
                    <SelectItem value="OAUTH2">OAuth (2 as any).0</SelectItem>
                    <SelectItem value="BASIC_AUTH">Basic Auth</SelectItem>
                    <SelectItem value="BEARER_TOKEN">Bearer Token</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label>{language === 'ar' ? 'مهلة الاتصال (ثانية)' : 'Timeout (seconds)'}</Label>
                <Input
                  type="number"
                  value={(serviceForm as any).timeout}
                  onChange={(e: any) => setServiceForm({...serviceForm, timeout: parseInt((e as any as any).target.value) || 30})}
                />
              </div>
              <div className="md:col-span-2">
                <Label>{language === 'ar' ? 'رابط فحص الصحة' : 'Health Check URL'}</Label>
                <Input
                  value={(serviceForm as any).health_check_url}
                  onChange={(e: any) => setServiceForm({...serviceForm, health_check_url: (e as any as any).target.value})}
                  placeholder="https://(api as any).example.com/health"
                />
              </div>
            </div>
            <div className="flex justify-end gap-2 mt-4">
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false as any)}>
                {language === 'ar' ? 'إلغاء' : 'Cancel'}
              </Button>
              <Button onClick={handleCreateService}>
                {language === 'ar' ? 'إضافة الخدمة' : 'Add Service'}
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Statistics */}
      {serviceStats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'إجمالي الخدمات' : 'Total Services'}
              </CardTitle>
              <Globe className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{(serviceStats as any).total_services}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'الخدمات النشطة' : 'Active Services'}
              </CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{(serviceStats as any).active_services}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'الخدمات الصحية' : 'Healthy Services'}
              </CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{(serviceStats as any).healthy_services}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'إجمالي الطلبات' : 'Total Requests'}
              </CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              // @ts-ignore
              <div className="text-2xl font-bold">{(serviceStats as any).total_requests.toLocaleString( as any)}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'معدل النجاح' : 'Success Rate'}
              </CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{(serviceStats as any).average_success_rate.toFixed(1 as any)}%</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-[200px]">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder={language === 'ar' ? 'البحث في الخدمات...' : 'Search services...'}
                  value={searchTerm}
                  onChange={(e: any) => setSearchTerm((e as any as any).target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder={language === 'ar' ? 'جميع الأنواع' : 'All types'} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="__all__">
                  {language === 'ar' ? 'جميع الأنواع' : 'All types'}
                </SelectItem>
                <SelectItem value="PAYMENT">
                  {language === 'ar' ? 'دفع' : 'Payment'}
                </SelectItem>
                <SelectItem value="SHIPPING">
                  {language === 'ar' ? 'شحن' : 'Shipping'}
                </SelectItem>
                <SelectItem value="EMAIL">
                  {language === 'ar' ? 'بريد إلكتروني' : 'Email'}
                </SelectItem>
                <SelectItem value="SMS">
                  {language === 'ar' ? 'رسائل نصية' : 'SMS'}
                </SelectItem>
              </SelectContent>
            </Select>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder={language === 'ar' ? 'جميع الحالات' : 'All statuses'} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="__all__">
                  {language === 'ar' ? 'جميع الحالات' : 'All statuses'}
                </SelectItem>
                <SelectItem value="ACTIVE">
                  {language === 'ar' ? 'نشط' : 'Active'}
                </SelectItem>
                <SelectItem value="INACTIVE">
                  {language === 'ar' ? 'غير نشط' : 'Inactive'}
                </SelectItem>
                <SelectItem value="ERROR">
                  {language === 'ar' ? 'خطأ' : 'Error'}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Services Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            {language === 'ar' ? 'الخدمات الخارجية' : 'External Services'}
            <Badge variant="secondary" className="ml-2">
              {(filteredServices as any).length}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{language === 'ar' ? 'اسم الخدمة' : 'Service Name'}</TableHead>
                  <TableHead>{language === 'ar' ? 'النوع' : 'Type'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الحالة' : 'Status'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الصحة' : 'Health'}</TableHead>
                  <TableHead>{language === 'ar' ? 'معدل النجاح' : 'Success Rate'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الطلبات' : 'Requests'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الإجراءات' : 'Actions'}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                // @ts-ignore
                {(filteredServices as any).map((service as any) => (
                  <TableRow key={(service as any).id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{(service as any).name}</div>
                        <div className="text-sm text-muted-foreground">
                          {(service as any).base_url}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getServiceTypeIcon((service as any as any).service_type)}
                        <span className="text-sm">
                          {language === 'ar' 
                            ? (service as any).service_type === 'PAYMENT' ? 'دفع' 
                              : (service as any).service_type === 'SHIPPING' ? 'شحن'
                              : (service as any).service_type === 'EMAIL' ? 'بريد إلكتروني'
                              : (service as any).service_type === 'SMS' ? 'رسائل نصية'
                              : (service as any).service_type === 'STORAGE' ? 'تخزين'
                              : (service as any).service_type === 'ANALYTICS' ? 'تحليلات'
                              : (service as any).service_type
                            : (service as any).service_type
                          }
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={getStatusColor((service as any as any).status)}>
                        {language === 'ar' 
                          ? (service as any).status === 'ACTIVE' ? 'نشط' 
                            : (service as any).status === 'INACTIVE' ? 'غير نشط'
                            : (service as any).status === 'ERROR' ? 'خطأ'
                            : (service as any).status === 'TESTING' ? 'اختبار'
                            : (service as any).status
                          : (service as any).status
                        }
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getHealthIcon((service as any as any).health_status, (service as any).is_healthy)}
                        <span className="text-sm">
                          {language === 'ar' 
                            ? (service as any).health_status === 'HEALTHY' ? 'صحي' 
                              : (service as any).health_status === 'UNHEALTHY' ? 'غير صحي'
                              : (service as any).health_status === 'STALE' ? 'قديم'
                              : 'غير معروف'
                            : (service as any).health_status
                          }
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <span className={(service as any).success_rate >= 95 ? 'text-green-600' : (service as any).success_rate >= 80 ? 'text-yellow-600' : 'text-red-600'}>
                          {(service as any).success_rate.toFixed(1 as any)}%
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        // @ts-ignore
                        <div className="text-sm font-medium">{(service as any).total_requests.toLocaleString( as any)}</div>
                        <div className="text-xs text-muted-foreground">
                          {language === 'ar' ? 'آخر طلب:' : 'Last:'} {formatDate((service as any as any).last_request_at)}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleHealthCheck((service as any as any).id)}
                          disabled={testingService === (service as any).id}
                        >
                          {testingService === (service as any).id ? (
                            <Clock className="h-4 w-4 animate-spin" />
                          ) : (
                            <Activity className="h-4 w-4" />
                          )}
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleTestConnection((service as any as any).id)}
                          disabled={testingService === (service as any).id}
                        >
                          <Play className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <Settings className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                // @ts-ignore
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
// @ts-ignore
};

export default ExternalServices;
