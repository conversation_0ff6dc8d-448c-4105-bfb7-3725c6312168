import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON>ontent, 
  <PERSON><PERSON><PERSON><PERSON>, 
  CardTitle 
} from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { 
  Webhook, 
  Plus, 
  Play, 
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  Settings,
  Search,
  Activity,
  Send,
  Eye
} from 'lucide-react';
// Language is passed as prop, not from context

interface WebhookEndpoint {
  id: number;
  name: string;
  description: string;
  event_types: string[];
  url: string;
  method: string;
  status: string;
  is_verified: boolean;
  last_triggered_at: string | null;
  total_calls: number;
  successful_calls: number;
  failed_calls: number;
  success_rate: number;
  recent_events_count: number;
  created_by_name: string;
  created_at: string;
}

interface WebhookEvent {
  id: number;
  webhook_endpoint: number;
  webhook_name: string;
  webhook_url: string;
  event_type: string;
  event_id: string;
  status: string;
  attempts: number;
  max_attempts: number;
  response_status_code: number | null;
  error_message: string;
  can_retry_status: boolean;
  duration_seconds: number | null;
  created_at: string;
}

interface WebhookStats {
  total_webhooks: number;
  active_webhooks: number;
  verified_webhooks: number;
  total_calls: number;
  average_success_rate: number;
}

interface WebhookManagementProps {
  language?: 'ar' | 'en';
}

const WebhookManagement: React.FC<WebhookManagementProps> = ({ language = 'en' }) => {
  const [webhooks, setWebhooks] = useState<WebhookEndpoint[]>([]);
  const [webhookEvents, setWebhookEvents] = useState<WebhookEvent[]>([]);
  const [webhookStats, setWebhookStats] = useState<WebhookStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isTriggerDialogOpen, setIsTriggerDialogOpen] = useState(false);
  const [selectedWebhook, setSelectedWebhook] = useState<WebhookEndpoint | null>(null);
  const [verifyingWebhook, setVerifyingWebhook] = useState<number | null>(null);

  // Form state
  const [webhookForm, setWebhookForm] = useState({
    name: '',
    description: '',
    url: '',
    method: 'POST',
    event_types: [] as string[],
    max_retries: 3,
    retry_delay: 60,
    timeout: 30
  });

  const [triggerForm, setTriggerForm] = useState({
    event_type: 'CUSTOM',
    payload: '{}'
  });

  const eventTypeOptions = [
    'PAYMENT_RECEIVED',
    'PAYMENT_FAILED',
    'INVOICE_PAID',
    'ORDER_CREATED',
    'ORDER_UPDATED',
    'CUSTOMER_CREATED',
    'ASSET_UPDATED',
    'MAINTENANCE_DUE',
    'CUSTOM'
  ];

  useEffect(() => {
    fetchWebhooks();
    fetchWebhookEvents();
    fetchWebhookStats();
  }, []);

  const fetchWebhooks = async () => {
    try {
      const params = new URLSearchParams();
      if (searchTerm) params.append('search', searchTerm);
      if (statusFilter) params.append('status', statusFilter);
      
      const response = await fetch(`/api/webhook-endpoints/?${params.toString()}`);
      if (response.ok) {
        const data = await response.json();
        setWebhooks(data.results || data);
      }
    } catch (error) {
      console.error('Error fetching webhooks:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchWebhookEvents = async () => {
    try {
      const response = await fetch('/api/webhook-events/?ordering=-created_at&page_size=20');
      if (response.ok) {
        const data = await response.json();
        setWebhookEvents(data.results || data);
      }
    } catch (error) {
      console.error('Error fetching webhook events:', error);
    }
  };

  const fetchWebhookStats = async () => {
    try {
      const response = await fetch('/api/webhook-endpoints/webhook_stats/');
      if (response.ok) {
        const data = await response.json();
        setWebhookStats(data);
      }
    } catch (error) {
      console.error('Error fetching webhook stats:', error);
    }
  };

  const handleCreateWebhook = async () => {
    try {
      const response = await fetch('/api/webhook-endpoints/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(webhookForm),
      });

      if (response.ok) {
        fetchWebhooks();
        fetchWebhookStats();
        setIsAddDialogOpen(false);
        resetForm();
      }
    } catch (error) {
      console.error('Error creating webhook:', error);
    }
  };

  const handleVerifyWebhook = async (webhookId: number) => {
    setVerifyingWebhook(webhookId);
    try {
      const response = await fetch(`/api/webhook-endpoints/${webhookId}/verify/`, {
        method: 'POST',
      });

      if (response.ok) {
        const data = await response.json();
        fetchWebhooks();
        alert(
          language === 'ar' 
            ? `التحقق: ${data.verified ? 'نجح' : 'فشل'} (${data.response_time_ms}ms)`
            : `Verification: ${data.verified ? 'Success' : 'Failed'} (${data.response_time_ms}ms)`
        );
      }
    } catch (error) {
      console.error('Error verifying webhook:', error);
    } finally {
      setVerifyingWebhook(null);
    }
  };

  const handleTriggerEvent = async () => {
    if (!selectedWebhook) return;

    try {
      let payload;
      try {
        payload = JSON.parse(triggerForm.payload);
      } catch (e) {
        alert(language === 'ar' ? 'تنسيق JSON غير صحيح' : 'Invalid JSON format');
        return;
      }

      const response = await fetch(`/api/webhook-endpoints/${selectedWebhook.id}/trigger_event/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          event_type: triggerForm.event_type,
          payload: payload
        }),
      });

      if (response.ok) {
        const data = await response.json();
        fetchWebhookEvents();
        setIsTriggerDialogOpen(false);
        setSelectedWebhook(null);
        resetTriggerForm();
        alert(
          language === 'ar' 
            ? `تم إرسال الحدث: ${data.event_id}`
            : `Event triggered: ${data.event_id}`
        );
      }
    } catch (error) {
      console.error('Error triggering event:', error);
    }
  };

  const resetForm = (): void => {
    setWebhookForm({
      name: '',
      description: '',
      url: '',
      method: 'POST',
      event_types: [],
      max_retries: 3,
      retry_delay: 60,
      timeout: 30
    });
  };

  const resetTriggerForm = (): void => {
    setTriggerForm({
      event_type: 'CUSTOM',
      payload: '{}'
    });
  };

  const getStatusColor = (status: string): void => {
    const colors = {
      'ACTIVE': 'bg-green-100 text-green-800',
      'INACTIVE': 'bg-gray-100 text-gray-800',
      'PAUSED': 'bg-yellow-100 text-yellow-800',
      'ERROR': 'bg-red-100 text-red-800'
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const getEventStatusColor = (status: string): void => {
    const colors = {
      'PENDING': 'bg-yellow-100 text-yellow-800',
      'DELIVERED': 'bg-green-100 text-green-800',
      'FAILED': 'bg-red-100 text-red-800',
      'RETRYING': 'bg-blue-100 text-blue-800',
      'CANCELLED': 'bg-gray-100 text-gray-800'
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const formatDate = (dateString: string | null): string => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString();
  };

  const filteredWebhooks = webhooks.filter(webhook => {
    const matchesSearch = !searchTerm || 
      webhook.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      webhook.url.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = !statusFilter || webhook.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">
          {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">
            {language === 'ar' ? 'إدارة الويب هوك' : 'Webhook Management'}
          </h1>
          <p className="text-muted-foreground">
            {language === 'ar' 
              ? 'إدارة ومراقبة نقاط نهاية الويب هوك والأحداث'
              : 'Manage and monitor webhook endpoints and events'
            }
          </p>
        </div>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              {language === 'ar' ? 'إضافة ويب هوك' : 'Add Webhook'}
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>
                {language === 'ar' ? 'إضافة ويب هوك جديد' : 'Add New Webhook'}
              </DialogTitle>
            </DialogHeader>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label>{language === 'ar' ? 'اسم الويب هوك' : 'Webhook Name'}</Label>
                <Input
                  value={webhookForm.name}
                  onChange={(e) => setWebhookForm({...webhookForm, name: e.target.value})}
                  placeholder={language === 'ar' ? 'أدخل اسم الويب هوك' : 'Enter webhook name'}
                />
              </div>
              <div>
                <Label>{language === 'ar' ? 'الطريقة' : 'Method'}</Label>
                <Select value={webhookForm.method} onValueChange={(value) => setWebhookForm({...webhookForm, method: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="POST">POST</SelectItem>
                    <SelectItem value="PUT">PUT</SelectItem>
                    <SelectItem value="PATCH">PATCH</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="md:col-span-2">
                <Label>{language === 'ar' ? 'الوصف' : 'Description'}</Label>
                <Textarea
                  value={webhookForm.description}
                  onChange={(e) => setWebhookForm({...webhookForm, description: e.target.value})}
                  placeholder={language === 'ar' ? 'أدخل وصف الويب هوك' : 'Enter webhook description'}
                  rows={3}
                />
              </div>
              <div className="md:col-span-2">
                <Label>{language === 'ar' ? 'الرابط' : 'URL'}</Label>
                <Input
                  value={webhookForm.url}
                  onChange={(e) => setWebhookForm({...webhookForm, url: e.target.value})}
                  placeholder="https://your-domain.com/webhook"
                />
              </div>
              <div>
                <Label>{language === 'ar' ? 'أقصى محاولات' : 'Max Retries'}</Label>
                <Input
                  type="number"
                  value={webhookForm.max_retries}
                  onChange={(e) => setWebhookForm({...webhookForm, max_retries: parseInt(e.target.value) || 3})}
                />
              </div>
              <div>
                <Label>{language === 'ar' ? 'تأخير المحاولة (ثانية)' : 'Retry Delay (seconds)'}</Label>
                <Input
                  type="number"
                  value={webhookForm.retry_delay}
                  onChange={(e) => setWebhookForm({...webhookForm, retry_delay: parseInt(e.target.value) || 60})}
                />
              </div>
            </div>
            <div className="flex justify-end gap-2 mt-4">
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                {language === 'ar' ? 'إلغاء' : 'Cancel'}
              </Button>
              <Button onClick={handleCreateWebhook}>
                {language === 'ar' ? 'إضافة الويب هوك' : 'Add Webhook'}
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Statistics */}
      {webhookStats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'إجمالي الويب هوك' : 'Total Webhooks'}
              </CardTitle>
              <Webhook className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{webhookStats.total_webhooks}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'النشطة' : 'Active'}
              </CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{webhookStats.active_webhooks}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'المتحقق منها' : 'Verified'}
              </CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{webhookStats.verified_webhooks}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'إجمالي الاستدعاءات' : 'Total Calls'}
              </CardTitle>
              <Send className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{webhookStats.total_calls.toLocaleString()}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'معدل النجاح' : 'Success Rate'}
              </CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{webhookStats.average_success_rate.toFixed(1)}%</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-[200px]">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder={language === 'ar' ? 'البحث في الويب هوك...' : 'Search webhooks...'}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder={language === 'ar' ? 'جميع الحالات' : 'All statuses'} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">
                  {language === 'ar' ? 'جميع الحالات' : 'All statuses'}
                </SelectItem>
                <SelectItem value="ACTIVE">
                  {language === 'ar' ? 'نشط' : 'Active'}
                </SelectItem>
                <SelectItem value="INACTIVE">
                  {language === 'ar' ? 'غير نشط' : 'Inactive'}
                </SelectItem>
                <SelectItem value="PAUSED">
                  {language === 'ar' ? 'متوقف' : 'Paused'}
                </SelectItem>
                <SelectItem value="ERROR">
                  {language === 'ar' ? 'خطأ' : 'Error'}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Webhooks Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Webhook className="h-5 w-5" />
            {language === 'ar' ? 'نقاط نهاية الويب هوك' : 'Webhook Endpoints'}
            <Badge variant="secondary" className="ml-2">
              {filteredWebhooks.length}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{language === 'ar' ? 'اسم الويب هوك' : 'Webhook Name'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الرابط' : 'URL'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الحالة' : 'Status'}</TableHead>
                  <TableHead>{language === 'ar' ? 'التحقق' : 'Verified'}</TableHead>
                  <TableHead>{language === 'ar' ? 'معدل النجاح' : 'Success Rate'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الاستدعاءات' : 'Calls'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الإجراءات' : 'Actions'}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredWebhooks.map((webhook) => (
                  <TableRow key={webhook.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{webhook.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {webhook.event_types.join(', ')}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm font-mono">
                        {webhook.url}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(webhook.status)}>
                        {language === 'ar' 
                          ? webhook.status === 'ACTIVE' ? 'نشط' 
                            : webhook.status === 'INACTIVE' ? 'غير نشط'
                            : webhook.status === 'PAUSED' ? 'متوقف'
                            : webhook.status === 'ERROR' ? 'خطأ'
                            : webhook.status
                          : webhook.status
                        }
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {webhook.is_verified ? (
                          <CheckCircle className="h-4 w-4 text-green-500" />
                        ) : (
                          <XCircle className="h-4 w-4 text-red-500" />
                        )}
                        <span className="text-sm">
                          {webhook.is_verified 
                            ? (language === 'ar' ? 'متحقق' : 'Verified')
                            : (language === 'ar' ? 'غير متحقق' : 'Not verified')
                          }
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <span className={webhook.success_rate >= 95 ? 'text-green-600' : webhook.success_rate >= 80 ? 'text-yellow-600' : 'text-red-600'}>
                          {webhook.success_rate.toFixed(1)}%
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="text-sm font-medium">{webhook.total_calls.toLocaleString()}</div>
                        <div className="text-xs text-muted-foreground">
                          {language === 'ar' ? 'آخر تشغيل:' : 'Last:'} {formatDate(webhook.last_triggered_at)}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleVerifyWebhook(webhook.id)}
                          disabled={verifyingWebhook === webhook.id}
                        >
                          {verifyingWebhook === webhook.id ? (
                            <Clock className="h-4 w-4 animate-spin" />
                          ) : (
                            <CheckCircle className="h-4 w-4" />
                          )}
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => {
                            setSelectedWebhook(webhook);
                            setIsTriggerDialogOpen(true);
                          }}
                        >
                          <Play className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <Settings className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Recent Events */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            {language === 'ar' ? 'الأحداث الأخيرة' : 'Recent Events'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{language === 'ar' ? 'معرف الحدث' : 'Event ID'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الويب هوك' : 'Webhook'}</TableHead>
                  <TableHead>{language === 'ar' ? 'نوع الحدث' : 'Event Type'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الحالة' : 'Status'}</TableHead>
                  <TableHead>{language === 'ar' ? 'المحاولات' : 'Attempts'}</TableHead>
                  <TableHead>{language === 'ar' ? 'التاريخ' : 'Date'}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {webhookEvents.map((event) => (
                  <TableRow key={event.id}>
                    <TableCell>
                      <code className="text-sm bg-muted px-2 py-1 rounded">
                        {event.event_id}
                      </code>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">{event.webhook_name}</div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{event.event_type}</Badge>
                    </TableCell>
                    <TableCell>
                      <Badge className={getEventStatusColor(event.status)}>
                        {language === 'ar' 
                          ? event.status === 'DELIVERED' ? 'تم التسليم' 
                            : event.status === 'FAILED' ? 'فشل'
                            : event.status === 'PENDING' ? 'في الانتظار'
                            : event.status === 'RETRYING' ? 'إعادة المحاولة'
                            : event.status
                          : event.status
                        }
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <span className="text-sm">
                        {event.attempts}/{event.max_attempts}
                      </span>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {new Date(event.created_at).toLocaleString()}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Trigger Event Dialog */}
      <Dialog open={isTriggerDialogOpen} onOpenChange={setIsTriggerDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {language === 'ar' ? 'تشغيل حدث' : 'Trigger Event'}
            </DialogTitle>
          </DialogHeader>
          {selectedWebhook && (
            <div className="space-y-4">
              <div className="p-4 bg-muted rounded-lg">
                <div className="font-medium">{selectedWebhook.name}</div>
                <div className="text-sm text-muted-foreground">{selectedWebhook.url}</div>
              </div>
              
              <div>
                <Label>{language === 'ar' ? 'نوع الحدث' : 'Event Type'}</Label>
                <Select value={triggerForm.event_type} onValueChange={(value) => setTriggerForm({...triggerForm, event_type: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {eventTypeOptions.map((eventType) => (
                      <SelectItem key={eventType} value={eventType}>
                        {eventType}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label>{language === 'ar' ? 'البيانات (JSON)' : 'Payload (JSON)'}</Label>
                <Textarea
                  value={triggerForm.payload}
                  onChange={(e) => setTriggerForm({...triggerForm, payload: e.target.value})}
                  placeholder='{"key": "value"}'
                  rows={6}
                  className="font-mono"
                />
              </div>
              
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setIsTriggerDialogOpen(false)}>
                  {language === 'ar' ? 'إلغاء' : 'Cancel'}
                </Button>
                <Button onClick={handleTriggerEvent}>
                  <Send className="h-4 w-4 mr-2" />
                  {language === 'ar' ? 'إرسال الحدث' : 'Send Event'}
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default WebhookManagement;
