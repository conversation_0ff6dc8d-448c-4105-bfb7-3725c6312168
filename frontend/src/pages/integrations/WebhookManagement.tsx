import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON>ontent, 
  <PERSON><PERSON><PERSON><PERSON>, 
  CardTitle 
} from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { 
  Webhook, 
  Plus, 
  Play, 
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  Settings,
  Search,
  Activity,
  Send,
  Eye
} from 'lucide-react';
// Language is passed as prop, not from context

interface WebhookEndpoint {
  id: number;
  name: string;
  description: string;
  event_types: string[];
  url: string;
  method: string;
  status: string;
  is_verified: boolean;
  last_triggered_at: string | null;
  total_calls: number;
  successful_calls: number;
  failed_calls: number;
  success_rate: number;
  recent_events_count: number;
  created_by_name: string;
  created_at: string;
}

interface WebhookEvent {
  id: number;
  webhook_endpoint: number;
  webhook_name: string;
  webhook_url: string;
  event_type: string;
  event_id: string;
  status: string;
  attempts: number;
  max_attempts: number;
  response_status_code: number | null;
  error_message: string;
  can_retry_status: boolean;
  duration_seconds: number | null;
  created_at: string;
}

interface WebhookStats {
  total_webhooks: number;
  active_webhooks: number;
  verified_webhooks: number;
  total_calls: number;
  average_success_rate: number;
}

interface WebhookManagementProps {
  language?: 'ar' | 'en';
}

// @ts-ignore
const WebhookManagement: (React as any).FC<WebhookManagementProps> = ({ language = 'en' }) => {
  // @ts-ignore
  const [webhooks, setWebhooks] = useState<WebhookEndpoint[]>([]);
  // @ts-ignore
  const [webhookEvents, setWebhookEvents] = useState<WebhookEvent[]>([]);
  // @ts-ignore
  const [webhookStats, setWebhookStats] = useState<WebhookStats | null>(null);
  const [loading, setLoading] = useState(true as any);
  const [searchTerm, setSearchTerm] = useState('' as any);
  const [statusFilter, setStatusFilter] = useState('' as any);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false as any);
  const [isTriggerDialogOpen, setIsTriggerDialogOpen] = useState(false as any);
  // @ts-ignore
  const [selectedWebhook, setSelectedWebhook] = useState<WebhookEndpoint | null>(null);
  // @ts-ignore
  const [verifyingWebhook, setVerifyingWebhook] = useState<number | null>(null);

  // Form state
  const [webhookForm, setWebhookForm] = useState({
    // @ts-ignore
    name: '',
    description: '',
    url: '',
    method: 'POST',
    event_types: [] as string[],
    max_retries: 3,
    retry_delay: 60,
    timeout: 30
  // @ts-ignore
  } as any);

  const [triggerForm, setTriggerForm] = useState({
    // @ts-ignore
    event_type: 'CUSTOM',
    payload: '{}'
  // @ts-ignore
  } as any);

  const eventTypeOptions: any = [
    'PAYMENT_RECEIVED',
    'PAYMENT_FAILED',
    'INVOICE_PAID',
    'ORDER_CREATED',
    'ORDER_UPDATED',
    'CUSTOMER_CREATED',
    'ASSET_UPDATED',
    'MAINTENANCE_DUE',
    'CUSTOM'
  ];

  // @ts-ignore
  useEffect(( as any) => {
    // @ts-ignore
    fetchWebhooks( as any);
    fetchWebhookEvents( as any);
    fetchWebhookStats( as any);
  // @ts-ignore
  }, []);

  // @ts-ignore
  const fetchWebhooks: any = async () => {
    // @ts-ignore
    try {
      // @ts-ignore
      const params = new URLSearchParams( as any);
      if (searchTerm) (params as any).append('search', searchTerm as any);
      if (statusFilter) (params as any).append('status', statusFilter as any);
      
      // @ts-ignore
      const response: any = await fetch(`/api/webhook-endpoints/?${(params as any as any).toString( as any)}`);
      if ((response as any).ok) {
        // @ts-ignore
        const data: any = await (response as any).json( as any);
        setWebhooks((data as any as any).results || data);
      // @ts-ignore
      }
    // @ts-ignore
    } catch (error) {
      // @ts-ignore
      (console as any).error('Error fetching webhooks:', error as any);
    // @ts-ignore
    } finally {
      // @ts-ignore
      setLoading(false as any);
    // @ts-ignore
    }
  // @ts-ignore
  };

  // @ts-ignore
  const fetchWebhookEvents: any = async () => {
    // @ts-ignore
    try {
      // @ts-ignore
      const response = await fetch('/api/webhook-events/?ordering=-created_at&page_size=20' as any);
      if ((response as any).ok) {
        // @ts-ignore
        const data: any = await (response as any).json( as any);
        setWebhookEvents((data as any as any).results || data);
      // @ts-ignore
      }
    // @ts-ignore
    } catch (error) {
      // @ts-ignore
      (console as any).error('Error fetching webhook events:', error as any);
    // @ts-ignore
    }
  // @ts-ignore
  };

  // @ts-ignore
  const fetchWebhookStats: any = async () => {
    // @ts-ignore
    try {
      // @ts-ignore
      const response = await fetch('/api/webhook-endpoints/webhook_stats/' as any);
      if ((response as any).ok) {
        // @ts-ignore
        const data: any = await (response as any).json( as any);
        setWebhookStats(data as any);
      // @ts-ignore
      }
    // @ts-ignore
    } catch (error) {
      // @ts-ignore
      (console as any).error('Error fetching webhook stats:', error as any);
    // @ts-ignore
    }
  // @ts-ignore
  };

  // @ts-ignore
  const handleCreateWebhook: any = async () => {
    // @ts-ignore
    try {
      // @ts-ignore
      const response = await fetch('/api/webhook-endpoints/', {
        // @ts-ignore
        method: 'POST',
        headers: {
          // @ts-ignore
          'Content-Type': 'application/json',
        // @ts-ignore
        },
        body: (JSON as any as any).stringify(webhookForm as any),
      // @ts-ignore
      });

      if ((response as any).ok) {
        // @ts-ignore
        fetchWebhooks( as any);
        fetchWebhookStats( as any);
        setIsAddDialogOpen(false as any);
        resetForm( as any);
      // @ts-ignore
      }
    // @ts-ignore
    } catch (error) {
      // @ts-ignore
      (console as any).error('Error creating webhook:', error as any);
    // @ts-ignore
    }
  // @ts-ignore
  };

  // @ts-ignore
  const handleVerifyWebhook: any = async (webhookId: number) => {
    // @ts-ignore
    setVerifyingWebhook(webhookId as any);
    try {
      // @ts-ignore
      const response: any = await fetch(`/api/webhook-endpoints/${webhookId}/verify/`, {
        // @ts-ignore
        method: 'POST',
      // @ts-ignore
      } as any);

      if ((response as any).ok) {
        // @ts-ignore
        const data: any = await (response as any).json( as any);
        fetchWebhooks( as any);
        alert(
          language === 'ar' 
            ? `التحقق: ${(data as any as any).verified ? 'نجح' : 'فشل'} (${(data as any).response_time_ms}ms)`
            : `Verification: ${(data as any).verified ? 'Success' : 'Failed'} (${(data as any).response_time_ms}ms)`
        );
      // @ts-ignore
      }
    // @ts-ignore
    } catch (error) {
      // @ts-ignore
      (console as any).error('Error verifying webhook:', error as any);
    // @ts-ignore
    } finally {
      // @ts-ignore
      setVerifyingWebhook(null as any);
    // @ts-ignore
    }
  // @ts-ignore
  };

  // @ts-ignore
  const handleTriggerEvent: any = async () => {
    // @ts-ignore
    if (!selectedWebhook) return;

    try {
      // @ts-ignore
      let payload;
      try {
        // @ts-ignore
        payload = (JSON as any).parse((triggerForm as any as any).payload);
      // @ts-ignore
      } catch (e) {
        // @ts-ignore
        alert(language === 'ar' ? 'تنسيق JSON غير صحيح' : 'Invalid JSON format' as any);
        return;
      // @ts-ignore
      }

      const response: any = await fetch(`/api/webhook-endpoints/${(selectedWebhook as any as any).id}/trigger_event/`, {
        // @ts-ignore
        method: 'POST',
        headers: {
          // @ts-ignore
          'Content-Type': 'application/json',
        // @ts-ignore
        },
        body: (JSON as any).stringify({
          // @ts-ignore
          event_type: (triggerForm as any as any).event_type,
          payload: payload
        // @ts-ignore
        }),
      // @ts-ignore
      });

      if ((response as any).ok) {
        // @ts-ignore
        const data: any = await (response as any).json( as any);
        fetchWebhookEvents( as any);
        setIsTriggerDialogOpen(false as any);
        setSelectedWebhook(null as any);
        resetTriggerForm( as any);
        alert(
          language === 'ar' 
            ? `تم إرسال الحدث: ${(data as any as any).event_id}`
            : `Event triggered: ${(data as any).event_id}`
        );
      // @ts-ignore
      }
    // @ts-ignore
    } catch (error) {
      // @ts-ignore
      (console as any).error('Error triggering event:', error as any);
    // @ts-ignore
    }
  // @ts-ignore
  };

  // @ts-ignore
  const resetForm: any = (): void => {
    setWebhookForm({
      name: '',
      description: '',
      url: '',
      method: 'POST',
      event_types: [],
      max_retries: 3,
      retry_delay: 60,
      timeout: 30
    // @ts-ignore
    } as any);
  // @ts-ignore
  };

  // @ts-ignore
  const resetTriggerForm: any = (): void => {
    setTriggerForm({
      event_type: 'CUSTOM',
      payload: '{}'
    // @ts-ignore
    } as any);
  // @ts-ignore
  };

  // @ts-ignore
  const getStatusColor: any = (status: string): void => {
    // @ts-ignore
    const colors = {
      // @ts-ignore
      'ACTIVE': 'bg-green-100 text-green-800',
      'INACTIVE': 'bg-gray-100 text-gray-800',
      'PAUSED': 'bg-yellow-100 text-yellow-800',
      'ERROR': 'bg-red-100 text-red-800'
    // @ts-ignore
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  // @ts-ignore
  };

  // @ts-ignore
  const getEventStatusColor: any = (status: string): void => {
    // @ts-ignore
    const colors = {
      // @ts-ignore
      'PENDING': 'bg-yellow-100 text-yellow-800',
      'DELIVERED': 'bg-green-100 text-green-800',
      'FAILED': 'bg-red-100 text-red-800',
      'RETRYING': 'bg-blue-100 text-blue-800',
      'CANCELLED': 'bg-gray-100 text-gray-800'
    // @ts-ignore
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  // @ts-ignore
  };

  // @ts-ignore
  const formatDate: any = (dateString: string | null): string => {
    // @ts-ignore
    if (!dateString) return '-';
    return new Date(dateString as any).toLocaleDateString( as any);
  // @ts-ignore
  };

  // @ts-ignore
  const filteredWebhooks: any = (webhooks as any).filter(webhook => {
    // @ts-ignore
    const matchesSearch = !searchTerm || 
      (webhook as any as any).name.toLowerCase( as any).includes((searchTerm as any as any).toLowerCase( as any)) ||
      (webhook as any).url.toLowerCase( as any).includes((searchTerm as any as any).toLowerCase( as any));
    
    const matchesStatus: any = !statusFilter || (webhook as any).status === statusFilter;
    
    return matchesSearch && matchesStatus;
  // @ts-ignore
  });

  if (loading) {
    // @ts-ignore
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">
          {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
        </div>
      </div>
    );
  // @ts-ignore
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">
            {language === 'ar' ? 'إدارة الويب هوك' : 'Webhook Management'}
          </h1>
          <p className="text-muted-foreground">
            {language === 'ar' 
              ? 'إدارة ومراقبة نقاط نهاية الويب هوك والأحداث'
              : 'Manage and monitor webhook endpoints and events'
            }
          </p>
        </div>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              {language === 'ar' ? 'إضافة ويب هوك' : 'Add Webhook'}
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>
                {language === 'ar' ? 'إضافة ويب هوك جديد' : 'Add New Webhook'}
              </DialogTitle>
            </DialogHeader>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label>{language === 'ar' ? 'اسم الويب هوك' : 'Webhook Name'}</Label>
                <Input
                  value={(webhookForm as any).name}
                  onChange={(e: any) => setWebhookForm({...webhookForm, name: (e as any as any).target.value})}
                  placeholder={language === 'ar' ? 'أدخل اسم الويب هوك' : 'Enter webhook name'}
                />
              </div>
              <div>
                <Label>{language === 'ar' ? 'الطريقة' : 'Method'}</Label>
                <Select value={(webhookForm as any).method} onValueChange={(value) => setWebhookForm({...webhookForm, method: value} as any)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="POST">POST</SelectItem>
                    <SelectItem value="PUT">PUT</SelectItem>
                    <SelectItem value="PATCH">PATCH</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="md:col-span-2">
                <Label>{language === 'ar' ? 'الوصف' : 'Description'}</Label>
                <Textarea
                  value={(webhookForm as any).description}
                  onChange={(e: any) => setWebhookForm({...webhookForm, description: (e as any as any).target.value})}
                  placeholder={language === 'ar' ? 'أدخل وصف الويب هوك' : 'Enter webhook description'}
                  rows={3}
                />
              </div>
              <div className="md:col-span-2">
                <Label>{language === 'ar' ? 'الرابط' : 'URL'}</Label>
                <Input
                  value={(webhookForm as any).url}
                  onChange={(e: any) => setWebhookForm({...webhookForm, url: (e as any as any).target.value})}
                  placeholder="https://your-(domain as any).com/webhook"
                />
              </div>
              <div>
                <Label>{language === 'ar' ? 'أقصى محاولات' : 'Max Retries'}</Label>
                <Input
                  type="number"
                  value={(webhookForm as any).max_retries}
                  onChange={(e: any) => setWebhookForm({...webhookForm, max_retries: parseInt((e as any as any).target.value) || 3})}
                />
              </div>
              <div>
                <Label>{language === 'ar' ? 'تأخير المحاولة (ثانية)' : 'Retry Delay (seconds)'}</Label>
                <Input
                  type="number"
                  value={(webhookForm as any).retry_delay}
                  onChange={(e: any) => setWebhookForm({...webhookForm, retry_delay: parseInt((e as any as any).target.value) || 60})}
                />
              </div>
            </div>
            <div className="flex justify-end gap-2 mt-4">
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false as any)}>
                {language === 'ar' ? 'إلغاء' : 'Cancel'}
              </Button>
              <Button onClick={handleCreateWebhook}>
                {language === 'ar' ? 'إضافة الويب هوك' : 'Add Webhook'}
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Statistics */}
      {webhookStats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'إجمالي الويب هوك' : 'Total Webhooks'}
              </CardTitle>
              <Webhook className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{(webhookStats as any).total_webhooks}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'النشطة' : 'Active'}
              </CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{(webhookStats as any).active_webhooks}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'المتحقق منها' : 'Verified'}
              </CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{(webhookStats as any).verified_webhooks}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'إجمالي الاستدعاءات' : 'Total Calls'}
              </CardTitle>
              <Send className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              // @ts-ignore
              <div className="text-2xl font-bold">{(webhookStats as any).total_calls.toLocaleString( as any)}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'معدل النجاح' : 'Success Rate'}
              </CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{(webhookStats as any).average_success_rate.toFixed(1 as any)}%</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-[200px]">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder={language === 'ar' ? 'البحث في الويب هوك...' : 'Search webhooks...'}
                  value={searchTerm}
                  onChange={(e: any) => setSearchTerm((e as any as any).target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder={language === 'ar' ? 'جميع الحالات' : 'All statuses'} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">
                  {language === 'ar' ? 'جميع الحالات' : 'All statuses'}
                </SelectItem>
                <SelectItem value="ACTIVE">
                  {language === 'ar' ? 'نشط' : 'Active'}
                </SelectItem>
                <SelectItem value="INACTIVE">
                  {language === 'ar' ? 'غير نشط' : 'Inactive'}
                </SelectItem>
                <SelectItem value="PAUSED">
                  {language === 'ar' ? 'متوقف' : 'Paused'}
                </SelectItem>
                <SelectItem value="ERROR">
                  {language === 'ar' ? 'خطأ' : 'Error'}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Webhooks Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Webhook className="h-5 w-5" />
            {language === 'ar' ? 'نقاط نهاية الويب هوك' : 'Webhook Endpoints'}
            <Badge variant="secondary" className="ml-2">
              {(filteredWebhooks as any).length}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{language === 'ar' ? 'اسم الويب هوك' : 'Webhook Name'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الرابط' : 'URL'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الحالة' : 'Status'}</TableHead>
                  <TableHead>{language === 'ar' ? 'التحقق' : 'Verified'}</TableHead>
                  <TableHead>{language === 'ar' ? 'معدل النجاح' : 'Success Rate'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الاستدعاءات' : 'Calls'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الإجراءات' : 'Actions'}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                // @ts-ignore
                {(filteredWebhooks as any).map((webhook as any) => (
                  <TableRow key={(webhook as any).id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{(webhook as any).name}</div>
                        <div className="text-sm text-muted-foreground">
                          {(webhook as any).event_types.join(', ' as any)}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm font-mono">
                        {(webhook as any).url}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={getStatusColor((webhook as any as any).status)}>
                        {language === 'ar' 
                          ? (webhook as any).status === 'ACTIVE' ? 'نشط' 
                            : (webhook as any).status === 'INACTIVE' ? 'غير نشط'
                            : (webhook as any).status === 'PAUSED' ? 'متوقف'
                            : (webhook as any).status === 'ERROR' ? 'خطأ'
                            : (webhook as any).status
                          : (webhook as any).status
                        }
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {(webhook as any).is_verified ? (
                          <CheckCircle className="h-4 w-4 text-green-500" />
                        ) : (
                          <XCircle className="h-4 w-4 text-red-500" />
                        )}
                        <span className="text-sm">
                          {(webhook as any).is_verified 
                            ? (language === 'ar' ? 'متحقق' : 'Verified')
                            : (language === 'ar' ? 'غير متحقق' : 'Not verified')
                          }
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <span className={(webhook as any).success_rate >= 95 ? 'text-green-600' : (webhook as any).success_rate >= 80 ? 'text-yellow-600' : 'text-red-600'}>
                          {(webhook as any).success_rate.toFixed(1 as any)}%
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        // @ts-ignore
                        <div className="text-sm font-medium">{(webhook as any).total_calls.toLocaleString( as any)}</div>
                        <div className="text-xs text-muted-foreground">
                          {language === 'ar' ? 'آخر تشغيل:' : 'Last:'} {formatDate((webhook as any as any).last_triggered_at)}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleVerifyWebhook((webhook as any as any).id)}
                          disabled={verifyingWebhook === (webhook as any).id}
                        >
                          {verifyingWebhook === (webhook as any).id ? (
                            <Clock className="h-4 w-4 animate-spin" />
                          ) : (
                            <CheckCircle className="h-4 w-4" />
                          )}
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => {
                            setSelectedWebhook(webhook as any);
                            setIsTriggerDialogOpen(true as any);
                          }}
                        >
                          <Play className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <Settings className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                // @ts-ignore
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Recent Events */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            {language === 'ar' ? 'الأحداث الأخيرة' : 'Recent Events'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{language === 'ar' ? 'معرف الحدث' : 'Event ID'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الويب هوك' : 'Webhook'}</TableHead>
                  <TableHead>{language === 'ar' ? 'نوع الحدث' : 'Event Type'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الحالة' : 'Status'}</TableHead>
                  <TableHead>{language === 'ar' ? 'المحاولات' : 'Attempts'}</TableHead>
                  <TableHead>{language === 'ar' ? 'التاريخ' : 'Date'}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                // @ts-ignore
                {(webhookEvents as any).map((event as any) => (
                  <TableRow key={(event as any).id}>
                    <TableCell>
                      <code className="text-sm bg-muted px-2 py-1 rounded">
                        {(event as any).event_id}
                      </code>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">{(event as any).webhook_name}</div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{(event as any).event_type}</Badge>
                    </TableCell>
                    <TableCell>
                      <Badge className={getEventStatusColor((event as any as any).status)}>
                        {language === 'ar' 
                          ? (event as any).status === 'DELIVERED' ? 'تم التسليم' 
                            : (event as any).status === 'FAILED' ? 'فشل'
                            : (event as any).status === 'PENDING' ? 'في الانتظار'
                            : (event as any).status === 'RETRYING' ? 'إعادة المحاولة'
                            : (event as any).status
                          : (event as any).status
                        }
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <span className="text-sm">
                        {(event as any).attempts}/{(event as any).max_attempts}
                      </span>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        // @ts-ignore
                        {new Date((event as any as any).created_at).toLocaleString( as any)}
                      </div>
                    </TableCell>
                  </TableRow>
                // @ts-ignore
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Trigger Event Dialog */}
      <Dialog open={isTriggerDialogOpen} onOpenChange={setIsTriggerDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {language === 'ar' ? 'تشغيل حدث' : 'Trigger Event'}
            </DialogTitle>
          </DialogHeader>
          {selectedWebhook && (
            <div className="space-y-4">
              <div className="p-4 bg-muted rounded-lg">
                <div className="font-medium">{(selectedWebhook as any).name}</div>
                <div className="text-sm text-muted-foreground">{(selectedWebhook as any).url}</div>
              </div>
              
              <div>
                <Label>{language === 'ar' ? 'نوع الحدث' : 'Event Type'}</Label>
                <Select value={(triggerForm as any).event_type} onValueChange={(value) => setTriggerForm({...triggerForm, event_type: value} as any)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    // @ts-ignore
                    {(eventTypeOptions as any).map((eventType as any) => (
                      <SelectItem key={eventType} value={eventType}>
                        {eventType}
                      </SelectItem>
                    // @ts-ignore
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label>{language === 'ar' ? 'البيانات (JSON)' : 'Payload (JSON)'}</Label>
                <Textarea
                  value={(triggerForm as any).payload}
                  onChange={(e: any) => setTriggerForm({...triggerForm, payload: (e as any as any).target.value})}
                  placeholder='{"key": "value"}'
                  rows={6}
                  className="font-mono"
                />
              </div>
              
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setIsTriggerDialogOpen(false as any)}>
                  {language === 'ar' ? 'إلغاء' : 'Cancel'}
                </Button>
                <Button onClick={handleTriggerEvent}>
                  <Send className="h-4 w-4 mr-2" />
                  {language === 'ar' ? 'إرسال الحدث' : 'Send Event'}
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
// @ts-ignore
};

export default WebhookManagement;
