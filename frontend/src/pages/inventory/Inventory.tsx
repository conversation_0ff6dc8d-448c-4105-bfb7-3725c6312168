import { useState, useEffect } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Package,
  Search,
  Filter,
  Plus,
  Eye,
  Edit,
  MoreHorizontal,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  BarChart3,
  Download,
  Upload,
  Scan,
  MapPin,
  Calendar,
  DollarSign
} from 'lucide-react'

interface InventoryProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    title: 'إدارة المخزون',
    subtitle: 'إدارة المخزون والمنتجات والمواد',
    inventoryOverview: 'نظرة عامة على المخزون',
    totalItems: 'إجمالي الأصناف',
    lowStock: 'مخزون منخفض',
    outOfStock: 'نفد المخزون',
    totalValue: 'القيمة الإجمالية',
    searchItems: 'البحث في الأصناف...',
    addItem: 'إضافة صنف',
    exportData: 'تصدير البيانات',
    importData: 'استيراد البيانات',
    scanBarcode: 'مسح الباركود',
    itemList: 'قائمة الأصناف',
    itemName: 'اسم الصنف',
    category: 'الفئة',
    sku: 'رمز المنتج',
    quantity: 'الكمية',
    unitPrice: 'سعر الوحدة',
    location: 'الموقع',
    lastUpdated: 'آخر تحديث',
    status: 'الحالة',
    actions: 'الإجراءات',
    viewDetails: 'عرض التفاصيل',
    editItem: 'تعديل الصنف',
    inStock: 'متوفر',
    electronics: 'إلكترونيات',
    furniture: 'أثاث',
    supplies: 'مستلزمات',
    equipment: 'معدات',
    warehouse: 'المستودع',
    office: 'المكتب',
    storage: 'المخزن',
    reorderLevel: 'مستوى إعادة الطلب',
    supplier: 'المورد',
    expiryDate: 'تاريخ الانتهاء',
    batchNumber: 'رقم الدفعة',
    refresh: 'تحديث',
    stockMovement: 'حركة المخزون',
    stockIn: 'إدخال مخزون',
    stockOut: 'إخراج مخزون',
    adjustment: 'تعديل',
    transfer: 'نقل',
    damaged: 'تالف',
    returned: 'مرتجع'
  },
  en: {
    title: 'Inventory Management',
    subtitle: 'Manage inventory, products and materials',
    inventoryOverview: 'Inventory Overview',
    totalItems: 'Total Items',
    lowStock: 'Low Stock',
    outOfStock: 'Out of Stock',
    totalValue: 'Total Value',
    searchItems: 'Search items...',
    addItem: 'Add Item',
    exportData: 'Export Data',
    importData: 'Import Data',
    scanBarcode: 'Scan Barcode',
    itemList: 'Item List',
    itemName: 'Item Name',
    category: 'Category',
    sku: 'SKU',
    quantity: 'Quantity',
    unitPrice: 'Unit Price',
    location: 'Location',
    lastUpdated: 'Last Updated',
    status: 'Status',
    actions: 'Actions',
    viewDetails: 'View Details',
    editItem: 'Edit Item',
    inStock: 'In Stock',
    electronics: 'Electronics',
    furniture: 'Furniture',
    supplies: 'Supplies',
    equipment: 'Equipment',
    warehouse: 'Warehouse',
    office: 'Office',
    storage: 'Storage',
    reorderLevel: 'Reorder Level',
    supplier: 'Supplier',
    expiryDate: 'Expiry Date',
    batchNumber: 'Batch Number',
    refresh: 'Refresh',
    stockMovement: 'Stock Movement',
    stockIn: 'Stock In',
    stockOut: 'Stock Out',
    adjustment: 'Adjustment',
    transfer: 'Transfer',
    damaged: 'Damaged',
    returned: 'Returned'
  }
}

export default function Inventory({ language }: InventoryProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('')
  const [refreshing, setRefreshing] = useState(false)
  const t = translations[language]
  const isRTL = language === 'ar'

  // Inventory metrics
  const [inventoryMetrics, setInventoryMetrics] = useState({
    totalItems: 1247,
    lowStock: 23,
    outOfStock: 8,
    totalValue: 2850000
  })

  // Sample inventory data
  const [inventoryItems] = useState([
    {
      id: 1,
      name: 'لابتوب ديل XPS 13',
      nameEn: 'Dell XPS 13 Laptop',
      sku: 'DELL-XPS13-001',
      category: 'electronics',
      categoryAr: 'إلكترونيات',
      quantity: 15,
      unitPrice: 4500,
      totalValue: 67500,
      location: 'warehouse',
      locationAr: 'المستودع',
      status: 'inStock',
      reorderLevel: 5,
      supplier: 'Dell Technologies',
      lastUpdated: '2024-01-28',
      batchNumber: 'BATCH-2024-001'
    },
    {
      id: 2,
      name: 'كرسي مكتب مريح',
      nameEn: 'Ergonomic Office Chair',
      sku: 'CHAIR-ERG-001',
      category: 'furniture',
      categoryAr: 'أثاث',
      quantity: 3,
      unitPrice: 850,
      totalValue: 2550,
      location: 'office',
      locationAr: 'المكتب',
      status: 'lowStock',
      reorderLevel: 5,
      supplier: 'Office Furniture Co.',
      lastUpdated: '2024-01-25',
      batchNumber: 'BATCH-2024-002'
    },
    {
      id: 3,
      name: 'ورق طباعة A4',
      nameEn: 'A4 Printing Paper',
      sku: 'PAPER-A4-001',
      category: 'supplies',
      categoryAr: 'مستلزمات',
      quantity: 0,
      unitPrice: 25,
      totalValue: 0,
      location: 'storage',
      locationAr: 'المخزن',
      status: 'outOfStock',
      reorderLevel: 50,
      supplier: 'Paper Supply Inc.',
      lastUpdated: '2024-01-20',
      batchNumber: 'BATCH-2024-003'
    },
    {
      id: 4,
      name: 'طابعة ليزر HP',
      nameEn: 'HP Laser Printer',
      sku: 'HP-LASER-001',
      category: 'equipment',
      categoryAr: 'معدات',
      quantity: 8,
      unitPrice: 1200,
      totalValue: 9600,
      location: 'warehouse',
      locationAr: 'المستودع',
      status: 'inStock',
      reorderLevel: 3,
      supplier: 'HP Inc.',
      lastUpdated: '2024-01-26',
      batchNumber: 'BATCH-2024-004'
    }
  ])

  const handleRefresh = async () => {
    setRefreshing(true)
    await new Promise(resolve => setTimeout(resolve, 1000))
    setRefreshing(false)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'inStock':
        return 'bg-green-500/20 text-green-400 border-green-500/30'
      case 'lowStock':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
      case 'outOfStock':
        return 'bg-red-500/20 text-red-400 border-red-500/30'
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'inStock':
        return <CheckCircle className="h-4 w-4" />
      case 'lowStock':
        return <AlertTriangle className="h-4 w-4" />
      case 'outOfStock':
        return <TrendingDown className="h-4 w-4" />
      default:
        return <Package className="h-4 w-4" />
    }
  }

  const filteredItems = inventoryItems.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.nameEn.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.sku.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = !selectedCategory || item.category === selectedCategory
    const matchesStatus = !selectedStatus || item.status === selectedStatus

    return matchesSearch && matchesCategory && matchesStatus
  })

  const inventoryMetricsCards = [
    {
      title: t.totalItems,
      value: inventoryMetrics.totalItems.toLocaleString(),
      change: '+12',
      trend: 'up',
      icon: Package,
      color: 'from-blue-500 to-blue-600'
    },
    {
      title: t.lowStock,
      value: inventoryMetrics.lowStock.toString(),
      change: '+3',
      trend: 'up',
      icon: AlertTriangle,
      color: 'from-yellow-500 to-yellow-600'
    },
    {
      title: t.outOfStock,
      value: inventoryMetrics.outOfStock.toString(),
      change: '-2',
      trend: 'down',
      icon: TrendingDown,
      color: 'from-red-500 to-red-600'
    },
    {
      title: t.totalValue,
      value: new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR'
      }).format(inventoryMetrics.totalValue),
      change: '+5.2%',
      trend: 'up',
      icon: DollarSign,
      color: 'from-green-500 to-green-600'
    }
  ]

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">{t.title}</h1>
          <p className="text-white/70">{t.subtitle}</p>
        </div>

        <div className="flex items-center gap-3">
          <Button
            onClick={handleRefresh}
            disabled={refreshing}
            className="glass-button"
          >
            <BarChart3 className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            {t.refresh}
          </Button>
          <Button variant="outline" className="glass-button">
            <Scan className="h-4 w-4 mr-2" />
            {t.scanBarcode}
          </Button>
          <Button className="glass-button">
            <Plus className="h-4 w-4 mr-2" />
            {t.addItem}
          </Button>
        </div>
      </div>

      {/* Inventory Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {inventoryMetricsCards.map((card, index) => (
          <Card key={index} className="glass-card border-white/20 hover:border-white/40 transition-all">
            <CardContent className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <p className="text-white/70 text-sm mb-1">{card.title}</p>
                  <p className="text-2xl font-bold text-white">{card.value}</p>
                </div>
                <div className={`p-3 rounded-xl bg-gradient-to-r ${card.color} shadow-lg`}>
                  <card.icon className="h-6 w-6 text-white" />
                </div>
              </div>

              <div className="flex items-center gap-2">
                <span className={`text-sm font-medium ${
                  card.trend === 'up' ? 'text-green-400' :
                  card.trend === 'down' ? 'text-red-400' : 'text-blue-400'
                }`}>
                  {card.change}
                </span>
                <span className="text-xs text-white/60">vs last month</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Search and Filters */}
      <Card className="glass-card border-white/20">
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/60" />
              <Input
                placeholder={t.searchItems}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 glass-input"
              />
            </div>

            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="glass-input"
            >
              <option value="">{t.category}</option>
              <option value="electronics">{t.electronics}</option>
              <option value="furniture">{t.furniture}</option>
              <option value="supplies">{t.supplies}</option>
              <option value="equipment">{t.equipment}</option>
            </select>

            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="glass-input"
            >
              <option value="">{t.status}</option>
              <option value="inStock">{t.inStock}</option>
              <option value="lowStock">{t.lowStock}</option>
              <option value="outOfStock">{t.outOfStock}</option>
            </select>

            <Button variant="outline" className="glass-button">
              <Upload className="h-4 w-4 mr-2" />
              {t.importData}
            </Button>

            <Button variant="outline" className="glass-button">
              <Download className="h-4 w-4 mr-2" />
              {t.exportData}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Inventory List */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white text-xl">{t.itemList}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-white/20">
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.itemName}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.sku}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.category}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.quantity}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.unitPrice}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.totalValue}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.status}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.actions}</th>
                </tr>
              </thead>
              <tbody>
                {filteredItems.map((item) => (
                  <tr key={item.id} className="border-b border-white/10 hover:bg-white/5 transition-colors">
                    <td className="py-4 px-4">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-lg bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-white font-bold">
                          <Package className="h-5 w-5" />
                        </div>
                        <div>
                          <p className="text-white font-medium">
                            {language === 'ar' ? item.name : item.nameEn}
                          </p>
                          <p className="text-white/60 text-sm">{item.supplier}</p>
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-4 text-white font-mono text-sm">
                      {item.sku}
                    </td>
                    <td className="py-4 px-4 text-white">
                      {language === 'ar' ? item.categoryAr : item.category}
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex items-center gap-2">
                        <span className="text-white font-medium">{item.quantity}</span>
                        {item.quantity <= item.reorderLevel && (
                          <AlertTriangle className="h-4 w-4 text-yellow-400" />
                        )}
                      </div>
                    </td>
                    <td className="py-4 px-4 text-white">
                      {new Intl.NumberFormat('ar-SA', {
                        style: 'currency',
                        currency: 'SAR'
                      }).format(item.unitPrice)}
                    </td>
                    <td className="py-4 px-4 text-white font-medium">
                      {new Intl.NumberFormat('ar-SA', {
                        style: 'currency',
                        currency: 'SAR'
                      }).format(item.totalValue)}
                    </td>
                    <td className="py-4 px-4">
                      <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(item.status)}`}>
                        {getStatusIcon(item.status)}
                        {t[item.status as keyof typeof t]}
                      </span>
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex items-center gap-2">
                        <Button size="sm" variant="outline" className="glass-button p-2">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="outline" className="glass-button p-2">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="outline" className="glass-button p-2">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
