import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Badge } from '../../components/ui/badge'
import { 
  BookOpen, 
  Plus, 
  Search, 
  Eye, 
  Edit, 
  Trash2,
  Star,
  ThumbsUp,
  MessageCircle,
  Download,
  Share,
  Bookmark,
  Filter,
  FileText,
  Video,
  Image,
  Link
} from 'lucide-react'

interface KnowledgeBaseProps {
  language: 'ar' | 'en'
}

interface Article {
  id: string
  title: string
  titleAr: string
  content: string
  contentAr: string
  summary: string
  summaryAr: string
  category: string
  categoryAr: string
  tags: string[]
  tagsAr: string[]
  author: string
  authorAr: string
  authorId: string
  createdDate: string
  lastUpdated: string
  views: number
  likes: number
  comments: number
  rating: number
  status: 'draft' | 'published' | 'archived' | 'under-review'
  type: 'article' | 'faq' | 'procedure' | 'policy' | 'tutorial' | 'video' | 'document'
  attachments?: {
    name: string
    url: string
    type: 'pdf' | 'doc' | 'video' | 'image'
  }[]
  relatedArticles?: string[]
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  estimatedReadTime: number // in minutes
  isBookmarked?: boolean
  isFeatured?: boolean
}

const mockArticles: Article[] = [
  {
    id: '1',
    title: 'Employee Onboarding Process',
    titleAr: 'عملية إدخال الموظفين الجدد',
    content: 'Complete guide for onboarding new employees...',
    contentAr: 'دليل شامل لإدخال الموظفين الجدد...',
    summary: 'Step-by-step guide for HR team to onboard new employees effectively',
    summaryAr: 'دليل خطوة بخطوة لفريق الموارد البشرية لإدخال الموظفين الجدد بفعالية',
    category: 'HR Procedures',
    categoryAr: 'إجراءات الموارد البشرية',
    tags: ['onboarding', 'hr', 'procedures', 'new-employee'],
    tagsAr: ['إدخال', 'موارد بشرية', 'إجراءات', 'موظف جديد'],
    author: 'Sarah Hassan',
    authorAr: 'سارة حسن',
    authorId: 'EMP002',
    createdDate: '2024-01-15',
    lastUpdated: '2024-01-20',
    views: 245,
    likes: 18,
    comments: 5,
    rating: 4.5,
    status: 'published',
    type: 'procedure',
    difficulty: 'beginner',
    estimatedReadTime: 8,
    isFeatured: true,
    attachments: [
      { name: 'Onboarding Checklist.pdf', url: '/docs/onboarding-checklist.pdf', type: 'pdf' },
      { name: 'Welcome Video.mp4', url: '/videos/welcome.mp4', type: 'video' }
    ]
  },
  {
    id: '2',
    title: 'IT Security Best Practices',
    titleAr: 'أفضل ممارسات الأمان التقني',
    content: 'Essential security guidelines for all employees...',
    contentAr: 'إرشادات الأمان الأساسية لجميع الموظفين...',
    summary: 'Important security measures every employee should follow',
    summaryAr: 'إجراءات الأمان المهمة التي يجب على كل موظف اتباعها',
    category: 'IT Security',
    categoryAr: 'الأمان التقني',
    tags: ['security', 'it', 'best-practices', 'guidelines'],
    tagsAr: ['أمان', 'تقنية معلومات', 'أفضل ممارسات', 'إرشادات'],
    author: 'Ahmed Al-Rashid',
    authorAr: 'أحمد الراشد',
    authorId: 'EMP001',
    createdDate: '2024-01-10',
    lastUpdated: '2024-01-18',
    views: 189,
    likes: 25,
    comments: 8,
    rating: 4.8,
    status: 'published',
    type: 'policy',
    difficulty: 'intermediate',
    estimatedReadTime: 12,
    isFeatured: true
  },
  {
    id: '3',
    title: 'How to Submit Expense Reports',
    titleAr: 'كيفية تقديم تقارير المصروفات',
    content: 'Tutorial on submitting expense reports through the system...',
    contentAr: 'دليل تعليمي لتقديم تقارير المصروفات من خلال النظام...',
    summary: 'Quick tutorial for submitting and tracking expense reports',
    summaryAr: 'دليل سريع لتقديم ومتابعة تقارير المصروفات',
    category: 'Finance',
    categoryAr: 'المالية',
    tags: ['expenses', 'finance', 'tutorial', 'reporting'],
    tagsAr: ['مصروفات', 'مالية', 'دليل', 'تقارير'],
    author: 'Fatima Mohammed',
    authorAr: 'فاطمة محمد',
    authorId: 'EMP003',
    createdDate: '2024-01-12',
    lastUpdated: '2024-01-16',
    views: 156,
    likes: 12,
    comments: 3,
    rating: 4.2,
    status: 'published',
    type: 'tutorial',
    difficulty: 'beginner',
    estimatedReadTime: 5
  },
  {
    id: '4',
    title: 'Project Management Guidelines',
    titleAr: 'إرشادات إدارة المشاريع',
    content: 'Comprehensive guide for managing projects effectively...',
    contentAr: 'دليل شامل لإدارة المشاريع بفعالية...',
    summary: 'Best practices and methodologies for project management',
    summaryAr: 'أفضل الممارسات والمنهجيات لإدارة المشاريع',
    category: 'Project Management',
    categoryAr: 'إدارة المشاريع',
    tags: ['project-management', 'agile', 'methodology', 'best-practices'],
    tagsAr: ['إدارة مشاريع', 'أجايل', 'منهجية', 'أفضل ممارسات'],
    author: 'Omar Abdullah',
    authorAr: 'عمر عبدالله',
    authorId: 'EMP004',
    createdDate: '2024-01-08',
    lastUpdated: '2024-01-14',
    views: 298,
    likes: 32,
    comments: 12,
    rating: 4.6,
    status: 'published',
    type: 'article',
    difficulty: 'advanced',
    estimatedReadTime: 15
  }
]

export default function KnowledgeBase({ language }: KnowledgeBaseProps) {
  const [articles, setArticles] = useState<Article[]>(mockArticles)
  const [searchTerm, setSearchTerm] = useState('')
  const [categoryFilter, setCategoryFilter] = useState<string>('all')
  const [typeFilter, setTypeFilter] = useState<string>('all')
  const [difficultyFilter, setDifficultyFilter] = useState<string>('all')
  const [sortBy, setSortBy] = useState<'recent' | 'popular' | 'rating'>('recent')

  const text = {
    ar: {
      title: 'قاعدة المعرفة',
      description: 'مركز المعلومات والإرشادات والسياسات',
      newArticle: 'مقال جديد',
      search: 'البحث في قاعدة المعرفة...',
      filter: 'تصفية',
      sort: 'ترتيب',
      articleTitle: 'عنوان المقال',
      author: 'الكاتب',
      category: 'الفئة',
      type: 'النوع',
      difficulty: 'المستوى',
      views: 'المشاهدات',
      likes: 'الإعجابات',
      rating: 'التقييم',
      readTime: 'وقت القراءة',
      actions: 'الإجراءات',
      view: 'عرض',
      edit: 'تعديل',
      delete: 'حذف',
      bookmark: 'إشارة مرجعية',
      share: 'مشاركة',
      download: 'تحميل',
      totalArticles: 'إجمالي المقالات',
      publishedArticles: 'المقالات المنشورة',
      totalViews: 'إجمالي المشاهدات',
      avgRating: 'متوسط التقييم',
      recent: 'الأحدث',
      popular: 'الأكثر شعبية',
      featured: 'مميز',
      minutes: 'دقيقة',
      article: 'مقال',
      faq: 'أسئلة شائعة',
      procedure: 'إجراء',
      policy: 'سياسة',
      tutorial: 'دليل تعليمي',
      video: 'فيديو',
      document: 'وثيقة',
      beginner: 'مبتدئ',
      intermediate: 'متوسط',
      advanced: 'متقدم',
      draft: 'مسودة',
      published: 'منشور',
      archived: 'مؤرشف',
      'under-review': 'قيد المراجعة'
    },
    en: {
      title: 'Knowledge Base',
      description: 'Information center for guidelines, policies, and procedures',
      newArticle: 'New Article',
      search: 'Search knowledge base...',
      filter: 'Filter',
      sort: 'Sort',
      articleTitle: 'Article Title',
      author: 'Author',
      category: 'Category',
      type: 'Type',
      difficulty: 'Difficulty',
      views: 'Views',
      likes: 'Likes',
      rating: 'Rating',
      readTime: 'Read Time',
      actions: 'Actions',
      view: 'View',
      edit: 'Edit',
      delete: 'Delete',
      bookmark: 'Bookmark',
      share: 'Share',
      download: 'Download',
      totalArticles: 'Total Articles',
      publishedArticles: 'Published Articles',
      totalViews: 'Total Views',
      avgRating: 'Average Rating',
      recent: 'Recent',
      popular: 'Popular',
      featured: 'Featured',
      minutes: 'minutes',
      article: 'Article',
      faq: 'FAQ',
      procedure: 'Procedure',
      policy: 'Policy',
      tutorial: 'Tutorial',
      video: 'Video',
      document: 'Document',
      beginner: 'Beginner',
      intermediate: 'Intermediate',
      advanced: 'Advanced',
      draft: 'Draft',
      published: 'Published',
      archived: 'Archived',
      'under-review': 'Under Review'
    }
  }

  const t = text[language]

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'article': return 'bg-blue-500/20 text-blue-300 border-blue-500/30'
      case 'faq': return 'bg-green-500/20 text-green-300 border-green-500/30'
      case 'procedure': return 'bg-purple-500/20 text-purple-300 border-purple-500/30'
      case 'policy': return 'bg-red-500/20 text-red-300 border-red-500/30'
      case 'tutorial': return 'bg-orange-500/20 text-orange-300 border-orange-500/30'
      case 'video': return 'bg-pink-500/20 text-pink-300 border-pink-500/30'
      case 'document': return 'bg-gray-500/20 text-gray-300 border-gray-500/30'
      default: return 'bg-gray-500/20 text-gray-300 border-gray-500/30'
    }
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'bg-green-500/20 text-green-300 border-green-500/30'
      case 'intermediate': return 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30'
      case 'advanced': return 'bg-red-500/20 text-red-300 border-red-500/30'
      default: return 'bg-gray-500/20 text-gray-300 border-gray-500/30'
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'article': return <FileText className="w-4 h-4" />
      case 'video': return <Video className="w-4 h-4" />
      case 'document': return <Download className="w-4 h-4" />
      default: return <BookOpen className="w-4 h-4" />
    }
  }

  const getRatingStars = (rating: number) => {
    const stars = []
    const fullStars = Math.floor(rating)
    const hasHalfStar = rating % 1 !== 0

    for (let i = 0; i < fullStars; i++) {
      stars.push(<Star key={i} className="w-3 h-3 fill-yellow-400 text-yellow-400" />)
    }

    if (hasHalfStar) {
      stars.push(<Star key="half" className="w-3 h-3 fill-yellow-400/50 text-yellow-400" />)
    }

    const remainingStars = 5 - Math.ceil(rating)
    for (let i = 0; i < remainingStars; i++) {
      stars.push(<Star key={`empty-${i}`} className="w-3 h-3 text-gray-400" />)
    }

    return stars
  }

  const filteredArticles = articles.filter(article => {
    const matchesSearch = article.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         article.titleAr.includes(searchTerm) ||
                         article.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         article.contentAr.includes(searchTerm) ||
                         article.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase())) ||
                         article.tagsAr.some(tag => tag.includes(searchTerm))
    const matchesCategory = categoryFilter === 'all' || article.category === categoryFilter
    const matchesType = typeFilter === 'all' || article.type === typeFilter
    const matchesDifficulty = difficultyFilter === 'all' || article.difficulty === difficultyFilter
    return matchesSearch && matchesCategory && matchesType && matchesDifficulty
  })

  const sortedArticles = [...filteredArticles].sort((a, b) => {
    switch (sortBy) {
      case 'popular':
        return b.views - a.views
      case 'rating':
        return b.rating - a.rating
      case 'recent':
      default:
        return new Date(b.lastUpdated).getTime() - new Date(a.lastUpdated).getTime()
    }
  })

  const publishedArticles = articles.filter(article => article.status === 'published').length
  const totalViews = articles.reduce((sum, article) => sum + article.views, 0)
  const avgRating = articles.length > 0 
    ? articles.reduce((sum, article) => sum + article.rating, 0) / articles.length 
    : 0

  return (
    <div className="min-h-screen gradient-bg p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">{t.title}</h1>
            <p className="text-white/70">{t.description}</p>
          </div>
          <Button className="bg-gradient-to-r from-sky-500 to-blue-600 hover:from-sky-600 hover:to-blue-700">
            <Plus className="w-4 h-4 mr-2" />
            {t.newArticle}
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="glass-card border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.totalArticles}</p>
                  <p className="text-2xl font-bold text-white">{articles.length}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-sky-500/20 flex items-center justify-center">
                  <BookOpen className="w-6 h-6 text-sky-400" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-card border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.publishedArticles}</p>
                  <p className="text-2xl font-bold text-white">{publishedArticles}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-green-500/20 flex items-center justify-center">
                  <FileText className="w-6 h-6 text-green-400" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-card border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.totalViews}</p>
                  <p className="text-2xl font-bold text-white">{totalViews.toLocaleString()}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-purple-500/20 flex items-center justify-center">
                  <Eye className="w-6 h-6 text-purple-400" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-card border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.avgRating}</p>
                  <div className="flex items-center gap-2">
                    <p className="text-2xl font-bold text-white">{avgRating.toFixed(1)}</p>
                    <div className="flex">
                      {getRatingStars(avgRating)}
                    </div>
                  </div>
                </div>
                <div className="w-12 h-12 rounded-full bg-yellow-500/20 flex items-center justify-center">
                  <Star className="w-6 h-6 text-yellow-400" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 w-4 h-4" />
                  <Input
                    placeholder={t.search}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 glass-input"
                  />
                </div>
              </div>
              <div className="flex gap-4">
                <select
                  value={categoryFilter}
                  onChange={(e) => setCategoryFilter(e.target.value)}
                  className="glass-input px-3 py-2 rounded-lg"
                >
                  <option value="all">جميع الفئات</option>
                  <option value="HR Procedures">إجراءات الموارد البشرية</option>
                  <option value="IT Security">الأمان التقني</option>
                  <option value="Finance">المالية</option>
                  <option value="Project Management">إدارة المشاريع</option>
                </select>
                <select
                  value={typeFilter}
                  onChange={(e) => setTypeFilter(e.target.value)}
                  className="glass-input px-3 py-2 rounded-lg"
                >
                  <option value="all">جميع الأنواع</option>
                  <option value="article">{t.article}</option>
                  <option value="procedure">{t.procedure}</option>
                  <option value="policy">{t.policy}</option>
                  <option value="tutorial">{t.tutorial}</option>
                  <option value="faq">{t.faq}</option>
                </select>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value as 'recent' | 'popular' | 'rating')}
                  className="glass-input px-3 py-2 rounded-lg"
                >
                  <option value="recent">{t.recent}</option>
                  <option value="popular">{t.popular}</option>
                  <option value="rating">{t.rating}</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Articles Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {sortedArticles.map((article) => (
            <Card key={article.id} className="glass-card border-white/20 hover:border-white/30 transition-colors">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-2">
                    {getTypeIcon(article.type)}
                    <Badge className={`${getTypeColor(article.type)} border text-xs`}>
                      {t[article.type as keyof typeof t]}
                    </Badge>
                    {article.isFeatured && (
                      <Badge className="bg-yellow-500/20 text-yellow-300 border-yellow-500/30 text-xs">
                        {t.featured}
                      </Badge>
                    )}
                  </div>
                  <div className="flex gap-1">
                    <Button size="sm" variant="ghost" className="h-6 w-6 p-0 text-white/70">
                      <Bookmark className="w-3 h-3" />
                    </Button>
                    <Button size="sm" variant="ghost" className="h-6 w-6 p-0 text-white/70">
                      <Share className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
                <CardTitle className="text-white text-lg leading-tight">
                  {language === 'ar' ? article.titleAr : article.title}
                </CardTitle>
                <CardDescription className="text-white/70 text-sm">
                  {language === 'ar' ? article.summaryAr : article.summary}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between text-sm text-white/70">
                  <span>{language === 'ar' ? article.authorAr : article.author}</span>
                  <span>{article.estimatedReadTime} {t.minutes}</span>
                </div>

                <div className="flex items-center gap-4 text-sm text-white/70">
                  <div className="flex items-center gap-1">
                    <Eye className="w-4 h-4" />
                    <span>{article.views}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <ThumbsUp className="w-4 h-4" />
                    <span>{article.likes}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <MessageCircle className="w-4 h-4" />
                    <span>{article.comments}</span>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="flex">
                      {getRatingStars(article.rating)}
                    </div>
                    <span className="text-white text-sm">{article.rating}</span>
                  </div>
                  <Badge className={`${getDifficultyColor(article.difficulty)} border text-xs`}>
                    {t[article.difficulty as keyof typeof t]}
                  </Badge>
                </div>

                <div className="flex gap-2 pt-2">
                  <Button size="sm" className="flex-1 bg-sky-600 hover:bg-sky-700">
                    <Eye className="w-4 h-4 mr-2" />
                    {t.view}
                  </Button>
                  <Button size="sm" variant="outline" className="glass-button">
                    <Edit className="w-4 h-4" />
                  </Button>
                  {article.attachments && article.attachments.length > 0 && (
                    <Button size="sm" variant="outline" className="glass-button">
                      <Download className="w-4 h-4" />
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}
