import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Badge } from '../../components/ui/badge'
import {
  DollarSign,
  Plus,
  Search,
  Eye,
  Edit,
  Download,
  Calculator,
  Calendar,
  Users,
  TrendingUp,
  FileText,
  CheckCircle,
  Clock,
  AlertTriangle
} from 'lucide-react'

interface PayrollManagementProps {
  language: 'ar' | 'en'
}

interface PayrollRecord {
  id: string
  employee: string
  employeeAr: string
  employeeId: string
  department: string
  departmentAr: string
  position: string
  positionAr: string
  payPeriod: string
  baseSalary: number
  overtime: number
  bonuses: number
  allowances: number
  deductions: number
  taxes: number
  netSalary: number
  status: 'draft' | 'processed' | 'paid' | 'cancelled'
  payDate: string
  processedBy: string
  processedByAr: string
  workingDays: number
  totalDays: number
  overtimeHours: number
  notes?: string
  notesAr?: string
}

const mockPayrollRecords: PayrollRecord[] = [
  {
    id: '1',
    employee: '<PERSON>',
    employeeAr: 'أحمد الراشد',
    employeeId: 'EMP001',
    department: 'Engineering',
    departmentAr: 'الهندسة',
    position: 'Senior Developer',
    positionAr: 'مطور أول',
    payPeriod: '2024-01',
    baseSalary: 15000,
    overtime: 1200,
    bonuses: 2000,
    allowances: 800,
    deductions: 500,
    taxes: 1800,
    netSalary: 16700,
    status: 'paid',
    payDate: '2024-01-31',
    processedBy: 'HR Manager',
    processedByAr: 'مدير الموارد البشرية',
    workingDays: 22,
    totalDays: 22,
    overtimeHours: 15
  },
  {
    id: '2',
    employee: 'Fatima Mohammed',
    employeeAr: 'فاطمة محمد',
    employeeId: 'EMP002',
    department: 'Marketing',
    departmentAr: 'التسويق',
    position: 'Marketing Manager',
    positionAr: 'مدير التسويق',
    payPeriod: '2024-01',
    baseSalary: 12000,
    overtime: 0,
    bonuses: 1500,
    allowances: 600,
    deductions: 300,
    taxes: 1350,
    netSalary: 12450,
    status: 'processed',
    payDate: '2024-01-31',
    processedBy: 'HR Manager',
    processedByAr: 'مدير الموارد البشرية',
    workingDays: 22,
    totalDays: 22,
    overtimeHours: 0
  },
  {
    id: '3',
    employee: 'Mohammed Ali',
    employeeAr: 'محمد علي',
    employeeId: 'EMP003',
    department: 'Finance',
    departmentAr: 'المالية',
    position: 'Financial Analyst',
    positionAr: 'محلل مالي',
    payPeriod: '2024-01',
    baseSalary: 10000,
    overtime: 800,
    bonuses: 1000,
    allowances: 500,
    deductions: 200,
    taxes: 1110,
    netSalary: 10990,
    status: 'draft',
    payDate: '2024-01-31',
    processedBy: 'HR Manager',
    processedByAr: 'مدير الموارد البشرية',
    workingDays: 20,
    totalDays: 22,
    overtimeHours: 10
  }
]

export default function PayrollManagement({ language }: PayrollManagementProps) {
  const [payrollRecords, setPayrollRecords] = useState<PayrollRecord[]>(mockPayrollRecords)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [departmentFilter, setDepartmentFilter] = useState<string>('all')
  const [selectedPeriod, setSelectedPeriod] = useState('2024-01')

  const text = {
    ar: {
      title: 'إدارة الرواتب',
      description: 'إدارة ومعالجة رواتب الموظفين',
      newPayroll: 'راتب جديد',
      processPayroll: 'معالجة الرواتب',
      search: 'البحث في الرواتب...',
      employee: 'الموظف',
      department: 'القسم',
      position: 'المنصب',
      payPeriod: 'فترة الراتب',
      baseSalary: 'الراتب الأساسي',
      overtime: 'العمل الإضافي',
      bonuses: 'المكافآت',
      allowances: 'البدلات',
      deductions: 'الخصومات',
      taxes: 'الضرائب',
      netSalary: 'صافي الراتب',
      status: 'الحالة',
      payDate: 'تاريخ الدفع',
      workingDays: 'أيام العمل',
      overtimeHours: 'ساعات إضافية',
      actions: 'الإجراءات',
      view: 'عرض',
      edit: 'تعديل',
      download: 'تحميل',
      paySlip: 'قسيمة راتب',
      totalEmployees: 'إجمالي الموظفين',
      totalPayroll: 'إجمالي الرواتب',
      avgSalary: 'متوسط الراتب',
      pendingPayments: 'المدفوعات المعلقة',
      draft: 'مسودة',
      processed: 'معالج',
      paid: 'مدفوع',
      cancelled: 'ملغي',
      sar: 'ر.س',
      hours: 'ساعة',
      days: 'يوم',
      generateReports: 'إنشاء التقارير',
      bulkProcess: 'معالجة جماعية'
    },
    en: {
      title: 'Payroll Management',
      description: 'Manage and process employee payroll',
      newPayroll: 'New Payroll',
      processPayroll: 'Process Payroll',
      search: 'Search payroll...',
      employee: 'Employee',
      department: 'Department',
      position: 'Position',
      payPeriod: 'Pay Period',
      baseSalary: 'Base Salary',
      overtime: 'Overtime',
      bonuses: 'Bonuses',
      allowances: 'Allowances',
      deductions: 'Deductions',
      taxes: 'Taxes',
      netSalary: 'Net Salary',
      status: 'Status',
      payDate: 'Pay Date',
      workingDays: 'Working Days',
      overtimeHours: 'Overtime Hours',
      actions: 'Actions',
      view: 'View',
      edit: 'Edit',
      download: 'Download',
      paySlip: 'Pay Slip',
      totalEmployees: 'Total Employees',
      totalPayroll: 'Total Payroll',
      avgSalary: 'Average Salary',
      pendingPayments: 'Pending Payments',
      draft: 'Draft',
      processed: 'Processed',
      paid: 'Paid',
      cancelled: 'Cancelled',
      sar: 'SAR',
      hours: 'Hours',
      days: 'Days',
      generateReports: 'Generate Reports',
      bulkProcess: 'Bulk Process'
    }
  }

  const t = text[language]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft': return 'bg-muted/20 text-muted-foreground border-muted/30'
      case 'processed': return 'bg-primary/20 text-primary border-primary/30'
      case 'paid': return 'bg-secondary/20 text-secondary border-secondary/30'
      case 'cancelled': return 'bg-destructive/20 text-destructive border-destructive/30'
      default: return 'bg-muted/20 text-muted-foreground border-muted/30'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'draft': return <Clock className="w-4 h-4" />
      case 'processed': return <Calculator className="w-4 h-4" />
      case 'paid': return <CheckCircle className="w-4 h-4" />
      case 'cancelled': return <AlertTriangle className="w-4 h-4" />
      default: return <Clock className="w-4 h-4" />
    }
  }

  const filteredRecords = payrollRecords.filter(record => {
    const matchesSearch = record.employee.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         record.employeeAr.includes(searchTerm) ||
                         record.employeeId.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === 'all' || record.status === statusFilter
    const matchesDepartment = departmentFilter === 'all' || record.department === departmentFilter
    const matchesPeriod = record.payPeriod === selectedPeriod
    return matchesSearch && matchesStatus && matchesDepartment && matchesPeriod
  })

  const totalPayroll = filteredRecords.reduce((sum, record) => sum + record.netSalary, 0)
  const avgSalary = filteredRecords.length > 0 ? totalPayroll / filteredRecords.length : 0
  const pendingPayments = payrollRecords.filter(record => record.status === 'draft' || record.status === 'processed').length

  return (
    <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">{t.title}</h1>
            <p className="text-white/70">{t.description}</p>
          </div>
          <div className="flex gap-3">
            <Button variant="outline" className="modern-button">
              <FileText className="w-4 h-4 mr-2" />
              {t.generateReports}
            </Button>
            <Button variant="outline" className="modern-button">
              <Calculator className="w-4 h-4 mr-2" />
              {t.bulkProcess}
            </Button>
            <Button className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70">
              <Plus className="w-4 h-4 mr-2" />
              {t.processPayroll}
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="modern-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.totalEmployees}</p>
                  <p className="text-2xl font-bold text-white">{filteredRecords.length}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-primary/20 flex items-center justify-center">
                  <Users className="w-6 h-6 text-primary" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="modern-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.totalPayroll}</p>
                  <p className="text-2xl font-bold text-white">{totalPayroll.toLocaleString()} {t.sar}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-secondary/20 flex items-center justify-center">
                  <DollarSign className="w-6 h-6 text-secondary" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="modern-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.avgSalary}</p>
                  <p className="text-2xl font-bold text-white">{avgSalary.toLocaleString()} {t.sar}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-accent/20 flex items-center justify-center">
                  <TrendingUp className="w-6 h-6 text-accent" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="modern-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.pendingPayments}</p>
                  <p className="text-2xl font-bold text-white">{pendingPayments}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-muted/20 flex items-center justify-center">
                  <Clock className="w-6 h-6 text-muted-foreground" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card className="modern-card">
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 w-4 h-4" />
                  <Input
                    placeholder={t.search}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 modern-input"
                  />
                </div>
              </div>
              <select
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(e.target.value)}
                className="modern-input px-3 py-2 rounded-lg"
              >
                <option value="2024-01">يناير 2024</option>
                <option value="2023-12">ديسمبر 2023</option>
                <option value="2023-11">نوفمبر 2023</option>
              </select>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="modern-input px-3 py-2 rounded-lg"
              >
                <option value="all">جميع الحالات</option>
                <option value="draft">{t.draft}</option>
                <option value="processed">{t.processed}</option>
                <option value="paid">{t.paid}</option>
                <option value="cancelled">{t.cancelled}</option>
              </select>
              <select
                value={departmentFilter}
                onChange={(e) => setDepartmentFilter(e.target.value)}
                className="modern-input px-3 py-2 rounded-lg"
              >
                <option value="all">جميع الأقسام</option>
                <option value="Engineering">الهندسة</option>
                <option value="Marketing">التسويق</option>
                <option value="Finance">المالية</option>
                <option value="HR">الموارد البشرية</option>
              </select>
            </div>
          </CardContent>
        </Card>

        {/* Payroll Table */}
        <Card className="modern-card">
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-white/10">
                    <th className="text-right p-4 text-white/70 font-medium">{t.employee}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.department}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.baseSalary}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.overtime}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.bonuses}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.deductions}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.netSalary}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.status}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.actions}</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredRecords.map((record) => (
                    <tr key={record.id} className="border-b border-white/5 hover:bg-white/5">
                      <td className="p-4">
                        <div>
                          <span className="text-white font-medium">
                            {language === 'ar' ? record.employeeAr : record.employee}
                          </span>
                          <div className="text-xs text-white/60 mt-1">
                            {record.employeeId} • {language === 'ar' ? record.positionAr : record.position}
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <span className="text-white">{language === 'ar' ? record.departmentAr : record.department}</span>
                      </td>
                      <td className="p-4">
                        <span className="text-white">{record.baseSalary.toLocaleString()} {t.sar}</span>
                      </td>
                      <td className="p-4">
                        <div>
                          <span className="text-white">{record.overtime.toLocaleString()} {t.sar}</span>
                          {record.overtimeHours > 0 && (
                            <div className="text-xs text-white/60">{record.overtimeHours} {t.hours}</div>
                          )}
                        </div>
                      </td>
                      <td className="p-4">
                        <span className="text-white">{record.bonuses.toLocaleString()} {t.sar}</span>
                      </td>
                      <td className="p-4">
                        <span className="text-white">{record.deductions.toLocaleString()} {t.sar}</span>
                      </td>
                      <td className="p-4">
                        <span className="text-white font-semibold">{record.netSalary.toLocaleString()} {t.sar}</span>
                      </td>
                      <td className="p-4">
                        <Badge className={`${getStatusColor(record.status)} border flex items-center gap-1`}>
                          {getStatusIcon(record.status)}
                          {t[record.status as keyof typeof t]}
                        </Badge>
                      </td>
                      <td className="p-4">
                        <div className="flex gap-2">
                          <Button size="sm" variant="ghost" className="text-white/70 hover:text-white">
                            <Eye className="w-4 h-4" />
                          </Button>
                          <Button size="sm" variant="ghost" className="text-white/70 hover:text-white">
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button size="sm" variant="ghost" className="text-blue-400 hover:text-blue-300">
                            <Download className="w-4 h-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
    </div>
  )
}
