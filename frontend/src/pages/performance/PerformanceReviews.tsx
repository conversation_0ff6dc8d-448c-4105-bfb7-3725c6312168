import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Badge } from '../../components/ui/badge'
import {
  Target,
  Plus,
  Search,
  Eye,
  Edit,
  Trash2,
  Star,
  TrendingUp,
  Calendar,
  User,
  FileText,
  Award
} from 'lucide-react'

interface PerformanceReviewsProps {
  language: 'ar' | 'en'
}

interface PerformanceReview {
  id: string
  employee: string
  employeeAr: string
  employeeId: string
  reviewer: string
  reviewerAr: string
  reviewPeriod: string
  reviewType: 'annual' | 'quarterly' | 'probation' | 'project'
  status: 'draft' | 'in-progress' | 'completed' | 'approved'
  overallRating: number
  goals: number
  competencies: number
  development: number
  reviewDate: string
  dueDate: string
  department: string
  departmentAr: string
  position: string
  positionAr: string
}

const mockReviews: PerformanceReview[] = [
  {
    id: '1',
    employee: '<PERSON>',
    employeeAr: 'أحمد الراشد',
    employeeId: 'EMP001',
    reviewer: '<PERSON>',
    reviewerAr: 'سارة حسن',
    reviewPeriod: '2024 Q1',
    reviewType: 'quarterly',
    status: 'completed',
    overallRating: 4.2,
    goals: 4.5,
    competencies: 4.0,
    development: 4.0,
    reviewDate: '2024-01-15',
    dueDate: '2024-01-31',
    department: 'Engineering',
    departmentAr: 'الهندسة',
    position: 'Senior Developer',
    positionAr: 'مطور أول'
  },
  {
    id: '2',
    employee: 'Fatima Mohammed',
    employeeAr: 'فاطمة محمد',
    employeeId: 'EMP002',
    reviewer: 'Omar Abdullah',
    reviewerAr: 'عمر عبدالله',
    reviewPeriod: '2024 Annual',
    reviewType: 'annual',
    status: 'in-progress',
    overallRating: 0,
    goals: 0,
    competencies: 0,
    development: 0,
    reviewDate: '2024-01-20',
    dueDate: '2024-02-15',
    department: 'Marketing',
    departmentAr: 'التسويق',
    position: 'Marketing Manager',
    positionAr: 'مدير التسويق'
  },
  {
    id: '3',
    employee: 'Mohammed Ali',
    employeeAr: 'محمد علي',
    employeeId: 'EMP003',
    reviewer: 'Layla Ahmad',
    reviewerAr: 'ليلى أحمد',
    reviewPeriod: 'Probation Review',
    reviewType: 'probation',
    status: 'approved',
    overallRating: 3.8,
    goals: 4.0,
    competencies: 3.5,
    development: 4.0,
    reviewDate: '2024-01-10',
    dueDate: '2024-01-25',
    department: 'Finance',
    departmentAr: 'المالية',
    position: 'Financial Analyst',
    positionAr: 'محلل مالي'
  }
]

export default function PerformanceReviews({ language }: PerformanceReviewsProps) {
  const [reviews, setReviews] = useState<PerformanceReview[]>(mockReviews)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [typeFilter, setTypeFilter] = useState<string>('all')

  const text = {
    ar: {
      title: 'تقييمات الأداء',
      description: 'إدارة ومتابعة تقييمات أداء الموظفين',
      newReview: 'تقييم جديد',
      search: 'البحث في التقييمات...',
      employee: 'الموظف',
      reviewer: 'المقيم',
      reviewPeriod: 'فترة التقييم',
      reviewType: 'نوع التقييم',
      status: 'الحالة',
      overallRating: 'التقييم العام',
      reviewDate: 'تاريخ التقييم',
      dueDate: 'تاريخ الاستحقاق',
      department: 'القسم',
      position: 'المنصب',
      actions: 'الإجراءات',
      view: 'عرض',
      edit: 'تعديل',
      delete: 'حذف',
      totalReviews: 'إجمالي التقييمات',
      completedReviews: 'التقييمات المكتملة',
      avgRating: 'متوسط التقييم',
      pendingReviews: 'التقييمات المعلقة',
      draft: 'مسودة',
      'in-progress': 'قيد التنفيذ',
      completed: 'مكتمل',
      approved: 'معتمد',
      annual: 'سنوي',
      quarterly: 'ربع سنوي',
      probation: 'فترة تجريبية',
      project: 'مشروع',
      goals: 'الأهداف',
      competencies: 'الكفاءات',
      development: 'التطوير'
    },
    en: {
      title: 'Performance Reviews',
      description: 'Manage and track employee performance reviews',
      newReview: 'New Review',
      search: 'Search reviews...',
      employee: 'Employee',
      reviewer: 'Reviewer',
      reviewPeriod: 'Review Period',
      reviewType: 'Review Type',
      status: 'Status',
      overallRating: 'Overall Rating',
      reviewDate: 'Review Date',
      dueDate: 'Due Date',
      department: 'Department',
      position: 'Position',
      actions: 'Actions',
      view: 'View',
      edit: 'Edit',
      delete: 'Delete',
      totalReviews: 'Total Reviews',
      completedReviews: 'Completed Reviews',
      avgRating: 'Average Rating',
      pendingReviews: 'Pending Reviews',
      draft: 'Draft',
      'in-progress': 'In Progress',
      completed: 'Completed',
      approved: 'Approved',
      annual: 'Annual',
      quarterly: 'Quarterly',
      probation: 'Probation',
      project: 'Project',
      goals: 'Goals',
      competencies: 'Competencies',
      development: 'Development'
    }
  }

  const t = text[language]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft': return 'bg-gray-500/20 text-gray-300 border-gray-500/30'
      case 'in-progress': return 'bg-blue-500/20 text-blue-300 border-blue-500/30'
      case 'completed': return 'bg-green-500/20 text-green-300 border-green-500/30'
      case 'approved': return 'bg-purple-500/20 text-purple-300 border-purple-500/30'
      default: return 'bg-gray-500/20 text-gray-300 border-gray-500/30'
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'annual': return 'bg-purple-500/20 text-purple-300 border-purple-500/30'
      case 'quarterly': return 'bg-blue-500/20 text-blue-300 border-blue-500/30'
      case 'probation': return 'bg-orange-500/20 text-orange-300 border-orange-500/30'
      case 'project': return 'bg-green-500/20 text-green-300 border-green-500/30'
      default: return 'bg-gray-500/20 text-gray-300 border-gray-500/30'
    }
  }

  const getRatingStars = (rating: number) => {
    const stars = []
    const fullStars = Math.floor(rating)
    const hasHalfStar = rating % 1 !== 0

    for (let i = 0; i < fullStars; i++) {
      stars.push(<Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />)
    }

    if (hasHalfStar) {
      stars.push(<Star key="half" className="w-4 h-4 fill-yellow-400/50 text-yellow-400" />)
    }

    const remainingStars = 5 - Math.ceil(rating)
    for (let i = 0; i < remainingStars; i++) {
      stars.push(<Star key={`empty-${i}`} className="w-4 h-4 text-gray-400" />)
    }

    return stars
  }

  const filteredReviews = reviews.filter(review => {
    const matchesSearch = review.employee.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         review.employeeAr.includes(searchTerm) ||
                         review.employeeId.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         review.reviewer.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         review.reviewerAr.includes(searchTerm)
    const matchesStatus = statusFilter === 'all' || review.status === statusFilter
    const matchesType = typeFilter === 'all' || review.reviewType === typeFilter
    return matchesSearch && matchesStatus && matchesType
  })

  const completedReviews = reviews.filter(review => review.status === 'completed' || review.status === 'approved')
  const avgRating = completedReviews.length > 0
    ? completedReviews.reduce((sum, review) => sum + review.overallRating, 0) / completedReviews.length
    : 0
  const pendingReviews = reviews.filter(review => review.status === 'draft' || review.status === 'in-progress').length

  return (
    <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">{t.title}</h1>
            <p className="text-white/70">{t.description}</p>
          </div>
          <Button className="bg-gradient-to-r from-sky-500 to-blue-600 hover:from-sky-600 hover:to-blue-700">
            <Plus className="w-4 h-4 mr-2" />
            {t.newReview}
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="modern-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.totalReviews}</p>
                  <p className="text-2xl font-bold text-white">{reviews.length}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-primary/20 flex items-center justify-center">
                  <FileText className="w-6 h-6 text-primary" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="modern-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.completedReviews}</p>
                  <p className="text-2xl font-bold text-white">{completedReviews.length}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-green-500/20 flex items-center justify-center">
                  <Award className="w-6 h-6 text-green-400" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="modern-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.avgRating}</p>
                  <div className="flex items-center gap-2">
                    <p className="text-2xl font-bold text-white">{avgRating.toFixed(1)}</p>
                    <div className="flex">
                      {getRatingStars(avgRating).slice(0, 5)}
                    </div>
                  </div>
                </div>
                <div className="w-12 h-12 rounded-full bg-secondary/20 flex items-center justify-center">
                  <Star className="w-6 h-6 text-secondary" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="modern-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.pendingReviews}</p>
                  <p className="text-2xl font-bold text-white">{pendingReviews}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-accent/20 flex items-center justify-center">
                  <Calendar className="w-6 h-6 text-accent" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card className="modern-card">
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 w-4 h-4" />
                  <Input
                    placeholder={t.search}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 glass-input"
                  />
                </div>
              </div>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="glass-input px-3 py-2 rounded-lg"
              >
                <option value="all">جميع الحالات</option>
                <option value="draft">{t.draft}</option>
                <option value="in-progress">{t['in-progress']}</option>
                <option value="completed">{t.completed}</option>
                <option value="approved">{t.approved}</option>
              </select>
              <select
                value={typeFilter}
                onChange={(e) => setTypeFilter(e.target.value)}
                className="glass-input px-3 py-2 rounded-lg"
              >
                <option value="all">جميع الأنواع</option>
                <option value="annual">{t.annual}</option>
                <option value="quarterly">{t.quarterly}</option>
                <option value="probation">{t.probation}</option>
                <option value="project">{t.project}</option>
              </select>
            </div>
          </CardContent>
        </Card>

        {/* Reviews Table */}
        <Card className="modern-card">
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-white/10">
                    <th className="text-right p-4 text-white/70 font-medium">{t.employee}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.reviewer}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.reviewPeriod}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.reviewType}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.status}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.overallRating}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.dueDate}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.actions}</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredReviews.map((review) => (
                    <tr key={review.id} className="border-b border-white/5 hover:bg-white/5">
                      <td className="p-4">
                        <div>
                          <span className="text-white font-medium">
                            {language === 'ar' ? review.employeeAr : review.employee}
                          </span>
                          <div className="text-xs text-white/60 mt-1">
                            {review.employeeId} • {language === 'ar' ? review.positionAr : review.position}
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <span className="text-white">{language === 'ar' ? review.reviewerAr : review.reviewer}</span>
                      </td>
                      <td className="p-4">
                        <span className="text-white">{review.reviewPeriod}</span>
                      </td>
                      <td className="p-4">
                        <Badge className={`${getTypeColor(review.reviewType)} border`}>
                          {t[review.reviewType as keyof typeof t]}
                        </Badge>
                      </td>
                      <td className="p-4">
                        <Badge className={`${getStatusColor(review.status)} border`}>
                          {t[review.status as keyof typeof t]}
                        </Badge>
                      </td>
                      <td className="p-4">
                        {review.overallRating > 0 ? (
                          <div className="flex items-center gap-2">
                            <span className="text-white">{review.overallRating.toFixed(1)}</span>
                            <div className="flex">
                              {getRatingStars(review.overallRating)}
                            </div>
                          </div>
                        ) : (
                          <span className="text-white/50">-</span>
                        )}
                      </td>
                      <td className="p-4">
                        <span className="text-white/70">{review.dueDate}</span>
                      </td>
                      <td className="p-4">
                        <div className="flex gap-2">
                          <Button size="sm" variant="ghost" className="text-white/70 hover:text-white">
                            <Eye className="w-4 h-4" />
                          </Button>
                          <Button size="sm" variant="ghost" className="text-white/70 hover:text-white">
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button size="sm" variant="ghost" className="text-red-400 hover:text-red-300">
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
    </div>
  )
}
