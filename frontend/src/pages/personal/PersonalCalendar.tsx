/**
 * Personal Calendar Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState, MouseEvent, ReactElement } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  Calendar as CalendarIcon,
  Clock,
  MapPin,
  Users,
  Video,
  Bell,
  Eye,
  Edit,
  Trash2,
  CheckCircle,
  Alert<PERSON>riangle,
  Star,
  User
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { personalCalendarService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface PersonalCalendarProps {
  language: 'ar' | 'en'
}

interface CalendarEvent {
  id: number
  title: string
  titleAr: string
  description: string
  descriptionAr: string
  date: string
  time: string
  duration: string
  type: 'meeting' | 'task' | 'reminder' | 'leave' | 'training' | 'appointment'
  location?: string
  locationAr?: string
  attendees?: string
  attendeesAr?: string
  isOnline: boolean
  priority: 'high' | 'medium' | 'low'
  status: 'confirmed' | 'pending' | 'cancelled' | 'completed'
  createdBy: string
  createdByAr: string
}

const translations = {
  ar: {
    personalCalendar: 'تقويمي الشخصي',
    addEvent: 'إضافة حدث',
    editEvent: 'تعديل الحدث',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذا الحدث؟',
    searchPlaceholder: 'البحث في الأحداث...',
    title: 'العنوان',
    description: 'الوصف',
    date: 'التاريخ',
    time: 'الوقت',
    duration: 'المدة',
    type: 'النوع',
    location: 'الموقع',
    attendees: 'الحضور',
    isOnline: 'عبر الإنترنت',
    priority: 'الأولوية',
    status: 'الحالة',
    createdBy: 'أنشأ بواسطة',
    meeting: 'اجتماع',
    task: 'مهمة',
    reminder: 'تذكير',
    leave: 'إجازة',
    training: 'تدريب',
    appointment: 'موعد',
    confirmed: 'مؤكد',
    pending: 'معلق',
    cancelled: 'ملغي',
    completed: 'مكتمل',
    high: 'عالي',
    medium: 'متوسط',
    low: 'منخفض'
  },
  en: {
    personalCalendar: 'Personal Calendar',
    addEvent: 'Add Event',
    editEvent: 'Edit Event',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this event?',
    searchPlaceholder: 'Search events...',
    title: 'Title',
    description: 'Description',
    date: 'Date',
    time: 'Time',
    duration: 'Duration',
    type: 'Type',
    location: 'Location',
    attendees: 'Attendees',
    isOnline: 'Online',
    priority: 'Priority',
    status: 'Status',
    createdBy: 'Created By',
    meeting: 'Meeting',
    task: 'Task',
    reminder: 'Reminder',
    leave: 'Leave',
    training: 'Training',
    appointment: 'Appointment',
    confirmed: 'Confirmed',
    pending: 'Pending',
    cancelled: 'Cancelled',
    completed: 'Completed',
    high: 'High',
    medium: 'Medium',
    low: 'Low'
  }
}

export default function PersonalCalendar({ language }: PersonalCalendarProps): React.ReactElement {
  const [showModal, setShowModal] = useState<boolean>(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: events,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<CalendarEvent>({
    service: personalCalendarService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getEventTypeColor = (type: string): void => {
    switch (type) {
      case 'meeting': return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'task': return 'bg-green-100 text-green-800 border-green-200'
      case 'reminder': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'leave': return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'training': return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'appointment': return 'bg-pink-100 text-pink-800 border-pink-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getPriorityColor = (priority: string): void => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200'
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'low': return 'bg-green-100 text-green-800 border-green-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusColor = (status: string): void => {
    switch (status) {
      case 'confirmed': return 'bg-green-100 text-green-800 border-green-200'
      case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'cancelled': return 'bg-red-100 text-red-800 border-red-200'
      case 'completed': return 'bg-blue-100 text-blue-800 border-blue-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getTypeIcon = (type: string): void => {
    switch (type) {
      case 'meeting': return <Users className="h-4 w-4" />
      case 'task': return <CheckCircle className="h-4 w-4" />
      case 'reminder': return <Bell className="h-4 w-4" />
      case 'leave': return <CalendarIcon className="h-4 w-4" />
      case 'training': return <Star className="h-4 w-4" />
      case 'appointment': return <User className="h-4 w-4" />
      default: return <CalendarIcon className="h-4 w-4" />
    }
  }

  // Table columns configuration
  const columns: TableColumn<CalendarEvent>[] = [
    {
      key: 'title',
      label: t.title,
      sortable: true,
      render: (item: CalendarEvent) => (
        <div className="flex items-center gap-2">
          <div className="p-2 rounded-lg bg-gradient-to-r from-blue-500 to-purple-500 text-white">
            {getTypeIcon(item.type)}
          </div>
          <div>
            <div className="font-medium text-white">
              {language === 'ar' ? item.titleAr : item.title}
            </div>
            <div className="text-sm text-white/60">
              {language === 'ar' ? item.descriptionAr : item.description}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'type',
      label: t.type,
      sortable: true,
      render: (item: CalendarEvent) => (
        <Badge className={getEventTypeColor(item.type)}>
          {t[item.type as keyof typeof t]}
        </Badge>
      )
    },
    {
      key: 'date',
      label: t.date,
      sortable: true,
      render: (item: CalendarEvent) => (
        <div className="flex items-center gap-1">
          <CalendarIcon className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">{item.date}</span>
        </div>
      )
    },
    {
      key: 'time',
      label: t.time,
      sortable: true,
      render: (item: CalendarEvent) => (
        <div className="flex items-center gap-1">
          <Clock className="h-3 w-3 text-green-400" />
          <span className="text-white/80">{item.time}</span>
          <span className="text-white/50 text-xs">({item.duration})</span>
        </div>
      )
    },
    {
      key: 'location',
      label: t.location,
      render: (item: CalendarEvent) => (
        <div className="flex items-center gap-1">
          {item.isOnline ? (
            <>
              <Video className="h-3 w-3 text-purple-400" />
              <span className="text-white/80">Online</span>
            </>
          ) : (
            <>
              <MapPin className="h-3 w-3 text-red-400" />
              <span className="text-white/80">
                {language === 'ar' ? item.locationAr : item.location}
              </span>
            </>
          )}
        </div>
      )
    },
    {
      key: 'priority',
      label: t.priority,
      sortable: true,
      render: (item: CalendarEvent) => (
        <Badge className={getPriorityColor(item.priority)}>
          {t[item.priority as keyof typeof t]}
        </Badge>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: CalendarEvent) => (
        <Badge className={getStatusColor(item.status)}>
          {t[item.status as keyof typeof t]}
        </Badge>
      )
    },
    {
      key: 'attendees',
      label: t.attendees,
      render: (item: CalendarEvent) => (
        <div className="flex items-center gap-1">
          <Users className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">
            {language === 'ar' ? item.attendeesAr : item.attendees}
          </span>
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: React.MouseEvent) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: React.MouseEvent) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: React.MouseEvent) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'type',
      label: t.type,
      options: [
        { label: t.meeting, value: 'meeting' },
        { label: t.task, value: 'task' },
        { label: t.reminder, value: 'reminder' },
        { label: t.leave, value: 'leave' },
        { label: t.training, value: 'training' },
        { label: t.appointment, value: 'appointment' }
      ]
    },
    {
      key: 'priority',
      label: t.priority,
      options: [
        { label: t.high, value: 'high' },
        { label: t.medium, value: 'medium' },
        { label: t.low, value: 'low' }
      ]
    },
    {
      key: 'status',
      label: t.status,
      options: [
        { label: t.confirmed, value: 'confirmed' },
        { label: t.pending, value: 'pending' },
        { label: t.cancelled, value: 'cancelled' },
        { label: t.completed, value: 'completed' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'title',
      label: t.title,
      type: 'text',
      required: true
    },
    {
      name: 'titleAr',
      label: t.title + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'description',
      label: t.description,
      type: 'textarea',
      required: true
    },
    {
      name: 'descriptionAr',
      label: t.description + ' (عربي)',
      type: 'textarea',
      required: true
    },
    {
      name: 'date',
      label: t.date,
      type: 'date',
      required: true
    },
    {
      name: 'time',
      label: t.time,
      type: 'text',
      required: true
    },
    {
      name: 'duration',
      label: t.duration,
      type: 'text',
      required: true,
      placeholder: 'e.g., 1h, 30m, 2h 30m'
    },
    {
      name: 'type',
      label: t.type,
      type: 'select',
      required: true,
      options: [
        { label: t.meeting, value: 'meeting' },
        { label: t.task, value: 'task' },
        { label: t.reminder, value: 'reminder' },
        { label: t.leave, value: 'leave' },
        { label: t.training, value: 'training' },
        { label: t.appointment, value: 'appointment' }
      ]
    },
    {
      name: 'location',
      label: t.location,
      type: 'text'
    },
    {
      name: 'locationAr',
      label: t.location + ' (عربي)',
      type: 'text'
    },
    {
      name: 'attendees',
      label: t.attendees,
      type: 'textarea',
      placeholder: 'One attendee per line'
    },
    {
      name: 'attendeesAr',
      label: t.attendees + ' (عربي)',
      type: 'textarea',
      placeholder: 'حضور واحد في كل سطر'
    },
    {
      name: 'isOnline',
      label: t.isOnline,
      type: 'checkbox'
    },
    {
      name: 'priority',
      label: t.priority,
      type: 'select',
      required: true,
      options: [
        { label: t.high, value: 'high' },
        { label: t.medium, value: 'medium' },
        { label: t.low, value: 'low' }
      ]
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { label: t.confirmed, value: 'confirmed' },
        { label: t.pending, value: 'pending' },
        { label: t.cancelled, value: 'cancelled' },
        { label: t.completed, value: 'completed' }
      ]
    },
    {
      name: 'createdBy',
      label: t.createdBy,
      type: 'text',
      required: true
    },
    {
      name: 'createdByAr',
      label: t.createdBy + ' (عربي)',
      type: 'text',
      required: true
    }
  ]

  // Event handlers
  const handleCreate = (): void => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = (): void => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<CalendarEvent>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async () => {
    try {
      await exportData('csv')
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.personalCalendar}
        data={events}
        columns={columns}
        actions={actions as unknown}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addEvent : modalMode === 'edit' ? t.editEvent : t.view}
        fields={formFields}
        initialData={selectedItem as unknown}
        language={language}
        loading={creating || updating}
      />
    </div>
  )

}
