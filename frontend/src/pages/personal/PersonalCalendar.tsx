import { useState, useEffect } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Calendar as CalendarIcon, 
  ChevronLeft, 
  ChevronRight,
  Plus,
  Clock,
  MapPin,
  Users,
  Video,
  Bell,
  Filter,
  Download,
  RefreshCw
} from 'lucide-react'

interface PersonalCalendarProps {
  language: 'ar' | 'en'
}

interface CalendarEvent {
  id: string
  title: string
  titleAr: string
  description: string
  descriptionAr: string
  date: string
  time: string
  duration: string
  type: 'meeting' | 'task' | 'reminder' | 'leave' | 'training'
  location?: string
  attendees?: string[]
  isOnline?: boolean
  priority: 'high' | 'medium' | 'low'
  status: 'confirmed' | 'pending' | 'cancelled'
}

const mockEvents: CalendarEvent[] = [
  {
    id: '1',
    title: 'Team Meeting',
    titleAr: 'اجتماع الفريق',
    description: 'Weekly team sync meeting',
    descriptionAr: 'اجتماع أسبوعي لمزامنة الفريق',
    date: '2024-01-15',
    time: '10:00',
    duration: '1h',
    type: 'meeting',
    location: 'Conference Room A',
    attendees: ['Ahmed Hassan', 'Sara Ali'],
    isOnline: false,
    priority: 'high',
    status: 'confirmed'
  },
  {
    id: '2',
    title: 'Project Review',
    titleAr: 'مراجعة المشروع',
    description: 'Review project progress and milestones',
    descriptionAr: 'مراجعة تقدم المشروع والمعالم',
    date: '2024-01-15',
    time: '14:00',
    duration: '2h',
    type: 'meeting',
    isOnline: true,
    priority: 'medium',
    status: 'confirmed'
  },
  {
    id: '3',
    title: 'Submit Monthly Report',
    titleAr: 'تقديم التقرير الشهري',
    description: 'Deadline for monthly performance report',
    descriptionAr: 'موعد تسليم تقرير الأداء الشهري',
    date: '2024-01-16',
    time: '17:00',
    duration: '30m',
    type: 'task',
    priority: 'high',
    status: 'pending'
  },
  {
    id: '4',
    title: 'Training Session',
    titleAr: 'جلسة تدريبية',
    description: 'New software training',
    descriptionAr: 'تدريب على البرنامج الجديد',
    date: '2024-01-17',
    time: '09:00',
    duration: '3h',
    type: 'training',
    location: 'Training Room',
    priority: 'medium',
    status: 'confirmed'
  },
  {
    id: '5',
    title: 'Annual Leave',
    titleAr: 'إجازة سنوية',
    description: 'Personal vacation time',
    descriptionAr: 'وقت إجازة شخصية',
    date: '2024-01-20',
    time: '00:00',
    duration: 'All day',
    type: 'leave',
    priority: 'low',
    status: 'confirmed'
  }
]

const monthNames = {
  ar: [
    'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
    'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
  ],
  en: [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ]
}

const dayNames = {
  ar: ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'],
  en: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
}

export default function PersonalCalendar({ language }: PersonalCalendarProps) {
  const [currentDate, setCurrentDate] = useState(new Date())
  const [selectedDate, setSelectedDate] = useState<string | null>(null)
  const [events, setEvents] = useState<CalendarEvent[]>(mockEvents)
  const [view, setView] = useState<'month' | 'week' | 'day'>('month')

  const text = {
    ar: {
      title: 'تقويمي الشخصي',
      description: 'إدارة مواعيدي ومهامي',
      today: 'اليوم',
      month: 'شهر',
      week: 'أسبوع',
      day: 'يوم',
      newEvent: 'حدث جديد',
      filter: 'تصفية',
      export: 'تصدير',
      refresh: 'تحديث',
      noEvents: 'لا توجد أحداث',
      upcomingEvents: 'الأحداث القادمة',
      allDay: 'طوال اليوم',
      meeting: 'اجتماع',
      task: 'مهمة',
      reminder: 'تذكير',
      leave: 'إجازة',
      training: 'تدريب',
      confirmed: 'مؤكد',
      pending: 'معلق',
      cancelled: 'ملغي',
      high: 'عالي',
      medium: 'متوسط',
      low: 'منخفض',
      online: 'عبر الإنترنت',
      location: 'الموقع',
      attendees: 'الحضور'
    },
    en: {
      title: 'My Personal Calendar',
      description: 'Manage my appointments and tasks',
      today: 'Today',
      month: 'Month',
      week: 'Week',
      day: 'Day',
      newEvent: 'New Event',
      filter: 'Filter',
      export: 'Export',
      refresh: 'Refresh',
      noEvents: 'No events',
      upcomingEvents: 'Upcoming Events',
      allDay: 'All day',
      meeting: 'Meeting',
      task: 'Task',
      reminder: 'Reminder',
      leave: 'Leave',
      training: 'Training',
      confirmed: 'Confirmed',
      pending: 'Pending',
      cancelled: 'Cancelled',
      high: 'High',
      medium: 'Medium',
      low: 'Low',
      online: 'Online',
      location: 'Location',
      attendees: 'Attendees'
    }
  }

  const t = text[language]

  const getEventTypeColor = (type: string) => {
    switch (type) {
      case 'meeting': return 'from-blue-500 to-blue-600'
      case 'task': return 'from-green-500 to-green-600'
      case 'reminder': return 'from-yellow-500 to-yellow-600'
      case 'leave': return 'from-purple-500 to-purple-600'
      case 'training': return 'from-orange-500 to-orange-600'
      default: return 'from-gray-500 to-gray-600'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-500'
      case 'medium': return 'bg-yellow-500'
      case 'low': return 'bg-green-500'
      default: return 'bg-gray-500'
    }
  }

  const getDaysInMonth = (date: Date) => {
    const year = date.getFullYear()
    const month = date.getMonth()
    const firstDay = new Date(year, month, 1)
    const lastDay = new Date(year, month + 1, 0)
    const daysInMonth = lastDay.getDate()
    const startingDayOfWeek = firstDay.getDay()

    const days = []
    
    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(null)
    }
    
    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(day)
    }
    
    return days
  }

  const getEventsForDate = (date: string) => {
    return events.filter(event => event.date === date)
  }

  const formatDate = (date: Date) => {
    return date.toISOString().split('T')[0]
  }

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentDate(prev => {
      const newDate = new Date(prev)
      if (direction === 'prev') {
        newDate.setMonth(prev.getMonth() - 1)
      } else {
        newDate.setMonth(prev.getMonth() + 1)
      }
      return newDate
    })
  }

  const goToToday = () => {
    setCurrentDate(new Date())
    setSelectedDate(formatDate(new Date()))
  }

  const days = getDaysInMonth(currentDate)
  const todayString = formatDate(new Date())
  const upcomingEvents = events
    .filter(event => new Date(event.date) >= new Date())
    .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
    .slice(0, 5)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 sm:gap-6 mb-6 sm:mb-8">
        <div>
          <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-white drop-shadow-lg">
            {t.title}
          </h1>
          <p className="text-white/80 mt-1 sm:mt-2 text-sm sm:text-base lg:text-lg">
            {t.description}
          </p>
        </div>
        
        <div className="flex flex-wrap items-center gap-2 sm:gap-3">
          <Button
            onClick={goToToday}
            variant="outline"
            className="glass-button text-xs sm:text-sm px-3 sm:px-4 py-2"
          >
            {t.today}
          </Button>
          <Button className="gradient-bg-blue hover:scale-105 transform transition-all duration-300 glow-hover text-white font-semibold px-4 sm:px-6 py-2 sm:py-3 text-sm sm:text-base">
            <Plus className="h-4 w-4 sm:h-5 sm:w-5 mr-2" />
            {t.newEvent}
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Calendar */}
        <Card className="modern-card border-0 lg:col-span-3">
          <CardHeader className="p-4 sm:p-6 border-b border-white/10">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => navigateMonth('prev')}
                  className="text-white/70 hover:text-white p-2"
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <h2 className="text-xl font-bold text-white">
                  {monthNames[language][currentDate.getMonth()]} {currentDate.getFullYear()}
                </h2>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => navigateMonth('next')}
                  className="text-white/70 hover:text-white p-2"
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
              
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" className="glass-button text-xs px-3 py-2">
                  <Filter className="h-3 w-3 mr-1" />
                  {t.filter}
                </Button>
                <Button variant="outline" size="sm" className="glass-button text-xs px-3 py-2">
                  <Download className="h-3 w-3 mr-1" />
                  {t.export}
                </Button>
              </div>
            </div>
          </CardHeader>
          
          <CardContent className="p-4 sm:p-6">
            {/* Day Headers */}
            <div className="grid grid-cols-7 gap-1 mb-4">
              {dayNames[language].map((day) => (
                <div key={day} className="p-2 text-center text-white/70 font-medium text-sm">
                  {day}
                </div>
              ))}
            </div>
            
            {/* Calendar Grid */}
            <div className="grid grid-cols-7 gap-1">
              {days.map((day, index) => {
                if (!day) {
                  return <div key={index} className="p-2 h-20"></div>
                }
                
                const dateString = formatDate(new Date(currentDate.getFullYear(), currentDate.getMonth(), day))
                const dayEvents = getEventsForDate(dateString)
                const isToday = dateString === todayString
                const isSelected = dateString === selectedDate
                
                return (
                  <div
                    key={day}
                    onClick={() => setSelectedDate(dateString)}
                    className={`p-2 h-20 border border-white/10 cursor-pointer transition-all duration-300 hover:bg-white/5 ${
                      isToday ? 'bg-blue-500/20 border-blue-500/50' : ''
                    } ${isSelected ? 'bg-white/10 border-white/30' : ''}`}
                  >
                    <div className="text-white text-sm font-medium mb-1">{day}</div>
                    <div className="space-y-1">
                      {dayEvents.slice(0, 2).map((event) => (
                        <div
                          key={event.id}
                          className={`text-xs p-1 rounded bg-gradient-to-r ${getEventTypeColor(event.type)} text-white truncate`}
                        >
                          {language === 'ar' ? event.titleAr : event.title}
                        </div>
                      ))}
                      {dayEvents.length > 2 && (
                        <div className="text-xs text-white/60">+{dayEvents.length - 2} more</div>
                      )}
                    </div>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>

        {/* Upcoming Events */}
        <Card className="modern-card border-0">
          <CardHeader className="p-4 sm:p-6 border-b border-white/10">
            <CardTitle className="text-white text-lg">{t.upcomingEvents}</CardTitle>
          </CardHeader>
          <CardContent className="p-4 sm:p-6">
            <div className="space-y-4">
              {upcomingEvents.map((event) => (
                <div key={event.id} className="glass-card border-white/10 p-4 rounded-lg">
                  <div className="flex items-start justify-between mb-2">
                    <h3 className="text-white font-medium text-sm">
                      {language === 'ar' ? event.titleAr : event.title}
                    </h3>
                    <div className={`w-2 h-2 rounded-full ${getPriorityColor(event.priority)}`}></div>
                  </div>
                  
                  <div className="space-y-1 text-xs text-white/70">
                    <div className="flex items-center gap-2">
                      <CalendarIcon className="h-3 w-3" />
                      <span>{new Date(event.date).toLocaleDateString()}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="h-3 w-3" />
                      <span>{event.time} ({event.duration})</span>
                    </div>
                    {event.location && (
                      <div className="flex items-center gap-2">
                        <MapPin className="h-3 w-3" />
                        <span>{event.location}</span>
                      </div>
                    )}
                    {event.isOnline && (
                      <div className="flex items-center gap-2">
                        <Video className="h-3 w-3" />
                        <span>{t.online}</span>
                      </div>
                    )}
                  </div>
                  
                  <div className="flex items-center justify-between mt-3">
                    <Badge className={`bg-gradient-to-r ${getEventTypeColor(event.type)} text-white text-xs`}>
                      {t[event.type as keyof typeof t] || event.type}
                    </Badge>
                    <Badge variant="outline" className="text-white/70 border-white/30 text-xs">
                      {t[event.status as keyof typeof t] || event.status}
                    </Badge>
                  </div>
                </div>
              ))}
              
              {upcomingEvents.length === 0 && (
                <div className="text-center py-8">
                  <CalendarIcon className="h-12 w-12 text-white/40 mx-auto mb-3" />
                  <p className="text-white/60">{t.noEvents}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
