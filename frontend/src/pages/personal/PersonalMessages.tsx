/**
 * Personal Messages Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  MessageSquare,
  Send,
  User,
  Clock,
  Check,
  CheckCheck,
  CheckCircle,
  Circle,
  Eye,
  Edit,
  Trash2,
  Users,
  Phone,
  Video,
  Star
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { personalMessageService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface PersonalMessagesProps {
  language: 'ar' | 'en'
}

// FIXED: Interface now matches backend MessageSerializer exactly
interface PersonalMessage {
  id: number
  sender: number
  sender_name: string
  recipient: number
  recipient_name: string
  subject: string
  content: string
  is_read: boolean
  is_important: boolean
  parent_message?: number
  attachment?: string
  sent_at: string
  read_at?: string
}

const translations = {
  ar: {
    personalMessages: 'رسائلي الشخصية',
    addMessage: 'إضافة رسالة',
    editMessage: 'تعديل الرسالة',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذه الرسالة؟',
    searchPlaceholder: 'البحث في الرسائل الشخصية...',
    subject: 'الموضوع',
    content: 'المحتوى',
    sender: 'المرسل',
    recipient: 'المستقبل',
    messageType: 'نوع الرسالة',
    priority: 'الأولوية',
    status: 'الحالة',
    sentDate: 'تاريخ الإرسال',
    readDate: 'تاريخ القراءة',
    isStarred: 'مميز',
    isOnline: 'متصل',
    attachments: 'المرفقات',
    sent: 'مرسل',
    delivered: 'تم التسليم',
    read: 'مقروء',
    archived: 'مؤرشف',
    direct: 'مباشر',
    group: 'جماعي',
    broadcast: 'إذاعة',
    high: 'عالي',
    medium: 'متوسط',
    low: 'منخفض',
    urgent: 'عاجل'
  },
  en: {
    personalMessages: 'Personal Messages',
    addMessage: 'Add Message',
    editMessage: 'Edit Message',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this message?',
    searchPlaceholder: 'Search personal messages...',
    subject: 'Subject',
    content: 'Content',
    sender: 'Sender',
    recipient: 'Recipient',
    messageType: 'Message Type',
    priority: 'Priority',
    status: 'Status',
    sentDate: 'Sent Date',
    readDate: 'Read Date',
    isStarred: 'Starred',
    isOnline: 'Online',
    attachments: 'Attachments',
    sent: 'Sent',
    delivered: 'Delivered',
    read: 'Read',
    archived: 'Archived',
    direct: 'Direct',
    group: 'Group',
    broadcast: 'Broadcast',
    high: 'High',
    medium: 'Medium',
    low: 'Low',
    urgent: 'Urgent'
  }
}

export default function PersonalMessages({ language }: PersonalMessagesProps): React.ReactElement {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: messages,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<PersonalMessage>({
    service: personalMessageService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getStatusIcon = (status: string): void => {
    switch (status) {
      case 'sent':
        return <Send className="h-4 w-4 text-blue-400" />
      case 'delivered':
        return <CheckCheck className="h-4 w-4 text-green-400" />
      case 'read':
        return <Eye className="h-4 w-4 text-purple-400" />
      case 'archived':
        return <MessageSquare className="h-4 w-4 text-gray-400" />
      default:
        return <Clock className="h-4 w-4 text-gray-400" />
    }
  }

  const getMessageTypeIcon = (type: string): void => {
    switch (type) {
      case 'direct':
        return <User className="h-4 w-4 text-blue-400" />
      case 'group':
        return <Users className="h-4 w-4 text-green-400" />
      case 'broadcast':
        return <MessageSquare className="h-4 w-4 text-purple-400" />
      default:
        return <MessageSquare className="h-4 w-4 text-gray-400" />
    }
  }

  const getPriorityColor = (priority: string): void => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'medium':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'low':
        return 'bg-green-100 text-green-800 border-green-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  // Table columns configuration
  const columns: TableColumn<PersonalMessage>[] = [
    {
      key: 'subject',
      label: t.subject,
      sortable: true,
      render: (item: PersonalMessage) => (
        <div className="flex items-center gap-2">
          <MessageSquare className="h-4 w-4 text-blue-400" />
          <div>
            <div className="font-medium text-white">
              {item.subject}
            </div>
            <div className="text-sm text-white/60">
              {item.content?.substring(0, 50) + '...'}
            </div>
          </div>
          {item.is_important && <Star className="h-4 w-4 text-yellow-400 fill-current" />}
        </div>
      )
    },
    {
      key: 'sender',
      label: t.sender,
      sortable: true,
      render: (item: PersonalMessage) => (
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-white font-bold text-xs">
            {(item.sender_name || '').charAt(0) || '?'}
          </div>
          <div>
            <div className="text-white/80">
              {item.sender_name}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'recipient',
      label: t.recipient,
      render: (item: PersonalMessage) => (
        <div className="flex items-center gap-1">
          <User className="h-3 w-3 text-green-400" />
          <span className="text-white/80">
            {item.recipient_name}
          </span>
        </div>
      )
    },
    {
      key: 'is_important',
      label: language === 'ar' ? 'مهم' : 'Important',
      sortable: true,
      render: (item: PersonalMessage) => (
        <div className="flex items-center gap-1">
          {item.is_important ? (
            <Star className="h-4 w-4 text-yellow-400 fill-current" />
          ) : (
            <Star className="h-4 w-4 text-gray-400" />
          )}
          <span className="text-white/80">
            {item.is_important ? (language === 'ar' ? 'مهم' : 'Important') : (language === 'ar' ? 'عادي' : 'Normal')}
          </span>
        </div>
      )
    },
    {
      key: 'is_read',
      label: language === 'ar' ? 'الحالة' : 'Status',
      sortable: true,
      render: (item: PersonalMessage) => (
        <div className="flex items-center gap-1">
          {item.is_read ? (
            <CheckCircle className="h-4 w-4 text-green-400" />
          ) : (
            <Circle className="h-4 w-4 text-blue-400" />
          )}
          <span className="text-white/80">
            {item.is_read ? (language === 'ar' ? 'مقروء' : 'Read') : (language === 'ar' ? 'غير مقروء' : 'Unread')}
          </span>
        </div>
      )
    },
    {
      key: 'sent_at',
      label: language === 'ar' ? 'تاريخ الإرسال' : 'Sent Date',
      sortable: true,
      render: (item: PersonalMessage) => (
        <div className="flex items-center gap-1">
          <Clock className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">
            {new Date(item.sent_at).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}
          </span>
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: React.MouseEvent) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: React.MouseEvent) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: React.MouseEvent) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'messageType',
      label: t.messageType,
      options: [
        { label: t.direct, value: 'direct' },
        { label: t.group, value: 'group' },
        { label: t.broadcast, value: 'broadcast' }
      ]
    },
    {
      key: 'priority',
      label: t.priority,
      options: [
        { label: t.urgent, value: 'urgent' },
        { label: t.high, value: 'high' },
        { label: t.medium, value: 'medium' },
        { label: t.low, value: 'low' }
      ]
    },
    {
      key: 'status',
      label: t.status,
      options: [
        { label: t.sent, value: 'sent' },
        { label: t.delivered, value: 'delivered' },
        { label: t.read, value: 'read' },
        { label: t.archived, value: 'archived' }
      ]
    }
  ]

  // FIXED: Form fields configuration to match actual Message model
  const formFields: FormField[] = [
    {
      name: 'subject',
      label: t.subject,
      type: 'text',
      required: true,
      placeholder: language === 'ar' ? 'أدخل موضوع الرسالة' : 'Enter message subject'
    },
    {
      name: 'content',
      label: t.content,
      type: 'textarea',
      required: true,
      placeholder: language === 'ar' ? 'أدخل محتوى الرسالة' : 'Enter message content'
    },
    {
      name: 'recipient',
      label: t.recipient,
      type: 'select', // IMPROVED: Changed to select dropdown
      required: true,
      placeholder: language === 'ar' ? 'اختر المستقبل' : 'Select recipient',
      options: [
        { label: 'Admin User', value: '1' },
        { label: 'John Doe (Current User)', value: '35' },
        // TODO: Fetch from API in production
      ]
    },
    {
      name: 'is_important',
      label: language === 'ar' ? 'مهم' : 'Important',
      type: 'checkbox'
    }
  ]

  // Event handlers
  const handleCreate = (): void => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = (): void => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<PersonalMessage>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async () => {
    try {
      await exportData('csv')
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.personalMessages}
        data={messages}
        columns={columns}
        actions={actions as any}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addMessage : modalMode === 'edit' ? t.editMessage : t.view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
