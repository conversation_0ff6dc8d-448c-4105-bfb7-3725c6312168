import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Badge } from '../../components/ui/badge'
import {
  Package,
  Plus,
  Search,
  Eye,
  Edit,
  Trash2,
  Star,
  ShoppingCart,
  TrendingUp,
  DollarSign,
  BarChart3,
  Image
} from 'lucide-react'

interface ProductCatalogProps {
  language: 'ar' | 'en'
}

interface Product {
  id: string
  name: string
  nameAr: string
  description: string
  descriptionAr: string
  sku: string
  category: string
  categoryAr: string
  brand: string
  brandAr: string
  price: number
  costPrice: number
  stockQuantity: number
  minStockLevel: number
  status: 'active' | 'inactive' | 'discontinued' | 'out-of-stock'
  rating: number
  reviewsCount: number
  salesCount: number
  imageUrl?: string
  tags: string[]
  tagsAr: string[]
  weight?: number
  dimensions?: {
    length: number
    width: number
    height: number
  }
  supplier: string
  supplierAr: string
  createdDate: string
  lastUpdated: string
}

const mockProducts: Product[] = [
  {
    id: '1',
    name: 'Wireless Bluetooth Headphones',
    nameAr: 'سماعات بلوتوث لاسلكية',
    description: 'High-quality wireless headphones with noise cancellation',
    descriptionAr: 'سماعات لاسلكية عالية الجودة مع إلغاء الضوضاء',
    sku: 'WBH-001',
    category: 'Electronics',
    categoryAr: 'الإلكترونيات',
    brand: 'TechSound',
    brandAr: 'تك ساوند',
    price: 299,
    costPrice: 180,
    stockQuantity: 150,
    minStockLevel: 20,
    status: 'active',
    rating: 4.5,
    reviewsCount: 128,
    salesCount: 450,
    tags: ['wireless', 'bluetooth', 'noise-cancelling'],
    tagsAr: ['لاسلكي', 'بلوتوث', 'إلغاء الضوضاء'],
    weight: 0.3,
    dimensions: { length: 20, width: 18, height: 8 },
    supplier: 'Tech Supplies Co.',
    supplierAr: 'شركة التوريدات التقنية',
    createdDate: '2024-01-15',
    lastUpdated: '2024-01-20'
  },
  {
    id: '2',
    name: 'Smart Fitness Watch',
    nameAr: 'ساعة ذكية للياقة البدنية',
    description: 'Advanced fitness tracking with heart rate monitor',
    descriptionAr: 'تتبع متقدم للياقة البدنية مع مراقب معدل ضربات القلب',
    sku: 'SFW-002',
    category: 'Wearables',
    categoryAr: 'الأجهزة القابلة للارتداء',
    brand: 'FitTech',
    brandAr: 'فيت تك',
    price: 199,
    costPrice: 120,
    stockQuantity: 75,
    minStockLevel: 15,
    status: 'active',
    rating: 4.2,
    reviewsCount: 89,
    salesCount: 320,
    tags: ['fitness', 'smartwatch', 'health'],
    tagsAr: ['لياقة', 'ساعة ذكية', 'صحة'],
    weight: 0.05,
    dimensions: { length: 4, width: 4, height: 1 },
    supplier: 'Wearable Tech Ltd.',
    supplierAr: 'شركة التقنيات القابلة للارتداء المحدودة',
    createdDate: '2024-01-10',
    lastUpdated: '2024-01-18'
  },
  {
    id: '3',
    name: 'Ergonomic Office Chair',
    nameAr: 'كرسي مكتب مريح',
    description: 'Comfortable ergonomic chair for office use',
    descriptionAr: 'كرسي مريح ومصمم هندسياً للاستخدام المكتبي',
    sku: 'EOC-003',
    category: 'Furniture',
    categoryAr: 'الأثاث',
    brand: 'ComfortSeating',
    brandAr: 'كومفورت سيتنج',
    price: 450,
    costPrice: 280,
    stockQuantity: 25,
    minStockLevel: 5,
    status: 'active',
    rating: 4.7,
    reviewsCount: 156,
    salesCount: 180,
    tags: ['office', 'ergonomic', 'furniture'],
    tagsAr: ['مكتب', 'مريح', 'أثاث'],
    weight: 15,
    dimensions: { length: 60, width: 60, height: 120 },
    supplier: 'Office Furniture Pro',
    supplierAr: 'برو أثاث المكاتب',
    createdDate: '2024-01-05',
    lastUpdated: '2024-01-22'
  },
  {
    id: '4',
    name: 'Portable Power Bank',
    nameAr: 'بنك طاقة محمول',
    description: '20000mAh portable charger with fast charging',
    descriptionAr: 'شاحن محمول 20000 مللي أمبير مع الشحن السريع',
    sku: 'PPB-004',
    category: 'Electronics',
    categoryAr: 'الإلكترونيات',
    brand: 'PowerMax',
    brandAr: 'باور ماكس',
    price: 89,
    costPrice: 45,
    stockQuantity: 0,
    minStockLevel: 30,
    status: 'out-of-stock',
    rating: 4.1,
    reviewsCount: 67,
    salesCount: 890,
    tags: ['portable', 'charger', 'power bank'],
    tagsAr: ['محمول', 'شاحن', 'بنك طاقة'],
    weight: 0.4,
    dimensions: { length: 15, width: 7, height: 2 },
    supplier: 'Mobile Accessories Inc.',
    supplierAr: 'شركة إكسسوارات الجوال',
    createdDate: '2023-12-20',
    lastUpdated: '2024-01-25'
  }
]

export default function ProductCatalog({ language }: ProductCatalogProps) {
  const [products, setProducts] = useState<Product[]>(mockProducts)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [categoryFilter, setCategoryFilter] = useState<string>('all')

  const text = {
    ar: {
      title: 'كتالوج المنتجات',
      description: 'إدارة ومتابعة كتالوج المنتجات والمخزون',
      newProduct: 'منتج جديد',
      search: 'البحث في المنتجات...',
      productName: 'اسم المنتج',
      sku: 'رمز المنتج',
      category: 'الفئة',
      brand: 'العلامة التجارية',
      price: 'السعر',
      stock: 'المخزون',
      status: 'الحالة',
      rating: 'التقييم',
      sales: 'المبيعات',
      actions: 'الإجراءات',
      view: 'عرض',
      edit: 'تعديل',
      delete: 'حذف',
      totalProducts: 'إجمالي المنتجات',
      activeProducts: 'المنتجات النشطة',
      totalValue: 'إجمالي القيمة',
      lowStock: 'مخزون منخفض',
      active: 'نشط',
      inactive: 'غير نشط',
      discontinued: 'متوقف',
      'out-of-stock': 'نفد المخزون',
      sar: 'ر.س',
      reviews: 'تقييم',
      sold: 'مباع',
      inStock: 'متوفر',
      lowStockWarning: 'مخزون منخفض'
    },
    en: {
      title: 'Product Catalog',
      description: 'Manage and track product catalog and inventory',
      newProduct: 'New Product',
      search: 'Search products...',
      productName: 'Product Name',
      sku: 'SKU',
      category: 'Category',
      brand: 'Brand',
      price: 'Price',
      stock: 'Stock',
      status: 'Status',
      rating: 'Rating',
      sales: 'Sales',
      actions: 'Actions',
      view: 'View',
      edit: 'Edit',
      delete: 'Delete',
      totalProducts: 'Total Products',
      activeProducts: 'Active Products',
      totalValue: 'Total Value',
      lowStock: 'Low Stock',
      active: 'Active',
      inactive: 'Inactive',
      discontinued: 'Discontinued',
      'out-of-stock': 'Out of Stock',
      sar: 'SAR',
      reviews: 'Reviews',
      sold: 'Sold',
      inStock: 'In Stock',
      lowStockWarning: 'Low Stock'
    }
  }

  const t = text[language]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-500/20 text-green-300 border-green-500/30'
      case 'inactive': return 'bg-gray-500/20 text-gray-300 border-gray-500/30'
      case 'discontinued': return 'bg-red-500/20 text-red-300 border-red-500/30'
      case 'out-of-stock': return 'bg-orange-500/20 text-orange-300 border-orange-500/30'
      default: return 'bg-gray-500/20 text-gray-300 border-gray-500/30'
    }
  }

  const getRatingStars = (rating: number) => {
    const stars = []
    const fullStars = Math.floor(rating)
    const hasHalfStar = rating % 1 !== 0

    for (let i = 0; i < fullStars; i++) {
      stars.push(<Star key={i} className="w-3 h-3 fill-yellow-400 text-yellow-400" />)
    }

    if (hasHalfStar) {
      stars.push(<Star key="half" className="w-3 h-3 fill-yellow-400/50 text-yellow-400" />)
    }

    const remainingStars = 5 - Math.ceil(rating)
    for (let i = 0; i < remainingStars; i++) {
      stars.push(<Star key={`empty-${i}`} className="w-3 h-3 text-gray-400" />)
    }

    return stars
  }

  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.nameAr.includes(searchTerm) ||
                         product.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.brand.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.brandAr.includes(searchTerm)
    const matchesStatus = statusFilter === 'all' || product.status === statusFilter
    const matchesCategory = categoryFilter === 'all' || product.category === categoryFilter
    return matchesSearch && matchesStatus && matchesCategory
  })

  const activeProducts = products.filter(product => product.status === 'active').length
  const totalValue = products.reduce((sum, product) => sum + (product.price * product.stockQuantity), 0)
  const lowStockProducts = products.filter(product => product.stockQuantity <= product.minStockLevel).length

  return (
    <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">{t.title}</h1>
            <p className="text-white/70">{t.description}</p>
          </div>
          <Button className="bg-gradient-to-r from-sky-500 to-blue-600 hover:from-sky-600 hover:to-blue-700">
            <Plus className="w-4 h-4 mr-2" />
            {t.newProduct}
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="modern-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.totalProducts}</p>
                  <p className="text-2xl font-bold text-white">{products.length}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-primary/20 flex items-center justify-center">
                  <Package className="w-6 h-6 text-primary" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="modern-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.activeProducts}</p>
                  <p className="text-2xl font-bold text-white">{activeProducts}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-secondary/20 flex items-center justify-center">
                  <TrendingUp className="w-6 h-6 text-secondary" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="modern-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.totalValue}</p>
                  <p className="text-2xl font-bold text-white">{totalValue.toLocaleString()} {t.sar}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-accent/20 flex items-center justify-center">
                  <DollarSign className="w-6 h-6 text-accent" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="modern-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.lowStock}</p>
                  <p className="text-2xl font-bold text-white">{lowStockProducts}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-muted/20 flex items-center justify-center">
                  <BarChart3 className="w-6 h-6 text-muted-foreground" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card className="modern-card">
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 w-4 h-4" />
                  <Input
                    placeholder={t.search}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 glass-input"
                  />
                </div>
              </div>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="glass-input px-3 py-2 rounded-lg"
              >
                <option value="all">جميع الحالات</option>
                <option value="active">{t.active}</option>
                <option value="inactive">{t.inactive}</option>
                <option value="discontinued">{t.discontinued}</option>
                <option value="out-of-stock">{t['out-of-stock']}</option>
              </select>
              <select
                value={categoryFilter}
                onChange={(e) => setCategoryFilter(e.target.value)}
                className="glass-input px-3 py-2 rounded-lg"
              >
                <option value="all">جميع الفئات</option>
                <option value="Electronics">الإلكترونيات</option>
                <option value="Wearables">الأجهزة القابلة للارتداء</option>
                <option value="Furniture">الأثاث</option>
                <option value="Accessories">الإكسسوارات</option>
              </select>
            </div>
          </CardContent>
        </Card>

        {/* Products Table */}
        <Card className="modern-card">
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-white/10">
                    <th className="text-right p-4 text-white/70 font-medium">{t.productName}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.sku}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.category}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.price}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.stock}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.status}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.rating}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.actions}</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredProducts.map((product) => (
                    <tr key={product.id} className="border-b border-white/5 hover:bg-white/5">
                      <td className="p-4">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 rounded-lg bg-white/10 flex items-center justify-center">
                            {product.imageUrl ? (
                              <img src={product.imageUrl} alt={product.name} className="w-8 h-8 rounded object-cover" />
                            ) : (
                              <Image className="w-5 h-5 text-white/50" />
                            )}
                          </div>
                          <div>
                            <span className="text-white font-medium">
                              {language === 'ar' ? product.nameAr : product.name}
                            </span>
                            <div className="text-xs text-white/60 mt-1">
                              {language === 'ar' ? product.brandAr : product.brand}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <span className="text-white font-mono">{product.sku}</span>
                      </td>
                      <td className="p-4">
                        <span className="text-white">{language === 'ar' ? product.categoryAr : product.category}</span>
                      </td>
                      <td className="p-4">
                        <span className="text-white">{product.price.toLocaleString()} {t.sar}</span>
                      </td>
                      <td className="p-4">
                        <div>
                          <span className="text-white">{product.stockQuantity}</span>
                          {product.stockQuantity <= product.minStockLevel && (
                            <div className="text-xs text-orange-400 mt-1">{t.lowStockWarning}</div>
                          )}
                        </div>
                      </td>
                      <td className="p-4">
                        <Badge className={`${getStatusColor(product.status)} border`}>
                          {t[product.status as keyof typeof t]}
                        </Badge>
                      </td>
                      <td className="p-4">
                        <div className="flex items-center gap-2">
                          <div className="flex">
                            {getRatingStars(product.rating)}
                          </div>
                          <span className="text-white text-sm">{product.rating}</span>
                          <span className="text-white/50 text-xs">({product.reviewsCount})</span>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="flex gap-2">
                          <Button size="sm" variant="ghost" className="text-white/70 hover:text-white">
                            <Eye className="w-4 h-4" />
                          </Button>
                          <Button size="sm" variant="ghost" className="text-white/70 hover:text-white">
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button size="sm" variant="ghost" className="text-red-400 hover:text-red-300">
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
    </div>
  )
}
