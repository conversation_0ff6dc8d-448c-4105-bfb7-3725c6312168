/**
 * Products Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  Package,
  Star,
  AlertTriangle,
  CheckCircle,
  DollarSign,
  ShoppingCart,
  Eye,
  Edit,
  Trash2,
  Barcode,
  Tag
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { productService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface ProductsProps {
  language: 'ar' | 'en'
}

interface Product {
  id: number
  name: string
  name_ar?: string
  sku: string
  description?: string
  description_ar?: string
  category: number
  category_name?: string
  brand?: string
  brand_ar?: string
  unit_price: number
  cost_price?: number
  quantity_in_stock: number
  minimum_stock_level: number
  maximum_stock_level?: number
  reorder_point: number
  unit_of_measure: string
  unit_of_measure_ar?: string
  barcode?: string
  weight?: number
  dimensions?: string
  status: 'active' | 'inactive' | 'discontinued'
  supplier?: number
  supplier_name?: string
  location?: string
  location_ar?: string
  expiry_date?: string
  batch_number?: string
  created_at: string
  updated_at: string
}

const translations = {
  ar: {
    products: 'المنتجات',
    addProduct: 'إضافة منتج',
    editProduct: 'تعديل المنتج',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذا المنتج؟',
    searchPlaceholder: 'البحث في المنتجات...',
    productName: 'اسم المنتج',
    description: 'الوصف',
    sku: 'رمز المنتج',
    category: 'الفئة',
    price: 'السعر',
    cost: 'التكلفة',
    stock: 'المخزون',
    sold: 'المباع',
    rating: 'التقييم',
    status: 'الحالة',
    weight: 'الوزن',
    dimensions: 'الأبعاد',
    brand: 'العلامة التجارية',
    active: 'نشط',
    inactive: 'غير نشط',
    outOfStock: 'نفد المخزون',
    categories: {
      electronics: 'إلكترونيات',
      clothing: 'ملابس',
      books: 'كتب',
      home: 'منزل',
      sports: 'رياضة',
      food: 'طعام',
      beauty: 'جمال',
      automotive: 'سيارات'
    }
  },
  en: {
    products: 'Products',
    addProduct: 'Add Product',
    editProduct: 'Edit Product',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this product?',
    searchPlaceholder: 'Search products...',
    productName: 'Product Name',
    description: 'Description',
    sku: 'SKU',
    category: 'Category',
    price: 'Price',
    cost: 'Cost',
    stock: 'Stock',
    sold: 'Sold',
    rating: 'Rating',
    status: 'Status',
    weight: 'Weight',
    dimensions: 'Dimensions',
    brand: 'Brand',
    active: 'Active',
    inactive: 'Inactive',
    outOfStock: 'Out of Stock',
    categories: {
      electronics: 'Electronics',
      clothing: 'Clothing',
      books: 'Books',
      home: 'Home',
      sports: 'Sports',
      food: 'Food',
      beauty: 'Beauty',
      automotive: 'Automotive'
    }
  }
}

export default function Products({ language }: ProductsProps): React.ReactElement {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: products,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<Product>({
    service: productService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getStatusColor = (status: string): void => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'inactive':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      case 'outOfStock':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStockStatus = (stock: number, minLevel: number = 10): void => {
    if (stock === 0) return { color: 'text-red-400', icon: <AlertTriangle className="h-3 w-3" /> }
    if (stock <= minLevel) return { color: 'text-yellow-400', icon: <AlertTriangle className="h-3 w-3" /> }
    return { color: 'text-green-400', icon: <CheckCircle className="h-3 w-3" /> }
  }

  const getCategoryIcon = (category: string): void => {
    switch (category) {
      case 'electronics':
        return <Package className="h-4 w-4" />
      case 'clothing':
        return <Tag className="h-4 w-4" />
      case 'books':
        return <Package className="h-4 w-4" />
      default:
        return <Package className="h-4 w-4" />
    }
  }

  // Table columns configuration
  const columns: TableColumn<Product>[] = [
    {
      key: 'name',
      label: t.productName,
      sortable: true,
      render: (item: Product) => (
        <div className="flex items-center gap-2">
          {getCategoryIcon(item.category_name || 'default')}
          <div>
            <div className="font-medium text-white">
              {language === 'ar' ? (item.name_ar || item.name) : item.name}
            </div>
            <div className="text-sm text-white/60">
              {language === 'ar' ? (item.description_ar || item.description) : item.description}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'sku',
      label: t.sku,
      render: (item: Product) => (
        <div className="flex items-center gap-1">
          <Barcode className="h-3 w-3 text-blue-400" />
          <span className="text-white font-mono text-sm">{item.sku}</span>
        </div>
      )
    },
    {
      key: 'category',
      label: t.category,
      sortable: true,
      render: (item: Product) => (
        <Badge variant="outline" className="text-white border-white/20">
          {item.category_name || 'Unknown'}
        </Badge>
      )
    },
    {
      key: 'unit_price',
      label: t.price,
      sortable: true,
      render: (item: Product) => (
        <span className="text-white font-medium">
          {new Intl.NumberFormat('ar-SA', {
            style: 'currency',
            currency: 'SAR'
          }).format(item.unit_price)}
        </span>
      )
    },
    {
      key: 'quantity_in_stock',
      label: t.stock,
      sortable: true,
      render: (item: Product) => {
        const stockStatus = getStockStatus(item.quantity_in_stock, item.minimum_stock_level)
        return (
          <div className={`flex items-center gap-2 ${stockStatus.color}`}>
            {stockStatus.icon}
            <span className="font-medium">{item.quantity_in_stock}</span>
          </div>
        )
      }
    },
    {
      key: 'sold',
      label: t.sold,
      sortable: true,
      render: (item: Product) => (
        <div className="flex items-center gap-1">
          <ShoppingCart className="h-3 w-3 text-green-400" />
          <span className="text-white">{(item as any).sold || 0}</span>
        </div>
      )
    },
    {
      key: 'rating',
      label: t.rating,
      sortable: true,
      render: (item: Product) => (
        <div className="flex items-center gap-1">
          <Star className="h-3 w-3 text-yellow-400 fill-current" />
          <span className="text-white text-sm">{(item as any).rating || 0}</span>
        </div>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: Product) => (
        <Badge className={getStatusColor(item.status)}>
          {String(t[item.status as keyof typeof t])}
        </Badge>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: React.MouseEvent) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: React.MouseEvent) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: React.MouseEvent) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'category',
      label: t.category,
      options: [
        { label: t.categories.electronics, value: 'electronics' },
        { label: t.categories.clothing, value: 'clothing' },
        { label: t.categories.books, value: 'books' },
        { label: t.categories.home, value: 'home' },
        { label: t.categories.sports, value: 'sports' },
        { label: t.categories.food, value: 'food' },
        { label: t.categories.beauty, value: 'beauty' },
        { label: t.categories.automotive, value: 'automotive' }
      ]
    },
    {
      key: 'status',
      label: t.status,
      options: [
        { label: t.active, value: 'active' },
        { label: t.inactive, value: 'inactive' },
        { label: t.outOfStock, value: 'outOfStock' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'name',
      label: t.productName,
      type: 'text',
      required: true
    },
    {
      name: 'name_ar',
      label: t.productName + ' (Arabic)',
      type: 'text'
    },
    {
      name: 'sku',
      label: t.sku,
      type: 'text',
      required: true
    },
    {
      name: 'description',
      label: t.description,
      type: 'textarea'
    },
    {
      name: 'description_ar',
      label: t.description + ' (Arabic)',
      type: 'textarea'
    },
    {
      name: 'category',
      label: t.category,
      type: 'select',
      required: true,
      options: [
        { label: 'Electronics', value: '1' },
        { label: 'Clothing', value: '2' },
        { label: 'Books', value: '3' },
        { label: 'Home & Garden', value: '4' },
        { label: 'Sports', value: '5' }
      ]
    },
    {
      name: 'brand',
      label: t.brand,
      type: 'text'
    },
    {
      name: 'brand_ar',
      label: t.brand + ' (Arabic)',
      type: 'text'
    },
    {
      name: 'unit_price',
      label: t.price || 'Unit Price',
      type: 'number',
      required: true,
      min: 0,
      step: 0.01
    },
    {
      name: 'cost_price',
      label: t.cost || 'Cost Price',
      type: 'number',
      min: 0,
      step: 0.01
    },
    {
      name: 'quantity_in_stock',
      label: t.stock || 'Stock Quantity',
      type: 'number',
      required: true,
      min: 0
    },
    {
      name: 'minimum_stock_level',
      label: 'Minimum Stock Level',
      type: 'number',
      min: 0
    },
    {
      name: 'reorder_point',
      label: 'Reorder Point',
      type: 'number',
      min: 0
    },
    {
      name: 'unit_of_measure',
      label: 'Unit of Measure',
      type: 'text'
    },
    {
      name: 'barcode',
      label: 'Barcode',
      type: 'text'
    },
    {
      name: 'weight',
      label: t.weight,
      type: 'number',
      min: 0,
      step: 0.01
    },
    {
      name: 'dimensions',
      label: t.dimensions,
      type: 'text'
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { label: t.active || 'Active', value: 'active' },
        { label: t.inactive || 'Inactive', value: 'inactive' },
        { label: 'Discontinued', value: 'discontinued' }
      ]
    }
  ]

  // Event handlers
  const handleCreate = (): void => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = (): void => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<Product>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async () => {
    try {
      await exportData('csv')
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.products}
        data={products}
        columns={columns}
        actions={actions as any}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addProduct : modalMode === 'edit' ? t.editProduct : t.view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
