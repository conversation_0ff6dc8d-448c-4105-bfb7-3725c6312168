import { useState, useEffect } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Package,
  Plus,
  Search,
  Filter,
  Download,
  Eye,
  Edit,
  Star,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  BarChart3,
  DollarSign,
  ShoppingCart,
  MoreHorizontal
} from 'lucide-react'

interface ProductsProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    title: 'إدارة المنتجات',
    subtitle: 'إدارة شاملة للمنتجات والخدمات والمخزون',
    totalProducts: 'إجمالي المنتجات',
    activeProducts: 'منتجات نشطة',
    lowStock: 'مخزون منخفض',
    totalValue: 'إجمالي القيمة',
    searchProducts: 'البحث في المنتجات...',
    addProduct: 'إضافة منتج',
    exportData: 'تصدير البيانات',
    filterBy: 'تصفية حسب',
    category: 'الفئة',
    status: 'الحالة',
    productList: 'قائمة المنتجات',
    productName: 'اسم المنتج',
    sku: 'رمز المنتج',
    price: 'السعر',
    stock: 'المخزون',
    sold: 'المباع',
    rating: 'التقييم',
    actions: 'الإجراءات',
    viewProduct: 'عرض المنتج',
    editProduct: 'تعديل المنتج',
    active: 'نشط',
    inactive: 'غير نشط',
    outOfStock: 'نفد المخزون',
    inStock: 'متوفر',
    electronics: 'إلكترونيات',
    clothing: 'ملابس',
    books: 'كتب',
    home: 'منزل',
    sports: 'رياضة',
    refresh: 'تحديث',
    productAnalytics: 'تحليلات المنتجات',
    topSelling: 'الأكثر مبيعاً',
    newProducts: 'منتجات جديدة'
  },
  en: {
    title: 'Product Management',
    subtitle: 'Comprehensive product, service, and inventory management',
    totalProducts: 'Total Products',
    activeProducts: 'Active Products',
    lowStock: 'Low Stock',
    totalValue: 'Total Value',
    searchProducts: 'Search products...',
    addProduct: 'Add Product',
    exportData: 'Export Data',
    filterBy: 'Filter By',
    category: 'Category',
    status: 'Status',
    productList: 'Product List',
    productName: 'Product Name',
    sku: 'SKU',
    price: 'Price',
    stock: 'Stock',
    sold: 'Sold',
    rating: 'Rating',
    actions: 'Actions',
    viewProduct: 'View Product',
    editProduct: 'Edit Product',
    active: 'Active',
    inactive: 'Inactive',
    outOfStock: 'Out of Stock',
    inStock: 'In Stock',
    electronics: 'Electronics',
    clothing: 'Clothing',
    books: 'Books',
    home: 'Home',
    sports: 'Sports',
    refresh: 'Refresh',
    productAnalytics: 'Product Analytics',
    topSelling: 'Top Selling',
    newProducts: 'New Products'
  }
}

export default function Products({ language }: ProductsProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('')
  const [refreshing, setRefreshing] = useState(false)
  const t = translations[language]
  const isRTL = language === 'ar'

  // Product metrics
  const [productMetrics, setProductMetrics] = useState({
    totalProducts: 1247,
    activeProducts: 1142,
    lowStock: 23,
    totalValue: 2850000
  })

  // Sample product data
  const [products] = useState([
    {
      id: 1,
      name: 'لابتوب ديل XPS 13',
      nameEn: 'Dell XPS 13 Laptop',
      sku: 'DELL-XPS13-001',
      category: 'electronics',
      categoryAr: 'إلكترونيات',
      price: 4500,
      stock: 25,
      sold: 145,
      rating: 4.8,
      status: 'active',
      image: '/products/laptop.jpg',
      description: 'لابتوب عالي الأداء للمحترفين',
      descriptionEn: 'High-performance laptop for professionals'
    },
    {
      id: 2,
      name: 'قميص قطني أزرق',
      nameEn: 'Blue Cotton Shirt',
      sku: 'SHIRT-BLUE-001',
      category: 'clothing',
      categoryAr: 'ملابس',
      price: 120,
      stock: 5,
      sold: 89,
      rating: 4.2,
      status: 'active',
      image: '/products/shirt.jpg',
      description: 'قميص قطني عالي الجودة',
      descriptionEn: 'High-quality cotton shirt'
    },
    {
      id: 3,
      name: 'كتاب إدارة الأعمال',
      nameEn: 'Business Management Book',
      sku: 'BOOK-BM-001',
      category: 'books',
      categoryAr: 'كتب',
      price: 85,
      stock: 0,
      sold: 234,
      rating: 4.6,
      status: 'outOfStock',
      image: '/products/book.jpg',
      description: 'دليل شامل لإدارة الأعمال',
      descriptionEn: 'Comprehensive business management guide'
    },
    {
      id: 4,
      name: 'كرة قدم احترافية',
      nameEn: 'Professional Football',
      sku: 'SPORT-FB-001',
      category: 'sports',
      categoryAr: 'رياضة',
      price: 150,
      stock: 45,
      sold: 67,
      rating: 4.4,
      status: 'active',
      image: '/products/football.jpg',
      description: 'كرة قدم احترافية معتمدة',
      descriptionEn: 'Certified professional football'
    }
  ])

  const handleRefresh = async () => {
    setRefreshing(true)
    await new Promise(resolve => setTimeout(resolve, 1000))
    setRefreshing(false)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-500/20 text-green-400 border-green-500/30'
      case 'inactive':
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30'
      case 'outOfStock':
        return 'bg-red-500/20 text-red-400 border-red-500/30'
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30'
    }
  }

  const getStockStatus = (stock: number) => {
    if (stock === 0) return { color: 'text-red-400', icon: <AlertTriangle className="h-4 w-4" /> }
    if (stock < 10) return { color: 'text-yellow-400', icon: <AlertTriangle className="h-4 w-4" /> }
    return { color: 'text-green-400', icon: <CheckCircle className="h-4 w-4" /> }
  }

  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.nameEn.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.sku.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = !selectedCategory || product.category === selectedCategory
    const matchesStatus = !selectedStatus || product.status === selectedStatus
    
    return matchesSearch && matchesCategory && matchesStatus
  })

  const productMetricsCards = [
    {
      title: t.totalProducts,
      value: productMetrics.totalProducts.toLocaleString(),
      change: '+12.5%',
      trend: 'up',
      icon: Package,
      color: 'from-blue-500 to-blue-600'
    },
    {
      title: t.activeProducts,
      value: productMetrics.activeProducts.toLocaleString(),
      change: '+8.3%',
      trend: 'up',
      icon: CheckCircle,
      color: 'from-green-500 to-green-600'
    },
    {
      title: t.lowStock,
      value: productMetrics.lowStock.toString(),
      change: '-5',
      trend: 'down',
      icon: AlertTriangle,
      color: 'from-red-500 to-red-600'
    },
    {
      title: t.totalValue,
      value: new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR',
        notation: 'compact'
      }).format(productMetrics.totalValue),
      change: '+15.7%',
      trend: 'up',
      icon: DollarSign,
      color: 'from-purple-500 to-purple-600'
    }
  ]

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">{t.title}</h1>
          <p className="text-white/70">{t.subtitle}</p>
        </div>
        
        <div className="flex items-center gap-3">
          <Button
            onClick={handleRefresh}
            disabled={refreshing}
            className="text-white/80 hover:text-white hover:bg-white/15 rounded-xl p-3 backdrop-blur-10"
          >
            <BarChart3 className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            {t.refresh}
          </Button>
          <Button className="text-white/80 hover:text-white hover:bg-white/15 rounded-xl p-3 backdrop-blur-10">
            <Plus className="h-4 w-4 mr-2" />
            {t.addProduct}
          </Button>
          <Button variant="outline" className="text-white/80 hover:text-white hover:bg-white/15 rounded-xl p-3 backdrop-blur-10">
            <Download className="h-4 w-4 mr-2" />
            {t.exportData}
          </Button>
        </div>
      </div>

      {/* Product Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {productMetricsCards.map((card, index) => (
          <Card key={index} className="glass-card border-white/20 hover:border-white/40 transition-all">
            <CardContent className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <p className="text-white/70 text-sm mb-1">{card.title}</p>
                  <p className="text-2xl font-bold text-white">{card.value}</p>
                </div>
                <div className={`p-3 rounded-xl bg-gradient-to-r ${card.color} shadow-lg`}>
                  <card.icon className="h-6 w-6 text-white" />
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <span className={`text-sm font-medium ${
                  card.trend === 'up' ? 'text-green-400' : 
                  card.trend === 'down' ? 'text-red-400' : 'text-blue-400'
                }`}>
                  {card.change}
                </span>
                <span className="text-xs text-white/60">vs last month</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Search and Filters */}
      <Card className="glass-card border-white/20">
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/60" />
              <Input
                placeholder={t.searchProducts}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-white/10 border-white/20 text-white placeholder:text-white/60"
              />
            </div>
            
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="bg-white/10 border-white/20 text-white rounded-lg px-3 py-2"
            >
              <option value="">{t.category}</option>
              <option value="electronics">{t.electronics}</option>
              <option value="clothing">{t.clothing}</option>
              <option value="books">{t.books}</option>
              <option value="home">{t.home}</option>
              <option value="sports">{t.sports}</option>
            </select>
            
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="bg-white/10 border-white/20 text-white rounded-lg px-3 py-2"
            >
              <option value="">{t.status}</option>
              <option value="active">{t.active}</option>
              <option value="inactive">{t.inactive}</option>
              <option value="outOfStock">{t.outOfStock}</option>
            </select>
            
            <Button variant="outline" className="text-white/80 hover:text-white hover:bg-white/15 rounded-xl p-3 backdrop-blur-10">
              <Filter className="h-4 w-4 mr-2" />
              {t.filterBy}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Product List */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white text-xl">{t.productList}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-white/20">
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.productName}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.sku}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.category}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.price}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.stock}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.sold}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.rating}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.status}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.actions}</th>
                </tr>
              </thead>
              <tbody>
                {filteredProducts.map((product) => {
                  const stockStatus = getStockStatus(product.stock)
                  return (
                    <tr key={product.id} className="border-b border-white/10 hover:bg-white/5 transition-colors">
                      <td className="py-4 px-4">
                        <div className="flex items-center gap-3">
                          <div className="w-12 h-12 rounded-lg bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-white font-bold">
                            <Package className="h-6 w-6" />
                          </div>
                          <div>
                            <p className="text-white font-medium">
                              {language === 'ar' ? product.name : product.nameEn}
                            </p>
                            <p className="text-white/60 text-sm">
                              {language === 'ar' ? product.description : product.descriptionEn}
                            </p>
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-4 text-white font-mono text-sm">{product.sku}</td>
                      <td className="py-4 px-4 text-white">
                        {language === 'ar' ? product.categoryAr : t[product.category as keyof typeof t]}
                      </td>
                      <td className="py-4 px-4 text-white font-medium">
                        {new Intl.NumberFormat('ar-SA', {
                          style: 'currency',
                          currency: 'SAR'
                        }).format(product.price)}
                      </td>
                      <td className="py-4 px-4">
                        <div className={`flex items-center gap-2 ${stockStatus.color}`}>
                          {stockStatus.icon}
                          <span className="font-medium">{product.stock}</span>
                        </div>
                      </td>
                      <td className="py-4 px-4 text-white">{product.sold}</td>
                      <td className="py-4 px-4">
                        <div className="flex items-center gap-1">
                          <Star className="h-4 w-4 text-yellow-400 fill-current" />
                          <span className="text-white text-sm">{product.rating}</span>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(product.status)}`}>
                          {t[product.status as keyof typeof t]}
                        </span>
                      </td>
                      <td className="py-4 px-4">
                        <div className="flex items-center gap-2">
                          <Button size="sm" variant="outline" className="text-white/80 hover:text-white hover:bg-white/15 rounded-lg p-2 backdrop-blur-10">
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button size="sm" variant="outline" className="text-white/80 hover:text-white hover:bg-white/15 rounded-lg p-2 backdrop-blur-10">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button size="sm" variant="outline" className="text-white/80 hover:text-white hover:bg-white/15 rounded-lg p-2 backdrop-blur-10">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  )
                })}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
