import { useState } from 'react'
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  FolderOpen,
  Plus,
  Search,
  Filter,
  Calendar,
  Users,
  Target,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertTriangle,
  User,
  DollarSign
} from 'lucide-react'

interface ProjectsProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    projects: 'المشاريع',
    createProject: 'إنشاء مشروع',
    projectOverview: 'نظرة عامة على المشاريع',
    search: 'بحث',
    filter: 'تصفية',
    projectName: 'اسم المشروع',
    projectManager: 'مدير المشروع',
    department: 'القسم',
    startDate: 'تاريخ البداية',
    endDate: 'تاريخ النهاية',
    progress: 'التقدم',
    status: 'الحالة',
    budget: 'الميزانية',
    teamMembers: 'أعضاء الفريق',
    actions: 'الإجراءات',
    planning: 'التخطيط',
    inProgress: 'قيد التنفيذ',
    onHold: 'معلق',
    completed: 'مكتمل',
    cancelled: 'ملغي',
    viewDetails: 'عرض التفاصيل',
    editProject: 'تعديل المشروع',
    totalProjects: 'إجمالي المشاريع',
    activeProjects: 'المشاريع النشطة',
    completedProjects: 'المشاريع المكتملة',
    overdueTasks: 'المهام المتأخرة',
    client: 'العميل',
    priority: 'الأولوية',
    low: 'منخفضة',
    medium: 'متوسطة',
    high: 'عالية',
    critical: 'حرجة',
    tasks: 'المهام',
    daysLeft: 'أيام متبقية'
  },
  en: {
    projects: 'Projects',
    createProject: 'Create Project',
    projectOverview: 'Project Overview',
    search: 'Search',
    filter: 'Filter',
    projectName: 'Project Name',
    projectManager: 'Project Manager',
    department: 'Department',
    startDate: 'Start Date',
    endDate: 'End Date',
    progress: 'Progress',
    status: 'Status',
    budget: 'Budget',
    teamMembers: 'Team Members',
    actions: 'Actions',
    planning: 'Planning',
    inProgress: 'In Progress',
    onHold: 'On Hold',
    completed: 'Completed',
    cancelled: 'Cancelled',
    viewDetails: 'View Details',
    editProject: 'Edit Project',
    totalProjects: 'Total Projects',
    activeProjects: 'Active Projects',
    completedProjects: 'Completed Projects',
    overdueTasks: 'Overdue Tasks',
    client: 'Client',
    priority: 'Priority',
    low: 'Low',
    medium: 'Medium',
    high: 'High',
    critical: 'Critical',
    tasks: 'Tasks',
    daysLeft: 'Days Left'
  }
}

export default function Projects({ language }: ProjectsProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('all')
  const t = translations[language]
  const isRTL = language === 'ar'

  // Mock data
  const projectStats = {
    totalProjects: 24,
    activeProjects: 18,
    completedProjects: 6,
    overdueTasks: 12
  }

  const projects = [
    {
      id: 1,
      name: 'نظام إدارة المخزون',
      nameEn: 'Inventory Management System',
      manager: 'أحمد محمد',
      department: 'تقنية المعلومات',
      client: 'شركة التجارة المتقدمة',
      startDate: '2024-01-01',
      endDate: '2024-06-30',
      progress: 75,
      status: 'inProgress',
      priority: 'high',
      budget: 150000,
      teamMembers: 8,
      tasksCount: 45,
      completedTasks: 34
    },
    {
      id: 2,
      name: 'تطوير تطبيق الموبايل',
      nameEn: 'Mobile App Development',
      manager: 'فاطمة علي',
      department: 'تقنية المعلومات',
      client: 'شركة الخدمات الذكية',
      startDate: '2024-02-15',
      endDate: '2024-08-15',
      progress: 45,
      status: 'inProgress',
      priority: 'medium',
      budget: 200000,
      teamMembers: 6,
      tasksCount: 32,
      completedTasks: 14
    },
    {
      id: 3,
      name: 'تحديث نظام المحاسبة',
      nameEn: 'Accounting System Update',
      manager: 'محمد حسن',
      department: 'المالية',
      client: 'داخلي',
      startDate: '2023-10-01',
      endDate: '2024-01-31',
      progress: 100,
      status: 'completed',
      priority: 'high',
      budget: 80000,
      teamMembers: 4,
      tasksCount: 28,
      completedTasks: 28
    },
    {
      id: 4,
      name: 'حملة التسويق الرقمي',
      nameEn: 'Digital Marketing Campaign',
      manager: 'سارة أحمد',
      department: 'التسويق',
      client: 'شركة المنتجات الاستهلاكية',
      startDate: '2024-03-01',
      endDate: '2024-05-31',
      progress: 20,
      status: 'planning',
      priority: 'medium',
      budget: 50000,
      teamMembers: 5,
      tasksCount: 18,
      completedTasks: 3
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'planning':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      case 'inProgress':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'onHold':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low':
        return 'text-green-400'
      case 'medium':
        return 'text-yellow-400'
      case 'high':
        return 'text-orange-400'
      case 'critical':
        return 'text-red-400'
      default:
        return 'text-gray-400'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'planning':
        return <Target className="h-4 w-4 text-gray-500" />
      case 'inProgress':
        return <Clock className="h-4 w-4 text-blue-500" />
      case 'onHold':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'cancelled':
        return <AlertTriangle className="h-4 w-4 text-red-500" />
      default:
        return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount)
  }

  const calculateDaysLeft = (endDate: string) => {
    const end = new Date(endDate)
    const today = new Date()
    const diffTime = end.getTime() - today.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">{t.projects}</h1>
          <p className="text-white/70">إدارة ومتابعة المشاريع والمهام</p>
        </div>
        <Button className="glass-button">
          <Plus className="h-4 w-4 mr-2" />
          {t.createProject}
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.totalProjects}</p>
                <p className="text-2xl font-bold text-white">{projectStats.totalProjects}</p>
              </div>
              <FolderOpen className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.activeProjects}</p>
                <p className="text-2xl font-bold text-blue-400">{projectStats.activeProjects}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.completedProjects}</p>
                <p className="text-2xl font-bold text-green-400">{projectStats.completedProjects}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.overdueTasks}</p>
                <p className="text-2xl font-bold text-red-400">{projectStats.overdueTasks}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Projects List */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <CardTitle className="text-white text-xl">{t.projectOverview}</CardTitle>
              <CardDescription className="text-white/70">
                قائمة بجميع المشاريع وحالتها الحالية
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 h-4 w-4" />
                <Input
                  placeholder={t.search}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 glass-input"
                />
              </div>
              <Button variant="outline" className="glass-button">
                <Filter className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {projects.map((project) => (
              <Card key={project.id} className="glass-card border-white/10 hover:border-white/30 transition-all">
                <CardHeader className="pb-3">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <CardTitle className="text-white text-lg mb-1">{project.name}</CardTitle>
                      <CardDescription className="text-white/70 text-sm">
                        {project.client} • {project.department}
                      </CardDescription>
                    </div>
                    <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(project.status)}`}>
                      {getStatusIcon(project.status)}
                      {t[project.status as keyof typeof t]}
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Project Manager and Team */}
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-blue-400" />
                      <span className="text-white/80">{project.manager}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-green-400" />
                      <span className="text-white/80">{project.teamMembers} أعضاء</span>
                    </div>
                  </div>

                  {/* Progress Bar */}
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span className="text-white/70">{t.progress}</span>
                      <span className="text-white font-medium">{project.progress}%</span>
                    </div>
                    <div className="w-full bg-white/20 rounded-full h-2">
                      <div 
                        className="bg-gradient-to-r from-blue-500 to-green-500 h-2 rounded-full transition-all"
                        style={{ width: `${project.progress}%` }}
                      ></div>
                    </div>
                  </div>

                  {/* Tasks and Budget */}
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-white/70">{t.tasks}</p>
                      <p className="text-white font-medium">{project.completedTasks}/{project.tasksCount}</p>
                    </div>
                    <div>
                      <p className="text-white/70">{t.budget}</p>
                      <p className="text-white font-medium">{formatCurrency(project.budget)}</p>
                    </div>
                  </div>

                  {/* Timeline */}
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-purple-400" />
                      <span className="text-white/80">{project.startDate} - {project.endDate}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4 text-orange-400" />
                      <span className="text-orange-400">{calculateDaysLeft(project.endDate)} {t.daysLeft}</span>
                    </div>
                  </div>

                  {/* Priority */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="text-white/70 text-sm">{t.priority}:</span>
                      <span className={`text-sm font-medium ${getPriorityColor(project.priority)}`}>
                        {t[project.priority as keyof typeof t]}
                      </span>
                    </div>
                    <div className="flex gap-2">
                      <Button size="sm" className="glass-button">
                        {t.viewDetails}
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
