/**
 * Tasks Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState, MouseEvent, ReactElement } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  CheckSquare,
  User,
  Calendar,
  Clock,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Flag,
  FolderO<PERSON>,
  Eye,
  Edit,
  Trash2,
  BarChart3
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { taskService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface TasksProps {
  language: 'ar' | 'en'
}

interface Task {
  id: string
  title: string
  title_ar?: string
  description?: string
  description_ar?: string
  project?: string
  project_id?: string
  assigned_to?: string
  assigned_to_id?: string
  created_by?: string
  created_by_id?: string
  start_date?: string
  due_date: string
  completion_date?: string
  status: 'TODO' | 'IN_PROGRESS' | 'REVIEW' | 'COMPLETED' | 'CANCELLED'
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'
  progress: number
  estimated_hours?: number
  actual_hours?: number
  tags?: string[]
  dependencies?: string[]
  created_at: string
  updated_at: string
}

const translations = {
  ar: {
    tasks: 'المهام',
    addTask: 'إضافة مهمة',
    editTask: 'تعديل المهمة',
    searchTasks: 'البحث في المهام',
    title: 'عنوان المهمة',
    titleAr: 'العنوان بالعربية',
    description: 'الوصف',
    descriptionAr: 'الوصف بالعربية',
    project: 'المشروع',
    assignedTo: 'مكلف إلى',
    createdBy: 'أنشأ بواسطة',
    startDate: 'تاريخ البداية',
    dueDate: 'تاريخ الاستحقاق',
    completionDate: 'تاريخ الإكمال',
    status: 'الحالة',
    priority: 'الأولوية',
    progress: 'التقدم',
    estimatedHours: 'الساعات المقدرة',
    actualHours: 'الساعات الفعلية',
    tags: 'العلامات',
    dependencies: 'التبعيات',
    createdAt: 'تاريخ الإنشاء',
    actions: 'الإجراءات',
    edit: 'تعديل',
    delete: 'حذف',
    view: 'عرض',
    todo: 'للقيام',
    inProgress: 'قيد التنفيذ',
    review: 'مراجعة',
    completed: 'مكتمل',
    cancelled: 'ملغي',
    low: 'منخفضة',
    medium: 'متوسطة',
    high: 'عالية',
    urgent: 'عاجل',
    searchPlaceholder: 'البحث بالعنوان أو الوصف...',
    noTasks: 'لا يوجد مهام',
    loading: 'جاري التحميل...',
    manageTasks: 'إدارة المهام والأنشطة',
    createSuccess: 'تم إنشاء المهمة بنجاح',
    updateSuccess: 'تم تحديث المهمة بنجاح',
    deleteSuccess: 'تم حذف المهمة بنجاح',
    confirmDelete: 'هل أنت متأكد من حذف هذه المهمة؟'
  },
  en: {
    tasks: 'Tasks',
    addTask: 'Add Task',
    editTask: 'Edit Task',
    searchTasks: 'Search Tasks',
    title: 'Task Title',
    titleAr: 'Title (Arabic)',
    description: 'Description',
    descriptionAr: 'Description (Arabic)',
    project: 'Project',
    assignedTo: 'Assigned To',
    createdBy: 'Created By',
    startDate: 'Start Date',
    dueDate: 'Due Date',
    completionDate: 'Completion Date',
    status: 'Status',
    priority: 'Priority',
    progress: 'Progress',
    estimatedHours: 'Estimated Hours',
    actualHours: 'Actual Hours',
    tags: 'Tags',
    dependencies: 'Dependencies',
    createdAt: 'Created Date',
    actions: 'Actions',
    edit: 'Edit',
    delete: 'Delete',
    view: 'View',
    todo: 'To Do',
    inProgress: 'In Progress',
    review: 'Review',
    completed: 'Completed',
    cancelled: 'Cancelled',
    low: 'Low',
    medium: 'Medium',
    high: 'High',
    urgent: 'Urgent',
    searchPlaceholder: 'Search by title or description...',
    noTasks: 'No tasks found',
    loading: 'Loading...',
    manageTasks: 'Manage tasks and activities',
    createSuccess: 'Task created successfully',
    updateSuccess: 'Task updated successfully',
    deleteSuccess: 'Task deleted successfully',
    confirmDelete: 'Are you sure you want to delete this task?'
  }
}

export default function Tasks({ language }: TasksProps): React.ReactElement {
  const [showModal, setShowModal] = useState<boolean>(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: tasks,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<Task>({
    service: taskService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getStatusColor = (status: string): void => {
    switch (status.toUpperCase()) {
      case 'TODO':
        return 'bg-gray-500/20 text-gray-300'
      case 'IN_PROGRESS':
        return 'bg-blue-500/20 text-blue-300'
      case 'REVIEW':
        return 'bg-yellow-500/20 text-yellow-300'
      case 'COMPLETED':
        return 'bg-green-500/20 text-green-300'
      case 'CANCELLED':
        return 'bg-red-500/20 text-red-300'
      default:
        return 'bg-gray-500/20 text-gray-300'
    }
  }

  const getPriorityColor = (priority: string): void => {
    switch (priority.toUpperCase()) {
      case 'LOW':
        return 'text-green-400'
      case 'MEDIUM':
        return 'text-yellow-400'
      case 'HIGH':
        return 'text-orange-400'
      case 'URGENT':
        return 'text-red-400'
      default:
        return 'text-gray-400'
    }
  }

  const getStatusIcon = (status: string): void => {
    switch (status.toUpperCase()) {
      case 'TODO':
        return <CheckSquare className="h-4 w-4" />
      case 'IN_PROGRESS':
        return <Clock className="h-4 w-4" />
      case 'REVIEW':
        return <AlertTriangle className="h-4 w-4" />
      case 'COMPLETED':
        return <CheckCircle className="h-4 w-4" />
      case 'CANCELLED':
        return <XCircle className="h-4 w-4" />
      default:
        return <Clock className="h-4 w-4" />
    }
  }

  const isOverdue = (dueDate: string, status: string): void => {
    if (status === 'COMPLETED') return false
    const due = new Date(dueDate)
    const today = new Date()
    return due < today
  }

  // Table columns configuration
  const columns: TableColumn[] = [
    {
      key: 'title',
      label: t.title,
      sortable: true,
      render: (item: any) => (
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 rounded-lg bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
            <CheckSquare className="h-5 w-5 text-white" />
          </div>
          <div>
            <div className="font-medium text-white">
              {language === 'ar' && item.title_ar ? item.title_ar : item.title}
            </div>
            <div className="text-sm text-white/60">
              {language === 'ar' && item.description_ar ? item.description_ar : item.description}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      align: 'center' as const,
      render: (item: any) => (
        <div className="flex items-center justify-center gap-2">
          <Badge className={getStatusColor(item.status)}>
            {getStatusIcon(item.status)}
            <span className="ml-1">{t[item.status.toLowerCase().replace('_', '') as keyof typeof t] || item.status}</span>
          </Badge>
          {isOverdue(item.due_date, item.status) && (
            <AlertTriangle className="h-4 w-4 text-red-400" />
          )}
        </div>
      )
    },
    {
      key: 'priority',
      label: t.priority,
      sortable: true,
      align: 'center' as const,
      render: (item: any) => (
        <div className="flex items-center justify-center gap-2">
          <Flag className={`h-4 w-4 ${getPriorityColor(item.priority)}`} />
          <span className={`font-medium ${getPriorityColor(item.priority)}`}>
            {t[item.priority.toLowerCase() as keyof typeof t] || item.priority}
          </span>
        </div>
      )
    },
    {
      key: 'assigned_to',
      label: t.assignedTo,
      render: (item: any) => (
        <div className="flex items-center gap-2 text-white/90">
          <User className="h-4 w-4 text-white/60" />
          <span>{item.assigned_to || '-'}</span>
        </div>
      )
    },
    {
      key: 'project',
      label: t.project,
      render: (item: any) => (
        <div className="flex items-center gap-2 text-white/90">
          <FolderOpen className="h-4 w-4 text-white/60" />
          <span>{item.project_name || '-'}</span>
        </div>
      )
    },
    {
      key: 'progress',
      label: t.progress,
      sortable: true,
      align: 'center' as const,
      render: (item: any) => (
        <div className="flex items-center gap-2">
          <div className="w-16 bg-white/20 rounded-full h-2">
            <div
              className="bg-gradient-to-r from-blue-500 to-green-500 h-2 rounded-full transition-all"
              style={{ width: `${item.progress}%` }}
            ></div>
          </div>
          <span className="text-white text-sm font-medium">{item.progress}%</span>
        </div>
      )
    },
    {
      key: 'due_date',
      label: t.dueDate,
      sortable: true,
      render: (item: any) => (
        <div className="flex items-center gap-2">
          <Calendar className="h-4 w-4 text-white/60" />
          <span className={`text-sm ${isOverdue(item.due_date, item.status) ? 'text-red-400 font-medium' : 'text-white/90'}`}>
            {new Date(item.due_date).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}
          </span>
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: React.MouseEvent) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: React.MouseEvent) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: React.MouseEvent) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'status',
      label: t.status,
      options: [
        { value: 'TODO', label: t.todo },
        { value: 'IN_PROGRESS', label: t.inProgress },
        { value: 'REVIEW', label: t.review },
        { value: 'COMPLETED', label: t.completed },
        { value: 'CANCELLED', label: t.cancelled }
      ]
    },
    {
      key: 'priority',
      label: t.priority,
      options: [
        { value: 'LOW', label: t.low },
        { value: 'MEDIUM', label: t.medium },
        { value: 'HIGH', label: t.high },
        { value: 'URGENT', label: t.urgent }
      ]
    }
  ]

  // Form fields for modal
  const formFields: FormField[] = [
    {
      name: 'title',
      label: t.title,
      type: 'text',
      required: true,
      placeholder: 'Enter task title'
    },
    {
      name: 'title_ar',
      label: t.titleAr,
      type: 'text',
      placeholder: 'أدخل عنوان المهمة بالعربية'
    },
    {
      name: 'description',
      label: t.description,
      type: 'textarea',
      placeholder: 'Enter task description',
      rows: 3
    },
    {
      name: 'description_ar',
      label: t.descriptionAr,
      type: 'textarea',
      placeholder: 'أدخل وصف المهمة بالعربية',
      rows: 3
    },
    {
      name: 'project',
      label: t.project,
      type: 'text',
      placeholder: 'Enter project name'
    },
    {
      name: 'assigned_to',
      label: t.assignedTo,
      type: 'text',
      placeholder: 'Enter assignee name'
    },
    {
      name: 'start_date',
      label: t.startDate,
      type: 'date'
    },
    {
      name: 'due_date',
      label: t.dueDate,
      type: 'date',
      required: true
    },
    {
      name: 'estimated_hours',
      label: t.estimatedHours,
      type: 'number',
      placeholder: 'Enter estimated hours',
      min: 0
    },
    {
      name: 'actual_hours',
      label: t.actualHours,
      type: 'number',
      placeholder: 'Enter actual hours',
      min: 0
    },
    {
      name: 'progress',
      label: t.progress,
      type: 'number',
      placeholder: 'Enter progress percentage',
      min: 0,
      max: 100
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { value: 'TODO', label: t.todo },
        { value: 'IN_PROGRESS', label: t.inProgress },
        { value: 'REVIEW', label: t.review },
        { value: 'COMPLETED', label: t.completed },
        { value: 'CANCELLED', label: t.cancelled }
      ]
    },
    {
      name: 'priority',
      label: t.priority,
      type: 'select',
      required: true,
      options: [
        { value: 'LOW', label: t.low },
        { value: 'MEDIUM', label: t.medium },
        { value: 'HIGH', label: t.high },
        { value: 'URGENT', label: t.urgent }
      ]
    }
  ]

  // Event handlers
  const handleCreate = (): void => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = (): void => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<Task>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
      selectItem(null)
    } catch (error) {
      // Error is handled by the CRUD hook
      console.error('Save error:', error)
    }
  }

  const handleExport = async () => {
    try {
      await exportData('csv')
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className="space-y-6">
      {/* CRUD Table */}
      <CrudTable
        title={t.tasks}
        data={tasks}
        columns={columns as unknown}
        actions={actions as unknown}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addTask : modalMode === 'edit' ? t.editTask : t.view}
        fields={formFields}
        initialData={selectedItem as unknown}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
