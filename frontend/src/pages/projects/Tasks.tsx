import { useState } from 'react'
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  CheckSquare,
  Plus,
  Search,
  Filter,
  Calendar,
  User,
  Clock,
  AlertTriangle,
  CheckCircle,
  XCircle,
  BarChart3,
  Flag,
  FolderOpen
} from 'lucide-react'

interface TasksProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    tasks: 'المهام',
    createTask: 'إنشاء مهمة',
    taskOverview: 'نظرة عامة على المهام',
    search: 'بحث',
    filter: 'تصفية',
    taskTitle: 'عنوان المهمة',
    project: 'المشروع',
    assignedTo: 'مكلف إلى',
    dueDate: 'تاريخ الاستحقاق',
    priority: 'الأولوية',
    status: 'الحالة',
    progress: 'التقدم',
    actions: 'الإجراءات',
    todo: 'للقيام',
    inProgress: 'قيد التنفيذ',
    review: 'مراجعة',
    completed: 'مكتمل',
    cancelled: 'ملغي',
    low: 'منخفضة',
    medium: 'متوسطة',
    high: 'عالية',
    urgent: 'عاجل',
    viewDetails: 'عرض التفاصيل',
    editTask: 'تعديل المهمة',
    totalTasks: 'إجمالي المهام',
    completedTasks: 'المهام المكتملة',
    inProgressTasks: 'المهام قيد التنفيذ',
    overdueTasks: 'المهام المتأخرة',
    estimatedHours: 'الساعات المقدرة',
    actualHours: 'الساعات الفعلية',
    description: 'الوصف',
    dependencies: 'التبعيات',
    tags: 'العلامات',
    createdBy: 'أنشأ بواسطة',
    startDate: 'تاريخ البداية',
    completionDate: 'تاريخ الإكمال'
  },
  en: {
    tasks: 'Tasks',
    createTask: 'Create Task',
    taskOverview: 'Task Overview',
    search: 'Search',
    filter: 'Filter',
    taskTitle: 'Task Title',
    project: 'Project',
    assignedTo: 'Assigned To',
    dueDate: 'Due Date',
    priority: 'Priority',
    status: 'Status',
    progress: 'Progress',
    actions: 'Actions',
    todo: 'To Do',
    inProgress: 'In Progress',
    review: 'Review',
    completed: 'Completed',
    cancelled: 'Cancelled',
    low: 'Low',
    medium: 'Medium',
    high: 'High',
    urgent: 'Urgent',
    viewDetails: 'View Details',
    editTask: 'Edit Task',
    totalTasks: 'Total Tasks',
    completedTasks: 'Completed Tasks',
    inProgressTasks: 'In Progress Tasks',
    overdueTasks: 'Overdue Tasks',
    estimatedHours: 'Estimated Hours',
    actualHours: 'Actual Hours',
    description: 'Description',
    dependencies: 'Dependencies',
    tags: 'Tags',
    createdBy: 'Created By',
    startDate: 'Start Date',
    completionDate: 'Completion Date'
  }
}

export default function Tasks({ language }: TasksProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('all')
  const [viewMode, setViewMode] = useState('list') // list or kanban
  const t = translations[language]
  const isRTL = language === 'ar'

  // Mock data
  const taskStats = {
    totalTasks: 156,
    completedTasks: 89,
    inProgressTasks: 45,
    overdueTasks: 22
  }

  const tasks = [
    {
      id: 1,
      title: 'تصميم واجهة المستخدم الرئيسية',
      project: 'نظام إدارة المخزون',
      assignedTo: 'أحمد محمد',
      createdBy: 'فاطمة علي',
      dueDate: '2024-02-15',
      startDate: '2024-01-20',
      priority: 'high',
      status: 'inProgress',
      progress: 65,
      estimatedHours: 40,
      actualHours: 26,
      description: 'تصميم وتطوير واجهة المستخدم الرئيسية للنظام',
      tags: ['UI/UX', 'Frontend', 'Design']
    },
    {
      id: 2,
      title: 'إعداد قاعدة البيانات',
      project: 'تطوير تطبيق الموبايل',
      assignedTo: 'محمد حسن',
      createdBy: 'أحمد محمد',
      dueDate: '2024-02-10',
      startDate: '2024-01-25',
      priority: 'urgent',
      status: 'review',
      progress: 90,
      estimatedHours: 24,
      actualHours: 22,
      description: 'إنشاء وإعداد قاعدة البيانات للتطبيق',
      tags: ['Database', 'Backend', 'Setup']
    },
    {
      id: 3,
      title: 'اختبار وحدة المدفوعات',
      project: 'تحديث نظام المحاسبة',
      assignedTo: 'سارة أحمد',
      createdBy: 'محمد حسن',
      dueDate: '2024-01-30',
      startDate: '2024-01-15',
      priority: 'medium',
      status: 'completed',
      progress: 100,
      estimatedHours: 16,
      actualHours: 18,
      description: 'اختبار شامل لوحدة المدفوعات والتحقق من صحة العمليات',
      tags: ['Testing', 'Payments', 'QA']
    },
    {
      id: 4,
      title: 'كتابة الوثائق التقنية',
      project: 'حملة التسويق الرقمي',
      assignedTo: 'علي محمود',
      createdBy: 'سارة أحمد',
      dueDate: '2024-02-20',
      startDate: '2024-02-01',
      priority: 'low',
      status: 'todo',
      progress: 0,
      estimatedHours: 12,
      actualHours: 0,
      description: 'إعداد الوثائق التقنية للحملة التسويقية',
      tags: ['Documentation', 'Marketing', 'Content']
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'todo':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      case 'inProgress':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'review':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low':
        return 'text-green-400'
      case 'medium':
        return 'text-yellow-400'
      case 'high':
        return 'text-orange-400'
      case 'urgent':
        return 'text-red-400'
      default:
        return 'text-gray-400'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'todo':
        return <CheckSquare className="h-4 w-4 text-gray-500" />
      case 'inProgress':
        return <Clock className="h-4 w-4 text-blue-500" />
      case 'review':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'cancelled':
        return <XCircle className="h-4 w-4 text-red-500" />
      default:
        return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  const isOverdue = (dueDate: string, status: string) => {
    if (status === 'completed') return false
    const due = new Date(dueDate)
    const today = new Date()
    return due < today
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">{t.tasks}</h1>
          <p className="text-white/70">إدارة ومتابعة المهام والأنشطة</p>
        </div>
        <div className="flex gap-2">
          <Button className="glass-button">
            <Plus className="h-4 w-4 mr-2" />
            {t.createTask}
          </Button>
          <Button 
            variant="outline" 
            className="glass-button"
            onClick={() => setViewMode(viewMode === 'list' ? 'kanban' : 'list')}
          >
            <BarChart3 className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.totalTasks}</p>
                <p className="text-2xl font-bold text-white">{taskStats.totalTasks}</p>
              </div>
              <CheckSquare className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.completedTasks}</p>
                <p className="text-2xl font-bold text-green-400">{taskStats.completedTasks}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.inProgressTasks}</p>
                <p className="text-2xl font-bold text-blue-400">{taskStats.inProgressTasks}</p>
              </div>
              <Clock className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.overdueTasks}</p>
                <p className="text-2xl font-bold text-red-400">{taskStats.overdueTasks}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tasks List */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <CardTitle className="text-white text-xl">{t.taskOverview}</CardTitle>
              <CardDescription className="text-white/70">
                قائمة بجميع المهام وحالتها الحالية
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 h-4 w-4" />
                <Input
                  placeholder={t.search}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 glass-input"
                />
              </div>
              <Button variant="outline" className="glass-button">
                <Filter className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {tasks.map((task) => (
              <Card key={task.id} className={`glass-card border-white/10 hover:border-white/30 transition-all ${isOverdue(task.dueDate, task.status) ? 'border-red-400/50' : ''}`}>
                <CardContent className="p-6">
                  <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                    {/* Task Info */}
                    <div className="flex-1 space-y-3">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h3 className="text-white font-semibold text-lg mb-1">{task.title}</h3>
                          <div className="flex items-center gap-4 text-sm text-white/70">
                            <div className="flex items-center gap-1">
                              <FolderOpen className="h-4 w-4 text-blue-400" />
                              <span>{task.project}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <User className="h-4 w-4 text-green-400" />
                              <span>{task.assignedTo}</span>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(task.status)}`}>
                            {getStatusIcon(task.status)}
                            {t[task.status as keyof typeof t]}
                          </div>
                          <Flag className={`h-4 w-4 ${getPriorityColor(task.priority)}`} />
                        </div>
                      </div>

                      {/* Progress Bar */}
                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span className="text-white/70">{t.progress}</span>
                          <span className="text-white">{task.progress}%</span>
                        </div>
                        <div className="w-full bg-white/20 rounded-full h-2">
                          <div 
                            className="bg-gradient-to-r from-blue-500 to-green-500 h-2 rounded-full transition-all"
                            style={{ width: `${task.progress}%` }}
                          ></div>
                        </div>
                      </div>

                      {/* Task Details */}
                      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                        <div>
                          <p className="text-white/70">{t.dueDate}</p>
                          <p className={`font-medium ${isOverdue(task.dueDate, task.status) ? 'text-red-400' : 'text-white'}`}>
                            {task.dueDate}
                          </p>
                        </div>
                        <div>
                          <p className="text-white/70">{t.estimatedHours}</p>
                          <p className="text-white font-medium">{task.estimatedHours}h</p>
                        </div>
                        <div>
                          <p className="text-white/70">{t.actualHours}</p>
                          <p className="text-white font-medium">{task.actualHours}h</p>
                        </div>
                        <div>
                          <p className="text-white/70">{t.priority}</p>
                          <p className={`font-medium ${getPriorityColor(task.priority)}`}>
                            {t[task.priority as keyof typeof t]}
                          </p>
                        </div>
                      </div>

                      {/* Tags */}
                      <div className="flex flex-wrap gap-2">
                        {task.tags.map((tag, index) => (
                          <span key={index} className="px-2 py-1 bg-white/10 text-white/80 text-xs rounded-full">
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex gap-2">
                      <Button size="sm" className="glass-button">
                        {t.viewDetails}
                      </Button>
                      <Button size="sm" variant="outline" className="glass-button">
                        {t.editTask}
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
