import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Badge } from '../../components/ui/badge'
import {
  Award,
  Plus,
  Search,
  Eye,
  Edit,
  Trash2,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  Target,
  TrendingUp,
  BarChart3,
  FileText,
  Star
} from 'lucide-react'

interface QualityManagementProps {
  language: 'ar' | 'en'
}

interface QualityRecord {
  id: string
  title: string
  titleAr: string
  description: string
  descriptionAr: string
  type: 'inspection' | 'audit' | 'review' | 'incident' | 'improvement'
  category: string
  categoryAr: string
  status: 'planned' | 'in-progress' | 'completed' | 'failed' | 'cancelled'
  priority: 'low' | 'medium' | 'high' | 'critical'
  inspector: string
  inspectorAr: string
  department: string
  departmentAr: string
  scheduledDate: string
  completedDate?: string
  score: number // 0-100
  findings: string
  findingsAr: string
  recommendations: string
  recommendationsAr: string
  correctiveActions: string
  correctiveActionsAr: string
  dueDate: string
  assignedTo: string
  assignedToAr: string
  standard: string
  standardAr: string
  nonConformities: number
  majorIssues: number
  minorIssues: number
  cost: number
}

const mockQualityRecords: QualityRecord[] = [
  {
    id: '1',
    title: 'ISO 9001 Internal Audit',
    titleAr: 'التدقيق الداخلي لآيزو 9001',
    description: 'Annual internal audit for ISO 9001 quality management system',
    descriptionAr: 'التدقيق الداخلي السنوي لنظام إدارة الجودة آيزو 9001',
    type: 'audit',
    category: 'Quality Management System',
    categoryAr: 'نظام إدارة الجودة',
    status: 'completed',
    priority: 'high',
    inspector: 'Sarah Hassan',
    inspectorAr: 'سارة حسن',
    department: 'Quality Assurance',
    departmentAr: 'ضمان الجودة',
    scheduledDate: '2024-01-15',
    completedDate: '2024-01-18',
    score: 92,
    findings: 'Overall compliance with ISO 9001 standards. Minor documentation gaps identified.',
    findingsAr: 'امتثال عام لمعايير آيزو 9001. تم تحديد فجوات طفيفة في التوثيق.',
    recommendations: 'Update procedure documentation and conduct staff training',
    recommendationsAr: 'تحديث توثيق الإجراءات وإجراء تدريب للموظفين',
    correctiveActions: 'Documentation update in progress, training scheduled for February',
    correctiveActionsAr: 'تحديث التوثيق قيد التنفيذ، التدريب مجدول لشهر فبراير',
    dueDate: '2024-02-28',
    assignedTo: 'Quality Manager',
    assignedToAr: 'مدير الجودة',
    standard: 'ISO 9001:2015',
    standardAr: 'آيزو 9001:2015',
    nonConformities: 2,
    majorIssues: 0,
    minorIssues: 2,
    cost: 15000
  },
  {
    id: '2',
    title: 'Product Quality Inspection',
    titleAr: 'فحص جودة المنتج',
    description: 'Quality inspection of latest product batch',
    descriptionAr: 'فحص جودة لأحدث دفعة من المنتجات',
    type: 'inspection',
    category: 'Product Quality',
    categoryAr: 'جودة المنتج',
    status: 'in-progress',
    priority: 'medium',
    inspector: 'Ahmed Al-Rashid',
    inspectorAr: 'أحمد الراشد',
    department: 'Production',
    departmentAr: 'الإنتاج',
    scheduledDate: '2024-01-22',
    score: 0,
    findings: 'Inspection in progress',
    findingsAr: 'الفحص قيد التنفيذ',
    recommendations: 'Pending completion of inspection',
    recommendationsAr: 'في انتظار إكمال الفحص',
    correctiveActions: 'N/A',
    correctiveActionsAr: 'غير متاح',
    dueDate: '2024-01-25',
    assignedTo: 'Production Manager',
    assignedToAr: 'مدير الإنتاج',
    standard: 'Company Quality Standards',
    standardAr: 'معايير الجودة للشركة',
    nonConformities: 0,
    majorIssues: 0,
    minorIssues: 0,
    cost: 5000
  },
  {
    id: '3',
    title: 'Customer Complaint Investigation',
    titleAr: 'تحقيق شكوى العميل',
    description: 'Investigation of customer complaint regarding product defect',
    descriptionAr: 'تحقيق في شكوى العميل بخصوص عيب في المنتج',
    type: 'incident',
    category: 'Customer Quality',
    categoryAr: 'جودة العملاء',
    status: 'completed',
    priority: 'high',
    inspector: 'Fatima Mohammed',
    inspectorAr: 'فاطمة محمد',
    department: 'Customer Service',
    departmentAr: 'خدمة العملاء',
    scheduledDate: '2024-01-10',
    completedDate: '2024-01-12',
    score: 65,
    findings: 'Manufacturing defect confirmed. Root cause identified in assembly process.',
    findingsAr: 'تأكيد عيب التصنيع. تم تحديد السبب الجذري في عملية التجميع.',
    recommendations: 'Revise assembly procedures and increase quality checks',
    recommendationsAr: 'مراجعة إجراءات التجميع وزيادة فحوصات الجودة',
    correctiveActions: 'Assembly procedure updated, additional quality checkpoint added',
    correctiveActionsAr: 'تم تحديث إجراء التجميع وإضافة نقطة فحص جودة إضافية',
    dueDate: '2024-01-30',
    assignedTo: 'Production Manager',
    assignedToAr: 'مدير الإنتاج',
    standard: 'Customer Satisfaction Standards',
    standardAr: 'معايير رضا العملاء',
    nonConformities: 1,
    majorIssues: 1,
    minorIssues: 0,
    cost: 8000
  },
  {
    id: '4',
    title: 'Process Improvement Review',
    titleAr: 'مراجعة تحسين العمليات',
    description: 'Quarterly review of process improvement initiatives',
    descriptionAr: 'المراجعة الربع سنوية لمبادرات تحسين العمليات',
    type: 'review',
    category: 'Process Improvement',
    categoryAr: 'تحسين العمليات',
    status: 'planned',
    priority: 'medium',
    inspector: 'Omar Abdullah',
    inspectorAr: 'عمر عبدالله',
    department: 'Operations',
    departmentAr: 'العمليات',
    scheduledDate: '2024-02-01',
    score: 0,
    findings: 'Review not yet conducted',
    findingsAr: 'لم يتم إجراء المراجعة بعد',
    recommendations: 'Pending review completion',
    recommendationsAr: 'في انتظار إكمال المراجعة',
    correctiveActions: 'N/A',
    correctiveActionsAr: 'غير متاح',
    dueDate: '2024-02-15',
    assignedTo: 'Operations Manager',
    assignedToAr: 'مدير العمليات',
    standard: 'Continuous Improvement Framework',
    standardAr: 'إطار التحسين المستمر',
    nonConformities: 0,
    majorIssues: 0,
    minorIssues: 0,
    cost: 3000
  }
]

export default function QualityManagement({ language }: QualityManagementProps) {
  const [qualityRecords, setQualityRecords] = useState<QualityRecord[]>(mockQualityRecords)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [typeFilter, setTypeFilter] = useState<string>('all')
  const [priorityFilter, setPriorityFilter] = useState<string>('all')

  const text = {
    ar: {
      title: 'إدارة الجودة',
      description: 'إدارة ومراقبة معايير الجودة والتحسين المستمر',
      newRecord: 'سجل جديد',
      search: 'البحث في سجلات الجودة...',
      recordTitle: 'عنوان السجل',
      type: 'النوع',
      category: 'الفئة',
      status: 'الحالة',
      priority: 'الأولوية',
      inspector: 'المفتش',
      department: 'القسم',
      score: 'النتيجة',
      scheduledDate: 'التاريخ المجدول',
      completedDate: 'تاريخ الإكمال',
      dueDate: 'تاريخ الاستحقاق',
      actions: 'الإجراءات',
      view: 'عرض',
      edit: 'تعديل',
      delete: 'حذف',
      totalRecords: 'إجمالي السجلات',
      completedRecords: 'السجلات المكتملة',
      avgScore: 'متوسط النتيجة',
      pendingActions: 'الإجراءات المعلقة',
      inspection: 'فحص',
      audit: 'تدقيق',
      review: 'مراجعة',
      incident: 'حادثة',
      improvement: 'تحسين',
      planned: 'مخطط',
      'in-progress': 'قيد التنفيذ',
      completed: 'مكتمل',
      failed: 'فاشل',
      cancelled: 'ملغي',
      low: 'منخفض',
      medium: 'متوسط',
      high: 'عالي',
      critical: 'حرج',
      findings: 'النتائج',
      recommendations: 'التوصيات',
      correctiveActions: 'الإجراءات التصحيحية',
      standard: 'المعيار',
      nonConformities: 'عدم المطابقة',
      majorIssues: 'القضايا الرئيسية',
      minorIssues: 'القضايا الطفيفة',
      cost: 'التكلفة',
      sar: 'ر.س'
    },
    en: {
      title: 'Quality Management',
      description: 'Manage and monitor quality standards and continuous improvement',
      newRecord: 'New Record',
      search: 'Search quality records...',
      recordTitle: 'Record Title',
      type: 'Type',
      category: 'Category',
      status: 'Status',
      priority: 'Priority',
      inspector: 'Inspector',
      department: 'Department',
      score: 'Score',
      scheduledDate: 'Scheduled Date',
      completedDate: 'Completed Date',
      dueDate: 'Due Date',
      actions: 'Actions',
      view: 'View',
      edit: 'Edit',
      delete: 'Delete',
      totalRecords: 'Total Records',
      completedRecords: 'Completed Records',
      avgScore: 'Average Score',
      pendingActions: 'Pending Actions',
      inspection: 'Inspection',
      audit: 'Audit',
      review: 'Review',
      incident: 'Incident',
      improvement: 'Improvement',
      planned: 'Planned',
      'in-progress': 'In Progress',
      completed: 'Completed',
      failed: 'Failed',
      cancelled: 'Cancelled',
      low: 'Low',
      medium: 'Medium',
      high: 'High',
      critical: 'Critical',
      findings: 'Findings',
      recommendations: 'Recommendations',
      correctiveActions: 'Corrective Actions',
      standard: 'Standard',
      nonConformities: 'Non-Conformities',
      majorIssues: 'Major Issues',
      minorIssues: 'Minor Issues',
      cost: 'Cost',
      sar: 'SAR'
    }
  }

  const t = text[language]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'planned': return 'bg-primary/20 text-primary border-primary/30'
      case 'in-progress': return 'bg-accent/20 text-accent border-accent/30'
      case 'completed': return 'bg-secondary/20 text-secondary border-secondary/30'
      case 'failed': return 'bg-destructive/20 text-destructive border-destructive/30'
      case 'cancelled': return 'bg-muted/20 text-muted-foreground border-muted/30'
      default: return 'bg-muted/20 text-muted-foreground border-muted/30'
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'inspection': return 'bg-primary/20 text-primary border-primary/30'
      case 'audit': return 'bg-accent/20 text-accent border-accent/30'
      case 'review': return 'bg-secondary/20 text-secondary border-secondary/30'
      case 'incident': return 'bg-destructive/20 text-destructive border-destructive/30'
      case 'improvement': return 'bg-muted/20 text-muted-foreground border-muted/30'
      default: return 'bg-muted/20 text-muted-foreground border-muted/30'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low': return 'bg-secondary/20 text-secondary border-secondary/30'
      case 'medium': return 'bg-accent/20 text-accent border-accent/30'
      case 'high': return 'bg-primary/20 text-primary border-primary/30'
      case 'critical': return 'bg-destructive/20 text-destructive border-destructive/30'
      default: return 'bg-muted/20 text-muted-foreground border-muted/30'
    }
  }

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-secondary'
    if (score >= 80) return 'text-accent'
    if (score >= 70) return 'text-primary'
    return 'text-destructive'
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'planned': return <Clock className="w-4 h-4" />
      case 'in-progress': return <Target className="w-4 h-4" />
      case 'completed': return <CheckCircle className="w-4 h-4" />
      case 'failed': return <XCircle className="w-4 h-4" />
      case 'cancelled': return <XCircle className="w-4 h-4" />
      default: return <Clock className="w-4 h-4" />
    }
  }

  const filteredRecords = qualityRecords.filter(record => {
    const matchesSearch = record.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         record.titleAr.includes(searchTerm) ||
                         record.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         record.descriptionAr.includes(searchTerm)
    const matchesStatus = statusFilter === 'all' || record.status === statusFilter
    const matchesType = typeFilter === 'all' || record.type === typeFilter
    const matchesPriority = priorityFilter === 'all' || record.priority === priorityFilter
    return matchesSearch && matchesStatus && matchesType && matchesPriority
  })

  const completedRecords = qualityRecords.filter(record => record.status === 'completed').length
  const avgScore = qualityRecords.filter(record => record.score > 0).length > 0
    ? qualityRecords.filter(record => record.score > 0).reduce((sum, record) => sum + record.score, 0) / qualityRecords.filter(record => record.score > 0).length
    : 0
  const pendingActions = qualityRecords.filter(record => record.status === 'planned' || record.status === 'in-progress').length

  return (
    <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">{t.title}</h1>
            <p className="text-white/70">{t.description}</p>
          </div>
          <Button className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70">
            <Plus className="w-4 h-4 mr-2" />
            {t.newRecord}
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="modern-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.totalRecords}</p>
                  <p className="text-2xl font-bold text-white">{qualityRecords.length}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-primary/20 flex items-center justify-center">
                  <Award className="w-6 h-6 text-primary" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="modern-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.completedRecords}</p>
                  <p className="text-2xl font-bold text-white">{completedRecords}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-secondary/20 flex items-center justify-center">
                  <CheckCircle className="w-6 h-6 text-secondary" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="modern-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.avgScore}</p>
                  <div className="flex items-center gap-2">
                    <p className="text-2xl font-bold text-white">{avgScore.toFixed(1)}%</p>
                    <Star className="w-5 h-5 text-accent" />
                  </div>
                </div>
                <div className="w-12 h-12 rounded-full bg-accent/20 flex items-center justify-center">
                  <BarChart3 className="w-6 h-6 text-accent" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="modern-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.pendingActions}</p>
                  <p className="text-2xl font-bold text-white">{pendingActions}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-muted/20 flex items-center justify-center">
                  <Clock className="w-6 h-6 text-muted-foreground" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card className="modern-card">
          <CardContent className="p-6">
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 w-4 h-4" />
                  <Input
                    placeholder={t.search}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 glass-input"
                  />
                </div>
              </div>
              <div className="flex gap-4">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="glass-input px-3 py-2 rounded-lg"
                >
                  <option value="all">جميع الحالات</option>
                  <option value="planned">{t.planned}</option>
                  <option value="in-progress">{t['in-progress']}</option>
                  <option value="completed">{t.completed}</option>
                  <option value="failed">{t.failed}</option>
                  <option value="cancelled">{t.cancelled}</option>
                </select>
                <select
                  value={typeFilter}
                  onChange={(e) => setTypeFilter(e.target.value)}
                  className="glass-input px-3 py-2 rounded-lg"
                >
                  <option value="all">جميع الأنواع</option>
                  <option value="inspection">{t.inspection}</option>
                  <option value="audit">{t.audit}</option>
                  <option value="review">{t.review}</option>
                  <option value="incident">{t.incident}</option>
                  <option value="improvement">{t.improvement}</option>
                </select>
                <select
                  value={priorityFilter}
                  onChange={(e) => setPriorityFilter(e.target.value)}
                  className="glass-input px-3 py-2 rounded-lg"
                >
                  <option value="all">جميع الأولويات</option>
                  <option value="low">{t.low}</option>
                  <option value="medium">{t.medium}</option>
                  <option value="high">{t.high}</option>
                  <option value="critical">{t.critical}</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quality Records Table */}
        <Card className="modern-card">
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-white/10">
                    <th className="text-right p-4 text-white/70 font-medium">{t.recordTitle}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.type}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.status}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.priority}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.score}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.inspector}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.dueDate}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.actions}</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredRecords.map((record) => (
                    <tr key={record.id} className="border-b border-white/5 hover:bg-white/5">
                      <td className="p-4">
                        <div>
                          <span className="text-white font-medium">
                            {language === 'ar' ? record.titleAr : record.title}
                          </span>
                          <div className="text-xs text-white/60 mt-1">
                            {language === 'ar' ? record.categoryAr : record.category}
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <Badge className={`${getTypeColor(record.type)} border`}>
                          {t[record.type as keyof typeof t]}
                        </Badge>
                      </td>
                      <td className="p-4">
                        <Badge className={`${getStatusColor(record.status)} border flex items-center gap-1`}>
                          {getStatusIcon(record.status)}
                          {t[record.status as keyof typeof t]}
                        </Badge>
                      </td>
                      <td className="p-4">
                        <Badge className={`${getPriorityColor(record.priority)} border`}>
                          {t[record.priority as keyof typeof t]}
                        </Badge>
                      </td>
                      <td className="p-4">
                        {record.score > 0 ? (
                          <div className="flex items-center gap-2">
                            <span className={`font-semibold ${getScoreColor(record.score)}`}>
                              {record.score}%
                            </span>
                            {record.nonConformities > 0 && (
                              <div className="text-xs text-red-400">
                                {record.nonConformities} عدم مطابقة
                              </div>
                            )}
                          </div>
                        ) : (
                          <span className="text-white/50">-</span>
                        )}
                      </td>
                      <td className="p-4">
                        <span className="text-white">{language === 'ar' ? record.inspectorAr : record.inspector}</span>
                      </td>
                      <td className="p-4">
                        <span className="text-white/70">{record.dueDate}</span>
                      </td>
                      <td className="p-4">
                        <div className="flex gap-2">
                          <Button size="sm" variant="ghost" className="text-white/70 hover:text-white">
                            <Eye className="w-4 h-4" />
                          </Button>
                          <Button size="sm" variant="ghost" className="text-white/70 hover:text-white">
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button size="sm" variant="ghost" className="text-red-400 hover:text-red-300">
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
    </div>
  )
}
