import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Badge } from '../../components/ui/badge'
import {
  Briefcase,
  Plus,
  Search,
  Eye,
  Edit,
  Trash2,
  MapPin,
  Clock,
  DollarSign,
  Users,
  Calendar,
  TrendingUp
} from 'lucide-react'

interface JobPostingsProps {
  language: 'ar' | 'en'
}

interface JobPosting {
  id: string
  title: string
  titleAr: string
  department: string
  departmentAr: string
  location: string
  locationAr: string
  employmentType: 'full-time' | 'part-time' | 'contract' | 'internship'
  experienceLevel: 'entry' | 'mid' | 'senior' | 'executive'
  salaryRange: {
    min: number
    max: number
  }
  status: 'draft' | 'active' | 'paused' | 'closed'
  postedDate: string
  closingDate: string
  applicationsCount: number
  viewsCount: number
  description: string
  descriptionAr: string
  requirements: string[]
  requirementsAr: string[]
  benefits: string[]
  benefitsAr: string[]
  isRemote: boolean
  isUrgent: boolean
}

const mockJobPostings: JobPosting[] = [
  {
    id: '1',
    title: 'Senior Software Engineer',
    titleAr: 'مهندس برمجيات أول',
    department: 'Engineering',
    departmentAr: 'الهندسة',
    location: 'Riyadh',
    locationAr: 'الرياض',
    employmentType: 'full-time',
    experienceLevel: 'senior',
    salaryRange: { min: 15000, max: 25000 },
    status: 'active',
    postedDate: '2024-01-15',
    closingDate: '2024-02-15',
    applicationsCount: 45,
    viewsCount: 320,
    description: 'We are looking for a senior software engineer to join our team...',
    descriptionAr: 'نبحث عن مهندس برمجيات أول للانضمام إلى فريقنا...',
    requirements: ['5+ years experience', 'React/Node.js', 'Team leadership'],
    requirementsAr: ['خبرة 5+ سنوات', 'React/Node.js', 'قيادة الفريق'],
    benefits: ['Health insurance', 'Annual bonus', 'Remote work'],
    benefitsAr: ['تأمين صحي', 'مكافأة سنوية', 'عمل عن بعد'],
    isRemote: true,
    isUrgent: false
  },
  {
    id: '2',
    title: 'Marketing Manager',
    titleAr: 'مدير التسويق',
    department: 'Marketing',
    departmentAr: 'التسويق',
    location: 'Jeddah',
    locationAr: 'جدة',
    employmentType: 'full-time',
    experienceLevel: 'mid',
    salaryRange: { min: 12000, max: 18000 },
    status: 'active',
    postedDate: '2024-01-20',
    closingDate: '2024-02-20',
    applicationsCount: 28,
    viewsCount: 180,
    description: 'Join our marketing team as a marketing manager...',
    descriptionAr: 'انضم إلى فريق التسويق كمدير تسويق...',
    requirements: ['3+ years marketing experience', 'Digital marketing', 'Arabic & English'],
    requirementsAr: ['خبرة 3+ سنوات في التسويق', 'التسويق الرقمي', 'العربية والإنجليزية'],
    benefits: ['Competitive salary', 'Training programs', 'Career growth'],
    benefitsAr: ['راتب تنافسي', 'برامج تدريبية', 'نمو مهني'],
    isRemote: false,
    isUrgent: true
  },
  {
    id: '3',
    title: 'Data Analyst Intern',
    titleAr: 'متدرب محلل بيانات',
    department: 'Analytics',
    departmentAr: 'التحليلات',
    location: 'Dammam',
    locationAr: 'الدمام',
    employmentType: 'internship',
    experienceLevel: 'entry',
    salaryRange: { min: 3000, max: 5000 },
    status: 'paused',
    postedDate: '2024-01-10',
    closingDate: '2024-02-10',
    applicationsCount: 67,
    viewsCount: 450,
    description: 'Internship opportunity for data analysis...',
    descriptionAr: 'فرصة تدريب في تحليل البيانات...',
    requirements: ['Statistics background', 'Python/R', 'Fresh graduate'],
    requirementsAr: ['خلفية إحصائية', 'Python/R', 'خريج جديد'],
    benefits: ['Learning opportunity', 'Mentorship', 'Certificate'],
    benefitsAr: ['فرصة تعلم', 'إرشاد', 'شهادة'],
    isRemote: false,
    isUrgent: false
  }
]

export default function JobPostings({ language }: JobPostingsProps) {
  const [jobPostings, setJobPostings] = useState<JobPosting[]>(mockJobPostings)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [departmentFilter, setDepartmentFilter] = useState<string>('all')

  const text = {
    ar: {
      title: 'الوظائف المعلنة',
      description: 'إدارة ومتابعة الوظائف المعلنة والمرشحين',
      newJob: 'وظيفة جديدة',
      search: 'البحث في الوظائف...',
      jobTitle: 'المسمى الوظيفي',
      department: 'القسم',
      location: 'الموقع',
      employmentType: 'نوع التوظيف',
      experienceLevel: 'مستوى الخبرة',
      salary: 'الراتب',
      status: 'الحالة',
      applications: 'الطلبات',
      views: 'المشاهدات',
      postedDate: 'تاريخ النشر',
      closingDate: 'تاريخ الإغلاق',
      actions: 'الإجراءات',
      view: 'عرض',
      edit: 'تعديل',
      delete: 'حذف',
      totalJobs: 'إجمالي الوظائف',
      activeJobs: 'الوظائف النشطة',
      totalApplications: 'إجمالي الطلبات',
      avgApplications: 'متوسط الطلبات',
      draft: 'مسودة',
      active: 'نشط',
      paused: 'متوقف',
      closed: 'مغلق',
      'full-time': 'دوام كامل',
      'part-time': 'دوام جزئي',
      contract: 'عقد',
      internship: 'تدريب',
      entry: 'مبتدئ',
      mid: 'متوسط',
      senior: 'أول',
      executive: 'تنفيذي',
      remote: 'عن بعد',
      urgent: 'عاجل',
      sar: 'ر.س'
    },
    en: {
      title: 'Job Postings',
      description: 'Manage and track job postings and candidates',
      newJob: 'New Job',
      search: 'Search jobs...',
      jobTitle: 'Job Title',
      department: 'Department',
      location: 'Location',
      employmentType: 'Employment Type',
      experienceLevel: 'Experience Level',
      salary: 'Salary',
      status: 'Status',
      applications: 'Applications',
      views: 'Views',
      postedDate: 'Posted Date',
      closingDate: 'Closing Date',
      actions: 'Actions',
      view: 'View',
      edit: 'Edit',
      delete: 'Delete',
      totalJobs: 'Total Jobs',
      activeJobs: 'Active Jobs',
      totalApplications: 'Total Applications',
      avgApplications: 'Avg Applications',
      draft: 'Draft',
      active: 'Active',
      paused: 'Paused',
      closed: 'Closed',
      'full-time': 'Full Time',
      'part-time': 'Part Time',
      contract: 'Contract',
      internship: 'Internship',
      entry: 'Entry',
      mid: 'Mid',
      senior: 'Senior',
      executive: 'Executive',
      remote: 'Remote',
      urgent: 'Urgent',
      sar: 'SAR'
    }
  }

  const t = text[language]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft': return 'bg-gray-500/20 text-gray-300 border-gray-500/30'
      case 'active': return 'bg-green-500/20 text-green-300 border-green-500/30'
      case 'paused': return 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30'
      case 'closed': return 'bg-red-500/20 text-red-300 border-red-500/30'
      default: return 'bg-gray-500/20 text-gray-300 border-gray-500/30'
    }
  }

  const getEmploymentTypeColor = (type: string) => {
    switch (type) {
      case 'full-time': return 'bg-blue-500/20 text-blue-300 border-blue-500/30'
      case 'part-time': return 'bg-purple-500/20 text-purple-300 border-purple-500/30'
      case 'contract': return 'bg-orange-500/20 text-orange-300 border-orange-500/30'
      case 'internship': return 'bg-green-500/20 text-green-300 border-green-500/30'
      default: return 'bg-gray-500/20 text-gray-300 border-gray-500/30'
    }
  }

  const filteredJobs = jobPostings.filter(job => {
    const matchesSearch = job.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         job.titleAr.includes(searchTerm) ||
                         job.department.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         job.departmentAr.includes(searchTerm)
    const matchesStatus = statusFilter === 'all' || job.status === statusFilter
    const matchesDepartment = departmentFilter === 'all' || job.department === departmentFilter
    return matchesSearch && matchesStatus && matchesDepartment
  })

  const activeJobs = jobPostings.filter(job => job.status === 'active').length
  const totalApplications = jobPostings.reduce((sum, job) => sum + job.applicationsCount, 0)
  const avgApplications = jobPostings.length > 0 ? totalApplications / jobPostings.length : 0

  return (
    <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">{t.title}</h1>
            <p className="text-white/70">{t.description}</p>
          </div>
          <Button className="bg-gradient-to-r from-sky-500 to-blue-600 hover:from-sky-600 hover:to-blue-700">
            <Plus className="w-4 h-4 mr-2" />
            {t.newJob}
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="modern-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.totalJobs}</p>
                  <p className="text-2xl font-bold text-white">{jobPostings.length}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-primary/20 flex items-center justify-center">
                  <Briefcase className="w-6 h-6 text-primary" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="modern-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.activeJobs}</p>
                  <p className="text-2xl font-bold text-white">{activeJobs}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-secondary/20 flex items-center justify-center">
                  <TrendingUp className="w-6 h-6 text-secondary" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="modern-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.totalApplications}</p>
                  <p className="text-2xl font-bold text-white">{totalApplications}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-accent/20 flex items-center justify-center">
                  <Users className="w-6 h-6 text-accent" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="modern-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.avgApplications}</p>
                  <p className="text-2xl font-bold text-white">{avgApplications.toFixed(1)}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-muted/20 flex items-center justify-center">
                  <Calendar className="w-6 h-6 text-muted-foreground" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card className="modern-card">
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 w-4 h-4" />
                  <Input
                    placeholder={t.search}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 glass-input"
                  />
                </div>
              </div>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="glass-input px-3 py-2 rounded-lg"
              >
                <option value="all">جميع الحالات</option>
                <option value="draft">{t.draft}</option>
                <option value="active">{t.active}</option>
                <option value="paused">{t.paused}</option>
                <option value="closed">{t.closed}</option>
              </select>
              <select
                value={departmentFilter}
                onChange={(e) => setDepartmentFilter(e.target.value)}
                className="glass-input px-3 py-2 rounded-lg"
              >
                <option value="all">جميع الأقسام</option>
                <option value="Engineering">الهندسة</option>
                <option value="Marketing">التسويق</option>
                <option value="Analytics">التحليلات</option>
                <option value="Finance">المالية</option>
                <option value="HR">الموارد البشرية</option>
              </select>
            </div>
          </CardContent>
        </Card>

        {/* Jobs Table */}
        <Card className="modern-card">
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-white/10">
                    <th className="text-right p-4 text-white/70 font-medium">{t.jobTitle}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.department}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.location}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.employmentType}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.status}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.applications}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.salary}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.actions}</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredJobs.map((job) => (
                    <tr key={job.id} className="border-b border-white/5 hover:bg-white/5">
                      <td className="p-4">
                        <div>
                          <span className="text-white font-medium">
                            {language === 'ar' ? job.titleAr : job.title}
                          </span>
                          <div className="flex items-center gap-2 mt-1">
                            {job.isRemote && (
                              <Badge variant="outline" className="text-xs bg-blue-500/20 text-blue-300">
                                {t.remote}
                              </Badge>
                            )}
                            {job.isUrgent && (
                              <Badge variant="outline" className="text-xs bg-red-500/20 text-red-300">
                                {t.urgent}
                              </Badge>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <span className="text-white">{language === 'ar' ? job.departmentAr : job.department}</span>
                      </td>
                      <td className="p-4">
                        <div className="flex items-center gap-1">
                          <MapPin className="w-4 h-4 text-white/50" />
                          <span className="text-white">{language === 'ar' ? job.locationAr : job.location}</span>
                        </div>
                      </td>
                      <td className="p-4">
                        <Badge className={`${getEmploymentTypeColor(job.employmentType)} border`}>
                          {t[job.employmentType as keyof typeof t]}
                        </Badge>
                      </td>
                      <td className="p-4">
                        <Badge className={`${getStatusColor(job.status)} border`}>
                          {t[job.status as keyof typeof t]}
                        </Badge>
                      </td>
                      <td className="p-4">
                        <div className="flex items-center gap-2">
                          <Users className="w-4 h-4 text-white/50" />
                          <span className="text-white">{job.applicationsCount}</span>
                          <span className="text-white/50">({job.viewsCount} مشاهدة)</span>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="flex items-center gap-1">
                          <DollarSign className="w-4 h-4 text-white/50" />
                          <span className="text-white">
                            {job.salaryRange.min.toLocaleString()} - {job.salaryRange.max.toLocaleString()} {t.sar}
                          </span>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="flex gap-2">
                          <Button size="sm" variant="ghost" className="text-white/70 hover:text-white">
                            <Eye className="w-4 h-4" />
                          </Button>
                          <Button size="sm" variant="ghost" className="text-white/70 hover:text-white">
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button size="sm" variant="ghost" className="text-red-400 hover:text-red-300">
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
    </div>
  )
}
