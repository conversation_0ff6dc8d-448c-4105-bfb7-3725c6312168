/**
 * Sales Customers Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState, MouseEvent, ReactElement } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  Users,
  UserPlus,
  Eye,
  Edit,
  Trash2,
  Phone,
  Mail,
  DollarSign,
  TrendingUp,
  Star,
  ShoppingCart,
  Target,
  Calendar,
  Building,
  User
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { salesCustomerService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface SalesCustomersProps {
  language: 'ar' | 'en'
}

interface SalesCustomer {
  id: number
  name: string
  nameAr: string
  company: string
  companyAr: string
  email: string
  phone: string
  lastContact: string
  nextFollowUp: string
  salesValue: number
  opportunities: number
  status: 'hot' | 'warm' | 'cold' | 'qualified'
  leadScore: number
  stage: 'contacted' | 'qualified' | 'proposal' | 'negotiation' | 'closed'
  industry?: string
  industryAr?: string
  source: 'website' | 'referral' | 'social' | 'advertising' | 'event'
  assignedTo: string
  assignedToAr: string
  notes?: string
  notesAr?: string
}

const translations = {
  ar: {
    salesCustomers: 'عملاء المبيعات',
    addCustomer: 'إضافة عميل',
    editCustomer: 'تعديل العميل',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذا العميل؟',
    searchPlaceholder: 'البحث في العملاء...',
    name: 'اسم العميل',
    company: 'الشركة',
    email: 'البريد الإلكتروني',
    phone: 'رقم الهاتف',
    lastContact: 'آخر تواصل',
    nextFollowUp: 'المتابعة القادمة',
    salesValue: 'قيمة المبيعات',
    opportunities: 'الفرص',
    status: 'الحالة',
    leadScore: 'نقاط العميل المحتمل',
    stage: 'المرحلة',
    industry: 'الصناعة',
    source: 'المصدر',
    assignedTo: 'مُعيَّن إلى',
    notes: 'ملاحظات',
    hot: 'ساخن',
    warm: 'دافئ',
    cold: 'بارد',
    qualified: 'مؤهل',
    contacted: 'تم التواصل',
    proposal: 'عرض',
    negotiation: 'تفاوض',
    closed: 'مغلق',
    website: 'موقع إلكتروني',
    referral: 'إحالة',
    social: 'وسائل التواصل الاجتماعي',
    advertising: 'إعلان',
    event: 'حدث'
  },
  en: {
    salesCustomers: 'Sales Customers',
    addCustomer: 'Add Customer',
    editCustomer: 'Edit Customer',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this customer?',
    searchPlaceholder: 'Search customers...',
    name: 'Customer Name',
    company: 'Company',
    email: 'Email',
    phone: 'Phone',
    lastContact: 'Last Contact',
    nextFollowUp: 'Next Follow-up',
    salesValue: 'Sales Value',
    opportunities: 'Opportunities',
    status: 'Status',
    leadScore: 'Lead Score',
    stage: 'Stage',
    industry: 'Industry',
    source: 'Source',
    assignedTo: 'Assigned To',
    notes: 'Notes',
    hot: 'Hot',
    warm: 'Warm',
    cold: 'Cold',
    qualified: 'Qualified',
    contacted: 'Contacted',
    proposal: 'Proposal',
    negotiation: 'Negotiation',
    closed: 'Closed',
    website: 'Website',
    referral: 'Referral',
    social: 'Social Media',
    advertising: 'Advertising',
    event: 'Event'
  }
}

export default function SalesCustomers({ language }: SalesCustomersProps): React.ReactElement {
  const [showModal, setShowModal] = useState<boolean>(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: customers,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<SalesCustomer>({
    service: salesCustomerService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getStatusColor = (status: string): void => {
    switch (status) {
      case 'hot':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'warm':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'cold':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'qualified':
        return 'bg-green-100 text-green-800 border-green-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStageColor = (stage: string): void => {
    switch (stage) {
      case 'contacted':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'qualified':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'proposal':
        return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'negotiation':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'closed':
        return 'bg-emerald-100 text-emerald-800 border-emerald-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getSourceColor = (source: string): void => {
    switch (source) {
      case 'website':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'referral':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'social':
        return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'advertising':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'event':
        return 'bg-pink-100 text-pink-800 border-pink-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getLeadScoreColor = (score: number): void => {
    if (score >= 80) return 'text-green-400'
    if (score >= 60) return 'text-yellow-400'
    return 'text-red-400'
  }

  const getLeadScoreBgColor = (score: number): void => {
    if (score >= 80) return 'bg-green-500'
    if (score >= 60) return 'bg-yellow-500'
    return 'bg-red-500'
  }

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount)
  }

  // Table columns configuration
  const columns: TableColumn<SalesCustomer>[] = [
    {
      key: 'name',
      label: t.name,
      sortable: true,
      render: (item: SalesCustomer) => (
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-white font-bold">
            {(language === 'ar' ? item.nameAr : item.name).charAt(0)}
          </div>
          <div>
            <div className="font-medium text-white">
              {language === 'ar' ? item.nameAr : item.name}
            </div>
            <div className="text-sm text-white/60 flex items-center gap-1">
              <Building className="h-3 w-3" />
              {language === 'ar' ? item.companyAr : item.company}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'contact',
      label: 'Contact',
      render: (item: SalesCustomer) => (
        <div className="space-y-1">
          <div className="flex items-center gap-1">
            <Mail className="h-3 w-3 text-blue-400" />
            <span className="text-white/80 text-sm">{item.email}</span>
          </div>
          <div className="flex items-center gap-1">
            <Phone className="h-3 w-3 text-green-400" />
            <span className="text-white/80 text-sm">{item.phone}</span>
          </div>
        </div>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: SalesCustomer) => (
        <Badge className={getStatusColor(item.status)}>
          {t[item.status as keyof typeof t]}
        </Badge>
      )
    },
    {
      key: 'stage',
      label: t.stage,
      sortable: true,
      render: (item: SalesCustomer) => (
        <Badge className={getStageColor(item.stage)}>
          {t[item.stage as keyof typeof t]}
        </Badge>
      )
    },
    {
      key: 'leadScore',
      label: t.leadScore,
      sortable: true,
      render: (item: SalesCustomer) => (
        <div className="flex items-center gap-2">
          <div className="w-16 bg-white/20 rounded-full h-2">
            <div
              className={`h-2 rounded-full ${getLeadScoreBgColor(item.leadScore)}`}
              style={{ width: `${item.leadScore}%` }}
            ></div>
          </div>
          <span className={`text-sm font-medium ${getLeadScoreColor(item.leadScore)}`}>
            {item.leadScore}%
          </span>
        </div>
      )
    },
    {
      key: 'salesValue',
      label: t.salesValue,
      sortable: true,
      render: (item: SalesCustomer) => (
        <div className="text-green-400 font-medium">
          {formatCurrency(item.salesValue)}
        </div>
      )
    },
    {
      key: 'opportunities',
      label: t.opportunities,
      sortable: true,
      render: (item: SalesCustomer) => (
        <div className="flex items-center gap-1">
          <Target className="h-3 w-3 text-purple-400" />
          <span className="text-white/80">{item.opportunities}</span>
        </div>
      )
    },
    {
      key: 'lastContact',
      label: t.lastContact,
      sortable: true,
      render: (item: SalesCustomer) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">{item.lastContact}</span>
        </div>
      )
    },
    {
      key: 'assignedTo',
      label: t.assignedTo,
      render: (item: SalesCustomer) => (
        <div className="flex items-center gap-1">
          <User className="h-3 w-3 text-yellow-400" />
          <span className="text-white/80">
            {language === 'ar' ? item.assignedToAr : item.assignedTo}
          </span>
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: React.MouseEvent) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: React.MouseEvent) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: 'Contact',
      icon: Phone,
      onClick: (item: React.MouseEvent) => {
        // Handle contact logic
        window.open(`tel:${item.phone}`)
      },
      variant: 'ghost',
      className: 'text-green-400 hover:text-green-300 hover:bg-green-500/20'
    },
    {
      label: 'Email',
      icon: Mail,
      onClick: (item: React.MouseEvent) => {
        // Handle email logic
        window.open(`mailto:${item.email}`)
      },
      variant: 'ghost',
      className: 'text-blue-400 hover:text-blue-300 hover:bg-blue-500/20'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: React.MouseEvent) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'status',
      label: t.status,
      options: [
        { label: t.hot, value: 'hot' },
        { label: t.warm, value: 'warm' },
        { label: t.cold, value: 'cold' },
        { label: t.qualified, value: 'qualified' }
      ]
    },
    {
      key: 'stage',
      label: t.stage,
      options: [
        { label: t.contacted, value: 'contacted' },
        { label: t.qualified, value: 'qualified' },
        { label: t.proposal, value: 'proposal' },
        { label: t.negotiation, value: 'negotiation' },
        { label: t.closed, value: 'closed' }
      ]
    },
    {
      key: 'source',
      label: t.source,
      options: [
        { label: t.website, value: 'website' },
        { label: t.referral, value: 'referral' },
        { label: t.social, value: 'social' },
        { label: t.advertising, value: 'advertising' },
        { label: t.event, value: 'event' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'name',
      label: t.name,
      type: 'text',
      required: true
    },
    {
      name: 'nameAr',
      label: t.name + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'company',
      label: t.company,
      type: 'text',
      required: true
    },
    {
      name: 'companyAr',
      label: t.company + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'email',
      label: t.email,
      type: 'email',
      required: true
    },
    {
      name: 'phone',
      label: t.phone,
      type: 'tel',
      required: true
    },
    {
      name: 'lastContact',
      label: t.lastContact,
      type: 'date',
      required: true
    },
    {
      name: 'nextFollowUp',
      label: t.nextFollowUp,
      type: 'date',
      required: true
    },
    {
      name: 'salesValue',
      label: t.salesValue,
      type: 'number',
      required: true,
      min: 0
    },
    {
      name: 'opportunities',
      label: t.opportunities,
      type: 'number',
      required: true,
      min: 0
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { label: t.hot, value: 'hot' },
        { label: t.warm, value: 'warm' },
        { label: t.cold, value: 'cold' },
        { label: t.qualified, value: 'qualified' }
      ]
    },
    {
      name: 'leadScore',
      label: t.leadScore,
      type: 'number',
      required: true,
      min: 0,
      max: 100
    },
    {
      name: 'stage',
      label: t.stage,
      type: 'select',
      required: true,
      options: [
        { label: t.contacted, value: 'contacted' },
        { label: t.qualified, value: 'qualified' },
        { label: t.proposal, value: 'proposal' },
        { label: t.negotiation, value: 'negotiation' },
        { label: t.closed, value: 'closed' }
      ]
    },
    {
      name: 'industry',
      label: t.industry,
      type: 'text'
    },
    {
      name: 'industryAr',
      label: t.industry + ' (عربي)',
      type: 'text'
    },
    {
      name: 'source',
      label: t.source,
      type: 'select',
      required: true,
      options: [
        { label: t.website, value: 'website' },
        { label: t.referral, value: 'referral' },
        { label: t.social, value: 'social' },
        { label: t.advertising, value: 'advertising' },
        { label: t.event, value: 'event' }
      ]
    },
    {
      name: 'assignedTo',
      label: t.assignedTo,
      type: 'text',
      required: true
    },
    {
      name: 'assignedToAr',
      label: t.assignedTo + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'notes',
      label: t.notes,
      type: 'textarea'
    },
    {
      name: 'notesAr',
      label: t.notes + ' (عربي)',
      type: 'textarea'
    }
  ]

  // Event handlers
  const handleCreate = (): void => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = (): void => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<SalesCustomer>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async () => {
    try {
      await exportData('csv')
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.salesCustomers}
        data={customers}
        columns={columns}
        actions={actions as unknown}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addCustomer : modalMode === 'edit' ? t.editCustomer : t.view}
        fields={formFields}
        initialData={selectedItem as unknown}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
