/**
 * Quotations Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '../../components/ui/badge'
import { toast } from 'react-hot-toast'
import {
  FileText,
  Calculator,
  Calendar,
  DollarSign,
  Eye,
  Edit,
  Trash2,
  User,
  Send,
  Copy,
  Mail,
  Download
} from 'lucide-react'
import { useCrud } from '../../hooks/useCrud'
import { quotationService } from '../../services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '../../components/common/CrudTable'
import CrudModal, { FormField } from '../../components/common/CrudModal'

interface QuotationsProps {
  language: 'ar' | 'en'
}

interface Quotation {
  id: number
  quotation_number: string
  customer_id: number
  customer_name: string
  total_amount: number
  status: 'draft' | 'sent' | 'viewed' | 'accepted' | 'rejected' | 'expired'
  valid_until: string
  created_date: string
  items_count: number
  discount: number
  tax: number
  notes?: string
}

const translations = {
  ar: {
    quotations: 'عروض الأسعار',
    addQuotation: 'إضافة عرض سعر',
    editQuotation: 'تعديل عرض السعر',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    send: 'إرسال',
    copy: 'نسخ',
    confirmDelete: 'هل أنت متأكد من حذف عرض السعر هذا؟',
    searchPlaceholder: 'البحث في عروض الأسعار...',
    quotationNumber: 'رقم عرض السعر',
    customer: 'العميل',
    totalAmount: 'إجمالي المبلغ',
    status: 'الحالة',
    validUntil: 'صالح حتى',
    createdDate: 'تاريخ الإنشاء',
    itemsCount: 'عدد العناصر',
    discount: 'الخصم',
    tax: 'الضريبة',
    notes: 'الملاحظات',
    draft: 'مسودة',
    sent: 'مرسل',
    viewed: 'تم العرض',
    accepted: 'مقبول',
    rejected: 'مرفوض',
    expired: 'منتهي الصلاحية'
  },
  en: {
    quotations: 'Quotations',
    addQuotation: 'Add Quotation',
    editQuotation: 'Edit Quotation',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    send: 'Send',
    copy: 'Copy',
    confirmDelete: 'Are you sure you want to delete this quotation?',
    searchPlaceholder: 'Search quotations...',
    quotationNumber: 'Quotation Number',
    customer: 'Customer',
    totalAmount: 'Total Amount',
    status: 'Status',
    validUntil: 'Valid Until',
    createdDate: 'Created Date',
    itemsCount: 'Items Count',
    discount: 'Discount',
    tax: 'Tax',
    notes: 'Notes',
    draft: 'Draft',
    sent: 'Sent',
    viewed: 'Viewed',
    accepted: 'Accepted',
    rejected: 'Rejected',
    expired: 'Expired'
  }
}

export default function Quotations({ language }: QuotationsProps): React.ReactElement {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: quotations,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<Quotation>({
    service: quotationService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getStatusColor = (status: string): void => {
    switch (status) {
      case 'draft':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      case 'sent':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'viewed':
        return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'accepted':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'rejected':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'expired':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount)
  }

  // Table columns configuration
  const columns: TableColumn<Quotation>[] = [
    {
      key: 'quotation_number',
      label: t.quotationNumber,
      sortable: true,
      render: (item: Quotation) => (
        <div className="flex items-center gap-2">
          <FileText className="h-4 w-4 text-blue-400" />
          <span className="text-white font-medium">{item.quotation_number}</span>
        </div>
      )
    },
    {
      key: 'customer_name',
      label: t.customer,
      sortable: true,
      render: (item: Quotation) => (
        <div className="flex items-center gap-2">
          <User className="h-4 w-4 text-purple-400" />
          <span className="text-white">{item.customer_name}</span>
        </div>
      )
    },
    {
      key: 'total_amount',
      label: t.totalAmount,
      sortable: true,
      render: (item: Quotation) => (
        <span className="text-white font-medium">{formatCurrency(item.total_amount)}</span>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: Quotation) => (
        <Badge className={getStatusColor(item.status)}>
          {t[item.status as keyof typeof t]}
        </Badge>
      )
    },
    {
      key: 'valid_until',
      label: t.validUntil,
      sortable: true,
      render: (item: Quotation) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-orange-400" />
          <span className="text-white/80">{item.valid_until}</span>
        </div>
      )
    },
    {
      key: 'items_count',
      label: t.itemsCount,
      sortable: true,
      render: (item: Quotation) => (
        <span className="text-white">{item.items_count}</span>
      )
    },
    {
      key: 'discount',
      label: t.discount,
      sortable: true,
      render: (item: Quotation) => (
        <span className="text-green-400">{item.discount}%</span>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: React.MouseEvent) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: React.MouseEvent) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.send,
      icon: Send,
      onClick: (item: any) => handleSendQuotation(item),
      variant: 'ghost',
      className: 'text-blue-400 hover:text-blue-300 hover:bg-blue-500/20',
      show: (item: any) => item.status !== 'sent'
    },
    {
      label: t.copy,
      icon: Copy,
      onClick: (item: any) => handleCopyQuotation(item),
      variant: 'ghost',
      className: 'text-green-400 hover:text-green-300 hover:bg-green-500/20'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: React.MouseEvent) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'status',
      label: t.status,
      options: [
        { label: t.draft, value: 'draft' },
        { label: t.sent, value: 'sent' },
        { label: t.viewed, value: 'viewed' },
        { label: t.accepted, value: 'accepted' },
        { label: t.rejected, value: 'rejected' },
        { label: t.expired, value: 'expired' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'quotation_number',
      label: t.quotationNumber,
      type: 'text',
      required: true
    },
    {
      name: 'customer_id',
      label: t.customer,
      type: 'select',
      required: true,
      options: [] // Should be populated with customer options
    },
    {
      name: 'total_amount',
      label: t.totalAmount,
      type: 'number',
      required: true,
      min: 0,
      step: 0.01
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { label: t.draft, value: 'draft' },
        { label: t.sent, value: 'sent' },
        { label: t.viewed, value: 'viewed' },
        { label: t.accepted, value: 'accepted' },
        { label: t.rejected, value: 'rejected' },
        { label: t.expired, value: 'expired' }
      ]
    },
    {
      name: 'valid_until',
      label: t.validUntil,
      type: 'date',
      required: true
    },
    {
      name: 'created_date',
      label: t.createdDate,
      type: 'date',
      required: true
    },
    {
      name: 'items_count',
      label: t.itemsCount,
      type: 'number',
      min: 1
    },
    {
      name: 'discount',
      label: t.discount,
      type: 'number',
      min: 0,
      max: 100,
      step: 0.01
    },
    {
      name: 'tax',
      label: t.tax,
      type: 'number',
      min: 0,
      step: 0.01
    },
    {
      name: 'notes',
      label: t.notes,
      type: 'textarea'
    }
  ]

  // Enhanced event handlers with proper functionality
  const handleCreate = (): void => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = (): void => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  // Send quotation handler
  const handleSendQuotation = async (quotation: Quotation) => {
    try {
      // Loading state managed by useCrud

      // Simulate sending quotation via email
      const response = await fetch(`/api/quotations/${quotation.id}/send/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`
        },
        body: JSON.stringify({
          recipient_email: (quotation as any).customer_email || '<EMAIL>',
          subject: `Quotation ${quotation.quotation_number}`,
          message: `Please find attached quotation ${quotation.quotation_number} for your review.`
        })
      })

      if (response.ok) {
        toast.success(
          language === 'ar'
            ? `تم إرسال عرض السعر ${quotation.quotation_number} بنجاح`
            : `Quotation ${quotation.quotation_number} sent successfully`
        )

        // Update quotation status to 'sent'
        await updateItem(quotation.id, { status: 'sent' })
      } else {
        throw new Error('Failed to send quotation')
      }
    } catch (error) {
      console.error('Send quotation error:', error)
      toast.error(
        language === 'ar'
          ? 'فشل في إرسال عرض السعر'
          : 'Failed to send quotation'
      )
    } finally {
      // Loading state managed by useCrud
    }
  }

  // Copy quotation handler
  const handleCopyQuotation = async (quotation: Quotation) => {
    try {
      // Loading state managed by useCrud

      // Create a copy of the quotation with new number
      const newQuotationNumber = `QUO-${Date.now()}`
      const quotationCopy = {
        ...quotation,
        quotation_number: newQuotationNumber,
        status: 'draft',
        created_date: new Date().toISOString().split('T')[0],
        valid_until: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // 30 days from now
      }

      delete (quotationCopy as any).id // Remove ID to create new record

      await createItem(quotationCopy as any)

      toast.success(
        language === 'ar'
          ? `تم نسخ عرض السعر كـ ${newQuotationNumber}`
          : `Quotation copied as ${newQuotationNumber}`
      )
    } catch (error) {
      console.error('Copy quotation error:', error)
      toast.error(
        language === 'ar'
          ? 'فشل في نسخ عرض السعر'
          : 'Failed to copy quotation'
      )
    } finally {
      // Loading state managed by useCrud
    }
  }

  const handleSave = async (data: Partial<Quotation>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async () => {
    try {
      await exportData('csv')
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.quotations}
        data={quotations}
        columns={columns}
        actions={actions as any}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addQuotation : modalMode === 'edit' ? t.editQuotation : t.view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
