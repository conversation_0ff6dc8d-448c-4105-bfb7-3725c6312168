/**
 * Sales Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  TrendingUp,
  DollarSign,
  Target,
  Users,
  Calendar,
  Eye,
  Edit,
  Trash2,
  BarChart3,
  User,
  Package,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  ShoppingCart
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { salesOrderService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface SalesOrder {
  id: string
  order_number: string
  customer_name: string
  customer_name_ar: string
  amount: number
  date: string
  due_date: string
  sales_person: string
  sales_person_ar: string
  status: 'pending' | 'confirmed' | 'shipped' | 'delivered' | 'cancelled'
  priority: 'low' | 'medium' | 'high'
  items_count: number
  discount: number
  tax: number
  total_amount: number
}

interface SalesProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    sales: 'المبيعات',
    searchPlaceholder: 'البحث في المبيعات...',
    addSale: 'إضافة مبيعة',
    editSale: 'تعديل المبيعة',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذه المبيعة؟',
    orderNumber: 'رقم الطلب',
    customer: 'العميل',
    amount: 'المبلغ',
    date: 'التاريخ',
    dueDate: 'تاريخ الاستحقاق',
    salesPerson: 'مندوب المبيعات',
    status: 'الحالة',
    priority: 'الأولوية',
    itemsCount: 'عدد العناصر',
    discount: 'الخصم',
    tax: 'الضريبة',
    totalAmount: 'المبلغ الإجمالي',
    notes: 'ملاحظات',
    pending: 'معلق',
    confirmed: 'مؤكد',
    shipped: 'تم الشحن',
    delivered: 'تم التسليم',
    cancelled: 'ملغي',
    low: 'منخفض',
    medium: 'متوسط',
    high: 'عالي',
    all: 'الكل'
  },
  en: {
    sales: 'Sales',
    searchPlaceholder: 'Search sales...',
    addSale: 'Add Sale',
    editSale: 'Edit Sale',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this sale?',
    orderNumber: 'Order Number',
    customer: 'Customer',
    amount: 'Amount',
    date: 'Date',
    dueDate: 'Due Date',
    salesPerson: 'Sales Person',
    status: 'Status',
    priority: 'Priority',
    itemsCount: 'Items Count',
    discount: 'Discount',
    tax: 'Tax',
    totalAmount: 'Total Amount',
    notes: 'Notes',
    pending: 'Pending',
    confirmed: 'Confirmed',
    shipped: 'Shipped',
    delivered: 'Delivered',
    cancelled: 'Cancelled',
    low: 'Low',
    medium: 'Medium',
    high: 'High',
    all: 'All'
  }
}

export default function Sales({ language }: SalesProps as any): (React as any).ReactElement {
  const [showModal, setShowModal] = useState(false as any)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: sales,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<SalesOrder>({
    service: salesOrderService,
    autoLoad: true,
    pageSize: 20
  })

  // Status badge helper
  const getStatusBadge = (status: string): void => {
    const statusConfig = {
      pending: { color: 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30', icon: Clock },
      confirmed: { color: 'bg-green-500/20 text-green-400 border-green-500/30', icon: CheckCircle },
      shipped: { color: 'bg-blue-500/20 text-blue-400 border-blue-500/30', icon: Package },
      delivered: { color: 'bg-green-500/20 text-green-400 border-green-500/30', icon: CheckCircle },
      cancelled: { color: 'bg-red-500/20 text-red-400 border-red-500/30', icon: XCircle }
    }

    const config = statusConfig[status as keyof typeof statusConfig] || (statusConfig as any).pending
    const Icon = (config as any).icon

    return (
      <Badge className={`${(config as any).color} border`}>
        <Icon className="h-3 w-3 mr-1" />
        {t[status as keyof typeof t] || status}
      </Badge>
    )
  }

  // Priority badge helper
  const getPriorityBadge = (priority: string): void => {
    const priorityConfig = {
      low: { color: 'bg-gray-500/20 text-gray-400 border-gray-500/30' },
      medium: { color: 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30' },
      high: { color: 'bg-red-500/20 text-red-400 border-red-500/30' }
    }

    const config = priorityConfig[priority as keyof typeof priorityConfig] || (priorityConfig as any).medium

    return (
      <Badge className={`${(config as any).color} border`}>
        {t[priority as keyof typeof t] || priority}
      </Badge>
    )
  }

  // Table columns configuration
  const columns: TableColumn<SalesOrder>[] = [
    {
      key: 'order_number',
      label: (t as any).orderNumber,
      sortable: true,
      render: (item: SalesOrder) => (
        <div className="flex items-center gap-2">
          <ShoppingCart className="h-4 w-4 text-blue-400" />
          <span className="font-medium text-white">{(item as any).order_number}</span>
        </div>
      )
    },
    {
      key: 'customer_name',
      label: (t as any).customer,
      sortable: true,
      render: (item: SalesOrder) => (
        <div className="flex items-center gap-2">
          <User className="h-4 w-4 text-green-400" />
          <span className="text-white">{language === 'ar' ? (item as any).customer_name_ar : (item as any).customer_name}</span>
        </div>
      )
    },
    {
      key: 'total_amount',
      label: (t as any).totalAmount,
      sortable: true,
      render: (item: SalesOrder) => (
        <div className="flex items-center gap-2">
          <DollarSign className="h-4 w-4 text-green-400" />
          <span className="font-medium text-white">
            {new (Intl as any).NumberFormat('ar-SA', {
              style: 'currency',
              currency: 'SAR'
            } as any).format((item as any as any).total_amount)}
          </span>
        </div>
      )
    },
    {
      key: 'date',
      label: (t as any).date,
      sortable: true,
      render: (item: SalesOrder) => (
        <div className="flex items-center gap-2">
          <Calendar className="h-4 w-4 text-blue-400" />
          <span className="text-white">{new Date((item as any as any).date).toLocaleDateString( as any)}</span>
        </div>
      )
    },
    {
      key: 'status',
      label: (t as any).status,
      sortable: true,
      render: (item: SalesOrder) => getStatusBadge((item as any as any).status)
    },
    {
      key: 'priority',
      label: (t as any).priority,
      sortable: true,
      render: (item: SalesOrder) => getPriorityBadge((item as any as any).priority)
    },
    {
      key: 'sales_person',
      label: (t as any).salesPerson,
      sortable: true,
      render: (item: SalesOrder) => (
        <div className="flex items-center gap-2">
          <User className="h-4 w-4 text-purple-400" />
          <span className="text-white">{language === 'ar' ? (item as any).sales_person_ar : (item as any).sales_person}</span>
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction[] = [
    {
      label: (t as any).view,
      icon: Eye,
      onClick: (item: (React as any).MouseEvent) => {
        selectItem(item as any)
        setModalMode('view' as any)
        setShowModal(true as any)
      },
      variant: 'ghost'
    },
    {
      label: (t as any).edit,
      icon: Edit,
      onClick: (item: (React as any).MouseEvent) => {
        selectItem(item as any)
        setModalMode('edit' as any)
        setShowModal(true as any)
      },
      variant: 'ghost'
    },
    {
      label: (t as any).delete,
      icon: Trash2,
      onClick: async (item: (React as any).MouseEvent) => {
        if ((window as any).confirm((t as any as any).confirmDelete)) {
          await deleteItem((item as any as any).id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options configuration
  const filterOptions: FilterOption[] = [
    {
      key: 'status',
      label: (t as any).status,
      options: [
        { label: (t as any).all, value: '' },
        { label: (t as any).pending, value: 'pending' },
        { label: (t as any).confirmed, value: 'confirmed' },
        { label: (t as any).shipped, value: 'shipped' },
        { label: (t as any).delivered, value: 'delivered' },
        { label: (t as any).cancelled, value: 'cancelled' }
      ]
    },
    {
      key: 'priority',
      label: (t as any).priority,
      options: [
        { label: (t as any).all, value: '' },
        { label: (t as any).low, value: 'low' },
        { label: (t as any).medium, value: 'medium' },
        { label: (t as any).high, value: 'high' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'order_number',
      label: (t as any).orderNumber,
      type: 'text',
      required: true
    },
    {
      name: 'customer_name',
      label: (t as any).customer,
      type: 'text',
      required: true
    },
    {
      name: 'customer_name_ar',
      label: `${(t as any).customer} (عربي)`,
      type: 'text'
    },
    {
      name: 'amount',
      label: (t as any).amount,
      type: 'number',
      min: 0,
      step: (0 as any).01,
      required: true
    },
    {
      name: 'date',
      label: (t as any).date,
      type: 'date',
      required: true
    },
    {
      name: 'due_date',
      label: (t as any).dueDate,
      type: 'date'
    },
    {
      name: 'sales_person',
      label: (t as any).salesPerson,
      type: 'text',
      required: true
    },
    {
      name: 'sales_person_ar',
      label: `${(t as any).salesPerson} (عربي)`,
      type: 'text'
    },
    {
      name: 'status',
      label: (t as any).status,
      type: 'select',
      required: true,
      options: [
        { label: (t as any).pending, value: 'pending' },
        { label: (t as any).confirmed, value: 'confirmed' },
        { label: (t as any).shipped, value: 'shipped' },
        { label: (t as any).delivered, value: 'delivered' },
        { label: (t as any).cancelled, value: 'cancelled' }
      ]
    },
    {
      name: 'priority',
      label: (t as any).priority,
      type: 'select',
      required: true,
      options: [
        { label: (t as any).low, value: 'low' },
        { label: (t as any).medium, value: 'medium' },
        { label: (t as any).high, value: 'high' }
      ]
    },
    {
      name: 'items_count',
      label: (t as any).itemsCount,
      type: 'number',
      min: 1
    },
    {
      name: 'discount',
      label: (t as any).discount,
      type: 'number',
      min: 0,
      step: (0 as any).01
    },
    {
      name: 'tax',
      label: (t as any).tax,
      type: 'number',
      min: 0,
      step: (0 as any).01
    },
    {
      name: 'total_amount',
      label: (t as any).totalAmount,
      type: 'number',
      min: 0,
      step: (0 as any).01,
      required: true
    },
    {
      name: 'notes',
      label: (t as any).notes,
      type: 'textarea'
    }
  ]

  // Event handlers
  const handleCreate = (): void => {
    setModalMode('create' as any)
    setShowModal(true as any)
  }

  const handleModalClose = (): void => {
    setShowModal(false as any)
    selectItem(null as any)
    clearError( as any)
  }

  const handleSave = async (data: Partial<SalesOrder>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data as any)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem((selectedItem as any as any).id, data)
      }
      setShowModal(false as any)
    } catch (error) {
      (console as any).error('Save error:', error as any)
    }
  }

  const handleExport = async (format: 'csv' | 'excel' | 'pdf' = 'csv') => {
    try {
      if (format === 'pdf') {
        // Use new PDF generation API for sales reports
        const response = await fetch(`http://localhost:8000/api/pdf/generate/sales-report/?language=${language}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${(localStorage as any as any).getItem('auth_token' as any) || ''}`,
          }
        })

        if (!(response as any).ok) {
          throw new Error(`HTTP error! status: ${(response as any as any).status}`)
        }

        const pdfBlob = await (response as any).blob( as any)
        const url = (window as any).URL.createObjectURL(pdfBlob as any)
        const link = (document as any).createElement('a' as any)
        (link as any).href = url
        (link as any).download = `sales-report-${language}-${new Date( as any).toISOString( as any).split('T' as any)[0]}.pdf`
        (document as any).body.appendChild(link as any)
        (link as any).click( as any)
        (document as any).body.removeChild(link as any)
        (window as any).URL.revokeObjectURL(url as any)
      } else {
        await exportData(format as any)
      }
    } catch (error) {
      (console as any).error('Export error:', error as any)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={(t as any).sales}
        data={sales}
        columns={columns}
        actions={actions as any}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={(t as any).searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? (t as any).addSale : modalMode === 'edit' ? (t as any).editSale : (t as any).view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
