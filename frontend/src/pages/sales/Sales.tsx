import { useState, useEffect } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  ShoppingCart,
  TrendingUp,
  DollarSign,
  Target,
  Plus,
  Search,
  Filter,
  Download,
  Eye,
  Edit,
  Calendar,
  Clock,
  CheckCircle,
  AlertTriangle,
  User,
  Building,
  MoreHorizontal
} from 'lucide-react'

interface SalesProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    title: 'إدارة المبيعات',
    subtitle: 'إدارة شاملة للمبيعات والطلبات والعروض',
    totalSales: 'إجمالي المبيعات',
    monthlySales: 'مبيعات الشهر',
    salesTarget: 'هدف المبيعات',
    conversionRate: 'معدل التحويل',
    searchSales: 'البحث في المبيعات...',
    addSale: 'إضافة مبيعة',
    exportData: 'تصدير البيانات',
    filterBy: 'تصفية حسب',
    status: 'الحالة',
    salesPerson: 'مندوب المبيعات',
    salesList: 'قائمة المبيعات',
    orderNumber: 'رقم الطلب',
    customer: 'العميل',
    amount: 'المبلغ',
    date: 'التاريخ',
    dueDate: 'تاريخ الاستحقاق',
    actions: 'الإجراءات',
    viewDetails: 'عرض التفاصيل',
    editSale: 'تعديل المبيعة',
    pending: 'معلق',
    confirmed: 'مؤكد',
    delivered: 'تم التسليم',
    cancelled: 'ملغي',
    draft: 'مسودة',
    thisMonth: 'هذا الشهر',
    refresh: 'تحديث',
    salesAnalytics: 'تحليلات المبيعات',
    topProducts: 'أفضل المنتجات',
    salesTrends: 'اتجاهات المبيعات'
  },
  en: {
    title: 'Sales Management',
    subtitle: 'Comprehensive sales, orders, and quotations management',
    totalSales: 'Total Sales',
    monthlySales: 'Monthly Sales',
    salesTarget: 'Sales Target',
    conversionRate: 'Conversion Rate',
    searchSales: 'Search sales...',
    addSale: 'Add Sale',
    exportData: 'Export Data',
    filterBy: 'Filter By',
    status: 'Status',
    salesPerson: 'Sales Person',
    salesList: 'Sales List',
    orderNumber: 'Order Number',
    customer: 'Customer',
    amount: 'Amount',
    date: 'Date',
    dueDate: 'Due Date',
    actions: 'Actions',
    viewDetails: 'View Details',
    editSale: 'Edit Sale',
    pending: 'Pending',
    confirmed: 'Confirmed',
    delivered: 'Delivered',
    cancelled: 'Cancelled',
    draft: 'Draft',
    thisMonth: 'This Month',
    refresh: 'Refresh',
    salesAnalytics: 'Sales Analytics',
    topProducts: 'Top Products',
    salesTrends: 'Sales Trends'
  }
}

export default function Sales({ language }: SalesProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('')
  const [selectedSalesPerson, setSelectedSalesPerson] = useState('')
  const [refreshing, setRefreshing] = useState(false)
  const t = translations[language]
  const isRTL = language === 'ar'

  // Sales metrics
  const [salesMetrics, setSalesMetrics] = useState({
    totalSales: 2850000,
    monthlySales: 485000,
    salesTarget: 500000,
    conversionRate: 68.5
  })

  // Sample sales data
  const [sales] = useState([
    {
      id: 1,
      orderNumber: 'ORD-2024-001',
      customer: 'شركة التقنية المتقدمة',
      customerEn: 'Advanced Technology Company',
      amount: 125000,
      date: '2024-01-25',
      dueDate: '2024-02-10',
      status: 'confirmed',
      salesPerson: 'أحمد محمد',
      salesPersonEn: 'Ahmed Mohammed',
      products: ['خدمات تقنية', 'استشارات'],
      productsEn: ['Technical Services', 'Consulting']
    },
    {
      id: 2,
      orderNumber: 'ORD-2024-002',
      customer: 'مؤسسة النور للتجارة',
      customerEn: 'Al-Noor Trading Est.',
      amount: 85000,
      date: '2024-01-24',
      dueDate: '2024-02-08',
      status: 'pending',
      salesPerson: 'فاطمة علي',
      salesPersonEn: 'Fatima Ali',
      products: ['منتجات تجارية'],
      productsEn: ['Commercial Products']
    },
    {
      id: 3,
      orderNumber: 'ORD-2024-003',
      customer: 'شركة البناء الحديث',
      customerEn: 'Modern Construction Co.',
      amount: 195000,
      date: '2024-01-23',
      dueDate: '2024-02-15',
      status: 'delivered',
      salesPerson: 'عمر سالم',
      salesPersonEn: 'Omar Salem',
      products: ['مواد بناء', 'معدات'],
      productsEn: ['Construction Materials', 'Equipment']
    }
  ])

  const handleRefresh = async () => {
    setRefreshing(true)
    await new Promise(resolve => setTimeout(resolve, 1000))
    setRefreshing(false)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'bg-green-500/20 text-green-400 border-green-500/30'
      case 'pending':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
      case 'delivered':
        return 'bg-blue-500/20 text-blue-400 border-blue-500/30'
      case 'cancelled':
        return 'bg-red-500/20 text-red-400 border-red-500/30'
      case 'draft':
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30'
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'confirmed':
        return <CheckCircle className="h-4 w-4" />
      case 'pending':
        return <Clock className="h-4 w-4" />
      case 'delivered':
        return <CheckCircle className="h-4 w-4" />
      case 'cancelled':
        return <AlertTriangle className="h-4 w-4" />
      default:
        return <Clock className="h-4 w-4" />
    }
  }

  const filteredSales = sales.filter(sale => {
    const matchesSearch = sale.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         sale.customer.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         sale.customerEn.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = !selectedStatus || sale.status === selectedStatus
    const matchesSalesPerson = !selectedSalesPerson || sale.salesPerson === selectedSalesPerson
    
    return matchesSearch && matchesStatus && matchesSalesPerson
  })

  const salesMetricsCards = [
    {
      title: t.totalSales,
      value: new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR',
        notation: 'compact'
      }).format(salesMetrics.totalSales),
      change: '+18.5%',
      trend: 'up',
      icon: DollarSign,
      color: 'from-green-500 to-green-600'
    },
    {
      title: t.monthlySales,
      value: new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR',
        notation: 'compact'
      }).format(salesMetrics.monthlySales),
      change: '+12.3%',
      trend: 'up',
      icon: TrendingUp,
      color: 'from-blue-500 to-blue-600'
    },
    {
      title: t.salesTarget,
      value: new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR',
        notation: 'compact'
      }).format(salesMetrics.salesTarget),
      change: '97%',
      trend: 'stable',
      icon: Target,
      color: 'from-purple-500 to-purple-600'
    },
    {
      title: t.conversionRate,
      value: `${salesMetrics.conversionRate}%`,
      change: '+5.2%',
      trend: 'up',
      icon: ShoppingCart,
      color: 'from-orange-500 to-orange-600'
    }
  ]

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">{t.title}</h1>
          <p className="text-white/70">{t.subtitle}</p>
        </div>
        
        <div className="flex items-center gap-3">
          <Button
            onClick={handleRefresh}
            disabled={refreshing}
            className="text-white/80 hover:text-white hover:bg-white/15 rounded-xl p-3 backdrop-blur-10"
          >
            <Calendar className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            {t.refresh}
          </Button>
          <Button className="text-white/80 hover:text-white hover:bg-white/15 rounded-xl p-3 backdrop-blur-10">
            <Plus className="h-4 w-4 mr-2" />
            {t.addSale}
          </Button>
          <Button variant="outline" className="text-white/80 hover:text-white hover:bg-white/15 rounded-xl p-3 backdrop-blur-10">
            <Download className="h-4 w-4 mr-2" />
            {t.exportData}
          </Button>
        </div>
      </div>

      {/* Sales Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {salesMetricsCards.map((card, index) => (
          <Card key={index} className="glass-card border-white/20 hover:border-white/40 transition-all">
            <CardContent className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <p className="text-white/70 text-sm mb-1">{card.title}</p>
                  <p className="text-2xl font-bold text-white">{card.value}</p>
                </div>
                <div className={`p-3 rounded-xl bg-gradient-to-r ${card.color} shadow-lg`}>
                  <card.icon className="h-6 w-6 text-white" />
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <span className={`text-sm font-medium ${
                  card.trend === 'up' ? 'text-green-400' : 
                  card.trend === 'down' ? 'text-red-400' : 'text-blue-400'
                }`}>
                  {card.change}
                </span>
                <span className="text-xs text-white/60">vs last month</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Search and Filters */}
      <Card className="glass-card border-white/20">
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/60" />
              <Input
                placeholder={t.searchSales}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-white/10 border-white/20 text-white placeholder:text-white/60"
              />
            </div>
            
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="bg-white/10 border-white/20 text-white rounded-lg px-3 py-2"
            >
              <option value="">{t.status}</option>
              <option value="pending">{t.pending}</option>
              <option value="confirmed">{t.confirmed}</option>
              <option value="delivered">{t.delivered}</option>
              <option value="cancelled">{t.cancelled}</option>
            </select>
            
            <select
              value={selectedSalesPerson}
              onChange={(e) => setSelectedSalesPerson(e.target.value)}
              className="bg-white/10 border-white/20 text-white rounded-lg px-3 py-2"
            >
              <option value="">{t.salesPerson}</option>
              <option value="أحمد محمد">أحمد محمد</option>
              <option value="فاطمة علي">فاطمة علي</option>
              <option value="عمر سالم">عمر سالم</option>
            </select>
            
            <Button variant="outline" className="text-white/80 hover:text-white hover:bg-white/15 rounded-xl p-3 backdrop-blur-10">
              <Filter className="h-4 w-4 mr-2" />
              {t.filterBy}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Sales List */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white text-xl">{t.salesList}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-white/20">
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.orderNumber}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.customer}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.amount}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.date}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.dueDate}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.salesPerson}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.status}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.actions}</th>
                </tr>
              </thead>
              <tbody>
                {filteredSales.map((sale) => (
                  <tr key={sale.id} className="border-b border-white/10 hover:bg-white/5 transition-colors">
                    <td className="py-4 px-4">
                      <div>
                        <p className="text-white font-medium">{sale.orderNumber}</p>
                        <p className="text-white/60 text-sm">
                          {language === 'ar' ? sale.products.join(', ') : sale.productsEn.join(', ')}
                        </p>
                      </div>
                    </td>
                    <td className="py-4 px-4 text-white">
                      {language === 'ar' ? sale.customer : sale.customerEn}
                    </td>
                    <td className="py-4 px-4 text-white font-medium">
                      {new Intl.NumberFormat('ar-SA', {
                        style: 'currency',
                        currency: 'SAR'
                      }).format(sale.amount)}
                    </td>
                    <td className="py-4 px-4 text-white">{sale.date}</td>
                    <td className="py-4 px-4 text-white">{sale.dueDate}</td>
                    <td className="py-4 px-4 text-white">
                      {language === 'ar' ? sale.salesPerson : sale.salesPersonEn}
                    </td>
                    <td className="py-4 px-4">
                      <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(sale.status)}`}>
                        {getStatusIcon(sale.status)}
                        {t[sale.status as keyof typeof t]}
                      </span>
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex items-center gap-2">
                        <Button size="sm" variant="outline" className="text-white/80 hover:text-white hover:bg-white/15 rounded-lg p-2 backdrop-blur-10">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="outline" className="text-white/80 hover:text-white hover:bg-white/15 rounded-lg p-2 backdrop-blur-10">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="outline" className="text-white/80 hover:text-white hover:bg-white/15 rounded-lg p-2 backdrop-blur-10">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
