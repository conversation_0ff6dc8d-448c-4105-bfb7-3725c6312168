import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Badge } from '../../components/ui/badge'
import {
  ShoppingCart,
  Plus,
  Search,
  Filter,
  Eye,
  Edit,
  Trash2,
  Download,
  Calendar,
  DollarSign,
  TrendingUp
} from 'lucide-react'

interface SalesOrdersProps {
  language: 'ar' | 'en'
}

interface SalesOrder {
  id: string
  orderNumber: string
  customer: string
  customerAr: string
  amount: number
  status: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled'
  orderDate: string
  deliveryDate: string
  items: number
  priority: 'low' | 'medium' | 'high' | 'urgent'
}

const mockSalesOrders: SalesOrder[] = [
  {
    id: '1',
    orderNumber: 'SO-2024-001',
    customer: 'ABC Company',
    customerAr: 'شركة أي بي سي',
    amount: 15000,
    status: 'confirmed',
    orderDate: '2024-01-15',
    deliveryDate: '2024-01-25',
    items: 5,
    priority: 'high'
  },
  {
    id: '2',
    orderNumber: 'SO-2024-002',
    customer: 'XYZ Corporation',
    customerAr: 'مؤسسة إكس واي زد',
    amount: 8500,
    status: 'processing',
    orderDate: '2024-01-16',
    deliveryDate: '2024-01-30',
    items: 3,
    priority: 'medium'
  },
  {
    id: '3',
    orderNumber: 'SO-2024-003',
    customer: 'Tech Solutions Ltd',
    customerAr: 'شركة الحلول التقنية المحدودة',
    amount: 22000,
    status: 'pending',
    orderDate: '2024-01-17',
    deliveryDate: '2024-02-05',
    items: 8,
    priority: 'urgent'
  }
]

export default function SalesOrders({ language }: SalesOrdersProps) {
  const [orders, setOrders] = useState<SalesOrder[]>(mockSalesOrders)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [isLoading, setIsLoading] = useState(false)

  const text = {
    ar: {
      title: 'أوامر المبيعات',
      description: 'إدارة ومتابعة أوامر المبيعات',
      newOrder: 'أمر جديد',
      search: 'البحث في الأوامر...',
      filter: 'تصفية',
      orderNumber: 'رقم الأمر',
      customer: 'العميل',
      amount: 'المبلغ',
      status: 'الحالة',
      orderDate: 'تاريخ الأمر',
      deliveryDate: 'تاريخ التسليم',
      items: 'العناصر',
      priority: 'الأولوية',
      actions: 'الإجراءات',
      view: 'عرض',
      edit: 'تعديل',
      delete: 'حذف',
      export: 'تصدير',
      totalOrders: 'إجمالي الأوامر',
      totalValue: 'إجمالي القيمة',
      avgOrderValue: 'متوسط قيمة الأمر',
      pending: 'معلق',
      confirmed: 'مؤكد',
      processing: 'قيد المعالجة',
      shipped: 'تم الشحن',
      delivered: 'تم التسليم',
      cancelled: 'ملغي',
      low: 'منخفض',
      medium: 'متوسط',
      high: 'عالي',
      urgent: 'عاجل'
    },
    en: {
      title: 'Sales Orders',
      description: 'Manage and track sales orders',
      newOrder: 'New Order',
      search: 'Search orders...',
      filter: 'Filter',
      orderNumber: 'Order Number',
      customer: 'Customer',
      amount: 'Amount',
      status: 'Status',
      orderDate: 'Order Date',
      deliveryDate: 'Delivery Date',
      items: 'Items',
      priority: 'Priority',
      actions: 'Actions',
      view: 'View',
      edit: 'Edit',
      delete: 'Delete',
      export: 'Export',
      totalOrders: 'Total Orders',
      totalValue: 'Total Value',
      avgOrderValue: 'Avg Order Value',
      pending: 'Pending',
      confirmed: 'Confirmed',
      processing: 'Processing',
      shipped: 'Shipped',
      delivered: 'Delivered',
      cancelled: 'Cancelled',
      low: 'Low',
      medium: 'Medium',
      high: 'High',
      urgent: 'Urgent'
    }
  }

  const t = text[language]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30'
      case 'confirmed': return 'bg-blue-500/20 text-blue-300 border-blue-500/30'
      case 'processing': return 'bg-purple-500/20 text-purple-300 border-purple-500/30'
      case 'shipped': return 'bg-orange-500/20 text-orange-300 border-orange-500/30'
      case 'delivered': return 'bg-green-500/20 text-green-300 border-green-500/30'
      case 'cancelled': return 'bg-red-500/20 text-red-300 border-red-500/30'
      default: return 'bg-gray-500/20 text-gray-300 border-gray-500/30'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low': return 'bg-green-500/20 text-green-300 border-green-500/30'
      case 'medium': return 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30'
      case 'high': return 'bg-orange-500/20 text-orange-300 border-orange-500/30'
      case 'urgent': return 'bg-red-500/20 text-red-300 border-red-500/30'
      default: return 'bg-gray-500/20 text-gray-300 border-gray-500/30'
    }
  }

  const filteredOrders = orders.filter(order => {
    const matchesSearch = order.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.customer.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.customerAr.includes(searchTerm)
    const matchesStatus = statusFilter === 'all' || order.status === statusFilter
    return matchesSearch && matchesStatus
  })

  const totalValue = orders.reduce((sum, order) => sum + order.amount, 0)
  const avgOrderValue = orders.length > 0 ? totalValue / orders.length : 0

  return (
    <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 sm:gap-6 mb-6 sm:mb-8">
          <div>
            <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-white drop-shadow-lg">
              {t.title}
            </h1>
            <p className="text-white/80 mt-1 sm:mt-2 text-sm sm:text-base lg:text-lg">
              {t.description}
            </p>
          </div>
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-3 w-full sm:w-auto">
            <Button className="glass text-white border-white/30 hover:bg-white/20 px-3 sm:px-4 py-2 text-sm sm:text-base">
              <Download className="w-4 h-4 mr-2" />
              {t.export}
            </Button>
            <Button className="gradient-bg-blue hover:scale-105 transform transition-all duration-300 glow-hover text-white font-semibold px-4 sm:px-6 py-2 sm:py-3 text-sm sm:text-base">
              <Plus className="h-4 w-4 sm:h-5 sm:w-5 mr-2" />
              {t.newOrder}
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
          <Card className="modern-card border-0 hover:scale-105 transition-all duration-300">
            <CardContent className="p-4 sm:p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs sm:text-sm font-medium text-white/80">
                    {t.totalOrders}
                  </p>
                  <p className="text-2xl sm:text-3xl font-bold text-white mt-1 sm:mt-2">
                    {orders.length}
                  </p>
                </div>
                <div className="p-3 sm:p-4 rounded-xl sm:rounded-2xl gradient-bg-blue glow floating">
                  <ShoppingCart className="h-5 w-5 sm:h-6 sm:w-6 lg:h-7 lg:w-7 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="modern-card border-0 hover:scale-105 transition-all duration-300">
            <CardContent className="p-4 sm:p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs sm:text-sm font-medium text-white/80">
                    {t.totalValue}
                  </p>
                  <p className="text-2xl sm:text-3xl font-bold text-white mt-1 sm:mt-2">
                    {totalValue.toLocaleString()} ر.س
                  </p>
                </div>
                <div className="p-3 sm:p-4 rounded-xl sm:rounded-2xl gradient-bg-purple glow floating">
                  <DollarSign className="h-5 w-5 sm:h-6 sm:w-6 lg:h-7 lg:w-7 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="modern-card border-0 hover:scale-105 transition-all duration-300">
            <CardContent className="p-4 sm:p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs sm:text-sm font-medium text-white/80">
                    {t.avgOrderValue}
                  </p>
                  <p className="text-2xl sm:text-3xl font-bold text-white mt-1 sm:mt-2">
                    {avgOrderValue.toLocaleString()} ر.س
                  </p>
                </div>
                <div className="p-3 sm:p-4 rounded-xl sm:rounded-2xl gradient-bg-alt glow floating">
                  <TrendingUp className="h-5 w-5 sm:h-6 sm:w-6 lg:h-7 lg:w-7 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card className="modern-card border-0">
          <CardContent className="p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60 h-4 w-4 sm:h-5 sm:w-5" />
                  <Input
                    placeholder={t.search}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="glass text-white placeholder-white/60 border-white/30 focus:border-white/50 pl-10 sm:pl-12 py-2 sm:py-3 text-sm sm:text-base"
                  />
                </div>
              </div>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="glass text-white border-white/30 focus:border-white/50 px-3 py-2 sm:py-3 rounded-lg text-sm sm:text-base min-w-[140px] sm:min-w-[160px]"
              >
                <option value="all">جميع الحالات</option>
                <option value="pending">{t.pending}</option>
                <option value="confirmed">{t.confirmed}</option>
                <option value="processing">{t.processing}</option>
                <option value="shipped">{t.shipped}</option>
                <option value="delivered">{t.delivered}</option>
                <option value="cancelled">{t.cancelled}</option>
              </select>
            </div>
          </CardContent>
        </Card>

        {/* Orders Table */}
        <Card className="modern-card border-0">
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full min-w-[800px]">
                <thead>
                  <tr className="border-b border-white/10">
                    <th className="text-right p-2 sm:p-4 text-white/70 font-medium text-xs sm:text-sm">{t.orderNumber}</th>
                    <th className="text-right p-2 sm:p-4 text-white/70 font-medium text-xs sm:text-sm">{t.customer}</th>
                    <th className="text-right p-2 sm:p-4 text-white/70 font-medium text-xs sm:text-sm">{t.amount}</th>
                    <th className="text-right p-2 sm:p-4 text-white/70 font-medium text-xs sm:text-sm">{t.status}</th>
                    <th className="text-right p-2 sm:p-4 text-white/70 font-medium text-xs sm:text-sm hidden md:table-cell">{t.priority}</th>
                    <th className="text-right p-2 sm:p-4 text-white/70 font-medium text-xs sm:text-sm hidden lg:table-cell">{t.orderDate}</th>
                    <th className="text-right p-2 sm:p-4 text-white/70 font-medium text-xs sm:text-sm hidden lg:table-cell">{t.items}</th>
                    <th className="text-right p-2 sm:p-4 text-white/70 font-medium text-xs sm:text-sm">{t.actions}</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredOrders.map((order) => (
                    <tr key={order.id} className="border-b border-white/5 hover:bg-white/5">
                      <td className="p-2 sm:p-4">
                        <span className="text-white font-medium text-xs sm:text-sm">{order.orderNumber}</span>
                      </td>
                      <td className="p-2 sm:p-4">
                        <div>
                          <span className="text-white text-xs sm:text-sm">{language === 'ar' ? order.customerAr : order.customer}</span>
                        </div>
                      </td>
                      <td className="p-2 sm:p-4">
                        <span className="text-white text-xs sm:text-sm">{order.amount.toLocaleString()} ر.س</span>
                      </td>
                      <td className="p-2 sm:p-4">
                        <Badge className={`${getStatusColor(order.status)} border text-xs`}>
                          {t[order.status as keyof typeof t]}
                        </Badge>
                      </td>
                      <td className="p-2 sm:p-4 hidden md:table-cell">
                        <Badge className={`${getPriorityColor(order.priority)} border text-xs`}>
                          {t[order.priority as keyof typeof t]}
                        </Badge>
                      </td>
                      <td className="p-2 sm:p-4 hidden lg:table-cell">
                        <span className="text-white/70 text-xs sm:text-sm">{order.orderDate}</span>
                      </td>
                      <td className="p-2 sm:p-4 hidden lg:table-cell">
                        <span className="text-white text-xs sm:text-sm">{order.items}</span>
                      </td>
                      <td className="p-2 sm:p-4">
                        <div className="flex gap-1 sm:gap-2">
                          <Button size="sm" variant="ghost" className="text-white/70 hover:text-white p-1 sm:p-2">
                            <Eye className="w-3 h-3 sm:w-4 sm:h-4" />
                          </Button>
                          <Button size="sm" variant="ghost" className="text-white/70 hover:text-white p-1 sm:p-2">
                            <Edit className="w-3 h-3 sm:w-4 sm:h-4" />
                          </Button>
                          <Button size="sm" variant="ghost" className="text-red-400 hover:text-red-300 p-1 sm:p-2">
                            <Trash2 className="w-3 h-3 sm:w-4 sm:h-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
    </div>
  )
}
