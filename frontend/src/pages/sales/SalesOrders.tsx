/**
 * Sales Orders Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState, useEffect, useCallback } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  ShoppingCart,
  Calendar,
  DollarSign,
  TrendingUp,
  Eye,
  Edit,
  Trash2,
  User,
  Package,
  RefreshCw
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { salesOrderService, customerService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'
import DeleteConfirmModal from '@/components/common/DeleteConfirmModal'
import { useDeleteConfirmation, getDeleteConfirmationMessage, getDeleteConfirmationTitle } from '@/utils/deleteConfirmationHelper'

interface SalesOrdersProps {
  language: 'ar' | 'en'
}

interface SalesOrder {
  id: number
  order_number: string
  customer: number
  customer_name?: string
  total_amount: number
  status: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  order_date: string
  delivery_date?: string
  items_count: number
  discount: number
  tax: number
  notes?: string
  created_at: string
  updated_at: string
}

interface Customer {
  id: number
  first_name: string
  last_name: string
  email: string
  phone?: string
  company_name?: string
  customer_type: string
  status: string
}

const translations = {
  ar: {
    salesOrders: 'أوامر المبيعات',
    addOrder: 'إضافة أمر',
    editOrder: 'تعديل الأمر',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذا الأمر؟',
    searchPlaceholder: 'البحث في أوامر المبيعات...',
    orderNumber: 'رقم الأمر',
    customer: 'العميل',
    totalAmount: 'إجمالي المبلغ',
    status: 'الحالة',
    priority: 'الأولوية',
    orderDate: 'تاريخ الأمر',
    deliveryDate: 'تاريخ التسليم',
    itemsCount: 'عدد العناصر',
    discount: 'الخصم',
    tax: 'الضريبة',
    notes: 'الملاحظات',
    pending: 'معلق',
    confirmed: 'مؤكد',
    processing: 'قيد المعالجة',
    shipped: 'تم الشحن',
    delivered: 'تم التسليم',
    cancelled: 'ملغي',
    priorities: {
      low: 'منخفض',
      medium: 'متوسط',
      high: 'عالي',
      urgent: 'عاجل'
    }
  },
  en: {
    salesOrders: 'Sales Orders',
    addOrder: 'Add Order',
    editOrder: 'Edit Order',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this order?',
    searchPlaceholder: 'Search sales orders...',
    orderNumber: 'Order Number',
    customer: 'Customer',
    totalAmount: 'Total Amount',
    status: 'Status',
    priority: 'Priority',
    orderDate: 'Order Date',
    deliveryDate: 'Delivery Date',
    itemsCount: 'Items Count',
    discount: 'Discount',
    tax: 'Tax',
    notes: 'Notes',
    pending: 'Pending',
    confirmed: 'Confirmed',
    processing: 'Processing',
    shipped: 'Shipped',
    delivered: 'Delivered',
    cancelled: 'Cancelled',
    priorities: {
      low: 'Low',
      medium: 'Medium',
      high: 'High',
      urgent: 'Urgent'
    }
  }
}

export default function SalesOrders({ language }: SalesOrdersProps as any): (React as any).ReactElement {
  const [showModal, setShowModal] = useState(false as any)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')
  const [customers, setCustomers] = useState<Customer[]>([])
  const [loadingCustomers, setLoadingCustomers] = useState(false as any)
  const [customerError, setCustomerError] = useState<string | null>(null)

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: salesOrders,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<SalesOrder>({
    service: salesOrderService,
    autoLoad: true,
    pageSize: 20
  })

  // Delete confirmation using helper hook
  const [deleteState, deleteActions] = useDeleteConfirmation<SalesOrder>({
    deleteItem,
    onDeleteSuccess: () => {
      (console as any).log('✅ Sales order deleted successfully' as any)
    },
    onDeleteError: (error) => {
      (console as any).error('❌ Failed to delete sales order:', error as any)
    }
  })

  // Retry customer loading
  const retryLoadCustomers = useCallback(async ( as any) => {
    setCustomerError(null as any)
    setLoadingCustomers(true as any)

    try {
      const response = await (customerService as any).getAll( as any)
      let customerData: Customer[] = []

      if ((Array as any).isArray(response as any)) {
        customerData = response
      } else if (response && typeof response === 'object') {
        if ('data' in response && (Array as any).isArray((response as any as any).data)) {
          customerData = (response as any).data as Customer[]
        } else if ('results' in response && (Array as any).isArray((response as any as any).results)) {
          customerData = (response as any).results as Customer[]
        }
      }

      setCustomers(customerData as any)

      if ((customerData as any).length === 0) {
        setCustomerError(language === 'ar'
          ? 'لا توجد عملاء متاحون. يرجى إضافة عملاء أولاً.'
          : 'No customers available. Please add customers first.'
         as any)
      }
    } catch (error) {
      (console as any).error('❌ Retry failed to load customers:', error as any)
      setCustomerError(language === 'ar'
        ? 'فشل في تحميل قائمة العملاء. يرجى المحاولة مرة أخرى.'
        : 'Failed to load customers list. Please try again.'
       as any)
    } finally {
      setLoadingCustomers(false as any)
    }
  }, [language])

  // Load customers for dropdown
  useEffect(( as any) => {
    const loadCustomers = async () => {
      try {
        setLoadingCustomers(true as any)
        setCustomerError(null as any) // Clear previous errors
        (console as any).log('🔍 Loading customers for dropdown...' as any)

        const response = await (customerService as any).getAll( as any)
        (console as any).log('📡 Customer service response:', response as any)

        // Handle both paginated and direct array responses
        let customerData: Customer[] = []

        if ((Array as any).isArray(response as any)) {
          customerData = response
        } else if (response && typeof response === 'object') {
          // Handle CrudResponse format
          if ('data' in response && (Array as any).isArray((response as any as any).data)) {
            customerData = (response as any).data as Customer[]
          } else if ('results' in response && (Array as any).isArray((response as any as any).results)) {
            customerData = (response as any).results as Customer[]
          } else {
            (console as any).warn('Unexpected customer response format:', response as any)
            setCustomerError(language === 'ar'
              ? 'تنسيق استجابة غير متوقع من الخادم'
              : 'Unexpected response format from server'
             as any)
          }
        }

        (console as any).log('✅ Processed customer data:', customerData as any)
        (console as any).log(`📊 Found ${(customerData as any as any).length} customers`)

        setCustomers(customerData as any)

        if ((customerData as any).length === 0) {
          (console as any).warn('⚠️ No customers found - dropdown will be empty' as any)
          setCustomerError(language === 'ar'
            ? 'لا توجد عملاء متاحون. يرجى إضافة عملاء أولاً.'
            : 'No customers available. Please add customers first.'
           as any)
        }
      } catch (error) {
        (console as any).error('❌ Failed to load customers:', error as any)
        setCustomers([] as any) // Set empty array on error

        // Set user-friendly error message
        let errorMessage = language === 'ar'
          ? 'فشل في تحميل قائمة العملاء. يرجى المحاولة مرة أخرى.'
          : 'Failed to load customers list. Please try again.'

        if (error instanceof Error) {
          (console as any).error('Customer loading error details:', (error as any as any).message)
          // Check for specific error types
          if ((error as any).message.includes('Network' as any)) {
            errorMessage = language === 'ar'
              ? 'خطأ في الشبكة. يرجى التحقق من اتصال الإنترنت.'
              : 'Network error. Please check your internet connection.'
          } else if ((error as any).message.includes('404' as any)) {
            errorMessage = language === 'ar'
              ? 'خدمة العملاء غير متاحة حالياً.'
              : 'Customer service is currently unavailable.'
          }
        }

        setCustomerError(errorMessage as any)
      } finally {
        setLoadingCustomers(false as any)
      }
    }

    loadCustomers( as any)
  }, [language]) // Add language dependency to reload on language change

  // Helper functions
  const getStatusColor = (status: string): void => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'confirmed':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'processing':
        return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'shipped':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'delivered':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getPriorityColor = (priority: string): void => {
    switch (priority) {
      case 'low':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'urgent':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const formatCurrency = (amount: number): string => {
    return new (Intl as any).NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    } as any).format(amount as any)
  }

  // Table columns configuration
  const columns: TableColumn<SalesOrder>[] = [
    {
      key: 'order_number',
      label: (t as any).orderNumber,
      sortable: true,
      render: (item: SalesOrder) => (
        <div className="flex items-center gap-2">
          <ShoppingCart className="h-4 w-4 text-blue-400" />
          <span className="text-white font-medium">{(item as any).order_number}</span>
        </div>
      )
    },
    {
      key: 'customer_name',
      label: (t as any).customer,
      sortable: true,
      render: (item: SalesOrder) => (
        <div className="flex items-center gap-2">
          <User className="h-4 w-4 text-purple-400" />
          <span className="text-white">{(item as any).customer_name || 'Unknown Customer'}</span>
        </div>
      )
    },
    {
      key: 'total_amount',
      label: (t as any).totalAmount,
      sortable: true,
      render: (item: SalesOrder) => (
        <span className="text-white font-medium">{formatCurrency((item as any as any).total_amount)}</span>
      )
    },
    {
      key: 'status',
      label: (t as any).status,
      sortable: true,
      render: (item: SalesOrder) => (
        <Badge className={getStatusColor((item as any as any).status)}>
          {String(t[(item as any as any).status as keyof typeof t])}
        </Badge>
      )
    },
    {
      key: 'priority',
      label: (t as any).priority,
      sortable: true,
      render: (item: SalesOrder) => (
        <Badge className={getPriorityColor((item as any as any).priority)}>
          {(t as any).priorities[(item as any).priority as keyof typeof (t as any).priorities]}
        </Badge>
      )
    },
    {
      key: 'order_date',
      label: (t as any).orderDate,
      sortable: true,
      render: (item: SalesOrder) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">{(item as any).order_date}</span>
        </div>
      )
    },
    {
      key: 'items_count',
      label: (t as any).itemsCount,
      sortable: true,
      render: (item: SalesOrder) => (
        <div className="flex items-center gap-1">
          <Package className="h-3 w-3 text-green-400" />
          <span className="text-white">{(item as any).items_count}</span>
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction[] = [
    {
      label: (t as any).view,
      icon: Eye,
      onClick: (item: (React as any).MouseEvent) => {
        selectItem(item as any)
        setModalMode('view' as any)
        setShowModal(true as any)
      },
      variant: 'ghost'
    },
    {
      label: (t as any).edit,
      icon: Edit,
      onClick: (item: (React as any).MouseEvent) => {
        selectItem(item as any)
        setModalMode('edit' as any)
        setShowModal(true as any)
      },
      variant: 'ghost'
    },
    {
      label: (t as any).delete,
      icon: Trash2,
      onClick: (item: any) => (deleteActions as any).handleDeleteClick(item as SalesOrder as any),
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'status',
      label: (t as any).status,
      options: [
        { label: (t as any).pending, value: 'pending' },
        { label: (t as any).confirmed, value: 'confirmed' },
        { label: (t as any).processing, value: 'processing' },
        { label: (t as any).shipped, value: 'shipped' },
        { label: (t as any).delivered, value: 'delivered' },
        { label: (t as any).cancelled, value: 'cancelled' }
      ]
    },
    {
      key: 'priority',
      label: (t as any).priority,
      options: [
        { label: (t as any).priorities.low, value: 'low' },
        { label: (t as any).priorities.medium, value: 'medium' },
        { label: (t as any).priorities.high, value: 'high' },
        { label: (t as any).priorities.urgent, value: 'urgent' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'order_number',
      label: (t as any).orderNumber,
      type: 'text',
      required: true
    },
    {
      name: 'customer',
      label: (t as any).customer,
      type: 'select',
      required: true,
      loading: loadingCustomers,
      options: (customers || []).map(customer => ({
        label: (customer as any as any).company_name || `${(customer as any).first_name} ${(customer as any).last_name}`.trim( as any),
        value: (customer as any).id.toString( as any)
      })),
      validation: (value) => {
        if (!value || value === '') {
          return language === 'ar' ? 'يجب اختيار عميل' : 'Customer is required'
        }
        // Check if the selected customer ID exists in the customers list
        const customerExists = (customers as any).some(customer => (customer as any as any).id.toString( as any) === (value as any).toString( as any))
        if (!customerExists) {
          return language === 'ar' ? 'العميل المحدد غير صحيح' : 'Selected customer is invalid'
        }
        return null
      },
      hint: customerError || (loadingCustomers
        ? (language === 'ar' ? 'جاري تحميل العملاء...' : 'Loading customers...')
        : (customers as any).length === 0
        ? (language === 'ar' ? 'لا توجد عملاء متاحون' : 'No customers available')
        : undefined
      ),
      disabled: !!customerError || (customers as any).length === 0,
      customAction: customerError ? {
        label: language === 'ar' ? 'إعادة المحاولة' : 'Retry',
        onClick: retryLoadCustomers,
        icon: RefreshCw,
        variant: 'secondary' as const
      } : undefined
    },
    {
      name: 'total_amount',
      label: (t as any).totalAmount,
      type: 'number',
      required: true,
      min: 0,
      step: (0 as any).01
    },
    {
      name: 'status',
      label: (t as any).status,
      type: 'select',
      required: true,
      options: [
        { label: (t as any).pending, value: 'pending' },
        { label: (t as any).confirmed, value: 'confirmed' },
        { label: (t as any).processing, value: 'processing' },
        { label: (t as any).shipped, value: 'shipped' },
        { label: (t as any).delivered, value: 'delivered' },
        { label: (t as any).cancelled, value: 'cancelled' }
      ]
    },
    {
      name: 'priority',
      label: (t as any).priority,
      type: 'select',
      required: true,
      options: [
        { label: (t as any).priorities.low, value: 'low' },
        { label: (t as any).priorities.medium, value: 'medium' },
        { label: (t as any).priorities.high, value: 'high' },
        { label: (t as any).priorities.urgent, value: 'urgent' }
      ]
    },
    {
      name: 'order_date',
      label: (t as any).orderDate,
      type: 'date',
      required: true
    },
    {
      name: 'delivery_date',
      label: (t as any).deliveryDate,
      type: 'date'
    },
    {
      name: 'items_count',
      label: (t as any).itemsCount,
      type: 'number',
      min: 1
    },
    {
      name: 'discount',
      label: (t as any).discount,
      type: 'number',
      min: 0,
      step: (0 as any).01
    },
    {
      name: 'tax',
      label: (t as any).tax,
      type: 'number',
      min: 0,
      step: (0 as any).01
    },
    {
      name: 'notes',
      label: (t as any).notes,
      type: 'textarea'
    }
  ]

  // Event handlers
  const handleCreate = (): void => {
    setModalMode('create' as any)
    setShowModal(true as any)
  }

  const handleModalClose = (): void => {
    setShowModal(false as any)
    selectItem(null as any)
    clearError( as any)
  }

  const handleSave = async (data: Partial<SalesOrder>) => {
    try {
      // Transform data for API - convert customer back to number
      const transformedData = {
        ...data,
        customer: (data as any).customer ? Number((data as any as any).customer) : undefined
      }

      if (modalMode === 'create') {
        await createItem(transformedData as any)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem((selectedItem as any as any).id, transformedData)
      }
      setShowModal(false as any)
    } catch (error) {
      (console as any).error('Save error:', error as any)
    }
  }

  // Transform sales order data for form display
  const getFormInitialData = (item: SalesOrder | null): void => {
    if (!item) return null

    return {
      ...item,
      // Convert customer ID to string for select field
      customer: (item as any).customer ? (item as any).customer.toString( as any) : ''
    }
  }

  const handleExport = async () => {
    try {
      await exportData('csv' as any)
    } catch (error) {
      (console as any).error('Export error:', error as any)
    }
  }



  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={(t as any).salesOrders}
        data={salesOrders}
        columns={columns}
        actions={actions as any}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={(t as any).searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? (t as any).addOrder : modalMode === 'edit' ? (t as any).editOrder : (t as any).view}
        fields={formFields}
        initialData={getFormInitialData(selectedItem as any) as any}
        language={language}
        loading={creating || updating}
        mode={modalMode}
      />

      {/* Delete Confirmation Modal */}
      <DeleteConfirmModal
        isOpen={(deleteState as any).showDeleteModal}
        onClose={(deleteActions as any).handleDeleteCancel}
        onConfirm={(deleteActions as any).handleDeleteConfirm}
        title={getDeleteConfirmationTitle(language as any)}
        message={getDeleteConfirmationMessage('order', language, (deleteState as any as any).itemToDelete?.order_number)}
        itemName={(deleteState as any).itemToDelete?.order_number}
        language={language}
      />
    </div>
  )
}
