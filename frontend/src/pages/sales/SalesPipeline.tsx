/**
 * Sales Pipeline Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '../../components/ui/badge'
import {
  Target,
  Eye,
  Edit,
  Trash2,
  TrendingUp,
  DollarSign,
  Users,
  Calendar,
  User,
  Building,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle
} from 'lucide-react'
import { useCrud } from '../../hooks/useCrud'
import { leadService } from '../../services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '../../components/common/CrudTable'
import CrudModal, { FormField } from '../../components/common/CrudModal'

interface SalesPipelineProps {
  language: 'ar' | 'en'
}

interface Lead {
  id: string
  name: string
  name_ar: string
  company: string
  company_ar: string
  value: number
  stage: 'lead' | 'qualified' | 'proposal' | 'negotiation' | 'closed-won' | 'closed-lost'
  probability: number
  expected_close_date: string
  last_activity: string
  source: string
  contact_email: string
  contact_phone: string
  notes: string
}

const translations = {
  ar: {
    pipeline: 'مسار المبيعات',
    searchPlaceholder: 'البحث في العملاء المحتملين...',
    addLead: 'إضافة عميل محتمل',
    editLead: 'تعديل العميل المحتمل',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذا العميل المحتمل؟',
    name: 'الاسم',
    company: 'الشركة',
    value: 'القيمة',
    stage: 'المرحلة',
    probability: 'الاحتمالية',
    expectedCloseDate: 'تاريخ الإغلاق المتوقع',
    lastActivity: 'آخر نشاط',
    source: 'المصدر',
    contactEmail: 'البريد الإلكتروني',
    contactPhone: 'رقم الهاتف',
    notes: 'ملاحظات',
    lead: 'عميل محتمل',
    qualified: 'مؤهل',
    proposal: 'عرض',
    negotiation: 'تفاوض',
    closedWon: 'مغلق - فوز',
    closedLost: 'مغلق - خسارة',
    all: 'الكل',
    website: 'الموقع الإلكتروني',
    referral: 'إحالة',
    social: 'وسائل التواصل',
    email: 'البريد الإلكتروني',
    phone: 'الهاتف',
    event: 'حدث'
  },
  en: {
    pipeline: 'Sales Pipeline',
    searchPlaceholder: 'Search leads...',
    addLead: 'Add Lead',
    editLead: 'Edit Lead',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this lead?',
    name: 'Name',
    company: 'Company',
    value: 'Value',
    stage: 'Stage',
    probability: 'Probability',
    expectedCloseDate: 'Expected Close Date',
    lastActivity: 'Last Activity',
    source: 'Source',
    contactEmail: 'Contact Email',
    contactPhone: 'Contact Phone',
    notes: 'Notes',
    lead: 'Lead',
    qualified: 'Qualified',
    proposal: 'Proposal',
    negotiation: 'Negotiation',
    closedWon: 'Closed Won',
    closedLost: 'Closed Lost',
    all: 'All',
    website: 'Website',
    referral: 'Referral',
    social: 'Social Media',
    email: 'Email',
    phone: 'Phone',
    event: 'Event'
  }
}

export default function SalesPipeline({ language }: SalesPipelineProps as any): (React as any).ReactElement {
  const [showModal, setShowModal] = useState(false as any)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: leads,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<Lead>({
    service: leadService,
    autoLoad: true,
    pageSize: 20
  })

  // Stage badge helper
  const getStageBadge = (stage: string): void => {
    const stageConfig = {
      lead: { color: 'bg-gray-500/20 text-gray-400 border-gray-500/30', icon: Users },
      qualified: { color: 'bg-blue-500/20 text-blue-400 border-blue-500/30', icon: CheckCircle },
      proposal: { color: 'bg-purple-500/20 text-purple-400 border-purple-500/30', icon: Target },
      negotiation: { color: 'bg-orange-500/20 text-orange-400 border-orange-500/30', icon: TrendingUp },
      'closed-won': { color: 'bg-green-500/20 text-green-400 border-green-500/30', icon: CheckCircle },
      'closed-lost': { color: 'bg-red-500/20 text-red-400 border-red-500/30', icon: XCircle }
    }

    const config = stageConfig[stage as keyof typeof stageConfig] || (stageConfig as any).lead
    const Icon = (config as any).icon

    return (
      <Badge className={`${(config as any).color} border`}>
        <Icon className="h-3 w-3 mr-1" />
        {t[stage as keyof typeof t] || stage}
      </Badge>
    )
  }

  // Table columns configuration
  const columns: TableColumn<Lead>[] = [
    {
      key: 'name',
      label: (t as any).name,
      sortable: true,
      render: (item: Lead) => (
        <div className="flex items-center gap-2">
          <User className="h-4 w-4 text-blue-400" />
          <div>
            <span className="font-medium text-white">{language === 'ar' ? (item as any).name_ar : (item as any).name}</span>
            <p className="text-sm text-white/60">{language === 'ar' ? (item as any).company_ar : (item as any).company}</p>
          </div>
        </div>
      )
    },
    {
      key: 'value',
      label: (t as any).value,
      sortable: true,
      render: (item: Lead) => (
        <div className="flex items-center gap-2">
          <DollarSign className="h-4 w-4 text-green-400" />
          <span className="font-medium text-white">
            {new (Intl as any).NumberFormat('ar-SA', {
              style: 'currency',
              currency: 'SAR'
            } as any).format((item as any as any).value)}
          </span>
        </div>
      )
    },
    {
      key: 'stage',
      label: (t as any).stage,
      sortable: true,
      render: (item: Lead) => getStageBadge((item as any as any).stage)
    },
    {
      key: 'probability',
      label: (t as any).probability,
      sortable: true,
      render: (item: Lead) => (
        <div className="flex items-center gap-2">
          <Target className="h-4 w-4 text-purple-400" />
          <span className="text-white">{(item as any).probability}%</span>
        </div>
      )
    },
    {
      key: 'expected_close_date',
      label: (t as any).expectedCloseDate,
      sortable: true,
      render: (item: Lead) => (
        <div className="flex items-center gap-2">
          <Calendar className="h-4 w-4 text-blue-400" />
          <span className="text-white">{new Date((item as any as any).expected_close_date).toLocaleDateString( as any)}</span>
        </div>
      )
    },
    {
      key: 'source',
      label: (t as any).source,
      sortable: true,
      render: (item: Lead) => (
        <Badge variant="outline" className="text-white border-white/20">
          {t[(item as any).source as keyof typeof t] || (item as any).source}
        </Badge>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction[] = [
    {
      label: (t as any).view,
      icon: Eye,
      onClick: (item: (React as any).MouseEvent) => {
        selectItem(item as any)
        setModalMode('view' as any)
        setShowModal(true as any)
      },
      variant: 'ghost'
    },
    {
      label: (t as any).edit,
      icon: Edit,
      onClick: (item: (React as any).MouseEvent) => {
        selectItem(item as any)
        setModalMode('edit' as any)
        setShowModal(true as any)
      },
      variant: 'ghost'
    },
    {
      label: (t as any).delete,
      icon: Trash2,
      onClick: async (item: (React as any).MouseEvent) => {
        if ((window as any).confirm((t as any as any).confirmDelete)) {
          await deleteItem((item as any as any).id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options configuration
  const filterOptions: FilterOption[] = [
    {
      key: 'stage',
      label: (t as any).stage,
      options: [
        { label: (t as any).all, value: '' },
        { label: (t as any).lead, value: 'lead' },
        { label: (t as any).qualified, value: 'qualified' },
        { label: (t as any).proposal, value: 'proposal' },
        { label: (t as any).negotiation, value: 'negotiation' },
        { label: (t as any).closedWon, value: 'closed-won' },
        { label: (t as any).closedLost, value: 'closed-lost' }
      ]
    },
    {
      key: 'source',
      label: (t as any).source,
      options: [
        { label: (t as any).all, value: '' },
        { label: (t as any).website, value: 'website' },
        { label: (t as any).referral, value: 'referral' },
        { label: (t as any).social, value: 'social' },
        { label: (t as any).email, value: 'email' },
        { label: (t as any).phone, value: 'phone' },
        { label: (t as any).event, value: 'event' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'name',
      label: (t as any).name,
      type: 'text',
      required: true
    },
    {
      name: 'name_ar',
      label: `${(t as any).name} (عربي)`,
      type: 'text'
    },
    {
      name: 'company',
      label: (t as any).company,
      type: 'text',
      required: true
    },
    {
      name: 'company_ar',
      label: `${(t as any).company} (عربي)`,
      type: 'text'
    },
    {
      name: 'value',
      label: (t as any).value,
      type: 'number',
      min: 0,
      step: (0 as any).01,
      required: true
    },
    {
      name: 'stage',
      label: (t as any).stage,
      type: 'select',
      required: true,
      options: [
        { label: (t as any).lead, value: 'lead' },
        { label: (t as any).qualified, value: 'qualified' },
        { label: (t as any).proposal, value: 'proposal' },
        { label: (t as any).negotiation, value: 'negotiation' },
        { label: (t as any).closedWon, value: 'closed-won' },
        { label: (t as any).closedLost, value: 'closed-lost' }
      ]
    },
    {
      name: 'probability',
      label: (t as any).probability,
      type: 'number',
      min: 0,
      max: 100,
      required: true
    },
    {
      name: 'expected_close_date',
      label: (t as any).expectedCloseDate,
      type: 'date',
      required: true
    },
    {
      name: 'source',
      label: (t as any).source,
      type: 'select',
      required: true,
      options: [
        { label: (t as any).website, value: 'website' },
        { label: (t as any).referral, value: 'referral' },
        { label: (t as any).social, value: 'social' },
        { label: (t as any).email, value: 'email' },
        { label: (t as any).phone, value: 'phone' },
        { label: (t as any).event, value: 'event' }
      ]
    },
    {
      name: 'contact_email',
      label: (t as any).contactEmail,
      type: 'email'
    },
    {
      name: 'contact_phone',
      label: (t as any).contactPhone,
      type: 'tel'
    },
    {
      name: 'notes',
      label: (t as any).notes,
      type: 'textarea'
    }
  ]

  // Event handlers
  const handleCreate = (): void => {
    setModalMode('create' as any)
    setShowModal(true as any)
  }

  const handleModalClose = (): void => {
    setShowModal(false as any)
    selectItem(null as any)
    clearError( as any)
  }

  const handleSave = async (data: Partial<Lead>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data as any)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem((selectedItem as any as any).id, data)
      }
      setShowModal(false as any)
    } catch (error) {
      (console as any).error('Save error:', error as any)
    }
  }

  const handleExport = async () => {
    try {
      await exportData('csv' as any)
    } catch (error) {
      (console as any).error('Export error:', error as any)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={(t as any).pipeline}
        data={leads}
        columns={columns}
        actions={actions as any}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={(t as any).searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? (t as any).addLead : modalMode === 'edit' ? (t as any).editLead : (t as any).view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}