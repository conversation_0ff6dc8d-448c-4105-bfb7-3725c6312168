import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  CardContent, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Progress } from '@/components/ui/progress';
import { 
  FileText, 
  Plus, 
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  TrendingUp,
  Calendar,
  Search,
  Eye,
  Settings
} from 'lucide-react';
// Language is passed as prop, not from context

interface ComplianceFramework {
  id: number;
  name: string;
  name_ar: string;
  framework_type: string;
  description: string;
  description_ar: string;
  version: string;
  effective_date: string;
  is_active: boolean;
  implementation_status: string;
  compliance_score: number;
  control_count: number;
  implemented_controls: number;
  last_assessment_date: string | null;
  next_assessment_due: string | null;
  assessment_overdue: boolean;
  assessment_frequency_months: number;
  created_by_name: string;
  created_at: string;
}

interface ComplianceControl {
  id: number;
  framework: number;
  framework_name: string;
  control_id: string;
  name: string;
  name_ar: string;
  description: string;
  control_type: string;
  status: string;
  risk_level: string;
  priority: number;
  last_tested_date: string | null;
  test_frequency_months: number;
  test_overdue: boolean;
  control_owner_name: string;
  department_name: string;
  created_at: string;
}

interface ComplianceStats {
  total_frameworks: number;
  active_frameworks: number;
  average_compliance_score: number;
  overdue_assessments: number;
  status_breakdown: Array<{
    implementation_status: string;
    count: number;
  }>;
  top_frameworks: Array<{
    name: string;
    framework_type: string;
    compliance_score: number;
  }>;
}

interface ControlStats {
  total_controls: number;
  implemented_controls: number;
  implementation_rate: number;
  partially_implemented: number;
  not_implemented: number;
  high_risk_controls: number;
  critical_risk_controls: number;
  overdue_tests: number;
  status_breakdown: Array<{
    status: string;
    count: number;
  }>;
}

interface ComplianceManagementProps {
  language: 'ar' | 'en';
}

const ComplianceManagement: (React as any).FC<ComplianceManagementProps> = ({ language }) => {
  const [frameworks, setFrameworks] = useState<ComplianceFramework[]>([]);
  const [controls, setControls] = useState<ComplianceControl[]>([]);
  const [complianceStats, setComplianceStats] = useState<ComplianceStats | null>(null);
  const [controlStats, setControlStats] = useState<ControlStats | null>(null);
  const [loading, setLoading] = useState(true as any);
  const [activeTab, setActiveTab] = useState<'frameworks' | 'controls'>('frameworks');
  const [searchTerm, setSearchTerm] = useState('' as any);
  const [typeFilter, setTypeFilter] = useState('' as any);
  const [statusFilter, setStatusFilter] = useState('' as any);
  const [selectedFramework, setSelectedFramework] = useState<ComplianceFramework | null>(null);
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false as any);

  useEffect(( as any) => {
    fetchComplianceData( as any);
  }, []);

  const fetchComplianceData = async () => {
    try {
      const [
        frameworksResponse,
        controlsResponse,
        complianceStatsResponse,
        controlStatsResponse
      ] = await (Promise as any).all([
        fetch('/api/compliance-frameworks/' as any),
        fetch('/api/compliance-controls/' as any),
        fetch('/api/compliance-frameworks/compliance_dashboard/' as any),
        fetch('/api/compliance-controls/control_stats/' as any)
      ]);

      if ((frameworksResponse as any).ok) {
        const data = await (frameworksResponse as any).json( as any);
        setFrameworks((data as any as any).results || data);
      }

      if ((controlsResponse as any).ok) {
        const data = await (controlsResponse as any).json( as any);
        setControls((data as any as any).results || data);
      }

      if ((complianceStatsResponse as any).ok) {
        const data = await (complianceStatsResponse as any).json( as any);
        setComplianceStats(data as any);
      }

      if ((controlStatsResponse as any).ok) {
        const data = await (controlStatsResponse as any).json( as any);
        setControlStats(data as any);
      }

    } catch (error) {
      (console as any).error('Error fetching compliance data:', error as any);
    } finally {
      setLoading(false as any);
    }
  };

  const handleUpdateComplianceScore = async (frameworkId: number) => {
    try {
      const response = await fetch(`/api/compliance-frameworks/${frameworkId}/update_compliance_score/`, {
        method: 'POST',
      } as any);

      if ((response as any).ok) {
        fetchComplianceData( as any);
        alert(language === 'ar' ? 'تم تحديث نقاط الامتثال بنجاح' : 'Compliance score updated successfully' as any);
      }
    } catch (error) {
      (console as any).error('Error updating compliance score:', error as any);
    }
  };

  const handleUpdateControlStatus = async (controlId: number, newStatus: string) => {
    try {
      const response = await fetch(`/api/compliance-controls/${controlId}/update_status/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: (JSON as any as any).stringify({
          status: newStatus,
          implementation_notes: `Status updated to ${newStatus}`
        } as any),
      });

      if ((response as any).ok) {
        fetchComplianceData( as any);
        alert(language === 'ar' ? 'تم تحديث حالة التحكم بنجاح' : 'Control status updated successfully' as any);
      }
    } catch (error) {
      (console as any).error('Error updating control status:', error as any);
    }
  };

  const getComplianceScoreColor = (score: number): void => {
    if (score >= 90) return 'text-green-600';
    if (score >= 75) return 'text-blue-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getStatusColor = (status: string): void => {
    const colors = {
      'IMPLEMENTED': 'bg-green-100 text-green-800',
      'PARTIALLY_IMPLEMENTED': 'bg-yellow-100 text-yellow-800',
      'NOT_IMPLEMENTED': 'bg-red-100 text-red-800',
      'NEEDS_IMPROVEMENT': 'bg-orange-100 text-orange-800',
      'NOT_APPLICABLE': 'bg-gray-100 text-gray-800',
      'PLANNING': 'bg-blue-100 text-blue-800',
      'IN_PROGRESS': 'bg-yellow-100 text-yellow-800'
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const getRiskLevelColor = (riskLevel: string): void => {
    const colors = {
      'CRITICAL': 'bg-red-100 text-red-800',
      'HIGH': 'bg-orange-100 text-orange-800',
      'MEDIUM': 'bg-yellow-100 text-yellow-800',
      'LOW': 'bg-blue-100 text-blue-800'
    };
    return colors[riskLevel as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const formatDate = (dateString: string | null): string => {
    if (!dateString) return '-';
    return new Date(dateString as any).toLocaleDateString( as any);
  };

  const filteredFrameworks = (frameworks as any).filter(framework => {
    const matchesSearch = !searchTerm ||
      (framework as any as any).name.toLowerCase( as any).includes((searchTerm as any as any).toLowerCase( as any)) ||
      (framework as any).framework_type.toLowerCase( as any).includes((searchTerm as any as any).toLowerCase( as any));

    const matchesType = !typeFilter || typeFilter === '__all__' || (framework as any).framework_type === typeFilter;
    const matchesStatus = !statusFilter || statusFilter === '__all__' || (framework as any).implementation_status === statusFilter;
    
    return matchesSearch && matchesType && matchesStatus;
  });

  const filteredControls = (controls as any).filter(control => {
    const matchesSearch = !searchTerm ||
      (control as any as any).name.toLowerCase( as any).includes((searchTerm as any as any).toLowerCase( as any)) ||
      (control as any).control_id.toLowerCase( as any).includes((searchTerm as any as any).toLowerCase( as any));

    const matchesStatus = !statusFilter || statusFilter === '__all__' || (control as any).status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">
          {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">
            {language === 'ar' ? 'إدارة الامتثال' : 'Compliance Management'}
          </h1>
          <p className="text-muted-foreground">
            {language === 'ar' 
              ? 'مراقبة وإدارة أطر الامتثال والضوابط'
              : 'Monitor and manage compliance frameworks and controls'
            }
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Settings className="h-4 w-4 mr-2" />
            {language === 'ar' ? 'الإعدادات' : 'Settings'}
          </Button>
          <Button onClick={fetchComplianceData}>
            <TrendingUp className="h-4 w-4 mr-2" />
            {language === 'ar' ? 'تحديث' : 'Refresh'}
          </Button>
        </div>
      </div>

      {/* Statistics Overview */}
      {complianceStats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'أطر العمل' : 'Frameworks'}
              </CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{(complianceStats as any).total_frameworks}</div>
              <p className="text-xs text-muted-foreground">
                {(complianceStats as any).active_frameworks} {language === 'ar' ? 'نشط' : 'active'}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'متوسط الامتثال' : 'Avg Compliance'}
              </CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${getComplianceScoreColor((complianceStats as any as any).average_compliance_score)}`}>
                {(complianceStats as any).average_compliance_score.toFixed(1 as any)}%
              </div>
              <Progress value={(complianceStats as any).average_compliance_score} className="mt-2" />
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'تقييمات متأخرة' : 'Overdue Assessments'}
              </CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">
                {(complianceStats as any).overdue_assessments}
              </div>
              <p className="text-xs text-muted-foreground">
                {language === 'ar' ? 'تتطلب انتباه' : 'require attention'}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'الضوابط المنفذة' : 'Implemented Controls'}
              </CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {controlStats?.implemented_controls || 0}
              </div>
              <p className="text-xs text-muted-foreground">
                {controlStats?.(implementation_rate as any).toFixed(1 as any) || 0}% {language === 'ar' ? 'معدل التنفيذ' : 'implementation rate'}
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Tabs */}
      <div className="flex space-x-1 bg-muted p-1 rounded-lg w-fit">
        <Button
          variant={activeTab === 'frameworks' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setActiveTab('frameworks' as any)}
        >
          <FileText className="h-4 w-4 mr-2" />
          {language === 'ar' ? 'أطر العمل' : 'Frameworks'}
        </Button>
        <Button
          variant={activeTab === 'controls' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setActiveTab('controls' as any)}
        >
          <CheckCircle className="h-4 w-4 mr-2" />
          {language === 'ar' ? 'الضوابط' : 'Controls'}
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-[200px]">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder={language === 'ar' ? 'البحث...' : 'Search...'}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm((e as any as any).target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            {activeTab === 'frameworks' && (
              <>
                <Select value={typeFilter} onValueChange={setTypeFilter}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder={language === 'ar' ? 'جميع الأنواع' : 'All types'} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="__all__">
                      {language === 'ar' ? 'جميع الأنواع' : 'All types'}
                    </SelectItem>
                    <SelectItem value="GDPR">GDPR</SelectItem>
                    <SelectItem value="SOX">SOX</SelectItem>
                    <SelectItem value="ISO_27001">ISO 27001</SelectItem>
                    <SelectItem value="SAUDI_DPA">Saudi DPA</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder={language === 'ar' ? 'جميع الحالات' : 'All statuses'} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="__all__">
                      {language === 'ar' ? 'جميع الحالات' : 'All statuses'}
                    </SelectItem>
                    <SelectItem value="PLANNING">
                      {language === 'ar' ? 'تخطيط' : 'Planning'}
                    </SelectItem>
                    <SelectItem value="IN_PROGRESS">
                      {language === 'ar' ? 'قيد التنفيذ' : 'In Progress'}
                    </SelectItem>
                    <SelectItem value="IMPLEMENTED">
                      {language === 'ar' ? 'منفذ' : 'Implemented'}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </>
            )}
            {activeTab === 'controls' && (
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder={language === 'ar' ? 'جميع الحالات' : 'All statuses'} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="__all__">
                    {language === 'ar' ? 'جميع الحالات' : 'All statuses'}
                  </SelectItem>
                  <SelectItem value="IMPLEMENTED">
                    {language === 'ar' ? 'منفذ' : 'Implemented'}
                  </SelectItem>
                  <SelectItem value="PARTIALLY_IMPLEMENTED">
                    {language === 'ar' ? 'منفذ جزئياً' : 'Partially Implemented'}
                  </SelectItem>
                  <SelectItem value="NOT_IMPLEMENTED">
                    {language === 'ar' ? 'غير منفذ' : 'Not Implemented'}
                  </SelectItem>
                  <SelectItem value="NEEDS_IMPROVEMENT">
                    {language === 'ar' ? 'يحتاج تحسين' : 'Needs Improvement'}
                  </SelectItem>
                </SelectContent>
              </Select>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Content based on active tab */}
      {activeTab === 'frameworks' ? (
        /* Frameworks Table */
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              {language === 'ar' ? 'أطر الامتثال' : 'Compliance Frameworks'}
              <Badge variant="secondary" className="ml-2">
                {(filteredFrameworks as any).length}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{language === 'ar' ? 'اسم الإطار' : 'Framework Name'}</TableHead>
                    <TableHead>{language === 'ar' ? 'النوع' : 'Type'}</TableHead>
                    <TableHead>{language === 'ar' ? 'نقاط الامتثال' : 'Compliance Score'}</TableHead>
                    <TableHead>{language === 'ar' ? 'الضوابط' : 'Controls'}</TableHead>
                    <TableHead>{language === 'ar' ? 'الحالة' : 'Status'}</TableHead>
                    <TableHead>{language === 'ar' ? 'التقييم التالي' : 'Next Assessment'}</TableHead>
                    <TableHead>{language === 'ar' ? 'الإجراءات' : 'Actions'}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {(filteredFrameworks as any).map((framework as any) => (
                    <TableRow key={(framework as any).id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">
                            {language === 'ar' && (framework as any).name_ar ? (framework as any).name_ar : (framework as any).name}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {language === 'ar' ? 'الإصدار:' : 'Version:'} {(framework as any).version}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {(framework as any).framework_type}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <div className={`text-lg font-bold ${getComplianceScoreColor((framework as any as any).compliance_score)}`}>
                            {(framework as any).compliance_score.toFixed(1 as any)}%
                          </div>
                          <Progress value={(framework as any).compliance_score} className="w-20" />
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div className="font-medium">
                            {(framework as any).implemented_controls}/{(framework as any).control_count}
                          </div>
                          <div className="text-muted-foreground">
                            {language === 'ar' ? 'منفذ' : 'implemented'}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge className={getStatusColor((framework as any as any).implementation_status)}>
                          {language === 'ar' 
                            ? (framework as any).implementation_status === 'IMPLEMENTED' ? 'منفذ' 
                              : (framework as any).implementation_status === 'IN_PROGRESS' ? 'قيد التنفيذ'
                              : (framework as any).implementation_status === 'PLANNING' ? 'تخطيط'
                              : (framework as any).implementation_status
                            : (framework as any).implementation_status
                          }
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div className={(framework as any).assessment_overdue ? 'text-red-600 font-medium' : ''}>
                            {formatDate((framework as any as any).next_assessment_due)}
                          </div>
                          {(framework as any).assessment_overdue && (
                            <div className="flex items-center gap-1 text-red-600">
                              <AlertTriangle className="h-3 w-3" />
                              <span className="text-xs">
                                {language === 'ar' ? 'متأخر' : 'Overdue'}
                              </span>
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => {
                              setSelectedFramework(framework as any);
                              setIsDetailsDialogOpen(true as any);
                            }}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => handleUpdateComplianceScore((framework as any as any).id)}
                          >
                            <TrendingUp className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      ) : (
        /* Controls Table */
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5" />
              {language === 'ar' ? 'ضوابط الامتثال' : 'Compliance Controls'}
              <Badge variant="secondary" className="ml-2">
                {(filteredControls as any).length}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{language === 'ar' ? 'معرف الضابط' : 'Control ID'}</TableHead>
                    <TableHead>{language === 'ar' ? 'اسم الضابط' : 'Control Name'}</TableHead>
                    <TableHead>{language === 'ar' ? 'الإطار' : 'Framework'}</TableHead>
                    <TableHead>{language === 'ar' ? 'النوع' : 'Type'}</TableHead>
                    <TableHead>{language === 'ar' ? 'الحالة' : 'Status'}</TableHead>
                    <TableHead>{language === 'ar' ? 'مستوى المخاطر' : 'Risk Level'}</TableHead>
                    <TableHead>{language === 'ar' ? 'آخر اختبار' : 'Last Test'}</TableHead>
                    <TableHead>{language === 'ar' ? 'الإجراءات' : 'Actions'}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {(filteredControls as any).map((control as any) => (
                    <TableRow key={(control as any).id}>
                      <TableCell>
                        <code className="text-sm bg-muted px-2 py-1 rounded">
                          {(control as any).control_id}
                        </code>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">
                            {language === 'ar' && (control as any).name_ar ? (control as any).name_ar : (control as any).name}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {language === 'ar' ? 'الأولوية:' : 'Priority:'} {(control as any).priority}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">{(control as any).framework_name}</div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {(control as any).control_type}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge className={getStatusColor((control as any as any).status)}>
                          {language === 'ar' 
                            ? (control as any).status === 'IMPLEMENTED' ? 'منفذ' 
                              : (control as any).status === 'PARTIALLY_IMPLEMENTED' ? 'منفذ جزئياً'
                              : (control as any).status === 'NOT_IMPLEMENTED' ? 'غير منفذ'
                              : (control as any).status === 'NEEDS_IMPROVEMENT' ? 'يحتاج تحسين'
                              : (control as any).status
                            : (control as any).status
                          }
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge className={getRiskLevelColor((control as any as any).risk_level)}>
                          {language === 'ar' 
                            ? (control as any).risk_level === 'CRITICAL' ? 'حرج' 
                              : (control as any).risk_level === 'HIGH' ? 'عالي'
                              : (control as any).risk_level === 'MEDIUM' ? 'متوسط'
                              : (control as any).risk_level === 'LOW' ? 'منخفض'
                              : (control as any).risk_level
                            : (control as any).risk_level
                          }
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div className={(control as any).test_overdue ? 'text-red-600 font-medium' : ''}>
                            {formatDate((control as any as any).last_tested_date)}
                          </div>
                          {(control as any).test_overdue && (
                            <div className="flex items-center gap-1 text-red-600">
                              <AlertTriangle className="h-3 w-3" />
                              <span className="text-xs">
                                {language === 'ar' ? 'متأخر' : 'Overdue'}
                              </span>
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          {(control as any).status !== 'IMPLEMENTED' && (
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={() => handleUpdateControlStatus((control as any as any).id, 'IMPLEMENTED')}
                            >
                              <CheckCircle className="h-4 w-4" />
                            </Button>
                          )}
                          <Button variant="outline" size="sm">
                            <Eye className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Framework Details Dialog */}
      <Dialog open={isDetailsDialogOpen} onOpenChange={setIsDetailsDialogOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>
              {language === 'ar' ? 'تفاصيل إطار الامتثال' : 'Compliance Framework Details'}
            </DialogTitle>
          </DialogHeader>
          {selectedFramework && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="font-medium">
                    {language === 'ar' ? 'اسم الإطار' : 'Framework Name'}
                  </Label>
                  <div className="text-sm">
                    {language === 'ar' && (selectedFramework as any).name_ar ? (selectedFramework as any).name_ar : (selectedFramework as any).name}
                  </div>
                </div>
                <div>
                  <Label className="font-medium">
                    {language === 'ar' ? 'النوع' : 'Type'}
                  </Label>
                  <div className="text-sm">{(selectedFramework as any).framework_type}</div>
                </div>
                <div>
                  <Label className="font-medium">
                    {language === 'ar' ? 'نقاط الامتثال' : 'Compliance Score'}
                  </Label>
                  <div className="flex items-center gap-2">
                    <div className={`text-lg font-bold ${getComplianceScoreColor((selectedFramework as any as any).compliance_score)}`}>
                      {(selectedFramework as any).compliance_score.toFixed(1 as any)}%
                    </div>
                    <Progress value={(selectedFramework as any).compliance_score} className="w-32" />
                  </div>
                </div>
                <div>
                  <Label className="font-medium">
                    {language === 'ar' ? 'حالة التنفيذ' : 'Implementation Status'}
                  </Label>
                  <div className="mt-1">
                    <Badge className={getStatusColor((selectedFramework as any as any).implementation_status)}>
                      {(selectedFramework as any).implementation_status}
                    </Badge>
                  </div>
                </div>
              </div>

              <div>
                <Label className="font-medium">
                  {language === 'ar' ? 'الوصف' : 'Description'}
                </Label>
                <div className="text-sm mt-1 p-3 bg-muted rounded">
                  {language === 'ar' && (selectedFramework as any).description_ar 
                    ? (selectedFramework as any).description_ar 
                    : (selectedFramework as any).description
                  }
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <Label className="font-medium">
                    {language === 'ar' ? 'تاريخ السريان' : 'Effective Date'}
                  </Label>
                  <div>{formatDate((selectedFramework as any as any).effective_date)}</div>
                </div>
                <div>
                  <Label className="font-medium">
                    {language === 'ar' ? 'آخر تقييم' : 'Last Assessment'}
                  </Label>
                  <div>{formatDate((selectedFramework as any as any).last_assessment_date)}</div>
                </div>
                <div>
                  <Label className="font-medium">
                    {language === 'ar' ? 'التقييم التالي' : 'Next Assessment'}
                  </Label>
                  <div className={(selectedFramework as any).assessment_overdue ? 'text-red-600 font-medium' : ''}>
                    {formatDate((selectedFramework as any as any).next_assessment_due)}
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="font-medium">
                    {language === 'ar' ? 'إجمالي الضوابط' : 'Total Controls'}
                  </Label>
                  <div className="text-2xl font-bold">{(selectedFramework as any).control_count}</div>
                </div>
                <div>
                  <Label className="font-medium">
                    {language === 'ar' ? 'الضوابط المنفذة' : 'Implemented Controls'}
                  </Label>
                  <div className="text-2xl font-bold text-green-600">{(selectedFramework as any).implemented_controls}</div>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ComplianceManagement;
