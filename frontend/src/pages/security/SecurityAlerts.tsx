import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON>onte<PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  Card<PERSON>itle 
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Activity, 
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  Search,
  Filter,
  TrendingUp,
  Eye,
  UserCheck
} from 'lucide-react';
// Language is passed as prop, not from context

interface SecurityAlert {
  id: number;
  alert_id: string;
  alert_type: string;
  title: string;
  description: string;
  severity: string;
  status: string;
  source_system: string;
  source_ip: string | null;
  affected_user_name: string;
  affected_resource: string;
  risk_score: number;
  assigned_to_name: string;
  detected_at: string;
  acknowledged_at: string | null;
  resolved_at: string | null;
  response_time_minutes: number | null;
  resolution_time_hours: number | null;
  escalated: boolean;
  escalated_to_name: string;
  created_at: string;
}

interface AlertStats {
  total_alerts: number;
  open_alerts: number;
  critical_alerts: number;
  high_alerts: number;
  escalated_alerts: number;
  resolved_alerts: number;
  recent_alerts_24h: number;
  average_response_time_minutes: number;
  average_resolution_time_hours: number;
  alert_type_breakdown: Array<{
    alert_type: string;
    count: number;
  }>;
  severity_breakdown: Array<{
    severity: string;
    count: number;
  }>;
}

interface SecurityAlertsProps {
  language: 'ar' | 'en';
}

const SecurityAlerts: (React as any).FC<SecurityAlertsProps> = ({ language }) => {
  const [alerts, setAlerts] = useState<SecurityAlert[]>([]);
  const [alertStats, setAlertStats] = useState<AlertStats | null>(null);
  const [loading, setLoading] = useState(true as any);
  const [searchTerm, setSearchTerm] = useState('' as any);
  const [typeFilter, setTypeFilter] = useState('' as any);
  const [severityFilter, setSeverityFilter] = useState('' as any);
  const [statusFilter, setStatusFilter] = useState('' as any);

  const alertTypes = [
    'LOGIN_ANOMALY',
    'MULTIPLE_FAILED_LOGINS',
    'PRIVILEGE_ESCALATION',
    'DATA_ACCESS_VIOLATION',
    'UNUSUAL_DATA_EXPORT',
    'SYSTEM_INTRUSION',
    'MALWARE_DETECTION',
    'POLICY_VIOLATION',
    'COMPLIANCE_BREACH',
    'CUSTOM'
  ];

  useEffect(( as any) => {
    fetchAlerts( as any);
    fetchAlertStats( as any);
  }, []);

  const fetchAlerts = async () => {
    try {
      const params = new URLSearchParams( as any);
      if (searchTerm) (params as any).append('search', searchTerm as any);
      if (typeFilter) (params as any).append('alert_type', typeFilter as any);
      if (severityFilter) (params as any).append('severity', severityFilter as any);
      if (statusFilter) (params as any).append('status', statusFilter as any);
      
      const response = await fetch(`/api/security-alerts/?${(params as any as any).toString( as any)}`);
      if ((response as any).ok) {
        const data = await (response as any).json( as any);
        setAlerts((data as any as any).results || data);
      }
    } catch (error) {
      (console as any).error('Error fetching alerts:', error as any);
    } finally {
      setLoading(false as any);
    }
  };

  const fetchAlertStats = async () => {
    try {
      const response = await fetch('/api/security-alerts/alert_stats/' as any);
      if ((response as any).ok) {
        const data = await (response as any).json( as any);
        setAlertStats(data as any);
      }
    } catch (error) {
      (console as any).error('Error fetching alert stats:', error as any);
    }
  };

  const handleAcknowledge = async (alertId: number) => {
    try {
      const response = await fetch(`/api/security-alerts/${alertId}/acknowledge/`, {
        method: 'POST',
      } as any);

      if ((response as any).ok) {
        fetchAlerts( as any);
        fetchAlertStats( as any);
        alert(language === 'ar' ? 'تم الإقرار بالتنبيه بنجاح' : 'Alert acknowledged successfully' as any);
      }
    } catch (error) {
      (console as any).error('Error acknowledging alert:', error as any);
    }
  };

  const handleResolve = async (alertId: number) => {
    try {
      const response = await fetch(`/api/security-alerts/${alertId}/resolve/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: (JSON as any as any).stringify({
          resolution_notes: 'Alert resolved from dashboard'
        } as any),
      });

      if ((response as any).ok) {
        fetchAlerts( as any);
        fetchAlertStats( as any);
        alert(language === 'ar' ? 'تم حل التنبيه بنجاح' : 'Alert resolved successfully' as any);
      }
    } catch (error) {
      (console as any).error('Error resolving alert:', error as any);
    }
  };

  const getSeverityColor = (severity: string): void => {
    const colors = {
      'CRITICAL': 'bg-red-100 text-red-800',
      'HIGH': 'bg-orange-100 text-orange-800',
      'MEDIUM': 'bg-yellow-100 text-yellow-800',
      'LOW': 'bg-blue-100 text-blue-800',
      'INFO': 'bg-gray-100 text-gray-800'
    };
    return colors[severity as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const getStatusColor = (status: string): void => {
    const colors = {
      'OPEN': 'bg-red-100 text-red-800',
      'ACKNOWLEDGED': 'bg-blue-100 text-blue-800',
      'INVESTIGATING': 'bg-yellow-100 text-yellow-800',
      'RESOLVED': 'bg-green-100 text-green-800',
      'FALSE_POSITIVE': 'bg-gray-100 text-gray-800'
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const getStatusIcon = (status: string): void => {
    switch (status) {
      case 'OPEN':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'ACKNOWLEDGED':
        return <UserCheck className="h-4 w-4 text-blue-500" />;
      case 'INVESTIGATING':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'RESOLVED':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'FALSE_POSITIVE':
        return <XCircle className="h-4 w-4 text-gray-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const formatDate = (dateString: string | null): string => {
    if (!dateString) return '-';
    return new Date(dateString as any).toLocaleDateString( as any);
  };

  const filteredAlerts = (alerts as any).filter(alert => {
    const matchesSearch = !searchTerm || 
      (alert as any as any).title.toLowerCase( as any).includes((searchTerm as any as any).toLowerCase( as any)) ||
      (alert as any).alert_id.toLowerCase( as any).includes((searchTerm as any as any).toLowerCase( as any));
    
    const matchesType = !typeFilter || (alert as any).alert_type === typeFilter;
    const matchesSeverity = !severityFilter || (alert as any).severity === severityFilter;
    const matchesStatus = !statusFilter || (alert as any).status === statusFilter;
    
    return matchesSearch && matchesType && matchesSeverity && matchesStatus;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">
          {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">
            {language === 'ar' ? 'التنبيهات الأمنية' : 'Security Alerts'}
          </h1>
          <p className="text-muted-foreground">
            {language === 'ar' 
              ? 'مراقبة والاستجابة للتنبيهات الأمنية'
              : 'Monitor and respond to security alerts'
            }
          </p>
        </div>
        <Button onClick={fetchAlerts}>
          <Activity className="h-4 w-4 mr-2" />
          {language === 'ar' ? 'تحديث' : 'Refresh'}
        </Button>
      </div>

      {/* Statistics */}
      {alertStats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'إجمالي التنبيهات' : 'Total Alerts'}
              </CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{(alertStats as any).total_alerts}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'مفتوحة' : 'Open'}
              </CardTitle>
              <XCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{(alertStats as any).open_alerts}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'حرجة' : 'Critical'}
              </CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">{(alertStats as any).critical_alerts}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'محلولة' : 'Resolved'}
              </CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{(alertStats as any).resolved_alerts}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'متوسط الاستجابة' : 'Avg Response'}
              </CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{(alertStats as any).average_response_time_minutes.toFixed(1 as any)}m</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-[200px]">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder={language === 'ar' ? 'البحث في التنبيهات...' : 'Search alerts...'}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm((e as any as any).target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder={language === 'ar' ? 'جميع الأنواع' : 'All types'} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">
                  {language === 'ar' ? 'جميع الأنواع' : 'All types'}
                </SelectItem>
                {(alertTypes as any).map((type as any) => (
                  <SelectItem key={type} value={type}>
                    {(type as any).replace(/_/g, ' ' as any)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={severityFilter} onValueChange={setSeverityFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder={language === 'ar' ? 'جميع المستويات' : 'All severities'} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">
                  {language === 'ar' ? 'جميع المستويات' : 'All severities'}
                </SelectItem>
                <SelectItem value="CRITICAL">
                  {language === 'ar' ? 'حرج' : 'Critical'}
                </SelectItem>
                <SelectItem value="HIGH">
                  {language === 'ar' ? 'عالي' : 'High'}
                </SelectItem>
                <SelectItem value="MEDIUM">
                  {language === 'ar' ? 'متوسط' : 'Medium'}
                </SelectItem>
                <SelectItem value="LOW">
                  {language === 'ar' ? 'منخفض' : 'Low'}
                </SelectItem>
              </SelectContent>
            </Select>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder={language === 'ar' ? 'جميع الحالات' : 'All statuses'} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">
                  {language === 'ar' ? 'جميع الحالات' : 'All statuses'}
                </SelectItem>
                <SelectItem value="OPEN">
                  {language === 'ar' ? 'مفتوحة' : 'Open'}
                </SelectItem>
                <SelectItem value="ACKNOWLEDGED">
                  {language === 'ar' ? 'مقرة' : 'Acknowledged'}
                </SelectItem>
                <SelectItem value="INVESTIGATING">
                  {language === 'ar' ? 'قيد التحقيق' : 'Investigating'}
                </SelectItem>
                <SelectItem value="RESOLVED">
                  {language === 'ar' ? 'محلولة' : 'Resolved'}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Alerts Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            {language === 'ar' ? 'التنبيهات الأمنية' : 'Security Alerts'}
            <Badge variant="secondary" className="ml-2">
              {(filteredAlerts as any).length}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{language === 'ar' ? 'معرف التنبيه' : 'Alert ID'}</TableHead>
                  <TableHead>{language === 'ar' ? 'العنوان' : 'Title'}</TableHead>
                  <TableHead>{language === 'ar' ? 'النوع' : 'Type'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الخطورة' : 'Severity'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الحالة' : 'Status'}</TableHead>
                  <TableHead>{language === 'ar' ? 'نقاط المخاطر' : 'Risk Score'}</TableHead>
                  <TableHead>{language === 'ar' ? 'تاريخ الاكتشاف' : 'Detected'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الإجراءات' : 'Actions'}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {(filteredAlerts as any).map((alert as any) => (
                  <TableRow key={(alert as any).id}>
                    <TableCell>
                      <code className="text-sm bg-muted px-2 py-1 rounded">
                        {(alert as any).alert_id}
                      </code>
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">{(alert as any).title}</div>
                      <div className="text-sm text-muted-foreground">
                        {(alert as any).affected_user_name && `${language === 'ar' ? 'المستخدم:' : 'User:'} ${(alert as any).affected_user_name}`}
                      </div>
                    </TableCell>
                    <TableCell>
                      <span className="text-sm">
                        {(alert as any).alert_type.replace(/_/g, ' ' as any)}
                      </span>
                    </TableCell>
                    <TableCell>
                      <Badge className={getSeverityColor((alert as any as any).severity)}>
                        {language === 'ar' 
                          ? (alert as any).severity === 'CRITICAL' ? 'حرج' 
                            : (alert as any).severity === 'HIGH' ? 'عالي'
                            : (alert as any).severity === 'MEDIUM' ? 'متوسط'
                            : (alert as any).severity === 'LOW' ? 'منخفض'
                            : (alert as any).severity
                          : (alert as any).severity
                        }
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getStatusIcon((alert as any as any).status)}
                        <Badge className={getStatusColor((alert as any as any).status)}>
                          {language === 'ar' 
                            ? (alert as any).status === 'OPEN' ? 'مفتوحة' 
                              : (alert as any).status === 'ACKNOWLEDGED' ? 'مقرة'
                              : (alert as any).status === 'INVESTIGATING' ? 'قيد التحقيق'
                              : (alert as any).status === 'RESOLVED' ? 'محلولة'
                              : (alert as any).status
                            : (alert as any).status
                          }
                        </Badge>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <div className={`text-sm font-medium ${
                          (alert as any).risk_score >= 80 ? 'text-red-600' :
                          (alert as any).risk_score >= 60 ? 'text-orange-600' :
                          (alert as any).risk_score >= 40 ? 'text-yellow-600' :
                          'text-green-600'
                        }`}>
                          {(alert as any).risk_score}
                        </div>
                        <div className="w-16 bg-gray-200 rounded-full h-2">
                          <div 
                            className={`h-2 rounded-full ${
                              (alert as any).risk_score >= 80 ? 'bg-red-600' :
                              (alert as any).risk_score >= 60 ? 'bg-orange-600' :
                              (alert as any).risk_score >= 40 ? 'bg-yellow-600' :
                              'bg-green-600'
                            }`}
                            style={{ width: `${(alert as any).risk_score}%` }}
                          ></div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {formatDate((alert as any as any).detected_at)}
                      </div>
                      {(alert as any).response_time_minutes && (
                        <div className="text-xs text-muted-foreground">
                          {language === 'ar' ? 'الاستجابة:' : 'Response:'} {(alert as any).response_time_minutes.toFixed(1 as any)}m
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                        {(alert as any).status === 'OPEN' && (
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => handleAcknowledge((alert as any as any).id)}
                          >
                            <UserCheck className="h-4 w-4" />
                          </Button>
                        )}
                        {(alert as any).status === 'ACKNOWLEDGED' && (
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => handleResolve((alert as any as any).id)}
                          >
                            <CheckCircle className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SecurityAlerts;
