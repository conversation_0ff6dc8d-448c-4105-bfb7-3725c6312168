import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON> 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  <PERSON>,
  AlertTriangle,
  XCircle,
  Users,
  Activity,
  FileText,
  Eye,
  Settings
} from 'lucide-react';
// Language is passed as prop, not from context

interface SecurityStats {
  total_users: number;
  mfa_enabled_users: number;
  mfa_adoption_rate: number;
  locked_accounts: number;
  high_security_users: number;
  critical_security_users: number;
  recent_failed_logins_24h: number;
}

interface IncidentStats {
  total_incidents: number;
  open_incidents: number;
  critical_incidents: number;
  resolved_incidents: number;
  recent_incidents_30d: number;
  average_resolution_hours: number;
  incident_type_breakdown: Array<{
    incident_type: string;
    count: number;
  }>;
}

interface AlertStats {
  total_alerts: number;
  open_alerts: number;
  critical_alerts: number;
  high_alerts: number;
  escalated_alerts: number;
  resolved_alerts: number;
  recent_alerts_24h: number;
  average_response_time_minutes: number;
  average_resolution_time_hours: number;
}

interface ComplianceStats {
  total_frameworks: number;
  active_frameworks: number;
  average_compliance_score: number;
  overdue_assessments: number;
  top_frameworks: Array<{
    name: string;
    framework_type: string;
    compliance_score: number;
  }>;
}

interface RecentIncident {
  id: number;
  incident_id: string;
  title: string;
  incident_type: string;
  severity: string;
  status: string;
  detected_at: string;
  assigned_to_name: string;
}

interface RecentAlert {
  id: number;
  alert_id: string;
  title: string;
  alert_type: string;
  severity: string;
  status: string;
  detected_at: string;
  risk_score: number;
}

interface SecurityDashboardProps {
  language: 'ar' | 'en';
}

// @ts-ignore
const SecurityDashboard: (React as any).FC<SecurityDashboardProps> = ({ language }) => {
  // @ts-ignore
  const [securityStats, setSecurityStats] = useState<SecurityStats | null>(null);
  // @ts-ignore
  const [incidentStats, setIncidentStats] = useState<IncidentStats | null>(null);
  // @ts-ignore
  const [alertStats, setAlertStats] = useState<AlertStats | null>(null);
  // @ts-ignore
  const [complianceStats, setComplianceStats] = useState<ComplianceStats | null>(null);
  // @ts-ignore
  const [recentIncidents, setRecentIncidents] = useState<RecentIncident[]>([]);
  // @ts-ignore
  const [recentAlerts, setRecentAlerts] = useState<RecentAlert[]>([]);
  const [loading, setLoading] = useState(true as any);

  // @ts-ignore
  useEffect(( as any) => {
    // @ts-ignore
    fetchSecurityData( as any);
  // @ts-ignore
  }, []);

  // @ts-ignore
  const fetchSecurityData: any = async () => {
    // @ts-ignore
    try {
      // @ts-ignore
      const [
        securityResponse,
        incidentResponse,
        alertResponse,
        complianceResponse,
        recentIncidentsResponse,
        recentAlertsResponse
      ] = await (Promise as any).all([
        fetch('/api/user-security-profiles/security_stats/' as any),
        fetch('/api/security-incidents/incident_stats/' as any),
        fetch('/api/security-alerts/alert_stats/' as any),
        fetch('/api/compliance-frameworks/compliance_dashboard/' as any),
        fetch('/api/security-incidents/?ordering=-detected_at&page_size=5' as any),
        fetch('/api/security-alerts/?ordering=-detected_at&page_size=5' as any)
      ]);

      if ((securityResponse as any).ok) {
        // @ts-ignore
        const data: any = await (securityResponse as any).json( as any);
        setSecurityStats(data as any);
      // @ts-ignore
      }

      if ((incidentResponse as any).ok) {
        // @ts-ignore
        const data: any = await (incidentResponse as any).json( as any);
        setIncidentStats(data as any);
      // @ts-ignore
      }

      if ((alertResponse as any).ok) {
        // @ts-ignore
        const data: any = await (alertResponse as any).json( as any);
        setAlertStats(data as any);
      // @ts-ignore
      }

      if ((complianceResponse as any).ok) {
        // @ts-ignore
        const data: any = await (complianceResponse as any).json( as any);
        setComplianceStats(data as any);
      // @ts-ignore
      }

      if ((recentIncidentsResponse as any).ok) {
        // @ts-ignore
        const data: any = await (recentIncidentsResponse as any).json( as any);
        setRecentIncidents((data as any as any).results || data);
      // @ts-ignore
      }

      if ((recentAlertsResponse as any).ok) {
        // @ts-ignore
        const data: any = await (recentAlertsResponse as any).json( as any);
        setRecentAlerts((data as any as any).results || data);
      // @ts-ignore
      }

    // @ts-ignore
    } catch (error) {
      // @ts-ignore
      (console as any).error('Error fetching security data:', error as any);
    // @ts-ignore
    } finally {
      // @ts-ignore
      setLoading(false as any);
    // @ts-ignore
    }
  // @ts-ignore
  };

  // @ts-ignore
  const getSeverityColor: any = (severity: string): void => {
    // @ts-ignore
    const colors = {
      // @ts-ignore
      'CRITICAL': 'bg-red-100 text-red-800',
      'HIGH': 'bg-orange-100 text-orange-800',
      'MEDIUM': 'bg-yellow-100 text-yellow-800',
      'LOW': 'bg-blue-100 text-blue-800',
      'INFO': 'bg-gray-100 text-gray-800'
    // @ts-ignore
    };
    return colors[severity as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  // @ts-ignore
  };

  // @ts-ignore
  const getStatusColor: any = (status: string): void => {
    // @ts-ignore
    const colors = {
      // @ts-ignore
      'OPEN': 'bg-red-100 text-red-800',
      'INVESTIGATING': 'bg-yellow-100 text-yellow-800',
      'CONTAINED': 'bg-blue-100 text-blue-800',
      'RESOLVED': 'bg-green-100 text-green-800',
      'CLOSED': 'bg-gray-100 text-gray-800',
      'ACKNOWLEDGED': 'bg-blue-100 text-blue-800'
    // @ts-ignore
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  // @ts-ignore
  };

  // @ts-ignore
  const formatDate: any = (dateString: string): string => {
    // @ts-ignore
    return new Date(dateString as any).toLocaleDateString( as any);
  // @ts-ignore
  };

  if (loading) {
    // @ts-ignore
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">
          {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
        </div>
      </div>
    );
  // @ts-ignore
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">
            {language === 'ar' ? 'لوحة تحكم الأمان' : 'Security Dashboard'}
          </h1>
          <p className="text-muted-foreground">
            {language === 'ar' 
              ? 'مراقبة الأمان والامتثال والحوادث الأمنية'
              : 'Monitor security, compliance, and security incidents'
            }
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Settings className="h-4 w-4 mr-2" />
            {language === 'ar' ? 'الإعدادات' : 'Settings'}
          </Button>
          <Button onClick={fetchSecurityData}>
            <Activity className="h-4 w-4 mr-2" />
            {language === 'ar' ? 'تحديث' : 'Refresh'}
          </Button>
        </div>
      </div>

      {/* Security Overview */}
      {securityStats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'إجمالي المستخدمين' : 'Total Users'}
              </CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{(securityStats as any).total_users}</div>
              <p className="text-xs text-muted-foreground">
                {language === 'ar' ? 'مستخدم نشط' : 'active users'}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'معدل اعتماد MFA' : 'MFA Adoption'}
              </CardTitle>
              <Shield className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {(securityStats as any).mfa_adoption_rate.toFixed(1 as any)}%
              </div>
              <p className="text-xs text-muted-foreground">
                {(securityStats as any).mfa_enabled_users} {language === 'ar' ? 'من' : 'of'} {(securityStats as any).total_users}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'الحسابات المقفلة' : 'Locked Accounts'}
              </CardTitle>
              <XCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{(securityStats as any).locked_accounts}</div>
              <p className="text-xs text-muted-foreground">
                {language === 'ar' ? 'تتطلب إلغاء القفل' : 'require unlock'}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'محاولات تسجيل دخول فاشلة' : 'Failed Logins (24h)'}
              </CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">
                {(securityStats as any).recent_failed_logins_24h}
              </div>
              <p className="text-xs text-muted-foreground">
                {language === 'ar' ? 'آخر 24 ساعة' : 'last 24 hours'}
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Incidents and Alerts Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Security Incidents */}
        {incidentStats && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5" />
                {language === 'ar' ? 'الحوادث الأمنية' : 'Security Incidents'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">{(incidentStats as any).open_incidents}</div>
                  <div className="text-sm text-muted-foreground">
                    {language === 'ar' ? 'مفتوحة' : 'Open'}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">{(incidentStats as any).critical_incidents}</div>
                  <div className="text-sm text-muted-foreground">
                    {language === 'ar' ? 'حرجة' : 'Critical'}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{(incidentStats as any).resolved_incidents}</div>
                  <div className="text-sm text-muted-foreground">
                    {language === 'ar' ? 'محلولة' : 'Resolved'}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{(incidentStats as any).average_resolution_hours.toFixed(1 as any)}h</div>
                  <div className="text-sm text-muted-foreground">
                    {language === 'ar' ? 'متوسط الحل' : 'Avg Resolution'}
                  </div>
                </div>
              </div>
              <div className="text-sm text-muted-foreground">
                {(incidentStats as any).recent_incidents_30d} {language === 'ar' ? 'حوادث جديدة آخر 30 يوم' : 'new incidents in last 30 days'}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Security Alerts */}
        {alertStats && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                {language === 'ar' ? 'التنبيهات الأمنية' : 'Security Alerts'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">{(alertStats as any).open_alerts}</div>
                  <div className="text-sm text-muted-foreground">
                    {language === 'ar' ? 'مفتوحة' : 'Open'}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">{(alertStats as any).critical_alerts}</div>
                  <div className="text-sm text-muted-foreground">
                    {language === 'ar' ? 'حرجة' : 'Critical'}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{(alertStats as any).resolved_alerts}</div>
                  <div className="text-sm text-muted-foreground">
                    {language === 'ar' ? 'محلولة' : 'Resolved'}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{(alertStats as any).average_response_time_minutes.toFixed(1 as any)}m</div>
                  <div className="text-sm text-muted-foreground">
                    {language === 'ar' ? 'متوسط الاستجابة' : 'Avg Response'}
                  </div>
                </div>
              </div>
              <div className="text-sm text-muted-foreground">
                {(alertStats as any).recent_alerts_24h} {language === 'ar' ? 'تنبيهات جديدة آخر 24 ساعة' : 'new alerts in last 24 hours'}
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Compliance Overview */}
      {complianceStats && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              {language === 'ar' ? 'نظرة عامة على الامتثال' : 'Compliance Overview'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <div className="text-center">
                <div className="text-2xl font-bold">{(complianceStats as any).total_frameworks}</div>
                <div className="text-sm text-muted-foreground">
                  {language === 'ar' ? 'أطر العمل' : 'Frameworks'}
                </div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{(complianceStats as any).active_frameworks}</div>
                <div className="text-sm text-muted-foreground">
                  {language === 'ar' ? 'نشطة' : 'Active'}
                </div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {(complianceStats as any).average_compliance_score.toFixed(1 as any)}%
                </div>
                <div className="text-sm text-muted-foreground">
                  {language === 'ar' ? 'متوسط الامتثال' : 'Avg Compliance'}
                </div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">{(complianceStats as any).overdue_assessments}</div>
                <div className="text-sm text-muted-foreground">
                  {language === 'ar' ? 'تقييمات متأخرة' : 'Overdue Assessments'}
                </div>
              </div>
            </div>

            {(complianceStats as any).top_frameworks.length > 0 && (
              <div>
                <h4 className="font-medium mb-3">
                  {language === 'ar' ? 'أفضل أطر الامتثال' : 'Top Compliance Frameworks'}
                </h4>
                <div className="space-y-2">
                  // @ts-ignore
                  {(complianceStats as any).top_frameworks.map((framework, index as any) => (
                    <div key={index} className="flex items-center justify-between p-2 bg-muted rounded">
                      <div>
                        <div className="font-medium">{(framework as any).name}</div>
                        <div className="text-sm text-muted-foreground">{(framework as any).framework_type}</div>
                      </div>
                      <div className="text-right">
                        <div className="font-bold text-green-600">{(framework as any).compliance_score.toFixed(1 as any)}%</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Incidents */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5" />
                {language === 'ar' ? 'الحوادث الأخيرة' : 'Recent Incidents'}
              </span>
              <Button variant="outline" size="sm">
                <Eye className="h-4 w-4 mr-2" />
                {language === 'ar' ? 'عرض الكل' : 'View All'}
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              // @ts-ignore
              {(recentIncidents as any).map((incident as any) => (
                <div key={(incident as any).id} className="flex items-center justify-between p-3 border rounded">
                  <div className="flex-1">
                    <div className="font-medium">{(incident as any).title}</div>
                    <div className="text-sm text-muted-foreground">
                      {(incident as any).incident_id} • {formatDate((incident as any as any).detected_at)}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge className={getSeverityColor((incident as any as any).severity)}>
                      {(incident as any).severity}
                    </Badge>
                    <Badge className={getStatusColor((incident as any as any).status)}>
                      {(incident as any).status}
                    </Badge>
                  </div>
                </div>
              // @ts-ignore
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recent Alerts */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                {language === 'ar' ? 'التنبيهات الأخيرة' : 'Recent Alerts'}
              </span>
              <Button variant="outline" size="sm">
                <Eye className="h-4 w-4 mr-2" />
                {language === 'ar' ? 'عرض الكل' : 'View All'}
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              // @ts-ignore
              {(recentAlerts as any).map((alert as any) => (
                <div key={(alert as any).id} className="flex items-center justify-between p-3 border rounded">
                  <div className="flex-1">
                    <div className="font-medium">{(alert as any).title}</div>
                    <div className="text-sm text-muted-foreground">
                      {(alert as any).alert_id} • {formatDate((alert as any as any).detected_at)}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="text-sm font-medium">
                      {language === 'ar' ? 'المخاطر:' : 'Risk:'} {(alert as any).risk_score}
                    </div>
                    <Badge className={getSeverityColor((alert as any as any).severity)}>
                      {(alert as any).severity}
                    </Badge>
                    <Badge className={getStatusColor((alert as any as any).status)}>
                      {(alert as any).status}
                    </Badge>
                  </div>
                </div>
              // @ts-ignore
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
// @ts-ignore
};

export default SecurityDashboard;
