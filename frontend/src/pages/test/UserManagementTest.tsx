/**
 * Test Page for UserManagement Component
 * Bypasses authentication for testing purposes
 */

import React from 'react'
import { Provider } from 'react-redux'
import { store } from '../../store'
import { ToastProvider } from '../../contexts/ToastContext'
import UserManagement from '../admin/UserManagement'
import { enableTestMode } from '../../utils/testHelpers'

// Enable test mode for debugging
if (typeof window !== 'undefined') {
  enableTestMode()
}

// Mock authentication provider
const MockAuthProvider = ({ children }: { children: React.ReactNode }): React.ReactElement => {
  return <>{children}</>
}

export default function UserManagementTest(): React.ReactElement {
  return (
    <Provider store={store}>
      <ToastProvider>
        <MockAuthProvider>
          <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 p-6">
            <div className="max-w-7xl mx-auto">
              <div className="mb-6">
                <h1 className="text-3xl font-bold text-white mb-2">🧪 UserManagement Test Page</h1>
                <p className="text-white/70">Testing all functionality without authentication</p>
              </div>
              
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6">
                <UserManagement language="en" />
              </div>
              
              <div className="mt-6 bg-yellow-500/20 border border-yellow-500/50 rounded-lg p-4">
                <h3 className="text-yellow-300 font-semibold mb-2">🔍 Test Instructions:</h3>
                <ul className="text-yellow-200 text-sm space-y-1">
                  <li>• Check if the table loads with data</li>
                  <li>• Test all action buttons (View, Edit, Delete, etc.)</li>
                  <li>• Try creating a new user</li>
                  <li>• Test search and filtering</li>
                  <li>• Verify modal functionality</li>
                  <li>• Check console for any errors</li>
                </ul>
              </div>
            </div>
          </div>
        </MockAuthProvider>
      </ToastProvider>
    </Provider>
  )
}
