/**
 * Certifications Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  Award,
  Eye,
  Edit,
  Trash2,
  Download,
  Calendar,
  User,
  CheckCircle,
  Clock,
  Building,
  FileText
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { certificationService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface CertificationsProps {
  language: 'ar' | 'en'
}

interface Certification {
  id: number
  name: string
  nameAr: string
  description: string
  descriptionAr: string
  issuingOrganization: string
  issuingOrganizationAr: string
  employee: string
  employeeAr: string
  employeeId: string
  issueDate: string
  expiryDate: string
  certificateNumber: string
  status: 'active' | 'expired' | 'pending' | 'revoked'
  category: string
  categoryAr: string
  level: 'basic' | 'intermediate' | 'advanced' | 'expert'
  verificationUrl?: string
  attachmentUrl?: string
}

const translations = {
  ar: {
    certifications: 'الشهادات المهنية',
    addCertification: 'إضافة شهادة',
    editCertification: 'تعديل الشهادة',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    download: 'تحميل',
    confirmDelete: 'هل أنت متأكد من حذف هذه الشهادة؟',
    searchPlaceholder: 'البحث في الشهادات...',
    name: 'اسم الشهادة',
    description: 'الوصف',
    issuingOrganization: 'الجهة المصدرة',
    employee: 'الموظف',
    employeeId: 'رقم الموظف',
    issueDate: 'تاريخ الإصدار',
    expiryDate: 'تاريخ الانتهاء',
    certificateNumber: 'رقم الشهادة',
    status: 'الحالة',
    category: 'الفئة',
    level: 'المستوى',
    verificationUrl: 'رابط التحقق',
    attachmentUrl: 'رابط المرفق',
    active: 'نشط',
    expired: 'منتهي',
    pending: 'معلق',
    revoked: 'ملغي',
    basic: 'أساسي',
    intermediate: 'متوسط',
    advanced: 'متقدم',
    expert: 'خبير',
    categories: {
      management: 'الإدارة',
      finance: 'المالية',
      marketing: 'التسويق',
      technology: 'التكنولوجيا',
      quality: 'الجودة',
      hr: 'الموارد البشرية'
    }
  },
  en: {
    certifications: 'Professional Certifications',
    addCertification: 'Add Certification',
    editCertification: 'Edit Certification',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    download: 'Download',
    confirmDelete: 'Are you sure you want to delete this certification?',
    searchPlaceholder: 'Search certifications...',
    name: 'Certification Name',
    description: 'Description',
    issuingOrganization: 'Issuing Organization',
    employee: 'Employee',
    employeeId: 'Employee ID',
    issueDate: 'Issue Date',
    expiryDate: 'Expiry Date',
    certificateNumber: 'Certificate Number',
    status: 'Status',
    category: 'Category',
    level: 'Level',
    verificationUrl: 'Verification URL',
    attachmentUrl: 'Attachment URL',
    active: 'Active',
    expired: 'Expired',
    pending: 'Pending',
    revoked: 'Revoked',
    basic: 'Basic',
    intermediate: 'Intermediate',
    advanced: 'Advanced',
    expert: 'Expert',
    categories: {
      management: 'Management',
      finance: 'Finance',
      marketing: 'Marketing',
      technology: 'Technology',
      quality: 'Quality',
      hr: 'Human Resources'
    }
  }
}

export default function Certifications({ language }: CertificationsProps as any): (React as any).ReactElement {
  const [showModal, setShowModal] = useState(false as any)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: certifications,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<Certification>({
    service: certificationService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getStatusColor = (status: string): void => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 border-green-200'
      case 'expired': return 'bg-red-100 text-red-800 border-red-200'
      case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'revoked': return 'bg-gray-100 text-gray-800 border-gray-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getLevelColor = (level: string): void => {
    switch (level) {
      case 'basic': return 'text-blue-400'
      case 'intermediate': return 'text-yellow-400'
      case 'advanced': return 'text-orange-400'
      case 'expert': return 'text-purple-400'
      default: return 'text-gray-400'
    }
  }

  const getDaysToExpiry = (expiryDate: string): void => {
    const today = new Date( as any)
    const expiry = new Date(expiryDate as any)
    const diffTime = (expiry as any).getTime( as any) - (today as any).getTime( as any)
    const diffDays = (Math as any).ceil(diffTime / (1000 * 60 * 60 * 24 as any))
    return diffDays
  }

  // Table columns configuration
  const columns: TableColumn<Certification>[] = [
    {
      key: 'name',
      label: (t as any).name,
      sortable: true,
      render: (item: Certification) => (
        <div className="flex items-center gap-2">
          <Award className="h-4 w-4 text-yellow-400" />
          <div>
            <div className="font-medium text-white">
              {language === 'ar' ? (item as any).nameAr : (item as any).name}
            </div>
            <div className="text-sm text-white/60 flex items-center gap-1">
              <FileText className="h-3 w-3" />
              {(item as any).certificateNumber}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'employee',
      label: (t as any).employee,
      render: (item: Certification) => (
        <div className="flex items-center gap-1">
          <User className="h-3 w-3 text-blue-400" />
          <div>
            <div className="text-white/80">
              {language === 'ar' ? (item as any).employeeAr : (item as any).employee}
            </div>
            <div className="text-xs text-white/50">{(item as any).employeeId}</div>
          </div>
        </div>
      )
    },
    {
      key: 'issuingOrganization',
      label: (t as any).issuingOrganization,
      render: (item: Certification) => (
        <div className="flex items-center gap-1">
          <Building className="h-3 w-3 text-purple-400" />
          <span className="text-white/80">
            {language === 'ar' ? (item as any).issuingOrganizationAr : (item as any).issuingOrganization}
          </span>
        </div>
      )
    },
    {
      key: 'status',
      label: (t as any).status,
      sortable: true,
      render: (item: Certification) => (
        <Badge className={getStatusColor((item as any as any).status)}>
          {String(t[(item as any as any).status as keyof typeof t])}
        </Badge>
      )
    },
    {
      key: 'level',
      label: (t as any).level,
      sortable: true,
      render: (item: Certification) => (
        <span className={`font-medium ${getLevelColor((item as any as any).level)}`}>
          {String(t[(item as any as any).level as keyof typeof t])}
        </span>
      )
    },
    {
      key: 'category',
      label: (t as any).category,
      render: (item: Certification) => (
        <span className="text-white/80">
          {language === 'ar' ? (item as any).categoryAr : (item as any).category}
        </span>
      )
    },
    {
      key: 'issueDate',
      label: (t as any).issueDate,
      sortable: true,
      render: (item: Certification) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-green-400" />
          <span className="text-white/80">{(item as any).issueDate}</span>
        </div>
      )
    },
    {
      key: 'expiryDate',
      label: (t as any).expiryDate,
      sortable: true,
      render: (item: Certification) => (
        <div>
          <div className="flex items-center gap-1">
            <Clock className="h-3 w-3 text-orange-400" />
            <span className="text-white/80">{(item as any).expiryDate}</span>
          </div>
          {(() => {
            const daysToExpiry = getDaysToExpiry((item as any as any).expiryDate)
            const isExpiringSoon = daysToExpiry <= 90 && daysToExpiry > 0
            return isExpiringSoon && (
              <div className="text-xs text-orange-400 mt-1">
                {daysToExpiry} days left
              </div>
            )
          })()}
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction[] = [
    {
      label: (t as any).view,
      icon: Eye,
      onClick: (item: (React as any).MouseEvent) => {
        selectItem(item as any)
        setModalMode('view' as any)
        setShowModal(true as any)
      },
      variant: 'ghost'
    },
    {
      label: (t as any).edit,
      icon: Edit,
      onClick: (item: (React as any).MouseEvent) => {
        selectItem(item as any)
        setModalMode('edit' as any)
        setShowModal(true as any)
      },
      variant: 'ghost'
    },
    {
      label: (t as any).download,
      icon: Download,
      onClick: (item: (React as any).MouseEvent) => {
        if ((item as any).attachmentUrl) {
          (window as any).open((item as any as any).attachmentUrl, '_blank')
        }
      },
      variant: 'ghost',
      className: 'text-blue-400 hover:text-blue-300 hover:bg-blue-500/20'
    },
    {
      label: (t as any).delete,
      icon: Trash2,
      onClick: async (item: (React as any).MouseEvent) => {
        if ((window as any).confirm((t as any as any).confirmDelete)) {
          await deleteItem((item as any as any).id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'status',
      label: (t as any).status,
      options: [
        { label: (t as any).active, value: 'active' },
        { label: (t as any).expired, value: 'expired' },
        { label: (t as any).pending, value: 'pending' },
        { label: (t as any).revoked, value: 'revoked' }
      ]
    },
    {
      key: 'level',
      label: (t as any).level,
      options: [
        { label: (t as any).basic, value: 'basic' },
        { label: (t as any).intermediate, value: 'intermediate' },
        { label: (t as any).advanced, value: 'advanced' },
        { label: (t as any).expert, value: 'expert' }
      ]
    },
    {
      key: 'category',
      label: (t as any).category,
      options: [
        { label: (t as any).categories.management, value: 'management' },
        { label: (t as any).categories.finance, value: 'finance' },
        { label: (t as any).categories.marketing, value: 'marketing' },
        { label: (t as any).categories.technology, value: 'technology' },
        { label: (t as any).categories.quality, value: 'quality' },
        { label: (t as any).categories.hr, value: 'hr' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'name',
      label: (t as any).name,
      type: 'text',
      required: true
    },
    {
      name: 'nameAr',
      label: (t as any).name + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'description',
      label: (t as any).description,
      type: 'textarea',
      required: true
    },
    {
      name: 'descriptionAr',
      label: (t as any).description + ' (عربي)',
      type: 'textarea',
      required: true
    },
    {
      name: 'issuingOrganization',
      label: (t as any).issuingOrganization,
      type: 'text',
      required: true
    },
    {
      name: 'issuingOrganizationAr',
      label: (t as any).issuingOrganization + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'employee',
      label: (t as any).employee,
      type: 'text',
      required: true
    },
    {
      name: 'employeeAr',
      label: (t as any).employee + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'employeeId',
      label: (t as any).employeeId,
      type: 'text',
      required: true
    },
    {
      name: 'issueDate',
      label: (t as any).issueDate,
      type: 'date',
      required: true
    },
    {
      name: 'expiryDate',
      label: (t as any).expiryDate,
      type: 'date',
      required: true
    },
    {
      name: 'certificateNumber',
      label: (t as any).certificateNumber,
      type: 'text',
      required: true
    },
    {
      name: 'status',
      label: (t as any).status,
      type: 'select',
      required: true,
      options: [
        { label: (t as any).active, value: 'active' },
        { label: (t as any).expired, value: 'expired' },
        { label: (t as any).pending, value: 'pending' },
        { label: (t as any).revoked, value: 'revoked' }
      ]
    },
    {
      name: 'category',
      label: (t as any).category,
      type: 'text',
      required: true
    },
    {
      name: 'categoryAr',
      label: (t as any).category + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'level',
      label: (t as any).level,
      type: 'select',
      required: true,
      options: [
        { label: (t as any).basic, value: 'basic' },
        { label: (t as any).intermediate, value: 'intermediate' },
        { label: (t as any).advanced, value: 'advanced' },
        { label: (t as any).expert, value: 'expert' }
      ]
    },
    {
      name: 'verificationUrl',
      label: (t as any).verificationUrl,
      type: 'text'
    },
    {
      name: 'attachmentUrl',
      label: (t as any).attachmentUrl,
      type: 'text'
    }
  ]

  // Event handlers
  const handleCreate = (): void => {
    setModalMode('create' as any)
    setShowModal(true as any)
  }

  const handleModalClose = (): void => {
    setShowModal(false as any)
    selectItem(null as any)
    clearError( as any)
  }

  const handleSave = async (data: Partial<Certification>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data as any)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem((selectedItem as any as any).id, data)
      }
      setShowModal(false as any)
    } catch (error) {
      (console as any).error('Save error:', error as any)
    }
  }

  const handleExport = async () => {
    try {
      await exportData('csv' as any)
    } catch (error) {
      (console as any).error('Export error:', error as any)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={(t as any).certifications}
        data={certifications}
        columns={columns}
        actions={actions as any}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={(t as any).searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? (t as any).addCertification : modalMode === 'edit' ? (t as any).editCertification : (t as any).view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
