/**
 * Training Programs Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  GraduationCap,
  Eye,
  Edit,
  Trash2,
  Users,
  Clock,
  Calendar,
  BookOpen,
  Award,
  TrendingUp,
  User,
  MapPin
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { trainingProgramService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface TrainingProgramsProps {
  language: 'ar' | 'en'
}

interface TrainingProgram {
  id: number
  title: string
  titleAr: string
  description: string
  descriptionAr: string
  instructor: string
  instructorAr: string
  duration: number
  startDate: string
  endDate: string
  maxParticipants: number
  enrolledParticipants: number
  status: 'upcoming' | 'ongoing' | 'completed' | 'cancelled'
  category: string
  categoryAr: string
  level: 'beginner' | 'intermediate' | 'advanced'
  cost: number
  location: string
  locationAr: string
  isOnline: boolean
}

const translations = {
  ar: {
    trainingPrograms: 'برامج التدريب',
    addProgram: 'إضافة برنامج',
    editProgram: 'تعديل البرنامج',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذا البرنامج؟',
    searchPlaceholder: 'البحث في برامج التدريب...',
    title: 'عنوان البرنامج',
    description: 'الوصف',
    instructor: 'المدرب',
    duration: 'المدة (بالساعات)',
    startDate: 'تاريخ البداية',
    endDate: 'تاريخ النهاية',
    maxParticipants: 'الحد الأقصى للمشاركين',
    enrolledParticipants: 'المشاركون المسجلون',
    status: 'الحالة',
    category: 'الفئة',
    level: 'المستوى',
    cost: 'التكلفة',
    location: 'المكان',
    isOnline: 'عبر الإنترنت',
    upcoming: 'قادم',
    ongoing: 'جاري',
    completed: 'مكتمل',
    cancelled: 'ملغي',
    beginner: 'مبتدئ',
    intermediate: 'متوسط',
    advanced: 'متقدم',
    categories: {
      marketing: 'التسويق',
      management: 'الإدارة',
      finance: 'المالية',
      technology: 'التكنولوجيا',
      hr: 'الموارد البشرية',
      sales: 'المبيعات'
    }
  },
  en: {
    trainingPrograms: 'Training Programs',
    addProgram: 'Add Program',
    editProgram: 'Edit Program',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this program?',
    searchPlaceholder: 'Search training programs...',
    title: 'Program Title',
    description: 'Description',
    instructor: 'Instructor',
    duration: 'Duration (hours)',
    startDate: 'Start Date',
    endDate: 'End Date',
    maxParticipants: 'Max Participants',
    enrolledParticipants: 'Enrolled Participants',
    status: 'Status',
    category: 'Category',
    level: 'Level',
    cost: 'Cost',
    location: 'Location',
    isOnline: 'Online',
    upcoming: 'Upcoming',
    ongoing: 'Ongoing',
    completed: 'Completed',
    cancelled: 'Cancelled',
    beginner: 'Beginner',
    intermediate: 'Intermediate',
    advanced: 'Advanced',
    categories: {
      marketing: 'Marketing',
      management: 'Management',
      finance: 'Finance',
      technology: 'Technology',
      hr: 'Human Resources',
      sales: 'Sales'
    }
  }
}

export default function TrainingPrograms({ language }: TrainingProgramsProps as any): (React as any).ReactElement {
  const [showModal, setShowModal] = useState(false as any)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: programs,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<TrainingProgram>({
    service: trainingProgramService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getStatusColor = (status: string): void => {
    switch (status) {
      case 'upcoming': return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'ongoing': return 'bg-green-100 text-green-800 border-green-200'
      case 'completed': return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'cancelled': return 'bg-red-100 text-red-800 border-red-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getLevelColor = (level: string): void => {
    switch (level) {
      case 'beginner': return 'text-green-400'
      case 'intermediate': return 'text-yellow-400'
      case 'advanced': return 'text-red-400'
      default: return 'text-gray-400'
    }
  }

  const formatCurrency = (amount: number): string => {
    return new (Intl as any).NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    } as any).format(amount as any)
  }

  // Table columns configuration
  const columns: TableColumn<TrainingProgram>[] = [
    {
      key: 'title',
      label: (t as any).title,
      sortable: true,
      render: (item: TrainingProgram) => (
        <div className="flex items-center gap-2">
          <GraduationCap className="h-4 w-4 text-blue-400" />
          <div>
            <div className="font-medium text-white">
              {language === 'ar' ? (item as any).titleAr : (item as any).title}
            </div>
            <div className="text-sm text-white/60">
              {language === 'ar' ? (item as any).descriptionAr?.substring(0, 50 as any) + '...' : (item as any).description?.substring(0, 50 as any) + '...'}
            </div>
          </div>
          {(item as any).isOnline && <BookOpen className="h-4 w-4 text-green-400" />}
        </div>
      )
    },
    {
      key: 'instructor',
      label: (t as any).instructor,
      render: (item: TrainingProgram) => (
        <div className="flex items-center gap-1">
          <User className="h-3 w-3 text-purple-400" />
          <span className="text-white/80">
            {language === 'ar' ? (item as any).instructorAr : (item as any).instructor}
          </span>
        </div>
      )
    },
    {
      key: 'duration',
      label: (t as any).duration,
      sortable: true,
      render: (item: TrainingProgram) => (
        <div className="flex items-center gap-1">
          <Clock className="h-3 w-3 text-orange-400" />
          <span className="text-white/80">{(item as any).duration}h</span>
        </div>
      )
    },
    {
      key: 'participants',
      label: 'المشاركون',
      render: (item: TrainingProgram) => (
        <div className="flex items-center gap-1">
          <Users className="h-3 w-3 text-blue-400" />
          <span className="text-white font-medium">
            {(item as any).enrolledParticipants}/{(item as any).maxParticipants}
          </span>
        </div>
      )
    },
    {
      key: 'status',
      label: (t as any).status,
      sortable: true,
      render: (item: TrainingProgram) => (
        <Badge className={getStatusColor((item as any as any).status)}>
          {String(t[(item as any as any).status as keyof typeof t])}
        </Badge>
      )
    },
    {
      key: 'level',
      label: (t as any).level,
      sortable: true,
      render: (item: TrainingProgram) => (
        <span className={`font-medium ${getLevelColor((item as any as any).level)}`}>
          {String(t[(item as any as any).level as keyof typeof t])}
        </span>
      )
    },
    {
      key: 'startDate',
      label: (t as any).startDate,
      sortable: true,
      render: (item: TrainingProgram) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-green-400" />
          <span className="text-white/80">{(item as any).startDate}</span>
        </div>
      )
    },
    {
      key: 'cost',
      label: (t as any).cost,
      sortable: true,
      render: (item: TrainingProgram) => (
        <span className="text-white font-medium">{formatCurrency((item as any as any).cost)}</span>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction[] = [
    {
      label: (t as any).view,
      icon: Eye,
      onClick: (item: (React as any).MouseEvent) => {
        selectItem(item as any)
        setModalMode('view' as any)
        setShowModal(true as any)
      },
      variant: 'ghost'
    },
    {
      label: (t as any).edit,
      icon: Edit,
      onClick: (item: (React as any).MouseEvent) => {
        selectItem(item as any)
        setModalMode('edit' as any)
        setShowModal(true as any)
      },
      variant: 'ghost'
    },
    {
      label: (t as any).delete,
      icon: Trash2,
      onClick: async (item: (React as any).MouseEvent) => {
        if ((window as any).confirm((t as any as any).confirmDelete)) {
          await deleteItem((item as any as any).id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'status',
      label: (t as any).status,
      options: [
        { label: (t as any).upcoming, value: 'upcoming' },
        { label: (t as any).ongoing, value: 'ongoing' },
        { label: (t as any).completed, value: 'completed' },
        { label: (t as any).cancelled, value: 'cancelled' }
      ]
    },
    {
      key: 'level',
      label: (t as any).level,
      options: [
        { label: (t as any).beginner, value: 'beginner' },
        { label: (t as any).intermediate, value: 'intermediate' },
        { label: (t as any).advanced, value: 'advanced' }
      ]
    },
    {
      key: 'category',
      label: (t as any).category,
      options: [
        { label: (t as any).categories.marketing, value: 'marketing' },
        { label: (t as any).categories.management, value: 'management' },
        { label: (t as any).categories.finance, value: 'finance' },
        { label: (t as any).categories.technology, value: 'technology' },
        { label: (t as any).categories.hr, value: 'hr' },
        { label: (t as any).categories.sales, value: 'sales' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'title',
      label: (t as any).title,
      type: 'text',
      required: true
    },
    {
      name: 'titleAr',
      label: (t as any).title + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'description',
      label: (t as any).description,
      type: 'textarea',
      required: true
    },
    {
      name: 'descriptionAr',
      label: (t as any).description + ' (عربي)',
      type: 'textarea',
      required: true
    },
    {
      name: 'instructor',
      label: (t as any).instructor,
      type: 'text',
      required: true
    },
    {
      name: 'instructorAr',
      label: (t as any).instructor + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'duration',
      label: (t as any).duration,
      type: 'number',
      required: true,
      min: 1
    },
    {
      name: 'startDate',
      label: (t as any).startDate,
      type: 'date',
      required: true
    },
    {
      name: 'endDate',
      label: (t as any).endDate,
      type: 'date',
      required: true
    },
    {
      name: 'maxParticipants',
      label: (t as any).maxParticipants,
      type: 'number',
      required: true,
      min: 1
    },
    {
      name: 'enrolledParticipants',
      label: (t as any).enrolledParticipants,
      type: 'number',
      required: true,
      min: 0
    },
    {
      name: 'status',
      label: (t as any).status,
      type: 'select',
      required: true,
      options: [
        { label: (t as any).upcoming, value: 'upcoming' },
        { label: (t as any).ongoing, value: 'ongoing' },
        { label: (t as any).completed, value: 'completed' },
        { label: (t as any).cancelled, value: 'cancelled' }
      ]
    },
    {
      name: 'category',
      label: (t as any).category,
      type: 'text',
      required: true
    },
    {
      name: 'categoryAr',
      label: (t as any).category + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'level',
      label: (t as any).level,
      type: 'select',
      required: true,
      options: [
        { label: (t as any).beginner, value: 'beginner' },
        { label: (t as any).intermediate, value: 'intermediate' },
        { label: (t as any).advanced, value: 'advanced' }
      ]
    },
    {
      name: 'cost',
      label: (t as any).cost,
      type: 'number',
      required: true,
      min: 0
    },
    {
      name: 'location',
      label: (t as any).location,
      type: 'text',
      required: true
    },
    {
      name: 'locationAr',
      label: (t as any).location + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'isOnline',
      label: (t as any).isOnline,
      type: 'checkbox'
    }
  ]

  // Event handlers
  const handleCreate = (): void => {
    setModalMode('create' as any)
    setShowModal(true as any)
  }

  const handleModalClose = (): void => {
    setShowModal(false as any)
    selectItem(null as any)
    clearError( as any)
  }

  const handleSave = async (data: Partial<TrainingProgram>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data as any)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem((selectedItem as any as any).id, data)
      }
      setShowModal(false as any)
    } catch (error) {
      (console as any).error('Save error:', error as any)
    }
  }

  const handleExport = async () => {
    try {
      await exportData('csv' as any)
    } catch (error) {
      (console as any).error('Export error:', error as any)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={(t as any).trainingPrograms}
        data={programs}
        columns={columns}
        actions={actions as any}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={(t as any).searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? (t as any).addProgram : modalMode === 'edit' ? (t as any).editProgram : (t as any).view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
