import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Badge } from '../../components/ui/badge'
import {
  Building,
  Plus,
  Search,
  Eye,
  Edit,
  Trash2,
  Star,
  Phone,
  Mail,
  MapPin,
  DollarSign,
  Calendar,
  TrendingUp,
  Users,
  FileText,
  CheckCircle,
  AlertTriangle
} from 'lucide-react'

interface VendorManagementProps {
  language: 'ar' | 'en'
}

interface Vendor {
  id: string
  name: string
  nameAr: string
  category: string
  categoryAr: string
  contactPerson: string
  contactPersonAr: string
  email: string
  phone: string
  address: string
  addressAr: string
  city: string
  cityAr: string
  country: string
  countryAr: string
  status: 'active' | 'inactive' | 'pending' | 'suspended'
  rating: number
  totalContracts: number
  totalValue: number
  lastOrderDate: string
  registrationDate: string
  paymentTerms: string
  paymentTermsAr: string
  deliveryTime: number // in days
  qualityScore: number
  reliabilityScore: number
  communicationScore: number
  certifications: string[]
  certificationsAr: string[]
  riskLevel: 'low' | 'medium' | 'high'
  preferredVendor: boolean
}

const mockVendors: Vendor[] = [
  {
    id: '1',
    name: 'Tech Solutions Ltd',
    nameAr: 'شركة الحلول التقنية المحدودة',
    category: 'Technology',
    categoryAr: 'التكنولوجيا',
    contactPerson: 'Ahmed Al-Rashid',
    contactPersonAr: 'أحمد الراشد',
    email: '<EMAIL>',
    phone: '+966501234567',
    address: '123 King Fahd Road',
    addressAr: '123 طريق الملك فهد',
    city: 'Riyadh',
    cityAr: 'الرياض',
    country: 'Saudi Arabia',
    countryAr: 'المملكة العربية السعودية',
    status: 'active',
    rating: 4.5,
    totalContracts: 15,
    totalValue: 850000,
    lastOrderDate: '2024-01-20',
    registrationDate: '2023-03-15',
    paymentTerms: 'Net 30',
    paymentTermsAr: 'صافي 30 يوم',
    deliveryTime: 7,
    qualityScore: 92,
    reliabilityScore: 88,
    communicationScore: 95,
    certifications: ['ISO 9001', 'ISO 27001'],
    certificationsAr: ['آيزو 9001', 'آيزو 27001'],
    riskLevel: 'low',
    preferredVendor: true
  },
  {
    id: '2',
    name: 'Office Supplies Co',
    nameAr: 'شركة اللوازم المكتبية',
    category: 'Office Supplies',
    categoryAr: 'اللوازم المكتبية',
    contactPerson: 'Sarah Hassan',
    contactPersonAr: 'سارة حسن',
    email: '<EMAIL>',
    phone: '+966507654321',
    address: '456 Business District',
    addressAr: '456 الحي التجاري',
    city: 'Jeddah',
    cityAr: 'جدة',
    country: 'Saudi Arabia',
    countryAr: 'المملكة العربية السعودية',
    status: 'active',
    rating: 4.2,
    totalContracts: 8,
    totalValue: 125000,
    lastOrderDate: '2024-01-18',
    registrationDate: '2023-06-10',
    paymentTerms: 'Net 15',
    paymentTermsAr: 'صافي 15 يوم',
    deliveryTime: 3,
    qualityScore: 85,
    reliabilityScore: 90,
    communicationScore: 82,
    certifications: ['Quality Certified'],
    certificationsAr: ['معتمد الجودة'],
    riskLevel: 'low',
    preferredVendor: false
  },
  {
    id: '3',
    name: 'Construction Materials Inc',
    nameAr: 'شركة مواد البناء المحدودة',
    category: 'Construction',
    categoryAr: 'البناء',
    contactPerson: 'Omar Abdullah',
    contactPersonAr: 'عمر عبدالله',
    email: '<EMAIL>',
    phone: '+966509876543',
    address: '789 Industrial Area',
    addressAr: '789 المنطقة الصناعية',
    city: 'Dammam',
    cityAr: 'الدمام',
    country: 'Saudi Arabia',
    countryAr: 'المملكة العربية السعودية',
    status: 'pending',
    rating: 3.8,
    totalContracts: 3,
    totalValue: 450000,
    lastOrderDate: '2024-01-10',
    registrationDate: '2024-01-05',
    paymentTerms: 'Net 45',
    paymentTermsAr: 'صافي 45 يوم',
    deliveryTime: 14,
    qualityScore: 78,
    reliabilityScore: 75,
    communicationScore: 80,
    certifications: ['Safety Certified'],
    certificationsAr: ['معتمد السلامة'],
    riskLevel: 'medium',
    preferredVendor: false
  },
  {
    id: '4',
    name: 'Global Logistics',
    nameAr: 'اللوجستيات العالمية',
    category: 'Logistics',
    categoryAr: 'اللوجستيات',
    contactPerson: 'Fatima Mohammed',
    contactPersonAr: 'فاطمة محمد',
    email: '<EMAIL>',
    phone: '+966502468135',
    address: '321 Port Area',
    addressAr: '321 منطقة الميناء',
    city: 'Jeddah',
    cityAr: 'جدة',
    country: 'Saudi Arabia',
    countryAr: 'المملكة العربية السعودية',
    status: 'suspended',
    rating: 2.8,
    totalContracts: 12,
    totalValue: 320000,
    lastOrderDate: '2023-12-15',
    registrationDate: '2022-08-20',
    paymentTerms: 'Net 60',
    paymentTermsAr: 'صافي 60 يوم',
    deliveryTime: 21,
    qualityScore: 65,
    reliabilityScore: 60,
    communicationScore: 70,
    certifications: [],
    certificationsAr: [],
    riskLevel: 'high',
    preferredVendor: false
  }
]

export default function VendorManagement({ language }: VendorManagementProps) {
  const [vendors, setVendors] = useState<Vendor[]>(mockVendors)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [categoryFilter, setCategoryFilter] = useState<string>('all')
  const [riskFilter, setRiskFilter] = useState<string>('all')

  const text = {
    ar: {
      title: 'إدارة الموردين',
      description: 'إدارة ومتابعة الموردين والعلاقات التجارية',
      newVendor: 'مورد جديد',
      search: 'البحث في الموردين...',
      vendorName: 'اسم المورد',
      category: 'الفئة',
      contactPerson: 'الشخص المسؤول',
      email: 'البريد الإلكتروني',
      phone: 'الهاتف',
      location: 'الموقع',
      status: 'الحالة',
      rating: 'التقييم',
      totalContracts: 'إجمالي العقود',
      totalValue: 'إجمالي القيمة',
      lastOrder: 'آخر طلب',
      paymentTerms: 'شروط الدفع',
      deliveryTime: 'وقت التسليم',
      riskLevel: 'مستوى المخاطر',
      actions: 'الإجراءات',
      view: 'عرض',
      edit: 'تعديل',
      delete: 'حذف',
      contact: 'اتصال',
      totalVendors: 'إجمالي الموردين',
      activeVendors: 'الموردين النشطين',
      avgRating: 'متوسط التقييم',
      preferredVendors: 'الموردين المفضلين',
      active: 'نشط',
      inactive: 'غير نشط',
      pending: 'معلق',
      suspended: 'موقوف',
      low: 'منخفض',
      medium: 'متوسط',
      high: 'عالي',
      days: 'يوم',
      sar: 'ر.س',
      preferred: 'مفضل',
      qualityScore: 'نتيجة الجودة',
      reliabilityScore: 'نتيجة الموثوقية',
      communicationScore: 'نتيجة التواصل',
      certifications: 'الشهادات'
    },
    en: {
      title: 'Vendor Management',
      description: 'Manage and track vendors and business relationships',
      newVendor: 'New Vendor',
      search: 'Search vendors...',
      vendorName: 'Vendor Name',
      category: 'Category',
      contactPerson: 'Contact Person',
      email: 'Email',
      phone: 'Phone',
      location: 'Location',
      status: 'Status',
      rating: 'Rating',
      totalContracts: 'Total Contracts',
      totalValue: 'Total Value',
      lastOrder: 'Last Order',
      paymentTerms: 'Payment Terms',
      deliveryTime: 'Delivery Time',
      riskLevel: 'Risk Level',
      actions: 'Actions',
      view: 'View',
      edit: 'Edit',
      delete: 'Delete',
      contact: 'Contact',
      totalVendors: 'Total Vendors',
      activeVendors: 'Active Vendors',
      avgRating: 'Average Rating',
      preferredVendors: 'Preferred Vendors',
      active: 'Active',
      inactive: 'Inactive',
      pending: 'Pending',
      suspended: 'Suspended',
      low: 'Low',
      medium: 'Medium',
      high: 'High',
      days: 'Days',
      sar: 'SAR',
      preferred: 'Preferred',
      qualityScore: 'Quality Score',
      reliabilityScore: 'Reliability Score',
      communicationScore: 'Communication Score',
      certifications: 'Certifications'
    }
  }

  const t = text[language]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-secondary/20 text-secondary border-secondary/30'
      case 'inactive': return 'bg-muted/20 text-muted-foreground border-muted/30'
      case 'pending': return 'bg-accent/20 text-accent border-accent/30'
      case 'suspended': return 'bg-destructive/20 text-destructive border-destructive/30'
      default: return 'bg-muted/20 text-muted-foreground border-muted/30'
    }
  }

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low': return 'bg-secondary/20 text-secondary border-secondary/30'
      case 'medium': return 'bg-accent/20 text-accent border-accent/30'
      case 'high': return 'bg-destructive/20 text-destructive border-destructive/30'
      default: return 'bg-muted/20 text-muted-foreground border-muted/30'
    }
  }

  const getRatingStars = (rating: number) => {
    const stars = []
    const fullStars = Math.floor(rating)
    const hasHalfStar = rating % 1 !== 0

    for (let i = 0; i < fullStars; i++) {
      stars.push(<Star key={i} className="w-3 h-3 fill-yellow-400 text-yellow-400" />)
    }

    if (hasHalfStar) {
      stars.push(<Star key="half" className="w-3 h-3 fill-yellow-400/50 text-yellow-400" />)
    }

    const remainingStars = 5 - Math.ceil(rating)
    for (let i = 0; i < remainingStars; i++) {
      stars.push(<Star key={`empty-${i}`} className="w-3 h-3 text-gray-400" />)
    }

    return stars
  }

  const filteredVendors = vendors.filter(vendor => {
    const matchesSearch = vendor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         vendor.nameAr.includes(searchTerm) ||
                         vendor.contactPerson.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         vendor.contactPersonAr.includes(searchTerm)
    const matchesStatus = statusFilter === 'all' || vendor.status === statusFilter
    const matchesCategory = categoryFilter === 'all' || vendor.category === categoryFilter
    const matchesRisk = riskFilter === 'all' || vendor.riskLevel === riskFilter
    return matchesSearch && matchesStatus && matchesCategory && matchesRisk
  })

  const activeVendors = vendors.filter(vendor => vendor.status === 'active').length
  const avgRating = vendors.length > 0
    ? vendors.reduce((sum, vendor) => sum + vendor.rating, 0) / vendors.length
    : 0
  const preferredVendors = vendors.filter(vendor => vendor.preferredVendor).length

  return (
    <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">{t.title}</h1>
            <p className="text-white/70">{t.description}</p>
          </div>
          <Button className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70">
            <Plus className="w-4 h-4 mr-2" />
            {t.newVendor}
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="modern-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.totalVendors}</p>
                  <p className="text-2xl font-bold text-white">{vendors.length}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-primary/20 flex items-center justify-center">
                  <Building className="w-6 h-6 text-primary" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="modern-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.activeVendors}</p>
                  <p className="text-2xl font-bold text-white">{activeVendors}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-secondary/20 flex items-center justify-center">
                  <CheckCircle className="w-6 h-6 text-secondary" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="modern-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.avgRating}</p>
                  <div className="flex items-center gap-2">
                    <p className="text-2xl font-bold text-white">{avgRating.toFixed(1)}</p>
                    <div className="flex">
                      {getRatingStars(avgRating)}
                    </div>
                  </div>
                </div>
                <div className="w-12 h-12 rounded-full bg-accent/20 flex items-center justify-center">
                  <Star className="w-6 h-6 text-accent" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="modern-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.preferredVendors}</p>
                  <p className="text-2xl font-bold text-white">{preferredVendors}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-muted/20 flex items-center justify-center">
                  <TrendingUp className="w-6 h-6 text-muted-foreground" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card className="modern-card">
          <CardContent className="p-6">
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 w-4 h-4" />
                  <Input
                    placeholder={t.search}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 glass-input"
                  />
                </div>
              </div>
              <div className="flex gap-4">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="glass-input px-3 py-2 rounded-lg"
                >
                  <option value="all">جميع الحالات</option>
                  <option value="active">{t.active}</option>
                  <option value="inactive">{t.inactive}</option>
                  <option value="pending">{t.pending}</option>
                  <option value="suspended">{t.suspended}</option>
                </select>
                <select
                  value={categoryFilter}
                  onChange={(e) => setCategoryFilter(e.target.value)}
                  className="glass-input px-3 py-2 rounded-lg"
                >
                  <option value="all">جميع الفئات</option>
                  <option value="Technology">التكنولوجيا</option>
                  <option value="Office Supplies">اللوازم المكتبية</option>
                  <option value="Construction">البناء</option>
                  <option value="Logistics">اللوجستيات</option>
                </select>
                <select
                  value={riskFilter}
                  onChange={(e) => setRiskFilter(e.target.value)}
                  className="glass-input px-3 py-2 rounded-lg"
                >
                  <option value="all">جميع المخاطر</option>
                  <option value="low">{t.low}</option>
                  <option value="medium">{t.medium}</option>
                  <option value="high">{t.high}</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Vendors Table */}
        <Card className="modern-card">
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-white/10">
                    <th className="text-right p-4 text-white/70 font-medium">{t.vendorName}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.category}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.contactPerson}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.status}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.rating}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.totalValue}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.riskLevel}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.actions}</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredVendors.map((vendor) => (
                    <tr key={vendor.id} className="border-b border-white/5 hover:bg-white/5">
                      <td className="p-4">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 rounded-full bg-sky-500/20 flex items-center justify-center">
                            <Building className="w-5 h-5 text-sky-400" />
                          </div>
                          <div>
                            <div className="flex items-center gap-2">
                              <span className="text-white font-medium">
                                {language === 'ar' ? vendor.nameAr : vendor.name}
                              </span>
                              {vendor.preferredVendor && (
                                <Badge className="bg-purple-500/20 text-purple-300 border-purple-500/30 text-xs">
                                  {t.preferred}
                                </Badge>
                              )}
                            </div>
                            <div className="text-xs text-white/60 mt-1">
                              {language === 'ar' ? vendor.cityAr : vendor.city}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <span className="text-white">{language === 'ar' ? vendor.categoryAr : vendor.category}</span>
                      </td>
                      <td className="p-4">
                        <div>
                          <span className="text-white">{language === 'ar' ? vendor.contactPersonAr : vendor.contactPerson}</span>
                          <div className="text-xs text-white/60 mt-1">
                            <div className="flex items-center gap-1">
                              <Mail className="w-3 h-3" />
                              {vendor.email}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <Badge className={`${getStatusColor(vendor.status)} border`}>
                          {t[vendor.status as keyof typeof t]}
                        </Badge>
                      </td>
                      <td className="p-4">
                        <div className="flex items-center gap-2">
                          <div className="flex">
                            {getRatingStars(vendor.rating)}
                          </div>
                          <span className="text-white text-sm">{vendor.rating}</span>
                        </div>
                      </td>
                      <td className="p-4">
                        <div>
                          <span className="text-white">{vendor.totalValue.toLocaleString()} {t.sar}</span>
                          <div className="text-xs text-white/60">{vendor.totalContracts} عقد</div>
                        </div>
                      </td>
                      <td className="p-4">
                        <Badge className={`${getRiskColor(vendor.riskLevel)} border`}>
                          {t[vendor.riskLevel as keyof typeof t]}
                        </Badge>
                      </td>
                      <td className="p-4">
                        <div className="flex gap-2">
                          <Button size="sm" variant="ghost" className="text-white/70 hover:text-white">
                            <Eye className="w-4 h-4" />
                          </Button>
                          <Button size="sm" variant="ghost" className="text-white/70 hover:text-white">
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button size="sm" variant="ghost" className="text-blue-400 hover:text-blue-300">
                            <Phone className="w-4 h-4" />
                          </Button>
                          <Button size="sm" variant="ghost" className="text-red-400 hover:text-red-300">
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
    </div>
  )
}
