/**
 * Workflow Management Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  Workflow,
  Eye,
  Edit,
  Trash2,
  Play,
  Pause,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  User,
  Calendar,
  Settings,
  Zap
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { workflowService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface WorkflowManagementProps {
  language: 'ar' | 'en'
}

interface WorkflowItem {
  id: number
  name: string
  name_ar: string
  description: string
  description_ar: string
  category: string
  category_ar: string
  status: 'active' | 'inactive' | 'draft' | 'archived'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  trigger_event: string
  conditions: string
  conditions_ar: string
  actions: string
  actions_ar: string
  is_automated: boolean
  next_run?: string
  last_run?: string
  run_count: number
  success_count: number
  created_by?: any
  created_at: string
  updated_at: string
}

const translations = {
  ar: {
    workflowManagement: 'إدارة سير العمل',
    addWorkflow: 'إضافة سير عمل',
    editWorkflow: 'تعديل سير العمل',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف سير العمل هذا؟',
    searchPlaceholder: 'البحث في سير العمل...',
    name: 'الاسم',
    description: 'الوصف',
    category: 'الفئة',
    status: 'الحالة',
    trigger: 'المحفز',
    priority: 'الأولوية',
    createdBy: 'أنشأ بواسطة',
    createdDate: 'تاريخ الإنشاء',
    lastModified: 'آخر تعديل',
    lastRun: 'آخر تشغيل',
    nextRun: 'التشغيل القادم',
    executionCount: 'عدد مرات التنفيذ',
    successRate: 'معدل النجاح',
    averageExecutionTime: 'متوسط وقت التنفيذ',
    assignedTo: 'مُكلف إلى',
    department: 'القسم',
    isAutomated: 'آلي',
    steps: 'الخطوات',
    conditions: 'الشروط',
    actions: 'الإجراءات',
    active: 'نشط',
    inactive: 'غير نشط',
    draft: 'مسودة',
    archived: 'مؤرشف',
    manual: 'يدوي',
    automatic: 'آلي',
    scheduled: 'مجدول',
    'event-based': 'مبني على الأحداث',
    low: 'منخفض',
    medium: 'متوسط',
    high: 'عالي',
    critical: 'حرج',
    categories: {
      approval: 'الموافقات',
      notification: 'الإشعارات',
      dataProcessing: 'معالجة البيانات',
      reporting: 'التقارير',
      integration: 'التكامل',
      maintenance: 'الصيانة'
    }
  },
  en: {
    workflowManagement: 'Workflow Management',
    addWorkflow: 'Add Workflow',
    editWorkflow: 'Edit Workflow',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this workflow?',
    searchPlaceholder: 'Search workflows...',
    name: 'Name',
    description: 'Description',
    category: 'Category',
    status: 'Status',
    trigger: 'Trigger',
    priority: 'Priority',
    createdBy: 'Created By',
    createdDate: 'Created Date',
    lastModified: 'Last Modified',
    lastRun: 'Last Run',
    nextRun: 'Next Run',
    executionCount: 'Execution Count',
    successRate: 'Success Rate',
    averageExecutionTime: 'Avg Execution Time',
    assignedTo: 'Assigned To',
    department: 'Department',
    isAutomated: 'Automated',
    steps: 'Steps',
    conditions: 'Conditions',
    actions: 'Actions',
    active: 'Active',
    inactive: 'Inactive',
    draft: 'Draft',
    archived: 'Archived',
    manual: 'Manual',
    automatic: 'Automatic',
    scheduled: 'Scheduled',
    'event-based': 'Event-based',
    low: 'Low',
    medium: 'Medium',
    high: 'High',
    critical: 'Critical',
    categories: {
      approval: 'Approval',
      notification: 'Notification',
      dataProcessing: 'Data Processing',
      reporting: 'Reporting',
      integration: 'Integration',
      maintenance: 'Maintenance'
    }
  }
}

export default function WorkflowManagement({ language }: WorkflowManagementProps as any): (React as any).ReactElement {
  const [showModal, setShowModal] = useState(false as any)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: workflows,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<WorkflowItem>({
    service: workflowService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getStatusColor = (status: string): void => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 border-green-200'
      case 'inactive': return 'bg-red-100 text-red-800 border-red-200'
      case 'draft': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'archived': return 'bg-gray-100 text-gray-800 border-gray-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getTriggerColor = (trigger: string): void => {
    switch (trigger) {
      case 'manual': return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'automatic': return 'bg-green-100 text-green-800 border-green-200'
      case 'scheduled': return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'event-based': return 'bg-orange-100 text-orange-800 border-orange-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getPriorityColor = (priority: string): void => {
    switch (priority) {
      case 'low': return 'text-green-400'
      case 'medium': return 'text-yellow-400'
      case 'high': return 'text-orange-400'
      case 'critical': return 'text-red-400'
      default: return 'text-gray-400'
    }
  }

  const getStatusIcon = (status: string): void => {
    switch (status) {
      case 'active': return <Play className="h-4 w-4 text-green-400" />
      case 'inactive': return <Pause className="h-4 w-4 text-red-400" />
      case 'draft': return <Clock className="h-4 w-4 text-yellow-400" />
      case 'archived': return <XCircle className="h-4 w-4 text-gray-400" />
      default: return <Clock className="h-4 w-4 text-gray-400" />
    }
  }

  // Table columns configuration
  const columns: TableColumn<WorkflowItem>[] = [
    {
      key: 'name',
      label: (t as any).name,
      sortable: true,
      render: (item: WorkflowItem) => (
        <div className="flex items-center gap-2">
          <Workflow className="h-4 w-4 text-blue-400" />
          <div>
            <div className="font-medium text-white">
              {language === 'ar' ? (item as any).name_ar || (item as any).name : (item as any).name}
            </div>
            <div className="text-sm text-white/60">
              {language === 'ar' ? (item as any).description_ar?.substring(0, 50 as any) + '...' : (item as any).description?.substring(0, 50 as any) + '...'}
            </div>
          </div>
          {(item as any).is_automated && <Zap className="h-4 w-4 text-yellow-400" />}
        </div>
      )
    },
    {
      key: 'category',
      label: (t as any).category,
      render: (item: WorkflowItem) => (
        <span className="text-white/80">
          {language === 'ar' ? (item as any).category_ar || (item as any).category : (item as any).category}
        </span>
      )
    },
    {
      key: 'status',
      label: (t as any).status,
      sortable: true,
      render: (item: WorkflowItem) => (
        <div className="flex items-center gap-2">
          {getStatusIcon((item as any as any).status)}
          <Badge className={getStatusColor((item as any as any).status)}>
            {String(t[(item as any as any).status as keyof typeof t])}
          </Badge>
        </div>
      )
    },
    {
      key: 'trigger_event',
      label: (t as any).trigger,
      sortable: true,
      render: (item: WorkflowItem) => (
        <Badge className="bg-blue-100 text-blue-800 border-blue-200">
          {(item as any).trigger_event || 'Manual'}
        </Badge>
      )
    },
    {
      key: 'priority',
      label: (t as any).priority,
      sortable: true,
      render: (item: WorkflowItem) => (
        <span className={`font-medium ${getPriorityColor((item as any as any).priority)}`}>
          {String(t[(item as any as any).priority as keyof typeof t])}
        </span>
      )
    },
    {
      key: 'run_count',
      label: (t as any).executionCount,
      sortable: true,
      render: (item: WorkflowItem) => (
        <div className="flex items-center gap-1">
          <Settings className="h-3 w-3 text-purple-400" />
          <span className="text-white font-medium">{(item as any).run_count}</span>
        </div>
      )
    },
    {
      key: 'success_rate',
      label: (t as any).successRate,
      sortable: true,
      render: (item: WorkflowItem) => (
        <div className="flex items-center gap-2">
          <span className="text-white font-medium">
            {(item as any).run_count > 0 ? (Math as any).round(((item as any as any).success_count / (item as any).run_count) * 100) : 0}%
          </span>
          <div className={`w-2 h-2 rounded-full ${
            (item as any).run_count > 0 && ((item as any).success_count / (item as any).run_count) * 100 >= 90 ? 'bg-green-400' :
            (item as any).run_count > 0 && ((item as any).success_count / (item as any).run_count) * 100 >= 70 ? 'bg-yellow-400' : 'bg-red-400'
          }`}></div>
        </div>
      )
    },
    {
      key: 'last_run',
      label: (t as any).lastRun,
      sortable: true,
      render: (item: WorkflowItem) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">
            {(item as any).last_run ? new Date((item as any as any).last_run).toLocaleDateString( as any) : 'Never'}
          </span>
        </div>
      )
    },
    {
      key: 'created_by',
      label: (t as any).assignedTo,
      render: (item: WorkflowItem) => (
        <div className="flex items-center gap-1">
          <User className="h-3 w-3 text-orange-400" />
          <span className="text-white/80">
            {(item as any).created_by?.user?.username || 'System'}
          </span>
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction[] = [
    {
      label: (t as any).view,
      icon: Eye,
      onClick: (item: (React as any).MouseEvent) => {
        selectItem(item as any)
        setModalMode('view' as any)
        setShowModal(true as any)
      },
      variant: 'ghost'
    },
    {
      label: (t as any).edit,
      icon: Edit,
      onClick: (item: (React as any).MouseEvent) => {
        selectItem(item as any)
        setModalMode('edit' as any)
        setShowModal(true as any)
      },
      variant: 'ghost'
    },
    {
      label: (t as any).delete,
      icon: Trash2,
      onClick: async (item: (React as any).MouseEvent) => {
        if ((window as any).confirm((t as any as any).confirmDelete)) {
          await deleteItem((item as any as any).id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'status',
      label: (t as any).status,
      options: [
        { label: (t as any).active, value: 'active' },
        { label: (t as any).inactive, value: 'inactive' },
        { label: (t as any).draft, value: 'draft' },
        { label: (t as any).archived, value: 'archived' }
      ]
    },
    {
      key: 'trigger',
      label: (t as any).trigger,
      options: [
        { label: (t as any).manual, value: 'manual' },
        { label: (t as any).automatic, value: 'automatic' },
        { label: (t as any).scheduled, value: 'scheduled' },
        { label: t['event-based'], value: 'event-based' }
      ]
    },
    {
      key: 'priority',
      label: (t as any).priority,
      options: [
        { label: (t as any).low, value: 'low' },
        { label: (t as any).medium, value: 'medium' },
        { label: (t as any).high, value: 'high' },
        { label: (t as any).critical, value: 'critical' }
      ]
    },
    {
      key: 'category',
      label: (t as any).category,
      options: [
        { label: (t as any).categories.approval, value: 'approval' },
        { label: (t as any).categories.notification, value: 'notification' },
        { label: (t as any).categories.dataProcessing, value: 'dataProcessing' },
        { label: (t as any).categories.reporting, value: 'reporting' },
        { label: (t as any).categories.integration, value: 'integration' },
        { label: (t as any).categories.maintenance, value: 'maintenance' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'name',
      label: (t as any).name,
      type: 'text',
      required: true
    },
    {
      name: 'nameAr',
      label: (t as any).name + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'description',
      label: (t as any).description,
      type: 'textarea',
      required: true
    },
    {
      name: 'descriptionAr',
      label: (t as any).description + ' (عربي)',
      type: 'textarea',
      required: true
    },
    {
      name: 'category',
      label: (t as any).category,
      type: 'text',
      required: true
    },
    {
      name: 'categoryAr',
      label: (t as any).category + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'status',
      label: (t as any).status,
      type: 'select',
      required: true,
      options: [
        { label: (t as any).active, value: 'active' },
        { label: (t as any).inactive, value: 'inactive' },
        { label: (t as any).draft, value: 'draft' },
        { label: (t as any).archived, value: 'archived' }
      ]
    },
    {
      name: 'trigger',
      label: (t as any).trigger,
      type: 'select',
      required: true,
      options: [
        { label: (t as any).manual, value: 'manual' },
        { label: (t as any).automatic, value: 'automatic' },
        { label: (t as any).scheduled, value: 'scheduled' },
        { label: t['event-based'], value: 'event-based' }
      ]
    },
    {
      name: 'priority',
      label: (t as any).priority,
      type: 'select',
      required: true,
      options: [
        { label: (t as any).low, value: 'low' },
        { label: (t as any).medium, value: 'medium' },
        { label: (t as any).high, value: 'high' },
        { label: (t as any).critical, value: 'critical' }
      ]
    },
    {
      name: 'createdBy',
      label: (t as any).createdBy,
      type: 'text',
      required: true
    },
    {
      name: 'createdByAr',
      label: (t as any).createdBy + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'assignedTo',
      label: (t as any).assignedTo,
      type: 'text',
      required: true
    },
    {
      name: 'assignedToAr',
      label: (t as any).assignedTo + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'department',
      label: (t as any).department,
      type: 'text',
      required: true
    },
    {
      name: 'departmentAr',
      label: (t as any).department + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'steps',
      label: (t as any).steps,
      type: 'number',
      required: true,
      min: 1
    },
    {
      name: 'conditions',
      label: (t as any).conditions,
      type: 'textarea',
      placeholder: 'Define workflow conditions'
    },
    {
      name: 'conditionsAr',
      label: (t as any).conditions + ' (عربي)',
      type: 'textarea',
      placeholder: 'تحديد شروط سير العمل'
    },
    {
      name: 'actions',
      label: (t as any).actions,
      type: 'textarea',
      placeholder: 'Define workflow actions'
    },
    {
      name: 'actionsAr',
      label: (t as any).actions + ' (عربي)',
      type: 'textarea',
      placeholder: 'تحديد إجراءات سير العمل'
    },
    {
      name: 'isAutomated',
      label: (t as any).isAutomated,
      type: 'checkbox'
    },
    {
      name: 'nextRun',
      label: (t as any).nextRun,
      type: 'datetime-local'
    }
  ]

  // Event handlers
  const handleCreate = (): void => {
    setModalMode('create' as any)
    setShowModal(true as any)
  }

  const handleModalClose = (): void => {
    setShowModal(false as any)
    selectItem(null as any)
    clearError( as any)
  }

  const handleSave = async (data: Partial<WorkflowItem>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data as any)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem((selectedItem as any as any).id, data)
      }
      setShowModal(false as any)
    } catch (error) {
      (console as any).error('Save error:', error as any)
    }
  }

  const handleExport = async () => {
    try {
      await exportData('csv' as any)
    } catch (error) {
      (console as any).error('Export error:', error as any)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={(t as any).workflowManagement}
        data={workflows}
        columns={columns}
        actions={actions as any}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={(t as any).searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? (t as any).addWorkflow : modalMode === 'edit' ? (t as any).editWorkflow : (t as any).view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}