import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Badge } from '../../components/ui/badge'
import { 
  GitBranch, 
  Plus, 
  Search, 
  Eye, 
  Edit, 
  Trash2,
  Play,
  Pause,
  CheckCircle,
  Clock,
  AlertCircle,
  Users,
  TrendingUp,
  BarChart3,
  ArrowRight,
  Settings
} from 'lucide-react'

interface WorkflowManagementProps {
  language: 'ar' | 'en'
}

interface WorkflowStep {
  id: string
  name: string
  nameAr: string
  assignee: string
  assigneeAr: string
  status: 'pending' | 'in-progress' | 'completed' | 'rejected'
  dueDate: string
  completedDate?: string
  notes?: string
  notesAr?: string
}

interface Workflow {
  id: string
  name: string
  nameAr: string
  description: string
  descriptionAr: string
  category: string
  categoryAr: string
  status: 'active' | 'inactive' | 'draft' | 'archived'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  createdBy: string
  createdByAr: string
  createdDate: string
  lastModified: string
  totalSteps: number
  completedSteps: number
  currentStep: number
  estimatedDuration: number // in days
  actualDuration?: number
  steps: WorkflowStep[]
  triggers: string[]
  triggersAr: string[]
  approvers: string[]
  approversAr: string[]
  department: string
  departmentAr: string
  isTemplate: boolean
  usageCount: number
}

const mockWorkflows: Workflow[] = [
  {
    id: '1',
    name: 'Employee Onboarding Process',
    nameAr: 'عملية إدخال الموظف الجديد',
    description: 'Complete workflow for onboarding new employees',
    descriptionAr: 'سير عمل كامل لإدخال الموظفين الجدد',
    category: 'HR',
    categoryAr: 'الموارد البشرية',
    status: 'active',
    priority: 'high',
    createdBy: 'HR Manager',
    createdByAr: 'مدير الموارد البشرية',
    createdDate: '2024-01-15',
    lastModified: '2024-01-20',
    totalSteps: 5,
    completedSteps: 3,
    currentStep: 4,
    estimatedDuration: 7,
    actualDuration: 5,
    steps: [
      {
        id: '1-1',
        name: 'Document Collection',
        nameAr: 'جمع الوثائق',
        assignee: 'HR Assistant',
        assigneeAr: 'مساعد الموارد البشرية',
        status: 'completed',
        dueDate: '2024-01-16',
        completedDate: '2024-01-16'
      },
      {
        id: '1-2',
        name: 'System Access Setup',
        nameAr: 'إعداد الوصول للنظام',
        assignee: 'IT Admin',
        assigneeAr: 'مدير تقنية المعلومات',
        status: 'completed',
        dueDate: '2024-01-17',
        completedDate: '2024-01-17'
      },
      {
        id: '1-3',
        name: 'Workspace Assignment',
        nameAr: 'تخصيص مكان العمل',
        assignee: 'Facilities Manager',
        assigneeAr: 'مدير المرافق',
        status: 'completed',
        dueDate: '2024-01-18',
        completedDate: '2024-01-18'
      },
      {
        id: '1-4',
        name: 'Training Schedule',
        nameAr: 'جدولة التدريب',
        assignee: 'Training Coordinator',
        assigneeAr: 'منسق التدريب',
        status: 'in-progress',
        dueDate: '2024-01-22'
      },
      {
        id: '1-5',
        name: 'Manager Introduction',
        nameAr: 'تعريف بالمدير',
        assignee: 'Direct Manager',
        assigneeAr: 'المدير المباشر',
        status: 'pending',
        dueDate: '2024-01-25'
      }
    ],
    triggers: ['New Employee Hired'],
    triggersAr: ['تم توظيف موظف جديد'],
    approvers: ['HR Manager', 'Department Manager'],
    approversAr: ['مدير الموارد البشرية', 'مدير القسم'],
    department: 'Human Resources',
    departmentAr: 'الموارد البشرية',
    isTemplate: true,
    usageCount: 25
  },
  {
    id: '2',
    name: 'Purchase Request Approval',
    nameAr: 'اعتماد طلب الشراء',
    description: 'Workflow for approving purchase requests',
    descriptionAr: 'سير عمل لاعتماد طلبات الشراء',
    category: 'Finance',
    categoryAr: 'المالية',
    status: 'active',
    priority: 'medium',
    createdBy: 'Finance Manager',
    createdByAr: 'مدير المالية',
    createdDate: '2024-01-10',
    lastModified: '2024-01-18',
    totalSteps: 4,
    completedSteps: 2,
    currentStep: 3,
    estimatedDuration: 3,
    steps: [
      {
        id: '2-1',
        name: 'Request Submission',
        nameAr: 'تقديم الطلب',
        assignee: 'Requester',
        assigneeAr: 'مقدم الطلب',
        status: 'completed',
        dueDate: '2024-01-18',
        completedDate: '2024-01-18'
      },
      {
        id: '2-2',
        name: 'Budget Verification',
        nameAr: 'التحقق من الميزانية',
        assignee: 'Budget Analyst',
        assigneeAr: 'محلل الميزانية',
        status: 'completed',
        dueDate: '2024-01-19',
        completedDate: '2024-01-19'
      },
      {
        id: '2-3',
        name: 'Manager Approval',
        nameAr: 'موافقة المدير',
        assignee: 'Department Manager',
        assigneeAr: 'مدير القسم',
        status: 'in-progress',
        dueDate: '2024-01-21'
      },
      {
        id: '2-4',
        name: 'Finance Approval',
        nameAr: 'موافقة المالية',
        assignee: 'Finance Manager',
        assigneeAr: 'مدير المالية',
        status: 'pending',
        dueDate: '2024-01-22'
      }
    ],
    triggers: ['Purchase Request Submitted'],
    triggersAr: ['تم تقديم طلب شراء'],
    approvers: ['Department Manager', 'Finance Manager'],
    approversAr: ['مدير القسم', 'مدير المالية'],
    department: 'Finance',
    departmentAr: 'المالية',
    isTemplate: true,
    usageCount: 45
  },
  {
    id: '3',
    name: 'Leave Request Process',
    nameAr: 'عملية طلب الإجازة',
    description: 'Employee leave request approval workflow',
    descriptionAr: 'سير عمل اعتماد طلب إجازة الموظف',
    category: 'HR',
    categoryAr: 'الموارد البشرية',
    status: 'active',
    priority: 'low',
    createdBy: 'HR Specialist',
    createdByAr: 'أخصائي الموارد البشرية',
    createdDate: '2024-01-12',
    lastModified: '2024-01-16',
    totalSteps: 3,
    completedSteps: 3,
    currentStep: 3,
    estimatedDuration: 2,
    actualDuration: 1,
    steps: [
      {
        id: '3-1',
        name: 'Leave Application',
        nameAr: 'طلب الإجازة',
        assignee: 'Employee',
        assigneeAr: 'الموظف',
        status: 'completed',
        dueDate: '2024-01-16',
        completedDate: '2024-01-16'
      },
      {
        id: '3-2',
        name: 'Manager Review',
        nameAr: 'مراجعة المدير',
        assignee: 'Direct Manager',
        assigneeAr: 'المدير المباشر',
        status: 'completed',
        dueDate: '2024-01-17',
        completedDate: '2024-01-17'
      },
      {
        id: '3-3',
        name: 'HR Approval',
        nameAr: 'موافقة الموارد البشرية',
        assignee: 'HR Manager',
        assigneeAr: 'مدير الموارد البشرية',
        status: 'completed',
        dueDate: '2024-01-18',
        completedDate: '2024-01-17'
      }
    ],
    triggers: ['Leave Request Submitted'],
    triggersAr: ['تم تقديم طلب إجازة'],
    approvers: ['Direct Manager', 'HR Manager'],
    approversAr: ['المدير المباشر', 'مدير الموارد البشرية'],
    department: 'Human Resources',
    departmentAr: 'الموارد البشرية',
    isTemplate: true,
    usageCount: 78
  }
]

export default function WorkflowManagement({ language }: WorkflowManagementProps) {
  const [workflows, setWorkflows] = useState<Workflow[]>(mockWorkflows)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [categoryFilter, setCategoryFilter] = useState<string>('all')
  const [priorityFilter, setPriorityFilter] = useState<string>('all')

  const text = {
    ar: {
      title: 'إدارة سير العمل',
      description: 'إدارة وأتمتة عمليات سير العمل والموافقات',
      newWorkflow: 'سير عمل جديد',
      search: 'البحث في سير العمل...',
      workflowName: 'اسم سير العمل',
      category: 'الفئة',
      status: 'الحالة',
      priority: 'الأولوية',
      progress: 'التقدم',
      currentStep: 'الخطوة الحالية',
      duration: 'المدة',
      usage: 'الاستخدام',
      actions: 'الإجراءات',
      view: 'عرض',
      edit: 'تعديل',
      delete: 'حذف',
      start: 'بدء',
      pause: 'إيقاف مؤقت',
      configure: 'تكوين',
      totalWorkflows: 'إجمالي سير العمل',
      activeWorkflows: 'سير العمل النشط',
      avgCompletion: 'متوسط الإنجاز',
      totalUsage: 'إجمالي الاستخدام',
      active: 'نشط',
      inactive: 'غير نشط',
      draft: 'مسودة',
      archived: 'مؤرشف',
      low: 'منخفض',
      medium: 'متوسط',
      high: 'عالي',
      urgent: 'عاجل',
      pending: 'معلق',
      'in-progress': 'قيد التنفيذ',
      completed: 'مكتمل',
      rejected: 'مرفوض',
      days: 'يوم',
      steps: 'خطوات',
      of: 'من',
      template: 'قالب',
      times: 'مرة'
    },
    en: {
      title: 'Workflow Management',
      description: 'Manage and automate workflow processes and approvals',
      newWorkflow: 'New Workflow',
      search: 'Search workflows...',
      workflowName: 'Workflow Name',
      category: 'Category',
      status: 'Status',
      priority: 'Priority',
      progress: 'Progress',
      currentStep: 'Current Step',
      duration: 'Duration',
      usage: 'Usage',
      actions: 'Actions',
      view: 'View',
      edit: 'Edit',
      delete: 'Delete',
      start: 'Start',
      pause: 'Pause',
      configure: 'Configure',
      totalWorkflows: 'Total Workflows',
      activeWorkflows: 'Active Workflows',
      avgCompletion: 'Avg Completion',
      totalUsage: 'Total Usage',
      active: 'Active',
      inactive: 'Inactive',
      draft: 'Draft',
      archived: 'Archived',
      low: 'Low',
      medium: 'Medium',
      high: 'High',
      urgent: 'Urgent',
      pending: 'Pending',
      'in-progress': 'In Progress',
      completed: 'Completed',
      rejected: 'Rejected',
      days: 'Days',
      steps: 'Steps',
      of: 'of',
      template: 'Template',
      times: 'times'
    }
  }

  const t = text[language]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-500/20 text-green-300 border-green-500/30'
      case 'inactive': return 'bg-gray-500/20 text-gray-300 border-gray-500/30'
      case 'draft': return 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30'
      case 'archived': return 'bg-purple-500/20 text-purple-300 border-purple-500/30'
      default: return 'bg-gray-500/20 text-gray-300 border-gray-500/30'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low': return 'bg-green-500/20 text-green-300 border-green-500/30'
      case 'medium': return 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30'
      case 'high': return 'bg-orange-500/20 text-orange-300 border-orange-500/30'
      case 'urgent': return 'bg-red-500/20 text-red-300 border-red-500/30'
      default: return 'bg-gray-500/20 text-gray-300 border-gray-500/30'
    }
  }

  const getStepStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-400'
      case 'in-progress': return 'text-blue-400'
      case 'pending': return 'text-yellow-400'
      case 'rejected': return 'text-red-400'
      default: return 'text-gray-400'
    }
  }

  const getStepStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="w-4 h-4" />
      case 'in-progress': return <Clock className="w-4 h-4" />
      case 'pending': return <Clock className="w-4 h-4" />
      case 'rejected': return <AlertCircle className="w-4 h-4" />
      default: return <Clock className="w-4 h-4" />
    }
  }

  const filteredWorkflows = workflows.filter(workflow => {
    const matchesSearch = workflow.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         workflow.nameAr.includes(searchTerm) ||
                         workflow.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         workflow.descriptionAr.includes(searchTerm)
    const matchesStatus = statusFilter === 'all' || workflow.status === statusFilter
    const matchesCategory = categoryFilter === 'all' || workflow.category === categoryFilter
    const matchesPriority = priorityFilter === 'all' || workflow.priority === priorityFilter
    return matchesSearch && matchesStatus && matchesCategory && matchesPriority
  })

  const activeWorkflows = workflows.filter(workflow => workflow.status === 'active').length
  const avgCompletion = workflows.length > 0 
    ? workflows.reduce((sum, workflow) => sum + (workflow.completedSteps / workflow.totalSteps * 100), 0) / workflows.length 
    : 0
  const totalUsage = workflows.reduce((sum, workflow) => sum + workflow.usageCount, 0)

  return (
    <div className="min-h-screen gradient-bg p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">{t.title}</h1>
            <p className="text-white/70">{t.description}</p>
          </div>
          <Button className="bg-gradient-to-r from-sky-500 to-blue-600 hover:from-sky-600 hover:to-blue-700">
            <Plus className="w-4 h-4 mr-2" />
            {t.newWorkflow}
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="glass-card border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.totalWorkflows}</p>
                  <p className="text-2xl font-bold text-white">{workflows.length}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-sky-500/20 flex items-center justify-center">
                  <GitBranch className="w-6 h-6 text-sky-400" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-card border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.activeWorkflows}</p>
                  <p className="text-2xl font-bold text-white">{activeWorkflows}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-green-500/20 flex items-center justify-center">
                  <Play className="w-6 h-6 text-green-400" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-card border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.avgCompletion}</p>
                  <p className="text-2xl font-bold text-white">{avgCompletion.toFixed(1)}%</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-purple-500/20 flex items-center justify-center">
                  <BarChart3 className="w-6 h-6 text-purple-400" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-card border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.totalUsage}</p>
                  <p className="text-2xl font-bold text-white">{totalUsage}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-orange-500/20 flex items-center justify-center">
                  <TrendingUp className="w-6 h-6 text-orange-400" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 w-4 h-4" />
                  <Input
                    placeholder={t.search}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 glass-input"
                  />
                </div>
              </div>
              <div className="flex gap-4">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="glass-input px-3 py-2 rounded-lg"
                >
                  <option value="all">جميع الحالات</option>
                  <option value="active">{t.active}</option>
                  <option value="inactive">{t.inactive}</option>
                  <option value="draft">{t.draft}</option>
                  <option value="archived">{t.archived}</option>
                </select>
                <select
                  value={categoryFilter}
                  onChange={(e) => setCategoryFilter(e.target.value)}
                  className="glass-input px-3 py-2 rounded-lg"
                >
                  <option value="all">جميع الفئات</option>
                  <option value="HR">الموارد البشرية</option>
                  <option value="Finance">المالية</option>
                  <option value="IT">تقنية المعلومات</option>
                  <option value="Operations">العمليات</option>
                </select>
                <select
                  value={priorityFilter}
                  onChange={(e) => setPriorityFilter(e.target.value)}
                  className="glass-input px-3 py-2 rounded-lg"
                >
                  <option value="all">جميع الأولويات</option>
                  <option value="low">{t.low}</option>
                  <option value="medium">{t.medium}</option>
                  <option value="high">{t.high}</option>
                  <option value="urgent">{t.urgent}</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Workflows Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {filteredWorkflows.map((workflow) => (
            <Card key={workflow.id} className="glass-card border-white/20">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <CardTitle className="text-white text-lg">
                        {language === 'ar' ? workflow.nameAr : workflow.name}
                      </CardTitle>
                      {workflow.isTemplate && (
                        <Badge variant="outline" className="text-xs bg-blue-500/20 text-blue-300">
                          {t.template}
                        </Badge>
                      )}
                    </div>
                    <CardDescription className="text-white/70 text-sm">
                      {language === 'ar' ? workflow.descriptionAr : workflow.description}
                    </CardDescription>
                  </div>
                  <div className="flex gap-1">
                    <Button size="sm" variant="ghost" className="h-6 w-6 p-0 text-white/70">
                      <Settings className="w-3 h-3" />
                    </Button>
                    <Button size="sm" variant="ghost" className="h-6 w-6 p-0 text-white/70">
                      <Edit className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between text-sm">
                  <div className="flex gap-2">
                    <Badge className={`${getStatusColor(workflow.status)} border text-xs`}>
                      {t[workflow.status as keyof typeof t]}
                    </Badge>
                    <Badge className={`${getPriorityColor(workflow.priority)} border text-xs`}>
                      {t[workflow.priority as keyof typeof t]}
                    </Badge>
                  </div>
                  <span className="text-white/70">
                    {workflow.usageCount} {t.times}
                  </span>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between text-sm text-white/70">
                    <span>{t.progress}</span>
                    <span>{workflow.completedSteps} {t.of} {workflow.totalSteps} {t.steps}</span>
                  </div>
                  <div className="w-full bg-white/10 rounded-full h-2">
                    <div 
                      className="bg-gradient-to-r from-sky-500 to-blue-600 h-2 rounded-full transition-all"
                      style={{ width: `${(workflow.completedSteps / workflow.totalSteps) * 100}%` }}
                    ></div>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="text-white text-sm font-medium">{t.currentStep}:</h4>
                  {workflow.steps.slice(workflow.currentStep - 1, workflow.currentStep).map((step) => (
                    <div key={step.id} className="flex items-center gap-2 p-2 rounded bg-white/5">
                      <div className={getStepStatusColor(step.status)}>
                        {getStepStatusIcon(step.status)}
                      </div>
                      <span className="text-white text-sm flex-1">
                        {language === 'ar' ? step.nameAr : step.name}
                      </span>
                      <span className="text-white/60 text-xs">
                        {language === 'ar' ? step.assigneeAr : step.assignee}
                      </span>
                    </div>
                  ))}
                </div>

                <div className="flex items-center justify-between text-sm text-white/70">
                  <span>
                    {workflow.estimatedDuration} {t.days} ({language === 'ar' ? workflow.categoryAr : workflow.category})
                  </span>
                  <span>{workflow.lastModified}</span>
                </div>

                <div className="flex gap-2 pt-2">
                  <Button size="sm" className="flex-1 bg-sky-600 hover:bg-sky-700">
                    <Eye className="w-4 h-4 mr-2" />
                    {t.view}
                  </Button>
                  <Button size="sm" variant="outline" className="glass-button">
                    <Play className="w-4 h-4" />
                  </Button>
                  <Button size="sm" variant="outline" className="glass-button">
                    <Pause className="w-4 h-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}
