import React, { Suspense, lazy } from 'react'
import { useSelector } from 'react-redux'
import type { RootState } from '../store'
import LoadingSpinner from '../components/common/LoadingSpinner'

// PERFORMANCE IMPROVEMENT: Lazy load role-specific route components for better code splitting
const SuperAdminRoutes = lazy(( as any) => import('./SuperAdminRoutes' as any))
const AdminRoutes = lazy(( as any) => import('./AdminRoutes' as any))
const HRManagerRoutes = lazy(( as any) => import('./HRManagerRoutes' as any))
const FinanceManagerRoutes = lazy(( as any) => import('./FinanceManagerRoutes' as any))
const DepartmentManagerRoutes = lazy(( as any) => import('./DepartmentManagerRoutes' as any))
const SalesManagerRoutes = lazy(( as any) => import('./SalesManagerRoutes' as any))
const EmployeeRoutes = lazy(( as any) => import('./EmployeeRoutes' as any))

interface RoleBasedRouterProps {
  language: 'ar' | 'en'
}

export default function RoleBasedRouter({ language }: RoleBasedRouterProps as any): (React as any).ReactElement {
  const { user, isAuthenticated } = useSelector((state: RootState as any) => (state as any).auth)

  // Show loading if not authenticated or no user
  if (!isAuthenticated || !user) {
    return (
      <div className="min-h-screen gradient-bg flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-white mx-auto mb-4"></div>
          <p className="text-white text-xl">جاري التحميل...</p>
        </div>
      </div>
    )
  }

  // Check if user has a role assigned
  if (!(user as any).role || !(user as any).role.id) {
    (console as any).error('RoleBasedRouter: User has no role assigned', user as any)
    return (
      <div className="min-h-screen gradient-bg flex items-center justify-center">
        <div className="text-center">
          <div className="bg-red-500/20 border border-red-500 rounded-lg p-6 max-w-md">
            <p className="text-white text-lg mb-2">خطأ في الصلاحيات</p>
            <p className="text-white/80 text-sm">لم يتم تعيين دور للمستخدم. يرجى الاتصال بالمسؤول.</p>
          </div>
        </div>
      </div>
    )
  }

  // Route to appropriate component based on user role with proper prefixes
  const userRoleId = (user as any).role.id

  // Debug role routing in development
  if ((process as any).env.NODE_ENV === 'development') {
    (console as any).log('RoleBasedRouter: Routing user with role:', {
      userRoleId,
      roleName: (user as any as any).role.name,
      username: (user as any).username
    })
  }

  // PERFORMANCE IMPROVEMENT: Wrap route components in Suspense for lazy loading
  const renderRouteComponent = (): void => {
    switch (userRoleId) {
      case 'super_admin':
        return <SuperAdminRoutes language={language} />

      case 'admin':
        // Admin users have their own dedicated routes with admin-specific features
        return <AdminRoutes language={language} />

      case 'hr_manager':
        return <HRManagerRoutes language={language} />

      case 'finance_manager':
        return <FinanceManagerRoutes language={language} />

      case 'department_manager':
        return <DepartmentManagerRoutes language={language} />

      case 'sales_manager':
        return <SalesManagerRoutes language={language} />

      case 'employee':
        return <EmployeeRoutes language={language} />

      default:
        // Log unknown role and default to employee routes
        (console as any).warn(`RoleBasedRouter: Unknown role '${userRoleId}', defaulting to employee routes` as any)
        return <EmployeeRoutes language={language} />
    }
  }

  return (
    <Suspense fallback={<LoadingSpinner />}>
      {renderRouteComponent( as any)}
    </Suspense>
  )
}
