import { lazy } from 'react';

// Lazy load all route components for better performance
export const LazyRoutes = {
  // Dashboard routes
  AdminDashboard: lazy(() => import('@/pages/dashboards/AdminDashboard')),
  SuperAdminDashboard: lazy(() => import('@/pages/dashboards/SuperAdminDashboard')),
  HRManagerDashboard: lazy(() => import('@/pages/dashboards/HRManagerDashboard')),
  FinanceManagerDashboard: lazy(() => import('@/pages/dashboards/FinanceManagerDashboard')),
  DepartmentManagerDashboard: lazy(() => import('@/pages/dashboards/DepartmentManagerDashboard')),
  SalesManagerDashboard: lazy(() => import('@/pages/dashboards/SalesManagerDashboard')),
  EmployeeDashboard: lazy(() => import('@/pages/dashboards/EmployeeDashboard')),

  // Admin routes
  UserManagement: lazy(() => import('@/pages/admin/UserManagement')),
  SystemSettings: lazy(() => import('@/pages/admin/SystemSettings')),
  AuditLogs: lazy(() => import('@/pages/admin/AuditLogs')),

  // HR routes
  Employees: lazy(() => import('@/pages/hr/Employees')),
  Departments: lazy(() => import('@/pages/hr/Departments')),
  LeaveManagement: lazy(() => import('@/pages/hr/LeaveManagement')),
  Attendance: lazy(() => import('@/pages/hr/Attendance')),
  Performance: lazy(() => import('@/pages/hr/Performance')),

  // Finance routes
  Budgets: lazy(() => import('@/pages/finance/Budgets')),
  Expenses: lazy(() => import('@/pages/finance/Expenses')),
  Invoices: lazy(() => import('@/pages/finance/Invoices')),
  Reports: lazy(() => import('@/pages/finance/Reports')),

  // Project routes
  Projects: lazy(() => import('@/pages/projects/Projects')),
  Tasks: lazy(() => import('@/pages/projects/Tasks')),
  ProjectReports: lazy(() => import('@/pages/projects/ProjectReports')),

  // Asset routes
  Assets: lazy(() => import('@/pages/assets/Assets')),
  AssetCategories: lazy(() => import('@/pages/assets/AssetCategories')),

  // CRM routes
  Customers: lazy(() => import('@/pages/crm/Customers')),
  Products: lazy(() => import('@/pages/crm/Products')),
  Sales: lazy(() => import('@/pages/crm/Sales')),

  // Communication routes
  Messages: lazy(() => import('@/pages/communication/Messages')),
  Announcements: lazy(() => import('@/pages/communication/Announcements')),
  Meetings: lazy(() => import('@/pages/communication/Meetings')),

  // KPI routes
  KPIDashboard: lazy(() => import('@/pages/kpi/KPIDashboard')),
  KPIManagement: lazy(() => import('@/pages/kpi/KPIManagement')),

  // Personal routes
  PersonalProfile: lazy(() => import('@/pages/personal/PersonalProfile')),
  PersonalCalendar: lazy(() => import('@/pages/personal/PersonalCalendar')),
  PersonalTasks: lazy(() => import('@/pages/personal/PersonalTasks')),

  // Employee-specific routes
  EmployeeTasks: lazy(() => import('@/pages/employee-specific/EmployeeTasks')),
  EmployeeLeave: lazy(() => import('@/pages/employee-specific/EmployeeLeave')),
  EmployeeAttendance: lazy(() => import('@/pages/employee-specific/EmployeeAttendance')),

  // Sales-specific routes
  SalesCustomers: lazy(() => import('@/pages/sales-specific/SalesCustomers')),
  SalesReports: lazy(() => import('@/pages/sales-specific/SalesReports')),

  // Quality routes
  QualityManagement: lazy(() => import('@/pages/quality/QualityManagement')),

  // Compliance routes
  RiskManagement: lazy(() => import('@/pages/compliance/RiskManagement')),

  // Customer Service routes
  CustomerService: lazy(() => import('@/pages/CustomerService/CustomerService')),

  // Public routes
  Home: lazy(() => import('@/pages/public/Home')),
  HowItWorks: lazy(() => import('@/pages/public/HowItWorks')),
  Login: lazy(() => import('@/pages/auth/Login')),
  EmployeeActivation: lazy(() => import('@/pages/auth/EmployeeActivation'))
};

export default LazyRoutes;
