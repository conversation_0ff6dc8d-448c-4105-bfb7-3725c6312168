/**
 * Authentication Service
 * Handles all authentication-related API calls
 */

import { apiClient, ApiResponse, LoginCredentials, AuthResponse, User } from './api'

export class AuthService {
  /**
   * Login user with credentials
   */
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      const response = await apiClient.post<AuthResponse>('/auth/login/', credentials)
      
      // Set the token in the API client
      if (response.data.access) {
        apiClient.setToken(response.data.access)
        
        // Store refresh token
        localStorage.setItem('refresh_token', response.data.refresh)
      }
      
      return response.data
    } catch (error) {
      console.error('Login failed:', error)
      throw error
    }
  }

  /**
   * Logout user
   */
  async logout(): Promise<void> {
    try {
      const refreshToken = localStorage.getItem('refresh_token')
      
      if (refreshToken) {
        await apiClient.post('/auth/logout/', { refresh: refreshToken })
      }
    } catch (error) {
      console.error('Logout error:', error)
      // Continue with logout even if API call fails
    } finally {
      // Clear tokens
      apiClient.setToken(null)
      localStorage.removeItem('refresh_token')
      localStorage.removeItem('access_token')
    }
  }

  /**
   * Refresh access token
   */
  async refreshToken(): Promise<string> {
    const refreshToken = localStorage.getItem('refresh_token')
    
    if (!refreshToken) {
      throw new Error('No refresh token available')
    }

    try {
      const response = await apiClient.post<{ access: string }>('/auth/refresh/', {
        refresh: refreshToken
      })
      
      const newAccessToken = response.data.access
      apiClient.setToken(newAccessToken)
      
      return newAccessToken
    } catch (error) {
      // If refresh fails, clear all tokens
      this.logout()
      throw error
    }
  }

  /**
   * Verify current token and get user data
   */
  async verifyToken(): Promise<User> {
    try {
      const response = await apiClient.get<User>('/auth/user/')
      return response.data
    } catch (error) {
      // If verification fails, try to refresh token
      try {
        await this.refreshToken()
        const response = await apiClient.get<User>('/auth/user/')
        return response.data
      } catch (refreshError) {
        // If refresh also fails, logout
        await this.logout()
        throw refreshError
      }
    }
  }

  /**
   * Register new user (if registration is enabled)
   */
  async register(userData: {
    username: string
    email: string
    password: string
    first_name: string
    last_name: string
  }): Promise<AuthResponse> {
    try {
      const response = await apiClient.post<AuthResponse>('/auth/register/', userData)
      
      if (response.data.access) {
        apiClient.setToken(response.data.access)
        localStorage.setItem('refresh_token', response.data.refresh)
      }
      
      return response.data
    } catch (error) {
      console.error('Registration failed:', error)
      throw error
    }
  }

  /**
   * Request password reset
   */
  async requestPasswordReset(email: string): Promise<void> {
    try {
      await apiClient.post('/auth/password-reset/', { email })
    } catch (error) {
      console.error('Password reset request failed:', error)
      throw error
    }
  }

  /**
   * Reset password with token
   */
  async resetPassword(token: string, newPassword: string): Promise<void> {
    try {
      await apiClient.post('/auth/password-reset-confirm/', {
        token,
        password: newPassword
      })
    } catch (error) {
      console.error('Password reset failed:', error)
      throw error
    }
  }

  /**
   * Change password for authenticated user
   */
  async changePassword(currentPassword: string, newPassword: string): Promise<void> {
    try {
      await apiClient.post('/auth/change-password/', {
        current_password: currentPassword,
        new_password: newPassword
      })
    } catch (error) {
      console.error('Password change failed:', error)
      throw error
    }
  }

  /**
   * Update user profile
   */
  async updateProfile(profileData: Partial<User>): Promise<User> {
    try {
      const response = await apiClient.patch<User>('/auth/profile/', profileData)
      return response.data
    } catch (error) {
      console.error('Profile update failed:', error)
      throw error
    }
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    const token = localStorage.getItem('access_token')
    return !!token
  }

  /**
   * Get current access token
   */
  getAccessToken(): string | null {
    return localStorage.getItem('access_token')
  }

  /**
   * Get current refresh token
   */
  getRefreshToken(): string | null {
    return localStorage.getItem('refresh_token')
  }
}

// Create and export auth service instance
export const authService = new AuthService()

// Export for convenience
export default authService
