/**
 * Authentication Service
 * Handles all authentication-related API calls
 */

import { apiClient, ApiResponse, LoginCredentials, AuthResponse, User } from './api'

export class AuthService {
  /**
   * Login user with credentials
   * MIGRATED: Now uses httpOnly cookies exclusively for enhanced security
   */
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      console.log('AuthService: Sending login request to API...')
      const response = await apiClient.post<AuthResponse>('/auth/login/', credentials)
      console.log('AuthService: Login API response received:', {
        status: response.status,
        hasAccessToken: !!response.data.access_token,
        hasUser: !!response.data.user,
        tokenType: response.data.token_type
      })

      // SECURITY MIGRATION: httpOnly cookies are set by backend automatically
      // No need to store tokens in localStorage - they're handled securely via cookies
      console.log('AuthService: Authentication tokens set via secure httpOnly cookies')

      return response.data
    } catch (error) {
      console.error('AuthService: Login failed:', error)
      throw error
    }
  }

  /**
   * Logout user and clear authentication state
   * MIGRATED: Now relies on httpOnly cookies exclusively
   */
  async logout(): Promise<void> {
    try {
      await apiClient.post('/auth/logout/', {})
      if (process.env.NODE_ENV === 'development') {
        console.log('AuthService: Logout successful - httpOnly cookies cleared by backend')
      }
    } catch (error) {
      console.error('Logout error:', error)
      // Continue with logout even if API call fails
    }
    // SECURITY MIGRATION: httpOnly cookies are cleared by backend automatically
    // No localStorage cleanup needed - authentication is fully cookie-based
  }

  /**
   * Verify current token and get user data
   */
  async verifyToken(): Promise<User> {
    try {
      const response = await apiClient.get<User>('/auth/user/')
      return response.data
    } catch (error) {
      console.warn('Token verification failed:', error)
      throw error
    }
  }

  /**
   * Register new user (if registration is enabled)
   */
  async register(userData: {
    username: string
    email: string
    password: string
    first_name: string
    last_name: string
  }): Promise<AuthResponse> {
    try {
      const response = await apiClient.post<AuthResponse>('/auth/register/', userData)

      // SECURITY FIX: No token storage needed - backend sets httpOnly cookies
      if (process.env.NODE_ENV === 'development') {
        console.log('AuthService: Registration successful - authentication cookies set by backend')
      }

      return response.data
    } catch (error) {
      console.error('Registration failed:', error)
      throw error
    }
  }

  /**
   * Sync Redux state with current authentication status
   * MIGRATED: Now uses httpOnly cookies exclusively for authentication verification
   */
  async syncAuthState(): Promise<User | null> {
    try {
      // SECURITY MIGRATION: Check only httpOnly cookies (more secure)
      // Note: We can't directly read httpOnly cookies, but we can verify via API
      console.log('AuthService: Syncing auth state via secure cookie verification...')

      // Try to verify current authentication status using httpOnly cookies
      const user = await this.verifyToken()
      console.log('AuthService: Auth state sync successful via httpOnly cookies:', user.username)
      return user
    } catch (error) {
      console.log('AuthService: Auth state sync failed - user not authenticated')
      // SECURITY MIGRATION: No localStorage cleanup needed
      // httpOnly cookies are managed by backend automatically
      return null
    }
  }

  /**
   * Request password reset
   */
  async requestPasswordReset(email: string): Promise<void> {
    try {
      await apiClient.post('/auth/password-reset/', { email })
    } catch (error) {
      console.error('Password reset request failed:', error)
      throw error
    }
  }

  /**
   * Reset password with token
   */
  async resetPassword(token: string, newPassword: string): Promise<void> {
    try {
      await apiClient.post('/auth/password-reset-confirm/', {
        token,
        password: newPassword
      })
    } catch (error) {
      console.error('Password reset failed:', error)
      throw error
    }
  }

  /**
   * Change password for authenticated user
   */
  async changePassword(currentPassword: string, newPassword: string): Promise<void> {
    try {
      await apiClient.post('/auth/change-password/', {
        current_password: currentPassword,
        new_password: newPassword
      })
    } catch (error) {
      console.error('Password change failed:', error)
      throw error
    }
  }

  /**
   * Update user profile
   */
  async updateProfile(profileData: Partial<User>): Promise<User> {
    try {
      const response = await apiClient.patch<User>('/auth/profile/', profileData)
      return response.data
    } catch (error) {
      console.error('Profile update failed:', error)
      throw error
    }
  }


}

// Create and export auth service instance
export const authService = new AuthService()

// Export for convenience
export default authService
