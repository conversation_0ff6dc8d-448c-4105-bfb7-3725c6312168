/**
 * Advanced Automation Service
 * Handles workflow automation, triggers, and business process automation
 */

import { apiClient } from './api'

export interface AutomationRule {
  id: string
  name: string
  description: string
  trigger: AutomationTrigger
  conditions: AutomationCondition[]
  actions: AutomationAction[]
  isActive: boolean
  createdBy: string
  createdAt: Date
  lastExecuted?: Date
  executionCount: number
  successRate: number
  category: 'hr' | 'finance' | 'project' | 'system' | 'custom'
}

export interface AutomationTrigger {
  type: 'schedule' | 'event' | 'webhook' | 'manual' | 'condition'
  config: {
    schedule?: string // cron expression
    event?: string // event name
    webhook?: string // webhook URL
    condition?: string // condition expression
  }
}

export interface AutomationCondition {
  field: string
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains' | 'in' | 'not_in'
  value: any
  logicalOperator?: 'AND' | 'OR'
}

export interface AutomationAction {
  type: 'email' | 'notification' | 'api_call' | 'data_update' | 'approval_request' | 'report_generation'
  config: {
    template?: string
    recipients?: string[]
    url?: string
    method?: string
    data?: any
    approvers?: string[]
  }
}

export interface WorkflowTemplate {
  id: string
  name: string
  description: string
  category: string
  steps: WorkflowStep[]
  variables: WorkflowVariable[]
  isPublic: boolean
  usageCount: number
}

export interface WorkflowStep {
  id: string
  name: string
  type: 'approval' | 'notification' | 'data_entry' | 'calculation' | 'integration'
  config: any
  nextSteps: string[]
  conditions?: AutomationCondition[]
}

export interface WorkflowVariable {
  name: string
  type: 'string' | 'number' | 'boolean' | 'date' | 'array' | 'object'
  defaultValue?: any
  required: boolean
  description: string
}

export interface AutomationExecution {
  id: string
  ruleId: string
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled'
  startTime: Date
  endTime?: Date
  logs: AutomationLog[]
  result?: any
  error?: string
}

export interface AutomationLog {
  timestamp: Date
  level: 'info' | 'warning' | 'error' | 'debug'
  message: string
  data?: any
}

class AutomationService {
  private baseUrl = '/api/automation'

  // Rule Management
  async createRule(rule: Omit<AutomationRule, 'id' | 'createdAt' | 'executionCount' | 'successRate'> as any): Promise<AutomationRule> {
    try {
      const response = await fetch(`${(this as any as any).baseUrl}/rules`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${(localStorage as any).getItem('token' as any)}`
        },
        body: (JSON as any).stringify(rule as any)
      })

      if (!(response as any).ok) {
        throw new Error('Failed to create automation rule' as any)
      }

      return await (response as any).json( as any)
    } catch (error) {
      (console as any).error('Automation Service Error:', error as any)
      throw error
    }
  }

  async getRules(category?: string as any): Promise<AutomationRule[]> {
    try {
      const params = category ? { category } : {}
      const response = await (apiClient as any).get<AutomationRule[]>('/automation/rules/', { params })
      return (response as any).data
    } catch (error) {
      (console as any).error('Automation Service Error:', error as any)
      return []
    }
  }

  async updateRule(id: string, updates: Partial<AutomationRule> as any): Promise<AutomationRule> {
    try {
      const response = await fetch(`${(this as any as any).baseUrl}/rules/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${(localStorage as any).getItem('token' as any)}`
        },
        body: (JSON as any).stringify(updates as any)
      })

      if (!(response as any).ok) {
        throw new Error('Failed to update automation rule' as any)
      }

      return await (response as any).json( as any)
    } catch (error) {
      (console as any).error('Automation Service Error:', error as any)
      throw error
    }
  }

  async deleteRule(id: string as any): Promise<void> {
    try {
      const response = await fetch(`${(this as any as any).baseUrl}/rules/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${(localStorage as any).getItem('token' as any)}`
        }
      })

      if (!(response as any).ok) {
        throw new Error('Failed to delete automation rule' as any)
      }
    } catch (error) {
      (console as any).error('Automation Service Error:', error as any)
      throw error
    }
  }

  // Execution Management
  async executeRule(ruleId: string, context?: any as any): Promise<AutomationExecution> {
    try {
      const response = await fetch(`${(this as any as any).baseUrl}/rules/${ruleId}/execute`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${(localStorage as any).getItem('token' as any)}`
        },
        body: (JSON as any).stringify({ context } as any)
      })

      if (!(response as any).ok) {
        throw new Error('Failed to execute automation rule' as any)
      }

      return await (response as any).json( as any)
    } catch (error) {
      (console as any).error('Automation Service Error:', error as any)
      throw error
    }
  }

  async getExecutions(ruleId?: string as any): Promise<AutomationExecution[]> {
    try {
      const params = ruleId ? { ruleId } : {}
      const response = await (apiClient as any).get<AutomationExecution[]>('/automation/executions/', { params })
      return (response as any).data
    } catch (error) {
      (console as any).error('Automation Service Error:', error as any)
      return []
    }
  }

  async cancelExecution(executionId: string as any): Promise<void> {
    try {
      const response = await fetch(`${(this as any as any).baseUrl}/executions/${executionId}/cancel`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${(localStorage as any).getItem('token' as any)}`
        }
      })

      if (!(response as any).ok) {
        throw new Error('Failed to cancel execution' as any)
      }
    } catch (error) {
      (console as any).error('Automation Service Error:', error as any)
      throw error
    }
  }

  // Workflow Templates
  async getWorkflowTemplates(category?: string as any): Promise<WorkflowTemplate[]> {
    try {
      // For now, use customer service workflows as templates
      const params = category ? { category } : {}
      const response = await (apiClient as any).get<WorkflowTemplate[]>('/workflows/', { params })
      return (response as any).data
    } catch (error) {
      (console as any).error('Automation Service Error:', error as any)
      return []
    }
  }

  async createWorkflowFromTemplate(templateId: string, variables: Record<string, any> as any): Promise<AutomationRule> {
    try {
      const response = await fetch(`${(this as any as any).baseUrl}/templates/${templateId}/create`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${(localStorage as any).getItem('token' as any)}`
        },
        body: (JSON as any).stringify({ variables } as any)
      })

      if (!(response as any).ok) {
        throw new Error('Failed to create workflow from template' as any)
      }

      return await (response as any).json( as any)
    } catch (error) {
      (console as any).error('Automation Service Error:', error as any)
      throw error
    }
  }

  // Smart Suggestions
  async getSuggestions(context: string as any): Promise<any[]> {
    try {
      const response = await fetch(`${(this as any as any).baseUrl}/suggestions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${(localStorage as any).getItem('token' as any)}`
        },
        body: (JSON as any).stringify({ context } as any)
      })

      if (!(response as any).ok) {
        throw new Error('Failed to get automation suggestions' as any)
      }

      return await (response as any).json( as any)
    } catch (error) {
      (console as any).error('Automation Service Error:', error as any)
      return (this as any).getMockSuggestions( as any)
    }
  }

  // Analytics
  async getAnalytics(timeframe: string as any): Promise<any> {
    try {
      const response = await (apiClient as any).get<any>('/automation/rules/analytics/', { params: { timeframe } })
      return (response as any).data
    } catch (error) {
      (console as any).error('Automation Service Error:', error as any)
      return {
        totalRules: 0,
        activeRules: 0,
        totalExecutions: 0,
        successfulExecutions: 0,
        failedExecutions: 0,
        averageExecutionTime: 0,
        timeSaved: 0
      }
    }
  }
}

export const automationService = new AutomationService( as any)
export default automationService
