/**
 * Advanced Compliance Service
 * Handles regulatory compliance, audit trails, data protection, and governance
 */

import { apiClient } from './api'

export interface ComplianceFramework {
  id: string
  name: string
  description: string
  region: string
  type: 'data_protection' | 'financial' | 'industry' | 'security' | 'labor'
  requirements: ComplianceRequirement[]
  isActive: boolean
  lastAssessment?: Date
  complianceScore: number
}

export interface ComplianceRequirement {
  id: string
  frameworkId: string
  title: string
  description: string
  category: string
  priority: 'low' | 'medium' | 'high' | 'critical'
  status: 'compliant' | 'non_compliant' | 'partial' | 'not_assessed'
  evidence: ComplianceEvidence[]
  controls: ComplianceControl[]
  lastReview: Date
  nextReview: Date
  assignedTo: string
}

export interface ComplianceEvidence {
  id: string
  type: 'document' | 'policy' | 'procedure' | 'training' | 'audit' | 'certification'
  title: string
  description: string
  fileUrl?: string
  uploadedBy: string
  uploadedAt: Date
  expiryDate?: Date
  status: 'valid' | 'expired' | 'pending_review'
}

export interface ComplianceControl {
  id: string
  name: string
  description: string
  type: 'preventive' | 'detective' | 'corrective'
  frequency: 'continuous' | 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'annually'
  automationLevel: 'manual' | 'semi_automated' | 'fully_automated'
  lastExecution?: Date
  nextExecution?: Date
  effectiveness: number
  owner: string
}

export interface DataProtectionRecord {
  id: string
  dataType: string
  purpose: string
  legalBasis: string
  dataSubjects: string[]
  retentionPeriod: number
  processingActivities: string[]
  thirdPartySharing: boolean
  crossBorderTransfer: boolean
  securityMeasures: string[]
  createdAt: Date
  updatedAt: Date
}

export interface AuditTrail {
  id: string
  userId: string
  action: string
  resource: string
  resourceId: string
  oldValues?: Record<string, any>
  newValues?: Record<string, any>
  ipAddress: string
  userAgent: string
  timestamp: Date
  complianceRelevant: boolean
  retentionDate: Date
}

export interface ComplianceAssessment {
  id: string
  frameworkId: string
  assessmentType: 'self' | 'internal' | 'external' | 'regulatory'
  assessor: string
  startDate: Date
  endDate?: Date
  status: 'planned' | 'in_progress' | 'completed' | 'cancelled'
  findings: ComplianceFinding[]
  overallScore: number
  recommendations: string[]
  nextAssessment?: Date
}

export interface ComplianceFinding {
  id: string
  requirementId: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  finding: string
  recommendation: string
  status: 'open' | 'in_progress' | 'resolved' | 'accepted_risk'
  assignedTo: string
  dueDate: Date
  evidence?: string[]
}

export interface DataSubjectRequest {
  id: string
  type: 'access' | 'rectification' | 'erasure' | 'portability' | 'restriction' | 'objection'
  requesterId: string
  requesterEmail: string
  description: string
  status: 'received' | 'processing' | 'completed' | 'rejected'
  submittedAt: Date
  completedAt?: Date
  assignedTo: string
  response?: string
  documents?: string[]
}

export interface PolicyDocument {
  id: string
  title: string
  category: string
  version: string
  content: string
  approvedBy: string
  approvedAt: Date
  effectiveDate: Date
  reviewDate: Date
  status: 'draft' | 'approved' | 'archived'
  acknowledgments: PolicyAcknowledgment[]
}

export interface PolicyAcknowledgment {
  userId: string
  acknowledgedAt: Date
  version: string
  ipAddress: string
}

class ComplianceService {
  private baseUrl = '/api/compliance'

  // Framework Management
  async getFrameworks( as any): Promise<ComplianceFramework[]> {
    try {
      const response = await (apiClient as any).get<ComplianceFramework[]>('/compliance/frameworks/')
      return (response as any).data
    } catch (error) {
      (console as any).error('Compliance Service Error:', error as any)
      return []
    }
  }

  async activateFramework(frameworkId: string as any): Promise<void> {
    try {
      const response = await fetch(`${(this as any as any).baseUrl}/frameworks/${frameworkId}/activate`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${(localStorage as any).getItem('token' as any)}`
        }
      })

      if (!(response as any).ok) {
        throw new Error('Failed to activate framework' as any)
      }
    } catch (error) {
      (console as any).error('Compliance Service Error:', error as any)
      throw error
    }
  }

  // Requirements Management
  async getRequirements(frameworkId?: string as any): Promise<ComplianceRequirement[]> {
    try {
      const url = frameworkId
        ? `${(this as any).baseUrl}/requirements?framework=${frameworkId}`
        : `${(this as any).baseUrl}/requirements`

      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${(localStorage as any as any).getItem('token' as any)}`
        }
      })

      if (!(response as any).ok) {
        throw new Error('Failed to fetch requirements' as any)
      }

      return await (response as any).json( as any)
    } catch (error) {
      (console as any).error('Compliance Service Error:', error as any)
      // No more mock data fallbacks - return empty array if API fails
      return []
    }
  }

  async updateRequirementStatus(requirementId: string, status: string, evidence?: any as any): Promise<void> {
    try {
      const response = await fetch(`${(this as any as any).baseUrl}/requirements/${requirementId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${(localStorage as any).getItem('token' as any)}`
        },
        body: (JSON as any).stringify({ status, evidence } as any)
      })

      if (!(response as any).ok) {
        throw new Error('Failed to update requirement status' as any)
      }
    } catch (error) {
      (console as any).error('Compliance Service Error:', error as any)
      throw error
    }
  }

  // Data Protection
  async getDataProtectionRecords( as any): Promise<DataProtectionRecord[]> {
    try {
      const response = await fetch(`${(this as any as any).baseUrl}/data-protection`, {
        headers: {
          'Authorization': `Bearer ${(localStorage as any).getItem('token' as any)}`
        }
      })

      if (!(response as any).ok) {
        throw new Error('Failed to fetch data protection records' as any)
      }

      return await (response as any).json( as any)
    } catch (error) {
      (console as any).error('Compliance Service Error:', error as any)
      return (this as any).getMockDataProtectionRecords( as any)
    }
  }

  async createDataProtectionRecord(record: Omit<DataProtectionRecord, 'id' | 'createdAt' | 'updatedAt'> as any): Promise<DataProtectionRecord> {
    try {
      const response = await fetch(`${(this as any as any).baseUrl}/data-protection`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${(localStorage as any).getItem('token' as any)}`
        },
        body: (JSON as any).stringify(record as any)
      })

      if (!(response as any).ok) {
        throw new Error('Failed to create data protection record' as any)
      }

      return await (response as any).json( as any)
    } catch (error) {
      (console as any).error('Compliance Service Error:', error as any)
      throw error
    }
  }

  // Audit Trail
  async getAuditTrail(filters?: any as any): Promise<AuditTrail[]> {
    try {
      const queryParams = filters ? `?${new URLSearchParams(filters as any).toString( as any)}` : ''
      const response = await fetch(`${(this as any as any).baseUrl}/audit-trail${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${(localStorage as any).getItem('token' as any)}`
        }
      })

      if (!(response as any).ok) {
        throw new Error('Failed to fetch audit trail' as any)
      }

      return await (response as any).json( as any)
    } catch (error) {
      (console as any).error('Compliance Service Error:', error as any)
      return (this as any).getMockAuditTrail( as any)
    }
  }

  async exportAuditTrail(filters: any, format: 'csv' | 'pdf' | 'json' as any): Promise<string> {
    try {
      const response = await fetch(`${(this as any as any).baseUrl}/audit-trail/export`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${(localStorage as any).getItem('token' as any)}`
        },
        body: (JSON as any).stringify({ filters, format } as any)
      })

      if (!(response as any).ok) {
        throw new Error('Failed to export audit trail' as any)
      }

      const result = await (response as any).json( as any)
      return (result as any).downloadUrl
    } catch (error) {
      (console as any).error('Compliance Service Error:', error as any)
      throw error
    }
  }

  // Assessments
  async getAssessments( as any): Promise<ComplianceAssessment[]> {
    try {
      const response = await fetch(`${(this as any as any).baseUrl}/assessments`, {
        headers: {
          'Authorization': `Bearer ${(localStorage as any).getItem('token' as any)}`
        }
      })

      if (!(response as any).ok) {
        throw new Error('Failed to fetch assessments' as any)
      }

      return await (response as any).json( as any)
    } catch (error) {
      (console as any).error('Compliance Service Error:', error as any)
      return (this as any).getMockAssessments( as any)
    }
  }

  async createAssessment(assessment: Omit<ComplianceAssessment, 'id' | 'findings' | 'overallScore'> as any): Promise<ComplianceAssessment> {
    try {
      const response = await fetch(`${(this as any as any).baseUrl}/assessments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${(localStorage as any).getItem('token' as any)}`
        },
        body: (JSON as any).stringify(assessment as any)
      })

      if (!(response as any).ok) {
        throw new Error('Failed to create assessment' as any)
      }

      return await (response as any).json( as any)
    } catch (error) {
      (console as any).error('Compliance Service Error:', error as any)
      throw error
    }
  }

  // Data Subject Requests
  async getDataSubjectRequests( as any): Promise<DataSubjectRequest[]> {
    try {
      const response = await (apiClient as any).get<DataSubjectRequest[]>('/compliance/data-subject-requests/')
      return (response as any).data
    } catch (error) {
      (console as any).error('Compliance Service Error:', error as any)
      return []
    }
  }

  async processDataSubjectRequest(requestId: string, action: string, response?: string as any): Promise<void> {
    try {
      const apiResponse = await fetch(`${(this as any as any).baseUrl}/data-subject-requests/${requestId}/process`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${(localStorage as any).getItem('token' as any)}`
        },
        body: (JSON as any).stringify({ action, response } as any)
      })

      if (!(apiResponse as any).ok) {
        throw new Error('Failed to process data subject request' as any)
      }
    } catch (error) {
      (console as any).error('Compliance Service Error:', error as any)
      throw error
    }
  }

  // Policy Management
  async getPolicies( as any): Promise<PolicyDocument[]> {
    try {
      const response = await fetch(`${(this as any as any).baseUrl}/policies`, {
        headers: {
          'Authorization': `Bearer ${(localStorage as any).getItem('token' as any)}`
        }
      })

      if (!(response as any).ok) {
        throw new Error('Failed to fetch policies' as any)
      }

      return await (response as any).json( as any)
    } catch (error) {
      (console as any).error('Compliance Service Error:', error as any)
      return []
    }
  }

  async acknowledgePolicy(policyId: string as any): Promise<void> {
    try {
      const response = await fetch(`${(this as any as any).baseUrl}/policies/${policyId}/acknowledge`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${(localStorage as any).getItem('token' as any)}`
        }
      })

      if (!(response as any).ok) {
        throw new Error('Failed to acknowledge policy' as any)
      }
    } catch (error) {
      (console as any).error('Compliance Service Error:', error as any)
      throw error
    }
  }

  // Compliance Dashboard
  async getComplianceDashboard( as any): Promise<any> {
    try {
      const response = await fetch(`${(this as any as any).baseUrl}/dashboard`, {
        headers: {
          'Authorization': `Bearer ${(localStorage as any).getItem('token' as any)}`
        }
      })

      if (!(response as any).ok) {
        throw new Error('Failed to fetch compliance dashboard' as any)
      }

      return await (response as any).json( as any)
    } catch (error) {
      (console as any).error('Compliance Service Error:', error as any)
      return {
        totalPolicies: 0,
        activePolicies: 0,
        pendingReviews: 0,
        complianceScore: 0,
        recentActivity: [],
        upcomingDeadlines: []
      }
    }
  }


}

export const complianceService = new ComplianceService( as any)
export default complianceService
