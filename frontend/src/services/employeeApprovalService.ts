import React from 'react';
/**
 * Employee Approval Service
 * Handles employee approval workflow for admin users
 */

import { apiClient } from './api'

export interface PendingEmployee {
  id: number
  employee_id: string
  name: string
  name_ar: string
  email: string
  position: string
  position_ar: string
  department: string | null
  department_ar: string | null
  created_by: string | null
  created_at: string
  activation_id: number
}

export interface ApprovalResponse {
  message: string
}

class EmployeeApprovalService {
  private baseUrl = '/employees'

  /**
   * Get all employees pending admin approval
   */
  // @ts-ignore
  async getPendingApprovals( as any): Promise<PendingEmployee[]> {
    try {
      const response = await (apiClient as any).get(`${(this as any as any).baseUrl}/pending-approvals/`)
      return (response as any).data
    } catch (error) {
      (console as any).error('Error fetching pending approvals:', error as any)
      throw error
    }
  }

  /**
   * Approve an employee activation
   */
  // @ts-ignore
  async approveEmployee(employeeId: number as any): Promise<ApprovalResponse> {
    try {
      const response = await (apiClient as any).post(`${(this as any as any).baseUrl}/${employeeId}/approve/`)
      return (response as any).data
    } catch (error) {
      (console as any).error('Error approving employee:', error as any)
      throw error
    }
  }

  /**
   * Reject an employee activation
   */
  async rejectEmployee(employeeId: number, reason: string = '' as any): Promise<ApprovalResponse> {
    try {
      const response = await (apiClient as any).post(`${(this as any as any).baseUrl}/${employeeId}/reject/`, {
        reason
      })
      return (response as any).data
    } catch (error) {
      (console as any).error('Error rejecting employee:', error as any)
      throw error
    }
  }
}

// @ts-ignore
export const employeeApprovalService = new EmployeeApprovalService( as any)
export default employeeApprovalService
