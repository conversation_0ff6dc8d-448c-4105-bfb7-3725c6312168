/**
 * Enhanced API Service
 * Provides improved API functionality with better error handling and type safety
 */

interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: Record<string, string[]>;
  status?: number;
}

interface ApiRequestConfig {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: any;
  timeout?: number;
}

interface ApiError {
  message: string;
  status?: number;
  code?: string;
  details?: any;
}

class EnhancedAPI {
  private baseURL: string;
  private defaultHeaders: Record<string, string>;
  private timeout: number;

  constructor(baseURL: string = '/api') {
    this.baseURL = baseURL;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    };
    this.timeout = 30000; // 30 seconds
  }

  private getAuthToken(): string | null {
    try {
      return localStorage.getItem('auth_token');
    } catch (error) {
      console.warn('Failed to get auth token:', error);
      return null;
    }
  }

  private buildHeaders(customHeaders?: Record<string, string>): Record<string, string> {
    const headers = { ...this.defaultHeaders };
    
    const token = this.getAuthToken();
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    // Add CSRF token if available
    const csrfToken = this.getCSRFToken();
    if (csrfToken) {
      headers['X-CSRFToken'] = csrfToken;
    }

    return { ...headers, ...customHeaders };
  }

  private getCSRFToken(): string | null {
    try {
      const cookies = document.cookie.split(';');
      for (const cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === 'csrftoken') {
          return value;
        }
      }
      return null;
    } catch (error) {
      console.warn('Failed to get CSRF token:', error);
      return null;
    }
  }

  private async makeRequest<T>(
    endpoint: string, 
    config: ApiRequestConfig = {}
  ): Promise<ApiResponse<T>> {
    const {
      method = 'GET',
      headers: customHeaders,
      body,
      timeout = this.timeout
    } = config;

    const url = `${this.baseURL}${endpoint}`;
    const headers = this.buildHeaders(customHeaders);

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const requestConfig: RequestInit = {
        method,
        headers,
        signal: controller.signal
      };

      if (body && method !== 'GET') {
        requestConfig.body = typeof body === 'string' ? body : JSON.stringify(body);
      }

      const response = await fetch(url, requestConfig);
      clearTimeout(timeoutId);

      let responseData: any;
      const contentType = response.headers.get('content-type');
      
      if (contentType && contentType.includes('application/json')) {
        responseData = await response.json();
      } else {
        responseData = await response.text();
      }

      if (!response.ok) {
        throw new Error(responseData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      return {
        success: true,
        data: responseData,
        status: response.status
      };

    } catch (error: any) {
      clearTimeout(timeoutId);
      
      if (error.name === 'AbortError') {
        throw new Error('Request timeout');
      }

      throw error;
    }
  }

  // GET request
  public async get<T>(endpoint: string, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(endpoint, { method: 'GET', headers });
  }

  // POST request
  public async post<T>(
    endpoint: string, 
    data?: any, 
    headers?: Record<string, string>
  ): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(endpoint, { method: 'POST', body: data, headers });
  }

  // PUT request
  public async put<T>(
    endpoint: string, 
    data?: any, 
    headers?: Record<string, string>
  ): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(endpoint, { method: 'PUT', body: data, headers });
  }

  // DELETE request
  public async delete<T>(endpoint: string, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(endpoint, { method: 'DELETE', headers });
  }

  // PATCH request
  public async patch<T>(
    endpoint: string, 
    data?: any, 
    headers?: Record<string, string>
  ): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(endpoint, { method: 'PATCH', body: data, headers });
  }

  // Upload file
  public async uploadFile<T>(
    endpoint: string, 
    file: File, 
    additionalData?: Record<string, any>
  ): Promise<ApiResponse<T>> {
    const formData = new FormData();
    formData.append('file', file);

    if (additionalData) {
      Object.entries(additionalData).forEach(([key, value]) => {
        formData.append(key, String(value));
      });
    }

    const headers = this.buildHeaders();
    delete headers['Content-Type']; // Let browser set it for FormData

    return this.makeRequest<T>(endpoint, {
      method: 'POST',
      body: formData,
      headers
    });
  }

  // Set auth token
  public setAuthToken(token: string): void {
    try {
      localStorage.setItem('auth_token', token);
    } catch (error) {
      console.warn('Failed to set auth token:', error);
    }
  }

  // Clear auth token
  public clearAuthToken(): void {
    try {
      localStorage.removeItem('auth_token');
    } catch (error) {
      console.warn('Failed to clear auth token:', error);
    }
  }

  // Set base URL
  public setBaseURL(url: string): void {
    this.baseURL = url;
  }

  // Set default timeout
  public setTimeout(timeout: number): void {
    this.timeout = timeout;
  }
}

// Create singleton instance
const enhancedAPI = new EnhancedAPI();

// Export convenience functions
export const apiGet = <T>(endpoint: string, headers?: Record<string, string>): Promise<ApiResponse<T>> => {
  return enhancedAPI.get<T>(endpoint, headers);
};

export const apiPost = <T>(endpoint: string, data?: any, headers?: Record<string, string>): Promise<ApiResponse<T>> => {
  return enhancedAPI.post<T>(endpoint, data, headers);
};

export const apiPut = <T>(endpoint: string, data?: any, headers?: Record<string, string>): Promise<ApiResponse<T>> => {
  return enhancedAPI.put<T>(endpoint, data, headers);
};

export const apiDelete = <T>(endpoint: string, headers?: Record<string, string>): Promise<ApiResponse<T>> => {
  return enhancedAPI.delete<T>(endpoint, headers);
};

export const apiPatch = <T>(endpoint: string, data?: any, headers?: Record<string, string>): Promise<ApiResponse<T>> => {
  return enhancedAPI.patch<T>(endpoint, data, headers);
};

export const apiUploadFile = <T>(endpoint: string, file: File, additionalData?: Record<string, any>): Promise<ApiResponse<T>> => {
  return enhancedAPI.uploadFile<T>(endpoint, file, additionalData);
};

export const setAuthToken = (token: string): void => {
  enhancedAPI.setAuthToken(token);
};

export const clearAuthToken = (): void => {
  enhancedAPI.clearAuthToken();
};

export default enhancedAPI;
