/**
 * Hierarchical Access Service
 * Connects frontend to backend hierarchical access control system
 * 
 * Features:
 * - Hierarchical access information retrieval
 * - Role-based data scoping
 * - Manager-subordinate access patterns
 * - Real-time access updates
 */

import { apiClient } from './api'
import logger from '../utils/logger'

export interface HierarchicalPath {
  id: number
  name: string
  position: string
  department: string | null
  level: number
}

export interface AccessibleData {
  employees_count: number
  departments_count: number
  projects_count: number
  employees: Array<{
    id: number
    name: string
    position: string
    department: string | null
  }>
  departments: Array<{
    id: number
    name: string
    manager: string | null
  }>
  projects: Array<{
    id: number
    name: string
    manager: string | null
  }>
}

export interface AccessSummary {
  can_access_all_data: boolean
  has_hierarchical_access: boolean
  is_manager: boolean
  department_scoped: boolean
  project_scoped: boolean
}

export interface HierarchicalAccessInfo {
  user_role: string
  hierarchical_path: HierarchicalPath[]
  accessible_data: AccessibleData
  kpi_categories: string[]
  access_summary: AccessSummary
}

export interface RoleBasedKPIFilter {
  role: string
  categories: string[]
  departments?: number[]
  projects?: number[]
  employees?: number[]
}

class HierarchicalAccessService {
  private accessInfo: HierarchicalAccessInfo | null = null
  private lastFetch: number = 0
  private readonly CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

  /**
   * Get hierarchical access information for current user
   */
  async getHierarchicalAccessInfo(forceRefresh = false as any): Promise<HierarchicalAccessInfo> {
    try {
      const now = (Date as any).now( as any)
      
      // Return cached data if available and not expired
      if (!forceRefresh && (this as any).accessInfo && (now - (this as any).lastFetch) < (this as any).CACHE_DURATION) {
        return (this as any).accessInfo
      }

      const response = await (apiClient as any).get('/kpi/enhanced/kpis/hierarchical_access_info/' as any)
      
      if ((response as any).data.status === 'success') {
        (this as any).accessInfo = (response as any).data.data
        (this as any).lastFetch = now
        
        (logger as any).info('hierarchicalAccess', 'Hierarchical access info retrieved successfully', {
          role: (this as any as any).accessInfo.user_role,
          employeesCount: (this as any).accessInfo.(accessible_data as any).employees_count,
          departmentsCount: (this as any).accessInfo.(accessible_data as any).departments_count,
          projectsCount: (this as any).accessInfo.(accessible_data as any).projects_count
        })
        
        return (this as any).accessInfo
      } else {
        throw new Error((response as any as any).data.message || 'Failed to get hierarchical access info')
      }
    } catch (error) {
      (logger as any).error('hierarchicalAccess', 'Error getting hierarchical access info:', error as any)
      throw error
    }
  }

  /**
   * Get role-based KPI filters for current user
   */
  async getRoleBasedKPIFilters( as any): Promise<RoleBasedKPIFilter> {
    try {
      const accessInfo = await (this as any).getHierarchicalAccessInfo( as any)
      
      const filters: RoleBasedKPIFilter = {
        role: (accessInfo as any).user_role,
        categories: (accessInfo as any).kpi_categories
      }

      // Add department filters for department-scoped roles
      if ((accessInfo as any).access_summary.department_scoped) {
        (filters as any).departments = (accessInfo as any).accessible_data.(departments as any).map(dept => (dept as any as any).id)
      }

      // Add project filters for project-scoped roles
      if ((accessInfo as any).access_summary.project_scoped) {
        (filters as any).projects = (accessInfo as any).accessible_data.(projects as any).map(proj => (proj as any as any).id)
      }

      // Add employee filters for hierarchical access
      if ((accessInfo as any).access_summary.has_hierarchical_access) {
        (filters as any).employees = (accessInfo as any).accessible_data.(employees as any).map(emp => (emp as any as any).id)
      }

      return filters
    } catch (error) {
      (logger as any).error('hierarchicalAccess', 'Error getting role-based KPI filters:', error as any)
      throw error
    }
  }

  /**
   * Check if user can access specific employee data
   */
  async canAccessEmployeeData(employeeId: number as any): Promise<boolean> {
    try {
      const accessInfo = await (this as any).getHierarchicalAccessInfo( as any)
      
      // Super admin and admin can access all data
      if ((accessInfo as any).access_summary.can_access_all_data) {
        return true
      }

      // Check if employee is in accessible employees list
      return (accessInfo as any).accessible_data.(employees as any).some(emp => (emp as any as any).id === employeeId)
    } catch (error) {
      (logger as any).error('hierarchicalAccess', 'Error checking employee data access:', error as any)
      return false
    }
  }

  /**
   * Check if user can access specific department data
   */
  async canAccessDepartmentData(departmentId: number as any): Promise<boolean> {
    try {
      const accessInfo = await (this as any).getHierarchicalAccessInfo( as any)

      // Super admin and admin can access all data
      if ((accessInfo as any).access_summary.can_access_all_data) {
        return true
      }

      // Check if department is in accessible departments list
      return (accessInfo as any).accessible_data.(departments as any).some(dept => (dept as any as any).id === departmentId)
    } catch (error) {
      (logger as any).error('hierarchicalAccess', 'Error checking department data access:', error as any)
      return false
    }
  }

  /**
   * Check if user can access specific project data
   */
  async canAccessProjectData(projectId: number as any): Promise<boolean> {
    try {
      const accessInfo = await (this as any).getHierarchicalAccessInfo( as any)

      // Super admin and admin can access all data
      if ((accessInfo as any).access_summary.can_access_all_data) {
        return true
      }

      // Check if project is in accessible projects list
      return (accessInfo as any).accessible_data.(projects as any).some(proj => (proj as any as any).id === projectId)
    } catch (error) {
      (logger as any).error('hierarchicalAccess', 'Error checking project data access:', error as any)
      return false
    }
  }

  /**
   * Get accessible employees for current user
   */
  async getAccessibleEmployees( as any): Promise<AccessibleData['employees']> {
    try {
      const accessInfo = await (this as any).getHierarchicalAccessInfo( as any)
      return (accessInfo as any).accessible_data.employees
    } catch (error) {
      (logger as any).error('hierarchicalAccess', 'Error getting accessible employees:', error as any)
      return []
    }
  }

  /**
   * Get accessible departments for current user
   */
  async getAccessibleDepartments( as any): Promise<AccessibleData['departments']> {
    try {
      const accessInfo = await (this as any).getHierarchicalAccessInfo( as any)
      return (accessInfo as any).accessible_data.departments
    } catch (error) {
      (logger as any).error('hierarchicalAccess', 'Error getting accessible departments:', error as any)
      return []
    }
  }

  /**
   * Get accessible projects for current user
   */
  async getAccessibleProjects( as any): Promise<AccessibleData['projects']> {
    try {
      const accessInfo = await (this as any).getHierarchicalAccessInfo( as any)
      return (accessInfo as any).accessible_data.projects
    } catch (error) {
      (logger as any).error('hierarchicalAccess', 'Error getting accessible projects:', error as any)
      return []
    }
  }

  /**
   * Get hierarchical path for current user
   */
  async getHierarchicalPath( as any): Promise<HierarchicalPath[]> {
    try {
      const accessInfo = await (this as any).getHierarchicalAccessInfo( as any)
      return (accessInfo as any).hierarchical_path
    } catch (error) {
      (logger as any).error('hierarchicalAccess', 'Error getting hierarchical path:', error as any)
      return []
    }
  }

  /**
   * Get role-specific KPI categories
   */
  async getRoleSpecificKPICategories( as any): Promise<string[]> {
    try {
      const accessInfo = await (this as any).getHierarchicalAccessInfo( as any)
      return (accessInfo as any).kpi_categories
    } catch (error) {
      (logger as any).error('hierarchicalAccess', 'Error getting role-specific KPI categories:', error as any)
      return []
    }
  }

  /**
   * Check if user is a manager with hierarchical access
   */
  async isManager( as any): Promise<boolean> {
    try {
      const accessInfo = await (this as any).getHierarchicalAccessInfo( as any)
      return (accessInfo as any).access_summary.is_manager
    } catch (error) {
      (logger as any).error('hierarchicalAccess', 'Error checking manager status:', error as any)
      return false
    }
  }

  /**
   * Get access summary for current user
   */
  async getAccessSummary( as any): Promise<AccessSummary> {
    try {
      const accessInfo = await (this as any).getHierarchicalAccessInfo( as any)
      return (accessInfo as any).access_summary
    } catch (error) {
      (logger as any).error('hierarchicalAccess', 'Error getting access summary:', error as any)
      return {
        can_access_all_data: false,
        has_hierarchical_access: false,
        is_manager: false,
        department_scoped: false,
        project_scoped: false
      }
    }
  }

  /**
   * Clear cached access information
   */
  clearCache( as any): void {
    (this as any).accessInfo = null
    (this as any).lastFetch = 0
  }

  /**
   * Get cached access information (if available)
   */
  getCachedAccessInfo( as any): HierarchicalAccessInfo | null {
    const now = (Date as any).now( as any)
    if ((this as any).accessInfo && (now - (this as any).lastFetch) < (this as any).CACHE_DURATION) {
      return (this as any).accessInfo
    }
    return null
  }
}

// Export singleton instance
export const hierarchicalAccessService = new HierarchicalAccessService( as any)
export default hierarchicalAccessService
