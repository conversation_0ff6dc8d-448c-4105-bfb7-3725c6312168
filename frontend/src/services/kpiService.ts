import React from 'react';
// KPI Service for managing Key Performance Indicators
import { apiClient } from './api'

export interface KPIMetric {
  id: number
  name: string
  nameAr: string
  description: string
  descriptionAr: string
  target_value: number
  current_value: number
  unit: string
  unitAr: string
  category: string
  categoryAr: string
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly'
  is_active: boolean
  created_at: string
  updated_at: string
}

// Aliases for backward compatibility
export type KPI = KPIMetric
export interface KPIValue {
  date: string
  value: number
  target: number
}

export interface KPICategory {
  id: string
  name: string
  nameAr: string
}

export interface KPIData {
  metric: KPIMetric
  values: Array<{
    date: string
    value: number
    target: number
  }>
}

class KPIService {
  /**
   * Get all KPI metrics
   */
  async getKPIMetrics(): Promise<KPIMetric[]> {
    try {
      const response = await apiClient.get<KPIMetric[]>('/kpis/')
      return response.data
    } catch (error) {
      console.error('Failed to fetch KPI metrics:', error)
      throw error
    }
  }

  /**
   * Get KPI data with historical values
   */
  async getKPIData(metricId: number, startDate?: string, endDate?: string): Promise<KPIData> {
    try {
      const params = new URLSearchParams()
      if (startDate) params.append('start_date', startDate)
      if (endDate) params.append('end_date', endDate)
      
      const response = await apiClient.get<KPIData>(`/kpis/${metricId}/data/?${params}`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch KPI data:', error)
      throw error
    }
  }

  /**
   * Create new KPI metric
   */
  async createKPIMetric(metric: Omit<KPIMetric, 'id' | 'created_at' | 'updated_at'>): Promise<KPIMetric> {
    try {
      const response = await apiClient.post<KPIMetric>('/kpis/', metric)
      return response.data
    } catch (error) {
      console.error('Failed to create KPI metric:', error)
      throw error
    }
  }

  /**
   * Update KPI metric
   */
  async updateKPIMetric(id: number, metric: Partial<KPIMetric>): Promise<KPIMetric> {
    try {
      const response = await apiClient.put<KPIMetric>(`/kpis/${id}/`, metric)
      return response.data
    } catch (error) {
      console.error('Failed to update KPI metric:', error)
      throw error
    }
  }

  /**
   * Delete KPI metric
   */
  async deleteKPIMetric(id: number): Promise<void> {
    try {
      await apiClient.delete(`/kpis/${id}/`)
    } catch (error) {
      console.error('Failed to delete KPI metric:', error)
      throw error
    }
  }

  /**
   * Get KPI values for a metric
   */
  async getKPIValues(metricId: number, startDate?: string, endDate?: string): Promise<KPIValue[]> {
    try {
      const params = new URLSearchParams()
      if (startDate) params.append('start_date', startDate)
      if (endDate) params.append('end_date', endDate)

      const response = await apiClient.get<KPIValue[]>(`/kpis/${metricId}/values/?${params}`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch KPI values:', error)
      throw error
    }
  }

  /**
   * Update KPI value
   */
  async updateKPIValue(metricId: number, value: number, date?: string): Promise<void> {
    try {
      const payload = {
        value,
        date: date || new Date().toISOString().split('T')[0]
      }
      await apiClient.post(`/kpis/${metricId}/values/`, payload)
    } catch (error) {
      console.error('Failed to update KPI value:', error)
      throw error
    }
  }
}

// Export singleton instance
export const kpiService = new KPIService()
export default kpiService

// Hook for KPI modals (placeholder for now)
export const useKPIModals = () => {
  return {
    openCreateModal: () => console.log('Create KPI modal'),
    openEditModal: (id: number) => console.log('Edit KPI modal', id),
    openDeleteModal: (id: number) => console.log('Delete KPI modal', id),
  }
}
