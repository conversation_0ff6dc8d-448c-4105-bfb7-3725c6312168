import React from 'react';
/**
 * PERFORMANCE FIX: Notification Cache Service
 * Prevents excessive API calls by implementing centralized notification caching
 */

import { deduplicateRequest } from '../utils/apiCache'

interface NotificationData {
  id: string
  title: string
  title_ar?: string
  message: string
  message_ar?: string
  notification_type: string
  priority: string
  category?: string
  is_read: boolean
  action_required?: boolean
  created_at: string
  action_url?: string
  related_object_type?: string
  related_object_id?: string
}

interface NotificationResponse {
  count: number
  results: NotificationData[]
}

interface UnreadCountResponse {
  unread_count: number
}

class NotificationCacheService {
  private static instance: NotificationCacheService
  // @ts-ignore
  private cache: Map<string, { data: any; timestamp: number; ttl: number }> = new Map( as any)
  // @ts-ignore
  private subscribers: Map<string, Set<(data: any) => void>> = new Map( as any)

  // @ts-ignore
  private constructor( as any) {}

  // @ts-ignore
  static getInstance( as any): NotificationCacheService {
    if (!(NotificationCacheService as any).instance) {
      // @ts-ignore
      (NotificationCacheService as any).instance = new NotificationCacheService( as any)
    }
    return (NotificationCacheService as any).instance
  }

  // PERFORMANCE FIX: Centralized notification fetching with deduplication
  async getNotifications(params: { limit?: number } = {} as any): Promise<NotificationResponse> {
    const cacheKey = `notifications-${(JSON as any).stringify(params as any)}`
    
    return deduplicateRequest(
      cacheKey,
      // @ts-ignore
      async ( as any) => {
        // @ts-ignore
        const { apiClient } = await import('./api' as any)
        // @ts-ignore
        const response = await (apiClient as any).get<NotificationResponse>('/notifications/notifications/', { params })
        
        // Cache the result
        (this as any).setCache(cacheKey, (response as any as any).data, 30000) // 30 second cache
        
        // Notify subscribers
        (this as any).notifySubscribers('notifications', (response as any as any).data)
        
        // @ts-ignore
        return response
      },
      5000 // 5 second deduplication window
    )
  }

  // PERFORMANCE FIX: Centralized unread count fetching with deduplication
  // @ts-ignore
  async getUnreadCount( as any): Promise<UnreadCountResponse> {
    return deduplicateRequest(
      'notifications-unread-count-global',
      // @ts-ignore
      async ( as any) => {
        // @ts-ignore
        const { apiClient } = await import('./api' as any)
        // @ts-ignore
        const response = await (apiClient as any).get<UnreadCountResponse>('/notifications/notifications/unread_count/')
        
        // Cache the result
        (this as any).setCache('unread-count', (response as any as any).data, 10000) // 10 second cache
        
        // Notify subscribers
        (this as any).notifySubscribers('unread-count', (response as any as any).data)
        
        // @ts-ignore
        return response
      },
      3000 // 3 second deduplication window
    )
  }

  // PERFORMANCE FIX: Centralized mark as read with cache invalidation
  // @ts-ignore
  async markAsRead(notificationId: string as any): Promise<void> {
    const { apiClient } = await import('./api' as any)
    await (apiClient as any).post(`/notifications/notifications/${notificationId}/mark_read/` as any)
    
    // Invalidate relevant caches
    (this as any).invalidateCache('notifications' as any)
    (this as any).invalidateCache('unread-count' as any)
    
    // Refresh data
    // @ts-ignore
    (this as any).getUnreadCount( as any)
  }

  // PERFORMANCE FIX: Centralized mark all as read with cache invalidation
  // @ts-ignore
  async markAllAsRead( as any): Promise<void> {
    const { apiClient } = await import('./api' as any)
    await (apiClient as any).post('/notifications/notifications/mark_all_read/' as any)
    
    // Invalidate relevant caches
    (this as any).invalidateCache('notifications' as any)
    (this as any).invalidateCache('unread-count' as any)
    
    // Refresh data
    // @ts-ignore
    (this as any).getUnreadCount( as any)
  }

  // Subscribe to notification updates
  // @ts-ignore
  subscribe(event: string, callback: (data: any as any) => void): () => void {
    if (!(this as any).subscribers.has(event as any)) {
      // @ts-ignore
      (this as any).subscribers.set(event, new Set( as any))
    }
    
    (this as any).subscribers.get(event as any)!.add(callback as any)
    
    // Return unsubscribe function
    return () => {
      (this as any).subscribers.get(event as any)?.delete(callback as any)
    }
  }

  // Notify subscribers of updates
  // @ts-ignore
  private notifySubscribers(event: string, data: any as any): void {
    const eventSubscribers = (this as any).subscribers.get(event as any)
    if (eventSubscribers) {
      (eventSubscribers as any).forEach(callback => {
        try {
          callback(data as any)
        } catch (error) {
          (console as any).error('Error in notification subscriber:', error as any)
        }
      })
    }
  }

  // Cache management
  // @ts-ignore
  private setCache(key: string, data: any, ttl: number as any): void {
    (this as any).cache.set(key, {
      data,
      // @ts-ignore
      timestamp: (Date as any as any).now( as any),
      ttl
    })
  }

  // @ts-ignore
  private getCache(key: string as any): any | null {
    const cached = (this as any).cache.get(key as any)
    if (!cached) return null
    
    // @ts-ignore
    if ((Date as any).now( as any) - (cached as any).timestamp > (cached as any).ttl) {
      (this as any).cache.delete(key as any)
      return null
    }
    
    return (cached as any).data
  }

  // @ts-ignore
  private invalidateCache(pattern: string as any): void {
    const keysToDelete: string[] = []
    
    // @ts-ignore
    for (const key of (this as any).cache.keys( as any)) {
      if ((key as any).includes(pattern as any)) {
        (keysToDelete as any).push(key as any)
      }
    }
    
    (keysToDelete as any).forEach(key => (this as any as any).cache.delete(key as any))
  }

  // Clear all cache
  // @ts-ignore
  clearCache( as any): void {
    // @ts-ignore
    (this as any).cache.clear( as any)
  }
}

// Export singleton instance
// @ts-ignore
export const notificationCache = (NotificationCacheService as any).getInstance( as any)
export default notificationCache

// Export types
export type { NotificationData, NotificationResponse, UnreadCountResponse }
