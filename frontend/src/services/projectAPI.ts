import React from 'react';
/**
 * Project Management API Services
 */

import { apiClient } from './api'

// Project Types
export interface Project {
  id: number
  name: string
  name_ar: string
  description: string
  description_ar?: string
  project_manager: number
  project_manager_name: string
  department: number
  department_name: string
  team_members: number[]
  team_members_count: number
  client?: string
  budget_amount?: number
  start_date: string
  end_date: string
  actual_start_date?: string
  actual_end_date?: string
  status: 'PLANNING' | 'IN_PROGRESS' | 'ON_HOLD' | 'COMPLETED' | 'CANCELLED'
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  progress_percentage: number
  tasks_count: number
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface CreateProjectData {
  name: string
  name_ar: string
  description: string
  description_ar?: string
  project_manager: number
  department?: number
  team_members?: number[]
  client?: string
  budget_amount?: number
  start_date: string
  end_date: string
  status?: 'PLANNING' | 'IN_PROGRESS' | 'ON_HOLD' | 'COMPLETED' | 'CANCELLED'
  priority?: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  progress_percentage?: number
}

export interface UpdateProjectData {
  name?: string
  name_ar?: string
  description?: string
  description_ar?: string
  project_manager?: number
  department?: number
  team_members?: number[]
  client?: string
  budget_amount?: number
  start_date?: string
  end_date?: string
  actual_start_date?: string
  actual_end_date?: string
  status?: 'PLANNING' | 'IN_PROGRESS' | 'ON_HOLD' | 'COMPLETED' | 'CANCELLED'
  priority?: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  progress_percentage?: number
  is_active?: boolean
}

// Task Types
export interface Task {
  id: number
  project: number
  project_name: string
  title: string
  title_ar?: string
  description: string
  description_ar?: string
  assigned_to?: number
  assigned_to_name?: string
  created_by: number
  created_by_name: string
  due_date: string
  start_date?: string
  completion_date?: string
  estimated_hours?: number
  actual_hours?: number
  status: 'TODO' | 'IN_PROGRESS' | 'REVIEW' | 'COMPLETED' | 'CANCELLED'
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'
  progress_percentage: number
  dependencies: number[]
  tags?: string
  created_at: string
  updated_at: string
}

export interface CreateTaskData {
  project: number
  title: string
  title_ar?: string
  description: string
  description_ar?: string
  assigned_to?: number
  due_date: string
  start_date?: string
  estimated_hours?: number
  status?: 'TODO' | 'IN_PROGRESS' | 'REVIEW' | 'COMPLETED' | 'CANCELLED'
  priority?: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'
  progress_percentage?: number
  dependencies?: number[]
  tags?: string
}

export interface UpdateTaskData {
  title?: string
  title_ar?: string
  description?: string
  description_ar?: string
  assigned_to?: number
  due_date?: string
  start_date?: string
  completion_date?: string
  estimated_hours?: number
  actual_hours?: number
  status?: 'TODO' | 'IN_PROGRESS' | 'REVIEW' | 'COMPLETED' | 'CANCELLED'
  priority?: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'
  progress_percentage?: number
  dependencies?: number[]
  tags?: string
}

export interface ProjectFilters {
  status?: string
  priority?: string
  project_manager?: number
  department?: number
  search?: string
}

export interface TaskFilters {
  project?: number
  assigned_to?: number
  status?: string
  priority?: string
  due_date_from?: string
  due_date_to?: string
  search?: string
}

// Project API
export const projectAPI = {
  // Get all projects
  getAll: async (filters?: ProjectFilters): Promise<Project[]> => {
    const response = await apiClient.get<Project[]>('/projects/', { params: filters as any })
    return response.data
  },

  // Get project by ID
  getById: async (id: number): Promise<Project> => {
    const response = await apiClient.get<Project>(`/projects/${id}/`)
    return response.data
  },

  // Create new project
  create: async (projectData: CreateProjectData): Promise<Project> => {
    const response = await apiClient.post<Project>('/projects/', projectData)
    return response.data
  },

  // Update project
  update: async (id: number, projectData: UpdateProjectData): Promise<Project> => {
    const response = await apiClient.patch<Project>(`/projects/${id}/`, projectData)
    return response.data
  },

  // Delete project
  delete: async (id: number): Promise<void> => {
    await apiClient.delete(`/projects/${id}/`)
  },

  // Get project tasks
  getTasks: async (id: number): Promise<Task[]> => {
    const response = await apiClient.get<Task[]>(`/projects/${id}/tasks/`)
    return response.data
  },

  // Get project statistics
  getStats: async (): Promise<{
    total: number
    by_status: Record<string, number>
    by_priority: Record<string, number>
    overdue: number
    completed_this_month: number
  }> => {
    const response = await apiClient.get('/projects/stats/')
    return response.data as {
      total: number
      by_status: Record<string, number>
      by_priority: Record<string, number>
      overdue: number
      completed_this_month: number
    }
  },

  // Get my projects (as manager or team member)
  getMyProjects: async (): Promise<Project[]> => {
    const response = await apiClient.get<Project[]>('/projects/my-projects/')
    return response.data
  },
}

// Task API
export const taskAPI = {
  // Get all tasks
  getAll: async (filters?: TaskFilters): Promise<Task[]> => {
    const response = await apiClient.get<Task[]>('/tasks/', { params: filters as any })
    return response.data
  },

  // Get task by ID
  getById: async (id: number): Promise<Task> => {
    const response = await apiClient.get<Task>(`/tasks/${id}/`)
    return response.data
  },

  // Create new task
  create: async (taskData: CreateTaskData): Promise<Task> => {
    const response = await apiClient.post<Task>('/tasks/', taskData)
    return response.data
  },

  // Update task
  update: async (id: number, taskData: UpdateTaskData): Promise<Task> => {
    const response = await apiClient.patch<Task>(`/tasks/${id}/`, taskData)
    return response.data
  },

  // Delete task
  delete: async (id: number): Promise<void> => {
    await apiClient.delete(`/tasks/${id}/`)
  },

  // Get my tasks (assigned to me)
  getMyTasks: async (filters?: Omit<TaskFilters, 'assigned_to'>): Promise<Task[]> => {
    const response = await apiClient.get<Task[]>('/tasks/my-tasks/', { params: filters })
    return response.data
  },

  // Update task status
  updateStatus: async (id: number, status: Task['status']): Promise<Task> => {
    const response = await apiClient.patch<Task>(`/tasks/${id}/`, { status })
    return response.data
  },

  // Update task progress
  updateProgress: async (id: number, progress_percentage: number): Promise<Task> => {
    const response = await apiClient.patch<Task>(`/tasks/${id}/`, { progress_percentage })
    return response.data
  },

  // Get task statistics
  getStats: async (): Promise<{
    total: number
    by_status: Record<string, number>
    by_priority: Record<string, number>
    overdue: number
    completed_today: number
    my_tasks: number
  }> => {
    const response = await apiClient.get('/tasks/stats/')
    return response.data as {
      total: number
      by_status: Record<string, number>
      by_priority: Record<string, number>
      overdue: number
      completed_today: number
      my_tasks: number
    }
  },
}

export default {
  projectAPI,
  taskAPI,
}
