import React from 'react';
/**
 * Sales & CRM API Services
 */

import { apiClient } from './api'

// Customer Types
export interface Customer {
  id: number
  name: string
  name_ar?: string
  company: string
  company_ar?: string
  email: string
  phone?: string
  address?: string
  address_ar?: string
  city?: string
  city_ar?: string
  country?: string
  country_ar?: string
  industry?: string
  industry_ar?: string
  customer_type: 'INDIVIDUAL' | 'CORPORATE' | 'VIP'
  status: 'ACTIVE' | 'INACTIVE' | 'PROSPECT'
  total_orders: number
  total_value: number
  last_order_date?: string
  assigned_to?: number
  assigned_to_name?: string
  notes?: string
  notes_ar?: string
  created_at: string
  updated_at: string
}

export interface CreateCustomerData {
  name: string
  name_ar?: string
  company?: string
  company_ar?: string
  email: string
  phone?: string
  address?: string
  address_ar?: string
  city?: string
  city_ar?: string
  country?: string
  country_ar?: string
  industry?: string
  industry_ar?: string
  customer_type?: 'INDIVIDUAL' | 'CORPORATE' | 'VIP'
  status?: 'ACTIVE' | 'INACTIVE' | 'PROSPECT'
  assigned_to?: number
  notes?: string
  notes_ar?: string
}

// Lead Types
export interface Lead {
  id: number
  name: string
  name_ar?: string
  company?: string
  company_ar?: string
  email: string
  phone?: string
  source: 'WEBSITE' | 'REFERRAL' | 'COLD_CALL' | 'SOCIAL_MEDIA' | 'EVENT' | 'OTHER'
  status: 'NEW' | 'CONTACTED' | 'QUALIFIED' | 'PROPOSAL' | 'NEGOTIATION' | 'CLOSED_WON' | 'CLOSED_LOST'
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'
  estimated_value?: number
  probability: number
  expected_close_date?: string
  assigned_to?: number
  assigned_to_name?: string
  notes?: string
  notes_ar?: string
  last_activity_date?: string
  created_at: string
  updated_at: string
}

export interface CreateLeadData {
  name: string
  name_ar?: string
  company?: string
  company_ar?: string
  email: string
  phone?: string
  source?: 'WEBSITE' | 'REFERRAL' | 'COLD_CALL' | 'SOCIAL_MEDIA' | 'EVENT' | 'OTHER'
  status?: 'NEW' | 'CONTACTED' | 'QUALIFIED' | 'PROPOSAL' | 'NEGOTIATION' | 'CLOSED_WON' | 'CLOSED_LOST'
  priority?: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'
  estimated_value?: number
  probability?: number
  expected_close_date?: string
  assigned_to?: number
  notes?: string
  notes_ar?: string
}

// Quotation Types
export interface QuotationItem {
  id?: number
  name: string
  name_ar?: string
  description?: string
  description_ar?: string
  quantity: number
  unit_price: number
  discount_percentage?: number
  total: number
}

export interface Quotation {
  id: number
  quotation_number: string
  customer: number
  customer_name: string
  customer_email: string
  items: QuotationItem[]
  subtotal: number
  discount_amount: number
  tax_rate: number
  tax_amount: number
  total_amount: number
  status: 'DRAFT' | 'SENT' | 'ACCEPTED' | 'REJECTED' | 'EXPIRED'
  valid_until: string
  notes?: string
  notes_ar?: string
  created_by: number
  created_by_name: string
  created_at: string
  updated_at: string
}

export interface CreateQuotationData {
  customer: number
  items: Omit<QuotationItem, 'id'>[]
  discount_amount?: number
  tax_rate?: number
  valid_until: string
  notes?: string
  notes_ar?: string
}

// Sales Order Types
export interface SalesOrder {
  id: number
  order_number: string
  customer: number
  customer_name: string
  quotation?: number
  items: QuotationItem[]
  subtotal: number
  discount_amount: number
  tax_amount: number
  total_amount: number
  status: 'PENDING' | 'CONFIRMED' | 'PROCESSING' | 'SHIPPED' | 'DELIVERED' | 'CANCELLED'
  order_date: string
  expected_delivery_date?: string
  actual_delivery_date?: string
  payment_status: 'PENDING' | 'PARTIAL' | 'PAID' | 'OVERDUE'
  payment_terms?: string
  notes?: string
  notes_ar?: string
  created_by: number
  created_by_name: string
  created_at: string
  updated_at: string
}

export interface CustomerFilters {
  customer_type?: string
  status?: string
  assigned_to?: number
  industry?: string
  search?: string
}

export interface LeadFilters {
  status?: string
  priority?: string
  source?: string
  assigned_to?: number
  search?: string
}

// Customer API
export const customerAPI = {
  getAll: async (filters?: CustomerFilters): Promise<Customer[]> => {
    const response = await apiClient.get<Customer[]>('/customers/', { params: filters as any })
    return response.data
  },

  getById: async (id: number): Promise<Customer> => {
    const response = await apiClient.get<Customer>(`/customers/${id}/`)
    return response.data
  },

  create: async (data: CreateCustomerData): Promise<Customer> => {
    const response = await apiClient.post<Customer>('/customers/', data)
    return response.data
  },

  update: async (id: number, data: Partial<CreateCustomerData>): Promise<Customer> => {
    const response = await apiClient.patch<Customer>(`/customers/${id}/`, data)
    return response.data
  },

  delete: async (id: number): Promise<void> => {
    await apiClient.delete(`/customers/${id}/`)
  },

  getStats: async (): Promise<{
    total: number
    by_type: Record<string, number>
    by_status: Record<string, number>
    total_value: number
    new_this_month: number
  }> => {
    const response = await apiClient.get('/customers/stats/')
    return response.data as {
      total: number
      by_type: Record<string, number>
      by_status: Record<string, number>
      total_value: number
      new_this_month: number
    }
  },
}

// Lead API
export const leadAPI = {
  getAll: async (filters?: LeadFilters): Promise<Lead[]> => {
    const response = await apiClient.get<Lead[]>('/leads/', { params: filters as any })
    return response.data
  },

  getById: async (id: number): Promise<Lead> => {
    const response = await apiClient.get<Lead>(`/leads/${id}/`)
    return response.data
  },

  create: async (data: CreateLeadData): Promise<Lead> => {
    const response = await apiClient.post<Lead>('/leads/', data)
    return response.data
  },

  update: async (id: number, data: Partial<CreateLeadData>): Promise<Lead> => {
    const response = await apiClient.patch<Lead>(`/leads/${id}/`, data)
    return response.data
  },

  delete: async (id: number): Promise<void> => {
    await apiClient.delete(`/leads/${id}/`)
  },

  convertToCustomer: async (id: number): Promise<Customer> => {
    const response = await apiClient.post<Customer>(`/leads/${id}/convert/`)
    return response.data
  },

  getMyLeads: async (): Promise<Lead[]> => {
    const response = await apiClient.get<Lead[]>('/leads/my-leads/')
    return response.data
  },
}

// Quotation API
export const quotationAPI = {
  getAll: async (): Promise<Quotation[]> => {
    const response = await apiClient.get<Quotation[]>('/quotations/')
    return response.data
  },

  getById: async (id: number): Promise<Quotation> => {
    const response = await apiClient.get<Quotation>(`/quotations/${id}/`)
    return response.data
  },

  create: async (data: CreateQuotationData): Promise<Quotation> => {
    const response = await apiClient.post<Quotation>('/quotations/', data)
    return response.data
  },

  update: async (id: number, data: Partial<CreateQuotationData>): Promise<Quotation> => {
    const response = await apiClient.patch<Quotation>(`/quotations/${id}/`, data)
    return response.data
  },

  delete: async (id: number): Promise<void> => {
    await apiClient.delete(`/quotations/${id}/`)
  },

  sendToCustomer: async (id: number): Promise<void> => {
    await apiClient.post(`/quotations/${id}/send/`)
  },

  convertToOrder: async (id: number): Promise<SalesOrder> => {
    const response = await apiClient.post<SalesOrder>(`/quotations/${id}/convert/`)
    return response.data
  },
}

// Sales Order API
export const salesOrderAPI = {
  getAll: async (): Promise<SalesOrder[]> => {
    const response = await apiClient.get<SalesOrder[]>('/sales-orders/')
    return response.data
  },

  getById: async (id: number): Promise<SalesOrder> => {
    const response = await apiClient.get<SalesOrder>(`/sales-orders/${id}/`)
    return response.data
  },

  updateStatus: async (id: number, status: SalesOrder['status']): Promise<SalesOrder> => {
    const response = await apiClient.patch<SalesOrder>(`/sales-orders/${id}/`, { status })
    return response.data
  },

  getStats: async (): Promise<{
    total_orders: number
    total_revenue: number
    pending_orders: number
    completed_orders: number
    monthly_revenue: number
  }> => {
    const response = await apiClient.get('/sales-orders/stats/')
    return response.data as {
      total_orders: number
      total_revenue: number
      pending_orders: number
      completed_orders: number
      monthly_revenue: number
    }
  },
}

export default {
  customerAPI,
  leadAPI,
  quotationAPI,
  salesOrderAPI,
}
