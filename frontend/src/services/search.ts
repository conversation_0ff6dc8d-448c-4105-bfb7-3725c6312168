import React from 'react';
/**
 * Advanced Search Service
 * Provides comprehensive search functionality across the application
 */

import { apiClient } from './api'

export interface SearchQuery {
  query: string
  filters?: SearchFilters
  sort?: SearchSort
  pagination?: SearchPagination
  facets?: string[]
}

export interface SearchFilters {
  [key: string]: any
  dateRange?: {
    start: string
    end: string
  }
  categories?: string[]
  status?: string[]
  departments?: string[]
  roles?: string[]
  tags?: string[]
}

export interface SearchSort {
  field: string
  direction: 'asc' | 'desc'
}

export interface SearchPagination {
  page: number
  limit: number
}

export interface SearchResult {
  id: string
  type: string
  title: string
  description: string
  url: string
  score: number
  highlights?: string[]
  metadata?: Record<string, any>
  thumbnail?: string
  createdAt: string
  updatedAt: string
}

export interface SearchResponse {
  results: SearchResult[]
  total: number
  page: number
  limit: number
  facets?: SearchFacets
  suggestions?: string[]
  executionTime: number
}

export interface SearchFacets {
  [key: string]: SearchFacetValue[]
}

export interface SearchFacetValue {
  value: string
  count: number
  selected?: boolean
}

export interface SearchHistory {
  id: string
  query: string
  timestamp: number
  resultCount: number
}

class SearchService {
  private searchHistory: SearchHistory[] = []
  private recentSearches: string[] = []
  private maxHistoryItems = 50
  private maxRecentSearches = 10

  /**
   * Perform global search across all modules
   */
  async globalSearch(query: SearchQuery): Promise<SearchResponse> {
    try {
      const response = await apiClient.post('/search/global', query)
      const data = response.data as SearchResponse

      // Save to search history
      this.addToHistory(query.query, data.total)

      return data
    } catch (error) {
      console.error('Global search error:', error)
      throw error
    }
  }

  /**
   * Search within a specific module
   */
  async moduleSearch(module: string, query: SearchQuery): Promise<SearchResponse> {
    try {
      const response = await apiClient.post(`/search/${module}`, query)
      const data = response.data as SearchResponse

      // Save to search history
      this.addToHistory(query.query, data.total)

      return data
    } catch (error) {
      console.error(`Module search error (${module}):`, error)
      throw error
    }
  }

  /**
   * Get search suggestions based on partial query
   */
  async getSuggestions(partialQuery: string, module?: string): Promise<string[]> {
    try {
      const endpoint = module ? `/search/${module}/suggestions` : '/search/suggestions'
      const response = await apiClient.get(endpoint, {
        params: { q: partialQuery }
      })
      return (response.data as { suggestions: string[] }).suggestions
    } catch (error) {
      console.error('Search suggestions error:', error)
      return []
    }
  }

  /**
   * Get search facets for filtering
   */
  async getFacets(module?: string): Promise<SearchFacets> {
    try {
      const endpoint = module ? `/search/${module}/facets` : '/search/facets'
      const response = await apiClient.get(endpoint)
      return (response.data as { facets: Record<string, any> }).facets
    } catch (error) {
      console.error('Search facets error:', error)
      return {}
    }
  }

  /**
   * Perform fuzzy search for typo tolerance
   */
  async fuzzySearch(query: string, threshold: number = 0.8): Promise<SearchResponse> {
    try {
      const response = await apiClient.post('/search/fuzzy', {
        query,
        threshold
      })
      return response.data as SearchResponse
    } catch (error) {
      console.error('Fuzzy search error:', error)
      throw error
    }
  }

  /**
   * Search with advanced filters and operators
   */
  async advancedSearch(searchParams: {
    must?: string[]
    should?: string[]
    mustNot?: string[]
    filters?: SearchFilters
    boost?: Record<string, number>
  }): Promise<SearchResponse> {
    try {
      const response = await apiClient.post('/search/advanced', searchParams)
      return response.data as SearchResponse
    } catch (error) {
      console.error('Advanced search error:', error)
      throw error
    }
  }

  /**
   * Search similar items based on content
   */
  async findSimilar(itemId: string, itemType: string, limit: number = 10): Promise<SearchResult[]> {
    try {
      const response = await apiClient.get(`/search/similar/${itemType}/${itemId}`, {
        params: { limit }
      })
      return (response.data as { results: SearchResult[] }).results
    } catch (error) {
      console.error('Similar search error:', error)
      return []
    }
  }

  /**
   * Get trending searches
   */
  async getTrendingSearches(period: 'day' | 'week' | 'month' = 'week'): Promise<string[]> {
    try {
      const response = await apiClient.get('/search/trending', {
        params: { period }
      })
      return (response.data as { trending: string[] }).trending
    } catch (error) {
      console.error('Trending searches error:', error)
      return []
    }
  }

  /**
   * Save search query for analytics
   */
  async saveSearchAnalytics(query: string, resultCount: number, clickedResults: string[]): Promise<void> {
    try {
      await apiClient.post('/search/analytics', {
        query,
        resultCount,
        clickedResults,
        timestamp: Date.now()
      })
    } catch (error) {
      console.error('Search analytics error:', error)
    }
  }

  /**
   * Add query to search history
   */
  private addToHistory(query: string, resultCount: number): void {
    const historyItem: SearchHistory = {
      id: Date.now().toString(),
      query,
      timestamp: Date.now(),
      resultCount
    }

    // Add to history
    this.searchHistory.unshift(historyItem)

    // Limit history size
    if (this.searchHistory.length > this.maxHistoryItems) {
      this.searchHistory = this.searchHistory.slice(0, this.maxHistoryItems)
    }

    // Add to recent searches if not already present
    if (!this.recentSearches.includes(query)) {
      this.recentSearches.unshift(query)

      // Limit recent searches
      if (this.recentSearches.length > this.maxRecentSearches) {
        this.recentSearches = this.recentSearches.slice(0, this.maxRecentSearches)
      }
    }

    // Save to localStorage
    this.saveToLocalStorage()
  }

  /**
   * Get search history
   */
  getSearchHistory(): SearchHistory[] {
    return this.searchHistory
  }

  /**
   * Get recent searches
   */
  getRecentSearches(): string[] {
    return this.recentSearches
  }

  /**
   * Clear search history
   */
  clearSearchHistory(): void {
    this.searchHistory = []
    this.recentSearches = []
    this.saveToLocalStorage()
  }

  /**
   * Remove item from search history
   */
  removeFromHistory(id: string): void {
    this.searchHistory = this.searchHistory.filter(item => item.id !== id)
    this.saveToLocalStorage()
  }

  /**
   * Save search data to localStorage
   */
  private saveToLocalStorage(): void {
    try {
      localStorage.setItem('searchHistory', JSON.stringify(this.searchHistory))
      localStorage.setItem('recentSearches', JSON.stringify(this.recentSearches))
    } catch (error) {
      console.error('Error saving search data to localStorage:', error)
    }
  }

  /**
   * Load search data from localStorage
   */
  private loadFromLocalStorage(): void {
    try {
      const history = localStorage.getItem('searchHistory')
      const recent = localStorage.getItem('recentSearches')

      if (history) {
        this.searchHistory = JSON.parse(history)
      }

      if (recent) {
        this.recentSearches = JSON.parse(recent)
      }
    } catch (error) {
      console.error('Error loading search data from localStorage:', error)
    }
  }

  /**
   * Build search query with operators
   */
  buildQuery(terms: {
    required?: string[]
    optional?: string[]
    excluded?: string[]
    exact?: string[]
  }): string {
    const queryParts: string[] = []

    // Required terms (AND)
    if (terms.required?.length) {
      queryParts.push(terms.required.map(term => `+${term}`).join(' '))
    }

    // Optional terms (OR)
    if (terms.optional?.length) {
      queryParts.push(terms.optional.join(' OR '))
    }

    // Excluded terms (NOT)
    if (terms.excluded?.length) {
      queryParts.push(terms.excluded.map(term => `-${term}`).join(' '))
    }

    // Exact phrases
    if (terms.exact?.length) {
      queryParts.push(terms.exact.map(phrase => `"${phrase}"`).join(' '))
    }

    return queryParts.join(' ')
  }

  /**
   * Initialize search service
   */
  init(): void {
    this.loadFromLocalStorage()
  }
}

// Create singleton instance
export const searchService = new SearchService()

// Initialize on import
searchService.init()

export default searchService
