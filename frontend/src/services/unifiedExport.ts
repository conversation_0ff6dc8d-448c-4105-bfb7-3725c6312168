import React from 'react';
/**
 * Unified Export Service
 * Consolidates all export functionality into a single, streamlined service
 * Eliminates duplication and provides consistent export experience
 */

export interface ExportOptions {
  format: 'csv' | 'excel' | 'pdf'
  data?: any[]
  template?: string
  language?: 'en' | 'ar'
  filename?: string
  emailOptions?: EmailOptions
  signatureOptions?: SignatureOptions
}

export interface EmailOptions {
  enabled: boolean
  recipients?: string[]
  subject?: string
  message?: string
  priority?: 'low' | 'normal' | 'high' | 'urgent'
  template?: 'default' | 'formal' | 'invoice' | 'report'
  cc?: string[]
  bcc?: string[]
}

export interface SignatureOptions {
  enabled: boolean
  certificateId?: string
  reason?: string
  location?: string
}

export interface ExportResult {
  success: boolean
  message: string
  downloadUrl?: string
  emailSent?: boolean
  signatureApplied?: boolean
  error?: string
}

class UnifiedExportService {
  private baseUrl = 'http://localhost:8000/api'
  private authToken = localStorage.getItem('auth_token') || ''

  /**
   * Universal export method - handles all export types
   */
  async export(
    dataType: string,
    options: ExportOptions
  ): Promise<ExportResult> {
    try {
      // Step 1: Generate the export based on format
      let result: ExportResult

      switch (options.format) {
        case 'pdf':
          result = await this.generatePDF(dataType, options)
          break
        case 'csv':
          result = await this.generateCSV(dataType, options)
          break
        case 'excel':
          result = await this.generateExcel(dataType, options)
          break
        default:
          throw new Error(`Unsupported format: ${options.format}`)
      }

      if (!result.success) {
        return result
      }

      // Step 2: Apply digital signature if requested
      if (options.signatureOptions?.enabled && options.format === 'pdf') {
        const signResult: any = await this.applySignature(result, options.signatureOptions)
        result.signatureApplied = signResult.success
        if (!signResult.success) {
          result.message += ` (Signature failed: ${signResult.error})`
        }
      }

      // Step 3: Send via email if requested
      if (options.emailOptions?.enabled) {
        const emailResult = await this.sendEmail(result, options.emailOptions)
        result.emailSent = emailResult.success
        if (!emailResult.success) {
          result.message += ` (Email failed: ${emailResult.error})`
        }
      }

      return result

    } catch (error) {
      return {
        success: false,
        message: `Export failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Generate PDF using unified template system
   */
  private async generatePDF(
    dataType: string,
    options: ExportOptions
  ): Promise<ExportResult> {
    try {
      const templateMap: Record<string, string> = {
        'employees': 'employee-report',
        'hr': 'hr-report',
        'attendance': 'attendance-report',
        'projects': 'project-report',
        'expenses': 'expense-report',
        'sales': 'sales-report',
        'tickets': 'support-ticket-report',
        'assets': 'asset-report',
        'customers': 'customer-report',
        'sales-customers': 'sales-customer-report',
        'finance-customers': 'finance-customer-report',
        'hr-customers': 'hr-customer-report',
        'kpis': 'kpi-report',
        'financial': 'financial-report',
        'inventory': 'asset-report',
        'leave': 'hr-report'
      }

      const template = options.template || templateMap[dataType] || 'hr-report'
      const language = options.language || 'en'

      // First try the PDF service
      try {
        const response = await fetch(`${this.baseUrl}/pdf/generate/${template}/?language=${language}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.authToken}`,
          },
          signal: AbortSignal.timeout(5000) // 5 second timeout
        })

        if (!response.ok) {
          if (response.status === 404) {
            throw new Error('PDF endpoint not found - falling back to HTML report')
          }
          throw new Error(`PDF generation failed: ${response.status}`)
        }

        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const filename = options.filename || `${dataType}-report-${language}-${new Date().toISOString().split('T')[0]}.pdf`

        // Auto-download if not sending via email
        if (!options.emailOptions?.enabled) {
          const link = document.createElement('a')
          link.href = url
          link.download = filename
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
          window.URL.revokeObjectURL(url)
        }

        return {
          success: true,
          message: 'PDF generated successfully',
          downloadUrl: url
        }
      } catch (pdfError) {
        console.warn('PDF service failed, falling back to simple HTML export:', pdfError)

        // Fallback: Generate a simple HTML report
        return this.generateFallbackPDF(dataType, options)
      }
    } catch (error) {
      return {
        success: false,
        message: `PDF generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Fallback PDF generation using HTML
   */
  private generateFallbackPDF(dataType: string, options: ExportOptions): ExportResult {
    try {
      const data = options.data || []
      const language = options.language || 'en'

      // Create a simple HTML report
      const htmlContent = this.generateHTMLReport(dataType, data, language)

      // Create blob and download
      const blob = new Blob([htmlContent], { type: 'text/html' })
      const url = window.URL.createObjectURL(blob)
      const filename = options.filename || `${dataType}-report-${language}-${new Date().toISOString().split('T')[0]}.html`

      const link = document.createElement('a')
      link.href = url
      link.download = filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      return {
        success: true,
        message: 'HTML report generated successfully (PDF service unavailable)',
        downloadUrl: url
      }
    } catch (error) {
      return {
        success: false,
        message: `Fallback report generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Generate HTML report with appropriate styling for each data type
   */
  private generateHTMLReport(dataType: string, data: any[], language: 'ar' | 'en'): string {
    const isRTL = language === 'ar'

    // Special handling for different report types
    if (dataType.includes('customer')) {
      return this.generateCustomerHTMLReport(data, language, dataType)
    }

    if (dataType.includes('employee') || dataType.includes('test-employee')) {
      return this.generateEmployeeHTMLReport(data, language, dataType)
    }

    if (dataType.includes('hr') || dataType.includes('attendance') || dataType.includes('leave')) {
      return this.generateHRHTMLReport(data, language, dataType)
    }

    if (dataType.includes('sales') || dataType.includes('order') || dataType.includes('invoice')) {
      return this.generateSalesHTMLReport(data, language, dataType)
    }

    if (dataType.includes('finance') || dataType.includes('budget') || dataType.includes('expense')) {
      return this.generateFinanceHTMLReport(data, language, dataType)
    }

    // Default generic report for other types
    return this.generateGenericHTMLReport(dataType, data, language)
  }

  /**
   * Generate generic HTML report for unspecified types
   */
  private generateGenericHTMLReport(dataType: string, data: any[], language: 'ar' | 'en'): string {
    const isRTL = language === 'ar'
    const title = language === 'ar' ? `تقرير ${dataType}` : `${dataType} Report`
    const headers = data.length > 0 ? Object.keys(data[0]) : []

    return `
<!DOCTYPE html>
<html dir="${isRTL ? 'rtl' : 'ltr'}" lang="${language}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            direction: ${isRTL ? 'rtl' : 'ltr'};
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: ${isRTL ? 'right' : 'left'};
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .date {
            text-align: ${isRTL ? 'left' : 'right'};
            color: #666;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>${title}</h1>
    </div>
    <div class="date">
        ${language === 'ar' ? 'تاريخ التقرير:' : 'Report Date:'} ${new Date().toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}
    </div>

    ${data.length > 0 ? `
    <table>
        <thead>
            <tr>
                ${headers.map(header => `<th>${header}</th>`).join('')}
            </tr>
        </thead>
        <tbody>
            ${data.map(row => `
                <tr>
                    ${headers.map(header => `<td>${row[header] || ''}</td>`).join('')}
                </tr>
            `).join('')}
        </tbody>
    </table>
    ` : `
    <p>${language === 'ar' ? 'لا توجد بيانات متاحة' : 'No data available'}</p>
    `}

    <div style="margin-top: 30px; text-align: center; color: #666; font-size: 12px;">
        ${language === 'ar' ? 'تم إنشاء هذا التقرير بواسطة نظام إدارة المؤسسات' : 'Generated by Enterprise Management System'}
    </div>
</body>
</html>`
  }

  /**
   * Generate CSV export
   */
  private async generateCSV(
    dataType: string,
    options: ExportOptions
  ): Promise<ExportResult> {
    try {
      // For now, use a simple CSV export - you can integrate with your existing export API
      const csvData: any = this.generateSimpleCSV(options.data || [])
      const blob = new Blob([csvData], { type: 'text/csv' })
      const url = window.URL.createObjectURL(blob)
      const filename = options.filename || `${dataType}-export-${new Date().toISOString().split('T')[0]}.csv`

      const link = document.createElement('a')
      link.href = url
      link.download = filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      return {
        success: true,
        message: 'CSV exported successfully',
        downloadUrl: url
      }
    } catch (error) {
      return {
        success: false,
        message: `CSV export failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  private generateSimpleCSV(data: any[]): string {
    if (!data || data.length === 0) {
      return 'No data available'
    }

    const headers = Object.keys(data[0])
    const csvContent = [
      headers.join(','),
      ...data.map(row =>
        headers.map(header =>
          JSON.stringify(row[header] || '')
        ).join(',')
      )
    ].join('\n')

    return csvContent
  }

  /**
   * Generate Excel export
   */
  private async generateExcel(
    dataType: string,
    options: ExportOptions
  ): Promise<ExportResult> {
    try {
      // For now, generate a simple Excel-compatible CSV
      // You can integrate with a proper Excel library like xlsx later
      const csvData = this.generateSimpleCSV(options.data || [])
      const blob = new Blob([csvData], { type: 'application/vnd.ms-excel' })
      const url = window.URL.createObjectURL(blob)
      const filename = options.filename || `${dataType}-export-${new Date().toISOString().split('T')[0]}.xlsx`

      const link = document.createElement('a')
      link.href = url
      link.download = filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      return {
        success: true,
        message: 'Excel exported successfully',
        downloadUrl: url
      }
    } catch (error) {
      return {
        success: false,
        message: `Excel export failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Apply digital signature to PDF
   */
  private async applySignature(
    exportResult: ExportResult,
    signatureOptions: SignatureOptions
  ): Promise<ExportResult> {
    try {
      // This would integrate with the signature API
      const response = await fetch(`${this.baseUrl}/pdf/signature/sign/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.authToken}`,
        },
        body: JSON.stringify({
          pdf_url: exportResult.downloadUrl,
          certificate_id: signatureOptions.certificateId,
          reason: signatureOptions.reason || 'Document approval',
          location: signatureOptions.location || 'Saudi Arabia'
        })
      })

      const result = await response.json()
      return {
        success: result.success,
        message: result.message,
        error: result.error
      }
    } catch (error) {
      return {
        success: false,
        message: 'Signature failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Send export via email
   */
  private async sendEmail(
    exportResult: ExportResult,
    emailOptions: EmailOptions
  ): Promise<ExportResult> {
    try {
      const response = await fetch(`${this.baseUrl}/pdf/email/send/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.authToken}`,
        },
        body: JSON.stringify({
          pdf_url: exportResult.downloadUrl,
          recipients: emailOptions.recipients,
          subject: emailOptions.subject,
          message: emailOptions.message,
          priority: emailOptions.priority || 'normal',
          email_template: emailOptions.template || 'default',
          cc_recipients: emailOptions.cc || [],
          bcc_recipients: emailOptions.bcc || []
        })
      })

      const result = await response.json()
      return {
        success: result.success,
        message: result.message,
        error: result.error
      }
    } catch (error) {
      return {
        success: false,
        message: 'Email sending failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Quick export methods for common use cases
   */
  async quickPDF(dataType: string, language: 'en' | 'ar' = 'en'): Promise<ExportResult> {
    return this.export(dataType, {
      format: 'pdf',
      language
    })
  }

  async quickCSV(dataType: string): Promise<ExportResult> {
    return this.export(dataType, {
      format: 'csv'
    })
  }

  async quickExcel(dataType: string): Promise<ExportResult> {
    return this.export(dataType, {
      format: 'excel'
    })
  }

  async emailPDF(
    dataType: string,
    recipients: string[],
    language: 'en' | 'ar' = 'en',
    subject?: string
  ): Promise<ExportResult> {
    return this.export(dataType, {
      format: 'pdf',
      language,
      emailOptions: {
        enabled: true,
        recipients,
        subject: subject || `${dataType} Report`,
        priority: 'normal'
      }
    })
  }

  async signedPDF(
    dataType: string,
    certificateId: string,
    language: 'en' | 'ar' = 'en'
  ): Promise<ExportResult> {
    return this.export(dataType, {
      format: 'pdf',
      language,
      signatureOptions: {
        enabled: true,
        certificateId,
        reason: 'Document approval'
      }
    })
  }

  /**
   * Generate professional customer HTML report
   */
  private generateCustomerHTMLReport(data: any[], language: 'ar' | 'en', reportType: string): string {
    const isRTL = language === 'ar'
    const translations = {
      ar: {
        customerReport: 'تقرير العملاء',
        totalCustomers: 'إجمالي العملاء',
        activeCustomers: 'العملاء النشطون',
        totalRevenue: 'إجمالي الإيرادات',
        customer: 'العميل',
        type: 'النوع',
        status: 'الحالة',
        contact: 'الاتصال',
        revenue: 'الإيرادات',
        individual: 'فردي',
        business: 'تجاري',
        enterprise: 'مؤسسي',
        active: 'نشط',
        inactive: 'غير نشط',
        prospect: 'محتمل'
      },
      en: {
        customerReport: 'Customer Report',
        totalCustomers: 'Total Customers',
        activeCustomers: 'Active Customers',
        totalRevenue: 'Total Revenue',
        customer: 'Customer',
        type: 'Type',
        status: 'Status',
        contact: 'Contact',
        revenue: 'Revenue',
        individual: 'Individual',
        business: 'Business',
        enterprise: 'Enterprise',
        active: 'Active',
        inactive: 'Inactive',
        prospect: 'Prospect'
      }
    }

    const t = translations[language]

    // Calculate summary statistics
    const totalCustomers = data.length
    const activeCustomers = data.filter(c => c.status === 'active').length
    const totalRevenue = data.reduce((sum, c) => sum + (c.total_spent || c.totalRevenue || c.salesValue || 0), 0)

    const formatCurrency = (amount: number) => {
      return new Intl.NumberFormat(language === 'ar' ? 'ar-SA' : 'en-US', {
        style: 'currency',
        currency: 'SAR'
      }).format(amount)
    }

    return `<!DOCTYPE html>
<html dir="${isRTL ? 'rtl' : 'ltr'}" lang="${language}">
<head>
    <meta charset="UTF-8">
    <title>${t.customerReport}</title>
    <style>
        body {
            font-family: ${isRTL ? "'Amiri', 'Cairo', 'Tajawal', Arial, sans-serif" : "'DejaVu Sans', Arial, sans-serif"};
            background: white;
            margin: 0; padding: 20px;
            direction: ${isRTL ? 'rtl' : 'ltr'};
            color: #333;
        }
        .container {
            max-width: 800px; margin: 0 auto;
            background: white;
            padding: 0;
        }
        .header {
            border-bottom: 2px solid #2563eb;
            padding: 30px 0; margin-bottom: 30px;
        }
        .company-header {
            display: flex; align-items: center; justify-content: space-between;
            margin-bottom: 20px;
        }
        .company-info {
            display: flex; align-items: center; gap: 15px;
        }
        .logo {
            width: 50px; height: 50px; background: #2563eb;
            border-radius: 8px; display: flex; align-items: center;
            justify-content: center; color: white; font-size: 24px;
        }
        .company-name {
            font-size: 1.5rem; font-weight: bold; color: #2563eb;
        }
        .report-title {
            text-align: center; font-size: 1.25rem;
            font-weight: 600; color: #374151; margin-bottom: 10px;
        }
        .summary {
            display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px; margin-bottom: 30px;
        }
        .summary-card {
            background: #f9fafb; padding: 20px; border-radius: 8px;
            border: 1px solid #e5e7eb; display: flex; align-items: center; gap: 15px;
        }
        .summary-icon {
            width: 40px; height: 40px; border-radius: 8px;
            display: flex; align-items: center; justify-content: center;
        }
        .summary-card h3 { color: #6b7280; font-size: 0.8rem; margin: 0 0 5px 0; }
        .summary-card p { color: #374151; font-size: 1.5rem; font-weight: 700; margin: 0; }
        table {
            width: 100%; border-collapse: collapse; margin: 20px 0;
            border: 1px solid #e5e7eb; border-radius: 8px; overflow: hidden;
        }
        th {
            background: #f9fafb; color: #374151; padding: 12px;
            text-align: ${isRTL ? 'right' : 'left'}; font-weight: 600;
            border-bottom: 1px solid #e5e7eb; font-size: 0.9rem;
        }
        td {
            padding: 10px 12px; border-bottom: 1px solid #f3f4f6;
            font-size: 0.85rem; color: #374151;
        }
        tr:hover { background-color: #f9fafb; }
        .status-badge {
            padding: 3px 8px; border-radius: 12px; font-size: 0.7rem;
            font-weight: 500; text-transform: uppercase; border: 1px solid;
        }
        .status-active { background: #dcfce7; color: #166534; border-color: #bbf7d0; }
        .status-inactive { background: #f1f5f9; color: #475569; border-color: #e2e8f0; }
        .status-prospect { background: #dbeafe; color: #1d4ed8; border-color: #bfdbfe; }
        .footer {
            border-top: 2px solid #2563eb; padding: 20px 0; margin-top: 30px;
            display: flex; justify-content: space-between; align-items: center;
            font-size: 0.8rem; color: #6b7280;
        }
        .footer-center {
            text-align: center; margin-top: 15px; padding-top: 15px;
            border-top: 1px solid #e5e7eb; font-size: 0.7rem; color: #9ca3af;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="company-header">
                <div class="company-info">
                    <div class="logo">🏢</div>
                    <div>
                        <div class="company-name">Enterprise Management System</div>
                        <div style="font-size: 0.8rem; color: #6b7280;">Professional Business Solutions</div>
                    </div>
                </div>
                <div style="text-align: ${isRTL ? 'left' : 'right'}; font-size: 0.8rem; color: #6b7280;">
                    ${new Date().toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}
                </div>
            </div>
            <div class="report-title">${t.customerReport}</div>
        </div>

        <div class="summary">
            <div class="summary-card">
                <div class="summary-icon" style="background: #dbeafe; color: #2563eb;">👥</div>
                <div>
                    <h3>${t.totalCustomers}</h3>
                    <p>${totalCustomers}</p>
                </div>
            </div>
            <div class="summary-card">
                <div class="summary-icon" style="background: #dcfce7; color: #16a34a;">✓</div>
                <div>
                    <h3>${t.activeCustomers}</h3>
                    <p>${activeCustomers}</p>
                </div>
            </div>
            <div class="summary-card">
                <div class="summary-icon" style="background: #fef3c7; color: #d97706;">💰</div>
                <div>
                    <h3>${t.totalRevenue}</h3>
                    <p>${formatCurrency(totalRevenue)}</p>
                </div>
            </div>
        </div>

        <div style="padding: 30px;">
            ${data.length > 0 ? `
            <table>
                <thead>
                    <tr>
                        <th>${t.customer}</th>
                        <th>${t.type}</th>
                        <th>${t.status}</th>
                        <th>${t.contact}</th>
                        <th>${t.revenue}</th>
                    </tr>
                </thead>
                <tbody>
                    ${data.map(customer => `
                        <tr>
                            <td><strong>${customer.full_name || customer.name || `${customer.first_name || ''} ${customer.last_name || ''}`.trim()}</strong></td>
                            <td>${t[customer.customer_type as keyof typeof t] || customer.customer_type || '-'}</td>
                            <td><span class="status-badge status-${customer.status || 'inactive'}">${t[customer.status as keyof typeof t] || customer.status || '-'}</span></td>
                            <td>${customer.email || ''}<br>${customer.phone || ''}</td>
                            <td><strong>${formatCurrency(customer.total_spent || customer.totalRevenue || customer.salesValue || 0)}</strong></td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
            ` : `<p style="text-align: center; padding: 40px; color: #6b7280;">${language === 'ar' ? 'لا توجد بيانات متاحة' : 'No data available'}</p>`}

        <div class="footer">
            <div>
                <div style="font-weight: 600;">Enterprise Management System</div>
                <div>${language === 'ar' ? 'تم إنشاء هذا التقرير بواسطة نظام إدارة المؤسسات' : 'Generated by Enterprise Management System'}</div>
            </div>
            <div style="text-align: ${isRTL ? 'left' : 'right'};">
                <div>${language === 'ar' ? 'تاريخ الإنشاء:' : 'Generated:'} ${new Date().toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}</div>
                <div>${language === 'ar' ? 'الوقت:' : 'Time:'} ${new Date().toLocaleTimeString(language === 'ar' ? 'ar-SA' : 'en-US')}</div>
            </div>
        </div>

        <div class="footer-center">
            ${language === 'ar'
                ? 'هذا التقرير سري ومخصص للاستخدام الداخلي فقط'
                : 'This report is confidential and for internal use only'
            }
        </div>
    </div>
</body>
</html>`
  }

  /**
   * Generate employee HTML report
   */
  private generateEmployeeHTMLReport(data: any[], language: 'ar' | 'en', reportType: string): string {
    const isRTL: any = language === 'ar'
    const translations = {
      ar: {
        employeeReport: 'تقرير الموظفين',
        totalEmployees: 'إجمالي الموظفين',
        departments: 'الأقسام',
        averageSalary: 'متوسط الراتب',
        employeeName: 'اسم الموظف',
        position: 'المنصب',
        department: 'القسم',
        salary: 'الراتب',
        hireDate: 'تاريخ التوظيف'
      },
      en: {
        employeeReport: 'Employee Report',
        totalEmployees: 'Total Employees',
        departments: 'Departments',
        averageSalary: 'Average Salary',
        employeeName: 'Employee Name',
        position: 'Position',
        department: 'Department',
        salary: 'Salary',
        hireDate: 'Hire Date'
      }
    }

    const t = translations[language]
    const totalEmployees = data.length
    const departments = [...new Set(data.map(e => e.department))].length
    const averageSalary = data.reduce((sum, e) => sum + (e.salary || 0), 0) / data.length

    const formatCurrency = (amount: number) => {
      return new Intl.NumberFormat(language === 'ar' ? 'ar-SA' : 'en-US', {
        style: 'currency',
        currency: 'SAR'
      }).format(amount)
    }

    return `<!DOCTYPE html>
<html dir="${isRTL ? 'rtl' : 'ltr'}" lang="${language}">
<head>
    <meta charset="UTF-8">
    <title>${t.employeeReport}</title>
    <style>
        body {
            font-family: ${isRTL ? "'Amiri', 'Cairo', Arial, sans-serif" : "'Arial', 'Helvetica', sans-serif"};
            background: white; margin: 0; padding: 20px;
            direction: ${isRTL ? 'rtl' : 'ltr'}; color: #333;
        }
        .container { max-width: 800px; margin: 0 auto; }
        .header {
            border-bottom: 2px solid #059669; padding: 30px 0; margin-bottom: 30px;
        }
        .company-header {
            display: flex; align-items: center; justify-content: space-between; margin-bottom: 20px;
        }
        .company-info { display: flex; align-items: center; gap: 15px; }
        .logo {
            width: 50px; height: 50px; background: #059669;
            border-radius: 8px; display: flex; align-items: center;
            justify-content: center; color: white; font-size: 24px;
        }
        .company-name { font-size: 1.5rem; font-weight: bold; color: #059669; }
        .report-title {
            text-align: center; font-size: 1.25rem;
            font-weight: 600; color: #374151; margin-bottom: 10px;
        }
        .summary {
            display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px; margin-bottom: 30px;
        }
        .summary-card {
            background: #f0fdf4; padding: 20px; border-radius: 8px;
            border: 1px solid #bbf7d0; display: flex; align-items: center; gap: 15px;
        }
        .summary-icon {
            width: 40px; height: 40px; border-radius: 8px;
            display: flex; align-items: center; justify-content: center;
            background: #dcfce7; color: #059669;
        }
        .summary-card h3 { color: #6b7280; font-size: 0.8rem; margin: 0 0 5px 0; }
        .summary-card p { color: #374151; font-size: 1.5rem; font-weight: 700; margin: 0; }
        table {
            width: 100%; border-collapse: collapse; margin: 20px 0;
            border: 1px solid #e5e7eb; border-radius: 8px; overflow: hidden;
        }
        th {
            background: #f0fdf4; color: #374151; padding: 12px;
            text-align: ${isRTL ? 'right' : 'left'}; font-weight: 600;
            border-bottom: 1px solid #bbf7d0; font-size: 0.9rem;
        }
        td {
            padding: 10px 12px; border-bottom: 1px solid #f3f4f6;
            font-size: 0.85rem; color: #374151;
        }
        tr:hover { background-color: #f9fafb; }
        .footer {
            border-top: 2px solid #059669; padding: 20px 0; margin-top: 30px;
            display: flex; justify-content: space-between; align-items: center;
            font-size: 0.8rem; color: #6b7280;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="company-header">
                <div class="company-info">
                    <div class="logo">👥</div>
                    <div>
                        <div class="company-name">Enterprise Management System</div>
                        <div style="font-size: 0.8rem; color: #6b7280;">Human Resources Department</div>
                    </div>
                </div>
                <div style="text-align: ${isRTL ? 'left' : 'right'}; font-size: 0.8rem; color: #6b7280;">
                    ${new Date().toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}
                </div>
            </div>
            <div class="report-title">${t.employeeReport}</div>
        </div>

        <div class="summary">
            <div class="summary-card">
                <div class="summary-icon">👥</div>
                <div>
                    <h3>${t.totalEmployees}</h3>
                    <p>${totalEmployees}</p>
                </div>
            </div>
            <div class="summary-card">
                <div class="summary-icon">🏢</div>
                <div>
                    <h3>${t.departments}</h3>
                    <p>${departments}</p>
                </div>
            </div>
            <div class="summary-card">
                <div class="summary-icon">💰</div>
                <div>
                    <h3>${t.averageSalary}</h3>
                    <p>${formatCurrency(averageSalary)}</p>
                </div>
            </div>
        </div>

        <table>
            <thead>
                <tr>
                    <th>${t.employeeName}</th>
                    <th>${t.position}</th>
                    <th>${t.department}</th>
                    <th>${t.salary}</th>
                    <th>${t.hireDate}</th>
                </tr>
            </thead>
            <tbody>
                ${data.map(employee => `
                    <tr>
                        <td><strong>${employee.name || employee.first_name || ''} ${employee.last_name || ''}</strong></td>
                        <td>${employee.position || employee.job_title || '-'}</td>
                        <td>${employee.department || '-'}</td>
                        <td><strong>${formatCurrency(employee.salary || 0)}</strong></td>
                        <td>${employee.hire_date || employee.created_at || '-'}</td>
                    </tr>
                `).join('')}
            </tbody>
        </table>

        <div class="footer">
            <div>
                <div style="font-weight: 600;">Enterprise Management System</div>
                <div>${language === 'ar' ? 'تم إنشاء هذا التقرير بواسطة قسم الموارد البشرية' : 'Generated by Human Resources Department'}</div>
            </div>
            <div style="text-align: ${isRTL ? 'left' : 'right'};">
                <div>${language === 'ar' ? 'تاريخ الإنشاء:' : 'Generated:'} ${new Date().toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}</div>
                <div>${language === 'ar' ? 'الوقت:' : 'Time:'} ${new Date().toLocaleTimeString(language === 'ar' ? 'ar-SA' : 'en-US')}</div>
            </div>
        </div>
    </div>
</body>
</html>`
  }

  /**
   * Generate HR HTML report
   */
  private generateHRHTMLReport(data: any[], language: 'ar' | 'en', reportType: string): string {
    const isRTL: any = language === 'ar'
    const title = language === 'ar' ? 'تقرير الموارد البشرية' : 'HR Report'
    const headers = data.length > 0 ? Object.keys(data[0]) : []

    return `<!DOCTYPE html>
<html dir="${isRTL ? 'rtl' : 'ltr'}" lang="${language}">
<head>
    <meta charset="UTF-8">
    <title>${title}</title>
    <style>
        body {
            font-family: ${isRTL ? "'Amiri', 'Cairo', Arial, sans-serif" : "'Arial', 'Helvetica', sans-serif"};
            background: white; margin: 0; padding: 20px;
            direction: ${isRTL ? 'rtl' : 'ltr'}; color: #333;
        }
        .container { max-width: 800px; margin: 0 auto; }
        .header {
            border-bottom: 2px solid #7c3aed; padding: 30px 0; margin-bottom: 30px;
            text-align: center;
        }
        .header h1 { color: #7c3aed; font-size: 1.8rem; margin: 0; }
        .header p { color: #6b7280; margin: 10px 0 0 0; }
        table {
            width: 100%; border-collapse: collapse; margin: 20px 0;
            border: 1px solid #e5e7eb; border-radius: 8px; overflow: hidden;
        }
        th {
            background: #f3f4f6; color: #374151; padding: 12px;
            text-align: ${isRTL ? 'right' : 'left'}; font-weight: 600;
            border-bottom: 1px solid #d1d5db;
        }
        td {
            padding: 10px 12px; border-bottom: 1px solid #f3f4f6;
            font-size: 0.9rem; color: #374151;
        }
        tr:hover { background-color: #f9fafb; }
        .footer {
            border-top: 2px solid #7c3aed; padding: 20px 0; margin-top: 30px;
            text-align: center; font-size: 0.8rem; color: #6b7280;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>${title}</h1>
            <p>${new Date().toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}</p>
        </div>

        ${data.length > 0 ? `
        <table>
            <thead>
                <tr>
                    ${headers.map(header => `<th>${header}</th>`).join('')}
                </tr>
            </thead>
            <tbody>
                ${data.map(row => `
                    <tr>
                        ${headers.map(header => `<td>${row[header] || ''}</td>`).join('')}
                    </tr>
                `).join('')}
            </tbody>
        </table>
        ` : `
        <p style="text-align: center; padding: 40px; color: #6b7280;">${language === 'ar' ? 'لا توجد بيانات متاحة' : 'No data available'}</p>
        `}

        <div class="footer">
            ${language === 'ar' ? 'تم إنشاء هذا التقرير بواسطة قسم الموارد البشرية' : 'Generated by Human Resources Department'}
        </div>
    </div>
</body>
</html>`
  }

  /**
   * Generate Sales HTML report
   */
  private generateSalesHTMLReport(data: any[], language: 'ar' | 'en', reportType: string): string {
    const isRTL: any = language === 'ar'
    const title = language === 'ar' ? 'تقرير المبيعات' : 'Sales Report'
    const headers = data.length > 0 ? Object.keys(data[0]) : []

    return `<!DOCTYPE html>
<html dir="${isRTL ? 'rtl' : 'ltr'}" lang="${language}">
<head>
    <meta charset="UTF-8">
    <title>${title}</title>
    <style>
        body {
            font-family: ${isRTL ? "'Amiri', 'Cairo', Arial, sans-serif" : "'Arial', 'Helvetica', sans-serif"};
            background: white; margin: 0; padding: 20px;
            direction: ${isRTL ? 'rtl' : 'ltr'}; color: #333;
        }
        .container { max-width: 800px; margin: 0 auto; }
        .header {
            border-bottom: 2px solid #dc2626; padding: 30px 0; margin-bottom: 30px;
            text-align: center;
        }
        .header h1 { color: #dc2626; font-size: 1.8rem; margin: 0; }
        .header p { color: #6b7280; margin: 10px 0 0 0; }
        table {
            width: 100%; border-collapse: collapse; margin: 20px 0;
            border: 1px solid #e5e7eb; border-radius: 8px; overflow: hidden;
        }
        th {
            background: #fef2f2; color: #374151; padding: 12px;
            text-align: ${isRTL ? 'right' : 'left'}; font-weight: 600;
            border-bottom: 1px solid #fecaca;
        }
        td {
            padding: 10px 12px; border-bottom: 1px solid #f3f4f6;
            font-size: 0.9rem; color: #374151;
        }
        tr:hover { background-color: #f9fafb; }
        .footer {
            border-top: 2px solid #dc2626; padding: 20px 0; margin-top: 30px;
            text-align: center; font-size: 0.8rem; color: #6b7280;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>${title}</h1>
            <p>${new Date().toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}</p>
        </div>

        ${data.length > 0 ? `
        <table>
            <thead>
                <tr>
                    ${headers.map(header => `<th>${header}</th>`).join('')}
                </tr>
            </thead>
            <tbody>
                ${data.map(row => `
                    <tr>
                        ${headers.map(header => `<td>${row[header] || ''}</td>`).join('')}
                    </tr>
                `).join('')}
            </tbody>
        </table>
        ` : `
        <p style="text-align: center; padding: 40px; color: #6b7280;">${language === 'ar' ? 'لا توجد بيانات متاحة' : 'No data available'}</p>
        `}

        <div class="footer">
            ${language === 'ar' ? 'تم إنشاء هذا التقرير بواسطة قسم المبيعات' : 'Generated by Sales Department'}
        </div>
    </div>
</body>
</html>`
  }

  /**
   * Generate Finance HTML report
   */
  private generateFinanceHTMLReport(data: any[], language: 'ar' | 'en', reportType: string): string {
    const isRTL: any = language === 'ar'
    const title = language === 'ar' ? 'التقرير المالي' : 'Finance Report'
    const headers = data.length > 0 ? Object.keys(data[0]) : []

    return `<!DOCTYPE html>
<html dir="${isRTL ? 'rtl' : 'ltr'}" lang="${language}">
<head>
    <meta charset="UTF-8">
    <title>${title}</title>
    <style>
        body {
            font-family: ${isRTL ? "'Amiri', 'Cairo', Arial, sans-serif" : "'Arial', 'Helvetica', sans-serif"};
            background: white; margin: 0; padding: 20px;
            direction: ${isRTL ? 'rtl' : 'ltr'}; color: #333;
        }
        .container { max-width: 800px; margin: 0 auto; }
        .header {
            border-bottom: 2px solid #d97706; padding: 30px 0; margin-bottom: 30px;
            text-align: center;
        }
        .header h1 { color: #d97706; font-size: 1.8rem; margin: 0; }
        .header p { color: #6b7280; margin: 10px 0 0 0; }
        table {
            width: 100%; border-collapse: collapse; margin: 20px 0;
            border: 1px solid #e5e7eb; border-radius: 8px; overflow: hidden;
        }
        th {
            background: #fffbeb; color: #374151; padding: 12px;
            text-align: ${isRTL ? 'right' : 'left'}; font-weight: 600;
            border-bottom: 1px solid #fed7aa;
        }
        td {
            padding: 10px 12px; border-bottom: 1px solid #f3f4f6;
            font-size: 0.9rem; color: #374151;
        }
        tr:hover { background-color: #f9fafb; }
        .footer {
            border-top: 2px solid #d97706; padding: 20px 0; margin-top: 30px;
            text-align: center; font-size: 0.8rem; color: #6b7280;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>${title}</h1>
            <p>${new Date().toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}</p>
        </div>

        ${data.length > 0 ? `
        <table>
            <thead>
                <tr>
                    ${headers.map(header => `<th>${header}</th>`).join('')}
                </tr>
            </thead>
            <tbody>
                ${data.map(row => `
                    <tr>
                        ${headers.map(header => `<td>${row[header] || ''}</td>`).join('')}
                    </tr>
                `).join('')}
            </tbody>
        </table>
        ` : `
        <p style="text-align: center; padding: 40px; color: #6b7280;">${language === 'ar' ? 'لا توجد بيانات متاحة' : 'No data available'}</p>
        `}

        <div class="footer">
            ${language === 'ar' ? 'تم إنشاء هذا التقرير بواسطة القسم المالي' : 'Generated by Finance Department'}
        </div>
    </div>
</body>
</html>`
  }

}

// Additional types for compatibility
export interface ExportColumn {
  key: string
  label: string
  type?: 'string' | 'number' | 'date' | 'boolean'
  format?: string
}

export interface ExportTemplate {
  id: string
  name: string
  description?: string
  columns: ExportColumn[]
}

export interface ExportProgress {
  progress: number
  message: string
  completed: boolean
}

export interface ExportData {
  [key: string]: any
}

// Export singleton instance
export const unifiedExport = new UnifiedExportService()

// Export service instance for compatibility
export const exportService = unifiedExport
