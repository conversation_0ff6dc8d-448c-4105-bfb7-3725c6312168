import React from 'react';
/**
 * User Management Service
 * Dedicated service for user management with proper role mapping and data transformation
 */

import { apiClient } from './api'
import { mapBackendRoleToFrontend } from '../utils/roleMapping'

export interface User {
  id: number
  fullName: string
  fullNameAr: string
  email: string
  phone: string
  role: string // Frontend role name (superAdmin, admin, hrManager, etc.)
  roleAr: string
  department: string
  departmentAr: string
  status: 'active' | 'inactive' | 'pending' | 'suspended'
  lastLogin: string
  joinDate: string
  position: string
  positionAr: string
  permissions: string[]
  profilePicture?: string
  address?: string
  addressAr?: string
  emergencyContact?: string
  emergencyContactAr?: string
  gender?: 'M' | 'F'
  employeeId?: string
  salary?: number
  // Creator information
  createdBy?: {
    id: number
    name: string
    nameAr: string
    role: string
    roleAr: string
  }
  createdAt: string
}

export interface BackendEmployee {
  id: number
  user: {
    id: number
    username: string
    email: string
    first_name: string
    last_name: string
    is_active: boolean
    last_login: string | null
    date_joined: string
  }
  employee_id: string
  department: {
    id: number
    name: string
    name_ar: string
  } | null
  department_name?: string
  department_name_ar?: string
  position: string
  position_ar: string
  first_name_ar?: string
  last_name_ar?: string
  phone: string
  gender?: string
  hire_date?: string
  salary?: number | null
  is_active?: boolean
  address?: string
  address_ar?: string
  emergency_contact?: string
  emergency_contact_ar?: string
  emergency_contact_name?: string
  emergency_contact_name_ar?: string
  profile_picture?: string | null
  created_at: string
  updated_at: string
  user_profile?: {
    role: {
      id: number
      name: string // Backend role name (SUPERADMIN, ADMIN, HR_MANAGER, etc.)
      name_ar: string
      permissions: Record<string, any>
    } | null
  }
  created_by_info?: {
    id: number
    name: string
    nameAr: string
    role: string
    roleAr: string
  } | null
}

export interface CreateUserData {
  fullName: string
  fullNameAr: string
  email: string
  phone: string
  role: string
  department: string
  position: string
  positionAr: string
  status: string
  joinDate: string
  address?: string
  addressAr?: string
  emergencyContact?: string
  emergencyContactAr?: string
  gender?: 'M' | 'F'
  employeeId?: string
  salary?: number
}

class UserManagementService {
  private baseUrl = '/employees'

  /**
   * Transform backend employee data to frontend user format
   * FIXED: Proper field mapping and data transformation
   */
  private transformEmployeeToUser(employee: BackendEmployee): User {
    const backendRole = employee.user_profile?.role?.name || 'EMPLOYEE'
    const frontendRole = mapBackendRoleToFrontend(backendRole)

    // Extract permissions from role
    const permissions = employee.user_profile?.role?.permissions ?
      Object.keys(employee.user_profile.role.permissions).filter(key =>
        employee.user_profile.role.permissions[key] === true
      ) : []

    // Format last login properly
    const formatLastLogin = (lastLogin: string | null) => {
      if (!lastLogin || lastLogin === 'null' || lastLogin === 'None') return 'Never'
      try {
        const date = new Date(lastLogin)
        if (isNaN(date.getTime())) return 'Never'

        // Format as relative time if recent, otherwise as date
        const now = new Date()
        const diffMs = now.getTime() - date.getTime()
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

        if (diffDays === 0) {
          return 'Today'
        } else if (diffDays === 1) {
          return 'Yesterday'
        } else if (diffDays < 7) {
          return `${diffDays} days ago`
        } else {
          return date.toLocaleDateString()
        }
      } catch {
        return 'Never'
      }
    }

    // Use proper Arabic names
    const fullNameAr = employee.first_name_ar && employee.last_name_ar ?
      `${employee.first_name_ar} ${employee.last_name_ar}`.trim() :
      `${employee.user.first_name} ${employee.user.last_name}`.trim()

    return {
      id: employee.id,
      fullName: `${employee.user.first_name} ${employee.user.last_name}`.trim(),
      fullNameAr: fullNameAr,
      email: employee.user.email,
      phone: employee.phone || '',
      role: frontendRole,
      roleAr: employee.user_profile?.role?.name_ar || 'موظف',
      department: employee.department_name || employee.department?.name || 'N/A',
      departmentAr: employee.department_name_ar || employee.department?.name_ar || 'غير محدد',
      status: employee.user.is_active ? 'active' : 'inactive',
      lastLogin: formatLastLogin(employee.user.last_login),
      joinDate: employee.hire_date || employee.user.date_joined, // Use hire_date first
      position: employee.position || 'N/A',
      positionAr: employee.position_ar || 'غير محدد',
      permissions: permissions,
      profilePicture: employee.profile_picture || undefined,
      address: employee.address || undefined,
      addressAr: employee.address_ar || undefined,
      emergencyContact: employee.emergency_contact || employee.emergency_contact_name || undefined,
      emergencyContactAr: employee.emergency_contact_ar || employee.emergency_contact_name_ar || undefined,
      gender: (employee.gender === 'M' || employee.gender === 'F') ? employee.gender : undefined,
      employeeId: employee.employee_id || undefined,
      salary: employee.salary || undefined,
      // Creator information
      createdBy: employee.created_by_info ? {
        id: employee.created_by_info.id,
        name: employee.created_by_info.name,
        nameAr: employee.created_by_info.nameAr,
        role: mapBackendRoleToFrontend(employee.created_by_info.role),
        roleAr: employee.created_by_info.roleAr
      } : undefined,
      createdAt: employee.created_at || employee.user.date_joined
    }
  }

  /**
   * Get all users with proper transformation
   */
  async getUsers(params?: {
    search?: string
    department?: string
    role?: string
    status?: string
    page?: number
    page_size?: number
  }): Promise<{ data: User[], total: number, page: number, totalPages: number }> {
    try {
      const response = await apiClient.get<{
        results: BackendEmployee[]
        count: number
        next: string | null
        previous: string | null
      }>(this.baseUrl, { params })

      const transformedUsers = response.data.results.map(employee => 
        this.transformEmployeeToUser(employee)
      )

      const pageSize = params?.page_size || 20
      const currentPage = params?.page || 1
      const totalPages = Math.ceil(response.data.count / pageSize)

      return {
        data: transformedUsers,
        total: response.data.count,
        page: currentPage,
        totalPages
      }
    } catch (error) {
      console.error('Error fetching users:', error)
      throw error
    }
  }

  /**
   * Get single user by ID
   */
  async getUser(id: number): Promise<User> {
    try {
      const response = await apiClient.get<BackendEmployee>(`${this.baseUrl}/${id}/`)
      return this.transformEmployeeToUser(response.data)
    } catch (error) {
      console.error('Error fetching user:', error)
      throw error
    }
  }

  /**
   * Create new user
   */
  async createUser(userData: CreateUserData): Promise<User> {
    try {
      // Transform frontend data to backend format
      // The EmployeeSerializer expects user fields at the top level, not nested
      const backendData = {
        // User fields (flattened from nested user object)
        first_name: userData.fullName.split(' ')[0] || '',
        last_name: userData.fullName.split(' ').slice(1).join(' ') || '',
        email: userData.email,
        username: userData.email, // Use email as username

        // Employee fields
        phone: userData.phone,
        position: userData.position,
        position_ar: userData.positionAr,
        hire_date: userData.joinDate,

        // Optional fields
        address: userData.address || '',
        address_ar: userData.addressAr || '',
        emergency_contact_name: userData.emergencyContact || '',
        emergency_contact_name_ar: userData.emergencyContactAr || '',

        // Set default values for required fields
        employee_id: userData.email.split('@')[0], // Generate employee ID from email
        gender: 'M', // Default gender - should be made configurable
        employment_status: 'FULL_TIME',

        // TODO: Handle department and role assignment properly
        // For now, we'll let the backend handle defaults
      }

      console.log('🔧 Creating user with backend data:', backendData)
      const response = await apiClient.post<BackendEmployee>(this.baseUrl, backendData)
      console.log('✅ User created successfully:', response.data)
      return this.transformEmployeeToUser(response.data)
    } catch (error: any) {
      console.error('❌ Error creating user:', error)

      // Provide more meaningful error messages
      if (error.response?.status === 400) {
        const errorData = error.response.data
        if (errorData.user?.email) {
          throw new Error('البريد الإلكتروني مستخدم بالفعل / Email already exists')
        }
        if (errorData.user?.username) {
          throw new Error('اسم المستخدم مستخدم بالفعل / Username already exists')
        }
        if (errorData.phone) {
          throw new Error('رقم الهاتف مستخدم بالفعل / Phone number already exists')
        }
        throw new Error('بيانات غير صحيحة / Invalid data provided')
      } else if (error.response?.status === 500) {
        throw new Error('خطأ في الخادم. يرجى المحاولة مرة أخرى / Server error. Please try again')
      } else if (error.code === 'NETWORK_ERROR' || !error.response) {
        throw new Error('خطأ في الاتصال. تحقق من الإنترنت / Network error. Check your connection')
      }

      throw new Error('حدث خطأ غير متوقع / An unexpected error occurred')
    }
  }

  /**
   * Update user
   */
  async updateUser(id: number, userData: Partial<CreateUserData>): Promise<User> {
    try {
      console.log('🔧 Updating user with frontend data:', userData)

      // Transform frontend data to backend format
      const backendData: any = {}

      // Handle user fields (Django User model)
      if (userData.fullName || userData.email) {
        backendData.user = {}

        if (userData.fullName) {
          const nameParts = userData.fullName.split(' ')
          backendData.user.first_name = nameParts[0] || ''
          backendData.user.last_name = nameParts.slice(1).join(' ') || ''
        }

        if (userData.email) {
          backendData.user.email = userData.email
        }
      }

      // Handle Employee model fields
      if (userData.phone) backendData.phone = userData.phone
      if (userData.position) backendData.position = userData.position
      if (userData.positionAr) backendData.position_ar = userData.positionAr
      if (userData.address) backendData.address = userData.address
      if (userData.addressAr) backendData.address_ar = userData.addressAr
      if (userData.emergencyContact) backendData.emergency_contact = userData.emergencyContact
      if (userData.emergencyContactAr) backendData.emergency_contact_ar = userData.emergencyContactAr
      if (userData.joinDate) backendData.hire_date = userData.joinDate
      if (userData.gender) backendData.gender = userData.gender
      if (userData.employeeId) backendData.employee_id = userData.employeeId
      if (userData.salary) backendData.salary = userData.salary

      // Handle Arabic name fields
      if (userData.fullNameAr) {
        const arabicNameParts = userData.fullNameAr.split(' ')
        backendData.first_name_ar = arabicNameParts[0] || ''
        backendData.last_name_ar = arabicNameParts.slice(1).join(' ') || ''
      }

      // Handle status (maps to user.is_active)
      if (userData.status) {
        if (!backendData.user) backendData.user = {}
        backendData.user.is_active = userData.status === 'active'
      }

      // Handle role update (requires separate API call to UserProfile)
      if (userData.role) {
        // Note: Role updates might need a separate endpoint
        console.log('🔄 Role update requested:', userData.role)
      }

      // Handle department update
      if (userData.department) {
        // This might need to be a department ID rather than name
        console.log('🔄 Department update requested:', userData.department)
      }

      console.log('📤 Sending backend data:', backendData)
      const response = await apiClient.patch<BackendEmployee>(`${this.baseUrl}/${id}/`, backendData)
      console.log('✅ User updated successfully:', response.data)
      return this.transformEmployeeToUser(response.data)
    } catch (error: any) {
      console.error('❌ Error updating user:', error)

      // Provide more meaningful error messages
      if (error.response?.status === 400) {
        const errorData = error.response.data
        if (errorData.user?.email) {
          throw new Error('البريد الإلكتروني مستخدم بالفعل / Email already exists')
        }
        if (errorData.phone) {
          throw new Error('رقم الهاتف مستخدم بالفعل / Phone number already exists')
        }
        throw new Error('بيانات غير صحيحة / Invalid data provided')
      } else if (error.response?.status === 404) {
        throw new Error('المستخدم غير موجود / User not found')
      } else if (error.response?.status === 500) {
        throw new Error('خطأ في الخادم. يرجى المحاولة مرة أخرى / Server error. Please try again')
      } else if (error.code === 'NETWORK_ERROR' || !error.response) {
        throw new Error('خطأ في الاتصال. تحقق من الإنترنت / Network error. Check your connection')
      }

      throw new Error('حدث خطأ غير متوقع / An unexpected error occurred')
    }
  }

  /**
   * Delete user
   */
  async deleteUser(id: number): Promise<void> {
    try {
      await apiClient.delete(`${this.baseUrl}/${id}/`)
    } catch (error) {
      console.error('Error deleting user:', error)
      throw error
    }
  }

  /**
   * Export users data
   */
  async exportUsers(format: 'csv' | 'excel' = 'csv'): Promise<Blob> {
    try {
      const response = await fetch(`${this.baseUrl}/export/?format=${format}`, {
        method: 'GET',
        credentials: 'include',
      })
      
      if (!response.ok) {
        throw new Error('Export failed')
      }
      
      return await response.blob()
    } catch (error) {
      console.error('Error exporting users:', error)
      throw error
    }
  }
}

/**
 * CRUD-compatible wrapper for UserManagementService
 * Adapts the service to work with the existing useCrud hook
 */
class CrudCompatibleUserService {
  private service = new UserManagementService()

  async getAll(options: any = {}) {
    const { search, filters, page = 1, pageSize = 20 } = options

    const params: any = {
      page,
      page_size: pageSize
    }

    if (search) params.search = search
    if (filters?.department) params.department = filters.department
    if (filters?.role) params.role = filters.role
    if (filters?.status) params.status = filters.status

    const result = await this.service.getUsers(params)

    // Return in format expected by useCrud
    return {
      data: result.data,
      total: result.total,
      page: result.page,
      totalPages: result.totalPages
    }
  }

  async getById(id: number) {
    return await this.service.getUser(id)
  }

  async create(data: CreateUserData) {
    return await this.service.createUser(data)
  }

  async update(id: number, data: Partial<CreateUserData>) {
    return await this.service.updateUser(id, data)
  }

  async delete(id: number) {
    return await this.service.deleteUser(id)
  }

  async export(format: 'csv' | 'excel' = 'csv') {
    return await this.service.exportUsers(format)
  }
}

export const userManagementService = new CrudCompatibleUserService()
export default userManagementService
