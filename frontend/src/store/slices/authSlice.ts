import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import type { PayloadAction } from '@reduxjs/toolkit'

export interface User {
  id: string
  email: string
  name: string
  nameAr: string
  role: UserRole
  department: string
  departmentAr: string
  avatar?: string
  permissions: Permission[]
  preferences: UserPreferences
  lastLogin?: string
  isActive: boolean
  // Extended profile information
  phone?: string
  address?: string
  addressEn?: string
  birthDate?: string
  nationalId?: string
  position?: string
  positionEn?: string
  manager?: string
  managerEn?: string
  joinDate?: string
  salary?: number
  employeeId?: string
  emergencyContact?: string
  emergencyContactEn?: string
  education?: string
  educationEn?: string
  experience?: string
  experienceEn?: string
}

export interface UserRole {
  id: string
  name: string
  nameAr: string
  level: number // 1=Super Admin, 2=Admin, 3=Manager, 4=Employee, 5=Intern
  permissions: string[]
}

export interface Permission {
  module: string
  actions: string[] // ['read', 'write', 'delete', 'approve']
}

export interface UserPreferences {
  language: 'ar' | 'en'
  theme: 'light' | 'dark'
  dashboardLayout: string[]
  notifications: {
    email: boolean
    push: boolean
    sms: boolean
  }
}

interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
  loginAttempts: number
  lastLoginAttempt: number | null
}

const initialState: AuthState = {
  user: null,
  token: localStorage.getItem('token'),
  isAuthenticated: false,
  isLoading: false,
  error: null,
  loginAttempts: 0,
  lastLoginAttempt: null,
}

// Mock user data for different roles
const mockUsers: Record<string, User> = {
  '<EMAIL>': {
    id: '1',
    email: '<EMAIL>',
    name: 'Ahmed Al-Rashid',
    nameAr: 'أحمد الراشد',
    role: {
      id: 'super_admin',
      name: 'Super Administrator',
      nameAr: 'مدير النظام الرئيسي',
      level: 1,
      permissions: ['*'] // All permissions
    },
    department: 'Information Technology',
    departmentAr: 'تقنية المعلومات',
    avatar: '👨‍💻',
    permissions: [
      { module: '*', actions: ['*'] }
    ],
    preferences: {
      language: 'ar',
      theme: 'dark',
      dashboardLayout: ['analytics', 'kpi', 'projects', 'notifications'],
      notifications: { email: true, push: true, sms: true }
    },
    lastLogin: '2024-01-16T10:30:00Z',
    isActive: true
  },
  '<EMAIL>': {
    id: '2',
    email: '<EMAIL>',
    name: 'Fatima Al-Zahra',
    nameAr: 'فاطمة الزهراء',
    role: {
      id: 'hr_manager',
      name: 'HR Manager',
      nameAr: 'مدير الموارد البشرية',
      level: 3,
      permissions: ['hr.*', 'employees.*', 'payroll.*']
    },
    department: 'Human Resources',
    departmentAr: 'الموارد البشرية',
    avatar: '👩‍💼',
    permissions: [
      { module: 'hr', actions: ['read', 'write', 'approve'] },
      { module: 'employees', actions: ['read', 'write', 'approve'] },
      { module: 'departments', actions: ['read', 'write'] },
      { module: 'payroll', actions: ['read', 'write'] },
      { module: 'reports', actions: ['read'] }
    ],
    preferences: {
      language: 'ar',
      theme: 'dark',
      dashboardLayout: ['hr_metrics', 'employee_stats', 'leave_requests', 'attendance'],
      notifications: { email: true, push: true, sms: false }
    },
    lastLogin: '2024-01-16T09:15:00Z',
    isActive: true
  },
  '<EMAIL>': {
    id: '3',
    email: '<EMAIL>',
    name: 'Mohammed Al-Mansouri',
    nameAr: 'محمد المنصوري',
    role: {
      id: 'finance_manager',
      name: 'Finance Manager',
      nameAr: 'مدير المالية',
      level: 3,
      permissions: ['finance.*', 'budget.*', 'expenses.*']
    },
    department: 'Finance',
    departmentAr: 'المالية',
    avatar: '👨‍💼',
    permissions: [
      { module: 'finance', actions: ['read', 'write', 'approve'] },
      { module: 'budget', actions: ['read', 'write', 'approve'] },
      { module: 'expenses', actions: ['read', 'write', 'approve'] },
      { module: 'assets', actions: ['read', 'write', 'approve'] },
      { module: 'analytics', actions: ['read'] },
      { module: 'reports', actions: ['read', 'write'] }
    ],
    preferences: {
      language: 'ar',
      theme: 'dark',
      dashboardLayout: ['financial_overview', 'budget_status', 'expense_tracking', 'revenue'],
      notifications: { email: true, push: true, sms: true }
    },
    lastLogin: '2024-01-16T08:45:00Z',
    isActive: true
  },
  '<EMAIL>': {
    id: '4',
    email: '<EMAIL>',
    name: 'Sara Al-Ahmad',
    nameAr: 'سارة الأحمد',
    role: {
      id: 'employee',
      name: 'Employee',
      nameAr: 'موظف',
      level: 4,
      permissions: ['profile.*', 'tasks.read', 'messages.*']
    },
    department: 'Marketing',
    departmentAr: 'التسويق',
    avatar: '👩‍💻',
    // Extended profile information
    phone: '+966507654321',
    address: 'جدة، المملكة العربية السعودية',
    addressEn: 'Jeddah, Saudi Arabia',
    birthDate: '1992-08-20',
    nationalId: '2987654321',
    position: 'أخصائي تسويق رقمي',
    positionEn: 'Digital Marketing Specialist',
    manager: 'أحمد الراشد',
    managerEn: 'Ahmed Al-Rashid',
    joinDate: '2023-03-01',
    salary: 8500,
    employeeId: 'EMP004',
    emergencyContact: 'محمد الأحمد - 0501234567',
    emergencyContactEn: 'Mohammed Al-Ahmad - 0501234567',
    education: 'بكالوريوس إدارة أعمال',
    educationEn: 'Bachelor of Business Administration',
    experience: '3 سنوات في التسويق الرقمي',
    experienceEn: '3 years in Digital Marketing',
    permissions: [
      { module: 'profile', actions: ['read', 'write'] },
      { module: 'projects', actions: ['read'] },
      { module: 'tasks', actions: ['read', 'write'] },
      { module: 'messages', actions: ['read', 'write'] },
      { module: 'leave', actions: ['read', 'write'] },
      { module: 'attendance', actions: ['read'] }
    ],
    preferences: {
      language: 'ar',
      theme: 'dark',
      dashboardLayout: ['my_tasks', 'my_schedule', 'team_updates', 'messages'],
      notifications: { email: true, push: true, sms: false }
    },
    lastLogin: '2024-01-16T09:00:00Z',
    isActive: true
  },
  '<EMAIL>': {
    id: '5',
    email: '<EMAIL>',
    name: 'Khalid Al-Otaibi',
    nameAr: 'خالد العتيبي',
    role: {
      id: 'department_manager',
      name: 'Department Manager',
      nameAr: 'مدير القسم',
      level: 3,
      permissions: ['projects.*', 'team.*', 'reports.read']
    },
    department: 'Operations',
    departmentAr: 'العمليات',
    avatar: '👨‍💼',
    permissions: [
      { module: 'projects', actions: ['read', 'write', 'approve'] },
      { module: 'departments', actions: ['read'] },
      { module: 'team', actions: ['read', 'write'] },
      { module: 'analytics', actions: ['read'] },
      { module: 'reports', actions: ['read'] },
      { module: 'tasks', actions: ['read', 'write', 'approve'] },
      { module: 'budget', actions: ['read'] }
    ],
    preferences: {
      language: 'ar',
      theme: 'dark',
      dashboardLayout: ['project_overview', 'team_performance', 'task_management', 'reports'],
      notifications: { email: true, push: true, sms: true }
    },
    lastLogin: '2024-01-16T08:30:00Z',
    isActive: true
  },
  '<EMAIL>': {
    id: '6',
    email: '<EMAIL>',
    name: 'Omar Al-Harbi',
    nameAr: 'عمر الحربي',
    role: {
      id: 'sales_manager',
      name: 'Sales Manager',
      nameAr: 'مدير المبيعات',
      level: 3,
      permissions: ['sales.*', 'customers.*', 'products.read']
    },
    department: 'Sales',
    departmentAr: 'المبيعات',
    avatar: '👨‍💼',
    permissions: [
      { module: 'sales', actions: ['read', 'write', 'approve'] },
      { module: 'customers', actions: ['read', 'write', 'approve'] },
      { module: 'products', actions: ['read', 'write'] },
      { module: 'analytics', actions: ['read'] },
      { module: 'reports', actions: ['read', 'write'] },
      { module: 'leads', actions: ['read', 'write', 'approve'] },
      { module: 'opportunities', actions: ['read', 'write', 'approve'] }
    ],
    preferences: {
      language: 'ar',
      theme: 'dark',
      dashboardLayout: ['sales_overview', 'pipeline', 'targets', 'customer_insights'],
      notifications: { email: true, push: true, sms: true }
    },
    lastLogin: '2024-01-16T09:45:00Z',
    isActive: true
  }
}

// Async thunks
export const loginUser = createAsyncThunk(
  'auth/loginUser',
  async (credentials: { email: string; password: string }, { rejectWithValue }) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      console.log('Login attempt:', credentials.email)

      const user = mockUsers[credentials.email]
      if (!user) {
        console.log('User not found:', credentials.email)
        throw new Error('User not found')
      }

      if (credentials.password !== 'password123') {
        console.log('Invalid password for:', credentials.email)
        throw new Error('Invalid password')
      }

      const token = `mock_token_${user.id}_${Date.now()}`
      localStorage.setItem('token', token)

      console.log('Login successful:', user.email, 'Token:', token)

      return { user, token }
    } catch (error: any) {
      console.error('Login error:', error.message)
      return rejectWithValue(error.message)
    }
  }
)

export const logoutUser = createAsyncThunk(
  'auth/logoutUser',
  async () => {
    localStorage.removeItem('token')
    return null
  }
)

export const verifyToken = createAsyncThunk(
  'auth/verifyToken',
  async (token: string, { rejectWithValue }) => {
    try {
      console.log('Verifying token:', token)

      // Simulate token verification with shorter timeout
      await new Promise(resolve => setTimeout(resolve, 300))

      // Extract user ID from token (mock implementation)
      const tokenParts = token.split('_')
      if (tokenParts.length < 3) {
        console.log('Invalid token format:', token)
        throw new Error('Invalid token format')
      }

      const userId = tokenParts[2]
      const user = Object.values(mockUsers).find(u => u.id === userId)

      if (!user) {
        console.log('User not found for token:', token, 'userId:', userId)
        throw new Error('Invalid token - user not found')
      }

      console.log('Token verification successful:', user.email)
      return user
    } catch (error: any) {
      console.log('Token verification failed:', error.message)
      localStorage.removeItem('token')
      return rejectWithValue(error.message)
    }
  }
)

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null
    },
    updateUserPreferences: (state, action: PayloadAction<Partial<UserPreferences>>) => {
      if (state.user) {
        state.user.preferences = { ...state.user.preferences, ...action.payload }
      }
    },
    resetLoginAttempts: (state) => {
      state.loginAttempts = 0
      state.lastLoginAttempt = null
    }
  },
  extraReducers: (builder) => {
    builder
      // Login
      .addCase(loginUser.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(loginUser.fulfilled, (state, action) => {
        state.isLoading = false
        state.user = action.payload.user
        state.token = action.payload.token
        state.isAuthenticated = true
        state.loginAttempts = 0
        state.lastLoginAttempt = null
      })
      .addCase(loginUser.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload as string
        state.loginAttempts += 1
        state.lastLoginAttempt = Date.now()
      })
      // Logout
      .addCase(logoutUser.pending, (state) => {
        state.isLoading = true
      })
      .addCase(logoutUser.fulfilled, (state) => {
        state.user = null
        state.token = null
        state.isAuthenticated = false
        state.error = null
        state.isLoading = false
      })
      .addCase(logoutUser.rejected, (state) => {
        // Even if logout fails, clear the state
        state.user = null
        state.token = null
        state.isAuthenticated = false
        state.isLoading = false
      })
      // Verify token
      .addCase(verifyToken.pending, (state) => {
        state.isLoading = true
      })
      .addCase(verifyToken.fulfilled, (state, action) => {
        state.isLoading = false
        state.user = action.payload
        state.isAuthenticated = true
      })
      .addCase(verifyToken.rejected, (state) => {
        state.isLoading = false
        state.user = null
        state.token = null
        state.isAuthenticated = false
      })
  },
})

export const { clearError, updateUserPreferences, resetLoginAttempts } = authSlice.actions
export default authSlice.reducer
