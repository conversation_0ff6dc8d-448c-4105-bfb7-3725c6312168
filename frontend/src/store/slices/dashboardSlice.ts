import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import type { PayloadAction } from '@reduxjs/toolkit'

export interface DashboardWidget {
  id: string
  type: string
  title: string
  titleAr: string
  position: { x: number; y: number; w: number; h: number }
  data: any
  isVisible: boolean
  permissions: string[]
}

export interface DashboardLayout {
  id: string
  name: string
  nameAr: string
  widgets: DashboardWidget[]
  isDefault: boolean
  userRole: string
}

interface DashboardState {
  currentLayout: DashboardLayout | null
  availableLayouts: DashboardLayout[]
  widgets: DashboardWidget[]
  isCustomizing: boolean
  isLoading: boolean
  error: string | null
  lastUpdated: number | null
}

const initialState: DashboardState = {
  currentLayout: null,
  availableLayouts: [],
  widgets: [],
  isCustomizing: false,
  isLoading: false,
  error: null,
  lastUpdated: null,
}

// Role-based dashboard layouts
const dashboardLayouts: Record<string, DashboardLayout> = {
  super_admin: {
    id: 'super_admin_dashboard',
    name: 'Super Admin Dashboard',
    nameAr: 'لوحة تحكم المدير الرئيسي',
    isDefault: true,
    userRole: 'super_admin',
    widgets: [
      {
        id: 'system_overview',
        type: 'system_metrics',
        title: 'System Overview',
        titleAr: 'نظرة عامة على النظام',
        position: { x: 0, y: 0, w: 6, h: 3 },
        data: {},
        isVisible: true,
        permissions: ['system.read']
      },
      {
        id: 'user_analytics',
        type: 'analytics',
        title: 'User Analytics',
        titleAr: 'تحليلات المستخدمين',
        position: { x: 6, y: 0, w: 6, h: 3 },
        data: {},
        isVisible: true,
        permissions: ['analytics.read']
      },
      {
        id: 'financial_summary',
        type: 'financial',
        title: 'Financial Summary',
        titleAr: 'الملخص المالي',
        position: { x: 0, y: 3, w: 4, h: 4 },
        data: {},
        isVisible: true,
        permissions: ['finance.read']
      },
      {
        id: 'hr_metrics',
        type: 'hr_stats',
        title: 'HR Metrics',
        titleAr: 'مقاييس الموارد البشرية',
        position: { x: 4, y: 3, w: 4, h: 4 },
        data: {},
        isVisible: true,
        permissions: ['hr.read']
      },
      {
        id: 'project_status',
        type: 'projects',
        title: 'Project Status',
        titleAr: 'حالة المشاريع',
        position: { x: 8, y: 3, w: 4, h: 4 },
        data: {},
        isVisible: true,
        permissions: ['projects.read']
      },
      {
        id: 'recent_activities',
        type: 'activities',
        title: 'Recent Activities',
        titleAr: 'الأنشطة الحديثة',
        position: { x: 0, y: 7, w: 6, h: 3 },
        data: {},
        isVisible: true,
        permissions: ['activities.read']
      },
      {
        id: 'notifications_panel',
        type: 'notifications',
        title: 'Notifications',
        titleAr: 'الإشعارات',
        position: { x: 6, y: 7, w: 6, h: 3 },
        data: {},
        isVisible: true,
        permissions: ['notifications.read']
      }
    ]
  },
  hr_manager: {
    id: 'hr_manager_dashboard',
    name: 'HR Manager Dashboard',
    nameAr: 'لوحة تحكم مدير الموارد البشرية',
    isDefault: true,
    userRole: 'hr_manager',
    widgets: [
      {
        id: 'employee_overview',
        type: 'employee_stats',
        title: 'Employee Overview',
        titleAr: 'نظرة عامة على الموظفين',
        position: { x: 0, y: 0, w: 6, h: 3 },
        data: {},
        isVisible: true,
        permissions: ['hr.read']
      },
      {
        id: 'attendance_summary',
        type: 'attendance',
        title: 'Attendance Summary',
        titleAr: 'ملخص الحضور',
        position: { x: 6, y: 0, w: 6, h: 3 },
        data: {},
        isVisible: true,
        permissions: ['attendance.read']
      },
      {
        id: 'leave_requests',
        type: 'leave_management',
        title: 'Leave Requests',
        titleAr: 'طلبات الإجازة',
        position: { x: 0, y: 3, w: 6, h: 4 },
        data: {},
        isVisible: true,
        permissions: ['leave.read']
      },
      {
        id: 'payroll_summary',
        type: 'payroll',
        title: 'Payroll Summary',
        titleAr: 'ملخص الرواتب',
        position: { x: 6, y: 3, w: 6, h: 4 },
        data: {},
        isVisible: true,
        permissions: ['payroll.read']
      },
      {
        id: 'performance_metrics',
        type: 'performance',
        title: 'Performance Metrics',
        titleAr: 'مقاييس الأداء',
        position: { x: 0, y: 7, w: 12, h: 3 },
        data: {},
        isVisible: true,
        permissions: ['performance.read']
      }
    ]
  },
  finance_manager: {
    id: 'finance_manager_dashboard',
    name: 'Finance Manager Dashboard',
    nameAr: 'لوحة تحكم مدير المالية',
    isDefault: true,
    userRole: 'finance_manager',
    widgets: [
      {
        id: 'financial_overview',
        type: 'financial_summary',
        title: 'Financial Overview',
        titleAr: 'النظرة المالية العامة',
        position: { x: 0, y: 0, w: 8, h: 4 },
        data: {},
        isVisible: true,
        permissions: ['finance.read']
      },
      {
        id: 'budget_status',
        type: 'budget_tracking',
        title: 'Budget Status',
        titleAr: 'حالة الميزانية',
        position: { x: 8, y: 0, w: 4, h: 4 },
        data: {},
        isVisible: true,
        permissions: ['budget.read']
      },
      {
        id: 'expense_analysis',
        type: 'expense_analytics',
        title: 'Expense Analysis',
        titleAr: 'تحليل المصروفات',
        position: { x: 0, y: 4, w: 6, h: 3 },
        data: {},
        isVisible: true,
        permissions: ['expenses.read']
      },
      {
        id: 'revenue_trends',
        type: 'revenue_chart',
        title: 'Revenue Trends',
        titleAr: 'اتجاهات الإيرادات',
        position: { x: 6, y: 4, w: 6, h: 3 },
        data: {},
        isVisible: true,
        permissions: ['revenue.read']
      },
      {
        id: 'pending_approvals',
        type: 'approvals',
        title: 'Pending Approvals',
        titleAr: 'الموافقات المعلقة',
        position: { x: 0, y: 7, w: 12, h: 3 },
        data: {},
        isVisible: true,
        permissions: ['approvals.read']
      }
    ]
  },
  department_manager: {
    id: 'department_manager_dashboard',
    name: 'Department Manager Dashboard',
    nameAr: 'لوحة تحكم مدير القسم',
    isDefault: true,
    userRole: 'department_manager',
    widgets: [
      {
        id: 'team_overview',
        type: 'team_stats',
        title: 'Team Overview',
        titleAr: 'نظرة عامة على الفريق',
        position: { x: 0, y: 0, w: 6, h: 3 },
        data: {},
        isVisible: true,
        permissions: ['team.read']
      },
      {
        id: 'project_progress',
        type: 'project_tracking',
        title: 'Project Progress',
        titleAr: 'تقدم المشاريع',
        position: { x: 6, y: 0, w: 6, h: 3 },
        data: {},
        isVisible: true,
        permissions: ['projects.read']
      },
      {
        id: 'task_management',
        type: 'task_board',
        title: 'Task Management',
        titleAr: 'إدارة المهام',
        position: { x: 0, y: 3, w: 8, h: 4 },
        data: {},
        isVisible: true,
        permissions: ['tasks.read']
      },
      {
        id: 'team_performance',
        type: 'performance_chart',
        title: 'Team Performance',
        titleAr: 'أداء الفريق',
        position: { x: 8, y: 3, w: 4, h: 4 },
        data: {},
        isVisible: true,
        permissions: ['performance.read']
      },
      {
        id: 'upcoming_deadlines',
        type: 'deadlines',
        title: 'Upcoming Deadlines',
        titleAr: 'المواعيد النهائية القادمة',
        position: { x: 0, y: 7, w: 12, h: 3 },
        data: {},
        isVisible: true,
        permissions: ['deadlines.read']
      }
    ]
  },
  employee: {
    id: 'employee_dashboard',
    name: 'Employee Dashboard',
    nameAr: 'لوحة تحكم الموظف',
    isDefault: true,
    userRole: 'employee',
    widgets: [
      {
        id: 'my_profile',
        type: 'profile_summary',
        title: 'My Profile',
        titleAr: 'ملفي الشخصي',
        position: { x: 0, y: 0, w: 4, h: 3 },
        data: {},
        isVisible: true,
        permissions: ['profile.read']
      },
      {
        id: 'my_tasks',
        type: 'personal_tasks',
        title: 'My Tasks',
        titleAr: 'مهامي',
        position: { x: 4, y: 0, w: 4, h: 3 },
        data: {},
        isVisible: true,
        permissions: ['tasks.read']
      },
      {
        id: 'my_schedule',
        type: 'personal_calendar',
        title: 'My Schedule',
        titleAr: 'جدولي',
        position: { x: 8, y: 0, w: 4, h: 3 },
        data: {},
        isVisible: true,
        permissions: ['calendar.read']
      },
      {
        id: 'attendance_record',
        type: 'personal_attendance',
        title: 'My Attendance',
        titleAr: 'حضوري',
        position: { x: 0, y: 3, w: 6, h: 4 },
        data: {},
        isVisible: true,
        permissions: ['attendance.read']
      },
      {
        id: 'leave_balance',
        type: 'leave_summary',
        title: 'Leave Balance',
        titleAr: 'رصيد الإجازات',
        position: { x: 6, y: 3, w: 6, h: 4 },
        data: {},
        isVisible: true,
        permissions: ['leave.read']
      },
      {
        id: 'team_updates',
        type: 'team_news',
        title: 'Team Updates',
        titleAr: 'تحديثات الفريق',
        position: { x: 0, y: 7, w: 12, h: 3 },
        data: {},
        isVisible: true,
        permissions: ['team.read']
      }
    ]
  }
}

export const fetchDashboardLayout = createAsyncThunk(
  'dashboard/fetchLayout',
  async (userRole: string, { rejectWithValue }) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 500))

      const layout = dashboardLayouts[userRole]
      if (!layout) {
        throw new Error('Dashboard layout not found for role')
      }

      return layout
    } catch (error: any) {
      return rejectWithValue(error.message)
    }
  }
)

export const updateDashboardLayout = createAsyncThunk(
  'dashboard/updateLayout',
  async (layout: DashboardLayout, { rejectWithValue }) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 1000))
      return layout
    } catch (error: any) {
      return rejectWithValue(error.message)
    }
  }
)

export const fetchWidgetData = createAsyncThunk(
  'dashboard/fetchWidgetData',
  async (widgetId: string, { rejectWithValue }) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 300))

      // Mock widget data
      const mockData = {
        system_overview: {
          uptime: '99.9%',
          activeUsers: 142,
          systemLoad: 68,
          storage: 75
        },
        employee_overview: {
          totalEmployees: 1247,
          presentToday: 1156,
          onLeave: 45,
          newHires: 12
        },
        financial_overview: {
          revenue: 2500000,
          expenses: 1800000,
          profit: 700000,
          growth: 15.3
        }
      }

      return { widgetId, data: mockData[widgetId as keyof typeof mockData] || {} }
    } catch (error: any) {
      return rejectWithValue(error.message)
    }
  }
)

const dashboardSlice = createSlice({
  name: 'dashboard',
  initialState,
  reducers: {
    setCustomizing: (state, action: PayloadAction<boolean>) => {
      state.isCustomizing = action.payload
    },
    updateWidgetPosition: (state, action: PayloadAction<{ widgetId: string; position: any }>) => {
      if (state.currentLayout) {
        const widget = state.currentLayout.widgets.find(w => w.id === action.payload.widgetId)
        if (widget) {
          widget.position = action.payload.position
        }
      }
    },
    toggleWidgetVisibility: (state, action: PayloadAction<string>) => {
      if (state.currentLayout) {
        const widget = state.currentLayout.widgets.find(w => w.id === action.payload)
        if (widget) {
          widget.isVisible = !widget.isVisible
        }
      }
    },
    addWidget: (state, action: PayloadAction<DashboardWidget>) => {
      if (state.currentLayout) {
        state.currentLayout.widgets.push(action.payload)
      }
    },
    removeWidget: (state, action: PayloadAction<string>) => {
      if (state.currentLayout) {
        state.currentLayout.widgets = state.currentLayout.widgets.filter(w => w.id !== action.payload)
      }
    },
    clearError: (state) => {
      state.error = null
    }
  },
  extraReducers: (builder) => {
    builder
      // Fetch layout
      .addCase(fetchDashboardLayout.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(fetchDashboardLayout.fulfilled, (state, action) => {
        state.isLoading = false
        state.currentLayout = action.payload
        state.lastUpdated = Date.now()
      })
      .addCase(fetchDashboardLayout.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload as string
      })
      // Update layout
      .addCase(updateDashboardLayout.fulfilled, (state, action) => {
        state.currentLayout = action.payload
        state.lastUpdated = Date.now()
      })
      // Fetch widget data
      .addCase(fetchWidgetData.fulfilled, (state, action) => {
        if (state.currentLayout) {
          const widget = state.currentLayout.widgets.find(w => w.id === action.payload.widgetId)
          if (widget) {
            widget.data = action.payload.data
          }
        }
      })
  },
})

export const {
  setCustomizing,
  updateWidgetPosition,
  toggleWidgetVisibility,
  addWidget,
  removeWidget,
  clearError
} = dashboardSlice.actions

export default dashboardSlice.reducer
