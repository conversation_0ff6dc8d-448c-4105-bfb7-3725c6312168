import React from 'react';
/**
 * Hierarchical Access Redux Slice
 * Centralized state management for hierarchical access data to prevent duplicate API calls
 */

import { createSlice, createAsyncThunk, PayloadAction, createSelector } from '@reduxjs/toolkit'
import { 
  hierarchicalAccessService, 
  HierarchicalAccessInfo, 
  RoleBasedKPIFilter,
  AccessSummary,
  HierarchicalPath
} from '../../services/hierarchicalAccessService'
import logger from '../../utils/logger'

interface HierarchicalAccessState {
  accessInfo: HierarchicalAccessInfo | null
  roleBasedKPIFilters: RoleBasedKPIFilter | null
  loading: boolean
  error: string | null
  lastFetch: number
  isInitialized: boolean
}

const initialState: HierarchicalAccessState = {
  accessInfo: null,
  roleBasedKPIFilters: null,
  loading: false,
  error: null,
  lastFetch: 0,
  isInitialized: false
}

const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

// Async thunk to fetch hierarchical access info
export const fetchHierarchicalAccessInfo = createAsyncThunk(
  'hierarchicalAccess/fetchAccessInfo',
  async (forceRefresh: boolean = false, { getState, rejectWithValue }) => {
    try {
      const state = getState() as { hierarchicalAccess: HierarchicalAccessState }
      const now = Date.now()

      // Prevent multiple simultaneous requests
      if (state.hierarchicalAccess.loading && !forceRefresh) {
        logger.info('hierarchicalAccessSlice', 'Request already in progress, skipping duplicate call')
        return rejectWithValue('Request already in progress')
      }

      // Check if we have cached data and it's still valid
      if (!forceRefresh &&
          state.hierarchicalAccess.accessInfo &&
          (now - state.hierarchicalAccess.lastFetch) < CACHE_DURATION) {
        logger.info('hierarchicalAccessSlice', 'Using cached data', {
          cacheAge: now - state.hierarchicalAccess.lastFetch
        })
        return {
          accessInfo: state.hierarchicalAccess.accessInfo,
          roleBasedKPIFilters: state.hierarchicalAccess.roleBasedKPIFilters,
          fromCache: true
        }
      }

      // Try to get cached data from service first
      if (!forceRefresh) {
        const cachedInfo = hierarchicalAccessService.getCachedAccessInfo()
        if (cachedInfo) {
          const kpiFilters = await hierarchicalAccessService.getRoleBasedKPIFilters()
          return {
            accessInfo: cachedInfo,
            roleBasedKPIFilters: kpiFilters,
            fromCache: true
          }
        }
      }

      // Fetch fresh data
      const [accessData, kpiFilters] = await Promise.all([
        hierarchicalAccessService.getHierarchicalAccessInfo(forceRefresh),
        hierarchicalAccessService.getRoleBasedKPIFilters()
      ])

      logger.info('hierarchicalAccessSlice', 'Access info fetched successfully', {
        role: accessData.user_role,
        employeesCount: accessData.accessible_data.employees_count,
        isManager: accessData.access_summary.is_manager,
        fromCache: false
      })

      return {
        accessInfo: accessData,
        roleBasedKPIFilters: kpiFilters,
        fromCache: false
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch hierarchical access info'
      logger.error('hierarchicalAccessSlice', 'Error fetching access info:', error)
      return rejectWithValue(errorMessage)
    }
  }
)

// Async thunk for access checks
export const checkEmployeeAccess = createAsyncThunk(
  'hierarchicalAccess/checkEmployeeAccess',
  async (employeeId: number, { rejectWithValue }) => {
    try {
      return await hierarchicalAccessService.canAccessEmployeeData(employeeId)
    } catch (error) {
      logger.error('hierarchicalAccessSlice', 'Error checking employee access:', error)
      return rejectWithValue(false)
    }
  }
)

export const checkDepartmentAccess = createAsyncThunk(
  'hierarchicalAccess/checkDepartmentAccess',
  async (departmentId: number, { rejectWithValue }) => {
    try {
      return await hierarchicalAccessService.canAccessDepartmentData(departmentId)
    } catch (error) {
      logger.error('hierarchicalAccessSlice', 'Error checking department access:', error)
      return rejectWithValue(false)
    }
  }
)

export const checkProjectAccess = createAsyncThunk(
  'hierarchicalAccess/checkProjectAccess',
  async (projectId: number, { rejectWithValue }) => {
    try {
      return await hierarchicalAccessService.canAccessProjectData(projectId)
    } catch (error) {
      logger.error('hierarchicalAccessSlice', 'Error checking project access:', error)
      return rejectWithValue(false)
    }
  }
)

const hierarchicalAccessSlice = createSlice({
  name: 'hierarchicalAccess',
  initialState,
  reducers: {
    clearAccessData: (state) => {
      state.accessInfo = null
      state.roleBasedKPIFilters = null
      state.error = null
      state.lastFetch = 0
      state.isInitialized = false
    },
    clearError: (state) => {
      state.error = null
    },
    clearCache: (state) => {
      hierarchicalAccessService.clearCache()
      state.accessInfo = null
      state.roleBasedKPIFilters = null
      state.lastFetch = 0
    },
    setInitialized: (state, action: PayloadAction<boolean>) => {
      state.isInitialized = action.payload
    }
  },
  extraReducers: (builder) => {
    builder
      // Fetch hierarchical access info
      .addCase(fetchHierarchicalAccessInfo.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(fetchHierarchicalAccessInfo.fulfilled, (state, action) => {
        state.loading = false
        state.accessInfo = action.payload.accessInfo
        state.roleBasedKPIFilters = action.payload.roleBasedKPIFilters
        state.lastFetch = Date.now()
        state.isInitialized = true
        state.error = null
      })
      .addCase(fetchHierarchicalAccessInfo.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
        state.isInitialized = true
      })
  }
})

export const { clearAccessData, clearError, clearCache, setInitialized } = hierarchicalAccessSlice.actions

export default hierarchicalAccessSlice.reducer

// Base selector
export const selectHierarchicalAccess = (state: { hierarchicalAccess: HierarchicalAccessState }) => state.hierarchicalAccess

// Memoized selectors to prevent unnecessary re-renders
export const selectAccessInfo = createSelector(
  [selectHierarchicalAccess],
  (hierarchicalAccess) => hierarchicalAccess.accessInfo
)

export const selectAccessSummary = createSelector(
  [selectAccessInfo],
  (accessInfo): AccessSummary | null => accessInfo?.access_summary || null
)

export const selectHierarchicalPath = createSelector(
  [selectAccessInfo],
  (accessInfo): HierarchicalPath[] => accessInfo?.hierarchical_path || []
)

export const selectKpiCategories = createSelector(
  [selectAccessInfo],
  (accessInfo): string[] => accessInfo?.kpi_categories || []
)

export const selectCanAccessAllData = createSelector(
  [selectAccessSummary],
  (accessSummary): boolean => accessSummary?.can_access_all_data || false
)

export const selectIsManager = createSelector(
  [selectAccessSummary],
  (accessSummary): boolean => accessSummary?.is_manager || false
)

export const selectHasHierarchicalAccess = createSelector(
  [selectAccessSummary],
  (accessSummary): boolean => accessSummary?.has_hierarchical_access || false
)

export const selectIsDepartmentScoped = createSelector(
  [selectAccessSummary],
  (accessSummary): boolean => accessSummary?.department_scoped || false
)

export const selectIsProjectScoped = createSelector(
  [selectAccessSummary],
  (accessSummary): boolean => accessSummary?.project_scoped || false
)

export const selectAccessibleEmployees = createSelector(
  [selectAccessInfo],
  (accessInfo) => accessInfo?.accessible_data.employees || []
)

export const selectAccessibleDepartments = createSelector(
  [selectAccessInfo],
  (accessInfo) => accessInfo?.accessible_data.departments || []
)

export const selectAccessibleProjects = createSelector(
  [selectAccessInfo],
  (accessInfo) => accessInfo?.accessible_data.projects || []
)

export const selectRoleBasedKPIFilters = createSelector(
  [selectHierarchicalAccess],
  (hierarchicalAccess) => hierarchicalAccess.roleBasedKPIFilters
)
