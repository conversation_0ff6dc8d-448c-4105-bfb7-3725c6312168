import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import type { PayloadAction } from '@reduxjs/toolkit'

export interface Notification {
  id: string
  type: 'info' | 'warning' | 'success' | 'error' | 'urgent'
  category: 'hr' | 'finance' | 'project' | 'system' | 'communication'
  title: string
  titleAr: string
  message: string
  messageAr: string
  timestamp: string
  isRead: boolean
  isStarred: boolean
  actionRequired: boolean
  priority: 'low' | 'medium' | 'high' | 'urgent'
  relatedUser?: string
  relatedEntity?: string
  actions?: Array<{
    id: string
    label: string
    labelAr: string
    action: string
    variant: 'primary' | 'secondary' | 'danger'
  }>
  metadata?: Record<string, any>
}

interface NotificationState {
  notifications: Notification[]
  unreadCount: number
  filter: 'all' | 'unread' | 'starred' | 'urgent' | 'actionRequired'
  category: 'all' | 'hr' | 'finance' | 'project' | 'system' | 'communication'
  isLoading: boolean
  error: string | null
  lastFetch: number | null
}

const initialState: NotificationState = {
  notifications: [],
  unreadCount: 0,
  filter: 'all',
  category: 'all',
  isLoading: false,
  error: null,
  lastFetch: null,
}

// Mock notifications based on user role
const generateMockNotifications = (userRole: string): Notification[] => {
  const baseNotifications: Notification[] = [
    {
      id: '1',
      type: 'urgent',
      category: 'hr',
      title: 'Urgent Leave Request',
      titleAr: 'طلب إجازة عاجل',
      message: 'Ahmed Mohammed submitted an urgent leave request requiring immediate approval',
      messageAr: 'أحمد محمد قدم طلب إجازة عاجلة تحتاج موافقة فورية',
      timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
      isRead: false,
      isStarred: true,
      actionRequired: true,
      priority: 'urgent',
      relatedUser: 'أحمد محمد',
      relatedEntity: 'طلب إجازة #1234',
      actions: [
        { id: 'approve', label: 'Approve', labelAr: 'موافقة', action: 'approve', variant: 'primary' },
        { id: 'reject', label: 'Reject', labelAr: 'رفض', action: 'reject', variant: 'danger' },
        { id: 'view', label: 'View Details', labelAr: 'عرض التفاصيل', action: 'view', variant: 'secondary' }
      ]
    },
    {
      id: '2',
      type: 'warning',
      category: 'finance',
      title: 'Budget Exceeded',
      titleAr: 'تجاوز الميزانية',
      message: 'Mobile app development project has exceeded 90% of allocated budget',
      messageAr: 'مشروع تطوير التطبيق تجاوز 90% من الميزانية المخصصة',
      timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
      isRead: false,
      isStarred: false,
      actionRequired: true,
      priority: 'high',
      relatedEntity: 'مشروع تطوير التطبيق',
      actions: [
        { id: 'view', label: 'View Project', labelAr: 'عرض المشروع', action: 'view', variant: 'primary' },
        { id: 'adjust', label: 'Adjust Budget', labelAr: 'تعديل الميزانية', action: 'adjust', variant: 'secondary' }
      ]
    },
    {
      id: '3',
      type: 'success',
      category: 'project',
      title: 'Task Completed',
      titleAr: 'اكتمال المهمة',
      message: 'Fatima Ali successfully completed "UI Design" task',
      messageAr: 'فاطمة علي أكملت مهمة "تصميم واجهة المستخدم" بنجاح',
      timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
      isRead: true,
      isStarred: false,
      actionRequired: false,
      priority: 'medium',
      relatedUser: 'فاطمة علي',
      relatedEntity: 'تصميم واجهة المستخدم'
    },
    {
      id: '4',
      type: 'info',
      category: 'system',
      title: 'System Update',
      titleAr: 'تحديث النظام',
      message: 'System successfully updated to version 2.1.0 with new features',
      messageAr: 'تم تحديث النظام إلى الإصدار 2.1.0 بنجاح مع ميزات جديدة',
      timestamp: new Date(Date.now() - 60 * 60 * 1000).toISOString(),
      isRead: true,
      isStarred: false,
      actionRequired: false,
      priority: 'low',
      relatedEntity: 'تحديث النظام v2.1.0'
    },
    {
      id: '5',
      type: 'info',
      category: 'communication',
      title: 'New Message',
      titleAr: 'رسالة جديدة',
      message: 'Mohammed Hassan sent a new message in development team chat',
      messageAr: 'محمد حسن أرسل رسالة جديدة في محادثة فريق التطوير',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      isRead: false,
      isStarred: false,
      actionRequired: false,
      priority: 'medium',
      relatedUser: 'محمد حسن',
      actions: [
        { id: 'reply', label: 'Reply', labelAr: 'رد', action: 'reply', variant: 'primary' },
        { id: 'view', label: 'View Chat', labelAr: 'عرض المحادثة', action: 'view', variant: 'secondary' }
      ]
    }
  ]

  // Filter notifications based on user role
  switch (userRole) {
    case 'super_admin':
      return baseNotifications
    case 'hr_manager':
      return baseNotifications.filter(n => ['hr', 'system', 'communication'].includes(n.category))
    case 'finance_manager':
      return baseNotifications.filter(n => ['finance', 'system', 'communication'].includes(n.category))
    case 'department_manager':
      return baseNotifications.filter(n => ['project', 'communication', 'system'].includes(n.category))
    case 'employee':
      return baseNotifications.filter(n => ['communication', 'system'].includes(n.category) && !n.actionRequired)
    default:
      return baseNotifications.filter(n => n.category === 'communication')
  }
}

export const fetchNotifications = createAsyncThunk(
  'notifications/fetchNotifications',
  async (userRole: string, { rejectWithValue }) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 500))

      const notifications = generateMockNotifications(userRole)
      return notifications
    } catch (error: any) {
      return rejectWithValue(error.message)
    }
  }
)

export const markAsRead = createAsyncThunk(
  'notifications/markAsRead',
  async (notificationId: string, { rejectWithValue }) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 200))
      return notificationId
    } catch (error: any) {
      return rejectWithValue(error.message)
    }
  }
)

export const markAllAsRead = createAsyncThunk(
  'notifications/markAllAsRead',
  async (_, { rejectWithValue }) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 300))
      return true
    } catch (error: any) {
      return rejectWithValue(error.message)
    }
  }
)

export const toggleStar = createAsyncThunk(
  'notifications/toggleStar',
  async (notificationId: string, { rejectWithValue }) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 200))
      return notificationId
    } catch (error: any) {
      return rejectWithValue(error.message)
    }
  }
)

export const performNotificationAction = createAsyncThunk(
  'notifications/performAction',
  async ({ notificationId, actionId }: { notificationId: string; actionId: string }, { rejectWithValue }) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 1000))
      return { notificationId, actionId }
    } catch (error: any) {
      return rejectWithValue(error.message)
    }
  }
)

const notificationSlice = createSlice({
  name: 'notifications',
  initialState,
  reducers: {
    setFilter: (state, action: PayloadAction<typeof state.filter>) => {
      state.filter = action.payload
    },
    setCategory: (state, action: PayloadAction<typeof state.category>) => {
      state.category = action.payload
    },
    addNotification: (state, action: PayloadAction<Notification>) => {
      state.notifications.unshift(action.payload)
      if (!action.payload.isRead) {
        state.unreadCount += 1
      }
    },
    removeNotification: (state, action: PayloadAction<string>) => {
      const notification = state.notifications.find(n => n.id === action.payload)
      if (notification && !notification.isRead) {
        state.unreadCount -= 1
      }
      state.notifications = state.notifications.filter(n => n.id !== action.payload)
    },
    clearError: (state) => {
      state.error = null
    }
  },
  extraReducers: (builder) => {
    builder
      // Fetch notifications
      .addCase(fetchNotifications.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(fetchNotifications.fulfilled, (state, action) => {
        state.isLoading = false
        state.notifications = action.payload
        state.unreadCount = action.payload.filter(n => !n.isRead).length
        state.lastFetch = Date.now()
      })
      .addCase(fetchNotifications.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload as string
      })
      // Mark as read
      .addCase(markAsRead.fulfilled, (state, action) => {
        const notification = state.notifications.find(n => n.id === action.payload)
        if (notification && !notification.isRead) {
          notification.isRead = true
          state.unreadCount -= 1
        }
      })
      // Mark all as read
      .addCase(markAllAsRead.fulfilled, (state) => {
        state.notifications.forEach(n => n.isRead = true)
        state.unreadCount = 0
      })
      // Toggle star
      .addCase(toggleStar.fulfilled, (state, action) => {
        const notification = state.notifications.find(n => n.id === action.payload)
        if (notification) {
          notification.isStarred = !notification.isStarred
        }
      })
      // Perform action
      .addCase(performNotificationAction.fulfilled, (state, action) => {
        const { notificationId, actionId } = action.payload
        const notification = state.notifications.find(n => n.id === notificationId)
        if (notification) {
          notification.actionRequired = false
          if (!notification.isRead) {
            notification.isRead = true
            state.unreadCount -= 1
          }
        }
      })
  },
})

export const { setFilter, setCategory, addNotification, removeNotification, clearError } = notificationSlice.actions
export default notificationSlice.reducer
