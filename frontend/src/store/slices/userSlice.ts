import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import type { PayloadAction } from '@reduxjs/toolkit'

interface UserProfile {
  personalInfo: {
    firstName: string
    lastName: string
    firstNameAr: string
    lastNameAr: string
    email: string
    phone: string
    dateOfBirth: string
    nationality: string
    nationalId: string
    address: string
    emergencyContact: {
      name: string
      phone: string
      relationship: string
    }
  }
  workInfo: {
    employeeId: string
    department: string
    position: string
    positionAr: string
    manager: string
    startDate: string
    workLocation: string
    workSchedule: string
    salary: number
    benefits: string[]
  }
  documents: {
    id: string
    name: string
    type: string
    uploadDate: string
    url: string
  }[]
}

interface UserStats {
  attendance: {
    present: number
    absent: number
    late: number
    overtime: number
  }
  leave: {
    available: number
    used: number
    pending: number
  }
  performance: {
    score: number
    goals: number
    achievements: number
  }
  tasks: {
    completed: number
    pending: number
    overdue: number
  }
}

interface UserState {
  profile: UserProfile | null
  stats: UserStats | null
  recentActivities: any[]
  quickActions: any[]
  isLoading: boolean
  error: string | null
}

const initialState: UserState = {
  profile: null,
  stats: null,
  recentActivities: [],
  quickActions: [],
  isLoading: false,
  error: null,
}

// Mock user profiles for different roles
const mockProfiles: Record<string, UserProfile> = {
  '1': { // Super Admin
    personalInfo: {
      firstName: 'Ahmed',
      lastName: 'Al-Rashid',
      firstNameAr: 'أحمد',
      lastNameAr: 'الراشد',
      email: '<EMAIL>',
      phone: '+966501234567',
      dateOfBirth: '1985-03-15',
      nationality: 'Saudi',
      nationalId: '1234567890',
      address: 'Riyadh, Saudi Arabia',
      emergencyContact: {
        name: 'Omar Al-Rashid',
        phone: '+966507654321',
        relationship: 'Brother'
      }
    },
    workInfo: {
      employeeId: 'EMP001',
      department: 'Information Technology',
      position: 'Chief Technology Officer',
      positionAr: 'مدير التقنية التنفيذي',
      manager: 'CEO',
      startDate: '2020-01-15',
      workLocation: 'Head Office - Riyadh',
      workSchedule: 'Full Time',
      salary: 25000,
      benefits: ['Health Insurance', 'Car Allowance', 'Housing Allowance']
    },
    documents: [
      { id: '1', name: 'National ID', type: 'identification', uploadDate: '2024-01-01', url: '#' },
      { id: '2', name: 'Contract', type: 'contract', uploadDate: '2024-01-01', url: '#' }
    ]
  },
  '2': { // HR Manager
    personalInfo: {
      firstName: 'Fatima',
      lastName: 'Al-Zahra',
      firstNameAr: 'فاطمة',
      lastNameAr: 'الزهراء',
      email: '<EMAIL>',
      phone: '+966502345678',
      dateOfBirth: '1988-07-22',
      nationality: 'Saudi',
      nationalId: '**********',
      address: 'Riyadh, Saudi Arabia',
      emergencyContact: {
        name: 'Aisha Al-Zahra',
        phone: '+966508765432',
        relationship: 'Sister'
      }
    },
    workInfo: {
      employeeId: 'EMP002',
      department: 'Human Resources',
      position: 'HR Manager',
      positionAr: 'مدير الموارد البشرية',
      manager: 'Ahmed Al-Rashid',
      startDate: '2021-03-01',
      workLocation: 'Head Office - Riyadh',
      workSchedule: 'Full Time',
      salary: 18000,
      benefits: ['Health Insurance', 'Transportation Allowance']
    },
    documents: [
      { id: '1', name: 'National ID', type: 'identification', uploadDate: '2024-01-01', url: '#' },
      { id: '2', name: 'Contract', type: 'contract', uploadDate: '2024-01-01', url: '#' }
    ]
  }
}

const mockStats: Record<string, UserStats> = {
  '1': {
    attendance: { present: 22, absent: 0, late: 1, overtime: 15 },
    leave: { available: 30, used: 5, pending: 0 },
    performance: { score: 95, goals: 10, achievements: 9 },
    tasks: { completed: 45, pending: 8, overdue: 1 }
  },
  '2': {
    attendance: { present: 21, absent: 1, late: 2, overtime: 8 },
    leave: { available: 30, used: 8, pending: 1 },
    performance: { score: 88, goals: 8, achievements: 7 },
    tasks: { completed: 32, pending: 12, overdue: 2 }
  }
}

export const fetchUserProfile = createAsyncThunk(
  'user/fetchProfile',
  async (userId: string, { rejectWithValue }) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 500))

      const profile = mockProfiles[userId]
      if (!profile) {
        throw new Error('Profile not found')
      }

      return profile
    } catch (error: any) {
      return rejectWithValue(error.message)
    }
  }
)

export const fetchUserStats = createAsyncThunk(
  'user/fetchStats',
  async (userId: string, { rejectWithValue }) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 300))

      const stats = mockStats[userId]
      if (!stats) {
        throw new Error('Stats not found')
      }

      return stats
    } catch (error: any) {
      return rejectWithValue(error.message)
    }
  }
)

export const updateUserProfile = createAsyncThunk(
  'user/updateProfile',
  async (profileData: Partial<UserProfile>, { rejectWithValue }) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 1000))
      return profileData
    } catch (error: any) {
      return rejectWithValue(error.message)
    }
  }
)

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    clearUserError: (state) => {
      state.error = null
    },
    addRecentActivity: (state, action) => {
      state.recentActivities.unshift(action.payload)
      if (state.recentActivities.length > 10) {
        state.recentActivities = state.recentActivities.slice(0, 10)
      }
    },
    updateQuickActions: (state, action) => {
      state.quickActions = action.payload
    }
  },
  extraReducers: (builder) => {
    builder
      // Fetch profile
      .addCase(fetchUserProfile.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(fetchUserProfile.fulfilled, (state, action) => {
        state.isLoading = false
        state.profile = action.payload
      })
      .addCase(fetchUserProfile.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload as string
      })
      // Fetch stats
      .addCase(fetchUserStats.fulfilled, (state, action) => {
        state.stats = action.payload
      })
      // Update profile
      .addCase(updateUserProfile.pending, (state) => {
        state.isLoading = true
      })
      .addCase(updateUserProfile.fulfilled, (state, action) => {
        state.isLoading = false
        if (state.profile) {
          state.profile = { ...state.profile, ...action.payload }
        }
      })
      .addCase(updateUserProfile.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload as string
      })
  },
})

export const { clearUserError, addRecentActivity, updateQuickActions } = userSlice.actions
export default userSlice.reducer
