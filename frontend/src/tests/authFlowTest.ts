/**
 * Frontend Authentication Flow Test
 * Tests the integration between frontend auth service and httpOnly cookies
 */

import { AuthService } from '../services/authService'
import { apiClient } from '../services/api'

interface TestResult {
  name: string
  passed: boolean
  message: string
  duration: number
}

class FrontendAuthTester {
  private authService: AuthService
  private results: TestResult[] = []

  constructor() {
    this.authService = new AuthService()
  }

  private log(message: string, level: 'INFO' | 'WARN' | 'ERROR' | 'SUCCESS' = 'INFO') {
    const timestamp = new Date().toLocaleTimeString()
    console.log(`[${timestamp}] ${level}: ${message}`)
  }

  private async runTest(
    name: string,
    testFn: () => Promise<boolean>,
    expectedMessage?: string
  ): Promise<TestResult> {
    const startTime = Date.now()
    this.log(`Running test: ${name}`)

    try {
      const passed = await testFn()
      const duration = Date.now() - startTime
      const message = expectedMessage || (passed ? 'Test passed' : 'Test failed')

      const result: TestResult = { name, passed, message, duration }
      this.results.push(result)

      this.log(`${name}: ${passed ? 'PASS' : 'FAIL'} (${duration}ms)`, passed ? 'SUCCESS' : 'ERROR')
      return result
    } catch (error) {
      const duration = Date.now() - startTime
      const message = `Test failed with error: ${error instanceof Error ? error.message : String(error)}`
      
      const result: TestResult = { name, passed: false, message, duration }
      this.results.push(result)

      this.log(`${name}: FAIL (${duration}ms) - ${message}`, 'ERROR')
      return result
    }
  }

  async testLoginFlow(username: string, password: string): Promise<boolean> {
    try {
      // Clear any existing auth state
      localStorage.removeItem('access_token')
      localStorage.removeItem('refresh_token')

      // Attempt login
      const authResponse = await this.authService.login({ username, password })

      // Verify response structure
      if (!authResponse.user || !authResponse.access_token) {
        this.log('Login response missing required fields', 'ERROR')
        return false
      }

      // Verify localStorage tokens are set (hybrid approach)
      const hasLocalStorageToken = !!localStorage.getItem('access_token')
      if (!hasLocalStorageToken) {
        this.log('localStorage token not set after login', 'WARN')
      }

      // Verify cookies are set (check document.cookie)
      const hasCookieToken = document.cookie.includes('access_token')
      if (!hasCookieToken) {
        this.log('HttpOnly cookie not detected after login', 'WARN')
      }

      this.log(`Login successful - User: ${authResponse.user.username}`)
      return true
    } catch (error) {
      this.log(`Login failed: ${error instanceof Error ? error.message : String(error)}`, 'ERROR')
      return false
    }
  }

  async testTokenVerification(): Promise<boolean> {
    try {
      const user = await this.authService.verifyToken()
      
      if (!user || !user.id) {
        this.log('Token verification returned invalid user data', 'ERROR')
        return false
      }

      this.log(`Token verification successful - User: ${user.username}`)
      return true
    } catch (error) {
      this.log(`Token verification failed: ${error instanceof Error ? error.message : String(error)}`, 'ERROR')
      return false
    }
  }

  async testApiClientWithCookies(): Promise<boolean> {
    try {
      // Test that API client can make authenticated requests using cookies
      const response = await apiClient.get('/auth/user/')
      
      if (!response.data || !response.data.id) {
        this.log('API client request returned invalid data', 'ERROR')
        return false
      }

      this.log(`API client authenticated request successful - User: ${response.data.username}`)
      return true
    } catch (error) {
      this.log(`API client request failed: ${error instanceof Error ? error.message : String(error)}`, 'ERROR')
      return false
    }
  }

  async testAuthStateSync(): Promise<boolean> {
    try {
      const user = await this.authService.syncAuthState()
      
      if (!user || !user.id) {
        this.log('Auth state sync returned no user', 'ERROR')
        return false
      }

      this.log(`Auth state sync successful - User: ${user.username}`)
      return true
    } catch (error) {
      this.log(`Auth state sync failed: ${error instanceof Error ? error.message : String(error)}`, 'ERROR')
      return false
    }
  }

  async testLogoutFlow(): Promise<boolean> {
    try {
      await this.authService.logout()

      // Verify localStorage is cleared
      const hasLocalStorageToken = !!localStorage.getItem('access_token')
      if (hasLocalStorageToken) {
        this.log('localStorage token not cleared after logout', 'WARN')
      }

      // Try to verify token (should fail)
      try {
        await this.authService.verifyToken()
        this.log('Token verification succeeded after logout (unexpected)', 'ERROR')
        return false
      } catch {
        // This is expected - token should be invalid after logout
        this.log('Token properly invalidated after logout')
        return true
      }
    } catch (error) {
      this.log(`Logout failed: ${error instanceof Error ? error.message : String(error)}`, 'ERROR')
      return false
    }
  }

  async testInvalidCredentials(): Promise<boolean> {
    try {
      await this.authService.login({ 
        username: 'invalid_user', 
        password: 'invalid_password' 
      })
      
      // If we get here, login succeeded when it shouldn't have
      this.log('Login with invalid credentials succeeded (unexpected)', 'ERROR')
      return false
    } catch (error) {
      // This is expected - invalid credentials should fail
      this.log('Invalid credentials properly rejected')
      return true
    }
  }

  async runFullTestSuite(username: string, password: string): Promise<TestResult[]> {
    this.log('=' * 60)
    this.log('STARTING FRONTEND AUTHENTICATION FLOW TEST')
    this.log('=' * 60)

    this.results = []

    // Test 1: Invalid credentials
    await this.runTest(
      'Invalid Credentials Test',
      () => this.testInvalidCredentials(),
      'Invalid credentials properly rejected'
    )

    // Test 2: Valid login
    await this.runTest(
      'Login Flow Test',
      () => this.testLoginFlow(username, password),
      'Login successful with httpOnly cookies and localStorage'
    )

    // Only run authenticated tests if login succeeded
    const loginResult = this.results.find(r => r.name === 'Login Flow Test')
    if (loginResult?.passed) {
      // Test 3: Token verification
      await this.runTest(
        'Token Verification Test',
        () => this.testTokenVerification(),
        'Token verification via httpOnly cookies'
      )

      // Test 4: API client with cookies
      await this.runTest(
        'API Client Cookie Test',
        () => this.testApiClientWithCookies(),
        'API client uses httpOnly cookies for authentication'
      )

      // Test 5: Auth state sync
      await this.runTest(
        'Auth State Sync Test',
        () => this.testAuthStateSync(),
        'Auth state properly synchronized'
      )

      // Test 6: Logout flow
      await this.runTest(
        'Logout Flow Test',
        () => this.testLogoutFlow(),
        'Logout clears cookies and invalidates tokens'
      )
    } else {
      this.log('Skipping authenticated tests due to login failure', 'WARN')
    }

    return this.results
  }

  printTestSummary(): void {
    this.log('=' * 60)
    this.log('FRONTEND TEST RESULTS SUMMARY')
    this.log('=' * 60)

    const passed = this.results.filter(r => r.passed).length
    const total = this.results.length

    this.results.forEach(result => {
      const status = result.passed ? 'PASS' : 'FAIL'
      this.log(`${result.name}: ${status} (${result.duration}ms)`)
      if (!result.passed) {
        this.log(`  └─ ${result.message}`, 'ERROR')
      }
    })

    this.log('-' * 60)
    this.log(`OVERALL: ${passed}/${total} tests passed (${((passed/total)*100).toFixed(1)}%)`)

    if (passed === total) {
      this.log('🎉 ALL FRONTEND TESTS PASSED! Authentication integration is working correctly.', 'SUCCESS')
    } else {
      this.log(`❌ ${total - passed} tests failed. Please review the frontend authentication integration.`, 'ERROR')
    }
  }
}

// Export for use in browser console or test runner
export { FrontendAuthTester }

// Auto-run if in development mode and credentials are provided
if (process.env.NODE_ENV === 'development') {
  // This can be called from browser console:
  // window.runAuthTests('admin', 'admin123')
  (window as any).runAuthTests = async (username: string, password: string) => {
    const tester = new FrontendAuthTester()
    await tester.runFullTestSuite(username, password)
    tester.printTestSummary()
    return tester.results
  }

  console.log('Frontend auth tester loaded. Run: window.runAuthTests("username", "password")')
}
