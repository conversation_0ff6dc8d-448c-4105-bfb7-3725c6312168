import React from 'react';
/**
 * API Types
 * Comprehensive interfaces for API communication
 */

import { ID, PaginationMeta, FilterConfig, SortConfig, Language } from './index'

// Generic API Response wrapper
export interface ApiResponse<T = unknown> {
  data: T
  message?: string
  status: number
  success: boolean
  timestamp: string
  request_id?: string
}

// Paginated API Response
export interface PaginatedApiResponse<T = unknown> extends ApiResponse<T[]> {
  pagination: PaginationMeta
}

// API Error Response
export interface ApiErrorResponse {
  error: {
    code: string
    message: string
    details?: Record<string, unknown>
    field_errors?: FieldError[]
  }
  status: number
  timestamp: string
  request_id?: string
  path?: string
}

export interface FieldError {
  field: string
  message: string
  code: string
  value?: unknown
}

// Request options for API calls
export interface ApiRequestOptions {
  headers?: Record<string, string>
  timeout?: number
  retries?: number
  cache?: boolean
  language?: Language
  signal?: AbortSignal
}

// Query parameters for list endpoints
export interface ListQueryParams {
  page?: number
  page_size?: number
  search?: string
  sort?: string
  order?: 'asc' | 'desc'
  filters?: Record<string, unknown>
  fields?: string[]
  expand?: string[]
  language?: Language
}

// Advanced query parameters
export interface AdvancedQueryParams extends ListQueryParams {
  filter_config?: FilterConfig[]
  sort_config?: SortConfig[]
  group_by?: string[]
  aggregate?: AggregateConfig[]
  date_range?: DateRangeFilter
}

export interface AggregateConfig {
  field: string
  function: 'count' | 'sum' | 'avg' | 'min' | 'max' | 'distinct'
  alias?: string
}

export interface DateRangeFilter {
  field: string
  start_date: string
  end_date: string
  timezone?: string
}

// Bulk operation types
export interface BulkOperation<T = unknown> {
  operation: 'create' | 'update' | 'delete'
  items: T[]
  options?: BulkOperationOptions
}

export interface BulkOperationOptions {
  batch_size?: number
  continue_on_error?: boolean
  validate_only?: boolean
  return_results?: boolean
}

export interface BulkOperationResult<T = unknown> {
  total_items: number
  successful_items: number
  failed_items: number
  results: BulkItemResult<T>[]
  errors: BulkOperationError[]
}

export interface BulkItemResult<T = unknown> {
  index: number
  status: 'success' | 'error' | 'skipped'
  data?: T
  error?: string
}

export interface BulkOperationError {
  index: number
  error_code: string
  error_message: string
  field_errors?: FieldError[]
}

// File upload types
export interface FileUploadRequest {
  file: File
  metadata?: Record<string, unknown>
  folder?: string
  overwrite?: boolean
}

export interface FileUploadResponse {
  file_id: ID
  filename: string
  original_filename: string
  size: number
  mime_type: string
  url: string
  thumbnail_url?: string
  metadata: Record<string, unknown>
  uploaded_at: string
}

export interface FileUploadProgress {
  file_id: ID
  progress: number
  status: 'uploading' | 'processing' | 'completed' | 'error'
  error?: string
}

// Export/Import types
export interface ExportRequest {
  format: 'csv' | 'excel' | 'pdf' | 'json'
  filters?: Record<string, unknown>
  fields?: string[]
  options?: ExportOptions
}

export interface ExportOptions {
  filename?: string
  include_headers?: boolean
  date_format?: string
  number_format?: string
  locale?: string
  template?: string
}

export interface ExportResponse {
  export_id: ID
  status: 'queued' | 'processing' | 'completed' | 'error'
  download_url?: string
  expires_at?: string
  file_size?: number
  error?: string
}

export interface ImportRequest {
  file: File
  mapping?: FieldMapping[]
  options?: ImportOptions
}

export interface FieldMapping {
  source_field: string
  target_field: string
  transformation?: string
  default_value?: unknown
}

export interface ImportOptions {
  skip_header_row?: boolean
  batch_size?: number
  validate_only?: boolean
  update_existing?: boolean
  create_missing?: boolean
  ignore_errors?: boolean
}

export interface ImportResponse {
  import_id: ID
  status: 'queued' | 'processing' | 'completed' | 'error'
  total_rows: number
  processed_rows: number
  successful_rows: number
  failed_rows: number
  errors: ImportError[]
  warnings: ImportWarning[]
}

export interface ImportError {
  row: number
  field: string
  message: string
  value: unknown
}

export interface ImportWarning {
  row: number
  field: string
  message: string
  value: unknown
}

// Authentication types
export interface LoginRequest {
  username: string
  password: string
  remember_me?: boolean
  two_factor_code?: string
}

export interface LoginResponse {
  access_token: string
  refresh_token: string
  token_type: string
  expires_in: number
  user: UserInfo
  permissions: string[]
  features: Record<string, boolean>
}

export interface UserInfo {
  id: ID
  username: string
  email: string
  first_name: string
  last_name: string
  full_name: string
  avatar_url?: string
  role: UserRole
  department?: UserDepartment
  last_login?: string
  is_active: boolean
  preferences: UserPreferences
}

export interface UserRole {
  id: ID
  name: string
  display_name: string
  permissions: string[]
}

export interface UserDepartment {
  id: ID
  name: string
  code: string
}

export interface UserPreferences {
  language: Language
  theme: 'light' | 'dark' | 'system'
  timezone: string
  date_format: string
  time_format: string
  notifications: NotificationPreferences
}

export interface NotificationPreferences {
  email: boolean
  push: boolean
  sms: boolean
  in_app: boolean
  frequency: 'immediate' | 'hourly' | 'daily' | 'weekly'
}

// Token refresh types
export interface RefreshTokenRequest {
  refresh_token: string
}

export interface RefreshTokenResponse {
  access_token: string
  token_type: string
  expires_in: number
}

// Password reset types
export interface PasswordResetRequest {
  email: string
}

export interface PasswordResetConfirmRequest {
  token: string
  new_password: string
  confirm_password: string
}

// Change password types
export interface ChangePasswordRequest {
  current_password: string
  new_password: string
  confirm_password: string
}

// API versioning
export interface ApiVersion {
  version: string
  release_date: string
  deprecated: boolean
  sunset_date?: string
  changelog_url?: string
}

// Health check types
export interface HealthCheckResponse {
  status: 'healthy' | 'degraded' | 'unhealthy'
  timestamp: string
  version: string
  uptime: number
  checks: HealthCheck[]
}

export interface HealthCheck {
  name: string
  status: 'pass' | 'fail' | 'warn'
  response_time?: number
  message?: string
  details?: Record<string, unknown>
}

// Rate limiting
export interface RateLimitInfo {
  limit: number
  remaining: number
  reset: number
  retry_after?: number
}

// Webhook types
export interface WebhookEvent {
  id: ID
  event_type: string
  resource_type: string
  resource_id: ID
  data: Record<string, unknown>
  timestamp: string
  signature: string
}

export interface WebhookSubscription {
  id: ID
  url: string
  events: string[]
  secret: string
  is_active: boolean
  created_at: string
  updated_at: string
}

// Search types
export interface SearchRequest {
  query: string
  filters?: Record<string, unknown>
  facets?: string[]
  highlight?: boolean
  page?: number
  page_size?: number
}

export interface SearchResponse<T = unknown> {
  results: SearchResult<T>[]
  total: number
  facets: SearchFacet[]
  suggestions: string[]
  query_time: number
}

export interface SearchResult<T = unknown> {
  item: T
  score: number
  highlights: Record<string, string[]>
}

export interface SearchFacet {
  field: string
  values: FacetValue[]
}

export interface FacetValue {
  value: string
  count: number
  selected: boolean
}
