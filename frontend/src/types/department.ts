import React from 'react';
/**
 * Department Domain Types
 * Comprehensive interfaces for department management
 */

import { BaseEntity, LocalizedContent, AuditTrail, ID, Timestamp, Status } from './index'
import { Employee } from './employee'

// Core Department interface
export interface Department extends BaseEntity, LocalizedContent, AuditTrail {
  code: string
  manager_id?: ID
  manager?: Employee
  parent_department_id?: ID
  parent_department?: Department
  sub_departments?: Department[]
  
  // Organizational Information
  level: number
  hierarchy_path: string
  cost_center?: string
  budget?: number
  budget_currency?: string
  
  // Location Information
  location?: string
  location_ar?: string
  building?: string
  floor?: string
  office_numbers?: string[]
  
  // Contact Information
  phone?: string
  email?: string
  fax?: string
  
  // Statistics
  employee_count: number
  direct_employee_count: number
  total_employee_count: number
  
  // Status and Configuration
  status: Status
  is_cost_center: boolean
  requires_approval: boolean
  
  // Metadata
  established_date?: Timestamp
  notes?: string
  tags?: string[]
}

// Department form data for creation/updates
export interface DepartmentFormData {
  // Required fields
  name: string
  code: string
  
  // Optional fields
  name_ar?: string
  description?: string
  description_ar?: string
  manager_id?: ID
  parent_department_id?: ID
  
  // Organizational
  cost_center?: string
  budget?: number
  budget_currency?: string
  
  // Location
  location?: string
  location_ar?: string
  building?: string
  floor?: string
  office_numbers?: string[]
  
  // Contact
  phone?: string
  email?: string
  fax?: string
  
  // Configuration
  is_cost_center?: boolean
  requires_approval?: boolean
  established_date?: string
  notes?: string
  tags?: string[]
}

// Department hierarchy node for tree structures
export interface DepartmentNode {
  department: Department
  children: DepartmentNode[]
  parent?: DepartmentNode
  depth: number
  path: Department[]
}

// Department filters for search/listing
export interface DepartmentFilters {
  search?: string
  manager_id?: ID
  parent_department_id?: ID
  status?: Status
  location?: string
  cost_center?: string
  budget_min?: number
  budget_max?: number
  employee_count_min?: number
  employee_count_max?: number
  is_cost_center?: boolean
  established_date_from?: string
  established_date_to?: string
  tags?: string[]
}

// Department statistics
export interface DepartmentStats {
  total_departments: number
  active_departments: number
  departments_with_managers: number
  departments_without_managers: number
  average_employees_per_department: number
  total_budget: number
  departments_by_level: Record<number, number>
  largest_department: {
    id: ID
    name: string
    employee_count: number
  }
  smallest_department: {
    id: ID
    name: string
    employee_count: number
  }
}

// Department budget information
export interface DepartmentBudget {
  id: ID
  department_id: ID
  department: Department
  fiscal_year: number
  allocated_budget: number
  spent_budget: number
  remaining_budget: number
  currency: string
  budget_categories: BudgetCategory[]
  status: 'DRAFT' | 'APPROVED' | 'ACTIVE' | 'CLOSED'
  approved_by?: ID
  approved_at?: Timestamp
  created_at: Timestamp
  updated_at: Timestamp
}

export interface BudgetCategory {
  id: ID
  name: string
  allocated_amount: number
  spent_amount: number
  remaining_amount: number
  percentage_of_total: number
  subcategories?: BudgetSubcategory[]
}

export interface BudgetSubcategory {
  id: ID
  name: string
  allocated_amount: number
  spent_amount: number
  remaining_amount: number
}

// Department performance metrics
export interface DepartmentPerformance {
  department_id: ID
  period_start: Timestamp
  period_end: Timestamp
  
  // Employee metrics
  employee_satisfaction_score?: number
  employee_turnover_rate: number
  new_hires: number
  terminations: number
  
  // Performance metrics
  productivity_score?: number
  goal_achievement_rate?: number
  project_completion_rate?: number
  
  // Financial metrics
  budget_utilization_rate: number
  cost_per_employee: number
  revenue_per_employee?: number
  
  // Operational metrics
  average_response_time?: number
  customer_satisfaction_score?: number
  quality_score?: number
  
  created_at: Timestamp
  updated_at: Timestamp
}

// Department goals and objectives
export interface DepartmentGoal {
  id: ID
  department_id: ID
  title: string
  description: string
  target_value?: number
  current_value?: number
  unit?: string
  target_date: Timestamp
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  status: 'NOT_STARTED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED' | 'ON_HOLD'
  assigned_to?: ID
  progress_percentage: number
  milestones: GoalMilestone[]
  created_by: ID
  created_at: Timestamp
  updated_at: Timestamp
}

export interface GoalMilestone {
  id: ID
  title: string
  description?: string
  target_date: Timestamp
  completed_date?: Timestamp
  status: 'PENDING' | 'COMPLETED' | 'OVERDUE'
  assigned_to?: ID
}

// Department organizational chart
export interface OrganizationalChart {
  root_department: Department
  structure: DepartmentNode[]
  total_levels: number
  total_departments: number
  total_employees: number
  generated_at: Timestamp
}

// Department approval workflow
export interface DepartmentApproval {
  id: ID
  department_id: ID
  request_type: 'CREATION' | 'UPDATE' | 'DELETION' | 'BUDGET_CHANGE' | 'MANAGER_CHANGE'
  requested_changes: Record<string, unknown>
  current_values: Record<string, unknown>
  requested_by: ID
  status: 'PENDING' | 'APPROVED' | 'REJECTED' | 'CANCELLED'
  approved_by?: ID
  approved_at?: Timestamp
  rejection_reason?: string
  comments?: string
  created_at: Timestamp
  updated_at: Timestamp
}

// Department reporting structure
export interface ReportingStructure {
  employee_id: ID
  employee: Employee
  direct_manager_id?: ID
  direct_manager?: Employee
  department_manager_id?: ID
  department_manager?: Employee
  reporting_chain: Employee[]
  reporting_level: number
}

// Department cost allocation
export interface CostAllocation {
  id: ID
  department_id: ID
  cost_type: 'SALARY' | 'BENEFITS' | 'EQUIPMENT' | 'UTILITIES' | 'RENT' | 'OTHER'
  amount: number
  currency: string
  allocation_method: 'DIRECT' | 'PROPORTIONAL' | 'ACTIVITY_BASED'
  allocation_basis?: string
  period_start: Timestamp
  period_end: Timestamp
  notes?: string
  created_at: Timestamp
  updated_at: Timestamp
}

// Department communication preferences
export interface DepartmentCommunication {
  department_id: ID
  email_notifications: boolean
  sms_notifications: boolean
  slack_channel?: string
  teams_channel?: string
  meeting_schedule: MeetingSchedule[]
  communication_protocols: CommunicationProtocol[]
}

export interface MeetingSchedule {
  type: 'DAILY_STANDUP' | 'WEEKLY_REVIEW' | 'MONTHLY_REVIEW' | 'QUARTERLY_REVIEW'
  frequency: string
  day_of_week?: number
  time: string
  duration_minutes: number
  location?: string
  virtual_meeting_link?: string
  required_attendees: ID[]
  optional_attendees: ID[]
}

export interface CommunicationProtocol {
  type: 'EMAIL' | 'SLACK' | 'TEAMS' | 'PHONE' | 'IN_PERSON'
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'
  response_time_hours: number
  escalation_rules: EscalationRule[]
}

export interface EscalationRule {
  condition: string
  escalate_to: ID
  escalation_time_hours: number
  notification_method: 'EMAIL' | 'SMS' | 'PHONE'
}
