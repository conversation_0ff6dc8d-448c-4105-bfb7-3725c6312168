import React from 'react';
/**
 * Employee Domain Types
 * Comprehensive interfaces for employee management
 */

import { BaseEntity, LocalizedContent, AuditTrail, ID, Timestamp, Status } from './index'

// Employment status types
export type EmploymentStatus = 
  | 'FULL_TIME' 
  | 'PART_TIME' 
  | 'CONTRACT' 
  | 'INTERN' 
  | 'CONSULTANT' 
  | 'TEMPORARY'

export type Gender = 'M' | 'F' | 'OTHER' | 'PREFER_NOT_TO_SAY'

export type MaritalStatus = 
  | 'SINGLE' 
  | 'MARRIED' 
  | 'DIVORCED' 
  | 'WIDOWED' 
  | 'SEPARATED'

// Core Employee interface
export interface Employee extends BaseEntity, AuditTrail {
  // Personal Information
  employee_id: string
  first_name: string
  last_name: string
  first_name_ar?: string
  last_name_ar?: string
  full_name: string
  full_name_ar?: string
  email: string
  phone: string
  mobile?: string
  gender: Gender
  date_of_birth?: Timestamp
  nationality?: string
  national_id?: string
  passport_number?: string
  marital_status?: MaritalStatus

  // Employment Information
  department_id: ID
  department: Department
  position: string
  position_ar?: string
  manager_id?: ID
  manager?: Employee
  hire_date: Timestamp
  termination_date?: Timestamp
  employment_status: EmploymentStatus
  work_location: string
  work_location_ar?: string

  // Compensation
  salary: number
  currency: string
  salary_grade?: string
  benefits?: EmployeeBenefit[]

  // Contact Information
  address: Address
  emergency_contact: EmergencyContact

  // Professional Information
  skills: Skill[]
  education: Education[]
  certifications: Certification[]
  languages: LanguageProficiency[]

  // System Information
  user_id?: ID
  avatar_url?: string
  timezone: string
  last_login?: Timestamp
  is_active: boolean
  notes?: string

  // Computed fields
  age?: number
  years_of_service?: number
  next_review_date?: Timestamp
}

// Employee form data for creation/updates
export interface EmployeeFormData {
  // Required fields
  employee_id: string
  first_name: string
  last_name: string
  email: string
  phone: string
  department_id: ID
  position: string
  hire_date: string
  employment_status: EmploymentStatus
  salary: number
  gender: Gender

  // Optional fields
  first_name_ar?: string
  last_name_ar?: string
  position_ar?: string
  mobile?: string
  date_of_birth?: string
  nationality?: string
  national_id?: string
  passport_number?: string
  marital_status?: MaritalStatus
  manager_id?: ID
  work_location?: string
  work_location_ar?: string
  currency?: string
  salary_grade?: string
  timezone?: string
  notes?: string

  // Nested objects
  address?: Partial<Address>
  emergency_contact?: Partial<EmergencyContact>
  user?: Partial<UserAccount>
}

// Address interface
export interface Address {
  street: string
  city: string
  state: string
  postal_code: string
  country: string
  street_ar?: string
  city_ar?: string
  state_ar?: string
  country_ar?: string
}

// Emergency contact interface
export interface EmergencyContact {
  name: string
  relationship: string
  phone: string
  email?: string
  address?: string
}

// Employee benefits
export interface EmployeeBenefit {
  id: ID
  type: string
  name: string
  description?: string
  value?: number
  currency?: string
  start_date: Timestamp
  end_date?: Timestamp
  is_active: boolean
}

// Skills and competencies
export interface Skill {
  id: ID
  name: string
  category: string
  level: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED' | 'EXPERT'
  years_of_experience?: number
  certified?: boolean
  certification_date?: Timestamp
}

// Education information
export interface Education {
  id: ID
  institution: string
  degree: string
  field_of_study: string
  start_date: Timestamp
  end_date?: Timestamp
  gpa?: number
  is_completed: boolean
  country?: string
}

// Professional certifications
export interface Certification {
  id: ID
  name: string
  issuing_organization: string
  issue_date: Timestamp
  expiry_date?: Timestamp
  credential_id?: string
  credential_url?: string
  is_active: boolean
}

// Language proficiency
export interface LanguageProficiency {
  language: string
  speaking_level: ProficiencyLevel
  reading_level: ProficiencyLevel
  writing_level: ProficiencyLevel
  is_native: boolean
}

export type ProficiencyLevel = 
  | 'BASIC' 
  | 'CONVERSATIONAL' 
  | 'PROFICIENT' 
  | 'FLUENT' 
  | 'NATIVE'

// User account information
export interface UserAccount {
  id: ID
  username: string
  email: string
  is_active: boolean
  last_login?: Timestamp
  role: UserRole
  permissions: string[]
}

// User roles
export interface UserRole {
  id: ID
  name: string
  description?: string
  permissions: string[]
  is_system_role: boolean
}

// Employee filters for search/listing
export interface EmployeeFilters {
  search?: string
  department_id?: ID
  position?: string
  employment_status?: EmploymentStatus
  manager_id?: ID
  hire_date_from?: string
  hire_date_to?: string
  salary_min?: number
  salary_max?: number
  is_active?: boolean
  skills?: string[]
  location?: string
  gender?: Gender
  age_min?: number
  age_max?: number
}

// Employee statistics
export interface EmployeeStats {
  total_employees: number
  active_employees: number
  new_hires_this_month: number
  terminations_this_month: number
  average_salary: number
  average_age: number
  average_tenure: number
  gender_distribution: Record<Gender, number>
  department_distribution: Record<string, number>
  employment_status_distribution: Record<EmploymentStatus, number>
}

// Performance review
export interface PerformanceReview {
  id: ID
  employee_id: ID
  reviewer_id: ID
  review_period_start: Timestamp
  review_period_end: Timestamp
  overall_rating: number
  goals: PerformanceGoal[]
  competencies: CompetencyRating[]
  feedback: string
  development_plan?: string
  status: 'DRAFT' | 'SUBMITTED' | 'APPROVED' | 'COMPLETED'
  created_at: Timestamp
  updated_at: Timestamp
}

export interface PerformanceGoal {
  id: ID
  title: string
  description: string
  target_date: Timestamp
  weight: number
  status: 'NOT_STARTED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED'
  achievement_rating?: number
  comments?: string
}

export interface CompetencyRating {
  competency: string
  rating: number
  comments?: string
}

// Leave management
export interface LeaveRequest {
  id: ID
  employee_id: ID
  leave_type: LeaveType
  start_date: Timestamp
  end_date: Timestamp
  days_requested: number
  reason?: string
  status: 'PENDING' | 'APPROVED' | 'REJECTED' | 'CANCELLED'
  approved_by?: ID
  approved_at?: Timestamp
  rejection_reason?: string
  created_at: Timestamp
  updated_at: Timestamp
}

export interface LeaveType {
  id: ID
  name: string
  name_ar?: string
  days_per_year: number
  carry_forward_days?: number
  requires_approval: boolean
  is_paid: boolean
  is_active: boolean
}

export interface LeaveBalance {
  employee_id: ID
  leave_type_id: ID
  leave_type: LeaveType
  total_days: number
  used_days: number
  remaining_days: number
  carried_forward_days: number
  year: number
}

// Department interface (referenced in Employee)
export interface Department extends BaseEntity, LocalizedContent {
  code: string
  manager_id?: ID
  manager?: Employee
  parent_department_id?: ID
  parent_department?: Department
  cost_center?: string
  budget?: number
  employee_count: number
  location?: string
  location_ar?: string
  status: Status
}

// Create and Update types for API calls
export interface CreateEmployeeData {
  employee_id?: string
  first_name: string
  last_name: string
  first_name_ar?: string
  last_name_ar?: string
  email: string
  phone: string
  mobile?: string
  gender: Gender
  date_of_birth?: string
  nationality?: string
  national_id?: string
  passport_number?: string
  marital_status?: MaritalStatus
  department_id: ID
  position: string
  position_ar?: string
  employment_status: EmploymentStatus
  hire_date: string
  salary: number
  address?: string
  emergency_contact?: string
  emergency_phone?: string
  is_active?: boolean
}

export interface UpdateEmployeeData extends Partial<CreateEmployeeData> {
  id?: ID
}
