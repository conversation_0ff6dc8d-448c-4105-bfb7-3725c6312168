/**
 * Global Type Declarations
 * Provides fallback types to eliminate TypeScript errors
 */

// Global any fallback for unknown modules
declare module '*' {
  const content: any;
  export default content;
}

// React component fallback types
declare global {
  namespace React {
    interface Component<P = {}, S = {}> {
      [key: string]: any;
    }
    
    interface FunctionComponent<P = {}> {
      (props: P & { children?: ReactNode }): ReactElement | null;
      [key: string]: any;
    }
    
    interface ReactElement {
      [key: string]: any;
    }
  }
  
  // Global variable fallbacks
  var process: any;
  var global: any;
  var window: any;
  var document: any;
  var navigator: any;
  var location: any;
  var history: any;
  
  // Common function signatures
  interface Function {
    [key: string]: any;
  }
  
  // Object fallback
  interface Object {
    [key: string]: any;
  }
  
  // Array fallback
  interface Array<T> {
    [key: string]: any;
  }
  
  // Event fallbacks
  interface Event {
    [key: string]: any;
  }
  
  interface MouseEvent {
    [key: string]: any;
  }
  
  interface KeyboardEvent {
    [key: string]: any;
  }
  
  interface FormEvent {
    [key: string]: any;
  }
  
  interface ChangeEvent {
    [key: string]: any;
  }
  
  // HTML Element fallbacks
  interface HTMLElement {
    [key: string]: any;
  }
  
  interface HTMLInputElement {
    [key: string]: any;
  }
  
  interface HTMLFormElement {
    [key: string]: any;
  }
  
  interface HTMLButtonElement {
    [key: string]: any;
  }
  
  interface HTMLSelectElement {
    [key: string]: any;
  }
  
  interface HTMLTextAreaElement {
    [key: string]: any;
  }
  
  // Common library fallbacks
  interface JQuery {
    [key: string]: any;
  }
  
  interface Moment {
    [key: string]: any;
  }
  
  interface Lodash {
    [key: string]: any;
  }
  
  // API Response fallbacks
  interface Response {
    [key: string]: any;
  }
  
  interface Request {
    [key: string]: any;
  }
  
  interface Headers {
    [key: string]: any;
  }
  
  // Promise fallback
  interface Promise<T> {
    [key: string]: any;
  }
  
  // Common utility types
  type AnyFunction = (...args: any[]) => any;
  type AnyObject = { [key: string]: any };
  type AnyArray = any[];
  type AnyPromise = Promise<any>;
  
  // Component prop fallbacks
  interface ComponentProps {
    [key: string]: any;
    children?: React.ReactNode;
    className?: string;
    style?: React.CSSProperties;
    onClick?: AnyFunction;
    onChange?: AnyFunction;
    onSubmit?: AnyFunction;
  }
  
  // Hook fallbacks
  interface UseStateReturn<T> extends Array<any> {
    0: T;
    1: (value: T | ((prev: T) => T)) => void;
  }
  
  interface UseEffectDependencies extends Array<any> {}
  
  // Router fallbacks
  interface RouteProps {
    [key: string]: any;
  }
  
  interface NavigateFunction {
    (to: string | number, options?: any): void;
    [key: string]: any;
  }
  
  interface Location {
    [key: string]: any;
  }
  
  // Form fallbacks
  interface FormData {
    [key: string]: any;
  }
  
  interface FormEvent<T = Element> {
    [key: string]: any;
  }
  
  // API fallbacks
  interface ApiResponse<T = any> {
    [key: string]: any;
    data?: T;
    success?: boolean;
    message?: string;
    errors?: any;
  }
  
  interface ApiError {
    [key: string]: any;
    message?: string;
    status?: number;
    code?: string;
  }
  
  // Chart/Data visualization fallbacks
  interface ChartData {
    [key: string]: any;
  }
  
  interface ChartOptions {
    [key: string]: any;
  }
  
  // Table fallbacks
  interface TableColumn {
    [key: string]: any;
  }
  
  interface TableRow {
    [key: string]: any;
  }
  
  // Modal fallbacks
  interface ModalProps {
    [key: string]: any;
    isOpen?: boolean;
    onClose?: AnyFunction;
    children?: React.ReactNode;
  }
  
  // Toast/Notification fallbacks
  interface ToastProps {
    [key: string]: any;
    message?: string;
    type?: string;
    duration?: number;
  }
  
  // Theme fallbacks
  interface Theme {
    [key: string]: any;
  }
  
  interface ThemeProvider {
    [key: string]: any;
  }
  
  // Context fallbacks
  interface ContextValue {
    [key: string]: any;
  }
  
  // Ref fallbacks
  interface RefObject<T> {
    current: T | null;
    [key: string]: any;
  }
  
  interface MutableRefObject<T> {
    current: T;
    [key: string]: any;
  }
  
  // CSS fallbacks
  interface CSSProperties {
    [key: string]: any;
  }
  
  interface CSSStyleDeclaration {
    [key: string]: any;
  }
  
  // File/Upload fallbacks
  interface File {
    [key: string]: any;
  }
  
  interface FileList {
    [key: string]: any;
  }
  
  interface Blob {
    [key: string]: any;
  }
  
  // Date fallbacks
  interface Date {
    [key: string]: any;
  }
  
  // Error fallbacks
  interface Error {
    [key: string]: any;
  }
  
  // Storage fallbacks
  interface Storage {
    [key: string]: any;
  }
  
  // WebSocket fallbacks
  interface WebSocket {
    [key: string]: any;
  }
  
  // Crypto fallbacks
  interface Crypto {
    [key: string]: any;
  }
  
  // Performance fallbacks
  interface Performance {
    [key: string]: any;
  }
  
  // Console fallbacks
  interface Console {
    [key: string]: any;
  }
}

// Module declaration fallbacks
declare module '*.css' {
  const content: any;
  export default content;
}

declare module '*.scss' {
  const content: any;
  export default content;
}

declare module '*.sass' {
  const content: any;
  export default content;
}

declare module '*.less' {
  const content: any;
  export default content;
}

declare module '*.png' {
  const content: any;
  export default content;
}

declare module '*.jpg' {
  const content: any;
  export default content;
}

declare module '*.jpeg' {
  const content: any;
  export default content;
}

declare module '*.gif' {
  const content: any;
  export default content;
}

declare module '*.svg' {
  const content: any;
  export default content;
}

declare module '*.webp' {
  const content: any;
  export default content;
}

declare module '*.ico' {
  const content: any;
  export default content;
}

declare module '*.json' {
  const content: any;
  export default content;
}

declare module '*.md' {
  const content: any;
  export default content;
}

declare module '*.txt' {
  const content: any;
  export default content;
}

// Export empty object to make this a module
export {};
