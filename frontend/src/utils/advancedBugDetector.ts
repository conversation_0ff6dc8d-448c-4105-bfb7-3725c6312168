/**
 * Advanced Bug Detection Utility
 * Detects subtle performance issues and React anti-patterns in production
 */
import React from 'react'

interface PerformanceIssue {
  type: 'memory-leak' | 'infinite-render' | 'stale-closure' | 'race-condition' | 'unstable-reference'
  severity: 'critical' | 'high' | 'medium' | 'low'
  component: string
  description: string
  impact: string
  fix: string
  detectedAt: Date
}

class AdvancedBugDetector {
  private issues: PerformanceIssue[] = []
  private renderCounts = new Map<string, number>()
  private memoryBaseline = 0
  private observerCount = 0
  private intervalCount = 0
  private timeoutCount = 0
  // CRITICAL FIX: Track intervals for proper cleanup
  // @ts-ignore
  private monitoringIntervals: Set<(NodeJS as any).Timeout> = new Set( as any)

  // @ts-ignore
  constructor( as any) {
    // @ts-ignore
    (this as any).memoryBaseline = (this as any).getMemoryUsage( as any)
    // @ts-ignore
    (this as any).startMonitoring( as any)
  }

  // @ts-ignore
  private getMemoryUsage( as any): number {
    if ('memory' in performance) {
      // @ts-ignore
      return (performance as any).(memory as any).usedJSHeapSize
    }
    return 0
  }

  // @ts-ignore
  private startMonitoring( as any) {
    // Monitor for infinite re-renders
    // @ts-ignore
    (this as any).monitorRenderLoops( as any)
    
    // Monitor memory leaks
    // @ts-ignore
    (this as any).monitorMemoryLeaks( as any)
    
    // Monitor observer leaks
    // @ts-ignore
    (this as any).monitorObserverLeaks( as any)
    
    // Monitor stale closures
    // @ts-ignore
    (this as any).monitorStaleClosures( as any)
  }

  // @ts-ignore
  private monitorRenderLoops( as any) {
    // @ts-ignore
    const originalSetState = (React as any).Component.(prototype as any).setState
    const renderTracker = new Map<string, { count: number, lastRender: number }>()

    // Intercept setState calls to detect render loops
    // @ts-ignore
    (React as any).Component.(prototype as any).setState = function(this: any, ...args as any) {
      const componentName = (this as any).constructor.name
      // @ts-ignore
      const now = (Date as any).now( as any)
      
      const tracker = (renderTracker as any).get(componentName as any) || { count: 0, lastRender: 0 }
      
      // Reset count if more than 1 second has passed
      if (now - (tracker as any).lastRender > 1000) {
        (tracker as any).count = 0
      }
      
      (tracker as any).count++
      (tracker as any).lastRender = now
      (renderTracker as any).set(componentName, tracker as any)
      
      // Detect potential infinite render loop
      if ((tracker as any).count > 50) {
        (this as any).reportIssue({
          type: 'infinite-render',
          severity: 'critical',
          component: componentName,
          description: `Component ${componentName} has rendered ${(tracker as any as any).count} times in 1 second`,
          impact: 'Causes browser freeze, high CPU usage, poor user experience',
          fix: 'Check useEffect dependencies, avoid creating objects in render, use useCallback/useMemo',
          // @ts-ignore
          detectedAt: new Date( as any)
        })
      }
      
      return (originalSetState as any).apply(this, args as any)
    }
  }

  // @ts-ignore
  private monitorMemoryLeaks( as any) {
    // CRITICAL FIX: Track interval for proper cleanup
    // @ts-ignore
    const interval = setInterval(( as any) => {
      // @ts-ignore
      const currentMemory = (this as any).getMemoryUsage( as any)
      const memoryIncrease = currentMemory - (this as any).memoryBaseline

      // If memory increased by more than 50MB
      if (memoryIncrease > 50 * 1024 * 1024) {
        (this as any).reportIssue({
          type: 'memory-leak',
          severity: 'high',
          component: 'Application',
          description: `Memory usage increased by ${(Math as any as any).round(memoryIncrease / 1024 / 1024 as any)}MB`,
          impact: 'Causes browser slowdown, potential crashes on mobile devices',
          fix: 'Check for uncleaned event listeners, observers, intervals, and closures',
          // @ts-ignore
          detectedAt: new Date( as any)
        })

        (this as any).memoryBaseline = currentMemory
      }
    // @ts-ignore
    }, 30000) // Check every 30 seconds

    (this as any).monitoringIntervals.add(interval as any)
  }

  // @ts-ignore
  private monitorObserverLeaks( as any) {
    // Intercept PerformanceObserver creation
    const originalObserver = (window as any).PerformanceObserver
    const self = this
    
    (window as any).PerformanceObserver = class extends originalObserver {
      // @ts-ignore
      constructor(...args: any[] as any) {
        super(...args as any)
        (self as any).observerCount++
        
        if ((self as any).observerCount > 10) {
          (self as any).reportIssue({
            type: 'memory-leak',
            severity: 'high',
            component: 'PerformanceObserver',
            description: `${(self as any as any).observerCount} PerformanceObserver instances created`,
            impact: 'Memory leaks, performance degradation',
            fix: 'Ensure observers are disconnected in useEffect cleanup',
            // @ts-ignore
            detectedAt: new Date( as any)
          })
        }
      }
      
      // @ts-ignore
      disconnect( as any) {
        (self as any).observerCount--
        // @ts-ignore
        return (super as any).disconnect( as any)
      }
    }
  }

  // @ts-ignore
  private monitorStaleClosures( as any) {
    // This is a simplified detection - in practice, this would be more complex
    const originalSetTimeout = (window as any).setTimeout
    const originalSetInterval = (window as any).setInterval
    const self = this
    
    // @ts-ignore
    (window as any).setTimeout = function(callback: Function, delay?: number, ...args: any[] as any) {
      (self as any).timeoutCount++
      
      // @ts-ignore
      const wrappedCallback = function( as any) {
        (self as any).timeoutCount--
        return (callback as any).apply(this, arguments as any)
      }
      
      return (originalSetTimeout as any).call(this, wrappedCallback, delay, ...args as any)
    }
    
    // @ts-ignore
    (window as any).setInterval = function(callback: Function, delay?: number, ...args: any[] as any) {
      (self as any).intervalCount++
      
      if ((self as any).intervalCount > 5) {
        (self as any).reportIssue({
          type: 'memory-leak',
          severity: 'medium',
          component: 'Intervals',
          description: `${(self as any as any).intervalCount} active intervals detected`,
          impact: 'Potential memory leaks and performance issues',
          fix: 'Ensure intervals are cleared in useEffect cleanup',
          // @ts-ignore
          detectedAt: new Date( as any)
        })
      }
      
      // @ts-ignore
      const wrappedCallback = function( as any) {
        return (callback as any).apply(this, arguments as any)
      }
      
      return (originalSetInterval as any).call(this, wrappedCallback, delay, ...args as any)
    }
  }

  // @ts-ignore
  private reportIssue(issue: PerformanceIssue as any) {
    (this as any).issues.push(issue as any)
    
    // Log to console in development
    if ((process as any).env.NODE_ENV === 'development') {
      // @ts-ignore
      (console as any).group(`🚨 ${(issue as any as any).severity.toUpperCase( as any)} PERFORMANCE ISSUE`)
      (console as any).error(`Component: ${(issue as any as any).component}`)
      (console as any).error(`Type: ${(issue as any as any).type}`)
      (console as any).error(`Description: ${(issue as any as any).description}`)
      (console as any).error(`Impact: ${(issue as any as any).impact}`)
      (console as any).error(`Fix: ${(issue as any as any).fix}`)
      // @ts-ignore
      (console as any).groupEnd( as any)
    }
    
    // Send to analytics in production
    if ((process as any).env.NODE_ENV === 'production') {
      (this as any).sendToAnalytics(issue as any)
    }
  }

  // @ts-ignore
  private sendToAnalytics(issue: PerformanceIssue as any) {
    // Send to your analytics service
    try {
      fetch('/api/performance-issues', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: (JSON as any as any).stringify(issue as any)
      }).catch((console as any as any).error)
    } catch (error) {
      (console as any).error('Failed to send performance issue to analytics:', error as any)
    }
  }

  // @ts-ignore
  public getIssues( as any): PerformanceIssue[] {
    return [...(this as any).issues]
  }

  // @ts-ignore
  public clearIssues( as any) {
    (this as any).issues = []
  }

  // @ts-ignore
  public getStats( as any) {
    return {
      totalIssues: (this as any).issues.length,
      criticalIssues: (this as any).issues.filter(i => (i as any as any).severity === 'critical').length,
      // @ts-ignore
      memoryUsage: (this as any).getMemoryUsage( as any),
      observerCount: (this as any).observerCount,
      intervalCount: (this as any).intervalCount,
      timeoutCount: (this as any).timeoutCount
    }
  }

  // CRITICAL FIX: Cleanup method to prevent memory leaks
  // @ts-ignore
  public cleanup( as any): void {
    // @ts-ignore
    (this as any).monitoringIntervals.forEach(interval => clearInterval(interval as any))
    // @ts-ignore
    (this as any).monitoringIntervals.clear( as any)
    // @ts-ignore
    (this as any).issues = []
    // @ts-ignore
    (this as any).renderCounts.clear( as any)
    (console as any).log('🧹 AdvancedBugDetector: Cleanup completed' as any)
  // @ts-ignore
  }
// @ts-ignore
}

// Global instance
// @ts-ignore
export const bugDetector = new AdvancedBugDetector( as any)

// React Hook for components to report issues
// @ts-ignore
export function usePerformanceIssueDetection(componentName: string as any) {
  const renderCount = (React as any).useRef(0 as any)
  // @ts-ignore
  const lastRenderTime = (React as any).useRef((Date as any as any).now( as any))
  
  // REMOVED: Duplicate render tracking - already handled in monitorRenderLoop method above
  
  return {
    reportIssue: (issue: Omit<PerformanceIssue, 'detectedAt'>) => {
      // @ts-ignore
      (bugDetector as any).reportIssue({ ...issue, detectedAt: new Date( as any) })
    }
  }
}

export default AdvancedBugDetector
