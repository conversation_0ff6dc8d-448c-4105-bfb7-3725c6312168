/**
 * Analytics Utility
 * Simple analytics tracking for the EMS application
 */

interface AnalyticsEvent {
  name: string;
  properties?: Record<string, any>;
  timestamp?: string;
  userId?: string;
  sessionId?: string;
}

interface AnalyticsConfig {
  enabled: boolean;
  debug: boolean;
  endpoint?: string;
}

class Analytics {
  private config: AnalyticsConfig;
  private sessionId: string;
  private userId?: string;

  constructor(config: AnalyticsConfig = { enabled: true, debug: false }) {
    this.config = config;
    this.sessionId = this.generateSessionId();
    this.init();
  }

  private init(): void {
    if (this.config.debug) {
      console.log('Analytics initialized', { sessionId: this.sessionId });
    }
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  public setUserId(userId: string): void {
    this.userId = userId;
    if (this.config.debug) {
      console.log('Analytics user ID set:', userId);
    }
  }

  public track(eventName: string, properties?: Record<string, any>): void {
    if (!this.config.enabled) {
      return;
    }

    const event: AnalyticsEvent = {
      name: eventName,
      properties: properties || {},
      timestamp: new Date().toISOString(),
      userId: this.userId,
      sessionId: this.sessionId
    };

    if (this.config.debug) {
      console.log('Analytics event tracked:', event);
    }

    // Store in localStorage for now (in production, send to analytics service)
    this.storeEvent(event);
  }

  private storeEvent(event: AnalyticsEvent): void {
    try {
      const existingEvents = this.getStoredEvents();
      existingEvents.push(event);
      
      // Keep only last 100 events
      const recentEvents = existingEvents.slice(-100);
      
      localStorage.setItem('ems_analytics_events', JSON.stringify(recentEvents));
    } catch (error) {
      if (this.config.debug) {
        console.error('Failed to store analytics event:', error);
      }
    }
  }

  private getStoredEvents(): AnalyticsEvent[] {
    try {
      const stored = localStorage.getItem('ems_analytics_events');
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      if (this.config.debug) {
        console.error('Failed to retrieve stored events:', error);
      }
      return [];
    }
  }

  public getEvents(): AnalyticsEvent[] {
    return this.getStoredEvents();
  }

  public clearEvents(): void {
    try {
      localStorage.removeItem('ems_analytics_events');
      if (this.config.debug) {
        console.log('Analytics events cleared');
      }
    } catch (error) {
      if (this.config.debug) {
        console.error('Failed to clear events:', error);
      }
    }
  }

  // Common tracking methods
  public trackPageView(pageName: string, additionalProperties?: Record<string, any>): void {
    this.track('page_view', {
      page: pageName,
      url: window.location.href,
      referrer: document.referrer,
      ...additionalProperties
    });
  }

  public trackUserAction(action: string, target: string, additionalProperties?: Record<string, any>): void {
    this.track('user_action', {
      action,
      target,
      ...additionalProperties
    });
  }

  public trackError(error: Error, context?: string): void {
    this.track('error', {
      message: error.message,
      stack: error.stack,
      context,
      url: window.location.href
    });
  }

  public trackPerformance(metric: string, value: number, unit: string = 'ms'): void {
    this.track('performance', {
      metric,
      value,
      unit,
      url: window.location.href
    });
  }

  public trackFeatureUsage(feature: string, additionalProperties?: Record<string, any>): void {
    this.track('feature_usage', {
      feature,
      ...additionalProperties
    });
  }
}

// Create singleton instance
const analytics = new Analytics({
  enabled: process.env.NODE_ENV === 'production',
  debug: process.env.NODE_ENV === 'development'
});

// Export convenience functions
export const trackPageView = (pageName: string, properties?: Record<string, any>): void => {
  analytics.trackPageView(pageName, properties);
};

export const trackUserAction = (action: string, target: string, properties?: Record<string, any>): void => {
  analytics.trackUserAction(action, target, properties);
};

export const trackError = (error: Error, context?: string): void => {
  analytics.trackError(error, context);
};

export const trackPerformance = (metric: string, value: number, unit?: string): void => {
  analytics.trackPerformance(metric, value, unit);
};

export const trackFeatureUsage = (feature: string, properties?: Record<string, any>): void => {
  analytics.trackFeatureUsage(feature, properties);
};

export const setUserId = (userId: string): void => {
  analytics.setUserId(userId);
};

export const getAnalyticsEvents = (): AnalyticsEvent[] => {
  return analytics.getEvents();
};

export const clearAnalyticsEvents = (): void => {
  analytics.clearEvents();
};

export default analytics;
