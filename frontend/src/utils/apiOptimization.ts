import React from 'react';
/**
 * API OPTIMIZATION UTILITIES
 * Prevents excessive API calls and optimizes data fetching patterns
 */

import { deduplicateRequest } from './apiCache'

// Configuration for API optimization
export const API_OPTIMIZATION_CONFIG = {
  // Maximum page sizes for different endpoints
  MAX_PAGE_SIZES: {
    employees: 50,
    departments: 25,
    projects: 30,
    tasks: 40,
    reports: 20,
    default: 25
  },
  
  // Cache durations in milliseconds
  CACHE_DURATIONS: {
    employees: 3 * 60 * 1000,    // 3 minutes
    departments: 5 * 60 * 1000,  // 5 minutes
    dashboard: 2 * 60 * 1000,    // 2 minutes
    reports: 10 * 60 * 1000,     // 10 minutes
    default: 5 * 60 * 1000       // 5 minutes
  },
  
  // Request throttling limits
  THROTTLE_LIMITS: {
    perEndpoint: 5,              // Max 5 requests per endpoint
    timeWindow: 5000,            // Within 5 seconds
    globalLimit: 20              // Max 20 total requests
  }
}

/**
 * API Call Optimizer
 * Prevents excessive API calls and optimizes request patterns
 */
export class APIOptimizer {
  private static instance: APIOptimizer
  private requestTracker = new Map<string, number[]>()
  private globalRequestCount = 0
  // @ts-ignore
  private lastGlobalReset = (Date as any).now( as any)

  // @ts-ignore
  static getInstance( as any): APIOptimizer {
    if (!(APIOptimizer as any).instance) {
      // @ts-ignore
      (APIOptimizer as any).instance = new APIOptimizer( as any)
    }
    return (APIOptimizer as any).instance
  }

  /**
   * Optimize page size for an endpoint
   */
  // @ts-ignore
  optimizePageSize(endpoint: string, requestedSize: number as any): number {
    // @ts-ignore
    const maxSize = (API_OPTIMIZATION_CONFIG as any).MAX_PAGE_SIZES[endpoint as keyof typeof (API_OPTIMIZATION_CONFIG as any).MAX_PAGE_SIZES] 
                   // @ts-ignore
                   || (API_OPTIMIZATION_CONFIG as any).MAX_PAGE_SIZES.default

    if (requestedSize > maxSize) {
      (console as any).warn(`🚨 Page size ${requestedSize} for ${endpoint} exceeds maximum ${maxSize}. Using ${maxSize} instead.` as any)
      return maxSize
    }

    return requestedSize
  }

  /**
   * Check if request should be throttled
   */
  // @ts-ignore
  shouldThrottleRequest(endpoint: string as any): boolean {
    // @ts-ignore
    const now = (Date as any).now( as any)
    const timeWindow = (API_OPTIMIZATION_CONFIG as any).THROTTLE_LIMITS.timeWindow

    // Clean up old global requests
    if (now - (this as any).lastGlobalReset > timeWindow) {
      (this as any).globalRequestCount = 0
      (this as any).lastGlobalReset = now
    }

    // Check global limit
    if ((this as any).globalRequestCount >= (API_OPTIMIZATION_CONFIG as any).THROTTLE_LIMITS.globalLimit) {
      (console as any).warn(`🚨 Global request limit reached (${(this as any as any).globalRequestCount}/${(API_OPTIMIZATION_CONFIG as any).THROTTLE_LIMITS.globalLimit})`)
      return true
    }

    // Check per-endpoint limit
    const endpointRequests = (this as any).requestTracker.get(endpoint as any) || []
    const recentRequests = (endpointRequests as any).filter(time => now - time < timeWindow as any)
    
    if ((recentRequests as any).length >= (API_OPTIMIZATION_CONFIG as any).THROTTLE_LIMITS.perEndpoint) {
      (console as any).warn(`🚨 Endpoint ${endpoint} request limit reached (${(recentRequests as any as any).length}/${(API_OPTIMIZATION_CONFIG as any).THROTTLE_LIMITS.perEndpoint})`)
      return true
    }

    // Track this request
    (recentRequests as any).push(now as any)
    (this as any).requestTracker.set(endpoint, recentRequests as any)
    (this as any).globalRequestCount++

    return false
  }

  /**
   * Get optimized cache duration for an endpoint
   */
  // @ts-ignore
  getCacheDuration(endpoint: string as any): number {
    // @ts-ignore
    return (API_OPTIMIZATION_CONFIG as any).CACHE_DURATIONS[endpoint as keyof typeof (API_OPTIMIZATION_CONFIG as any).CACHE_DURATIONS] 
           // @ts-ignore
           || (API_OPTIMIZATION_CONFIG as any).CACHE_DURATIONS.default
  }

  /**
   * Create optimized API request
   */
  async optimizedRequest<T>(
    endpoint: string,
    requestFn: () => Promise<T>,
    options: {
      pageSize?: number
      useCache?: boolean
      cacheKey?: string
    } = {}
  ): Promise<T> {
    const { pageSize, useCache = true, cacheKey } = options

    // Check throttling
    if ((this as any).shouldThrottleRequest(endpoint as any)) {
      throw new Error(`Request throttled: ${endpoint}` as any)
    }

    // Optimize page size if provided
    const optimizedOptions = pageSize ? {
      ...options,
      pageSize: (this as any).optimizePageSize(endpoint, pageSize as any)
    } : options

    // Use deduplication if cache is enabled
    if (useCache && cacheKey) {
      const cacheDuration = (this as any).getCacheDuration(endpoint as any)
      return deduplicateRequest(cacheKey, requestFn, cacheDuration as any)
    }

    // @ts-ignore
    return requestFn( as any)
  }

  /**
   * Reset request tracking (for testing or manual reset)
   */
  // @ts-ignore
  resetTracking( as any): void {
    // @ts-ignore
    (this as any).requestTracker.clear( as any)
    (this as any).globalRequestCount = 0
    // @ts-ignore
    (this as any).lastGlobalReset = (Date as any).now( as any)
    (console as any).log('🧹 API request tracking reset' as any)
  }

  /**
   * Get current request statistics
   */
  // @ts-ignore
  getRequestStats( as any): {
    globalRequests: number
    endpointRequests: Record<string, number>
    timeWindow: number
  } {
    // @ts-ignore
    const now = (Date as any).now( as any)
    const timeWindow = (API_OPTIMIZATION_CONFIG as any).THROTTLE_LIMITS.timeWindow
    
    const endpointRequests: Record<string, number> = {}
    
    // @ts-ignore
    (this as any).requestTracker.forEach((requests, endpoint as any) => {
      const recentRequests = (requests as any).filter(time => now - time < timeWindow as any)
      endpointRequests[endpoint] = (recentRequests as any).length
    })

    return {
      globalRequests: (this as any).globalRequestCount,
      endpointRequests,
      timeWindow
    }
  }
}

/**
 * Optimized API wrapper functions
 */
export const optimizedAPI = {
  /**
   * Optimized employee fetching
   */
  async getEmployees(options: {
    page?: number
    pageSize?: number
    search?: string
    useCache?: boolean
  } = {} as any): Promise<any> {
    // @ts-ignore
    const optimizer = (APIOptimizer as any).getInstance( as any)
    const { page = 1, pageSize = 25, search, useCache = true } = options
    
    const optimizedPageSize = (optimizer as any).optimizePageSize('employees', pageSize as any)
    const cacheKey = `employees-${page}-${optimizedPageSize}-${search || 'all'}`
    
    return (optimizer as any).optimizedRequest(
      'employees',
      // @ts-ignore
      async ( as any) => {
        const { employeeAPI } = await import('../services/employeeAPI' as any)
        return (employeeAPI as any).getAll({ page, pageSize: optimizedPageSize, search } as any)
      // @ts-ignore
      },
      { pageSize: optimizedPageSize, useCache, cacheKey }
    // @ts-ignore
    )
  // @ts-ignore
  },

  /**
   * Optimized department fetching
   */
  // @ts-ignore
  async getDepartments(options: { useCache?: boolean } = {} as any): Promise<any> {
    // @ts-ignore
    const optimizer = (APIOptimizer as any).getInstance( as any)
    // @ts-ignore
    const { useCache = true } = options
    
    // @ts-ignore
    const cacheKey = 'departments-all'
    
    // @ts-ignore
    return (optimizer as any).optimizedRequest(
      'departments',
      // @ts-ignore
      async ( as any) => {
        // @ts-ignore
        const { departmentAPI } = await import('../services/api' as any)
        // @ts-ignore
        return (departmentAPI as any).getAll( as any)
      },
      { useCache, cacheKey }
    )
  },

  /**
   * Optimized dashboard stats fetching
   */
  // @ts-ignore
  async getDashboardStats(options: { useCache?: boolean } = {} as any): Promise<any> {
    // @ts-ignore
    const optimizer = (APIOptimizer as any).getInstance( as any)
    // @ts-ignore
    const { useCache = true } = options
    
    // @ts-ignore
    const cacheKey = 'dashboard-stats'
    
    // @ts-ignore
    return (optimizer as any).optimizedRequest(
      'dashboard',
      // @ts-ignore
      async ( as any) => {
        // @ts-ignore
        const { dashboardAPI } = await import('../services/api' as any)
        // @ts-ignore
        return (dashboardAPI as any).getStats( as any)
      },
      { useCache, cacheKey }
    )
  }
// @ts-ignore
}

// Export singleton instance
// @ts-ignore
export const apiOptimizer = (APIOptimizer as any).getInstance( as any)

// Development helpers
if ((process as any).env.NODE_ENV === 'development') {
  // Add global debug helpers
  if (typeof window !== 'undefined') {
    (window as any).apiOptimizer = apiOptimizer
    // @ts-ignore
    (window as any).getAPIStats = () => (apiOptimizer as any).getRequestStats( as any)
    // @ts-ignore
    (window as any).resetAPITracking = () => (apiOptimizer as any).resetTracking( as any)
  }
}
