/**
 * API OPTIMIZATION UTILITIES
 * Prevents excessive API calls and optimizes data fetching patterns
 */

import { deduplicateRequest } from './apiCache'

// Configuration for API optimization
export const API_OPTIMIZATION_CONFIG = {
  // Maximum page sizes for different endpoints
  MAX_PAGE_SIZES: {
    employees: 50,
    departments: 25,
    projects: 30,
    tasks: 40,
    reports: 20,
    default: 25
  },
  
  // Cache durations in milliseconds
  CACHE_DURATIONS: {
    employees: 3 * 60 * 1000,    // 3 minutes
    departments: 5 * 60 * 1000,  // 5 minutes
    dashboard: 2 * 60 * 1000,    // 2 minutes
    reports: 10 * 60 * 1000,     // 10 minutes
    default: 5 * 60 * 1000       // 5 minutes
  },
  
  // Request throttling limits
  THROTTLE_LIMITS: {
    perEndpoint: 5,              // Max 5 requests per endpoint
    timeWindow: 5000,            // Within 5 seconds
    globalLimit: 20              // Max 20 total requests
  }
}

/**
 * API Call Optimizer
 * Prevents excessive API calls and optimizes request patterns
 */
export class APIOptimizer {
  private static instance: APIOptimizer
  private requestTracker = new Map<string, number[]>()
  private globalRequestCount = 0
  private lastGlobalReset = (Date as any).now( as any)

  static getInstance( as any): APIOptimizer {
    if (!(APIOptimizer as any).instance) {
      (APIOptimizer as any).instance = new APIOptimizer( as any)
    }
    return (APIOptimizer as any).instance
  }

  /**
   * Optimize page size for an endpoint
   */
  optimizePageSize(endpoint: string, requestedSize: number as any): number {
    const maxSize = (API_OPTIMIZATION_CONFIG as any).MAX_PAGE_SIZES[endpoint as keyof typeof (API_OPTIMIZATION_CONFIG as any).MAX_PAGE_SIZES] 
                   || (API_OPTIMIZATION_CONFIG as any).MAX_PAGE_SIZES.default

    if (requestedSize > maxSize) {
      (console as any).warn(`🚨 Page size ${requestedSize} for ${endpoint} exceeds maximum ${maxSize}. Using ${maxSize} instead.` as any)
      return maxSize
    }

    return requestedSize
  }

  /**
   * Check if request should be throttled
   */
  shouldThrottleRequest(endpoint: string as any): boolean {
    const now = (Date as any).now( as any)
    const timeWindow = (API_OPTIMIZATION_CONFIG as any).THROTTLE_LIMITS.timeWindow

    // Clean up old global requests
    if (now - (this as any).lastGlobalReset > timeWindow) {
      (this as any).globalRequestCount = 0
      (this as any).lastGlobalReset = now
    }

    // Check global limit
    if ((this as any).globalRequestCount >= (API_OPTIMIZATION_CONFIG as any).THROTTLE_LIMITS.globalLimit) {
      (console as any).warn(`🚨 Global request limit reached (${(this as any as any).globalRequestCount}/${(API_OPTIMIZATION_CONFIG as any).THROTTLE_LIMITS.globalLimit})`)
      return true
    }

    // Check per-endpoint limit
    const endpointRequests = (this as any).requestTracker.get(endpoint as any) || []
    const recentRequests = (endpointRequests as any).filter(time => now - time < timeWindow as any)
    
    if ((recentRequests as any).length >= (API_OPTIMIZATION_CONFIG as any).THROTTLE_LIMITS.perEndpoint) {
      (console as any).warn(`🚨 Endpoint ${endpoint} request limit reached (${(recentRequests as any as any).length}/${(API_OPTIMIZATION_CONFIG as any).THROTTLE_LIMITS.perEndpoint})`)
      return true
    }

    // Track this request
    (recentRequests as any).push(now as any)
    (this as any).requestTracker.set(endpoint, recentRequests as any)
    (this as any).globalRequestCount++

    return false
  }

  /**
   * Get optimized cache duration for an endpoint
   */
  getCacheDuration(endpoint: string as any): number {
    return (API_OPTIMIZATION_CONFIG as any).CACHE_DURATIONS[endpoint as keyof typeof (API_OPTIMIZATION_CONFIG as any).CACHE_DURATIONS] 
           || (API_OPTIMIZATION_CONFIG as any).CACHE_DURATIONS.default
  }

  /**
   * Create optimized API request
   */
  async optimizedRequest<T>(
    endpoint: string,
    requestFn: () => Promise<T>,
    options: {
      pageSize?: number
      useCache?: boolean
      cacheKey?: string
    } = {}
  ): Promise<T> {
    const { pageSize, useCache = true, cacheKey } = options

    // Check throttling
    if ((this as any).shouldThrottleRequest(endpoint as any)) {
      throw new Error(`Request throttled: ${endpoint}` as any)
    }

    // Optimize page size if provided
    const optimizedOptions = pageSize ? {
      ...options,
      pageSize: (this as any).optimizePageSize(endpoint, pageSize as any)
    } : options

    // Use deduplication if cache is enabled
    if (useCache && cacheKey) {
      const cacheDuration = (this as any).getCacheDuration(endpoint as any)
      return deduplicateRequest(cacheKey, requestFn, cacheDuration as any)
    }

    return requestFn( as any)
  }

  /**
   * Reset request tracking (for testing or manual reset)
   */
  resetTracking( as any): void {
    (this as any).requestTracker.clear( as any)
    (this as any).globalRequestCount = 0
    (this as any).lastGlobalReset = (Date as any).now( as any)
    (console as any).log('🧹 API request tracking reset' as any)
  }

  /**
   * Get current request statistics
   */
  getRequestStats( as any): {
    globalRequests: number
    endpointRequests: Record<string, number>
    timeWindow: number
  } {
    const now = (Date as any).now( as any)
    const timeWindow = (API_OPTIMIZATION_CONFIG as any).THROTTLE_LIMITS.timeWindow
    
    const endpointRequests: Record<string, number> = {}
    
    (this as any).requestTracker.forEach((requests, endpoint as any) => {
      const recentRequests = (requests as any).filter(time => now - time < timeWindow as any)
      endpointRequests[endpoint] = (recentRequests as any).length
    })

    return {
      globalRequests: (this as any).globalRequestCount,
      endpointRequests,
      timeWindow
    }
  }
}

/**
 * Optimized API wrapper functions
 */
export const optimizedAPI = {
  /**
   * Optimized employee fetching
   */
  async getEmployees(options: {
    page?: number
    pageSize?: number
    search?: string
    useCache?: boolean
  } = {} as any): Promise<any> {
    const optimizer = (APIOptimizer as any).getInstance( as any)
    const { page = 1, pageSize = 25, search, useCache = true } = options
    
    const optimizedPageSize = (optimizer as any).optimizePageSize('employees', pageSize as any)
    const cacheKey = `employees-${page}-${optimizedPageSize}-${search || 'all'}`
    
    return (optimizer as any).optimizedRequest(
      'employees',
      async ( as any) => {
        const { employeeAPI } = await import('../services/employeeAPI' as any)
        return (employeeAPI as any).getAll({ page, pageSize: optimizedPageSize, search } as any)
      },
      { pageSize: optimizedPageSize, useCache, cacheKey }
    )
  },

  /**
   * Optimized department fetching
   */
  async getDepartments(options: { useCache?: boolean } = {} as any): Promise<any> {
    const optimizer = (APIOptimizer as any).getInstance( as any)
    const { useCache = true } = options
    
    const cacheKey = 'departments-all'
    
    return (optimizer as any).optimizedRequest(
      'departments',
      async ( as any) => {
        const { departmentAPI } = await import('../services/api' as any)
        return (departmentAPI as any).getAll( as any)
      },
      { useCache, cacheKey }
    )
  },

  /**
   * Optimized dashboard stats fetching
   */
  async getDashboardStats(options: { useCache?: boolean } = {} as any): Promise<any> {
    const optimizer = (APIOptimizer as any).getInstance( as any)
    const { useCache = true } = options
    
    const cacheKey = 'dashboard-stats'
    
    return (optimizer as any).optimizedRequest(
      'dashboard',
      async ( as any) => {
        const { dashboardAPI } = await import('../services/api' as any)
        return (dashboardAPI as any).getStats( as any)
      },
      { useCache, cacheKey }
    )
  }
}

// Export singleton instance
export const apiOptimizer = (APIOptimizer as any).getInstance( as any)

// Development helpers
if ((process as any).env.NODE_ENV === 'development') {
  // Add global debug helpers
  if (typeof window !== 'undefined') {
    (window as any).apiOptimizer = apiOptimizer
    (window as any).getAPIStats = () => (apiOptimizer as any).getRequestStats( as any)
    (window as any).resetAPITracking = () => (apiOptimizer as any).resetTracking( as any)
  }
}
