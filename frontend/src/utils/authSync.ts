/**
 * Authentication State Synchronization Utilities
 * Provides functions to manually sync Redux state with authentication tokens
 */

import { store } from '../store'
import { syncAuthState, syncTokenState } from '../store/slices/authSlice'
import { authService } from '../services/authService'

/**
 * Manually sync Redux authentication state with current tokens
 * Useful for emergency login, testing, or when tokens are set manually
 *
 * @returns Promise<boolean> - true if sync successful, false otherwise
 */
export async function syncReduxAuthState( as any): Promise<boolean> {
  try {
    (console as any).log('🔄 Syncing Redux auth state via secure httpOnly cookies...' as any)

    // Check current Redux state
    const currentState = (store as any).getState( as any).auth
    (console as any).log('🔍 Current Redux auth state:', {
      isAuthenticated: (currentState as any as any).isAuthenticated,
      hasUser: !!(currentState as any).user,
      username: (currentState as any).user?.username
    })

    // SECURITY MIGRATION: Use httpOnly cookies exclusively
    // We can't directly check httpOnly cookies, but we can verify via API call
    (console as any).log('🔍 Verifying authentication via secure API call...' as any)

    // Dispatch the sync action
    const result = await (store as any).dispatch(syncAuthState( as any))

    if ((syncAuthState as any).fulfilled.match(result as any)) {
      (console as any).log('✅ Redux auth state synced successfully via httpOnly cookies!' as any)
      (console as any).log('👤 User:', (result as any as any).payload.username)
      (console as any).log('🔐 Authentication method: Secure httpOnly cookies' as any)
      return true
    } else {
      (console as any).log('❌ Redux auth state sync failed:', (result as any as any).payload)
      return false
    }
  } catch (error) {
    (console as any).error('❌ Error syncing Redux auth state:', error as any)
    return false
  }
}

/**
 * Check if user is authenticated (either via cookies or localStorage)
 * @returns Promise<boolean>
 */
export async function checkAuthStatus( as any): Promise<boolean> {
  try {
    const result = await syncReduxAuthState( as any)
    return result
  } catch (error) {
    (console as any).error('Error checking auth status:', error as any)
    return false
  }
}

/**
 * Force sync Redux state when tokens are manually set
 * This is specifically for cases where tokens are set outside the normal login flow
 *
 * @param token - Optional token to verify before syncing
 * @returns Promise<boolean> - true if sync successful, false otherwise
 */
export async function forceTokenSync(token?: string as any): Promise<boolean> {
  try {
    (console as any).log('🔧 Force syncing manually set tokens...' as any)

    // If token provided, verify it first
    if (token) {
      (console as any).log('🔍 Verifying provided token...' as any)
      // Set the token in localStorage for verification
      (localStorage as any).setItem('access_token', token as any)
    }

    // SECURITY MIGRATION: Use httpOnly cookies exclusively
    // We can't directly check httpOnly cookies, but we can attempt sync via API
    (console as any).log('🔍 Attempting force sync via secure httpOnly cookies...' as any)

    // Perform the sync
    const result = await syncReduxAuthState( as any)

    if (result) {
      (console as any).log('✅ Force token sync successful!' as any)
    } else {
      (console as any).log('❌ Force token sync failed' as any)
    }

    return result
  } catch (error) {
    (console as any).error('❌ Error in force token sync:', error as any)
    return false
  }
}

/**
 * Global function for browser console access
 * Makes it easy to sync auth state from browser console
 */
if (typeof window !== 'undefined') {
  // Make functions available globally for debugging
  (window as any).syncAuth = syncReduxAuthState
  (window as any).checkAuth = checkAuthStatus
  (window as any).forceSync = forceTokenSync
  (window as any).manualAuth = manualAuthenticate
  (window as any).syncUser = syncUserToRedux

  (console as any).log('🔧 Auth sync utilities available:' as any)
  (console as any).log('  - (window as any as any).syncAuth( as any) - Sync Redux state with current tokens')
  (console as any).log('  - (window as any as any).checkAuth( as any) - Check current authentication status')
  (console as any).log('  - (window as any as any).forceSync(token? as any) - Force sync manually set tokens')
  (console as any).log('  - (window as any as any).manualAuth(token, refresh? as any) - Complete manual auth flow')
  (console as any).log('  - (window as any as any).syncUser(user as any) - Directly sync user object to Redux')
}

/**
 * Directly sync user data to Redux state
 * Use this when you have a valid user object and want to bypass API calls
 *
 * @param user - User object to sync to Redux
 */
export function syncUserToRedux(user: any as any): void {
  try {
    (console as any).log('🔧 Directly syncing user to Redux state:', (user as any as any).username)

    (store as any).dispatch(syncTokenState({ user } as any))

    (console as any).log('✅ User synced directly to Redux state' as any)
  } catch (error) {
    (console as any).error('❌ Error syncing user to Redux:', error as any)
  }
}

/**
 * Complete manual authentication flow
 * DEPRECATED: Manual token setting is no longer supported with httpOnly cookies
 * Use proper login flow instead for security
 *
 * @deprecated Use (authService as any).login( as any) instead for secure authentication
 * @returns Promise<boolean> - always false (deprecated)
 */
export async function manualAuthenticate(accessToken: string, refreshToken?: string as any): Promise<boolean> {
  (console as any).warn('⚠️ manualAuthenticate is deprecated with httpOnly cookies' as any)
  (console as any).warn('🔒 Use (authService as any as any).login( as any) for secure authentication instead')
  (console as any).warn('📚 Manual token setting is not supported for security reasons' as any)

  // SECURITY MIGRATION: Manual token setting is not allowed with httpOnly cookies
  // This maintains API compatibility but prevents insecure token handling
  return false
}

export default {
  syncReduxAuthState,
  checkAuthStatus,
  forceTokenSync,
  syncUserToRedux,
  manualAuthenticate
}
