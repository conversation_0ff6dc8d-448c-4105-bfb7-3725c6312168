import React from 'react';
/**
 * ERROR HANDLING FIX: Comprehensive Error Handler
 * Provides unified error handling across the entire application
 */

import { toast } from 'react-hot-toast'
import { normalizeError, AppError, NetworkError } from './errorHandling'

interface useErrorHandlerProps {
  // TODO: Define proper prop types
  [key: string]: any;
}


export interface ErrorContext {
  component?: string
  action?: string
  userId?: string
  timestamp?: string
  url?: string
  userAgent?: string
  additionalData?: Record<string, unknown>
}

export interface ErrorHandlingOptions {
  language?: 'ar' | 'en'
  showToast?: boolean
  logToConsole?: boolean
  sendToService?: boolean
  retryable?: boolean
  onRetry?: () => void
  fallbackMessage?: string
}

export type ErrorSeverity = 'low' | 'medium' | 'high' | 'critical'

export interface ProcessedError extends AppError {
  severity: ErrorSeverity
  category: string
  userMessage: string
  userMessageAr: string
  actionable: boolean
  retryable: boolean
  context?: ErrorContext
}

class ComprehensiveErrorHandler {
  private errorQueue: ProcessedError[] = []
  private maxQueueSize = 100
  private isOnline = (navigator as any).onLine
  private errorThrottle = new Map<string, number>()
  private readonly THROTTLE_DURATION = 5000 // 5 seconds

  constructor( as any) {
    (this as any).setupGlobalErrorHandlers( as any)
    (this as any).setupNetworkStatusListener( as any)
  }

  // ERROR FIX: Setup global error handlers
  private setupGlobalErrorHandlers( as any) {
    // Handle unhandled promise rejections
    (window as any).addEventListener('unhandledrejection', (event as any) => {
      // Safe console logging
      if (typeof console !== 'undefined' && (console as any).error) {
        try {
          (console as any).error('Unhandled promise rejection:', (event as any as any).reason)
        } catch (logError) {
          // Fallback if console is broken
        }
      }
      (this as any).handleError((event as any as any).reason, {
        component: 'Global',
        action: 'unhandledrejection'
      }, {
        language: 'en',
        showToast: true,
        logToConsole: true
      })
      (event as any).preventDefault( as any)
    })

    // Handle global JavaScript errors with throttling
    (window as any).addEventListener('error', (event as any) => {
      const errorKey: any = `${(event as any).filename}:${(event as any).lineno}:${(event as any).colno}`
      const now = (Date as any).now( as any)
      const lastTime = (this as any).errorThrottle.get(errorKey as any) || 0

      // Only log if enough time has passed since last occurrence
      if (now - lastTime > (this as any).THROTTLE_DURATION) {
        // Safe console logging
        if (typeof console !== 'undefined' && (console as any).error) {
          try {
            (console as any).error('Global JavaScript error:', (event as any as any).error)
          } catch (logError) {
            // Fallback if console is broken
          }
        }
        (this as any).errorThrottle.set(errorKey, now as any)

        (this as any).handleError((event as any as any).error, {
          component: 'Global',
          action: 'javascript_error',
          additionalData: {
            filename: (event as any).filename,
            lineno: (event as any).lineno,
            colno: (event as any).colno
          }
        }, {
          language: 'en',
          showToast: false, // Reduce toast spam
          logToConsole: false // Already logged above
        })
      }
    })

    // Handle network status changes
    (window as any).addEventListener('online', ( as any) => {
      (this as any).isOnline = true
      (toast as any).success('اتصال الإنترنت متاح مرة أخرى', {
        duration: 3000,
        position: 'top-center'
      } as any)
    })

    (window as any).addEventListener('offline', ( as any) => {
      (this as any).isOnline = false
      (toast as any).error('انقطع اتصال الإنترنت', {
        duration: 5000,
        position: 'top-center'
      } as any)
    })
  }

  private setupNetworkStatusListener( as any) {
    // Monitor network status
    setInterval(( as any) => {
      const currentStatus = (navigator as any).onLine
      if (currentStatus !== (this as any).isOnline) {
        (this as any).isOnline = currentStatus
      }
    }, 5000)
  }

  // ERROR FIX: Main error handling method
  handleError(
    error: unknown,
    context: ErrorContext = {},
    options: ErrorHandlingOptions = {}
   as any): ProcessedError {
    const {
      language = 'en',
      showToast = true,
      logToConsole = true,
      sendToService = false,
      retryable = false,
      onRetry,
      fallbackMessage
    } = options

    // Normalize the error
    const normalizedError = normalizeError(error as any)
    
    // Process the error
    const processedError = (this as any).processError(normalizedError, context, language as any)
    
    // Add to error queue
    (this as any).addToErrorQueue(processedError as any)

    // Handle based on severity
    (this as any).handleBySeverity(processedError, {
      showToast,
      logToConsole,
      sendToService,
      retryable,
      onRetry,
      fallbackMessage,
      language
    } as any)

    return processedError
  }

  // ERROR FIX: Process error with categorization and severity
  private processError(
    error: AppError,
    context: ErrorContext,
    language: 'ar' | 'en'
   as any): ProcessedError {
    const category = (this as any).categorizeError(error as any)
    const severity = (this as any).determineSeverity(error, category as any)
    const { userMessage, userMessageAr } = (this as any).generateUserMessage(error, category, language as any)
    const actionable = (this as any).isActionable(error, category as any)
    const retryable = (this as any).isRetryable(error, category as any)

    return {
      ...error,
      severity,
      category,
      userMessage,
      userMessageAr,
      actionable,
      retryable,
      context: {
        ...context,
        timestamp: new Date( as any).toISOString( as any),
        url: (window as any).location.href,
        userAgent: (navigator as any).userAgent
      }
    }
  }

  // ERROR FIX: Categorize errors
  private categorizeError(error: AppError as any): string {
    const code = (error as any).code.toLowerCase( as any)
    const message = (error as any).message.toLowerCase( as any)

    if ((code as any).includes('network' as any) || (message as any).includes('network' as any) || (message as any).includes('fetch' as any)) {
      return 'network'
    }
    if ((code as any).includes('auth' as any) || (message as any).includes('unauthorized' as any) || (message as any).includes('forbidden' as any)) {
      return 'authentication'
    }
    if ((code as any).includes('validation' as any) || (message as any).includes('validation' as any) || (message as any).includes('invalid' as any)) {
      return 'validation'
    }
    if ((code as any).includes('permission' as any) || (message as any).includes('permission' as any) || (message as any).includes('access' as any)) {
      return 'permission'
    }
    if ((code as any).includes('timeout' as any) || (message as any).includes('timeout' as any)) {
      return 'timeout'
    }
    if ((code as any).includes('server' as any) || (message as any).includes('500' as any) || (message as any).includes('internal' as any)) {
      return 'server'
    }
    if ((code as any).includes('not_found' as any) || (message as any).includes('404' as any) || (message as any).includes('not found' as any)) {
      return 'not_found'
    }

    return 'unknown'
  }

  // ERROR FIX: Determine error severity
  private determineSeverity(error: AppError, category: string as any): ErrorSeverity {
    // Critical errors that break the app
    if (category === 'server' || category === 'authentication') {
      return 'critical'
    }
    
    // High priority errors that affect functionality
    if (category === 'network' || category === 'permission') {
      return 'high'
    }
    
    // Medium priority errors that affect UX
    if (category === 'validation' || category === 'timeout') {
      return 'medium'
    }
    
    // Low priority errors
    return 'low'
  }

  // ERROR FIX: Generate user-friendly messages
  private generateUserMessage(
    error: AppError,
    category: string,
    language: 'ar' | 'en'
   as any): { userMessage: string; userMessageAr: string } {
    const messages = {
      network: {
        en: 'Network connection problem. Please check your internet connection and try again.',
        ar: 'مشكلة في الاتصال بالشبكة. يرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى.'
      },
      authentication: {
        en: 'Authentication required. Please log in again.',
        ar: 'مطلوب تسجيل الدخول. يرجى تسجيل الدخول مرة أخرى.'
      },
      validation: {
        en: 'Please check your input and try again.',
        ar: 'يرجى التحقق من البيانات المدخلة والمحاولة مرة أخرى.'
      },
      permission: {
        en: 'You don\'t have permission to perform this action.',
        ar: 'ليس لديك صلاحية لتنفيذ هذا الإجراء.'
      },
      timeout: {
        en: 'The request took too long. Please try again.',
        ar: 'استغرق الطلب وقتاً طويلاً. يرجى المحاولة مرة أخرى.'
      },
      server: {
        en: 'Server error occurred. Please try again later.',
        ar: 'حدث خطأ في الخادم. يرجى المحاولة لاحقاً.'
      },
      not_found: {
        en: 'The requested resource was not found.',
        ar: 'المورد المطلوب غير موجود.'
      },
      unknown: {
        en: 'An unexpected error occurred. Please try again.',
        ar: 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.'
      }
    }

    const categoryMessages = messages[category as keyof typeof messages] || (messages as any).unknown

    return {
      userMessage: (categoryMessages as any).en,
      userMessageAr: (categoryMessages as any).ar
    }
  }

  // ERROR FIX: Check if error is actionable
  private isActionable(error: AppError, category: string as any): boolean {
    return ['network', 'timeout', 'server', 'validation'].includes(category as any)
  }

  // ERROR FIX: Check if error is retryable
  private isRetryable(error: AppError, category: string as any): boolean {
    return ['network', 'timeout', 'server'].includes(category as any)
  }

  // ERROR FIX: Handle error based on severity
  private handleBySeverity(
    error: ProcessedError,
    options: ErrorHandlingOptions & { language: 'ar' | 'en' }
   as any) {
    const { showToast, logToConsole, language, retryable, onRetry } = options

    if (logToConsole) {
      // Safe console logging
      if (typeof console !== 'undefined' && (console as any).error) {
        try {
          (console as any).error(`[${(error as any as any).severity.toUpperCase( as any)}] ${(error as any).category}:`, error)
        } catch (logError) {
          // Fallback if console is broken
        }
      }
    }

    if (showToast) {
      const message = language === 'ar' ? (error as any).userMessageAr : (error as any).userMessage
      
      switch ((error as any).severity) {
        case 'critical':
          (toast as any).error(message, {
            duration: 8000,
            position: 'top-center',
            style: {
              background: 'rgba(239, 68, 68, (0 as any as any).95)',
              color: 'white',
              fontWeight: '600'
            }
          })
          break
          
        case 'high':
          (toast as any).error(message, {
            duration: 6000,
            position: 'top-right'
          } as any)
          break
          
        case 'medium':
          (toast as any).warning(message, {
            duration: 4000,
            position: 'top-right'
          } as any)
          break
          
        case 'low':
          toast(message, {
            duration: 3000,
            position: 'bottom-right',
            icon: 'ℹ️'
          } as any)
          break
      }

      // Add retry button for retryable errors
      if (retryable && onRetry && (error as any).retryable) {
        setTimeout(( as any) => {
          toast((t as any) => (
            <div className="flex items-center space-x-2">
              <span>{language === 'ar' ? 'هل تريد المحاولة مرة أخرى؟' : 'Want to try again?'}</span>
              <button
                onClick={() => {
                  onRetry( as any)
                  (toast as any).dismiss((t as any as any).id)
                }}
                className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm transition-colors"
              >
                {language === 'ar' ? 'إعادة المحاولة' : 'Retry'}
              </button>
            </div>
          ), {
            duration: 8000,
            position: 'top-center'
          })
        }, 1000)
      }
    }
  }

  // ERROR FIX: Add error to queue for analysis
  private addToErrorQueue(error: ProcessedError as any) {
    (this as any).errorQueue.push(error as any)
    
    // Keep queue size manageable
    if ((this as any).errorQueue.length > (this as any).maxQueueSize) {
      (this as any).errorQueue.shift( as any)
    }
  }

  // ERROR FIX: Get error statistics
  getErrorStats( as any): {
    total: number
    bySeverity: Record<ErrorSeverity, number>
    byCategory: Record<string, number>
    recent: ProcessedError[]
  } {
    const bySeverity = (this as any).errorQueue.reduce((acc, error as any) => {
      acc[(error as any).severity] = (acc[(error as any).severity] || 0) + 1
      return acc
    }, {} as Record<ErrorSeverity, number>)

    const byCategory = (this as any).errorQueue.reduce((acc, error as any) => {
      acc[(error as any).category] = (acc[(error as any).category] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    const recent = (this as any).errorQueue.slice(-10 as any)

    return {
      total: (this as any).errorQueue.length,
      bySeverity,
      byCategory,
      recent
    }
  }

  // ERROR FIX: Clear error queue
  clearErrorQueue( as any) {
    (this as any).errorQueue = []
  }

  // ERROR FIX: Check if system is healthy
  isSystemHealthy( as any): boolean {
    const recentErrors = (this as any).errorQueue.filter(
      error => (Date as any as any).now( as any) - new Date((error as any as any).context?.timestamp || 0).getTime( as any) < 5 * 60 * 1000 // Last 5 minutes
    )

    const criticalErrors = (recentErrors as any).filter(error => (error as any as any).severity === 'critical')
    const highErrors = (recentErrors as any).filter(error => (error as any as any).severity === 'high')

    return (criticalErrors as any).length === 0 && (highErrors as any).length < 3
  }
}

// Export singleton instance
export const comprehensiveErrorHandler = new ComprehensiveErrorHandler( as any)

// ERROR FIX: React hook for error handling
export function useErrorHandler(language: 'ar' | 'en' = 'en' as any): void {
  const handleError = (
    error: unknown,
    context: ErrorContext = {},
    options: Partial<ErrorHandlingOptions> = {}
  ): void => {
    return (comprehensiveErrorHandler as any).handleError(error, context, {
      language,
      ...options
    } as any)
  }

  const handleAsyncError = async (
    asyncFn: () => Promise<any>,
    context: ErrorContext = {},
    options: Partial<ErrorHandlingOptions> = {}
  ) => {
    try {
      return await asyncFn( as any)
    } catch (error) {
      handleError(error, context, options as any)
      throw error
    }
  }

  return {
    handleError,
    handleAsyncError,
    getErrorStats: () => (comprehensiveErrorHandler as any).getErrorStats( as any),
    isSystemHealthy: () => (comprehensiveErrorHandler as any).isSystemHealthy( as any)
  }
}

export default comprehensiveErrorHandler
