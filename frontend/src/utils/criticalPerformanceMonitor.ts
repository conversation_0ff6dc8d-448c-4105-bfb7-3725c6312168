import React from 'react';
/**
 * CRITICAL PERFORMANCE MONITOR
 * Real-time monitoring to verify all bug fixes are working correctly
 */

interface CriticalMetrics {
  timestamp: Date
  apiResponseTimes: Record<string, number[]>
  memoryUsage: {
    current: number
    baseline: number
    peak: number
    leakDetected: boolean
  }
  resourceCounts: {
    intervals: number
    timeouts: number
    observers: number
    eventListeners: number
  }
  databaseQueries: Record<string, number>
  renderPerformance: {
    averageRenderTime: number
    slowRenders: number
    infiniteRenderDetected: boolean
  }
  overallScore: number
}

interface CriticalAlert {
  type: 'memory' | 'api' | 'database' | 'render' | 'resource'
  severity: 'critical' | 'high' | 'medium' | 'low'
  message: string
  value: number
  threshold: number
  timestamp: Date
  fix: string
}

export class CriticalPerformanceMonitor {
  private metrics: CriticalMetrics
  private alerts: CriticalAlert[] = []
  private isMonitoring = false
  private monitoringInterval: NodeJS.Timeout | null = null
  private memoryBaseline = 0
  private apiCallTimes = new Map<string, number[]>()
  private renderTimes: number[] = []

  // Critical performance thresholds based on our fixes
  private thresholds = {
    apiResponseTime: 500, // ms - should be much faster with N+1 fixes
    memoryLeakThreshold: 50 * 1024 * 1024, // 50MB - should be minimal with cleanup fixes
    maxDatabaseQueries: 10, // Should be low with select_related fixes
    maxRenderTime: 100, // ms - should be fast with React optimizations
    maxResourceCount: 20 // Should be low with proper cleanup
  }

  constructor() {
    this.metrics = this.initializeMetrics()
    this.memoryBaseline = this.getMemoryUsage()
  }

  startMonitoring(): void {
    if (this.isMonitoring) return

    this.isMonitoring = true
    console.log('🔍 Critical performance monitoring started - verifying bug fixes...')

    // Monitor every 5 seconds
    this.monitoringInterval = setInterval(() => {
      this.collectMetrics()
      this.checkCriticalThresholds()
    }, 5000)

    // Hook into fetch for API monitoring
    this.hookFetchAPI()
  }

  stopMonitoring(): void {
    if (!this.isMonitoring) return

    this.isMonitoring = false
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval)
      this.monitoringInterval = null
    }

    console.log('🛑 Critical performance monitoring stopped')
  }

  private initializeMetrics(): CriticalMetrics {
    return {
      timestamp: new Date(),
      apiResponseTimes: {},
      memoryUsage: {
        current: 0,
        baseline: 0,
        peak: 0,
        leakDetected: false
      },
      resourceCounts: {
        intervals: 0,
        timeouts: 0,
        observers: 0,
        eventListeners: 0
      },
      databaseQueries: {},
      renderPerformance: {
        averageRenderTime: 0,
        slowRenders: 0,
        infiniteRenderDetected: false
      },
      overallScore: 100
    }
  }

  private collectMetrics(): void {
    this.metrics.timestamp = new Date()
    
    // Memory metrics - verify memory leak fixes
    const currentMemory = this.getMemoryUsage()
    this.metrics.memoryUsage = {
      current: currentMemory,
      baseline: this.memoryBaseline,
      peak: Math.max(this.metrics.memoryUsage.peak, currentMemory),
      leakDetected: (currentMemory - this.memoryBaseline) > this.thresholds.memoryLeakThreshold
    }

    // Resource metrics - verify cleanup fixes
    try {
      const { MemoryLeakPrevention } = require('./performanceBugFixes')
      this.metrics.resourceCounts = MemoryLeakPrevention.getResourceCounts()
    } catch (error) {
      // Fallback if module not available
      this.metrics.resourceCounts = {
        intervals: 0,
        timeouts: 0,
        observers: 0,
        eventListeners: 0
      }
    }

    // API response times - verify N+1 query fixes
    this.metrics.apiResponseTimes = Object.fromEntries(this.apiCallTimes)

    // Calculate overall score
    this.metrics.overallScore = this.calculateOverallScore()
  }

  private checkCriticalThresholds(): void {
    // CRITICAL: Check memory leaks (Fix #4 verification)
    if (this.metrics.memoryUsage.leakDetected) {
      this.addCriticalAlert({
        type: 'memory',
        severity: 'critical',
        message: '🚨 MEMORY LEAK DETECTED - Fix #4 may have failed',
        value: this.metrics.memoryUsage.current - this.metrics.memoryUsage.baseline,
        threshold: this.thresholds.memoryLeakThreshold,
        timestamp: new Date(),
        fix: 'Check useEffect cleanup and MemoryLeakPrevention usage'
      })
    }

    // CRITICAL: Check API performance (Fix #3 verification)
    Object.entries(this.metrics.apiResponseTimes).forEach(([endpoint, times]) => {
      const avgTime = times.reduce((a, b) => a + b) / times.length
      if (avgTime > this.thresholds.apiResponseTime) {
        this.addCriticalAlert({
          type: 'api',
          severity: 'critical',
          message: `🚨 SLOW API DETECTED - Fix #3 may have failed: ${endpoint}`,
          value: avgTime,
          threshold: this.thresholds.apiResponseTime,
          timestamp: new Date(),
          fix: 'Verify select_related and prefetch_related optimizations'
        })
      }
    })

    // CRITICAL: Check resource counts (Fix #6 verification)
    const totalResources = Object.values(this.metrics.resourceCounts).reduce((a, b) => a + b)
    if (totalResources > this.thresholds.maxResourceCount) {
      this.addCriticalAlert({
        type: 'resource',
        severity: 'high',
        message: `🚨 HIGH RESOURCE COUNT - Fix #6 may have failed`,
        value: totalResources,
        threshold: this.thresholds.maxResourceCount,
        timestamp: new Date(),
        fix: 'Check useEffect cleanup and interval management'
      })
    }
  }

  private addCriticalAlert(alert: CriticalAlert): void {
    this.alerts.push(alert)
    
    // Keep only last 50 alerts
    if (this.alerts.length > 50) {
      this.alerts = this.alerts.slice(-50)
    }

    console.error(`🚨 CRITICAL PERFORMANCE ALERT: ${alert.message}`)
    console.error(`   Value: ${alert.value}, Threshold: ${alert.threshold}`)
    console.error(`   Fix: ${alert.fix}`)
  }

  private hookFetchAPI(): void {
    const originalFetch = window.fetch
    
    window.fetch = async (...args) => {
      const url = args[0].toString()
      const startTime = performance.now()
      
      try {
        const response = await originalFetch(...args)
        const endTime = performance.now()
        const responseTime = endTime - startTime
        
        // Track API response time
        if (!this.apiCallTimes.has(url)) {
          this.apiCallTimes.set(url, [])
        }
        
        const times = this.apiCallTimes.get(url)!
        times.push(responseTime)
        
        // Keep only last 10 measurements
        if (times.length > 10) {
          times.shift()
        }
        
        return response
      } catch (error) {
        throw error
      }
    }
  }

  private getMemoryUsage(): number {
    if ('memory' in performance) {
      return (performance as any).memory.usedJSHeapSize
    }
    return 0
  }

  private calculateOverallScore(): number {
    let score = 100

    // Memory score (40% weight) - most critical
    if (this.metrics.memoryUsage.leakDetected) {
      score -= 40
    }

    // API score (30% weight) - N+1 query fixes
    const slowApis = Object.values(this.metrics.apiResponseTimes).filter(times => {
      const avg = times.reduce((a, b) => a + b) / times.length
      return avg > this.thresholds.apiResponseTime
    }).length
    
    const totalApis = Object.keys(this.metrics.apiResponseTimes).length
    if (totalApis > 0) {
      score -= (slowApis / totalApis) * 30
    }

    // Resource score (30% weight) - cleanup fixes
    const totalResources = Object.values(this.metrics.resourceCounts).reduce((a, b) => a + b)
    if (totalResources > this.thresholds.maxResourceCount) {
      score -= 30
    }

    return Math.max(0, Math.round(score))
  }

  // Public API for testing and monitoring
  getMetrics(): CriticalMetrics {
    return { ...this.metrics }
  }

  getCriticalAlerts(): CriticalAlert[] {
    return this.alerts.filter(alert => alert.severity === 'critical')
  }

  /**
   * PERFORMANCE FIX: Manual report trigger for debugging
   * Use this instead of automatic logging to reduce console spam
   */
  logVerificationReport(): void {
    console.log(this.getFixVerificationReport())
  }

  getFixVerificationReport(): string {
    const criticalAlerts = this.getCriticalAlerts()
    const score = this.metrics.overallScore
    
    return `
🔍 CRITICAL BUG FIX VERIFICATION REPORT
======================================
Timestamp: ${this.metrics.timestamp.toISOString()}
Overall Fix Success Score: ${score}/100

🎯 FIX VERIFICATION STATUS:
${score >= 90 ? '✅' : score >= 70 ? '⚠️' : '❌'} Fix #1 (JWT Security): ${this.verifySecurityFix()}
${this.metrics.apiResponseTimes ? '✅' : '❌'} Fix #2 (API Deduplication): Monitoring active
${this.verifyDatabaseFix()} Fix #3 (N+1 Queries): ${this.getApiPerformanceStatus()}
${this.verifyMemoryFix()} Fix #4 (Memory Leaks): ${this.getMemoryStatus()}
${this.metrics.resourceCounts ? '✅' : '❌'} Fix #5 (WebSocket): Resource tracking active
${this.verifyCleanupFix()} Fix #6 (useEffect Cleanup): ${this.getResourceStatus()}

🚨 CRITICAL ISSUES (${criticalAlerts.length}):
${criticalAlerts.map(alert => `- ${alert.message}`).join('\n') || 'None detected'}

📊 CURRENT METRICS:
Memory Usage: ${Math.round(this.metrics.memoryUsage.current / 1024 / 1024)}MB
Active Resources: ${Object.values(this.metrics.resourceCounts).reduce((a, b) => a + b)}
API Endpoints: ${Object.keys(this.metrics.apiResponseTimes).length}
Average API Response: ${this.getAverageApiTime()}ms

${score >= 90 ? '🎉 ALL FIXES WORKING CORRECTLY!' : 
  score >= 70 ? '⚠️ SOME ISSUES DETECTED - REVIEW NEEDED' : 
  '🚨 CRITICAL ISSUES DETECTED - IMMEDIATE ACTION REQUIRED'}
    `
  }

  private verifySecurityFix(): string {
    // Check if authentication tokens are in localStorage (should be none after migration)
    const authTokenKeys = Object.keys(localStorage).filter(key =>
      key.toLowerCase().includes('access_token') ||
      key.toLowerCase().includes('refresh_token') ||
      key.toLowerCase().includes('jwt')
    )
    return authTokenKeys.length === 0 ? 'Secure (httpOnly)' : 'VULNERABLE'
  }

  private verifyDatabaseFix(): string {
    const avgApiTime = this.getAverageApiTime()
    return avgApiTime < this.thresholds.apiResponseTime ? '✅' : '❌'
  }

  private verifyMemoryFix(): string {
    return this.metrics.memoryUsage.leakDetected ? '❌' : '✅'
  }

  private verifyCleanupFix(): string {
    const totalResources = Object.values(this.metrics.resourceCounts).reduce((a, b) => a + b)
    return totalResources <= this.thresholds.maxResourceCount ? '✅' : '❌'
  }

  private getApiPerformanceStatus(): string {
    const avgTime = this.getAverageApiTime()
    return avgTime < 200 ? 'Excellent' : avgTime < 500 ? 'Good' : 'Poor'
  }

  private getMemoryStatus(): string {
    const memoryIncrease = this.metrics.memoryUsage.current - this.metrics.memoryUsage.baseline
    return memoryIncrease < 10 * 1024 * 1024 ? 'Stable' : 'Increasing'
  }

  private getResourceStatus(): string {
    const total = Object.values(this.metrics.resourceCounts).reduce((a, b) => a + b)
    return total < 10 ? 'Clean' : total < 20 ? 'Moderate' : 'High'
  }

  private getAverageApiTime(): number {
    const allTimes = Object.values(this.metrics.apiResponseTimes).flat()
    return allTimes.length > 0 ? allTimes.reduce((a, b) => a + b) / allTimes.length : 0
  }
}

// Export singleton instance
export const criticalPerformanceMonitor = new CriticalPerformanceMonitor()

// PERFORMANCE OPTIMIZATION: Conditional monitoring to reduce overhead
// Only start monitoring if explicitly requested or if there are performance issues
if (import.meta.env.DEV && typeof window !== 'undefined') {
  // Check if performance monitoring is needed
  const shouldMonitor = localStorage.getItem('enablePerformanceMonitoring') === 'true' ||
                       window.location.search.includes('debug=performance')

  if (shouldMonitor) {
    criticalPerformanceMonitor.startMonitoring()
    console.log('🔍 Critical performance monitoring enabled (debug mode)')

    // PERFORMANCE FIX: Only log if there are actual issues
    setInterval(() => {
      const criticalAlerts = criticalPerformanceMonitor.getCriticalAlerts()
      if (criticalAlerts.length > 0) {
        console.log(criticalPerformanceMonitor.getFixVerificationReport())
      }
    }, 10 * 60 * 1000) // 10 minutes instead of 5 minutes
  } else {
    console.log('🔇 Performance monitoring disabled for better performance. Enable with ?debug=performance')
  }
}
