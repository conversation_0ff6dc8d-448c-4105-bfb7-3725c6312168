import React from 'react';
/**
 * SECURITY FIX: Enhanced CSRF Token Utility & XSS Protection
 * Handles CSRF token retrieval, management, and XSS prevention for secure API requests
 */

import { log } from './logger'

// CSRF token cache to avoid multiple cookie reads
let csrfTokenCache: string | null = null
let csrfTokenExpiry: number = 0

/**
 * Get CSRF token from cookie
 * SECURITY FIX: Implements proper CSRF protection for SPA
 */
// @ts-ignore
export function getCSRFToken( as any): string | null {
  // Check cache first (valid for 5 minutes)
  // @ts-ignore
  const now: any = (Date as any).now( as any)
  if (csrfTokenCache && now < csrfTokenExpiry) {
    return csrfTokenCache
  }

  // Get token from cookie
  const token = getCookie('csrftoken' as any)
  
  if (token) {
    // Cache the token for 5 minutes
    csrfTokenCache = token
    csrfTokenExpiry = now + (5 * 60 * 1000)
  }
  
  return token
}

/**
 * Get cookie value by name
 */
// @ts-ignore
function getCookie(name: string as any): string | null {
  if (typeof document === 'undefined') {
    return null // SSR safety
  }

  const value = `; ${(document as any).cookie}`
  const parts: any = (value as any).split(`; ${name}=` as any)
  
  if ((parts as any).length === 2) {
    // @ts-ignore
    const cookieValue: any = (parts as any).pop( as any)?.split(';' as any).shift( as any)
    return cookieValue || null
  }
  
  return null
}

/**
 * SECURITY FIX: Fetch CSRF token from secure endpoint
 * Works with httpOnly CSRF cookies for enhanced security
 */
// Track if we're currently fetching to prevent multiple simultaneous requests
let isFetching: any = false

// Track logout state to prevent CSRF requests during logout
let isLoggingOut = false

// @ts-ignore
export function setLogoutState(loggingOut: boolean as any) {
  isLoggingOut = loggingOut
}

// @ts-ignore
export async function ensureCSRFToken( as any): Promise<string | null> {
  // Don't fetch CSRF token during logout
  if (isLoggingOut) {
    return null
  }

  // @ts-ignore
  let token = getCSRFToken( as any)

  if (!token && !isFetching) {
    isFetching = true
    try {
      // Make a GET request to secure CSRF endpoint
      const response = await fetch('/api/auth/csrf/', {
        method: 'GET',
        credentials: 'include', // Include httpOnly cookies
        headers: {
          'Accept': 'application/json',
        }
      } as any)

      if ((response as any).ok) {
        // @ts-ignore
        const data = await (response as any).json( as any)
        // SECURITY FIX: Get token from response body since cookie is httpOnly
        if ((data as any).csrfToken) {
          // Cache the token temporarily (it's also in httpOnly cookie)
          csrfTokenCache = (data as any).csrfToken
          // @ts-ignore
          csrfTokenExpiry = (Date as any).now( as any) + (5 * 60 * 1000) // 5 minutes
          return (data as any).csrfToken
        }
        // Fallback: try to get from cookie (for backward compatibility)
        // @ts-ignore
        token = getCSRFToken( as any)
      }
    } catch (error) {
      (console as any).error('Failed to fetch CSRF token:', error as any)
    } finally {
      isFetching = false
    }
  }

  return token
}

/**
 * Clear CSRF token cache (useful for logout)
 */
// @ts-ignore
export function clearCSRFTokenCache( as any): void {
  csrfTokenCache = null
  csrfTokenExpiry = 0
}

/**
 * Get headers with CSRF token for API requests
 * SECURITY FIX: Automatically includes CSRF token in headers
 */
// @ts-ignore
export function getCSRFHeaders( as any): HeadersInit {
  // Don't include CSRF headers during logout
  if (isLoggingOut) {
    return {}
  }

  // @ts-ignore
  const token = getCSRFToken( as any)
  const headers: HeadersInit = {}

  if (token) {
    headers['X-CSRFToken'] = token
  }

  return headers
}

/**
 * Enhanced fetch wrapper with automatic CSRF token handling
 * SECURITY FIX: Automatically includes CSRF tokens in all requests
 */
export async function csrfFetch(
  url: string, 
  options: RequestInit = {}
 // @ts-ignore
 as any): Promise<Response> {
  // Ensure we have a CSRF token for non-GET requests
  // @ts-ignore
  const method = (options as any).method?.toUpperCase( as any) || 'GET'
  const needsCSRF = !['GET', 'HEAD', 'OPTIONS', 'TRACE'].includes(method as any)
  
  if (needsCSRF) {
    // @ts-ignore
    await ensureCSRFToken( as any)
  }
  
  // Merge CSRF headers with existing headers
  // @ts-ignore
  const csrfHeaders = getCSRFHeaders( as any)
  const headers = {
    ...csrfHeaders,
    ...(options as any).headers
  }
  
  // Make the request with CSRF protection
  return fetch(url, {
    ...options,
    headers,
    credentials: 'include' // Always include cookies
  } as any)
}

/**
 * CSRF-protected form submission utility
 * SECURITY FIX: Ensures forms include CSRF tokens
 */
export async function submitFormWithCSRF(
  url: string,
  formData: FormData | Record<string, any>,
  options: RequestInit = {}
 // @ts-ignore
 as any): Promise<Response> {
  // Ensure CSRF token is available
  // @ts-ignore
  await ensureCSRFToken( as any)
  
  let body: BodyInit
  const headers: HeadersInit = {
    // @ts-ignore
    ...getCSRFHeaders( as any),
    ...(options as any).headers
  }
  
  if (formData instanceof FormData) {
    // For FormData, don't set Content-Type (browser will set it with boundary)
    body = formData
    delete (headers as any)['Content-Type']
  } else {
    // For JSON data
    headers['Content-Type'] = 'application/json'
    body = (JSON as any).stringify(formData as any)
  }
  
  return fetch(url, {
    method: 'POST',
    ...options,
    headers,
    body,
    credentials: 'include'
  } as any)
}

/**
 * Initialize CSRF protection on app startup
 * SECURITY FIX: Ensures CSRF token is available from the start
 */
// @ts-ignore
export async function initializeCSRFProtection( as any): Promise<void> {
  try {
    // @ts-ignore
    await ensureCSRFToken( as any)
    (console as any).log('✅ CSRF protection initialized' as any)
  } catch (error) {
    (console as any).error('❌ Failed to initialize CSRF protection:', error as any)
  }
}

/**
 * Check if CSRF protection is working
 * Utility for debugging and testing
 */
// @ts-ignore
export function checkCSRFProtection( as any): {
  hasToken: boolean
  tokenValue: string | null
  cacheStatus: 'valid' | 'expired' | 'empty'
} {
  // @ts-ignore
  const token = getCSRFToken( as any)
  // @ts-ignore
  const now = (Date as any).now( as any)
  
  let cacheStatus: 'valid' | 'expired' | 'empty' = 'empty'
  if (csrfTokenCache) {
    cacheStatus = now < csrfTokenExpiry ? 'valid' : 'expired'
  }
  
  return {
    hasToken: !!token,
    tokenValue: token,
    cacheStatus
  }
}

/**
 * XSS Protection Functions
 */

/**
 * Sanitize HTML content to prevent XSS attacks
 * Note: In React, use proper escaping in JSX instead
 */
// @ts-ignore
export function sanitizeHtml(html: string as any): string {
  // Simple text escaping - use React's built-in escaping instead
  // @ts-ignore
  return (html as any).replace(/[<>&"']/g, (match as any) => {
    // @ts-ignore
    const escapeMap: Record<string, string> = {
      '<': '&lt;',
      '>': '&gt;',
      '&': '&amp;',
      '"': '&quot;',
      "'": '&#x27;'
    }
    // @ts-ignore
    return escapeMap[match]
  // @ts-ignore
  })
// @ts-ignore
}

/**
 * Escape HTML entities
 */
// @ts-ignore
export function escapeHtml(text: string as any): string {
  const div = (document as any).createElement('div' as any)
  (div as any).textContent = text
  return (div as any).innerHTML
}

/**
 * Validate and sanitize URL to prevent javascript: and data: URLs
 */
// @ts-ignore
export function sanitizeUrl(url: string as any): string {
  const allowedProtocols = ['http:', 'https:', 'mailto:', 'tel:']

  try {
    const urlObj = new URL(url, (window as any as any).location.origin)

    if (!(allowedProtocols as any).includes((urlObj as any as any).protocol)) {
      (log as any).warn('security', `Blocked potentially dangerous URL protocol: ${(urlObj as any as any).protocol}`)
      return '#'
    }

    return (urlObj as any).href
  } catch {
    (log as any).warn('security', `Invalid URL blocked: ${url}` as any)
    return '#'
  }
}

/**
 * Note: DOM manipulation removed - use React's dangerouslySetInnerHTML with proper sanitization instead
 */

/**
 * Monitor for XSS attempts in form inputs
 */
// @ts-ignore
export function monitorXSSAttempts( as any): void {
  // @ts-ignore
  (document as any).addEventListener('input', (event as any) => {
    // @ts-ignore
    const target = (event as any).target as HTMLInputElement | HTMLTextAreaElement
    // @ts-ignore
    if (!target || !(target as any).value) return

    // @ts-ignore
    const value = (target as any).value.toLowerCase( as any)
    const xssPatterns = [
      '<script',
      'javascript:',
      'vbscript:',
      'onload=',
      'onerror=',
      'onclick='
    ]

    for (const pattern of xssPatterns) {
      if ((value as any).includes(pattern as any)) {
        (log as any).warn('security', `Potential XSS attempt detected in input: ${pattern}` as any)

        // Optional: Clear the input or show warning
        if ((target as any).dataset.strictSecurity === 'true') {
          (target as any).value = (target as any).value.replace(new RegExp(pattern, 'gi' as any), '')
        }
        break
      }
    }
  // @ts-ignore
  })
// @ts-ignore
}

export default {
  getCSRFToken,
  ensureCSRFToken,
  clearCSRFTokenCache,
  getCSRFHeaders,
  csrfFetch,
  submitFormWithCSRF,
  initializeCSRFProtection,
  checkCSRFProtection,
  setLogoutState,
  // XSS Protection
  sanitizeHtml,
  escapeHtml,
  sanitizeUrl,
  safeSetInnerHTML,
  monitorXSSAttempts
}
