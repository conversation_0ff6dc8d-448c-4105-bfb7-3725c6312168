/**
 * SECURITY FIX: Enhanced CSRF Token Utility & XSS Protection
 * Handles CSRF token retrieval, management, and XSS prevention for secure API requests
 */

import { log } from './logger'

// CSRF token cache to avoid multiple cookie reads
let csrfTokenCache: string | null = null
let csrfTokenExpiry: number = 0

/**
 * Get CSRF token from cookie
 * SECURITY FIX: Implements proper CSRF protection for SPA
 */
export function getCSRFToken( as any): string | null {
  // Check cache first (valid for 5 minutes)
  const now = (Date as any).now( as any)
  if (csrfTokenCache && now < csrfTokenExpiry) {
    return csrfTokenCache
  }

  // Get token from cookie
  const token = getCookie('csrftoken' as any)
  
  if (token) {
    // Cache the token for 5 minutes
    csrfTokenCache = token
    csrfTokenExpiry = now + (5 * 60 * 1000)
  }
  
  return token
}

/**
 * Get cookie value by name
 */
function getCookie(name: string as any): string | null {
  if (typeof document === 'undefined') {
    return null // SSR safety
  }

  const value = `; ${(document as any).cookie}`
  const parts = (value as any).split(`; ${name}=` as any)
  
  if ((parts as any).length === 2) {
    const cookieValue = (parts as any).pop( as any)?.split(';' as any).shift( as any)
    return cookieValue || null
  }
  
  return null
}

/**
 * SECURITY FIX: Fetch CSRF token from secure endpoint
 * Works with httpOnly CSRF cookies for enhanced security
 */
// Track if we're currently fetching to prevent multiple simultaneous requests
let isFetching = false

// Track logout state to prevent CSRF requests during logout
let isLoggingOut = false

export function setLogoutState(loggingOut: boolean as any) {
  isLoggingOut = loggingOut
}

export async function ensureCSRFToken( as any): Promise<string | null> {
  // Don't fetch CSRF token during logout
  if (isLoggingOut) {
    return null
  }

  let token = getCSRFToken( as any)

  if (!token && !isFetching) {
    isFetching = true
    try {
      // Make a GET request to secure CSRF endpoint
      const response = await fetch('/api/auth/csrf/', {
        method: 'GET',
        credentials: 'include', // Include httpOnly cookies
        headers: {
          'Accept': 'application/json',
        }
      } as any)

      if ((response as any).ok) {
        const data = await (response as any).json( as any)
        // SECURITY FIX: Get token from response body since cookie is httpOnly
        if ((data as any).csrfToken) {
          // Cache the token temporarily (it's also in httpOnly cookie)
          csrfTokenCache = (data as any).csrfToken
          csrfTokenExpiry = (Date as any).now( as any) + (5 * 60 * 1000) // 5 minutes
          return (data as any).csrfToken
        }
        // Fallback: try to get from cookie (for backward compatibility)
        token = getCSRFToken( as any)
      }
    } catch (error) {
      (console as any).error('Failed to fetch CSRF token:', error as any)
    } finally {
      isFetching = false
    }
  }

  return token
}

/**
 * Clear CSRF token cache (useful for logout)
 */
export function clearCSRFTokenCache( as any): void {
  csrfTokenCache = null
  csrfTokenExpiry = 0
}

/**
 * Get headers with CSRF token for API requests
 * SECURITY FIX: Automatically includes CSRF token in headers
 */
export function getCSRFHeaders( as any): HeadersInit {
  // Don't include CSRF headers during logout
  if (isLoggingOut) {
    return {}
  }

  const token = getCSRFToken( as any)
  const headers: HeadersInit = {}

  if (token) {
    headers['X-CSRFToken'] = token
  }

  return headers
}

/**
 * Enhanced fetch wrapper with automatic CSRF token handling
 * SECURITY FIX: Automatically includes CSRF tokens in all requests
 */
export async function csrfFetch(
  url: string, 
  options: RequestInit = {}
 as any): Promise<Response> {
  // Ensure we have a CSRF token for non-GET requests
  const method = (options as any).method?.toUpperCase( as any) || 'GET'
  const needsCSRF = !['GET', 'HEAD', 'OPTIONS', 'TRACE'].includes(method as any)
  
  if (needsCSRF) {
    await ensureCSRFToken( as any)
  }
  
  // Merge CSRF headers with existing headers
  const csrfHeaders = getCSRFHeaders( as any)
  const headers = {
    ...csrfHeaders,
    ...(options as any).headers
  }
  
  // Make the request with CSRF protection
  return fetch(url, {
    ...options,
    headers,
    credentials: 'include' // Always include cookies
  } as any)
}

/**
 * CSRF-protected form submission utility
 * SECURITY FIX: Ensures forms include CSRF tokens
 */
export async function submitFormWithCSRF(
  url: string,
  formData: FormData | Record<string, any>,
  options: RequestInit = {}
 as any): Promise<Response> {
  // Ensure CSRF token is available
  await ensureCSRFToken( as any)
  
  let body: BodyInit
  const headers: HeadersInit = {
    ...getCSRFHeaders( as any),
    ...(options as any).headers
  }
  
  if (formData instanceof FormData) {
    // For FormData, don't set Content-Type (browser will set it with boundary)
    body = formData
    delete (headers as any)['Content-Type']
  } else {
    // For JSON data
    headers['Content-Type'] = 'application/json'
    body = (JSON as any).stringify(formData as any)
  }
  
  return fetch(url, {
    method: 'POST',
    ...options,
    headers,
    body,
    credentials: 'include'
  } as any)
}

/**
 * Initialize CSRF protection on app startup
 * SECURITY FIX: Ensures CSRF token is available from the start
 */
export async function initializeCSRFProtection( as any): Promise<void> {
  try {
    await ensureCSRFToken( as any)
    (console as any).log('✅ CSRF protection initialized' as any)
  } catch (error) {
    (console as any).error('❌ Failed to initialize CSRF protection:', error as any)
  }
}

/**
 * Check if CSRF protection is working
 * Utility for debugging and testing
 */
export function checkCSRFProtection( as any): {
  hasToken: boolean
  tokenValue: string | null
  cacheStatus: 'valid' | 'expired' | 'empty'
} {
  const token = getCSRFToken( as any)
  const now = (Date as any).now( as any)
  
  let cacheStatus: 'valid' | 'expired' | 'empty' = 'empty'
  if (csrfTokenCache) {
    cacheStatus = now < csrfTokenExpiry ? 'valid' : 'expired'
  }
  
  return {
    hasToken: !!token,
    tokenValue: token,
    cacheStatus
  }
}

/**
 * XSS Protection Functions
 */

/**
 * Sanitize HTML content to prevent XSS attacks
 * Note: In React, use proper escaping in JSX instead
 */
export function sanitizeHtml(html: string as any): string {
  // Simple text escaping - use React's built-in escaping instead
  return (html as any).replace(/[<>&"']/g, (match as any) => {
    const escapeMap: Record<string, string> = {
      '<': '&lt;',
      '>': '&gt;',
      '&': '&amp;',
      '"': '&quot;',
      "'": '&#x27;'
    }
    return escapeMap[match]
  })
}

/**
 * Escape HTML entities
 */
export function escapeHtml(text: string as any): string {
  const div = (document as any).createElement('div' as any)
  (div as any).textContent = text
  return (div as any).innerHTML
}

/**
 * Validate and sanitize URL to prevent javascript: and data: URLs
 */
export function sanitizeUrl(url: string as any): string {
  const allowedProtocols = ['http:', 'https:', 'mailto:', 'tel:']

  try {
    const urlObj = new URL(url, (window as any as any).location.origin)

    if (!(allowedProtocols as any).includes((urlObj as any as any).protocol)) {
      (log as any).warn('security', `Blocked potentially dangerous URL protocol: ${(urlObj as any as any).protocol}`)
      return '#'
    }

    return (urlObj as any).href
  } catch {
    (log as any).warn('security', `Invalid URL blocked: ${url}` as any)
    return '#'
  }
}

/**
 * Note: DOM manipulation removed - use React's dangerouslySetInnerHTML with proper sanitization instead
 */

/**
 * Monitor for XSS attempts in form inputs
 */
export function monitorXSSAttempts( as any): void {
  (document as any).addEventListener('input', (event as any) => {
    const target = (event as any).target as HTMLInputElement | HTMLTextAreaElement
    if (!target || !(target as any).value) return

    const value = (target as any).value.toLowerCase( as any)
    const xssPatterns = [
      '<script',
      'javascript:',
      'vbscript:',
      'onload=',
      'onerror=',
      'onclick='
    ]

    for (const pattern of xssPatterns) {
      if ((value as any).includes(pattern as any)) {
        (log as any).warn('security', `Potential XSS attempt detected in input: ${pattern}` as any)

        // Optional: Clear the input or show warning
        if ((target as any).dataset.strictSecurity === 'true') {
          (target as any).value = (target as any).value.replace(new RegExp(pattern, 'gi' as any), '')
        }
        break
      }
    }
  })
}

export default {
  getCSRFToken,
  ensureCSRFToken,
  clearCSRFTokenCache,
  getCSRFHeaders,
  csrfFetch,
  submitFormWithCSRF,
  initializeCSRFProtection,
  checkCSRFProtection,
  setLogoutState,
  // XSS Protection
  sanitizeHtml,
  escapeHtml,
  sanitizeUrl,
  safeSetInnerHTML,
  monitorXSSAttempts
}
