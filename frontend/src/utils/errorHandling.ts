import React from 'react';
/**
 * Safe Error Handling Utilities
 * Type-safe error handling with proper error boundaries
 */

import { isError, isHttpError, isApiErrorResponse, isString, isObject } from './typeGuards'

// Error types
export interface AppError {
  code: string
  message: string
  details?: Record<string, unknown>
  timestamp: string
  stack?: string
}

export interface ValidationError {
  field: string
  message: string
  code: string
  value?: unknown
}

export interface NetworkError extends AppError {
  status: number
  url?: string
  method?: string
}

// Error creation utilities
export const createAppError: any = (
  code: string,
  message: string,
  details?: Record<string, unknown>
): AppError => ({
  code,
  message,
  details,
  timestamp: new Date().toISOString(),
})

export const createValidationError = (
  field: string,
  message: string,
  code: string,
  value?: unknown
): ValidationError => ({
  field,
  message,
  code,
  value,
})

export const createNetworkError = (
  status: number,
  message: string,
  url?: string,
  method?: string
): NetworkError => ({
  code: 'NETWORK_ERROR',
  message,
  status,
  url,
  method,
  timestamp: new Date().toISOString(),
})

// Safe error message extraction
export const getErrorMessage = (error: unknown): string => {
  // Handle standard Error objects
  if (isError(error)) {
    return error.message
  }

  // Handle API error responses
  if (isApiErrorResponse(error)) {
    return error.error.message
  }

  // Handle HTTP errors
  if (isHttpError(error)) {
    return error.message
  }

  // Handle objects with message property
  if (isObject(error) && 'message' in error && isString(error.message)) {
    return error.message
  }

  // Handle string errors
  if (isString(error)) {
    return error
  }

  // Fallback for unknown error types
  return 'An unexpected error occurred'
}

// Safe error code extraction
export const getErrorCode = (error: unknown): string => {
  // Handle API error responses
  if (isApiErrorResponse(error)) {
    return error.error.code
  }

  // Handle objects with code property
  if (isObject(error) && 'code' in error && isString(error.code)) {
    return error.code
  }

  // Handle HTTP errors
  if (isHttpError(error)) {
    return `HTTP_${error.status}`
  }

  // Handle standard Error objects
  if (isError(error)) {
    return error.name || 'ERROR'
  }

  // Fallback
  return 'UNKNOWN_ERROR'
}

// Safe error details extraction
export const getErrorDetails = (error: unknown): Record<string, unknown> | undefined => {
  // Handle API error responses
  if (isApiErrorResponse(error)) {
    return error.error.details
  }

  // Handle objects with details property
  if (isObject(error) && 'details' in error && isObject(error.details)) {
    return error.details
  }

  // Handle Error objects with additional properties
  if (isError(error)) {
    const details: Record<string, unknown> = {}
    
    // Add stack trace if available
    if (error.stack) {
      details.stack = error.stack
    }

    // Add any additional enumerable properties
    Object.getOwnPropertyNames(error).forEach(key => {
      if (key !== 'name' && key !== 'message' && key !== 'stack') {
        details[key] = (error as unknown as Record<string, unknown>)[key]
      }
    })

    return Object.keys(details).length > 0 ? details : undefined
  }

  return undefined
}

// Error transformation utilities
export const normalizeError = (error: unknown): AppError => {
  return createAppError(
    getErrorCode(error),
    getErrorMessage(error),
    getErrorDetails(error)
  )
}

// Async error handling wrapper
export const withErrorHandling = <T extends unknown[], R>(
  fn: (...args: T) => Promise<R>,
  errorHandler?: (error: AppError) => void
) => {
  return async (...args: T): Promise<R | null> => {
    try {
      return await fn(...args)
    } catch (error) {
      const normalizedError = normalizeError(error)
      
      if (errorHandler) {
        errorHandler(normalizedError)
      } else {
        console.error('Unhandled error:', normalizedError)
      }
      
      return null
    }
  }
}

// Sync error handling wrapper
export const withSyncErrorHandling = <T extends unknown[], R>(
  fn: (...args: T) => R,
  errorHandler?: (error: AppError) => void
) => {
  return (...args: T): R | null => {
    try {
      return fn(...args)
    } catch (error) {
      const normalizedError = normalizeError(error)
      
      if (errorHandler) {
        errorHandler(normalizedError)
      } else {
        console.error('Unhandled error:', normalizedError)
      }
      
      return null
    }
  }
}

// Validation error handling
export const collectValidationErrors = (
  data: Record<string, unknown>,
  validators: Record<string, (value: unknown) => string | null>
): ValidationError[] => {
  const errors: ValidationError[] = []

  Object.entries(validators).forEach(([field, validator]) => {
    const value = data[field]
    const errorMessage = validator(value)
    
    if (errorMessage) {
      errors.push(createValidationError(
        field,
        errorMessage,
        'VALIDATION_ERROR',
        value
      ))
    }
  })

  return errors
}

// Network error handling
export const handleNetworkError = (error: unknown, url?: string, method?: string): NetworkError => {
  // Handle fetch API errors
  if (error instanceof TypeError && error.message.includes('fetch')) {
    return createNetworkError(0, 'Network connection failed', url, method)
  }

  // Handle HTTP errors
  if (isHttpError(error)) {
    return createNetworkError(error.status, error.message, url, method)
  }

  // Handle API error responses
  if (isApiErrorResponse(error)) {
    return createNetworkError(error.status, error.error.message, url, method)
  }

  // Fallback
  return createNetworkError(500, getErrorMessage(error), url, method)
}

// Error boundary utilities for React
export const createErrorBoundaryHandler = (
  onError?: (error: AppError, errorInfo?: Record<string, unknown>) => void
) => {
  return (error: Error, errorInfo: Record<string, unknown>) => {
    const normalizedError = normalizeError(error)
    
    if (onError) {
      onError(normalizedError, errorInfo)
    } else {
      console.error('Error boundary caught error:', normalizedError, errorInfo)
    }
  }
}

// Retry utilities with error handling
export const withRetry = <T extends unknown[], R>(
  fn: (...args: T) => Promise<R>,
  maxRetries: number = 3,
  delay: number = 1000,
  shouldRetry?: (error: AppError, attempt: number) => boolean
) => {
  return async (...args: T): Promise<R> => {
    let lastError: AppError | null = null

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await fn(...args)
      } catch (error) {
        lastError = normalizeError(error)

        // Check if we should retry
        if (attempt === maxRetries || (shouldRetry && !shouldRetry(lastError, attempt))) {
          throw lastError
        }

        // Wait before retrying
        if (delay > 0) {
          await new Promise(resolve => setTimeout(resolve, delay * attempt))
        }
      }
    }

    // This should never be reached, but TypeScript requires it
    throw lastError || createAppError('RETRY_FAILED', 'All retry attempts failed')
  }
}

// Error logging utilities
export const logError = (error: AppError, context?: Record<string, unknown>) => {
  const logData = {
    ...error,
    context,
    userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : undefined,
    url: typeof window !== 'undefined' ? window.location.href : undefined,
  }

  // In development, log to console
  if (process.env.NODE_ENV === 'development') {
    console.error('Application Error:', logData)
  }

  // In production, you might want to send to an error tracking service
  // Example: Sentry, LogRocket, etc.
}

// Safe JSON parsing with error handling
export const safeJsonParse = <T = unknown>(
  json: string,
  fallback?: T
): T | null => {
  try {
    return JSON.parse(json) as T
  } catch (error) {
    console.warn('Failed to parse JSON:', getErrorMessage(error))
    return fallback ?? null
  }
}

// Safe localStorage operations with error handling
export const safeLocalStorageGet = (key: string, fallback?: string): string | null => {
  try {
    return localStorage.getItem(key) ?? fallback ?? null
  } catch (error) {
    console.warn('Failed to read from localStorage:', getErrorMessage(error))
    return fallback ?? null
  }
}

export const safeLocalStorageSet = (key: string, value: string): boolean => {
  try {
    localStorage.setItem(key, value)
    return true
  } catch (error) {
    console.warn('Failed to write to localStorage:', getErrorMessage(error))
    return false
  }
}

export const safeLocalStorageRemove = (key: string): boolean => {
  try {
    localStorage.removeItem(key)
    return true
  } catch (error) {
    console.warn('Failed to remove from localStorage:', getErrorMessage(error))
    return false
  }
}
