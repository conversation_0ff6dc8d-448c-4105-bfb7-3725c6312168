import React from 'react';
/**
 * Advanced Export Utilities for Business Intelligence
 * Supports CSV, Excel, and PDF exports with professional formatting
 */

import jsPDF from 'jspdf'
import autoTable from 'jspdf-autotable'
import * as XLSX from 'xlsx'

export interface ExportData {
  id: number
  name: string
  name_ar: string
  current_value: {
    value: number
    formatted: string
  }
  target_value: number
  target_achievement: number
  category: {
    name: string
    name_ar: string
  }
  status: string
  unit: string
  unit_ar: string
}

export interface ExportOptions {
  format: 'csv' | 'excel' | 'pdf'
  language: 'ar' | 'en'
  includeCharts?: boolean
  title?: string
  subtitle?: string
  companyName?: string
  reportDate?: string
}

/**
 * Export KPI data to CSV format
 */
export const exportToCSV: any = (data: ExportData[], options: ExportOptions): void => {
  const { language } = options
  
  // Define headers based on language
  const headers = language === 'ar' 
    ? ['اسم المؤشر', 'القيمة الحالية', 'القيمة المستهدفة', 'نسبة الإنجاز', 'الفئة', 'الحالة', 'الوحدة']
    : ['KPI Name', 'Current Value', 'Target Value', 'Achievement %', 'Category', 'Status', 'Unit']
  
  // Prepare CSV content
  const csvContent = [
    headers.join(','),
    ...data.map(kpi => [
      `"${language === 'ar' ? kpi.name_ar : kpi.name}"`,
      kpi.current_value.value,
      kpi.target_value,
      `${kpi.target_achievement.toFixed(1)}%`,
      `"${language === 'ar' ? kpi.category.name_ar : kpi.category.name}"`,
      kpi.status,
      `"${language === 'ar' ? kpi.unit_ar : kpi.unit}"`
    ].join(','))
  ].join('\n')
  
  // Create and download file
  const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' })
  const link: any = document.createElement('a')
  const url = URL.createObjectURL(blob)
  
  link.setAttribute('href', url)
  link.setAttribute('download', `kpi-report-${new Date().toISOString().split('T')[0]}.csv`)
  link.style.visibility = 'hidden'
  
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

/**
 * Export KPI data to Excel format with advanced formatting
 */
export const exportToExcel = (data: ExportData[], options: ExportOptions): void => {
  const { language, title = 'KPI Report', companyName = 'نمو - نظام إدارة المؤسسات' } = options
  
  // Create workbook
  const wb = XLSX.utils.book_new()
  
  // Prepare data for Excel
  const headers = language === 'ar' 
    ? ['اسم المؤشر', 'القيمة الحالية', 'القيمة المستهدفة', 'نسبة الإنجاز (%)', 'الفئة', 'الحالة', 'الوحدة']
    : ['KPI Name', 'Current Value', 'Target Value', 'Achievement (%)', 'Category', 'Status', 'Unit']
  
  const excelData = [
    // Title row
    [language === 'ar' ? 'تقرير مؤشرات الأداء الرئيسية' : 'Key Performance Indicators Report'],
    [companyName],
    [`${language === 'ar' ? 'تاريخ التقرير' : 'Report Date'}: ${new Date().toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}`],
    [], // Empty row
    headers,
    ...data.map(kpi => [
      language === 'ar' ? kpi.name_ar : kpi.name,
      kpi.current_value.formatted,
      `${kpi.target_value} ${language === 'ar' ? kpi.unit_ar : kpi.unit}`,
      typeof kpi.target_achievement === 'number' ? kpi.target_achievement.toFixed(1) : '0.0',
      language === 'ar' ? kpi.category.name_ar : kpi.category.name,
      kpi.status,
      language === 'ar' ? kpi.unit_ar : kpi.unit
    ])
  ]
  
  // Create worksheet
  const ws = XLSX.utils.aoa_to_sheet(excelData)
  
  // Set column widths
  ws['!cols'] = [
    { wch: 25 }, // KPI Name
    { wch: 15 }, // Current Value
    { wch: 15 }, // Target Value
    { wch: 12 }, // Achievement
    { wch: 15 }, // Category
    { wch: 10 }, // Status
    { wch: 12 }  // Unit
  ]
  
  // Add worksheet to workbook
  XLSX.utils.book_append_sheet(wb, ws, language === 'ar' ? 'مؤشرات الأداء' : 'KPIs')
  
  // Create summary sheet
  const summaryData = createSummaryData(data, language)
  const summaryWs = XLSX.utils.aoa_to_sheet(summaryData)
  summaryWs['!cols'] = [{ wch: 20 }, { wch: 15 }]
  XLSX.utils.book_append_sheet(wb, summaryWs, language === 'ar' ? 'الملخص' : 'Summary')
  
  // Download file
  XLSX.writeFile(wb, `kpi-report-${new Date().toISOString().split('T')[0]}.xlsx`)
}

/**
 * Export KPI data to PDF format with professional layout
 */
export const exportToPDF = (data: ExportData[], options: ExportOptions): void => {
  const { language, title = 'KPI Report', companyName = 'نمو - نظام إدارة المؤسسات' } = options
  
  // Create PDF document
  const doc = new jsPDF('landscape', 'mm', 'a4')
  
  // Set font for Arabic support
  if (language === 'ar') {
    // Note: For proper Arabic support, you would need to add Arabic fonts
    // For now, we'll use the default font
  }
  
  // Add header
  doc.setFontSize(20)
  doc.text(language === 'ar' ? 'تقرير مؤشرات الأداء الرئيسية' : 'Key Performance Indicators Report', 20, 20)
  
  doc.setFontSize(12)
  doc.text(companyName, 20, 30)
  doc.text(`${language === 'ar' ? 'تاريخ التقرير' : 'Report Date'}: ${new Date().toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}`, 20, 40)
  
  // Add summary statistics
  const summary = calculateSummary(data, language)
  let yPosition = 55
  
  doc.setFontSize(14)
  doc.text(language === 'ar' ? 'ملخص الأداء' : 'Performance Summary', 20, yPosition)
  yPosition += 10
  
  doc.setFontSize(10)
  summary.forEach(item => {
    doc.text(`${item.label}: ${item.value}`, 20, yPosition)
    yPosition += 7
  })
  
  // Prepare table data
  const headers = language === 'ar' 
    ? ['اسم المؤشر', 'القيمة الحالية', 'الهدف', 'الإنجاز %', 'الفئة', 'الحالة']
    : ['KPI Name', 'Current Value', 'Target', 'Achievement %', 'Category', 'Status']
  
  const tableData = data.map(kpi => [
    language === 'ar' ? kpi.name_ar : kpi.name,
    kpi.current_value.formatted,
    `${kpi.target_value} ${language === 'ar' ? kpi.unit_ar : kpi.unit}`,
    `${kpi.target_achievement.toFixed(1)}%`,
    language === 'ar' ? kpi.category.name_ar : kpi.category.name,
    kpi.status
  ])
  
  // Add table
  autoTable(doc, {
    head: [headers],
    body: tableData,
    startY: yPosition + 10,
    styles: {
      fontSize: 8,
      cellPadding: 3
    },
    headStyles: {
      fillColor: [41, 128, 185],
      textColor: 255,
      fontStyle: 'bold'
    },
    alternateRowStyles: {
      fillColor: [245, 245, 245]
    },
    columnStyles: {
      0: { cellWidth: 45 }, // KPI Name
      1: { cellWidth: 30 }, // Current Value
      2: { cellWidth: 30 }, // Target
      3: { cellWidth: 25 }, // Achievement
      4: { cellWidth: 35 }, // Category
      5: { cellWidth: 20 }  // Status
    }
  })
  
  // Add footer
  const pageCount = doc.getNumberOfPages()
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i)
    doc.setFontSize(8)
    doc.text(`${language === 'ar' ? 'صفحة' : 'Page'} ${i} ${language === 'ar' ? 'من' : 'of'} ${pageCount}`, 
             doc.internal.pageSize.width - 30, doc.internal.pageSize.height - 10)
  }
  
  // Download PDF
  doc.save(`kpi-report-${new Date().toISOString().split('T')[0]}.pdf`)
}

/**
 * Create summary data for Excel export
 */
const createSummaryData = (data: ExportData[], language: 'ar' | 'en'): any[][] => {
  const total = data.length
  const onTarget = data.filter(kpi => kpi.target_achievement >= 95 && kpi.target_achievement <= 105).length
  const aboveTarget = data.filter(kpi => kpi.target_achievement > 105).length
  const belowTarget = data.filter(kpi => kpi.target_achievement < 95).length
  const avgAchievement = data.reduce((sum, kpi) => sum + kpi.target_achievement, 0) / total
  
  return [
    [language === 'ar' ? 'ملخص الأداء' : 'Performance Summary', ''],
    [language === 'ar' ? 'إجمالي المؤشرات' : 'Total KPIs', total],
    [language === 'ar' ? 'على الهدف' : 'On Target', onTarget],
    [language === 'ar' ? 'فوق الهدف' : 'Above Target', aboveTarget],
    [language === 'ar' ? 'تحت الهدف' : 'Below Target', belowTarget],
    [language === 'ar' ? 'متوسط الإنجاز' : 'Average Achievement', `${avgAchievement.toFixed(1)}%`]
  ]
}

/**
 * Calculate summary statistics for PDF export
 */
const calculateSummary = (data: ExportData[], language: 'ar' | 'en') => {
  const total = data.length
  const onTarget = data.filter(kpi => kpi.target_achievement >= 95 && kpi.target_achievement <= 105).length
  const aboveTarget = data.filter(kpi => kpi.target_achievement > 105).length
  const belowTarget = data.filter(kpi => kpi.target_achievement < 95).length
  const avgAchievement = data.reduce((sum, kpi) => sum + kpi.target_achievement, 0) / total
  
  return [
    { label: language === 'ar' ? 'إجمالي المؤشرات' : 'Total KPIs', value: total },
    { label: language === 'ar' ? 'على الهدف' : 'On Target', value: onTarget },
    { label: language === 'ar' ? 'فوق الهدف' : 'Above Target', value: aboveTarget },
    { label: language === 'ar' ? 'تحت الهدف' : 'Below Target', value: belowTarget },
    { label: language === 'ar' ? 'متوسط الإنجاز' : 'Average Achievement', value: `${avgAchievement.toFixed(1)}%` }
  ]
}

/**
 * Main export function that handles all formats
 */
export const exportKPIData = (data: ExportData[], options: ExportOptions): void => {
  switch (options.format) {
    case 'csv':
      exportToCSV(data, options)
      break
    case 'excel':
      exportToExcel(data, options)
      break
    case 'pdf':
      exportToPDF(data, options)
      break
    default:
      throw new Error(`Unsupported export format: ${options.format}`)
  }
}
