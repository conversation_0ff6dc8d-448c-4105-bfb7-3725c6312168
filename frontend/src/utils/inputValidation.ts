import React from 'react';
/**
 * Input Validation & Sanitization
 * Comprehensive security validation for user inputs
 */

import { log } from './logger'

// Validation rule types
type ValidationRule = 
  | 'required'
  | 'email'
  | 'phone'
  | 'url'
  | 'alphanumeric'
  | 'numeric'
  | 'arabic'
  | 'english'
  | 'noHtml'
  | 'noScript'
  | 'safeHtml'

interface ValidationConfig {
  rules: ValidationRule[]
  minLength?: number
  maxLength?: number
  pattern?: RegExp
  customValidator?: (value: string) => boolean | string
  sanitize?: boolean
}

interface ValidationResult {
  isValid: boolean
  errors: string[]
  sanitizedValue?: string
  securityIssues?: string[]
}

class InputValidator {
  private patterns = {
    email: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
    phone: /^[\+]?[0-9\s\-\(\)]{10,15}$/,
    url: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
    alphanumeric: /^[a-zA-Z0-9\u0600-\u06FF\s]+$/,
    numeric: /^[0-9]+$/,
    arabic: /^[\u0600-\u06FF\s\u060C\u061B\u061F\u0640]+$/,
    english: /^[a-zA-Z\s]+$/,
    // Security patterns
    htmlTags: /<[^>]*>/g,
    scriptTags: /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    sqlInjection: /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)|('|('')|;|--|\/\*|\*\/)/gi,
    xssPatterns: /(javascript:|vbscript:|onload=|onerror=|onclick=|onmouseover=|onfocus=|onblur=)/gi,
    pathTraversal: /(\.\.|\/\.\.|\\\.\.)/g
  }

  private securityThreats = {
    xss: ['<script', 'javascript:', 'vbscript:', 'onload=', 'onerror=', 'onclick='],
    sqlInjection: ['SELECT', 'INSERT', 'UPDATE', 'DELETE', 'DROP', 'UNION', '--', '/*'],
    pathTraversal: ['../', '..\\', '%2e%2e', '%252e%252e'],
    htmlInjection: ['<iframe', '<object', '<embed', '<form', '<input']
  }

  /**
   * Validate input against configuration
   */
  validate(value: string, config: ValidationConfig): ValidationResult {
    const errors: string[] = []
    const securityIssues: string[] = []
    let sanitizedValue: any = value

    // Security checks first
    const securityResult = this.checkSecurity(value)
    if (securityResult.issues.length > 0) {
      securityIssues.push(...securityResult.issues)
      log.warn('security', 'Input security issues detected', {
        value: value.substring(0, 100),
        issues: securityResult.issues
      })
    }

    // Sanitize if requested
    if (config.sanitize) {
      sanitizedValue = this.sanitizeInput(value, config.rules)
    }

    // Required validation
    if (config.rules.includes('required') && !sanitizedValue.trim()) {
      errors.push('هذا الحقل مطلوب')
    }

    // Length validation
    if (config.minLength && sanitizedValue.length < config.minLength) {
      errors.push(`الحد الأدنى للطول هو ${config.minLength} أحرف`)
    }

    if (config.maxLength && sanitizedValue.length > config.maxLength) {
      errors.push(`الحد الأقصى للطول هو ${config.maxLength} أحرف`)
    }

    // Pattern validation
    for (const rule of config.rules) {
      if (rule === 'required') continue // Already handled

      const pattern = this.patterns[rule as keyof typeof this.patterns]
      if (pattern && !pattern.test(sanitizedValue)) {
        errors.push(this.getErrorMessage(rule))
      }
    }

    // Custom validation
    if (config.customValidator) {
      const customResult = config.customValidator(sanitizedValue)
      if (typeof customResult === 'string') {
        errors.push(customResult)
      } else if (!customResult) {
        errors.push('قيمة غير صالحة')
      }
    }

    // Custom pattern validation
    if (config.pattern && !config.pattern.test(sanitizedValue)) {
      errors.push('تنسيق غير صالح')
    }

    return {
      isValid: errors.length === 0 && securityIssues.length === 0,
      errors,
      sanitizedValue: config.sanitize ? sanitizedValue : undefined,
      securityIssues: securityIssues.length > 0 ? securityIssues : undefined
    }
  }

  /**
   * Check for security threats
   */
  private checkSecurity(value: string): { issues: string[] } {
    const issues: string[] = []
    const lowerValue = value.toLowerCase()

    // XSS detection
    for (const threat of this.securityThreats.xss) {
      if (lowerValue.includes(threat.toLowerCase())) {
        issues.push(`Potential XSS: ${threat}`)
      }
    }

    // SQL Injection detection
    for (const threat of this.securityThreats.sqlInjection) {
      if (lowerValue.includes(threat.toLowerCase())) {
        issues.push(`Potential SQL Injection: ${threat}`)
      }
    }

    // Path Traversal detection
    for (const threat of this.securityThreats.pathTraversal) {
      if (value.includes(threat)) {
        issues.push(`Potential Path Traversal: ${threat}`)
      }
    }

    // HTML Injection detection
    for (const threat of this.securityThreats.htmlInjection) {
      if (lowerValue.includes(threat.toLowerCase())) {
        issues.push(`Potential HTML Injection: ${threat}`)
      }
    }

    return { issues }
  }

  /**
   * Sanitize input based on rules
   */
  private sanitizeInput(value: string, rules: ValidationRule[]): string {
    let sanitized = value

    // Remove HTML tags if noHtml rule
    if (rules.includes('noHtml')) {
      sanitized = sanitized.replace(this.patterns.htmlTags, '')
    }

    // Remove script tags if noScript rule
    if (rules.includes('noScript')) {
      sanitized = sanitized.replace(this.patterns.scriptTags, '')
    }

    // Safe HTML sanitization
    if (rules.includes('safeHtml')) {
      sanitized = this.sanitizeHtml(sanitized)
    }

    // Trim whitespace
    sanitized = sanitized.trim()

    // Encode special characters
    sanitized = this.encodeSpecialChars(sanitized)

    return sanitized
  }

  /**
   * Sanitize HTML keeping only safe tags
   */
  private sanitizeHtml(html: string): string {
    const allowedTags = ['b', 'i', 'u', 'strong', 'em', 'p', 'br']
    const allowedAttributes: string[] = []

    // Simple HTML sanitization (in production, use a library like DOMPurify)
    let sanitized = html

    // Remove script tags
    sanitized = sanitized.replace(this.patterns.scriptTags, '')

    // Remove dangerous attributes
    sanitized = sanitized.replace(/on\w+="[^"]*"/gi, '')
    sanitized = sanitized.replace(/javascript:/gi, '')

    return sanitized
  }

  /**
   * Encode special characters
   */
  private encodeSpecialChars(value: string): string {
    return value
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;')
  }

  /**
   * Get error message for validation rule
   */
  private getErrorMessage(rule: ValidationRule): string {
    const messages = {
      email: 'يرجى إدخال بريد إلكتروني صالح',
      phone: 'يرجى إدخال رقم هاتف صالح',
      url: 'يرجى إدخال رابط صالح',
      alphanumeric: 'يُسمح بالأحرف والأرقام فقط',
      numeric: 'يُسمح بالأرقام فقط',
      arabic: 'يُسمح بالأحرف العربية فقط',
      english: 'يُسمح بالأحرف الإنجليزية فقط',
      noHtml: 'لا يُسمح بعلامات HTML',
      noScript: 'لا يُسمح بالنصوص البرمجية',
      safeHtml: 'HTML غير آمن'
    }

    return messages[rule] || 'قيمة غير صالحة'
  }

  /**
   * Validate multiple fields
   */
  validateForm(data: Record<string, string>, schema: Record<string, ValidationConfig>): {
    isValid: boolean
    errors: Record<string, string[]>
    sanitizedData: Record<string, string>
    securityIssues: string[]
  } {
    const errors: Record<string, string[]> = {}
    const sanitizedData: Record<string, string> = {}
    const allSecurityIssues: string[] = []

    for (const [field, value] of Object.entries(data)) {
      const config = schema[field]
      if (!config) continue

      const result = this.validate(value, config)
      
      if (!result.isValid) {
        errors[field] = result.errors
      }

      if (result.sanitizedValue !== undefined) {
        sanitizedData[field] = result.sanitizedValue
      } else {
        sanitizedData[field] = value
      }

      if (result.securityIssues) {
        allSecurityIssues.push(...result.securityIssues.map(issue => `${field}: ${issue}`))
      }
    }

    return {
      isValid: Object.keys(errors).length === 0 && allSecurityIssues.length === 0,
      errors,
      sanitizedData,
      securityIssues: allSecurityIssues
    }
  }
}

// Singleton instance
export const inputValidator = new InputValidator()

// Convenience functions
export const validateEmail = (email: string) => 
  inputValidator.validate(email, { rules: ['required', 'email'], sanitize: true })

export const validatePhone = (phone: string) => 
  inputValidator.validate(phone, { rules: ['required', 'phone'], sanitize: true })

export const validateText = (text: string, maxLength = 255) => 
  inputValidator.validate(text, { rules: ['required', 'noScript'], maxLength, sanitize: true })

export const validateSafeHtml = (html: string, maxLength = 1000) => 
  inputValidator.validate(html, { rules: ['required', 'safeHtml'], maxLength, sanitize: true })

export default inputValidator
