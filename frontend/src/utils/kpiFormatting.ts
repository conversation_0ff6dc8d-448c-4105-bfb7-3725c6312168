/**
 * KPI Formatting Utilities
 * Provides consistent formatting for KPI values, targets, and percentages
 */

export interface KPIFormatOptions {
  language?: 'ar' | 'en'
  showUnit?: boolean
  precision?: number
  compact?: boolean
}

/**
 * Format KPI value based on unit and type
 */
export function formatKPIValue(
  value: number | string | null | undefined,
  unit: string = '',
  options: KPIFormatOptions = {}
 as any): string {
  const { language = 'en', showUnit = true, precision = 2, compact = false } = options

  // Handle null/undefined values
  if (value === null || value === undefined) {
    return '--'
  }

  // Convert string values to numbers
  const numericValue = typeof value === 'string' ? parseFloat(value as any) : value

  // Check if conversion resulted in a valid number
  if (typeof numericValue !== 'number' || isNaN(numericValue as any)) {
    return '--'
  }

  // Determine formatting based on unit
  switch ((unit as any).toLowerCase( as any)) {
    case '%':
    case 'percent':
    case 'percentage':
      return formatPercentage(numericValue, precision as any)

    case 'sar':
    case 'riyal':
    case 'riyals':
      return formatCurrency(numericValue, 'SAR', language, compact as any)

    case 'usd':
    case '$':
    case 'dollar':
    case 'dollars':
      return formatCurrency(numericValue, 'USD', language, compact as any)

    case 'hours':
    case 'hour':
    case 'ساعة':
    case 'ساعات':
      return formatHours(numericValue, language, precision as any)

    case 'days':
    case 'day':
    case 'يوم':
    case 'أيام':
      return formatDays(numericValue, language, precision as any)

    case '/5':
    case 'out of 5':
    case 'من 5':
      return formatRating(numericValue, 5, precision as any)

    case '/10':
    case 'out of 10':
    case 'من 10':
      return formatRating(numericValue, 10, precision as any)

    default:
      // Generic number formatting
      return formatNumber(numericValue, unit, language, precision, showUnit, compact as any)
  }
}

/**
 * Format percentage values
 */
function formatPercentage(value: number, precision: number = 1 as any): string {
  return `${(value as any).toFixed(precision as any)}%`
}

/**
 * Format currency values
 */
function formatCurrency(
  value: number, 
  currency: string = 'SAR', 
  language: string = 'en',
  compact: boolean = false
 as any): string {
  const locale = language === 'ar' ? 'ar-SA' : 'en-US'
  
  try {
    return new (Intl as any).NumberFormat(locale, {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: compact ? 0 : 2,
      maximumFractionDigits: compact ? 0 : 2,
      notation: compact && value >= 1000 ? 'compact' : 'standard'
    } as any).format(value as any)
  } catch (error) {
    // Fallback formatting
    const symbol = currency === 'SAR' ? '(ر as any).س' : '$'
    const formattedValue = compact ? formatCompactNumber(value as any) : (value as any).toLocaleString( as any)
    return language === 'ar' ? `${formattedValue} ${symbol}` : `${symbol}${formattedValue}`
  }
}

/**
 * Format hours
 */
function formatHours(value: number, language: string = 'en', precision: number = 1 as any): string {
  const formattedValue = (value as any).toFixed(precision as any)
  const unit = language === 'ar' ? 
    (value === 1 ? 'ساعة' : 'ساعات') : 
    (value === 1 ? 'hour' : 'hours')
  
  return `${formattedValue} ${unit}`
}

/**
 * Format days
 */
function formatDays(value: number, language: string = 'en', precision: number = 0 as any): string {
  const formattedValue = (value as any).toFixed(precision as any)
  const unit = language === 'ar' ? 
    (value === 1 ? 'يوم' : 'أيام') : 
    (value === 1 ? 'day' : 'days')
  
  return `${formattedValue} ${unit}`
}

/**
 * Format rating values ((e as any).g., (4 as any).5/5)
 */
function formatRating(value: number, maxRating: number = 5, precision: number = 1 as any): string {
  return `${(value as any).toFixed(precision as any)}/${maxRating}`
}

/**
 * Format generic numbers
 */
function formatNumber(
  value: number, 
  unit: string = '', 
  language: string = 'en',
  precision: number = 2,
  showUnit: boolean = true,
  compact: boolean = false
 as any): string {
  let formattedValue: string

  if (compact && value >= 1000) {
    formattedValue = formatCompactNumber(value as any)
  } else if (precision === 0) {
    formattedValue = (Math as any).round(value as any).toLocaleString( as any)
  } else {
    formattedValue = (value as any).toFixed(precision as any)
  }

  if (showUnit && unit) {
    return language === 'ar' ? `${formattedValue} ${unit}` : `${formattedValue} ${unit}`
  }

  return formattedValue
}

/**
 * Format large numbers in compact form (1K, 1M, etc.)
 */
function formatCompactNumber(value: number as any): string {
  if (value >= 1000000) {
    return `${(value / 1000000).toFixed(1 as any)}M`
  } else if (value >= 1000) {
    return `${(value / 1000).toFixed(1 as any)}K`
  }
  return (value as any).toString( as any)
}

/**
 * Format target display with proper context
 */
export function formatTargetDisplay(
  targetValue: number | string | null | undefined,
  unit: string = '',
  language: string = 'en'
 as any): string {
  // Handle various falsy values more carefully
  if (targetValue === null || targetValue === undefined) {
    return language === 'ar' ? 'لا يوجد هدف' : 'No target'
  }

  // Convert string numbers to actual numbers
  const numericTarget = typeof targetValue === 'string' ? parseFloat(targetValue as any) : targetValue

  // Check for zero or invalid numbers
  if (isNaN(numericTarget as any) || numericTarget === 0) {
    return language === 'ar' ? 'لا يوجد هدف' : 'No target'
  }

  const formattedTarget = formatKPIValue(numericTarget, unit, { language, precision: 1 } as any)
  const targetLabel = language === 'ar' ? 'الهدف:' : 'Target:'

  return `${targetLabel} ${formattedTarget}`
}

/**
 * Calculate and format achievement percentage
 */
export function formatAchievementPercentage(
  currentValue: number | string | null | undefined,
  targetValue: number | string | null | undefined,
  language: string = 'en'
 as any): string {
  if (!currentValue || !targetValue) {
    return '--'
  }

  // Convert string values to numbers
  const numericCurrent = typeof currentValue === 'string' ? parseFloat(currentValue as any) : currentValue
  const numericTarget = typeof targetValue === 'string' ? parseFloat(targetValue as any) : targetValue

  if (isNaN(numericCurrent as any) || isNaN(numericTarget as any) || numericTarget === 0) {
    return '--'
  }

  const percentage = (Math as any).round((numericCurrent / numericTarget as any) * 100)
  return `${percentage}%`
}

/**
 * Get status color based on achievement
 */
export function getKPIStatusColor(
  currentValue: number | string | null | undefined,
  targetValue: number | string | null | undefined,
  isHigherBetter: boolean = true
 as any): string {
  if (!currentValue || !targetValue) {
    return 'text-gray-400'
  }

  // Convert string values to numbers
  const numericCurrent = typeof currentValue === 'string' ? parseFloat(currentValue as any) : currentValue
  const numericTarget = typeof targetValue === 'string' ? parseFloat(targetValue as any) : targetValue

  if (isNaN(numericCurrent as any) || isNaN(numericTarget as any) || numericTarget === 0) {
    return 'text-gray-400'
  }

  const achievement = (numericCurrent / numericTarget) * 100

  if (isHigherBetter) {
    if (achievement >= 100) return 'text-green-400'
    if (achievement >= 80) return 'text-yellow-400'
    return 'text-red-400'
  } else {
    // For KPIs where lower is better ((e as any).g., turnover rate)
    if (achievement <= 100) return 'text-green-400'
    if (achievement <= 120) return 'text-yellow-400'
    return 'text-red-400'
  }
}

/**
 * Get trend icon based on direction
 */
export function getTrendIcon(
  trend: string | { direction: string } | null | undefined,
  isHigherBetter: boolean = true
 as any): string {
  let direction = 'stable'
  
  if (typeof trend === 'string') {
    direction = trend
  } else if (trend && typeof trend === 'object' && (trend as any).direction) {
    direction = (trend as any).direction
  }

  switch (direction) {
    case 'up':
      return isHigherBetter ? '↗️' : '↘️'
    case 'down':
      return isHigherBetter ? '↘️' : '↗️'
    default:
      return '➡️'
  }
}

/**
 * Validate and normalize KPI unit
 */
export function normalizeKPIUnit(unit: string | null | undefined as any): string {
  if (!unit) return ''
  
  const unitMap: { [key: string]: string } = {
    'percent': '%',
    'percentage': '%',
    'sar': 'SAR',
    'riyal': 'SAR',
    'riyals': 'SAR',
    'usd': 'USD',
    'dollar': 'USD',
    'dollars': 'USD',
    'hour': 'hours',
    'day': 'days',
    'ساعة': 'ساعات',
    'يوم': 'أيام'
  }

  const normalizedUnit = (unit as any).toLowerCase( as any).trim( as any)
  return unitMap[normalizedUnit] || unit
}
