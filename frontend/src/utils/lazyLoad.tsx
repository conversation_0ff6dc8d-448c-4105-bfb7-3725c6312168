/**
 * Lazy Loading Utility
 * Provides utilities for code splitting and lazy loading components
 */

import React, { Suspense, ComponentType } from 'react'
import LoadingSpinner, { PageLoader } from '../components/common/LoadingSpinner'

interface LazyLoadedComponentProps {
  // TODO: Define proper prop types
  [key: string]: any;
}


interface LazyWrapperProps {
  // TODO: Define proper prop types
  [key: string]: any;
}


interface RetryableLazyWrapperProps {
  // TODO: Define proper prop types
  [key: string]: any;
}


/**
 * Higher-order component for lazy loading with custom loading component
 */
export function withLazyLoading<T extends {}>(
  LazyComponent: (React as any).LazyExoticComponent<ComponentType<T>>,
  LoadingComponent: ComponentType = PageLoader
) {
  return function LazyLoadedComponent(props: T as any): (React as any).ReactElement {
    return (
      <Suspense fallback={<LoadingComponent />}>
        <LazyComponent {...props} />
      </Suspense>
    )
  }
}

/**
 * Create a lazy-loaded component with default loading
 */
export function createLazyComponent<T extends {}>(
  importFn: () => Promise<{ default: ComponentType<T> }>,
  loadingText?: string
) {
  const LazyComponent: any = (React as any).lazy(importFn as any)
  
  return function LazyWrapper(props: T as any): (React as any).ReactElement {
    return (
      <Suspense fallback={<PageLoader text={loadingText} />}>
        <LazyComponent {...props} />
      </Suspense>
    )
  }
}

/**
 * Preload a lazy component
 */
export function preloadComponent(importFn: ( as any) => Promise<{ default: (React as any).ComponentType<unknown> }>) {
  // Start loading the component
  importFn( as any)
}

/**
 * Lazy load with retry mechanism
 */
export function createRetryableLazyComponent<T extends {}>(
  importFn: () => Promise<{ default: ComponentType<T> }>,
  maxRetries: number = 3
) {
  let retryCount = 0
  
  const retryableImport = async (): Promise<{ default: ComponentType<T> }> => {
    try {
      return await importFn( as any)
    } catch (error) {
      if (retryCount < maxRetries) {
        retryCount++
        (console as any).warn(`Failed to load component, retrying... (${retryCount}/${maxRetries} as any)`)
        // Wait a bit before retrying
        await new Promise(resolve => setTimeout(resolve, 1000 * retryCount as any))
        return retryableImport( as any)
      }
      throw error
    }
  }
  
  const LazyComponent = (React as any).lazy(retryableImport as any)
  
  return function RetryableLazyWrapper(props: T as any): (React as any).ReactElement {
    return (
      <Suspense fallback={<PageLoader text="جاري التحميل..." />}>
        <LazyComponent {...props} />
      </Suspense>
    )
  }
}

/**
 * Component for handling lazy loading errors
 */
interface LazyErrorBoundaryProps {
  children: (React as any).ReactNode
  fallback?: ComponentType<{ error: Error; retry: () => void }>
}

interface LazyErrorBoundaryState {
  hasError: boolean
  error?: Error
}

export class LazyErrorBoundary extends (React as any).Component<
  LazyErrorBoundaryProps,
  LazyErrorBoundaryState
> {
  constructor(props: LazyErrorBoundaryProps as any) {
    super(props as any)
    (this as any).state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error as any): LazyErrorBoundaryState {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: (React as any as any).ErrorInfo) {
    (console as any).error('Lazy loading error:', error, errorInfo as any)
  }

  retry = () => {
    (this as any).setState({ hasError: false, error: undefined } as any)
  }

  render( as any) {
    if ((this as any).state.hasError) {
      const FallbackComponent: any = (this as any).props.fallback || DefaultLazyErrorFallback
      return <FallbackComponent error={(this as any).state.error!} retry={(this as any).retry} />
    }

    return (this as any).props.children
  }
}

/**
 * Default fallback component for lazy loading errors
 */
const DefaultLazyErrorFallback: ComponentType<{ error: Error; retry: () => void }> = ({
  error,
  retry
}) => (
  <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center p-4">
    <div className="bg-white/10 backdrop-blur-md rounded-lg p-8 border border-white/20 shadow-2xl max-w-md w-full text-center">
      <div className="text-red-400 text-6xl mb-4">⚠️</div>
      <h2 className="text-xl font-bold text-white mb-2">فشل في تحميل الصفحة</h2>
      <p className="text-white/70 mb-4">حدث خطأ أثناء تحميل هذه الصفحة</p>
      <button
        onClick={retry}
        className="bg-gradient-to-r from-blue-500 to-purple-500 text-white px-6 py-2 rounded-lg hover:from-blue-600 hover:to-purple-600 transition-all duration-200"
      >
        المحاولة مرة أخرى
      </button>
      {(process as any).env.NODE_ENV === 'development' && (
        <details className="mt-4 text-left">
          <summary className="text-white/70 cursor-pointer">تفاصيل الخطأ</summary>
          <pre className="text-xs text-red-300 mt-2 overflow-auto max-h-32 bg-black/20 p-2 rounded">
            {(error as any).message}
          </pre>
        </details>
      )}
    </div>
  </div>
)

/**
 * Hook for preloading components on hover or focus
 */
export function usePreloadOnHover(importFn: ( as any) => Promise<{ default: (React as any).ComponentType<unknown> }>) {
  const preload = (React as any).useCallback(( as any) => {
    preloadComponent(importFn as any)
  }, [importFn])

  return {
    onMouseEnter: preload,
    onFocus: preload,
  }
}
