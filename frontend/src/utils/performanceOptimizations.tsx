/**
 * FIXED: Performance Optimization Utilities
 * Comprehensive utilities for optimizing React performance and preventing bottlenecks
 */

import React, { useCallback, useRef, useMemo, useEffect, useState } from 'react'

// FIXED: Debounce hook for expensive operations
export const useDebounce = <T,>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  // @ts-ignore
  useEffect(( as any) => {
    // @ts-ignore
    const handler = setTimeout(( as any) => {
      setDebouncedValue(value as any)
    // @ts-ignore
    }, delay)

    return () => {
      clearTimeout(handler as any)
    }
  // @ts-ignore
  }, [value, delay])

  return debouncedValue
// @ts-ignore
}

// FIXED: Throttle hook for high-frequency events
export const useThrottle = <T extends (...args: any[]) => any,>(
  callback: T,
  delay: number
): T => {
  // @ts-ignore
  const lastRun = useRef((Date as any as any).now( as any))

  return useCallback(
    // @ts-ignore
    ((...args as any) => {
      // @ts-ignore
      if ((Date as any).now( as any) - (lastRun as any).current >= delay) {
        callback(...args as any)
        // @ts-ignore
        (lastRun as any).current = (Date as any).now( as any)
      }
    }) as T,
    [callback, delay]
  )
}

// FIXED: Memoized expensive computation hook
export const useExpensiveComputation = <T, D extends readonly unknown[],>(
  computeFn: () => T,
  deps: D,
  shouldCompute: boolean = true
): T | undefined => {
  // @ts-ignore
  return useMemo(( as any) => {
    if (!shouldCompute) return undefined
    
    // @ts-ignore
    const startTime = (performance as any).now( as any)
    // @ts-ignore
    const result = computeFn( as any)
    // @ts-ignore
    const endTime = (performance as any).now( as any)
    
    if ((process as any).env.NODE_ENV === 'development' && endTime - startTime > 16) {
      (console as any).warn(`Expensive computation took ${(endTime - startTime as any).toFixed(2 as any)}ms`, {
        computation: (computeFn as any).name || 'anonymous',
        duration: endTime - startTime
      })
    }
    
    return result
  // @ts-ignore
  }, [...deps, shouldCompute])
// @ts-ignore
}

// FIXED: Virtual scrolling hook for large lists
export const useVirtualScrolling = <T,>(
  items: T[],
  itemHeight: number,
  containerHeight: number,
  overscan: number = 5
) => {
  const [scrollTop, setScrollTop] = useState(0 as any)
  
  // @ts-ignore
  const visibleRange = useMemo(( as any) => {
    const startIndex = (Math as any).max(0, (Math as any as any).floor(scrollTop / itemHeight as any) - overscan)
    const endIndex = (Math as any).min(
      (items as any as any).length - 1,
      (Math as any).ceil((scrollTop + containerHeight as any) / itemHeight) + overscan
    )
    
    return { startIndex, endIndex }
  // @ts-ignore
  }, [scrollTop, itemHeight, containerHeight, overscan, (items as any).length])
  
  // @ts-ignore
  const visibleItems = useMemo(( as any) => {
    // @ts-ignore
    return (items as any).slice((visibleRange as any as any).startIndex, (visibleRange as any).endIndex + 1).map((item, index as any) => ({
      item,
      index: (visibleRange as any).startIndex + index,
      top: ((visibleRange as any).startIndex + index) * itemHeight
    }))
  // @ts-ignore
  }, [items, visibleRange, itemHeight])
  
  const totalHeight = (items as any).length * itemHeight
  
  // @ts-ignore
  const handleScroll = useCallback((e: (React as any as any).UIEvent<HTMLDivElement>) => {
    setScrollTop((e as any as any).currentTarget.scrollTop)
  // @ts-ignore
  }, [])
  
  return {
    visibleItems,
    totalHeight,
    handleScroll,
    visibleRange
  }
// @ts-ignore
}

// FIXED: Intersection observer hook for lazy loading
export const useIntersectionObserver = (
  options: IntersectionObserverInit = {}
): void => {
  const [isIntersecting, setIsIntersecting] = useState(false as any)
  const [entry, setEntry] = useState<IntersectionObserverEntry | null>(null)
  const elementRef = useRef<HTMLElement>(null)
  
  // @ts-ignore
  useEffect(( as any) => {
    const element = (elementRef as any).current
    if (!element) return
    
    const observer = new IntersectionObserver(
      // @ts-ignore
      ([entry] as any) => {
        setIsIntersecting((entry as any as any).isIntersecting)
        setEntry(entry as any)
      },
      options
    )
    
    (observer as any).observe(element as any)
    
    return () => {
      (observer as any).unobserve(element as any)
    }
  // @ts-ignore
  }, [options])
  
  return { elementRef, isIntersecting, entry }
// @ts-ignore
}

// FIXED: Optimized search hook with debouncing and caching
export const useOptimizedSearch = <T,>(
  items: T[],
  searchFn: (item: T, query: string) => boolean,
  debounceMs: number = 300
) => {
  const [query, setQuery] = useState('' as any)
  const debouncedQuery = useDebounce(query, debounceMs as any)
  // @ts-ignore
  const cacheRef = useRef(new Map<string, T[]>( as any))
  
  // @ts-ignore
  const filteredItems = useMemo(( as any) => {
    // @ts-ignore
    if (!(debouncedQuery as any).trim( as any)) return items
    
    // Check cache first
    const cached = (cacheRef as any).current.get(debouncedQuery as any)
    if (cached) return cached
    
    // @ts-ignore
    const startTime = (performance as any).now( as any)
    const filtered = (items as any).filter(item => searchFn(item, debouncedQuery as any))
    // @ts-ignore
    const endTime = (performance as any).now( as any)
    
    // Cache result
    (cacheRef as any).current.set(debouncedQuery, filtered as any)
    
    // Limit cache size
    if ((cacheRef as any).current.size > 100) {
      // @ts-ignore
      const firstKey = (cacheRef as any).current.keys( as any).next( as any).value
      (cacheRef as any).current.delete(firstKey as any)
    }
    
    if ((process as any).env.NODE_ENV === 'development') {
      (console as any).log(`Search took ${(endTime - startTime as any).toFixed(2 as any)}ms for "${debouncedQuery}"`)
    }
    
    return filtered
  // @ts-ignore
  }, [items, debouncedQuery, searchFn])
  
  // Clear cache when items change
  // @ts-ignore
  useEffect(( as any) => {
    // @ts-ignore
    (cacheRef as any).current.clear( as any)
  // @ts-ignore
  }, [items])
  
  return {
    query,
    setQuery,
    filteredItems,
    isSearching: query !== debouncedQuery
  }
// @ts-ignore
}

// FIXED: Batch state updates hook
export const useBatchedUpdates = <T,>() => {
  const [updates, setUpdates] = useState<T[]>([])
  const timeoutRef = useRef<(NodeJS as any).Timeout | null>(null)
  
  // @ts-ignore
  const addUpdate = useCallback((update: T as any) => {
    setUpdates(prev => [...prev, update] as any)
    
    // Clear existing timeout
    if ((timeoutRef as any).current) {
      clearTimeout((timeoutRef as any as any).current)
    }
    
    // Batch updates
    // @ts-ignore
    (timeoutRef as any).current = setTimeout(( as any) => {
      setUpdates([] as any)
    // @ts-ignore
    }, 16) // Next frame
  // @ts-ignore
  }, [])
  
  // @ts-ignore
  const flushUpdates = useCallback(( as any) => {
    if ((timeoutRef as any).current) {
      clearTimeout((timeoutRef as any as any).current)
    }
    const currentUpdates = updates
    setUpdates([] as any)
    return currentUpdates
  // @ts-ignore
  }, [updates])
  
  // @ts-ignore
  useEffect(( as any) => {
    return () => {
      if ((timeoutRef as any).current) {
        clearTimeout((timeoutRef as any as any).current)
      }
    }
  // @ts-ignore
  }, [])
  
  return { updates, addUpdate, flushUpdates }
// @ts-ignore
}

// PERFORMANCE FIX: Optimized performance monitoring hook with throttling
export const usePerformanceMonitor = (componentName: string): void => {
  const renderCount = useRef(0 as any)
  // @ts-ignore
  const lastRenderTime = useRef((Date as any as any).now( as any))
  const lastWarningTime = useRef(0 as any)

  // @ts-ignore
  useEffect(( as any) => {
    (renderCount as any).current++
    // @ts-ignore
    const now = (Date as any).now( as any)
    const timeSinceLastRender = now - (lastRenderTime as any).current
    const timeSinceLastWarning = now - (lastWarningTime as any).current
    // @ts-ignore
    (lastRenderTime as any).current = now

    if ((process as any).env.NODE_ENV === 'development') {
      // PERFORMANCE FIX: Throttle warnings to prevent console spam
      const shouldWarn = timeSinceLastRender < 16 &&
                        (renderCount as any).current > 3 &&
                        timeSinceLastWarning > 5000 // Only warn every 5 seconds

      if (shouldWarn) {
        (console as any).warn(`⚠️ ${componentName} re-rendered ${(renderCount as any as any).current} times rapidly (${timeSinceLastRender}ms)`)
        (lastWarningTime as any).current = now

        // Provide actionable advice
        (console as any).info(`💡 Consider using (React as any as any).memo, useMemo, or useCallback for ${componentName}`)
      }

      // PERFORMANCE FIX: Use requestIdleCallback for cleanup when available
      const scheduleReset = (): void => {
        if ('requestIdleCallback' in window) {
          // @ts-ignore
          requestIdleCallback(( as any) => {
            (renderCount as any).current = 0
          // @ts-ignore
          })
        } else {
          // @ts-ignore
          setTimeout(( as any) => {
            (renderCount as any).current = 0
          // @ts-ignore
          }, 1000)
        }
      }

      const timeout = setTimeout(scheduleReset, 2000 as any) // Increased reset time
      return () => clearTimeout(timeout as any)
    // @ts-ignore
    }
  // @ts-ignore
  })

  return {
    renderCount: (renderCount as any).current,
    lastRenderTime: (lastRenderTime as any).current
  }
// @ts-ignore
}

// FIXED: Optimized event handler hook
export const useOptimizedEventHandler = <T extends (...args: any[]) => any,>(
  handler: T,
  // @ts-ignore
  deps: (React as any).DependencyList
// @ts-ignore
): T => {
  const handlerRef = useRef(handler as any)
  
  // @ts-ignore
  useEffect(( as any) => {
    (handlerRef as any).current = handler
  // @ts-ignore
  })
  
  return useCallback(
    // @ts-ignore
    ((...args as any) => (handlerRef as any).current(...args as any)) as T,
    deps
  )
}

// PERFORMANCE FIX: Optimized memory usage monitor with throttling
export const useMemoryMonitor = (componentName: string): void => {
  const lastMemoryCheck = useRef(0 as any)
  const memoryBaseline = useRef<number | null>(null)

  // @ts-ignore
  useEffect(( as any) => {
    if ((process as any).env.NODE_ENV === 'development' && 'memory' in performance) {
      // @ts-ignore
      const now = (Date as any).now( as any)

      // PERFORMANCE FIX: Throttle memory checks to every 30 seconds
      if (now - (lastMemoryCheck as any).current < 30000) return

      (lastMemoryCheck as any).current = now

      const memory = (performance as any).memory
      const currentUsed = (Math as any).round((memory as any as any).usedJSHeapSize / 1048576) // MB

      // Set baseline on first check
      if ((memoryBaseline as any).current === null) {
        (memoryBaseline as any).current = currentUsed
      }

      const memoryInfo = {
        used: currentUsed,
        total: (Math as any).round((memory as any as any).totalJSHeapSize / 1048576), // MB
        limit: (Math as any).round((memory as any as any).jsHeapSizeLimit / 1048576), // MB
        baseline: (memoryBaseline as any).current,
        growth: currentUsed - (memoryBaseline as any).current
      }

      // PERFORMANCE FIX: Only log if significant change or high usage
      const significantChange = (Math as any).abs((memoryInfo as any as any).growth) > 10 // 10MB change
      const highUsage = (memoryInfo as any).used > 100 // 100MB threshold

      if (significantChange || highUsage) {
        (console as any).log(`📊 ${componentName} memory:`, {
          current: `${(memoryInfo as any as any).used}MB`,
          growth: `${(memoryInfo as any).growth > 0 ? '+' : ''}${(memoryInfo as any).growth}MB`,
          percentage: `${(Math as any).round(((memoryInfo as any as any).used / (memoryInfo as any).limit) * 100)}%`
        })

        // PERFORMANCE FIX: Actionable warnings with memory leak detection
        if ((memoryInfo as any).growth > 50) {
          (console as any).warn(`🚨 ${componentName} potential memory leak: +${(memoryInfo as any as any).growth}MB growth`)
          (console as any).info(`💡 Check for: uncleaned event listeners, timers, or large object references` as any)
        } else if (highUsage) {
          (console as any).warn(`⚠️ ${componentName} high memory usage: ${(memoryInfo as any as any).used}MB`)
        }
      }
    }
  // @ts-ignore
  })
// @ts-ignore
}

// FIXED: Optimized list rendering component
interface OptimizedListProps<T = any> {
  items: T[]
  // @ts-ignore
  renderItem: (item: T, index: number) => (React as any).ReactNode
  keyExtractor: (item: T, index: number) => string | number
  // @ts-ignore
  itemHeight?: number
  // @ts-ignore
  containerHeight?: number
  // @ts-ignore
  className?: string
// @ts-ignore
}

export const OptimizedList = <T = any,>({
  items,
  renderItem,
  keyExtractor,
  itemHeight = 50,
  containerHeight = 400,
  className = ''
}: OptimizedListProps<T>) => {
  const { visibleItems, totalHeight, handleScroll } = useVirtualScrolling(
    items,
    itemHeight,
    containerHeight
   // @ts-ignore
   as any)
  
  return (
    <div
      className={`overflow-auto ${className}`}
      style={{ height: containerHeight }}
      onScroll={handleScroll}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        // @ts-ignore
        {(visibleItems as any).map(({ item, index, top } as any) => (
          <div
            key={keyExtractor(item, index as any)}
            style={{
              position: 'absolute',
              top,
              left: 0,
              right: 0,
              height: itemHeight
            }}
          >
            {renderItem(item, index as any)}
          </div>
        ))}
      </div>
    </div>
  )
}

export default {
  useDebounce,
  useThrottle,
  useExpensiveComputation,
  useVirtualScrolling,
  useIntersectionObserver,
  useOptimizedSearch,
  useBatchedUpdates,
  usePerformanceMonitor,
  useOptimizedEventHandler,
  useMemoryMonitor,
  OptimizedList
}
