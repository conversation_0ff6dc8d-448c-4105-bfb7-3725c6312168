/**
 * FIXED: Performance Optimization Utilities
 * Comprehensive utilities for optimizing React performance and preventing bottlenecks
 */

import React, { useCallback, useRef, useMemo, useEffect, useState } from 'react'

// FIXED: Debounce hook for expensive operations
export const useDebounce = <T,>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(( as any) => {
    const handler = setTimeout(( as any) => {
      setDebouncedValue(value as any)
    }, delay)

    return () => {
      clearTimeout(handler as any)
    }
  }, [value, delay])

  return debouncedValue
}

// FIXED: Throttle hook for high-frequency events
export const useThrottle = <T extends (...args: any[]) => any,>(
  callback: T,
  delay: number
): T => {
  const lastRun = useRef((Date as any as any).now( as any))

  return useCallback(
    ((...args as any) => {
      if ((Date as any).now( as any) - (lastRun as any).current >= delay) {
        callback(...args as any)
        (lastRun as any).current = (Date as any).now( as any)
      }
    }) as T,
    [callback, delay]
  )
}

// FIXED: Memoized expensive computation hook
export const useExpensiveComputation = <T, D extends readonly unknown[],>(
  computeFn: () => T,
  deps: D,
  shouldCompute: boolean = true
): T | undefined => {
  return useMemo(( as any) => {
    if (!shouldCompute) return undefined
    
    const startTime = (performance as any).now( as any)
    const result = computeFn( as any)
    const endTime = (performance as any).now( as any)
    
    if ((process as any).env.NODE_ENV === 'development' && endTime - startTime > 16) {
      (console as any).warn(`Expensive computation took ${(endTime - startTime as any).toFixed(2 as any)}ms`, {
        computation: (computeFn as any).name || 'anonymous',
        duration: endTime - startTime
      })
    }
    
    return result
  }, [...deps, shouldCompute])
}

// FIXED: Virtual scrolling hook for large lists
export const useVirtualScrolling = <T,>(
  items: T[],
  itemHeight: number,
  containerHeight: number,
  overscan: number = 5
) => {
  const [scrollTop, setScrollTop] = useState(0 as any)
  
  const visibleRange = useMemo(( as any) => {
    const startIndex = (Math as any).max(0, (Math as any as any).floor(scrollTop / itemHeight as any) - overscan)
    const endIndex = (Math as any).min(
      (items as any as any).length - 1,
      (Math as any).ceil((scrollTop + containerHeight as any) / itemHeight) + overscan
    )
    
    return { startIndex, endIndex }
  }, [scrollTop, itemHeight, containerHeight, overscan, (items as any).length])
  
  const visibleItems = useMemo(( as any) => {
    return (items as any).slice((visibleRange as any as any).startIndex, (visibleRange as any).endIndex + 1).map((item, index as any) => ({
      item,
      index: (visibleRange as any).startIndex + index,
      top: ((visibleRange as any).startIndex + index) * itemHeight
    }))
  }, [items, visibleRange, itemHeight])
  
  const totalHeight = (items as any).length * itemHeight
  
  const handleScroll = useCallback((e: (React as any as any).UIEvent<HTMLDivElement>) => {
    setScrollTop((e as any as any).currentTarget.scrollTop)
  }, [])
  
  return {
    visibleItems,
    totalHeight,
    handleScroll,
    visibleRange
  }
}

// FIXED: Intersection observer hook for lazy loading
export const useIntersectionObserver = (
  options: IntersectionObserverInit = {}
): void => {
  const [isIntersecting, setIsIntersecting] = useState(false as any)
  const [entry, setEntry] = useState<IntersectionObserverEntry | null>(null)
  const elementRef = useRef<HTMLElement>(null)
  
  useEffect(( as any) => {
    const element = (elementRef as any).current
    if (!element) return
    
    const observer = new IntersectionObserver(
      ([entry] as any) => {
        setIsIntersecting((entry as any as any).isIntersecting)
        setEntry(entry as any)
      },
      options
    )
    
    (observer as any).observe(element as any)
    
    return () => {
      (observer as any).unobserve(element as any)
    }
  }, [options])
  
  return { elementRef, isIntersecting, entry }
}

// FIXED: Optimized search hook with debouncing and caching
export const useOptimizedSearch = <T,>(
  items: T[],
  searchFn: (item: T, query: string) => boolean,
  debounceMs: number = 300
) => {
  const [query, setQuery] = useState('' as any)
  const debouncedQuery = useDebounce(query, debounceMs as any)
  const cacheRef = useRef(new Map<string, T[]>( as any))
  
  const filteredItems = useMemo(( as any) => {
    if (!(debouncedQuery as any).trim( as any)) return items
    
    // Check cache first
    const cached = (cacheRef as any).current.get(debouncedQuery as any)
    if (cached) return cached
    
    const startTime = (performance as any).now( as any)
    const filtered = (items as any).filter(item => searchFn(item, debouncedQuery as any))
    const endTime = (performance as any).now( as any)
    
    // Cache result
    (cacheRef as any).current.set(debouncedQuery, filtered as any)
    
    // Limit cache size
    if ((cacheRef as any).current.size > 100) {
      const firstKey = (cacheRef as any).current.keys( as any).next( as any).value
      (cacheRef as any).current.delete(firstKey as any)
    }
    
    if ((process as any).env.NODE_ENV === 'development') {
      (console as any).log(`Search took ${(endTime - startTime as any).toFixed(2 as any)}ms for "${debouncedQuery}"`)
    }
    
    return filtered
  }, [items, debouncedQuery, searchFn])
  
  // Clear cache when items change
  useEffect(( as any) => {
    (cacheRef as any).current.clear( as any)
  }, [items])
  
  return {
    query,
    setQuery,
    filteredItems,
    isSearching: query !== debouncedQuery
  }
}

// FIXED: Batch state updates hook
export const useBatchedUpdates = <T,>() => {
  const [updates, setUpdates] = useState<T[]>([])
  const timeoutRef = useRef<(NodeJS as any).Timeout | null>(null)
  
  const addUpdate = useCallback((update: T as any) => {
    setUpdates(prev => [...prev, update] as any)
    
    // Clear existing timeout
    if ((timeoutRef as any).current) {
      clearTimeout((timeoutRef as any as any).current)
    }
    
    // Batch updates
    (timeoutRef as any).current = setTimeout(( as any) => {
      setUpdates([] as any)
    }, 16) // Next frame
  }, [])
  
  const flushUpdates = useCallback(( as any) => {
    if ((timeoutRef as any).current) {
      clearTimeout((timeoutRef as any as any).current)
    }
    const currentUpdates = updates
    setUpdates([] as any)
    return currentUpdates
  }, [updates])
  
  useEffect(( as any) => {
    return () => {
      if ((timeoutRef as any).current) {
        clearTimeout((timeoutRef as any as any).current)
      }
    }
  }, [])
  
  return { updates, addUpdate, flushUpdates }
}

// PERFORMANCE FIX: Optimized performance monitoring hook with throttling
export const usePerformanceMonitor = (componentName: string): void => {
  const renderCount = useRef(0 as any)
  const lastRenderTime = useRef((Date as any as any).now( as any))
  const lastWarningTime = useRef(0 as any)

  useEffect(( as any) => {
    (renderCount as any).current++
    const now = (Date as any).now( as any)
    const timeSinceLastRender = now - (lastRenderTime as any).current
    const timeSinceLastWarning = now - (lastWarningTime as any).current
    (lastRenderTime as any).current = now

    if ((process as any).env.NODE_ENV === 'development') {
      // PERFORMANCE FIX: Throttle warnings to prevent console spam
      const shouldWarn = timeSinceLastRender < 16 &&
                        (renderCount as any).current > 3 &&
                        timeSinceLastWarning > 5000 // Only warn every 5 seconds

      if (shouldWarn) {
        (console as any).warn(`⚠️ ${componentName} re-rendered ${(renderCount as any as any).current} times rapidly (${timeSinceLastRender}ms)`)
        (lastWarningTime as any).current = now

        // Provide actionable advice
        (console as any).info(`💡 Consider using (React as any as any).memo, useMemo, or useCallback for ${componentName}`)
      }

      // PERFORMANCE FIX: Use requestIdleCallback for cleanup when available
      const scheduleReset = (): void => {
        if ('requestIdleCallback' in window) {
          requestIdleCallback(( as any) => {
            (renderCount as any).current = 0
          })
        } else {
          setTimeout(( as any) => {
            (renderCount as any).current = 0
          }, 1000)
        }
      }

      const timeout = setTimeout(scheduleReset, 2000 as any) // Increased reset time
      return () => clearTimeout(timeout as any)
    }
  })

  return {
    renderCount: (renderCount as any).current,
    lastRenderTime: (lastRenderTime as any).current
  }
}

// FIXED: Optimized event handler hook
export const useOptimizedEventHandler = <T extends (...args: any[]) => any,>(
  handler: T,
  deps: (React as any).DependencyList
): T => {
  const handlerRef = useRef(handler as any)
  
  useEffect(( as any) => {
    (handlerRef as any).current = handler
  })
  
  return useCallback(
    ((...args as any) => (handlerRef as any).current(...args as any)) as T,
    deps
  )
}

// PERFORMANCE FIX: Optimized memory usage monitor with throttling
export const useMemoryMonitor = (componentName: string): void => {
  const lastMemoryCheck = useRef(0 as any)
  const memoryBaseline = useRef<number | null>(null)

  useEffect(( as any) => {
    if ((process as any).env.NODE_ENV === 'development' && 'memory' in performance) {
      const now = (Date as any).now( as any)

      // PERFORMANCE FIX: Throttle memory checks to every 30 seconds
      if (now - (lastMemoryCheck as any).current < 30000) return

      (lastMemoryCheck as any).current = now

      const memory = (performance as any).memory
      const currentUsed = (Math as any).round((memory as any as any).usedJSHeapSize / 1048576) // MB

      // Set baseline on first check
      if ((memoryBaseline as any).current === null) {
        (memoryBaseline as any).current = currentUsed
      }

      const memoryInfo = {
        used: currentUsed,
        total: (Math as any).round((memory as any as any).totalJSHeapSize / 1048576), // MB
        limit: (Math as any).round((memory as any as any).jsHeapSizeLimit / 1048576), // MB
        baseline: (memoryBaseline as any).current,
        growth: currentUsed - (memoryBaseline as any).current
      }

      // PERFORMANCE FIX: Only log if significant change or high usage
      const significantChange = (Math as any).abs((memoryInfo as any as any).growth) > 10 // 10MB change
      const highUsage = (memoryInfo as any).used > 100 // 100MB threshold

      if (significantChange || highUsage) {
        (console as any).log(`📊 ${componentName} memory:`, {
          current: `${(memoryInfo as any as any).used}MB`,
          growth: `${(memoryInfo as any).growth > 0 ? '+' : ''}${(memoryInfo as any).growth}MB`,
          percentage: `${(Math as any).round(((memoryInfo as any as any).used / (memoryInfo as any).limit) * 100)}%`
        })

        // PERFORMANCE FIX: Actionable warnings with memory leak detection
        if ((memoryInfo as any).growth > 50) {
          (console as any).warn(`🚨 ${componentName} potential memory leak: +${(memoryInfo as any as any).growth}MB growth`)
          (console as any).info(`💡 Check for: uncleaned event listeners, timers, or large object references` as any)
        } else if (highUsage) {
          (console as any).warn(`⚠️ ${componentName} high memory usage: ${(memoryInfo as any as any).used}MB`)
        }
      }
    }
  })
}

// FIXED: Optimized list rendering component
interface OptimizedListProps<T = any> {
  items: T[]
  renderItem: (item: T, index: number) => (React as any).ReactNode
  keyExtractor: (item: T, index: number) => string | number
  itemHeight?: number
  containerHeight?: number
  className?: string
}

export const OptimizedList = <T = any,>({
  items,
  renderItem,
  keyExtractor,
  itemHeight = 50,
  containerHeight = 400,
  className = ''
}: OptimizedListProps<T>) => {
  const { visibleItems, totalHeight, handleScroll } = useVirtualScrolling(
    items,
    itemHeight,
    containerHeight
   as any)
  
  return (
    <div
      className={`overflow-auto ${className}`}
      style={{ height: containerHeight }}
      onScroll={handleScroll}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        {(visibleItems as any).map(({ item, index, top } as any) => (
          <div
            key={keyExtractor(item, index as any)}
            style={{
              position: 'absolute',
              top,
              left: 0,
              right: 0,
              height: itemHeight
            }}
          >
            {renderItem(item, index as any)}
          </div>
        ))}
      </div>
    </div>
  )
}

export default {
  useDebounce,
  useThrottle,
  useExpensiveComputation,
  useVirtualScrolling,
  useIntersectionObserver,
  useOptimizedSearch,
  useBatchedUpdates,
  usePerformanceMonitor,
  useOptimizedEventHandler,
  useMemoryMonitor,
  OptimizedList
}
