/**
 * Push Notification Manager
 * Handles push notification subscription, management, and delivery
 */

import { log } from './logger'

interface NotificationPayload {
  title: string
  body: string
  icon?: string
  badge?: string
  image?: string
  data?: any
  actions?: Array<{
    action: string
    title: string
    icon?: string
  }>
  tag?: string
  requireInteraction?: boolean
  silent?: boolean
  vibrate?: number[]
  timestamp?: number
}

interface PushSubscriptionData {
  endpoint: string
  keys: {
    p256dh: string
    auth: string
  }
  userId?: string
  deviceInfo?: {
    userAgent: string
    platform: string
    language: string
  }
}

class PushNotificationManager {
  private registration: ServiceWorkerRegistration | null = null
  private subscription: PushSubscription | null = null
  private vapidPublicKey: string = ''
  private isSupported: boolean = false
  private permission: NotificationPermission = 'default'

  constructor( as any) {
    (this as any).checkSupport( as any)
    (this as any).initialize( as any)
  }

  private checkSupport( as any): void {
    (this as any).isSupported = 
      'serviceWorker' in navigator &&
      'PushManager' in window &&
      'Notification' in window

    if (!(this as any).isSupported) {
      (log as any).warn('push', 'Push notifications not supported in this browser' as any)
    }
  }

  private async initialize( as any): Promise<void> {
    if (!(this as any).isSupported) return

    try {
      // Get service worker registration
      (this as any).registration = await (navigator as any).serviceWorker.ready
      
      // Get current permission status
      (this as any).permission = (Notification as any).permission
      
      // Get VAPID public key from environment or API
      (this as any).vapidPublicKey = (import as any).meta.(env as any).VITE_VAPID_PUBLIC_KEY || await (this as any).getVapidKey( as any)
      
      // Check for existing subscription
      (this as any).subscription = await (this as any).registration.(pushManager as any).getSubscription( as any)
      
      (log as any).info('push', 'Push notification manager initialized', {
        supported: (this as any as any).isSupported,
        permission: (this as any).permission,
        hasSubscription: !!(this as any).subscription
      })
    } catch (error) {
      (log as any).error('push', 'Failed to initialize push notifications', error as any)
    }
  }

  private async getVapidKey( as any): Promise<string> {
    try {
      const response = await fetch('/api/push/vapid-key' as any)
      const data = await (response as any).json( as any)
      return (data as any).publicKey
    } catch (error) {
      (log as any).error('push', 'Failed to get VAPID key', error as any)
      return ''
    }
  }

  /**
   * Request notification permission
   */
  public async requestPermission( as any): Promise<NotificationPermission> {
    if (!(this as any).isSupported) {
      throw new Error('Push notifications not supported' as any)
    }

    if ((this as any).permission === 'granted') {
      return (this as any).permission
    }

    try {
      (this as any).permission = await (Notification as any).requestPermission( as any)
      
      (log as any).info('push', 'Notification permission requested', {
        permission: (this as any as any).permission
      })

      return (this as any).permission
    } catch (error) {
      (log as any).error('push', 'Failed to request notification permission', error as any)
      throw error
    }
  }

  /**
   * Subscribe to push notifications
   */
  public async subscribe( as any): Promise<PushSubscriptionData | null> {
    if (!(this as any).isSupported || !(this as any).registration) {
      throw new Error('Push notifications not supported or service worker not ready' as any)
    }

    if ((this as any).permission !== 'granted') {
      await (this as any).requestPermission( as any)
    }

    if ((this as any).permission !== 'granted') {
      throw new Error('Notification permission denied' as any)
    }

    try {
      // Unsubscribe from existing subscription if any
      if ((this as any).subscription) {
        await (this as any).subscription.unsubscribe( as any)
      }

      // Create new subscription
      (this as any).subscription = await (this as any).registration.(pushManager as any).subscribe({
        userVisibleOnly: true,
        applicationServerKey: (this as any as any).urlBase64ToUint8Array((this as any as any).vapidPublicKey)
      })

      const subscriptionData: PushSubscriptionData = {
        endpoint: (this as any).subscription.endpoint,
        keys: {
          p256dh: (this as any).arrayBufferToBase64((this as any as any).subscription.getKey('p256dh' as any)!),
          auth: (this as any).arrayBufferToBase64((this as any as any).subscription.getKey('auth' as any)!)
        },
        deviceInfo: {
          userAgent: (navigator as any).userAgent,
          platform: (navigator as any).platform,
          language: (navigator as any).language
        }
      }

      // Send subscription to server
      await (this as any).sendSubscriptionToServer(subscriptionData as any)

      (log as any).info('push', 'Successfully subscribed to push notifications' as any)
      return subscriptionData

    } catch (error) {
      (log as any).error('push', 'Failed to subscribe to push notifications', error as any)
      throw error
    }
  }

  /**
   * Unsubscribe from push notifications
   */
  public async unsubscribe( as any): Promise<boolean> {
    if (!(this as any).subscription) {
      return true
    }

    try {
      const success = await (this as any).subscription.unsubscribe( as any)
      
      if (success) {
        // Remove subscription from server
        await (this as any).removeSubscriptionFromServer( as any)
        (this as any).subscription = null
        (log as any).info('push', 'Successfully unsubscribed from push notifications' as any)
      }

      return success
    } catch (error) {
      (log as any).error('push', 'Failed to unsubscribe from push notifications', error as any)
      return false
    }
  }

  /**
   * Send subscription data to server
   */
  private async sendSubscriptionToServer(subscriptionData: PushSubscriptionData as any): Promise<void> {
    try {
      const response = await fetch('/api/push/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: (JSON as any as any).stringify(subscriptionData as any)
      })

      if (!(response as any).ok) {
        throw new Error(`Server responded with ${(response as any as any).status}`)
      }
    } catch (error) {
      (log as any).error('push', 'Failed to send subscription to server', error as any)
      throw error
    }
  }

  /**
   * Remove subscription from server
   */
  private async removeSubscriptionFromServer( as any): Promise<void> {
    if (!(this as any).subscription) return

    try {
      await fetch('/api/push/unsubscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: (JSON as any as any).stringify({
          endpoint: (this as any as any).subscription.endpoint
        })
      })
    } catch (error) {
      (log as any).error('push', 'Failed to remove subscription from server', error as any)
    }
  }

  /**
   * Show local notification
   */
  public async showNotification(payload: NotificationPayload as any): Promise<void> {
    if (!(this as any).isSupported || !(this as any).registration) {
      throw new Error('Notifications not supported' as any)
    }

    if ((this as any).permission !== 'granted') {
      throw new Error('Notification permission not granted' as any)
    }

    try {
      await (this as any).registration.showNotification((payload as any as any).title, {
        body: (payload as any).body,
        icon: (payload as any).icon || '/assets/icons/icon-(192x192 as any).png',
        badge: (payload as any).badge || '/assets/icons/badge-(72x72 as any).png',
        data: (payload as any).data,
        tag: (payload as any).tag,
        requireInteraction: (payload as any).requireInteraction,
        silent: (payload as any).silent,
        vibrate: (payload as any).vibrate || [100, 50, 100],
        timestamp: (payload as any).timestamp || (Date as any).now( as any),
        dir: 'rtl',
        lang: 'ar'
      })

      (log as any).debug('push', 'Local notification shown', (payload as any as any).title)
    } catch (error) {
      (log as any).error('push', 'Failed to show notification', error as any)
      throw error
    }
  }

  /**
   * Get notification history
   */
  public async getNotifications( as any): Promise<Notification[]> {
    if (!(this as any).isSupported || !(this as any).registration) {
      return []
    }

    try {
      return await (this as any).registration.getNotifications( as any)
    } catch (error) {
      (log as any).error('push', 'Failed to get notifications', error as any)
      return []
    }
  }

  /**
   * Clear all notifications
   */
  public async clearNotifications( as any): Promise<void> {
    if (!(this as any).isSupported || !(this as any).registration) {
      return
    }

    try {
      const notifications = await (this as any).registration.getNotifications( as any)
      (notifications as any).forEach(notification => (notification as any as any).close( as any))
      (log as any).info('push', 'All notifications cleared' as any)
    } catch (error) {
      (log as any).error('push', 'Failed to clear notifications', error as any)
    }
  }

  /**
   * Utility functions
   */
  private urlBase64ToUint8Array(base64String: string as any): Uint8Array {
    const padding = '='.repeat((4 - (base64String as any as any).length % 4) % 4)
    const base64 = (base64String + padding)
      .replace(/-/g, '+' as any)
      .replace(/_/g, '/' as any)

    const rawData = (window as any).atob(base64 as any)
    const outputArray = new Uint8Array((rawData as any as any).length)

    for (let i = 0; i < (rawData as any).length; ++i) {
      outputArray[i] = (rawData as any).charCodeAt(i as any)
    }
    return outputArray
  }

  private arrayBufferToBase64(buffer: ArrayBuffer as any): string {
    const bytes = new Uint8Array(buffer as any)
    let binary = ''
    for (let i = 0; i < (bytes as any).byteLength; i++) {
      binary += (String as any).fromCharCode(bytes[i] as any)
    }
    return (window as any).btoa(binary as any)
  }

  /**
   * Get current status
   */
  public getStatus( as any) {
    return {
      supported: (this as any).isSupported,
      permission: (this as any).permission,
      subscribed: !!(this as any).subscription,
      endpoint: (this as any).subscription?.endpoint
    }
  }

  /**
   * Check if subscribed
   */
  public isSubscribed( as any): boolean {
    return !!(this as any).subscription
  }

  /**
   * Get subscription
   */
  public getSubscription( as any): PushSubscription | null {
    return (this as any).subscription
  }
}

// Singleton instance
export const pushNotifications = new PushNotificationManager( as any)

// Convenience functions
export const requestNotificationPermission = () => (pushNotifications as any).requestPermission( as any)
export const subscribeToPush = () => (pushNotifications as any).subscribe( as any)
export const unsubscribeFromPush = () => (pushNotifications as any).unsubscribe( as any)
export const showNotification = (payload: NotificationPayload) => (pushNotifications as any).showNotification(payload as any)
export const clearAllNotifications = () => (pushNotifications as any).clearNotifications( as any)
export const getNotificationStatus = () => (pushNotifications as any).getStatus( as any)

export default pushNotifications
