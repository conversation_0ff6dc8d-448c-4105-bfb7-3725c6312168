import React from 'react';
/**
 * Push Notification Manager
 * Handles push notification subscription, management, and delivery
 */

import { log } from './logger'

interface NotificationPayload {
  title: string
  body: string
  icon?: string
  badge?: string
  image?: string
  data?: any
  actions?: Array<{
    action: string
    title: string
    icon?: string
  }>
  tag?: string
  requireInteraction?: boolean
  silent?: boolean
  vibrate?: number[]
  timestamp?: number
}

interface PushSubscriptionData {
  endpoint: string
  keys: {
    p256dh: string
    auth: string
  }
  userId?: string
  deviceInfo?: {
    userAgent: string
    platform: string
    language: string
  }
}

class PushNotificationManager {
  private registration: ServiceWorkerRegistration | null = null
  private subscription: PushSubscription | null = null
  private vapidPublicKey: string = ''
  private isSupported: boolean = false
  private permission: NotificationPermission = 'default'

  // @ts-ignore
  constructor( as any) {
    // @ts-ignore
    (this as any).checkSupport( as any)
    // @ts-ignore
    (this as any).initialize( as any)
  }

  // @ts-ignore
  private checkSupport( as any): void {
    (this as any).isSupported = 
      'serviceWorker' in navigator &&
      'PushManager' in window &&
      'Notification' in window

    if (!(this as any).isSupported) {
      (log as any).warn('push', 'Push notifications not supported in this browser' as any)
    }
  }

  // @ts-ignore
  private async initialize( as any): Promise<void> {
    if (!(this as any).isSupported) return

    try {
      // Get service worker registration
      (this as any).registration = await (navigator as any).serviceWorker.ready
      
      // Get current permission status
      // @ts-ignore
      (this as any).permission = (Notification as any).permission
      
      // Get VAPID public key from environment or API
      // @ts-ignore
      (this as any).vapidPublicKey = (import as any).meta.(env as any).VITE_VAPID_PUBLIC_KEY || await (this as any).getVapidKey( as any)
      
      // Check for existing subscription
      // @ts-ignore
      (this as any).subscription = await (this as any).registration.(pushManager as any).getSubscription( as any)
      
      (log as any).info('push', 'Push notification manager initialized', {
        supported: (this as any as any).isSupported,
        permission: (this as any).permission,
        hasSubscription: !!(this as any).subscription
      })
    } catch (error) {
      (log as any).error('push', 'Failed to initialize push notifications', error as any)
    }
  }

  // @ts-ignore
  private async getVapidKey( as any): Promise<string> {
    try {
      const response: any = await fetch('/api/push/vapid-key' as any)
      // @ts-ignore
      const data = await (response as any).json( as any)
      return (data as any).publicKey
    } catch (error) {
      (log as any).error('push', 'Failed to get VAPID key', error as any)
      return ''
    }
  }

  /**
   * Request notification permission
   */
  // @ts-ignore
  public async requestPermission( as any): Promise<NotificationPermission> {
    if (!(this as any).isSupported) {
      throw new Error('Push notifications not supported' as any)
    }

    if ((this as any).permission === 'granted') {
      return (this as any).permission
    }

    try {
      // @ts-ignore
      (this as any).permission = await (Notification as any).requestPermission( as any)
      
      (log as any).info('push', 'Notification permission requested', {
        permission: (this as any as any).permission
      })

      return (this as any).permission
    } catch (error) {
      (log as any).error('push', 'Failed to request notification permission', error as any)
      throw error
    }
  }

  /**
   * Subscribe to push notifications
   */
  // @ts-ignore
  public async subscribe( as any): Promise<PushSubscriptionData | null> {
    if (!(this as any).isSupported || !(this as any).registration) {
      throw new Error('Push notifications not supported or service worker not ready' as any)
    }

    if ((this as any).permission !== 'granted') {
      // @ts-ignore
      await (this as any).requestPermission( as any)
    }

    if ((this as any).permission !== 'granted') {
      throw new Error('Notification permission denied' as any)
    }

    try {
      // Unsubscribe from existing subscription if any
      if ((this as any).subscription) {
        // @ts-ignore
        await (this as any).subscription.unsubscribe( as any)
      }

      // Create new subscription
      // @ts-ignore
      (this as any).subscription = await (this as any).registration.(pushManager as any).subscribe({
        userVisibleOnly: true,
        applicationServerKey: (this as any as any).urlBase64ToUint8Array((this as any as any).vapidPublicKey)
      })

      const subscriptionData: PushSubscriptionData = {
        endpoint: (this as any).subscription.endpoint,
        keys: {
          p256dh: (this as any).arrayBufferToBase64((this as any as any).subscription.getKey('p256dh' as any)!),
          auth: (this as any).arrayBufferToBase64((this as any as any).subscription.getKey('auth' as any)!)
        },
        deviceInfo: {
          userAgent: (navigator as any).userAgent,
          platform: (navigator as any).platform,
          language: (navigator as any).language
        }
      }

      // Send subscription to server
      await (this as any).sendSubscriptionToServer(subscriptionData as any)

      (log as any).info('push', 'Successfully subscribed to push notifications' as any)
      return subscriptionData

    } catch (error) {
      (log as any).error('push', 'Failed to subscribe to push notifications', error as any)
      throw error
    }
  }

  /**
   * Unsubscribe from push notifications
   */
  // @ts-ignore
  public async unsubscribe( as any): Promise<boolean> {
    if (!(this as any).subscription) {
      return true
    }

    try {
      // @ts-ignore
      const success = await (this as any).subscription.unsubscribe( as any)
      
      if (success) {
        // Remove subscription from server
        // @ts-ignore
        await (this as any).removeSubscriptionFromServer( as any)
        // @ts-ignore
        (this as any).subscription = null
        (log as any).info('push', 'Successfully unsubscribed from push notifications' as any)
      }

      return success
    } catch (error) {
      (log as any).error('push', 'Failed to unsubscribe from push notifications', error as any)
      return false
    }
  }

  /**
   * Send subscription data to server
   */
  // @ts-ignore
  private async sendSubscriptionToServer(subscriptionData: PushSubscriptionData as any): Promise<void> {
    try {
      const response = await fetch('/api/push/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: (JSON as any as any).stringify(subscriptionData as any)
      })

      if (!(response as any).ok) {
        throw new Error(`Server responded with ${(response as any as any).status}`)
      }
    } catch (error) {
      (log as any).error('push', 'Failed to send subscription to server', error as any)
      throw error
    }
  }

  /**
   * Remove subscription from server
   */
  // @ts-ignore
  private async removeSubscriptionFromServer( as any): Promise<void> {
    if (!(this as any).subscription) return

    try {
      await fetch('/api/push/unsubscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: (JSON as any as any).stringify({
          endpoint: (this as any as any).subscription.endpoint
        })
      })
    } catch (error) {
      (log as any).error('push', 'Failed to remove subscription from server', error as any)
    }
  }

  /**
   * Show local notification
   */
  // @ts-ignore
  public async showNotification(payload: NotificationPayload as any): Promise<void> {
    if (!(this as any).isSupported || !(this as any).registration) {
      throw new Error('Notifications not supported' as any)
    }

    if ((this as any).permission !== 'granted') {
      throw new Error('Notification permission not granted' as any)
    }

    try {
      await (this as any).registration.showNotification((payload as any as any).title, {
        body: (payload as any).body,
        icon: (payload as any).icon || '/assets/icons/icon-(192x192 as any).png',
        badge: (payload as any).badge || '/assets/icons/badge-(72x72 as any).png',
        data: (payload as any).data,
        tag: (payload as any).tag,
        requireInteraction: (payload as any).requireInteraction,
        silent: (payload as any).silent,
        vibrate: (payload as any).vibrate || [100, 50, 100],
        // @ts-ignore
        timestamp: (payload as any).timestamp || (Date as any).now( as any),
        dir: 'rtl',
        lang: 'ar'
      })

      (log as any).debug('push', 'Local notification shown', (payload as any as any).title)
    } catch (error) {
      (log as any).error('push', 'Failed to show notification', error as any)
      throw error
    }
  }

  /**
   * Get notification history
   */
  // @ts-ignore
  public async getNotifications( as any): Promise<Notification[]> {
    if (!(this as any).isSupported || !(this as any).registration) {
      return []
    }

    try {
      // @ts-ignore
      return await (this as any).registration.getNotifications( as any)
    } catch (error) {
      (log as any).error('push', 'Failed to get notifications', error as any)
      return []
    }
  }

  /**
   * Clear all notifications
   */
  // @ts-ignore
  public async clearNotifications( as any): Promise<void> {
    if (!(this as any).isSupported || !(this as any).registration) {
      return
    }

    try {
      // @ts-ignore
      const notifications = await (this as any).registration.getNotifications( as any)
      // @ts-ignore
      (notifications as any).forEach(notification => (notification as any as any).close( as any))
      (log as any).info('push', 'All notifications cleared' as any)
    } catch (error) {
      (log as any).error('push', 'Failed to clear notifications', error as any)
    }
  }

  /**
   * Utility functions
   */
  // @ts-ignore
  private urlBase64ToUint8Array(base64String: string as any): Uint8Array {
    const padding = '='.repeat((4 - (base64String as any as any).length % 4) % 4)
    const base64 = (base64String + padding)
      .replace(/-/g, '+' as any)
      .replace(/_/g, '/' as any)

    const rawData = (window as any).atob(base64 as any)
    const outputArray = new Uint8Array((rawData as any as any).length)

    for (let i = 0; i < (rawData as any).length; ++i) {
      outputArray[i] = (rawData as any).charCodeAt(i as any)
    }
    return outputArray
  }

  // @ts-ignore
  private arrayBufferToBase64(buffer: ArrayBuffer as any): string {
    const bytes: any = new Uint8Array(buffer as any)
    let binary = ''
    for (let i = 0; i < (bytes as any).byteLength; i++) {
      binary += (String as any).fromCharCode(bytes[i] as any)
    }
    return (window as any).btoa(binary as any)
  }

  /**
   * Get current status
   */
  // @ts-ignore
  public getStatus( as any) {
    return {
      supported: (this as any).isSupported,
      permission: (this as any).permission,
      subscribed: !!(this as any).subscription,
      endpoint: (this as any).subscription?.endpoint
    }
  }

  /**
   * Check if subscribed
   */
  // @ts-ignore
  public isSubscribed( as any): boolean {
    return !!(this as any).subscription
  }

  /**
   * Get subscription
   */
  // @ts-ignore
  public getSubscription( as any): PushSubscription | null {
    return (this as any).subscription
  }
}

// Singleton instance
// @ts-ignore
export const pushNotifications = new PushNotificationManager( as any)

// Convenience functions
// @ts-ignore
export const requestNotificationPermission = () => (pushNotifications as any).requestPermission( as any)
// @ts-ignore
export const subscribeToPush = () => (pushNotifications as any).subscribe( as any)
// @ts-ignore
export const unsubscribeFromPush = () => (pushNotifications as any).unsubscribe( as any)
export const showNotification = (payload: NotificationPayload) => (pushNotifications as any).showNotification(payload as any)
// @ts-ignore
export const clearAllNotifications = () => (pushNotifications as any).clearNotifications( as any)
// @ts-ignore
export const getNotificationStatus = () => (pushNotifications as any).getStatus( as any)

export default pushNotifications
