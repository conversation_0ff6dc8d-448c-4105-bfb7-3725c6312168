import React from 'react';
/**
 * Security Headers & Content Security Policy
 * Comprehensive security configuration for production deployment
 */

import { log } from './logger'

interface CSPConfig {
  'default-src': string[]
  'script-src': string[]
  'style-src': string[]
  'img-src': string[]
  'font-src': string[]
  'connect-src': string[]
  'media-src': string[]
  'object-src': string[]
  'frame-src': string[]
  'worker-src': string[]
  'manifest-src': string[]
  'base-uri': string[]
  'form-action': string[]
  'frame-ancestors': string[]
  'upgrade-insecure-requests': boolean
  'block-all-mixed-content': boolean
}

class SecurityHeadersManager {
  private cspConfig: CSPConfig
  private nonce: string
  private isDevelopment: boolean

  constructor() {
    this.isDevelopment = import.meta.env.DEV
    this.nonce = this.generateNonce()
    this.cspConfig = this.getCSPConfig()
  }

  /**
   * Generate cryptographically secure nonce
   */
  private generateNonce(): string {
    const array: any = new Uint8Array(16)
    crypto.getRandomValues(array)
    return btoa(String.fromCharCode(...array))
  }

  /**
   * Get Content Security Policy configuration
   */
  private getCSPConfig(): CSPConfig {
    const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:8000'
    const wsUrl = import.meta.env.VITE_WS_URL || 'ws://localhost:8000'
    
    return {
      'default-src': ["'self'"],
      'script-src': [
        "'self'",
        `'nonce-${this.nonce}'`,
        ...(this.isDevelopment ? ["'unsafe-eval'", "'unsafe-inline'"] : []),
        'https://cdn.jsdelivr.net',
        'https://unpkg.com'
      ],
      'style-src': [
        "'self'",
        "'unsafe-inline'", // Required for CSS-in-JS and dynamic styles
        'https://fonts.googleapis.com',
        'https://cdn.jsdelivr.net'
      ],
      'img-src': [
        "'self'",
        'data:',
        'blob:',
        'https:',
        apiUrl
      ],
      'font-src': [
        "'self'",
        'data:',
        'https://fonts.gstatic.com',
        'https://cdn.jsdelivr.net'
      ],
      'connect-src': [
        "'self'",
        apiUrl,
        wsUrl,
        'https://api.github.com', // For version checks
        ...(this.isDevelopment ? ['ws://localhost:*', 'http://localhost:*'] : [])
      ],
      'media-src': [
        "'self'",
        'data:',
        'blob:'
      ],
      'object-src': ["'none'"],
      'frame-src': [
        "'self'",
        'https://www.youtube.com', // If embedding videos
        'https://player.vimeo.com'
      ],
      'worker-src': [
        "'self'",
        'blob:'
      ],
      'manifest-src': ["'self'"],
      'base-uri': ["'self'"],
      'form-action': [
        "'self'",
        apiUrl
      ],
      'frame-ancestors': ["'none'"],
      'upgrade-insecure-requests': !this.isDevelopment,
      'block-all-mixed-content': !this.isDevelopment
    }
  }

  /**
   * Generate CSP header string
   */
  generateCSPHeader(): string {
    const directives = Object.entries(this.cspConfig)
      .filter(([, value]) => {
        if (typeof value === 'boolean') return value
        return Array.isArray(value) && value.length > 0
      })
      .map(([directive, value]) => {
        if (typeof value === 'boolean') {
          return directive
        }
        return `${directive} ${(value as string[]).join(' ')}`
      })

    return directives.join('; ')
  }

  /**
   * Apply security headers to document
   * Note: Some headers like X-Frame-Options and frame-ancestors only work via HTTP headers
   */
  applySecurityHeaders(): void {
    // TEMPORARILY DISABLE SECURITY HEADERS TO FIX CONSOLE WARNINGS
    // These headers should be set by the server, not via JavaScript
    if (this.isDevelopment) {
      log.info('security', 'Security headers disabled in development to avoid console warnings')
      return
    }

    // CSP Meta tag (fallback for when server headers aren't available)
    // Note: frame-ancestors directive is ignored in meta tags
    const cspConfig: any = { ...this.cspConfig }
    delete (cspConfig as any)['frame-ancestors'] // Remove frame-ancestors for meta tag

    const cspMeta = document.createElement('meta')
    cspMeta.httpEquiv = 'Content-Security-Policy'
    cspMeta.content = this.generateCSPHeaderForMeta(cspConfig)
    document.head.appendChild(cspMeta)

    // Referrer Policy (this works in meta tags)
    const referrerMeta = document.createElement('meta')
    referrerMeta.name = 'referrer'
    referrerMeta.content = 'strict-origin-when-cross-origin'
    document.head.appendChild(referrerMeta)

    log.info('security', 'Security headers applied (meta tag compatible only)', {
      csp: this.generateCSPHeaderForMeta(cspConfig),
      nonce: this.nonce,
      note: 'X-Frame-Options and frame-ancestors require server-side HTTP headers'
    })
  }

  /**
   * Generate CSP header string for meta tags (excludes unsupported directives)
   */
  private generateCSPHeaderForMeta(config: Partial<CSPConfig>): string {
    const directives = Object.entries(config)
      .filter(([, value]) => {
        if (typeof value === 'boolean') return value
        return Array.isArray(value) && value.length > 0
      })
      .map(([directive, value]) => {
        if (typeof value === 'boolean') {
          return directive
        }
        return `${directive} ${(value as string[]).join(' ')}`
      })

    return directives.join('; ')
  }

  /**
   * Validate current page against CSP
   */
  validateCSP(): void {
    // Check for inline scripts without nonce
    const inlineScripts: any = document.querySelectorAll('script:not([src]):not([nonce])')
    if (inlineScripts.length > 0) {
      log.warn('security', `Found ${inlineScripts.length} inline scripts without nonce`)
    }

    // Check for inline styles (acceptable for CSS-in-JS)
    const inlineStyles = document.querySelectorAll('style:not([nonce])')
    if (inlineStyles.length > 10) { // Threshold for CSS-in-JS
      log.warn('security', `Found ${inlineStyles.length} inline styles`)
    }

    // Check for external resources not in CSP
    const externalScripts = document.querySelectorAll('script[src]')
    externalScripts.forEach(script => {
      const src = (script as HTMLScriptElement).src
      if (!this.isAllowedSource(src, 'script-src')) {
        log.warn('security', `External script not in CSP: ${src}`)
      }
    })
  }

  /**
   * Check if source is allowed by CSP directive
   */
  private isAllowedSource(src: string, directive: keyof CSPConfig): boolean {
    const allowedSources = this.cspConfig[directive] as string[]
    if (!allowedSources) return false

    return allowedSources.some(source => {
      if (source === "'self'") {
        return new URL(src).origin === window.location.origin
      }
      if (source === "'unsafe-inline'" || source === "'unsafe-eval'") {
        return true
      }
      if (source.startsWith('https://')) {
        return src.startsWith(source) || new URL(src).origin === new URL(source).origin
      }
      return false
    })
  }

  /**
   * Get current nonce for script tags
   */
  getNonce(): string {
    return this.nonce
  }

  /**
   * Get recommended server-side HTTP headers for production
   */
  getServerHeaders(): Record<string, string> {
    return {
      'Content-Security-Policy': this.generateCSPHeader(),
      'X-Frame-Options': 'DENY',
      'X-Content-Type-Options': 'nosniff',
      'X-XSS-Protection': '1; mode=block',
      'Referrer-Policy': 'strict-origin-when-cross-origin',
      'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',
      'Permissions-Policy': 'camera=(), microphone=(), geolocation=(), payment=()',
    }
  }

  /**
   * Log server header recommendations for production deployment
   */
  logServerHeaderRecommendations(): void {
    const headers: any = this.getServerHeaders()
    log.info('security', 'Recommended server-side HTTP headers for production:', headers)

    console.group('🔒 Security Headers for Production Deployment')
    console.log('Add these headers to your web server configuration:')
    Object.entries(headers).forEach(([header, value]) => {
      console.log(`${header}: ${value}`)
    })
    console.log('\nFor Nginx, add to your server block:')
    Object.entries(headers).forEach(([header, value]) => {
      console.log(`add_header ${header} "${value}";`)
    })
    console.log('\nFor Apache, add to .htaccess or virtual host:')
    Object.entries(headers).forEach(([header, value]) => {
      console.log(`Header always set ${header} "${value}"`)
    })
    console.groupEnd()
  }

  /**
   * Report CSP violations
   */
  setupCSPReporting(): void {
    document.addEventListener('securitypolicyviolation', (e) => {
      log.error('security', 'CSP Violation', {
        blockedURI: e.blockedURI,
        violatedDirective: e.violatedDirective,
        originalPolicy: e.originalPolicy,
        disposition: e.disposition
      })

      // Send to monitoring service in production
      if (import.meta.env.PROD) {
        this.reportViolation({
          blockedURI: e.blockedURI,
          violatedDirective: e.violatedDirective,
          userAgent: navigator.userAgent,
          timestamp: new Date().toISOString()
        })
      }
    })
  }

  /**
   * Send violation report to monitoring service
   */
  private async reportViolation(violation: any): Promise<void> {
    try {
      // In production, send to your monitoring service
      const response = await fetch('/api/security/csp-violation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(violation)
      })

      if (!response.ok) {
        throw new Error(`Failed to report violation: ${response.status}`)
      }
    } catch (error) {
      log.error('security', 'Failed to report CSP violation', error)
    }
  }
}

// Singleton instance
export const securityHeaders = new SecurityHeadersManager()

// Initialize security headers
export const initializeSecurity = () => {
  securityHeaders.applySecurityHeaders()
  securityHeaders.setupCSPReporting()

  // Show server header recommendations in development
  if (import.meta.env.DEV) {
    securityHeaders.logServerHeaderRecommendations()
  }

  // Validate CSP after page load
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(() => securityHeaders.validateCSP(), 1000)
    })
  } else {
    setTimeout(() => securityHeaders.validateCSP(), 1000)
  }
}

// Rate limiting functionality
interface RateLimitEntry {
  count: number
  resetTime: number
  blocked: boolean
}

class RateLimiter {
  private storage: Map<string, RateLimitEntry> = new Map()

  checkRateLimit(key: string, maxAttempts: number, windowMs: number): {
    allowed: boolean
    remaining: number
    resetTime: number
    blocked: boolean
  } {
    const now = Date.now()
    const entry = this.storage.get(key)

    if (!entry || now > entry.resetTime) {
      const newEntry: RateLimitEntry = {
        count: 1,
        resetTime: now + windowMs,
        blocked: false
      }
      this.storage.set(key, newEntry)

      return {
        allowed: true,
        remaining: maxAttempts - 1,
        resetTime: newEntry.resetTime,
        blocked: false
      }
    }

    if (entry.blocked) {
      return {
        allowed: false,
        remaining: 0,
        resetTime: entry.resetTime,
        blocked: true
      }
    }

    entry.count++

    if (entry.count > maxAttempts) {
      entry.blocked = true
      log.warn('security', 'Rate limit exceeded', { key, count: entry.count, maxAttempts })

      return {
        allowed: false,
        remaining: 0,
        resetTime: entry.resetTime,
        blocked: true
      }
    }

    return {
      allowed: true,
      remaining: maxAttempts - entry.count,
      resetTime: entry.resetTime,
      blocked: false
    }
  }

  resetRateLimit(key: string): void {
    this.storage.delete(key)
  }
}

const rateLimiter = new RateLimiter()

export function checkLoginRateLimit(identifier: string) {
  const key = `login:${identifier}`
  return rateLimiter.checkRateLimit(key, 5, 15 * 60 * 1000) // 5 attempts per 15 minutes
}

export function logSecurityEvent(event: string, details: Record<string, any>): void {
  log.warn('security', `Security event: ${event}`, {
    timestamp: new Date().toISOString(),
    userAgent: navigator.userAgent,
    url: window.location.href,
    ...details
  })
}

export default securityHeaders
