import React from 'react';
/**
 * Service Checker Utility
 * Checks if backend services are running and provides fallbacks
 */

export interface ServiceStatus {
  name: string
  url: string
  status: 'online' | 'offline' | 'unknown'
  lastChecked: Date
}

export class ServiceChecker {
  private static instance: ServiceChecker
  private services: Map<string, ServiceStatus> = new Map()

  private constructor() {
    // Initialize known services (both on port 8002)
    this.services.set('main-api', {
      name: 'Main API',
      url: 'http://localhost:8000',
      status: 'unknown',
      lastChecked: new Date()
    })

    this.services.set('pdf-api', {
      name: 'PDF Service',
      url: 'http://localhost:8000',
      status: 'unknown',
      lastChecked: new Date()
    })
  }

  static getInstance(): ServiceChecker {
    if (!ServiceChecker.instance) {
      ServiceChecker.instance = new ServiceChecker()
    }
    return ServiceChecker.instance
  }

  /**
   * Check if a service is online
   */
  async checkService(serviceKey: string, timeout: number = 3000): Promise<boolean> {
    const service = this.services.get(serviceKey)
    if (!service) {
      console.warn(`Unknown service: ${serviceKey}`)
      return false
    }

    try {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), timeout)

      // Try the main API endpoint instead of /health/
      const response = await fetch(`${service.url}/api/`, {
        method: 'GET',
        signal: controller.signal,
        mode: 'cors'
      })

      clearTimeout(timeoutId)

      // Consider 200, 404, or any response as "online" - just not network errors
      const isOnline = response.status < 500
      this.updateServiceStatus(serviceKey, isOnline ? 'online' : 'offline')
      return isOnline

    } catch (error) {
      console.warn(`Service ${service.name} check failed:`, error)
      this.updateServiceStatus(serviceKey, 'offline')
      return false
    }
  }

  /**
   * Check all services
   */
  async checkAllServices(): Promise<Map<string, boolean>> {
    const results = new Map<string, boolean>()

    for (const [key] of this.services) {
      const isOnline = await this.checkService(key)
      results.set(key, isOnline)
    }

    return results
  }

  /**
   * Get service status
   */
  getServiceStatus(serviceKey: string): ServiceStatus | undefined {
    return this.services.get(serviceKey)
  }

  /**
   * Get all service statuses
   */
  getAllServiceStatuses(): ServiceStatus[] {
    return Array.from(this.services.values())
  }

  /**
   * Update service status
   */
  private updateServiceStatus(serviceKey: string, status: 'online' | 'offline' | 'unknown') {
    const service = this.services.get(serviceKey)
    if (service) {
      service.status = status
      service.lastChecked = new Date()
    }
  }

  /**
   * Check if PDF service is available
   */
  async isPDFServiceAvailable(): Promise<boolean> {
    return this.checkService('pdf-api', 2000)
  }

  /**
   * Check if main API is available
   */
  async isMainAPIAvailable(): Promise<boolean> {
    return this.checkService('main-api', 2000)
  }

  /**
   * Get user-friendly error message
   */
  getServiceErrorMessage(serviceKey: string, language: 'ar' | 'en' = 'en'): string {
    const service = this.services.get(serviceKey)
    if (!service) {
      return language === 'ar' ? 'خدمة غير معروفة' : 'Unknown service'
    }

    const messages = {
      'main-api': {
        ar: 'الخدمة الرئيسية غير متاحة. يرجى التأكد من تشغيل الخادم على المنفذ 8000',
        en: 'Main API service is not available. Please ensure the server is running on port 8000'
      },
      'pdf-api': {
        ar: 'خدمة PDF غير متاحة. يرجى التأكد من تشغيل خادم PDF على المنفذ 8000',
        en: 'PDF service is not available. Please ensure the PDF server is running on port 8000'
      }
    }

    return messages[serviceKey as keyof typeof messages]?.[language] ||
           (language === 'ar' ? 'خدمة غير متاحة' : 'Service not available')
  }
}

// Export singleton instance
export const serviceChecker = ServiceChecker.getInstance()

// Export utility functions
export const checkPDFService = () => serviceChecker.isPDFServiceAvailable()
export const checkMainAPI = () => serviceChecker.isMainAPIAvailable()
export const getServiceError = (service: string, language: 'ar' | 'en' = 'en') =>
  serviceChecker.getServiceErrorMessage(service, language)
