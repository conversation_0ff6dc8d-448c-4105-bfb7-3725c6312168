import React from 'react';
/**
 * ARCHITECTURE FIX: Standardized State Management Patterns
 * Provides consistent state management across the application
 */

import { useCallback, useReducer, useRef, useEffect } from 'react'

export enum AsyncState {
  IDLE = 'idle',
  LOADING = 'loading',
  SUCCESS = 'success',
  ERROR = 'error'
}

export interface StandardAsyncState<T> {
  data: T | null
  status: AsyncState
  error: string | null
  lastUpdated: number | null
  isLoading: boolean
  isSuccess: boolean
  isError: boolean
  isIdle: boolean
}

export interface AsyncAction<T> {
  type: 'SET_LOADING' | 'SET_SUCCESS' | 'SET_ERROR' | 'RESET'
  payload?: T
  error?: string
}

/**
 * ARCHITECTURE FIX: Standardized async state reducer
 */
function asyncStateReducer<T>(
  state: StandardAsyncState<T>,
  action: AsyncAction<T>
): StandardAsyncState<T> {
  switch (action.type) {
    case 'SET_LOADING':
      return {
        ...state,
        status: AsyncState.LOADING,
        isLoading: true,
        isSuccess: false,
        isError: false,
        isIdle: false,
        error: null
      }
    
    case 'SET_SUCCESS':
      return {
        ...state,
        data: action.payload || null,
        status: AsyncState.SUCCESS,
        isLoading: false,
        isSuccess: true,
        isError: false,
        isIdle: false,
        error: null,
        lastUpdated: Date.now()
      }
    
    case 'SET_ERROR':
      return {
        ...state,
        status: AsyncState.ERROR,
        isLoading: false,
        isSuccess: false,
        isError: true,
        isIdle: false,
        error: action.error || 'An error occurred'
      }
    
    case 'RESET':
      return createInitialAsyncState<T>()
    
    default:
      return state
  }
}

function createInitialAsyncState<T>(): StandardAsyncState<T> {
  return {
    data: null,
    status: AsyncState.IDLE,
    error: null,
    lastUpdated: null,
    isLoading: false,
    isSuccess: false,
    isError: false,
    isIdle: true
  }
}

/**
 * ARCHITECTURE FIX: Standardized async state hook
 */
export function useAsyncState<T>(initialData?: T): [
  StandardAsyncState<T>,
  {
    setLoading: () => void
    setSuccess: (data: T) => void
    setError: (error: string) => void
    reset: () => void
    execute: (asyncFn: () => Promise<T>) => Promise<void>
  }
] {
  const [state, dispatch] = useReducer(
    asyncStateReducer<T>,
    initialData ? { ...createInitialAsyncState<T>(), data: initialData } : createInitialAsyncState<T>()
  )

  const setLoading = useCallback(() => {
    dispatch({ type: 'SET_LOADING' })
  }, [])

  const setSuccess = useCallback((data: T) => {
    dispatch({ type: 'SET_SUCCESS', payload: data })
  }, [])

  const setError = useCallback((error: string) => {
    dispatch({ type: 'SET_ERROR', error })
  }, [])

  const reset = useCallback(() => {
    dispatch({ type: 'RESET' })
  }, [])

  const execute = useCallback(async (asyncFn: () => Promise<T>) => {
    try {
      setLoading()
      const result = await asyncFn()
      setSuccess(result)
    } catch (error) {
      setError(error instanceof Error ? error.message : String(error))
    }
  }, [setLoading, setSuccess, setError])

  return [state, { setLoading, setSuccess, setError, reset, execute }]
}

/**
 * ARCHITECTURE FIX: Standardized form state management
 */
export interface FormState<T> {
  values: T
  errors: Partial<Record<keyof T, string>>
  touched: Partial<Record<keyof T, boolean>>
  isValid: boolean
  isDirty: boolean
  isSubmitting: boolean
  submitCount: number
}

export interface FormAction<T> {
  type: 'SET_VALUE' | 'SET_ERROR' | 'SET_TOUCHED' | 'SET_SUBMITTING' | 'RESET' | 'SUBMIT_ATTEMPT'
  field?: keyof T
  value?: any
  error?: string
}

function formStateReducer<T extends Record<string, any>>(
  state: FormState<T>,
  action: FormAction<T>
): FormState<T> {
  switch (action.type) {
    case 'SET_VALUE':
      if (!action.field) return state
      
      const newValues = { ...state.values, [action.field]: action.value }
      return {
        ...state,
        values: newValues,
        isDirty: true,
        errors: { ...state.errors, [action.field]: undefined } // Clear error when value changes
      }
    
    case 'SET_ERROR':
      if (!action.field) return state
      
      return {
        ...state,
        errors: { ...state.errors, [action.field]: action.error }
      }
    
    case 'SET_TOUCHED':
      if (!action.field) return state
      
      return {
        ...state,
        touched: { ...state.touched, [action.field]: true }
      }
    
    case 'SET_SUBMITTING':
      return {
        ...state,
        isSubmitting: action.value || false
      }
    
    case 'SUBMIT_ATTEMPT':
      return {
        ...state,
        submitCount: state.submitCount + 1
      }
    
    case 'RESET':
      return createInitialFormState(action.value as T)
    
    default:
      return state
  }
}

function createInitialFormState<T extends Record<string, any>>(initialValues: T): FormState<T> {
  return {
    values: initialValues,
    errors: {},
    touched: {},
    isValid: true,
    isDirty: false,
    isSubmitting: false,
    submitCount: 0
  }
}

/**
 * ARCHITECTURE FIX: Standardized form state hook
 */
export function useFormState<T extends Record<string, any>>(initialValues: T): [
  FormState<T>,
  {
    setValue: (field: keyof T, value: any) => void
    setError: (field: keyof T, error: string) => void
    setTouched: (field: keyof T) => void
    setSubmitting: (isSubmitting: boolean) => void
    submitAttempt: () => void
    reset: (newValues?: T) => void
    validateField: (field: keyof T, validator: (value: any) => string | undefined) => void
  }
] {
  const [state, dispatch] = useReducer(
    formStateReducer<T>,
    createInitialFormState(initialValues)
  )

  const setValue = useCallback((field: keyof T, value: any) => {
    dispatch({ type: 'SET_VALUE', field, value })
  }, [])

  const setError = useCallback((field: keyof T, error: string) => {
    dispatch({ type: 'SET_ERROR', field, error })
  }, [])

  const setTouched = useCallback((field: keyof T) => {
    dispatch({ type: 'SET_TOUCHED', field })
  }, [])

  const setSubmitting = useCallback((isSubmitting: boolean) => {
    dispatch({ type: 'SET_SUBMITTING', value: isSubmitting })
  }, [])

  const submitAttempt = useCallback(() => {
    dispatch({ type: 'SUBMIT_ATTEMPT' })
  }, [])

  const reset = useCallback((newValues?: T) => {
    dispatch({ type: 'RESET', value: newValues || initialValues })
  }, [initialValues])

  const validateField = useCallback((field: keyof T, validator: (value: any) => string | undefined) => {
    const error = validator(state.values[field])
    if (error) {
      setError(field, error)
    }
  }, [state.values, setError])

  return [state, { setValue, setError, setTouched, setSubmitting, submitAttempt, reset, validateField }]
}

/**
 * ARCHITECTURE FIX: Standardized cache management
 */
export interface CacheEntry<T> {
  data: T
  timestamp: number
  ttl: number
}

export class StandardizedCache<T> {
  private cache = new Map<string, CacheEntry<T>>()
  private defaultTTL = 5 * 60 * 1000 // 5 minutes

  set(key: string, data: T, ttl?: number): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: ttl || this.defaultTTL
    })
  }

  get(key: string): T | null {
    const entry = this.cache.get(key)
    if (!entry) return null

    // Check if expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key)
      return null
    }

    return entry.data
  }

  has(key: string): boolean {
    return this.get(key) !== null
  }

  delete(key: string): void {
    this.cache.delete(key)
  }

  clear(): void {
    this.cache.clear()
  }

  cleanup(): void {
    const now = Date.now()
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key)
      }
    }
  }
}

/**
 * ARCHITECTURE FIX: Global cache instance
 */
export const globalCache = new StandardizedCache<any>()
