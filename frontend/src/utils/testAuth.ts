// Test utility for authentication system
export const testUsers = {
  '<EMAIL>': {
    email: '<EMAIL>',
    password: 'password123',
    role: 'super_admin',
    name: '<PERSON>'
  },
  '<EMAIL>': {
    email: '<EMAIL>',
    password: 'password123',
    role: 'hr_manager',
    name: '<PERSON><PERSON>'
  },
  '<EMAIL>': {
    email: '<EMAIL>',
    password: 'password123',
    role: 'finance_manager',
    name: '<PERSON>'
  },
  '<EMAIL>': {
    email: '<EMAIL>',
    password: 'password123',
    role: 'sales_manager',
    name: '<PERSON>'
  },
  '<EMAIL>': {
    email: '<EMAIL>',
    password: 'password123',
    role: 'department_manager',
    name: '<PERSON>'
  },
  '<EMAIL>': {
    email: '<EMAIL>',
    password: 'password123',
    role: 'employee',
    name: '<PERSON>'
  }
}

export const validateTestUser = (email: string, password: string): boolean => {
  const user = testUsers[email as keyof typeof testUsers]
  return user && user.password === password
}

export const getTestUserInfo = (email: string) => {
  return testUsers[email as keyof typeof testUsers] || null
}

// Debug function to test authentication
export const debugAuth = () => {
  console.log('=== Authentication Debug Info ===')
  console.log('Available test users:')
  Object.values(testUsers).forEach(user => {
    console.log(`- ${user.email} (${user.role}): ${user.name}`)
  })
  console.log('Password for all accounts: password123')
  console.log('Current token:', localStorage.getItem('token'))
  console.log('================================')
}

// Clear authentication data
export const clearAuth = () => {
  localStorage.removeItem('token')
  sessionStorage.clear()
  console.log('Authentication data cleared. Please refresh the page.')
}

// Force logout and clear everything
export const forceLogout = () => {
  localStorage.clear()
  sessionStorage.clear()
  window.location.reload()
}
