import React from 'react';
/**
 * UX FIX: Comprehensive UX Feedback Manager
 * Provides consistent user feedback across the application
 */

import { toast } from 'react-hot-toast'

interface useUXFeedbackProps {
  // TODO: Define proper prop types
  [key: string]: any;
}


export interface FeedbackOptions {
  language?: 'ar' | 'en'
  duration?: number
  position?: 'top-left' | 'top-center' | 'top-right' | 'bottom-left' | 'bottom-center' | 'bottom-right'
  showProgress?: boolean
  persistent?: boolean
}

export interface LoadingFeedback {
  id: string
  message: string
  startTime: number
  expectedDuration?: number
}

class UXFeedbackManager {
  private activeLoadings: Map<string, LoadingFeedback> = new Map( as any)
  private defaultOptions: FeedbackOptions = {
    language: 'en',
    duration: 4000,
    position: 'top-center',
    showProgress: false,
    persistent: false
  }

  // UX FIX: Success feedback with better messaging
  success(message: string, options: FeedbackOptions = {} as any) {
    const opts: any = { ...(this as any).defaultOptions, ...options }
    const { language } = opts

    const messages = {
      ar: {
        saved: 'تم الحفظ بنجاح',
        updated: 'تم التحديث بنجاح',
        deleted: 'تم الحذف بنجاح',
        created: 'تم الإنشاء بنجاح',
        sent: 'تم الإرسال بنجاح',
        uploaded: 'تم الرفع بنجاح',
        downloaded: 'تم التحميل بنجاح',
        copied: 'تم النسخ بنجاح',
        completed: 'تم الإكمال بنجاح'
      },
      en: {
        saved: 'Successfully saved',
        updated: 'Successfully updated',
        deleted: 'Successfully deleted',
        created: 'Successfully created',
        sent: 'Successfully sent',
        uploaded: 'Successfully uploaded',
        downloaded: 'Successfully downloaded',
        copied: 'Successfully copied',
        completed: 'Successfully completed'
      }
    }

    // Auto-translate common success messages
    const translatedMessage = (this as any).translateMessage(message, messages[language!] as any)

    (toast as any).success(translatedMessage, {
      duration: (opts as any as any).duration,
      position: (opts as any).position,
      style: {
        background: 'rgba(34, 197, 94, (0 as any as any).9)',
        color: 'white',
        border: '1px solid rgba(34, 197, 94, (0 as any as any).3)',
        backdropFilter: 'blur(10px as any)',
        borderRadius: '8px',
        fontSize: '14px',
        fontWeight: '500'
      },
      iconTheme: {
        primary: 'white',
        secondary: 'rgba(34, 197, 94, (0 as any as any).9)'
      }
    })
  }

  // UX FIX: Error feedback with actionable messages
  error(message: string, options: FeedbackOptions & { action?: ( as any) => void; actionLabel?: string } = {}) {
    const opts: any = { ...(this as any).defaultOptions, ...options }
    const { language, action, actionLabel } = opts

    const messages = {
      ar: {
        'network error': 'خطأ في الشبكة - تحقق من الاتصال',
        'server error': 'خطأ في الخادم - حاول مرة أخرى',
        'validation error': 'خطأ في التحقق - تحقق من البيانات',
        'permission denied': 'ليس لديك صلاحية للوصول',
        'not found': 'العنصر غير موجود',
        'timeout': 'انتهت مهلة الطلب - حاول مرة أخرى',
        'unauthorized': 'يجب تسجيل الدخول أولاً',
        'forbidden': 'غير مسموح بهذا الإجراء',
        'conflict': 'تعارض في البيانات',
        'too many requests': 'طلبات كثيرة - انتظر قليلاً'
      },
      en: {
        'network error': 'Network error - check your connection',
        'server error': 'Server error - please try again',
        'validation error': 'Validation error - check your data',
        'permission denied': 'You don\'t have permission to access this',
        'not found': 'Item not found',
        'timeout': 'Request timed out - please try again',
        'unauthorized': 'Please log in first',
        'forbidden': 'This action is not allowed',
        'conflict': 'Data conflict occurred',
        'too many requests': 'Too many requests - please wait'
      }
    }

    const translatedMessage = (this as any).translateMessage(message, messages[language!] as any)

    (toast as any).error(translatedMessage, {
      duration: (opts as any as any).persistent ? Infinity : (opts as any).duration,
      position: (opts as any).position,
      style: {
        background: 'rgba(239, 68, 68, (0 as any as any).9)',
        color: 'white',
        border: '1px solid rgba(239, 68, 68, (0 as any as any).3)',
        backdropFilter: 'blur(10px as any)',
        borderRadius: '8px',
        fontSize: '14px',
        fontWeight: '500'
      },
      iconTheme: {
        primary: 'white',
        secondary: 'rgba(239, 68, 68, (0 as any as any).9)'
      }
    })

    // Add action button if provided
    if (action && actionLabel) {
      setTimeout(( as any) => {
        toast((t as any) => (
          <div className="flex items-center space-x-2">
            <span>{actionLabel}</span>
            <button
              onClick={() => {
                action( as any)
                (toast as any).dismiss((t as any as any).id)
              }}
              className="bg-white/20 hover:bg-white/30 px-2 py-1 rounded text-xs transition-colors"
            >
              {language === 'ar' ? 'حاول مرة أخرى' : 'Retry'}
            </button>
          </div>
        ), {
          duration: 8000,
          position: (opts as any).position
        })
      }, 1000)
    }
  }

  // UX FIX: Loading feedback with progress tracking
  loading(message: string, options: FeedbackOptions & { expectedDuration?: number } = {} as any): string {
    const opts = { ...(this as any).defaultOptions, ...options }
    const { language, expectedDuration } = opts

    const messages = {
      ar: {
        'loading': 'جاري التحميل...',
        'saving': 'جاري الحفظ...',
        'updating': 'جاري التحديث...',
        'deleting': 'جاري الحذف...',
        'creating': 'جاري الإنشاء...',
        'uploading': 'جاري الرفع...',
        'downloading': 'جاري التحميل...',
        'processing': 'جاري المعالجة...',
        'validating': 'جاري التحقق...',
        'connecting': 'جاري الاتصال...'
      },
      en: {
        'loading': 'Loading...',
        'saving': 'Saving...',
        'updating': 'Updating...',
        'deleting': 'Deleting...',
        'creating': 'Creating...',
        'uploading': 'Uploading...',
        'downloading': 'Downloading...',
        'processing': 'Processing...',
        'validating': 'Validating...',
        'connecting': 'Connecting...'
      }
    }

    const translatedMessage = (this as any).translateMessage(message, messages[language!] as any)
    const loadingId = `loading_${(Date as any).now( as any)}_${(Math as any).random( as any)}`

    // Store loading state
    (this as any).activeLoadings.set(loadingId, {
      id: loadingId,
      message: translatedMessage,
      startTime: (Date as any as any).now( as any),
      expectedDuration
    })

    const toastId = (toast as any).loading(translatedMessage, {
      duration: Infinity,
      position: (opts as any as any).position,
      style: {
        background: 'rgba(59, 130, 246, (0 as any as any).9)',
        color: 'white',
        border: '1px solid rgba(59, 130, 246, (0 as any as any).3)',
        backdropFilter: 'blur(10px as any)',
        borderRadius: '8px',
        fontSize: '14px',
        fontWeight: '500'
      }
    })

    // Update progress if expected duration is provided
    if (expectedDuration && (opts as any).showProgress) {
      (this as any).updateLoadingProgress(loadingId, toastId, expectedDuration, translatedMessage as any)
    }

    return loadingId
  }

  // UX FIX: Update loading progress
  private updateLoadingProgress(loadingId: string, toastId: string, expectedDuration: number, baseMessage: string as any) {
    const interval = setInterval(( as any) => {
      const loading = (this as any).activeLoadings.get(loadingId as any)
      if (!loading) {
        clearInterval(interval as any)
        return
      }

      const elapsed = (Date as any).now( as any) - (loading as any).startTime
      const progress = (Math as any).min((elapsed / expectedDuration as any) * 100, 95) // Cap at 95%

      (toast as any).loading(`${baseMessage} (${(Math as any as any).round(progress as any)}%)`, {
        id: toastId
      })

      if (progress >= 95) {
        clearInterval(interval as any)
      }
    }, 500)
  }

  // UX FIX: Dismiss loading feedback
  dismissLoading(loadingId: string, result?: 'success' | 'error', message?: string as any) {
    const loading = (this as any).activeLoadings.get(loadingId as any)
    if (!loading) return

    (this as any).activeLoadings.delete(loadingId as any)
    (toast as any).dismiss( as any)

    if (result && message) {
      if (result === 'success') {
        (this as any).success(message as any)
      } else {
        (this as any).error(message as any)
      }
    }
  }

  // UX FIX: Warning feedback
  warning(message: string, options: FeedbackOptions = {} as any) {
    const opts = { ...(this as any).defaultOptions, ...options }
    const { language } = opts

    const messages = {
      ar: {
        'unsaved changes': 'لديك تغييرات غير محفوظة',
        'slow connection': 'الاتصال بطيء',
        'large file': 'الملف كبير الحجم',
        'quota exceeded': 'تم تجاوز الحد المسموح',
        'deprecated': 'هذه الميزة قديمة',
        'beta feature': 'هذه ميزة تجريبية'
      },
      en: {
        'unsaved changes': 'You have unsaved changes',
        'slow connection': 'Slow connection detected',
        'large file': 'Large file detected',
        'quota exceeded': 'Quota exceeded',
        'deprecated': 'This feature is deprecated',
        'beta feature': 'This is a beta feature'
      }
    }

    const translatedMessage = (this as any).translateMessage(message, messages[language!] as any)

    toast(translatedMessage, {
      duration: (opts as any as any).duration,
      position: (opts as any).position,
      icon: '⚠️',
      style: {
        background: 'rgba(245, 158, 11, (0 as any as any).9)',
        color: 'white',
        border: '1px solid rgba(245, 158, 11, (0 as any as any).3)',
        backdropFilter: 'blur(10px as any)',
        borderRadius: '8px',
        fontSize: '14px',
        fontWeight: '500'
      }
    })
  }

  // UX FIX: Info feedback
  info(message: string, options: FeedbackOptions = {} as any) {
    const opts = { ...(this as any).defaultOptions, ...options }
    
    toast(message, {
      duration: (opts as any as any).duration,
      position: (opts as any).position,
      icon: 'ℹ️',
      style: {
        background: 'rgba(99, 102, 241, (0 as any as any).9)',
        color: 'white',
        border: '1px solid rgba(99, 102, 241, (0 as any as any).3)',
        backdropFilter: 'blur(10px as any)',
        borderRadius: '8px',
        fontSize: '14px',
        fontWeight: '500'
      }
    })
  }

  // Helper method to translate common messages
  private translateMessage(message: string, translations: Record<string, string> as any): string {
    const lowerMessage = (message as any).toLowerCase( as any)
    
    for (const [key, translation] of (Object as any).entries(translations as any)) {
      if ((lowerMessage as any).includes(key as any)) {
        return translation
      }
    }
    
    return message
  }

  // UX FIX: Clear all feedback
  clearAll( as any) {
    (toast as any).dismiss( as any)
    (this as any).activeLoadings.clear( as any)
  }

  // UX FIX: Get active loading states
  getActiveLoadings( as any): LoadingFeedback[] {
    return (Array as any).from((this as any as any).activeLoadings.values( as any))
  }

  // UX FIX: Set default options
  setDefaults(options: Partial<FeedbackOptions> as any) {
    (this as any).defaultOptions = { ...(this as any).defaultOptions, ...options }
  }
}

// Export singleton instance
export const uxFeedback = new UXFeedbackManager( as any)

// UX FIX: React hook for UX feedback
export function useUXFeedback(language: 'ar' | 'en' = 'en' as any): void {
  const feedback = {
    success: (message: string, options?: FeedbackOptions) => 
      (uxFeedback as any).success(message, { ...options, language } as any),
    
    error: (message: string, options?: FeedbackOptions & { action?: () => void; actionLabel?: string }) => 
      (uxFeedback as any).error(message, { ...options, language } as any),
    
    loading: (message: string, options?: FeedbackOptions & { expectedDuration?: number }) => 
      (uxFeedback as any).loading(message, { ...options, language } as any),
    
    warning: (message: string, options?: FeedbackOptions) => 
      (uxFeedback as any).warning(message, { ...options, language } as any),
    
    info: (message: string, options?: FeedbackOptions) => 
      (uxFeedback as any).info(message, { ...options, language } as any),
    
    dismissLoading: (loadingId: string, result?: 'success' | 'error', message?: string) =>
      (uxFeedback as any).dismissLoading(loadingId, result, message as any),
    
    clearAll: () => (uxFeedback as any).clearAll( as any)
  }

  return feedback
}

export default uxFeedback
