<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Migration Verification</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border-left: 4px solid #4CAF50;
        }
        
        .test-section.warning {
            border-left-color: #FF9800;
        }
        
        .test-section.error {
            border-left-color: #F44336;
        }
        
        button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 8px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }
        
        button:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 8px;
            font-weight: bold;
        }
        
        .status.success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4CAF50;
        }
        
        .status.error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid #F44336;
        }
        
        .status.info {
            background: rgba(33, 150, 243, 0.2);
            border: 1px solid #2196F3;
        }
        
        .status.warning {
            background: rgba(255, 152, 0, 0.2);
            border: 1px solid #FF9800;
        }
        
        #logs {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 20px;
        }
        
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-radius: 4px;
        }
        
        .log-success { color: #4CAF50; }
        .log-error { color: #F44336; }
        .log-warning { color: #FF9800; }
        .log-info { color: #2196F3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Authentication Migration Verification</h1>
        
        <div class="test-section">
            <h3>🧪 Migration Tests</h3>
            <button onclick="testTokenSecurity()">Test Token Security</button>
            <button onclick="testAuthFlow()">Test Auth Flow</button>
            <button onclick="testAPIAccess()">Test API Access</button>
            <button onclick="runAllTests()">Run All Tests</button>
        </div>
        
        <div class="test-section">
            <h3>🔧 Manual Testing</h3>
            <button onclick="loginTest()">Manual Login Test</button>
            <button onclick="logoutTest()">Manual Logout Test</button>
            <button onclick="clearAllData()">Clear All Data</button>
        </div>
        
        <div id="logs"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8000/api';
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            const logsContainer = document.getElementById('logs');
            logsContainer.appendChild(logEntry);
            logsContainer.scrollTop = logsContainer.scrollHeight;
        }
        
        async function testTokenSecurity() {
            log('🔍 Testing Token Security...', 'info');
            
            // Check localStorage for auth tokens
            const localTokens = Object.keys(localStorage).filter(key => 
                key.includes('access_token') || key.includes('refresh_token') || key.includes('jwt')
            );
            
            if (localTokens.length === 0) {
                log('✅ No authentication tokens found in localStorage', 'success');
            } else {
                log(`❌ Found ${localTokens.length} auth tokens in localStorage: ${localTokens.join(', ')}`, 'error');
            }
            
            // Check sessionStorage
            const sessionTokens = Object.keys(sessionStorage).filter(key => 
                key.includes('access_token') || key.includes('refresh_token') || key.includes('jwt')
            );
            
            if (sessionTokens.length === 0) {
                log('✅ No authentication tokens found in sessionStorage', 'success');
            } else {
                log(`❌ Found ${sessionTokens.length} auth tokens in sessionStorage: ${sessionTokens.join(', ')}`, 'error');
            }
            
            // Check if access tokens are accessible via cookies (they shouldn't be for httpOnly)
            const cookieMatch = document.cookie.match(/access_token=([^;]+)/);
            if (cookieMatch) {
                log('⚠️ Access token found in accessible cookies (should be httpOnly)', 'warning');
            } else {
                log('✅ Access tokens not accessible via JavaScript (secure httpOnly)', 'success');
            }
        }
        
        async function testAuthFlow() {
            log('🔐 Testing Authentication Flow...', 'info');
            
            try {
                // Test auth verification endpoint
                const response = await fetch(`${API_BASE_URL}/auth/verify/`, {
                    credentials: 'include'
                });
                
                if (response.ok) {
                    const user = await response.json();
                    log(`✅ Authentication verified via httpOnly cookies: ${user.username}`, 'success');
                    log(`👤 User Role: ${user.role.name}`, 'info');
                } else if (response.status === 401) {
                    log('ℹ️ Not authenticated (401) - this is expected if not logged in', 'info');
                } else {
                    log(`❌ Auth verification failed: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ Auth flow test error: ${error.message}`, 'error');
            }
        }
        
        async function testAPIAccess() {
            log('🌐 Testing API Access...', 'info');
            
            try {
                // Test a protected API endpoint
                const response = await fetch(`${API_BASE_URL}/auth/user/`, {
                    credentials: 'include'
                });
                
                if (response.ok) {
                    const user = await response.json();
                    log('✅ Protected API access successful via httpOnly cookies', 'success');
                    log(`📊 User data retrieved: ${user.username} (${user.email})`, 'info');
                } else if (response.status === 401) {
                    log('ℹ️ Protected API returned 401 - authentication required', 'info');
                } else {
                    log(`❌ API access failed: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ API access test error: ${error.message}`, 'error');
            }
        }
        
        async function loginTest() {
            log('🔑 Starting Manual Login Test...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/auth/login/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include',
                    body: JSON.stringify({
                        username: 'test_api_user',
                        password: 'password123'
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ Login successful: ${data.user.username}`, 'success');
                    log('🍪 httpOnly cookies set by backend', 'success');
                    
                    // Test immediate API access
                    setTimeout(testAPIAccess, 1000);
                } else {
                    const error = await response.text();
                    log(`❌ Login failed: ${error}`, 'error');
                }
            } catch (error) {
                log(`❌ Login test error: ${error.message}`, 'error');
            }
        }
        
        async function logoutTest() {
            log('🚪 Starting Manual Logout Test...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/auth/logout/`, {
                    method: 'POST',
                    credentials: 'include'
                });
                
                if (response.ok) {
                    log('✅ Logout successful', 'success');
                    log('🧹 httpOnly cookies cleared by backend', 'success');
                    
                    // Test that API access is now denied
                    setTimeout(testAPIAccess, 1000);
                } else {
                    log(`❌ Logout failed: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ Logout test error: ${error.message}`, 'error');
            }
        }
        
        async function runAllTests() {
            log('🧪 Running All Migration Tests...', 'info');
            log('=' * 50, 'info');
            
            await testTokenSecurity();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testAuthFlow();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testAPIAccess();
            
            log('🎯 All tests completed!', 'success');
        }
        
        function clearAllData() {
            localStorage.clear();
            sessionStorage.clear();
            log('🗑️ All browser storage cleared', 'info');
        }
        
        // Initialize
        window.addEventListener('load', () => {
            log('🚀 Authentication Migration Verification Tool Loaded', 'success');
            log('📋 Ready to test httpOnly cookie authentication!', 'info');
            
            // Run initial security check
            setTimeout(testTokenSecurity, 1000);
        });
    </script>
</body>
</html>
