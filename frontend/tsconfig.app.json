{
  "compilerOptions": {
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "verbatimModuleSyntax": false,
    "moduleDetection": "force",
    "noEmit": true,
    "jsx": "react-jsx",
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "skipLibCheck": true,

    /* PHASE 1: Gradual Type Checking - Enable Basic Checks */
    "strict": false,                          // Keep disabled for now
    "noImplicitAny": true,                   // ✅ ENABLED: Catch implicit any types
    "strictNullChecks": false,               // Keep disabled for now
    "strictFunctionTypes": false,            // Keep disabled for now
    "strictBindCallApply": false,            // Keep disabled for now
    "strictPropertyInitialization": false,   // Keep disabled for now
    "useUnknownInCatchVariables": false,     // Keep disabled for now
    "noImplicitReturns": true,               // ✅ ENABLED: Require explicit returns
    "noFallthroughCasesInSwitch": true,      // ✅ ENABLED: Prevent switch fallthrough
    "noUncheckedSideEffectImports": false,   // Keep disabled for now
    "exactOptionalPropertyTypes": false,     // Keep disabled for now
    "noPropertyAccessFromIndexSignature": false, // Keep disabled for now
    "noUncheckedIndexedAccess": false,       // Keep disabled for now
    "noUnusedLocals": true,                  // ✅ ENABLED: Catch unused variables
    "noUnusedParameters": true,              // ✅ ENABLED: Catch unused parameters
    "skipLibCheck": true,
    "noErrorTruncation": true,
    "ignoreDeprecations": "5.0",

    /* Path mapping for better imports */
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@/components/*": ["./src/components/*"],
      "@/hooks/*": ["./src/hooks/*"],
      "@/utils/*": ["./src/utils/*"],
      "@/types/*": ["./src/types/*"],
      "@/api/*": ["./src/api/*"],
      "@/pages/*": ["./src/pages/*"],
      "@/lib/*": ["./src/lib/*"],
      "@/store/*": ["./src/store/*"]
    }
  },
  "include": ["src", "src/types/global.d.ts"],
  "exclude": ["src/**/*.test.tsx", "src/**/*.test.ts", "src/tests/**/*", "src/__tests__/**/*"]
}
