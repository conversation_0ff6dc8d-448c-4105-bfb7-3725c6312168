#!/usr/bin/env python3
"""
Models Analysis Script for EMS Refactoring
Analyzes the monolithic models.py file to understand structure and plan domain separation
"""

import re
import os
import sys
from pathlib import Path

def analyze_models_file(file_path):
    """Analyze the models.py file and extract model information"""
    
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return None
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Extract model classes
    model_pattern = r'class\s+(\w+)\(.*?models\.Model.*?\):'
    models = re.findall(model_pattern, content, re.MULTILINE)
    
    # Extract imports
    import_pattern = r'^(from .+ import .+|import .+)$'
    imports = re.findall(import_pattern, content, re.MULTILINE)
    
    # Count lines
    total_lines = len(content.split('\n'))
    
    # Analyze model relationships
    foreign_key_pattern = r'(\w+)\s*=\s*models\.ForeignKey\([\'"]?(\w+)[\'"]?'
    foreign_keys = re.findall(foreign_key_pattern, content)
    
    # Analyze model methods
    method_pattern = r'def\s+(\w+)\(self'
    methods = re.findall(method_pattern, content)
    
    return {
        'total_lines': total_lines,
        'models': models,
        'imports': imports,
        'foreign_keys': foreign_keys,
        'methods': methods,
        'content': content
    }

def categorize_models_by_domain(models):
    """Categorize models into business domains"""
    
    domains = {
        'core': [],
        'hr': [],
        'finance': [],
        'projects': [],
        'assets': [],
        'crm': [],
        'communication': [],
        'kpi': [],
        'integrations': [],
        'security': [],
        'enterprise': [],
        'uncategorized': []
    }
    
    # Domain classification rules
    domain_rules = {
        'core': ['User', 'Role', 'UserProfile', 'Department', 'Employee', 'Activity'],
        'hr': ['LeaveType', 'LeaveRequest', 'Attendance', 'EmployeeActivation', 
               'PerformanceReview', 'PayrollPeriod', 'PayrollEntry', 'TrainingProgram'],
        'finance': ['Currency', 'ExchangeRate', 'AccountType', 'ChartOfAccounts', 
                   'FiscalYear', 'JournalEntryBatch', 'JournalEntry', 'Budget', 
                   'Expense', 'Vendor', 'VendorInvoice', 'CustomerInvoice', 'Payment'],
        'projects': ['Project', 'Task', 'Workflow'],
        'assets': ['AssetCategory', 'Asset', 'AssetDepreciation', 'AssetMaintenance', 
                  'AssetTransfer', 'AssetAudit', 'Supplier', 'PurchaseOrder'],
        'crm': ['Customer', 'ProductCategory', 'Product', 'SalesOrder', 'Report'],
        'communication': ['Announcement', 'Message', 'Document', 'Meeting'],
        'kpi': ['KPICategory', 'KPI', 'KPIValue', 'KPITarget', 'KPIAlert',
               'KPIMetric', 'KPIMetricValue', 'ReportTemplate', 'ReportExecution', 
               'Dashboard', 'AnalyticsQuery'],
        'integrations': ['APIKey', 'ExternalService', 'WebhookEndpoint', 'WebhookEvent', 
                        'IntegrationLog'],
        'security': ['UserSecurityProfile', 'AuditTrail', 'SecurityIncident', 
                    'ComplianceFramework', 'ComplianceControl', 'DataClassification', 
                    'SecurityAlert'],
        'enterprise': ['Tenant', 'TenantUser', 'MLModel', 'MLPrediction']
    }
    
    # Categorize each model
    for model in models:
        categorized = False
        for domain, domain_models in domain_rules.items():
            if model in domain_models:
                domains[domain].append(model)
                categorized = True
                break
        
        if not categorized:
            domains['uncategorized'].append(model)
    
    return domains

def analyze_model_dependencies(content, models):
    """Analyze dependencies between models"""
    dependencies = {}
    
    for model in models:
        # Find foreign key relationships for this model
        model_section = extract_model_section(content, model)
        if model_section:
            fk_pattern = r'models\.ForeignKey\([\'"]?(\w+)[\'"]?'
            fks = re.findall(fk_pattern, model_section)
            dependencies[model] = fks
    
    return dependencies

def extract_model_section(content, model_name):
    """Extract the section of content for a specific model"""
    pattern = rf'class\s+{model_name}\(.*?\):(.*?)(?=class\s+\w+\(.*?\):|$)'
    match = re.search(pattern, content, re.DOTALL)
    return match.group(1) if match else None

def generate_migration_plan(domains, dependencies):
    """Generate a migration plan based on dependencies"""
    
    # Order domains by dependency complexity (least dependent first)
    domain_order = [
        'core',        # Foundation - User, Role, Department, Employee
        'finance',     # Currency, Budget - depends on core
        'hr',          # Leave, Attendance - depends on core
        'assets',      # Asset management - depends on core
        'projects',    # Project management - depends on core
        'crm',         # Customer management - depends on core
        'communication', # Messages, Documents - depends on core
        'kpi',         # Analytics - depends on multiple domains
        'security',    # Security features - depends on core
        'integrations', # External integrations - depends on multiple
        'enterprise',  # Multi-tenant features - depends on all
    ]
    
    return domain_order

def main():
    """Main analysis function"""
    print("🔍 EMS Models Analysis")
    print("=" * 50)
    
    # Analyze the models file
    models_path = "backend/ems/models.py"
    analysis = analyze_models_file(models_path)
    
    if not analysis:
        return
    
    print(f"📊 File Statistics:")
    print(f"  - Total lines: {analysis['total_lines']:,}")
    print(f"  - Total models: {len(analysis['models'])}")
    print(f"  - Total imports: {len(analysis['imports'])}")
    print(f"  - Total methods: {len(analysis['methods'])}")
    
    # Categorize models
    domains = categorize_models_by_domain(analysis['models'])
    
    print(f"\n📋 Models by Domain:")
    for domain, models in domains.items():
        if models:  # Only show domains with models
            print(f"  🏷️  {domain.upper()}: {len(models)} models")
            for model in models:
                print(f"    - {model}")
    
    # Analyze dependencies
    dependencies = analyze_model_dependencies(analysis['content'], analysis['models'])
    
    print(f"\n🔗 Model Dependencies (Top 10 most connected):")
    sorted_deps = sorted(dependencies.items(), key=lambda x: len(x[1]), reverse=True)
    for model, deps in sorted_deps[:10]:
        if deps:
            print(f"  - {model}: depends on {', '.join(deps)}")
    
    # Generate migration plan
    domain_order = generate_migration_plan(domains, dependencies)
    
    print(f"\n🚀 Recommended Migration Order:")
    for i, domain in enumerate(domain_order, 1):
        if domains[domain]:  # Only show domains with models
            print(f"  {i}. {domain.upper()}: {len(domains[domain])} models")
    
    # Check for uncategorized models
    if domains['uncategorized']:
        print(f"\n⚠️  Uncategorized Models ({len(domains['uncategorized'])}):")
        for model in domains['uncategorized']:
            print(f"  - {model}")
    
    # Generate summary report
    print(f"\n📈 Refactoring Impact:")
    print(f"  - Largest domain: {max(domains.keys(), key=lambda k: len(domains[k]))} ({len(domains[max(domains.keys(), key=lambda k: len(domains[k]))])} models)")
    print(f"  - Average models per domain: {sum(len(models) for models in domains.values()) / len([d for d in domains.values() if d]):.1f}")
    print(f"  - Estimated file size reduction: {(analysis['total_lines'] / len([d for d in domains.values() if d])):.0f} lines per domain file")
    
    print(f"\n✅ Analysis Complete!")
    print(f"📄 Next step: Create domain directory structure")

if __name__ == "__main__":
    main()
