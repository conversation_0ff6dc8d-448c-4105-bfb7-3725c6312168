#!/usr/bin/env python3
"""
Complete Models Extraction Script
Extracts all remaining models from the monolithic models.py file
"""

import re
import os
from pathlib import Path

def extract_model_content(content, model_name):
    """Extract complete model content including class definition and methods"""
    
    # Find the start of the model
    pattern = rf'class\s+{model_name}\s*\([^)]*\):'
    match = re.search(pattern, content)
    
    if not match:
        return None
    
    start_pos = match.start()
    lines = content[:start_pos].count('\n')
    
    # Find the end of the model (next class or end of file)
    remaining_content = content[start_pos:]
    
    # Look for next class definition
    next_class_pattern = r'\nclass\s+\w+\s*\([^)]*\):'
    next_match = re.search(next_class_pattern, remaining_content)
    
    if next_match:
        end_pos = start_pos + next_match.start()
        model_content = content[start_pos:end_pos]
    else:
        model_content = remaining_content
    
    return model_content.strip()

def create_finance_models_completion():
    """Complete the finance models file"""
    
    # Read the original models file
    with open('backend/ems/models.py', 'r') as f:
        content = f.read()
    
    # Models to extract for finance
    finance_models = ['Vendor', 'VendorInvoice', 'CustomerInvoice', 'Payment']
    
    finance_additions = []
    
    for model_name in finance_models:
        model_content = extract_model_content(content, model_name)
        if model_content:
            # Clean up the model content and add proper imports
            cleaned_content = clean_model_content(model_content, 'finance')
            finance_additions.append(cleaned_content)
    
    return '\n\n'.join(finance_additions)

def clean_model_content(content, domain):
    """Clean model content and fix imports for the domain"""
    
    # Replace Employee references with 'core.Employee'
    content = re.sub(r'models\.ForeignKey\(Employee,', "models.ForeignKey('core.Employee',", content)
    content = re.sub(r'models\.OneToOneField\(Employee,', "models.OneToOneField('core.Employee',", content)
    
    # Replace Department references
    content = re.sub(r'models\.ForeignKey\(Department,', "models.ForeignKey('core.Department',", content)
    
    # Replace Project references
    content = re.sub(r'models\.ForeignKey\(Project,', "models.ForeignKey('projects.Project',", content)
    
    # Replace Customer references
    content = re.sub(r'models\.ForeignKey\(Customer,', "models.ForeignKey('crm.Customer',", content)
    
    # Replace other domain references based on context
    if domain == 'finance':
        content = re.sub(r'models\.ForeignKey\(Currency,', "models.ForeignKey(Currency,", content)
        content = re.sub(r'models\.ForeignKey\(Budget,', "models.ForeignKey(Budget,", content)
    
    # Add proper base class inheritance
    content = re.sub(r'class (\w+)\(models\.Model\):', r'class \1(BaseModel):', content)
    
    # Replace DecimalField for money with MoneyField where appropriate
    money_fields = ['amount', 'total_amount', 'subtotal', 'tax_amount', 'credit_limit', 'balance']
    for field in money_fields:
        pattern = rf'{field}\s*=\s*models\.DecimalField\([^)]*max_digits=\d+,\s*decimal_places=2[^)]*\)'
        content = re.sub(pattern, f'{field} = MoneyField()', content)
    
    return content

def create_projects_models():
    """Create projects domain models"""
    
    with open('backend/ems/models.py', 'r') as f:
        content = f.read()
    
    projects_models = ['Project', 'Task', 'Workflow']
    
    projects_content = '''"""
Projects Domain Models for EMS
Contains project management related models:
- Project: Project management
- Task: Task tracking
- Workflow: Workflow management
"""

from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator
from .base import BaseModel, NamedModel, AuditableModel, MoneyField, PRIORITY_CHOICES, APPROVAL_STATUS_CHOICES

'''
    
    for model_name in projects_models:
        model_content = extract_model_content(content, model_name)
        if model_content:
            cleaned_content = clean_model_content(model_content, 'projects')
            projects_content += cleaned_content + '\n\n'
    
    return projects_content

def main():
    """Main extraction function"""
    print("🔄 Completing models extraction...")
    
    # Complete finance models
    print("📊 Completing finance models...")
    finance_additions = create_finance_models_completion()
    
    if finance_additions:
        # Append to existing finance.py
        with open('backend/ems/models/finance.py', 'a') as f:
            f.write('\n\n' + finance_additions)
        print("✅ Finance models completed")
    
    # Create projects models
    print("📋 Creating projects models...")
    projects_content = create_projects_models()
    
    with open('backend/ems/models/projects.py', 'w') as f:
        f.write(projects_content)
    print("✅ Projects models created")
    
    print("🎉 Models extraction completed!")

if __name__ == '__main__':
    main()
