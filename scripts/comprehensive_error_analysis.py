#!/usr/bin/env python3
"""
Comprehensive Error Analysis Script
Analyzes all TypeScript errors and categorizes them for systematic fixing
"""

import subprocess
import re
import json
from collections import defaultdict, Counter
from pathlib import Path

def get_detailed_typescript_errors():
    """Get detailed TypeScript errors with categorization"""
    try:
        result = subprocess.run(
            ['npx', 'tsc', '--noEmit', '--project', 'tsconfig.app.json'],
            cwd='frontend',
            capture_output=True,
            text=True,
            timeout=120
        )
        return result.stdout + result.stderr
    except Exception as e:
        return str(e)

def categorize_errors(error_output):
    """Categorize TypeScript errors by type and severity"""
    
    error_categories = {
        'type_mismatch': [],
        'missing_properties': [],
        'undefined_variables': [],
        'import_errors': [],
        'return_type_errors': [],
        'parameter_errors': [],
        'generic_errors': [],
        'null_undefined': [],
        'any_type_issues': [],
        'component_errors': [],
        'hook_errors': [],
        'other': []
    }
    
    error_patterns = {
        'type_mismatch': [
            r"Type '.*' is not assignable to type '.*'",
            r"Argument of type '.*' is not assignable to parameter of type '.*'",
            r"Type '.*' has no properties in common with type '.*'"
        ],
        'missing_properties': [
            r"Property '.*' does not exist on type '.*'",
            r"Property '.*' is missing in type '.*'",
            r"Object literal may only specify known properties"
        ],
        'undefined_variables': [
            r"Cannot find name '.*'",
            r"'.*' is not defined",
            r"Cannot find module '.*'"
        ],
        'import_errors': [
            r"Module '.*' has no exported member '.*'",
            r"Cannot resolve module '.*'",
            r"Could not find a declaration file for module '.*'"
        ],
        'return_type_errors': [
            r"Function lacks ending return statement",
            r"Not all code paths return a value",
            r"Type '.*' is not assignable to type '.*' return type"
        ],
        'parameter_errors': [
            r"Expected .* arguments, but got .*",
            r"Parameter '.*' implicitly has an '.*' type",
            r"Rest parameter '.*' implicitly has an 'any\[\]' type"
        ],
        'null_undefined': [
            r"Object is possibly 'null'",
            r"Object is possibly 'undefined'",
            r"Object is possibly 'null' or 'undefined'"
        ],
        'any_type_issues': [
            r"Parameter '.*' implicitly has an 'any' type",
            r"Variable '.*' implicitly has an 'any' type",
            r"'.*' implicitly has type 'any'"
        ],
        'component_errors': [
            r"JSX element type '.*' does not have any construct or call signatures",
            r"Property '.*' does not exist on type 'IntrinsicAttributes'",
            r"Type '.*' is not assignable to type 'ReactNode'"
        ],
        'hook_errors': [
            r"React Hook .* is called conditionally",
            r"React Hook .* has a missing dependency",
            r"React Hook .* has an unnecessary dependency"
        ]
    }
    
    lines = error_output.split('\n')
    current_error = None
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
            
        # Check if this is an error line
        if 'error TS' in line:
            # Extract file, line, column, and error message
            match = re.match(r'(.+?)\((\d+),(\d+)\): error TS(\d+): (.+)', line)
            if match:
                file_path, line_num, col_num, error_code, error_msg = match.groups()
                current_error = {
                    'file': file_path,
                    'line': int(line_num),
                    'column': int(col_num),
                    'code': error_code,
                    'message': error_msg,
                    'category': 'other'
                }
                
                # Categorize the error
                for category, patterns in error_patterns.items():
                    for pattern in patterns:
                        if re.search(pattern, error_msg, re.IGNORECASE):
                            current_error['category'] = category
                            break
                    if current_error['category'] != 'other':
                        break
                
                error_categories[current_error['category']].append(current_error)
    
    return error_categories

def analyze_error_patterns(error_categories):
    """Analyze error patterns to identify systematic issues"""
    
    analysis = {
        'total_errors': sum(len(errors) for errors in error_categories.values()),
        'category_counts': {cat: len(errors) for cat, errors in error_categories.items()},
        'file_error_counts': defaultdict(int),
        'most_problematic_files': [],
        'common_error_codes': Counter(),
        'fixable_automatically': 0,
        'requires_manual_fix': 0
    }
    
    # Count errors per file
    for category, errors in error_categories.items():
        for error in errors:
            analysis['file_error_counts'][error['file']] += 1
            analysis['common_error_codes'][error['code']] += 1
    
    # Find most problematic files
    analysis['most_problematic_files'] = sorted(
        analysis['file_error_counts'].items(),
        key=lambda x: x[1],
        reverse=True
    )[:20]
    
    # Estimate fixability
    auto_fixable_categories = ['return_type_errors', 'parameter_errors', 'any_type_issues', 'import_errors']
    manual_fix_categories = ['type_mismatch', 'missing_properties', 'component_errors', 'hook_errors']
    
    for category in auto_fixable_categories:
        analysis['fixable_automatically'] += len(error_categories[category])
    
    for category in manual_fix_categories:
        analysis['requires_manual_fix'] += len(error_categories[category])
    
    return analysis

def generate_fixing_strategy(error_categories, analysis):
    """Generate a systematic fixing strategy"""
    
    strategy = {
        'phases': [],
        'estimated_time': '0 days',
        'automation_potential': 0
    }
    
    # Phase 1: Automated fixes (high success rate)
    if error_categories['import_errors']:
        strategy['phases'].append({
            'phase': 1,
            'name': 'Import and Module Fixes',
            'errors': len(error_categories['import_errors']),
            'automation': 'High',
            'estimated_time': '2-4 hours',
            'description': 'Fix missing imports, module resolution, and export issues'
        })
    
    if error_categories['return_type_errors']:
        strategy['phases'].append({
            'phase': 2,
            'name': 'Return Type Annotations',
            'errors': len(error_categories['return_type_errors']),
            'automation': 'High',
            'estimated_time': '1-2 hours',
            'description': 'Add missing return type annotations to functions'
        })
    
    if error_categories['any_type_issues']:
        strategy['phases'].append({
            'phase': 3,
            'name': 'Implicit Any Type Fixes',
            'errors': len(error_categories['any_type_issues']),
            'automation': 'Medium',
            'estimated_time': '3-6 hours',
            'description': 'Replace implicit any types with proper type annotations'
        })
    
    # Phase 2: Semi-automated fixes
    if error_categories['parameter_errors']:
        strategy['phases'].append({
            'phase': 4,
            'name': 'Parameter Type Fixes',
            'errors': len(error_categories['parameter_errors']),
            'automation': 'Medium',
            'estimated_time': '4-8 hours',
            'description': 'Fix parameter type mismatches and missing types'
        })
    
    if error_categories['null_undefined']:
        strategy['phases'].append({
            'phase': 5,
            'name': 'Null/Undefined Safety',
            'errors': len(error_categories['null_undefined']),
            'automation': 'Medium',
            'estimated_time': '6-12 hours',
            'description': 'Add null checks and optional chaining'
        })
    
    # Phase 3: Manual fixes (require understanding)
    if error_categories['type_mismatch']:
        strategy['phases'].append({
            'phase': 6,
            'name': 'Type Mismatch Resolution',
            'errors': len(error_categories['type_mismatch']),
            'automation': 'Low',
            'estimated_time': '1-2 days',
            'description': 'Resolve complex type mismatches and interface issues'
        })
    
    if error_categories['missing_properties']:
        strategy['phases'].append({
            'phase': 7,
            'name': 'Missing Properties',
            'errors': len(error_categories['missing_properties']),
            'automation': 'Low',
            'estimated_time': '1-2 days',
            'description': 'Add missing properties to interfaces and fix object structures'
        })
    
    if error_categories['component_errors']:
        strategy['phases'].append({
            'phase': 8,
            'name': 'React Component Fixes',
            'errors': len(error_categories['component_errors']),
            'automation': 'Low',
            'estimated_time': '1-2 days',
            'description': 'Fix React component prop types and JSX issues'
        })
    
    if error_categories['hook_errors']:
        strategy['phases'].append({
            'phase': 9,
            'name': 'React Hook Fixes',
            'errors': len(error_categories['hook_errors']),
            'automation': 'Low',
            'estimated_time': '1-2 days',
            'description': 'Fix useEffect dependencies and hook usage patterns'
        })
    
    # Calculate totals
    total_days = sum(
        float(phase['estimated_time'].split('-')[1].split()[0]) 
        for phase in strategy['phases'] 
        if 'day' in phase['estimated_time']
    )
    total_hours = sum(
        float(phase['estimated_time'].split('-')[1].split()[0]) 
        for phase in strategy['phases'] 
        if 'hour' in phase['estimated_time']
    )
    
    strategy['estimated_time'] = f"{total_days + (total_hours / 8):.1f} days"
    strategy['automation_potential'] = analysis['fixable_automatically'] / analysis['total_errors'] * 100
    
    return strategy

def main():
    """Main analysis function"""
    print("🔍 Comprehensive TypeScript Error Analysis")
    print("=" * 60)
    
    print("📊 Analyzing TypeScript errors...")
    error_output = get_detailed_typescript_errors()
    
    print("🏷️  Categorizing errors...")
    error_categories = categorize_errors(error_output)
    
    print("📈 Analyzing patterns...")
    analysis = analyze_error_patterns(error_categories)
    
    print("🚀 Generating fixing strategy...")
    strategy = generate_fixing_strategy(error_categories, analysis)
    
    # Print comprehensive report
    print(f"\n📊 ERROR ANALYSIS RESULTS")
    print(f"Total TypeScript errors: {analysis['total_errors']}")
    print(f"Automation potential: {strategy['automation_potential']:.1f}%")
    print(f"Estimated fixing time: {strategy['estimated_time']}")
    
    print(f"\n📋 ERROR CATEGORIES:")
    for category, count in sorted(analysis['category_counts'].items(), key=lambda x: x[1], reverse=True):
        if count > 0:
            percentage = (count / analysis['total_errors']) * 100
            print(f"  {category.replace('_', ' ').title()}: {count} ({percentage:.1f}%)")
    
    print(f"\n🔥 MOST PROBLEMATIC FILES:")
    for file_path, error_count in analysis['most_problematic_files'][:10]:
        print(f"  {file_path}: {error_count} errors")
    
    print(f"\n📈 COMMON ERROR CODES:")
    for error_code, count in analysis['common_error_codes'].most_common(10):
        print(f"  TS{error_code}: {count} occurrences")
    
    print(f"\n🚀 SYSTEMATIC FIXING STRATEGY:")
    for phase in strategy['phases']:
        print(f"\n  Phase {phase['phase']}: {phase['name']}")
        print(f"    Errors: {phase['errors']}")
        print(f"    Automation: {phase['automation']}")
        print(f"    Time: {phase['estimated_time']}")
        print(f"    Description: {phase['description']}")
    
    print(f"\n✅ Analysis completed!")
    print(f"📄 Next: Begin Phase 1 automated fixes")

if __name__ == '__main__':
    main()
