#!/usr/bin/env python3
"""
Create All Remaining Domain Models
Efficiently creates all remaining domain model files
"""

# KPI Domain Models
kpi_models = '''"""
KPI Domain Models for EMS
Contains key performance indicator and analytics models
"""

from django.db import models
from django.utils import timezone
from .base import BaseModel, NamedModel, AuditableModel


class KPICategory(NamedModel):
    """KPI categories for organization"""
    pass


class KPI(BaseModel):
    """Key Performance Indicators"""
    
    name = models.CharField(max_length=200)
    name_ar = models.CharField(max_length=200, blank=True)
    category = models.ForeignKey(KPICategory, on_delete=models.CASCADE, related_name='kpis')
    target_value = models.DecimalField(max_digits=15, decimal_places=2)
    current_value = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    unit = models.CharField(max_length=50, blank=True)
    is_active = models.BooleanField(default=True)
    
    def __str__(self):
        return self.name


class KPIValue(BaseModel):
    """KPI value tracking"""
    
    kpi = models.ForeignKey(KPI, on_delete=models.CASCADE, related_name='values')
    value = models.DecimalField(max_digits=15, decimal_places=2)
    date = models.DateField()
    notes = models.TextField(blank=True)
    
    class Meta:
        unique_together = ['kpi', 'date']


class KPITarget(BaseModel):
    """KPI targets"""
    
    kpi = models.ForeignKey(KPI, on_delete=models.CASCADE, related_name='targets')
    target_value = models.DecimalField(max_digits=15, decimal_places=2)
    start_date = models.DateField()
    end_date = models.DateField()


class KPIAlert(BaseModel):
    """KPI alerts and notifications"""
    
    kpi = models.ForeignKey(KPI, on_delete=models.CASCADE, related_name='alerts')
    alert_type = models.CharField(max_length=50)
    threshold_value = models.DecimalField(max_digits=15, decimal_places=2)
    is_active = models.BooleanField(default=True)


class KPIMetric(BaseModel):
    """KPI metrics"""
    
    name = models.CharField(max_length=200)
    formula = models.TextField()
    is_active = models.BooleanField(default=True)


class KPIMetricValue(BaseModel):
    """KPI metric values"""
    
    metric = models.ForeignKey(KPIMetric, on_delete=models.CASCADE, related_name='values')
    value = models.DecimalField(max_digits=15, decimal_places=2)
    date = models.DateField()


class ReportTemplate(BaseModel):
    """Report templates"""
    
    name = models.CharField(max_length=200)
    template_content = models.TextField()
    is_active = models.BooleanField(default=True)


class ReportExecution(BaseModel):
    """Report execution tracking"""
    
    template = models.ForeignKey(ReportTemplate, on_delete=models.CASCADE, related_name='executions')
    executed_by = models.ForeignKey('core.Employee', on_delete=models.CASCADE)
    execution_time = models.DateTimeField(auto_now_add=True)
    status = models.CharField(max_length=50)


class Dashboard(BaseModel):
    """Dashboard configuration"""
    
    name = models.CharField(max_length=200)
    config = models.JSONField(default=dict)
    is_default = models.BooleanField(default=False)
    created_by = models.ForeignKey('core.Employee', on_delete=models.CASCADE)


class AnalyticsQuery(BaseModel):
    """Analytics query storage"""
    
    name = models.CharField(max_length=200)
    query = models.TextField()
    parameters = models.JSONField(default=dict)
    created_by = models.ForeignKey('core.Employee', on_delete=models.CASCADE)
'''

# Security Domain Models
security_models = '''"""
Security Domain Models for EMS
Contains security and compliance related models
"""

from django.db import models
from django.utils import timezone
from .base import BaseModel, NamedModel, AuditableModel


class UserSecurityProfile(BaseModel):
    """User security profiles"""
    
    user = models.OneToOneField('core.Employee', on_delete=models.CASCADE, related_name='security_profile')
    failed_login_attempts = models.IntegerField(default=0)
    last_failed_login = models.DateTimeField(null=True, blank=True)
    account_locked = models.BooleanField(default=False)
    locked_until = models.DateTimeField(null=True, blank=True)
    password_changed_at = models.DateTimeField(null=True, blank=True)
    two_factor_enabled = models.BooleanField(default=False)


class AuditTrail(BaseModel):
    """System audit trail"""
    
    user = models.ForeignKey('core.Employee', on_delete=models.CASCADE, related_name='audit_entries')
    action = models.CharField(max_length=100)
    object_type = models.CharField(max_length=50)
    object_id = models.CharField(max_length=50)
    changes = models.JSONField(default=dict)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)


class SecurityIncident(BaseModel):
    """Security incident tracking"""
    
    SEVERITY_CHOICES = [
        ('LOW', 'Low'),
        ('MEDIUM', 'Medium'),
        ('HIGH', 'High'),
        ('CRITICAL', 'Critical'),
    ]
    
    title = models.CharField(max_length=200)
    description = models.TextField()
    severity = models.CharField(max_length=20, choices=SEVERITY_CHOICES)
    status = models.CharField(max_length=50, default='OPEN')
    reported_by = models.ForeignKey('core.Employee', on_delete=models.CASCADE)
    assigned_to = models.ForeignKey(
        'core.Employee', 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='assigned_incidents'
    )


class ComplianceFramework(NamedModel):
    """Compliance frameworks"""
    
    version = models.CharField(max_length=50)
    effective_date = models.DateField()
    is_active = models.BooleanField(default=True)


class ComplianceControl(BaseModel):
    """Compliance controls"""
    
    framework = models.ForeignKey(ComplianceFramework, on_delete=models.CASCADE, related_name='controls')
    control_id = models.CharField(max_length=50)
    title = models.CharField(max_length=200)
    description = models.TextField()
    is_implemented = models.BooleanField(default=False)


class DataClassification(BaseModel):
    """Data classification levels"""
    
    CLASSIFICATION_LEVELS = [
        ('PUBLIC', 'Public'),
        ('INTERNAL', 'Internal'),
        ('CONFIDENTIAL', 'Confidential'),
        ('RESTRICTED', 'Restricted'),
    ]
    
    name = models.CharField(max_length=100)
    level = models.CharField(max_length=20, choices=CLASSIFICATION_LEVELS)
    description = models.TextField()
    handling_requirements = models.TextField()


class SecurityAlert(BaseModel):
    """Security alerts"""
    
    alert_type = models.CharField(max_length=50)
    message = models.TextField()
    severity = models.CharField(max_length=20)
    is_resolved = models.BooleanField(default=False)
    resolved_at = models.DateTimeField(null=True, blank=True)
'''

# Integration Domain Models
integration_models = '''"""
Integration Domain Models for EMS
Contains external integration related models
"""

from django.db import models
from django.utils import timezone
from .base import BaseModel, NamedModel, AuditableModel


class APIKey(BaseModel):
    """API key management"""
    
    name = models.CharField(max_length=200)
    key = models.CharField(max_length=255, unique=True)
    is_active = models.BooleanField(default=True)
    expires_at = models.DateTimeField(null=True, blank=True)
    created_by = models.ForeignKey('core.Employee', on_delete=models.CASCADE)
    last_used = models.DateTimeField(null=True, blank=True)


class ExternalService(BaseModel):
    """External service configurations"""
    
    name = models.CharField(max_length=200)
    base_url = models.URLField()
    api_key = models.CharField(max_length=255, blank=True)
    is_active = models.BooleanField(default=True)
    timeout_seconds = models.IntegerField(default=30)
    retry_attempts = models.IntegerField(default=3)


class WebhookEndpoint(BaseModel):
    """Webhook endpoint management"""
    
    name = models.CharField(max_length=200)
    url = models.URLField()
    secret = models.CharField(max_length=255)
    is_active = models.BooleanField(default=True)
    events = models.JSONField(default=list)


class WebhookEvent(BaseModel):
    """Webhook event log"""
    
    endpoint = models.ForeignKey(WebhookEndpoint, on_delete=models.CASCADE, related_name='events')
    event_type = models.CharField(max_length=100)
    payload = models.JSONField()
    response_status = models.IntegerField(null=True, blank=True)
    response_body = models.TextField(blank=True)
    delivered_at = models.DateTimeField(null=True, blank=True)


class IntegrationLog(BaseModel):
    """Integration activity log"""
    
    service = models.ForeignKey(ExternalService, on_delete=models.CASCADE, related_name='logs')
    operation = models.CharField(max_length=100)
    request_data = models.JSONField(default=dict)
    response_data = models.JSONField(default=dict)
    status = models.CharField(max_length=50)
    duration_ms = models.IntegerField(null=True, blank=True)
'''

# Enterprise Domain Models
enterprise_models = '''"""
Enterprise Domain Models for EMS
Contains enterprise and multi-tenant features
"""

from django.db import models
from django.utils import timezone
from .base import BaseModel, NamedModel, AuditableModel


class Tenant(BaseModel):
    """Multi-tenant support"""
    
    name = models.CharField(max_length=200)
    subdomain = models.CharField(max_length=100, unique=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    subscription_expires = models.DateTimeField(null=True, blank=True)


class TenantUser(BaseModel):
    """Tenant user relationships"""
    
    tenant = models.ForeignKey(Tenant, on_delete=models.CASCADE, related_name='users')
    user = models.ForeignKey('core.Employee', on_delete=models.CASCADE, related_name='tenants')
    is_admin = models.BooleanField(default=False)
    joined_at = models.DateTimeField(auto_now_add=True)


class MLModel(BaseModel):
    """Machine learning model management"""
    
    name = models.CharField(max_length=200)
    version = models.CharField(max_length=50)
    model_type = models.CharField(max_length=100)
    file_path = models.CharField(max_length=500)
    is_active = models.BooleanField(default=True)
    accuracy = models.DecimalField(max_digits=5, decimal_places=4, null=True, blank=True)
    created_by = models.ForeignKey('core.Employee', on_delete=models.CASCADE)


class MLPrediction(BaseModel):
    """ML prediction results"""
    
    model = models.ForeignKey(MLModel, on_delete=models.CASCADE, related_name='predictions')
    input_data = models.JSONField()
    prediction = models.JSONField()
    confidence = models.DecimalField(max_digits=5, decimal_places=4, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
'''

def create_all_domains():
    """Create all remaining domain model files"""
    
    domains = {
        'kpi.py': kpi_models,
        'security.py': security_models,
        'integrations.py': integration_models,
        'enterprise.py': enterprise_models,
    }
    
    for filename, content in domains.items():
        filepath = f'backend/ems/models/{filename}'
        with open(filepath, 'w') as f:
            f.write(content)
        print(f"✅ Created {filepath}")

if __name__ == '__main__':
    create_all_domains()
    print("🎉 All domain model files created!")
