#!/usr/bin/env python3
"""
Create Remaining Domain Models
Quickly creates all remaining domain model files
"""

# CRM Domain Models
crm_models = '''"""
CRM Domain Models for EMS
Contains customer relationship management models:
- ProductCategory: Product categorization
- Product: Product management
- Customer: Customer management
- SalesOrder: Sales order processing
- Report: Reporting system
"""

from django.db import models
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator
from .base import BaseModel, NamedModel, AuditableModel, MoneyField, APPROVAL_STATUS_CHOICES


class ProductCategory(NamedModel):
    """Product categories for organization"""
    
    parent_category = models.ForeignKey(
        'self', 
        on_delete=models.CASCADE, 
        null=True, 
        blank=True, 
        related_name='subcategories'
    )
    
    class Meta:
        verbose_name_plural = "Product Categories"
        ordering = ['name']


class Product(NamedModel):
    """Product management"""
    
    category = models.ForeignKey(
        ProductCategory, 
        on_delete=models.CASCADE,
        related_name='products'
    )
    sku = models.CharField(max_length=50, unique=True, help_text="Stock Keeping Unit")
    price = MoneyField(help_text="Product price")
    cost = MoneyField(default=0, help_text="Product cost")
    stock_quantity = models.IntegerField(default=0, help_text="Current stock")
    min_stock_level = models.IntegerField(default=0, help_text="Minimum stock level")
    
    def __str__(self):
        return f"{self.sku} - {self.name}"
    
    class Meta:
        ordering = ['sku']


class Customer(BaseModel):
    """Customer management"""
    
    customer_code = models.CharField(max_length=20, unique=True)
    company_name = models.CharField(max_length=200)
    company_name_ar = models.CharField(max_length=200, blank=True)
    contact_person = models.CharField(max_length=100)
    email = models.EmailField()
    phone = models.CharField(max_length=20)
    address = models.TextField()
    address_ar = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    
    def __str__(self):
        return f"{self.customer_code} - {self.company_name}"
    
    class Meta:
        ordering = ['customer_code']


class SalesOrder(BaseModel):
    """Sales order processing"""
    
    STATUS_CHOICES = [
        ('DRAFT', 'Draft'),
        ('CONFIRMED', 'Confirmed'),
        ('SHIPPED', 'Shipped'),
        ('DELIVERED', 'Delivered'),
        ('CANCELLED', 'Cancelled'),
    ]
    
    order_number = models.CharField(max_length=50, unique=True)
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, related_name='orders')
    order_date = models.DateField()
    delivery_date = models.DateField(null=True, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='DRAFT')
    total_amount = MoneyField()
    
    def __str__(self):
        return f"{self.order_number} - {self.customer.company_name}"
    
    class Meta:
        ordering = ['-order_date']


class Report(BaseModel):
    """Reporting system"""
    
    title = models.CharField(max_length=200)
    title_ar = models.CharField(max_length=200, blank=True)
    description = models.TextField()
    report_type = models.CharField(max_length=50)
    generated_by = models.ForeignKey('core.Employee', on_delete=models.CASCADE)
    file_path = models.CharField(max_length=500, blank=True)
    
    def __str__(self):
        return self.title
    
    class Meta:
        ordering = ['-created_at']
'''

# Communication Domain Models
communication_models = '''"""
Communication Domain Models for EMS
Contains communication and collaboration models:
- Announcement: Company announcements
- Message: Internal messaging
- Document: Document management
- Meeting: Meeting management
"""

from django.db import models
from django.utils import timezone
from .base import BaseModel, NamedModel, AuditableModel


class Announcement(BaseModel):
    """Company announcements"""
    
    title = models.CharField(max_length=200)
    title_ar = models.CharField(max_length=200, blank=True)
    content = models.TextField()
    content_ar = models.TextField(blank=True)
    is_urgent = models.BooleanField(default=False)
    is_published = models.BooleanField(default=False)
    published_at = models.DateTimeField(null=True, blank=True)
    expires_at = models.DateTimeField(null=True, blank=True)
    created_by = models.ForeignKey('core.Employee', on_delete=models.CASCADE)
    
    def __str__(self):
        return self.title
    
    class Meta:
        ordering = ['-created_at']


class Message(BaseModel):
    """Internal messaging system"""
    
    sender = models.ForeignKey('core.Employee', on_delete=models.CASCADE, related_name='sent_messages')
    recipient = models.ForeignKey('core.Employee', on_delete=models.CASCADE, related_name='received_messages')
    subject = models.CharField(max_length=200)
    content = models.TextField()
    is_read = models.BooleanField(default=False)
    read_at = models.DateTimeField(null=True, blank=True)
    
    def __str__(self):
        return f"{self.sender} to {self.recipient}: {self.subject}"
    
    class Meta:
        ordering = ['-created_at']


class Document(BaseModel):
    """Document management"""
    
    title = models.CharField(max_length=200)
    title_ar = models.CharField(max_length=200, blank=True)
    description = models.TextField(blank=True)
    file = models.FileField(upload_to='documents/')
    category = models.CharField(max_length=50)
    is_public = models.BooleanField(default=False)
    uploaded_by = models.ForeignKey('core.Employee', on_delete=models.CASCADE)
    
    def __str__(self):
        return self.title
    
    class Meta:
        ordering = ['-created_at']


class Meeting(BaseModel):
    """Meeting management"""
    
    title = models.CharField(max_length=200)
    title_ar = models.CharField(max_length=200, blank=True)
    description = models.TextField(blank=True)
    start_time = models.DateTimeField()
    end_time = models.DateTimeField()
    location = models.CharField(max_length=200, blank=True)
    organizer = models.ForeignKey('core.Employee', on_delete=models.CASCADE, related_name='organized_meetings')
    attendees = models.ManyToManyField('core.Employee', related_name='meetings')
    
    def __str__(self):
        return f"{self.title} - {self.start_time.strftime('%Y-%m-%d %H:%M')}"
    
    class Meta:
        ordering = ['-start_time']
'''

def create_domain_files():
    """Create all remaining domain model files"""
    
    domains = {
        'crm.py': crm_models,
        'communication.py': communication_models,
    }
    
    for filename, content in domains.items():
        filepath = f'backend/ems/models/{filename}'
        with open(filepath, 'w') as f:
            f.write(content)
        print(f"✅ Created {filepath}")

if __name__ == '__main__':
    create_domain_files()
    print("🎉 Domain model files created!")
