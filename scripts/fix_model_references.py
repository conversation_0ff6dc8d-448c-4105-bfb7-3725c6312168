#!/usr/bin/env python3
"""
Fix Model References Script
Fixes cross-domain model references to use correct app names
"""

import os
import re
import glob

def fix_references_in_file(file_path):
    """Fix model references in a single file"""
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Fix foreign key references - all models are in the same 'ems' app
        # Replace 'core.Employee' with 'Employee' (same app reference)
        content = re.sub(r"'core\.Employee'", "'Employee'", content)
        content = re.sub(r"'core\.Department'", "'Department'", content)
        content = re.sub(r"'core\.Role'", "'Role'", content)
        content = re.sub(r"'core\.UserProfile'", "'UserProfile'", content)
        content = re.sub(r"'core\.Activity'", "'Activity'", content)
        
        # Fix other domain references
        content = re.sub(r"'projects\.Project'", "'Project'", content)
        content = re.sub(r"'crm\.Customer'", "'Customer'", content)
        content = re.sub(r"'hr\.LeaveType'", "'LeaveType'", content)
        content = re.sub(r"'finance\.Currency'", "'Currency'", content)
        content = re.sub(r"'finance\.Budget'", "'Budget'", content)
        
        # If content changed, write it back
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        
        return False
        
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False

def main():
    """Main function to fix all model references"""
    
    print("🔄 Fixing model references...")
    
    # Fix references in all model files
    model_files = glob.glob('backend/ems/models/*.py')
    updated_files = []
    
    for file_path in model_files:
        if file_path.endswith('__init__.py') or file_path.endswith('base.py'):
            continue
            
        if fix_references_in_file(file_path):
            updated_files.append(file_path)
    
    print(f"✅ Fixed references in {len(updated_files)} files")
    
    for file_path in updated_files:
        print(f"  - {file_path}")
    
    print("🎉 Model reference fixing completed!")

if __name__ == '__main__':
    main()
