#!/usr/bin/env python3
"""
Fix React Component Return Types
Fixes React components that have incorrect void return types
"""

import os
import re
import subprocess
from pathlib import Path

def fix_react_component_return_types(file_path):
    """Fix React component return types from void to React.ReactElement"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Pattern 1: Function components with void return type that return JSX
        # const ComponentName = (props): void => {
        content = re.sub(
            r'(const\s+[A-Z]\w*\s*=\s*\([^)]*\))\s*:\s*void\s*(=>\s*{)',
            r'\1: React.ReactElement \2',
            content
        )
        
        # Pattern 2: Function declarations with void return type
        # function ComponentName(props): void {
        content = re.sub(
            r'(function\s+[A-Z]\w*\s*\([^)]*\))\s*:\s*void\s*({)',
            r'\1: React.ReactElement \2',
            content
        )
        
        # Pattern 3: Export function components
        # export const ComponentName = (props): void => {
        content = re.sub(
            r'(export\s+const\s+[A-Z]\w*\s*=\s*\([^)]*\))\s*:\s*void\s*(=>\s*{)',
            r'\1: React.ReactElement \2',
            content
        )
        
        # Pattern 4: Export function declarations
        # export function ComponentName(props): void {
        content = re.sub(
            r'(export\s+function\s+[A-Z]\w*\s*\([^)]*\))\s*:\s*void\s*({)',
            r'\1: React.ReactElement \2',
            content
        )
        
        # Pattern 5: Default export functions
        # export default function ComponentName(props): void {
        content = re.sub(
            r'(export\s+default\s+function\s+[A-Z]\w*\s*\([^)]*\))\s*:\s*void\s*({)',
            r'\1: React.ReactElement \2',
            content
        )
        
        # Pattern 6: Arrow function components assigned to variables
        # const ComponentName: React.FC = (props): void => {
        content = re.sub(
            r'(const\s+[A-Z]\w*:\s*React\.FC[^=]*=\s*\([^)]*\))\s*:\s*void\s*(=>\s*{)',
            r'\1 \2',
            content
        )
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        
        return False
        
    except Exception as e:
        print(f"Error fixing {file_path}: {e}")
        return False

def ensure_react_import(file_path):
    """Ensure React is imported for React.ReactElement type"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Check if React.ReactElement is used but React is not imported properly
        if 'React.ReactElement' in content:
            # Check if React is imported
            if not re.search(r"import\s+React", content):
                # Add React import at the top
                content = "import React from 'react';\n" + content
            elif re.search(r"import\s+React\s+from\s+'react'", content):
                # React is already imported correctly
                pass
            elif re.search(r"import\s+\{[^}]*\}\s+from\s+'react'", content):
                # Named imports only, need to add React
                content = re.sub(
                    r"import\s+\{([^}]*)\}\s+from\s+'react'",
                    r"import React, { \1 } from 'react'",
                    content
                )
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        
        return False
        
    except Exception as e:
        print(f"Error ensuring React import in {file_path}: {e}")
        return False

def process_files():
    """Process all TypeScript React files"""
    
    frontend_dir = Path('frontend/src')
    fixed_files = []
    
    for file_path in frontend_dir.rglob('*.tsx'):
        if file_path.is_file():
            fixed = False
            
            # Fix return types
            if fix_react_component_return_types(file_path):
                fixed = True
            
            # Ensure React import
            if ensure_react_import(file_path):
                fixed = True
            
            if fixed:
                fixed_files.append(str(file_path))
    
    return fixed_files

def get_typescript_errors():
    """Get current TypeScript errors"""
    try:
        result = subprocess.run(
            ['npx', 'tsc', '--noEmit', '--project', 'tsconfig.app.json'],
            cwd='frontend',
            capture_output=True,
            text=True,
            timeout=60
        )
        return result.stdout + result.stderr
    except Exception as e:
        return str(e)

def main():
    """Main function"""
    print("🔧 Fixing React Component Return Types")
    print("=" * 50)
    
    # Get initial error count
    print("📊 Getting initial TypeScript errors...")
    initial_errors = get_typescript_errors()
    initial_error_count = len([line for line in initial_errors.split('\n') if 'error TS' in line])
    
    print(f"Initial errors: {initial_error_count}")
    
    # Process files
    print("\n🔄 Processing React component files...")
    fixed_files = process_files()
    
    print(f"✅ Fixed return types in {len(fixed_files)} files")
    
    if fixed_files:
        print("\nFixed files:")
        for file_path in fixed_files[:10]:  # Show first 10
            print(f"  - {file_path}")
        if len(fixed_files) > 10:
            print(f"  ... and {len(fixed_files) - 10} more files")
    
    # Get final error count
    print("\n📊 Getting final TypeScript errors...")
    final_errors = get_typescript_errors()
    final_error_count = len([line for line in final_errors.split('\n') if 'error TS' in line])
    
    print(f"Final errors: {final_error_count}")
    
    if final_error_count < initial_error_count:
        improvement = initial_error_count - final_error_count
        print(f"🎉 Improved! Reduced errors by {improvement}")
    elif final_error_count == 0:
        print("🎉 Perfect! Zero TypeScript errors!")
    
    print("\n✅ React return type fixing completed!")

if __name__ == '__main__':
    main()
