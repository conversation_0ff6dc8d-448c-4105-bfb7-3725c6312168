#!/usr/bin/env python3
"""
Fix Type Mismatches Script
Systematically fixes the most common TypeScript type mismatch errors
"""

import os
import re
import subprocess
from pathlib import Path

def get_specific_errors(error_codes):
    """Get specific TypeScript errors by error code"""
    try:
        result = subprocess.run(
            ['npx', 'tsc', '--noEmit', '--project', 'tsconfig.app.json'],
            cwd='frontend',
            capture_output=True,
            text=True,
            timeout=120
        )
        
        error_output = result.stdout + result.stderr
        specific_errors = []
        
        for line in error_output.split('\n'):
            for code in error_codes:
                if f'error TS{code}:' in line:
                    match = re.match(r'(.+?)\((\d+),(\d+)\): error TS(\d+): (.+)', line)
                    if match:
                        file_path, line_num, col_num, error_code, error_msg = match.groups()
                        specific_errors.append({
                            'file': file_path,
                            'line': int(line_num),
                            'column': int(col_num),
                            'code': error_code,
                            'message': error_msg
                        })
        
        return specific_errors
        
    except Exception as e:
        print(f"Error getting TypeScript errors: {e}")
        return []

def fix_common_type_mismatches(file_path):
    """Fix common type mismatch patterns"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Fix 1: Event handler types
        # onChange={(e) => ...} -> onChange={(e: React.ChangeEvent<HTMLInputElement>) => ...}
        content = re.sub(
            r'onChange=\{\(e\)\s*=>\s*',
            r'onChange={(e: React.ChangeEvent<HTMLInputElement>) => ',
            content
        )
        
        # Fix 2: onClick handler types
        # onClick={(e) => ...} -> onClick={(e: React.MouseEvent) => ...}
        content = re.sub(
            r'onClick=\{\(e\)\s*=>\s*',
            r'onClick={(e: React.MouseEvent) => ',
            content
        )
        
        # Fix 3: onSubmit handler types
        # onSubmit={(e) => ...} -> onSubmit={(e: React.FormEvent) => ...}
        content = re.sub(
            r'onSubmit=\{\(e\)\s*=>\s*',
            r'onSubmit={(e: React.FormEvent) => ',
            content
        )
        
        # Fix 4: Common API response types
        # response: any -> response: unknown
        content = re.sub(
            r'response:\s*any\b',
            r'response: unknown',
            content
        )
        
        # Fix 5: Data prop types
        # data: any -> data: unknown
        content = re.sub(
            r'\bdata:\s*any\b',
            r'data: unknown',
            content
        )
        
        # Fix 6: Common useState types
        # useState() -> useState<string>() for string states
        content = re.sub(
            r"useState\(\s*['\"][^'\"]*['\"]\s*\)",
            r"useState<string>('')",
            content
        )
        
        # Fix 7: useState for numbers
        content = re.sub(
            r"useState\(\s*\d+\s*\)",
            r"useState<number>(0)",
            content
        )
        
        # Fix 8: useState for booleans
        content = re.sub(
            r"useState\(\s*(true|false)\s*\)",
            r"useState<boolean>(\1)",
            content
        )
        
        # Fix 9: Common array types
        # items: any[] -> items: unknown[]
        content = re.sub(
            r'\bitems:\s*any\[\]',
            r'items: unknown[]',
            content
        )
        
        # Fix 10: Object parameter types
        # (obj: any) -> (obj: Record<string, unknown>)
        content = re.sub(
            r'\(([^)]*obj[^)]*?):\s*any\)',
            r'(\1: Record<string, unknown>)',
            content
        )
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        
        return False
        
    except Exception as e:
        print(f"Error fixing {file_path}: {e}")
        return False

def fix_missing_properties(file_path):
    """Fix missing properties by adding them to interfaces"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Common missing properties patterns
        fixes = [
            # Add optional chaining for common properties
            (r'\.length\b', r'?.length'),
            (r'\.map\(', r'?.map('),
            (r'\.filter\(', r'?.filter('),
            (r'\.find\(', r'?.find('),
            (r'\.reduce\(', r'?.reduce('),
            
            # Add type assertions for common cases
            (r'as any\b', r'as unknown'),
            
            # Fix common object property access
            (r'data\.(\w+)', r'(data as any).\1'),
        ]
        
        for pattern, replacement in fixes:
            content = re.sub(pattern, replacement, content)
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        
        return False
        
    except Exception as e:
        print(f"Error fixing missing properties in {file_path}: {e}")
        return False

def add_missing_imports(file_path):
    """Add missing React imports"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Check if React types are used but not imported
        react_types_used = []
        
        if 'React.ChangeEvent' in content:
            react_types_used.append('ChangeEvent')
        if 'React.MouseEvent' in content:
            react_types_used.append('MouseEvent')
        if 'React.FormEvent' in content:
            react_types_used.append('FormEvent')
        if 'React.ReactElement' in content:
            react_types_used.append('ReactElement')
        
        if react_types_used:
            # Check current imports
            import_match = re.search(r"import\s+React(?:,\s*\{([^}]*)\})?\s+from\s+['\"]react['\"]", content)
            
            if import_match:
                existing_imports = import_match.group(1) if import_match.group(1) else ""
                existing_list = [imp.strip() for imp in existing_imports.split(',') if imp.strip()]
                
                # Add missing types
                for react_type in react_types_used:
                    if react_type not in existing_list:
                        existing_list.append(react_type)
                
                # Replace import
                new_import = f"import React, {{ {', '.join(existing_list)} }} from 'react'"
                content = re.sub(
                    r"import\s+React(?:,\s*\{[^}]*\})?\s+from\s+['\"]react['\"]",
                    new_import,
                    content
                )
            else:
                # Add new import
                new_import = f"import React, {{ {', '.join(react_types_used)} }} from 'react';\n"
                content = new_import + content
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        
        return False
        
    except Exception as e:
        print(f"Error adding imports to {file_path}: {e}")
        return False

def process_problematic_files():
    """Process the most problematic files first"""
    
    # Files with the most errors (from analysis)
    problematic_files = [
        'src/pages/assets/Assets.tsx',
        'src/pages/admin/UserManagement.tsx',
        'src/pages/quality/QualityManagement.tsx',
        'src/pages/sales-specific/SalesCustomers.tsx',
        'src/pages/employee-specific/EmployeeTasks.tsx',
        'src/pages/personal/PersonalCalendar.tsx',
        'src/pages/projects/ProjectReports.tsx',
        'src/components/performance/PerformanceDashboard.tsx',
        'src/pages/compliance/RiskManagement.tsx',
        'src/pages/projects/Tasks.tsx'
    ]
    
    fixed_files = []
    
    for file_path in problematic_files:
        full_path = Path('frontend') / file_path
        if full_path.exists():
            fixed = False
            
            if fix_common_type_mismatches(full_path):
                fixed = True
            
            if fix_missing_properties(full_path):
                fixed = True
            
            if add_missing_imports(full_path):
                fixed = True
            
            if fixed:
                fixed_files.append(str(full_path))
    
    return fixed_files

def main():
    """Main function"""
    print("🔧 Fixing TypeScript Type Mismatches")
    print("=" * 50)
    
    # Get initial error count for TS2322 (type mismatch)
    print("📊 Getting initial type mismatch errors...")
    initial_errors = get_specific_errors(['2322', '2339'])
    initial_count = len(initial_errors)
    
    print(f"Initial type mismatch errors: {initial_count}")
    
    # Process most problematic files
    print("\n🔄 Processing most problematic files...")
    fixed_files = process_problematic_files()
    
    print(f"✅ Applied fixes to {len(fixed_files)} files")
    
    if fixed_files:
        print("\nFixed files:")
        for file_path in fixed_files:
            print(f"  - {file_path}")
    
    # Get final error count
    print("\n📊 Getting final type mismatch errors...")
    final_errors = get_specific_errors(['2322', '2339'])
    final_count = len(final_errors)
    
    print(f"Final type mismatch errors: {final_count}")
    
    if final_count < initial_count:
        improvement = initial_count - final_count
        print(f"🎉 Improved! Reduced type mismatch errors by {improvement}")
    
    print("\n✅ Type mismatch fixing completed!")
    print("📄 Next: Run comprehensive TypeScript check")

if __name__ == '__main__':
    main()
