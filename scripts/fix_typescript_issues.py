#!/usr/bin/env python3
"""
Fix TypeScript Issues Script
Automatically fixes common TypeScript issues across the codebase
"""

import os
import re
import subprocess
from pathlib import Path

def get_typescript_errors():
    """Get current TypeScript errors"""
    try:
        result = subprocess.run(
            ['npx', 'tsc', '--noEmit', '--project', 'tsconfig.app.json'],
            cwd='frontend',
            capture_output=True,
            text=True,
            timeout=60
        )
        return result.stdout + result.stderr
    except Exception as e:
        return str(e)

def fix_missing_return_types(file_path):
    """Fix missing return type annotations"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Fix arrow functions without return types
        patterns = [
            # const functionName = (params) => {
            (r'const\s+(\w+)\s*=\s*\(([^)]*)\)\s*=>\s*{', r'const \1 = (\2): void => {'),
            # const functionName = (params) => 
            (r'const\s+(\w+)\s*=\s*\(([^)]*)\)\s*=>\s*([^{])', r'const \1 = (\2): any => \3'),
            # function functionName(params) {
            (r'function\s+(\w+)\s*\(([^)]*)\)\s*{', r'function \1(\2): void {'),
        ]
        
        for pattern, replacement in patterns:
            content = re.sub(pattern, replacement, content)
        
        # Fix specific common patterns
        # Handle event handlers
        content = re.sub(
            r'const\s+handle(\w+)\s*=\s*\(([^)]*)\)\s*:\s*void\s*=>\s*{',
            r'const handle\1 = (\2): void => {',
            content
        )
        
        # Handle formatters that should return strings
        content = re.sub(
            r'const\s+(format\w+|.*Formatter)\s*=\s*\(([^)]*)\)\s*:\s*void\s*=>\s*{',
            r'const \1 = (\2): string => {',
            content
        )
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        
        return False
        
    except Exception as e:
        print(f"Error fixing {file_path}: {e}")
        return False

def fix_missing_props_interfaces(file_path):
    """Add missing props interfaces to React components"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Find React components without props interfaces
        component_pattern = r'(?:export\s+)?(?:const|function)\s+(\w+)\s*(?:\([^)]*\)|=\s*\([^)]*\))\s*(?::\s*\w+)?\s*(?:=>)?\s*{'
        components = re.findall(component_pattern, content)
        
        interfaces_to_add = []
        
        for component in components:
            # Check if component already has props interface
            if not re.search(rf'interface\s+{component}Props', content) and \
               not re.search(rf'type\s+{component}Props', content):
                
                # Check if component takes props
                component_def_pattern = rf'(?:const|function)\s+{component}\s*\(\s*([^)]*)\s*\)'
                match = re.search(component_def_pattern, content)
                
                if match and match.group(1).strip() and match.group(1).strip() != '':
                    interfaces_to_add.append(f"""
interface {component}Props {{
  // TODO: Define proper prop types
  [key: string]: any;
}}
""")
        
        if interfaces_to_add:
            # Add interfaces at the top of the file after imports
            import_end = content.rfind('import ')
            if import_end != -1:
                # Find the end of the last import line
                import_end = content.find('\n', import_end)
                content = content[:import_end] + '\n' + '\n'.join(interfaces_to_add) + content[import_end:]
            else:
                content = '\n'.join(interfaces_to_add) + '\n' + content
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        
        return False
        
    except Exception as e:
        print(f"Error fixing props in {file_path}: {e}")
        return False

def fix_any_types(file_path):
    """Replace common 'any' types with more specific types"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Common replacements
        replacements = [
            # Event handlers
            (r': any\) => \{', ': React.MouseEvent) => {'),
            (r'event: any', 'event: React.ChangeEvent<HTMLInputElement>'),
            (r'e: any', 'e: React.FormEvent'),
            # API responses
            (r'data: any', 'data: unknown'),
            (r'response: any', 'response: unknown'),
            # Props
            (r'props: any', 'props: Record<string, unknown>'),
        ]
        
        for pattern, replacement in replacements:
            content = re.sub(pattern, replacement, content)
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        
        return False
        
    except Exception as e:
        print(f"Error fixing any types in {file_path}: {e}")
        return False

def fix_import_statements(file_path):
    """Fix import statements to include proper types"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Add React import if using JSX but no React import
        if 'React.' not in content and ('<' in content or 'jsx' in file_path.suffix):
            if 'import React' not in content:
                content = "import React from 'react';\n" + content
        
        # Add type imports for common patterns
        if 'React.MouseEvent' in content and 'import { type ' not in content:
            content = re.sub(
                r"import React from 'react';",
                "import React, { type MouseEvent, type ChangeEvent, type FormEvent } from 'react';",
                content
            )
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        
        return False
        
    except Exception as e:
        print(f"Error fixing imports in {file_path}: {e}")
        return False

def process_typescript_files():
    """Process all TypeScript files in the frontend"""
    
    frontend_dir = Path('frontend/src')
    fixed_files = []
    
    for file_path in frontend_dir.rglob('*.tsx'):
        if file_path.is_file():
            fixed = False
            
            # Apply fixes
            if fix_missing_return_types(file_path):
                fixed = True
            
            if fix_missing_props_interfaces(file_path):
                fixed = True
            
            if fix_any_types(file_path):
                fixed = True
            
            if fix_import_statements(file_path):
                fixed = True
            
            if fixed:
                fixed_files.append(str(file_path))
    
    return fixed_files

def main():
    """Main function"""
    print("🔧 Fixing TypeScript Issues")
    print("=" * 50)
    
    # Get initial error count
    print("📊 Getting initial TypeScript errors...")
    initial_errors = get_typescript_errors()
    initial_error_count = len([line for line in initial_errors.split('\n') if 'error TS' in line])
    
    print(f"Initial errors: {initial_error_count}")
    
    # Process files
    print("\n🔄 Processing TypeScript files...")
    fixed_files = process_typescript_files()
    
    print(f"✅ Fixed issues in {len(fixed_files)} files")
    
    if fixed_files:
        print("\nFixed files:")
        for file_path in fixed_files[:10]:  # Show first 10
            print(f"  - {file_path}")
        if len(fixed_files) > 10:
            print(f"  ... and {len(fixed_files) - 10} more files")
    
    # Get final error count
    print("\n📊 Getting final TypeScript errors...")
    final_errors = get_typescript_errors()
    final_error_count = len([line for line in final_errors.split('\n') if 'error TS' in line])
    
    print(f"Final errors: {final_error_count}")
    
    if final_error_count < initial_error_count:
        improvement = initial_error_count - final_error_count
        print(f"🎉 Improved! Reduced errors by {improvement}")
    
    print("\n✅ TypeScript issue fixing completed!")

if __name__ == '__main__':
    main()
