#!/usr/bin/env python3
"""
TypeScript Audit Script
Comprehensive analysis of TypeScript issues and migration roadmap
"""

import os
import subprocess
import json
import re
from pathlib import Path

def run_typescript_check():
    """Run TypeScript compiler to get current errors"""
    
    print("🔍 Running TypeScript compilation check...")
    
    try:
        # Change to frontend directory and run tsc
        result = subprocess.run(
            ['npx', 'tsc', '--noEmit', '--project', 'tsconfig.app.json'],
            cwd='frontend',
            capture_output=True,
            text=True,
            timeout=60
        )
        
        return result.returncode, result.stdout, result.stderr
        
    except subprocess.TimeoutExpired:
        return -1, "", "TypeScript check timed out"
    except Exception as e:
        return -1, "", str(e)

def analyze_typescript_files():
    """Analyze TypeScript files in the project"""
    
    print("📁 Analyzing TypeScript files...")
    
    frontend_dir = Path('frontend/src')
    
    file_stats = {
        'total_files': 0,
        'ts_files': 0,
        'tsx_files': 0,
        'js_files': 0,
        'jsx_files': 0,
        'total_lines': 0,
        'files_by_type': {}
    }
    
    type_issues = {
        'any_usage': [],
        'missing_types': [],
        'implicit_any': [],
        'no_return_type': []
    }
    
    for file_path in frontend_dir.rglob('*'):
        if file_path.is_file() and file_path.suffix in ['.ts', '.tsx', '.js', '.jsx']:
            file_stats['total_files'] += 1
            
            if file_path.suffix == '.ts':
                file_stats['ts_files'] += 1
            elif file_path.suffix == '.tsx':
                file_stats['tsx_files'] += 1
            elif file_path.suffix == '.js':
                file_stats['js_files'] += 1
            elif file_path.suffix == '.jsx':
                file_stats['jsx_files'] += 1
            
            # Count lines
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    lines = len(content.split('\n'))
                    file_stats['total_lines'] += lines
                    
                    # Analyze type issues
                    analyze_file_content(file_path, content, type_issues)
                    
            except Exception as e:
                print(f"Error reading {file_path}: {e}")
    
    return file_stats, type_issues

def analyze_file_content(file_path, content, type_issues):
    """Analyze individual file for type issues"""
    
    relative_path = str(file_path).replace('frontend/src/', '')
    
    # Check for 'any' usage
    any_matches = re.findall(r'\b(any)\b', content)
    if any_matches:
        type_issues['any_usage'].append({
            'file': relative_path,
            'count': len(any_matches)
        })
    
    # Check for missing prop types in components
    if file_path.suffix in ['.tsx', '.jsx']:
        # Look for function components without prop types
        component_pattern = r'(?:function|const)\s+(\w+)\s*(?:\([^)]*\)|=\s*\([^)]*\))\s*(?::\s*\w+)?\s*(?:=>)?\s*{'
        components = re.findall(component_pattern, content)
        
        for component in components:
            if not re.search(rf'interface\s+{component}Props', content) and \
               not re.search(rf'type\s+{component}Props', content):
                type_issues['missing_types'].append({
                    'file': relative_path,
                    'component': component,
                    'issue': 'Missing prop types'
                })
    
    # Check for functions without return types
    function_pattern = r'(?:function|const)\s+(\w+)\s*(?:\([^)]*\))\s*(?!:\s*\w+)\s*(?:=>)?\s*{'
    functions = re.findall(function_pattern, content)
    
    if functions:
        type_issues['no_return_type'].extend([{
            'file': relative_path,
            'function': func,
            'issue': 'Missing return type'
        } for func in functions])

def analyze_package_json():
    """Analyze package.json for TypeScript dependencies"""
    
    print("📦 Analyzing package.json dependencies...")
    
    try:
        with open('frontend/package.json', 'r') as f:
            package_data = json.load(f)
        
        dependencies = package_data.get('dependencies', {})
        dev_dependencies = package_data.get('devDependencies', {})
        
        typescript_deps = {}
        missing_types = []
        
        # Check for TypeScript and related packages
        all_deps = {**dependencies, **dev_dependencies}
        
        for dep, version in all_deps.items():
            if 'typescript' in dep or '@types/' in dep:
                typescript_deps[dep] = version
        
        # Check for common packages that need @types
        common_packages = [
            'react', 'react-dom', 'react-router-dom', 'axios', 
            'lodash', 'moment', 'uuid', 'classnames'
        ]
        
        for pkg in common_packages:
            if pkg in dependencies and f'@types/{pkg}' not in all_deps:
                missing_types.append(f'@types/{pkg}')
        
        return typescript_deps, missing_types
        
    except Exception as e:
        print(f"Error analyzing package.json: {e}")
        return {}, []

def create_migration_roadmap(file_stats, type_issues, typescript_deps, missing_types):
    """Create a comprehensive migration roadmap"""
    
    print("\n📋 Creating TypeScript Migration Roadmap...")
    
    roadmap = {
        'current_state': {
            'total_files': file_stats['total_files'],
            'typescript_files': file_stats['ts_files'] + file_stats['tsx_files'],
            'javascript_files': file_stats['js_files'] + file_stats['jsx_files'],
            'typescript_percentage': round(
                (file_stats['ts_files'] + file_stats['tsx_files']) / file_stats['total_files'] * 100, 1
            ) if file_stats['total_files'] > 0 else 0,
            'total_lines': file_stats['total_lines']
        },
        'issues_found': {
            'any_usage_files': len(type_issues['any_usage']),
            'missing_prop_types': len(type_issues['missing_types']),
            'functions_without_return_types': len(type_issues['no_return_type']),
            'missing_type_packages': len(missing_types)
        },
        'migration_phases': [
            {
                'phase': 1,
                'name': 'Foundation Setup',
                'tasks': [
                    'Install missing @types packages',
                    'Update tsconfig.json with strict settings',
                    'Create core type definitions',
                    'Set up type-checking scripts'
                ],
                'estimated_time': '1-2 days'
            },
            {
                'phase': 2,
                'name': 'Core Types',
                'tasks': [
                    'Define API response types',
                    'Create model interfaces',
                    'Add utility types',
                    'Fix import/export types'
                ],
                'estimated_time': '2-3 days'
            },
            {
                'phase': 3,
                'name': 'Component Types',
                'tasks': [
                    'Add prop interfaces to components',
                    'Fix component return types',
                    'Add event handler types',
                    'Update hook types'
                ],
                'estimated_time': '3-4 days'
            },
            {
                'phase': 4,
                'name': 'Strict Mode',
                'tasks': [
                    'Enable strict mode gradually',
                    'Fix all type errors',
                    'Remove any types',
                    'Add comprehensive type coverage'
                ],
                'estimated_time': '2-3 days'
            }
        ]
    }
    
    return roadmap

def main():
    """Main audit function"""
    
    print("🔍 TypeScript Migration Audit")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not os.path.exists('frontend'):
        print("❌ Error: frontend directory not found. Please run from project root.")
        return
    
    # Analyze current TypeScript setup
    file_stats, type_issues = analyze_typescript_files()
    typescript_deps, missing_types = analyze_package_json()
    
    # Run TypeScript compiler check
    returncode, stdout, stderr = run_typescript_check()
    
    # Create migration roadmap
    roadmap = create_migration_roadmap(file_stats, type_issues, typescript_deps, missing_types)
    
    # Print comprehensive report
    print(f"\n📊 Current State Analysis:")
    print(f"  - Total files: {roadmap['current_state']['total_files']}")
    print(f"  - TypeScript files: {roadmap['current_state']['typescript_files']}")
    print(f"  - JavaScript files: {roadmap['current_state']['javascript_files']}")
    print(f"  - TypeScript coverage: {roadmap['current_state']['typescript_percentage']}%")
    print(f"  - Total lines of code: {roadmap['current_state']['total_lines']:,}")
    
    print(f"\n⚠️  Issues Found:")
    print(f"  - Files using 'any': {roadmap['issues_found']['any_usage_files']}")
    print(f"  - Components missing prop types: {roadmap['issues_found']['missing_prop_types']}")
    print(f"  - Functions without return types: {roadmap['issues_found']['functions_without_return_types']}")
    print(f"  - Missing @types packages: {roadmap['issues_found']['missing_type_packages']}")
    
    if missing_types:
        print(f"\n📦 Missing Type Packages:")
        for pkg in missing_types:
            print(f"  - {pkg}")
    
    print(f"\n🚀 Migration Roadmap:")
    for phase in roadmap['migration_phases']:
        print(f"\n  Phase {phase['phase']}: {phase['name']} ({phase['estimated_time']})")
        for task in phase['tasks']:
            print(f"    - {task}")
    
    # TypeScript compilation results
    print(f"\n🔧 TypeScript Compilation Status:")
    if returncode == 0:
        print("  ✅ No compilation errors found")
    else:
        print(f"  ❌ Compilation failed with {returncode} errors")
        if stdout:
            error_count = len(stdout.split('\n')) - 1
            print(f"  📝 {error_count} type errors to fix")
    
    print(f"\n✅ Audit completed!")
    print(f"📄 Next step: Begin Phase 1 - Foundation Setup")

if __name__ == '__main__':
    main()
