#!/usr/bin/env python3
"""
Update Model Imports Script
Updates all imports across the codebase to use the new domain-specific model structure
"""

import os
import re
import glob
from pathlib import Path

def update_imports_in_file(file_path):
    """Update imports in a single file"""
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Pattern 1: from ems.models import Model1, Model2, ...
        # Replace with: from ems.models import Model1, Model2, ...
        # (This should work because our __init__.py imports everything)
        
        # Pattern 2: from .models import Model1, Model2, ...
        # Replace with: from .models import Model1, Model2, ...
        # (This should also work)
        
        # Pattern 3: ems.models.ModelName
        # Replace with: ems.models.ModelName
        # (This should work through __init__.py)
        
        # The key insight is that our __init__.py already imports everything,
        # so existing imports should continue to work!
        
        # However, we should update any direct references to the old models.py file
        content = re.sub(
            r'from ems\.models\.models import',
            'from ems.models import',
            content
        )
        
        # Update any references to models.models
        content = re.sub(
            r'ems\.models\.models\.',
            'ems.models.',
            content
        )
        
        # If content changed, write it back
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        
        return False
        
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False

def find_python_files():
    """Find all Python files in the backend directory"""
    
    python_files = []
    
    # Find all .py files in backend directory
    for root, dirs, files in os.walk('backend'):
        # Skip __pycache__ directories
        dirs[:] = [d for d in dirs if d != '__pycache__']
        
        for file in files:
            if file.endswith('.py'):
                python_files.append(os.path.join(root, file))
    
    return python_files

def main():
    """Main function to update all imports"""
    
    print("🔄 Updating model imports across the codebase...")
    
    # Find all Python files
    python_files = find_python_files()
    print(f"📁 Found {len(python_files)} Python files")
    
    updated_files = []
    
    # Update imports in each file
    for file_path in python_files:
        # Skip the original models.py file
        if file_path.endswith('ems/models.py'):
            continue
            
        # Skip migration files (they should reference the old structure)
        if '/migrations/' in file_path:
            continue
            
        if update_imports_in_file(file_path):
            updated_files.append(file_path)
    
    print(f"✅ Updated imports in {len(updated_files)} files")
    
    if updated_files:
        print("\nUpdated files:")
        for file_path in updated_files[:10]:  # Show first 10
            print(f"  - {file_path}")
        if len(updated_files) > 10:
            print(f"  ... and {len(updated_files) - 10} more files")
    
    print("\n🎉 Import update completed!")
    print("\n📝 Note: The new models package uses __init__.py to maintain backward compatibility.")
    print("   Existing imports should continue to work without changes.")

if __name__ == '__main__':
    main()
