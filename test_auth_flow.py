#!/usr/bin/env python3
"""
Comprehensive Authentication Flow Test Script
Tests httpOnly cookie-based authentication for EMS application

This script tests:
1. Login with httpOnly cookies
2. Token verification via cookies
3. Token refresh via cookies
4. Logout and cookie cleanup
5. Error handling for invalid credentials
6. Error handling for expired tokens
"""

import requests
import json
import time
import sys
from typing import Dict, Any, Optional

class AuthFlowTester:
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
        
    def log(self, message: str, level: str = "INFO"):
        """Log test messages with timestamp"""
        timestamp = time.strftime("%H:%M:%S")
        print(f"[{timestamp}] {level}: {message}")
        
    def test_login(self, username: str, password: str) -> bool:
        """Test login functionality with httpOnly cookies"""
        self.log("Testing login with httpOnly cookies...")
        
        try:
            response = self.session.post(
                f"{self.base_url}/api/auth/login/",
                json={
                    "username": username,
                    "password": password
                }
            )
            
            self.log(f"Login response status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                self.log(f"Login successful for user: {data.get('user', {}).get('username', 'unknown')}")
                
                # Check if httpOnly cookies are set
                cookies = response.cookies
                has_access_token = 'access_token' in cookies
                has_refresh_token = 'refresh_token' in cookies
                
                self.log(f"HttpOnly cookies set - Access: {has_access_token}, Refresh: {has_refresh_token}")
                
                # Verify response contains expected data
                required_fields = ['user', 'access_token', 'refresh_token', 'token_type']
                missing_fields = [field for field in required_fields if field not in data]
                
                if missing_fields:
                    self.log(f"WARNING: Missing response fields: {missing_fields}", "WARN")
                    return False
                    
                return True
            else:
                error_data = response.json() if response.content else {}
                self.log(f"Login failed: {error_data.get('message', 'Unknown error')}", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"Login test failed with exception: {str(e)}", "ERROR")
            return False
    
    def test_token_verification(self) -> bool:
        """Test token verification using httpOnly cookies"""
        self.log("Testing token verification via httpOnly cookies...")
        
        try:
            response = self.session.get(f"{self.base_url}/api/auth/verify/")
            
            self.log(f"Token verification response status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                user_id = data.get('id')
                username = data.get('username')
                
                self.log(f"Token verification successful - User: {username} (ID: {user_id})")
                return True
            else:
                error_data = response.json() if response.content else {}
                self.log(f"Token verification failed: {error_data.get('message', 'Unknown error')}", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"Token verification test failed with exception: {str(e)}", "ERROR")
            return False
    
    def test_token_refresh(self) -> bool:
        """Test token refresh using httpOnly cookies"""
        self.log("Testing token refresh via httpOnly cookies...")
        
        try:
            response = self.session.post(f"{self.base_url}/api/auth/refresh/")
            
            self.log(f"Token refresh response status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                self.log(f"Token refresh successful: {data.get('message', 'No message')}")
                
                # Check if new access token cookie is set
                cookies = response.cookies
                has_new_access_token = 'access_token' in cookies
                
                self.log(f"New access token cookie set: {has_new_access_token}")
                return True
            else:
                error_data = response.json() if response.content else {}
                self.log(f"Token refresh failed: {error_data.get('message', 'Unknown error')}", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"Token refresh test failed with exception: {str(e)}", "ERROR")
            return False
    
    def test_logout(self) -> bool:
        """Test logout and cookie cleanup"""
        self.log("Testing logout and cookie cleanup...")
        
        try:
            response = self.session.post(f"{self.base_url}/api/auth/logout/")
            
            self.log(f"Logout response status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                self.log(f"Logout successful: {data.get('message', 'No message')}")
                
                # Check if cookies are cleared (they should be empty or expired)
                cookies = response.cookies
                self.log(f"Cookies after logout: {dict(cookies)}")
                
                return True
            else:
                error_data = response.json() if response.content else {}
                self.log(f"Logout failed: {error_data.get('message', 'Unknown error')}", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"Logout test failed with exception: {str(e)}", "ERROR")
            return False
    
    def test_invalid_credentials(self) -> bool:
        """Test error handling for invalid credentials"""
        self.log("Testing error handling for invalid credentials...")
        
        try:
            response = self.session.post(
                f"{self.base_url}/api/auth/login/",
                json={
                    "username": "invalid_user",
                    "password": "invalid_password"
                }
            )
            
            self.log(f"Invalid credentials response status: {response.status_code}")
            
            if response.status_code == 401:
                data = response.json()
                self.log(f"Invalid credentials properly rejected: {data.get('message', 'No message')}")
                return True
            else:
                self.log(f"Unexpected response for invalid credentials: {response.status_code}", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"Invalid credentials test failed with exception: {str(e)}", "ERROR")
            return False
    
    def test_unauthorized_access(self) -> bool:
        """Test access to protected endpoints without authentication"""
        self.log("Testing unauthorized access to protected endpoints...")
        
        # Clear any existing cookies
        self.session.cookies.clear()
        
        try:
            response = self.session.get(f"{self.base_url}/api/auth/verify/")
            
            self.log(f"Unauthorized access response status: {response.status_code}")
            
            if response.status_code == 401:
                self.log("Unauthorized access properly blocked")
                return True
            else:
                self.log(f"Unexpected response for unauthorized access: {response.status_code}", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"Unauthorized access test failed with exception: {str(e)}", "ERROR")
            return False
    
    def run_full_test_suite(self, username: str, password: str) -> Dict[str, bool]:
        """Run the complete authentication test suite"""
        self.log("=" * 60)
        self.log("STARTING COMPREHENSIVE AUTHENTICATION FLOW TEST")
        self.log("=" * 60)
        
        results = {}
        
        # Test 1: Invalid credentials
        results['invalid_credentials'] = self.test_invalid_credentials()
        
        # Test 2: Unauthorized access
        results['unauthorized_access'] = self.test_unauthorized_access()
        
        # Test 3: Valid login
        results['login'] = self.test_login(username, password)
        
        if results['login']:
            # Test 4: Token verification
            results['token_verification'] = self.test_token_verification()
            
            # Test 5: Token refresh
            results['token_refresh'] = self.test_token_refresh()
            
            # Test 6: Verify token still works after refresh
            results['post_refresh_verification'] = self.test_token_verification()
            
            # Test 7: Logout
            results['logout'] = self.test_logout()
            
            # Test 8: Verify logout worked
            results['post_logout_verification'] = not self.test_token_verification()
        else:
            self.log("Skipping authenticated tests due to login failure", "WARN")
            results.update({
                'token_verification': False,
                'token_refresh': False,
                'post_refresh_verification': False,
                'logout': False,
                'post_logout_verification': False
            })
        
        return results
    
    def print_test_summary(self, results: Dict[str, bool]):
        """Print a summary of test results"""
        self.log("=" * 60)
        self.log("TEST RESULTS SUMMARY")
        self.log("=" * 60)
        
        passed = sum(results.values())
        total = len(results)
        
        for test_name, passed_test in results.items():
            status = "PASS" if passed_test else "FAIL"
            self.log(f"{test_name.replace('_', ' ').title()}: {status}")
        
        self.log("-" * 60)
        self.log(f"OVERALL: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
        
        if passed == total:
            self.log("🎉 ALL TESTS PASSED! HttpOnly cookie authentication is working correctly.", "SUCCESS")
        else:
            self.log(f"❌ {total - passed} tests failed. Please review the authentication implementation.", "ERROR")

def main():
    """Main test execution"""
    if len(sys.argv) < 3:
        print("Usage: python test_auth_flow.py <username> <password>")
        print("Example: python test_auth_flow.py admin admin123")
        sys.exit(1)
    
    username = sys.argv[1]
    password = sys.argv[2]
    
    tester = AuthFlowTester()
    results = tester.run_full_test_suite(username, password)
    tester.print_test_summary(results)
    
    # Exit with error code if any tests failed
    if not all(results.values()):
        sys.exit(1)

if __name__ == "__main__":
    main()
