#!/usr/bin/env python3
"""
Simple Authentication Test Script
Tests the core httpOnly cookie authentication flow
"""

import requests
import json

def test_auth_flow():
    base_url = "http://localhost:8000"
    session = requests.Session()
    
    print("🔐 Testing httpOnly Cookie Authentication Flow")
    print("=" * 50)
    
    # Test 1: Login
    print("\n1️⃣ Testing Login...")
    try:
        login_response = session.post(
            f"{base_url}/api/auth/login/",
            json={
                "username": "test_api_user",
                "password": "password123"
            },
            timeout=10
        )
        
        print(f"   Status: {login_response.status_code}")
        
        if login_response.status_code == 200:
            data = login_response.json()
            print(f"   ✅ Login successful for user: {data['user']['username']}")
            print(f"   🍪 Cookies received: {len(session.cookies)} cookies")
            
            # Check for httpOnly cookies in response headers
            set_cookies = login_response.headers.get('Set-Cookie', '')
            has_access_token = 'access_token' in set_cookies
            has_refresh_token = 'refresh_token' in set_cookies
            has_httponly = 'HttpOnly' in set_cookies
            
            print(f"   🔑 Access token cookie: {'✅' if has_access_token else '❌'}")
            print(f"   🔄 Refresh token cookie: {'✅' if has_refresh_token else '❌'}")
            print(f"   🔒 HttpOnly flag: {'✅' if has_httponly else '❌'}")
            
        else:
            print(f"   ❌ Login failed: {login_response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Login error: {str(e)}")
        return False
    
    # Test 2: Token Verification
    print("\n2️⃣ Testing Token Verification...")
    try:
        verify_response = session.get(
            f"{base_url}/api/auth/verify/",
            timeout=10
        )
        
        print(f"   Status: {verify_response.status_code}")
        
        if verify_response.status_code == 200:
            user_data = verify_response.json()
            print(f"   ✅ Token verification successful")
            print(f"   👤 User: {user_data['username']} (ID: {user_data['id']})")
            print(f"   🏢 Role: {user_data['role']['name']}")
        else:
            print(f"   ❌ Token verification failed: {verify_response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Verification error: {str(e)}")
        return False
    
    # Test 3: Logout
    print("\n3️⃣ Testing Logout...")
    try:
        logout_response = session.post(
            f"{base_url}/api/auth/logout/",
            timeout=10
        )
        
        print(f"   Status: {logout_response.status_code}")
        
        if logout_response.status_code == 200:
            print(f"   ✅ Logout successful")
            
            # Check if cookies were cleared
            set_cookies = logout_response.headers.get('Set-Cookie', '')
            cookies_cleared = 'access_token=' in set_cookies and 'refresh_token=' in set_cookies
            print(f"   🧹 Cookies cleared: {'✅' if cookies_cleared else '❌'}")
            
        else:
            print(f"   ❌ Logout failed: {logout_response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Logout error: {str(e)}")
        return False
    
    # Test 4: Verify logout (should fail)
    print("\n4️⃣ Testing Post-Logout Verification...")
    try:
        post_logout_response = session.get(
            f"{base_url}/api/auth/verify/",
            timeout=10
        )
        
        print(f"   Status: {post_logout_response.status_code}")
        
        if post_logout_response.status_code == 401:
            print(f"   ✅ Post-logout verification correctly failed (401)")
        else:
            print(f"   ⚠️ Unexpected status after logout: {post_logout_response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Post-logout verification error: {str(e)}")
    
    print("\n🎉 Authentication flow test completed!")
    return True

if __name__ == "__main__":
    test_auth_flow()
