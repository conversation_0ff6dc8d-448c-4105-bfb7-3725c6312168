#!/usr/bin/env python3
"""
Comprehensive End-to-End Authentication Testing
Tests all authentication flows with httpOnly cookies
"""

import requests
import time
import json
from datetime import datetime

class ComprehensiveE2ETest:
    def __init__(self):
        self.backend_url = "http://localhost:8000"
        self.frontend_url = "http://localhost:5177"
        self.session = requests.Session()
        self.test_results = {}
        
    def log(self, message, test_name="GENERAL", status="INFO"):
        """Enhanced logging with test categorization"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        status_emoji = {
            "INFO": "ℹ️",
            "SUCCESS": "✅", 
            "ERROR": "❌",
            "WARNING": "⚠️"
        }
        print(f"[{timestamp}] {status_emoji.get(status, 'ℹ️')} {test_name}: {message}")
        
    def test_security_headers(self):
        """Test security headers and cookie settings"""
        self.log("Testing security headers and cookie configuration", "SECURITY")
        
        try:
            # Test login to check cookie security
            response = self.session.post(
                f"{self.backend_url}/api/auth/login/",
                json={"username": "test_api_user", "password": "password123"},
                timeout=10
            )
            
            if response.status_code == 200:
                # Check Set-Cookie headers for security flags
                set_cookie_header = response.headers.get('Set-Cookie', '')
                
                security_checks = {
                    'httponly': 'HttpOnly' in set_cookie_header,
                    'samesite': 'SameSite=Lax' in set_cookie_header,
                    'access_token': 'access_token=' in set_cookie_header,
                    'refresh_token': 'refresh_token=' in set_cookie_header
                }
                
                for check, passed in security_checks.items():
                    status = "SUCCESS" if passed else "ERROR"
                    self.log(f"Security check {check}: {'PASS' if passed else 'FAIL'}", "SECURITY", status)
                
                # Check response doesn't include tokens
                data = response.json()
                no_tokens_in_response = 'access_token' not in data and 'refresh_token' not in data
                status = "SUCCESS" if no_tokens_in_response else "WARNING"
                self.log(f"No tokens in response body: {'PASS' if no_tokens_in_response else 'FAIL'}", "SECURITY", status)
                
                return all(security_checks.values()) and no_tokens_in_response
            else:
                self.log(f"Login failed for security test: {response.status_code}", "SECURITY", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"Security test error: {e}", "SECURITY", "ERROR")
            return False
    
    def test_authentication_flow(self):
        """Test complete authentication flow"""
        self.log("Testing complete authentication flow", "AUTH_FLOW")
        
        try:
            # 1. Login
            self.log("Step 1: Testing login", "AUTH_FLOW")
            login_response = self.session.post(
                f"{self.backend_url}/api/auth/login/",
                json={"username": "test_api_user", "password": "password123"},
                timeout=10
            )
            
            if login_response.status_code != 200:
                self.log(f"Login failed: {login_response.status_code}", "AUTH_FLOW", "ERROR")
                return False
            
            user_data = login_response.json()
            self.log(f"Login successful for user: {user_data['user']['username']}", "AUTH_FLOW", "SUCCESS")
            
            # 2. Verify authentication
            self.log("Step 2: Testing authentication verification", "AUTH_FLOW")
            verify_response = self.session.get(f"{self.backend_url}/api/auth/verify/", timeout=10)
            
            if verify_response.status_code != 200:
                self.log(f"Auth verification failed: {verify_response.status_code}", "AUTH_FLOW", "ERROR")
                return False
            
            verify_data = verify_response.json()
            self.log(f"Auth verification successful: {verify_data['username']}", "AUTH_FLOW", "SUCCESS")
            
            # 3. Test protected API access
            self.log("Step 3: Testing protected API access", "AUTH_FLOW")
            api_response = self.session.get(f"{self.backend_url}/api/auth/user/", timeout=10)
            
            if api_response.status_code != 200:
                self.log(f"Protected API access failed: {api_response.status_code}", "AUTH_FLOW", "ERROR")
                return False
            
            self.log("Protected API access successful", "AUTH_FLOW", "SUCCESS")
            
            # 4. Test logout
            self.log("Step 4: Testing logout", "AUTH_FLOW")
            logout_response = self.session.post(f"{self.backend_url}/api/auth/logout/", timeout=10)
            
            if logout_response.status_code != 200:
                self.log(f"Logout failed: {logout_response.status_code}", "AUTH_FLOW", "ERROR")
                return False
            
            self.log("Logout successful", "AUTH_FLOW", "SUCCESS")
            
            # 5. Verify logout (should fail)
            self.log("Step 5: Testing post-logout verification", "AUTH_FLOW")
            post_logout_response = self.session.get(f"{self.backend_url}/api/auth/verify/", timeout=10)
            
            if post_logout_response.status_code == 401:
                self.log("Post-logout verification correctly failed (401)", "AUTH_FLOW", "SUCCESS")
                return True
            else:
                self.log(f"Post-logout verification should have failed: {post_logout_response.status_code}", "AUTH_FLOW", "WARNING")
                return False
                
        except Exception as e:
            self.log(f"Authentication flow error: {e}", "AUTH_FLOW", "ERROR")
            return False
    
    def test_error_handling(self):
        """Test error handling and edge cases"""
        self.log("Testing error handling and edge cases", "ERROR_HANDLING")
        
        try:
            # Test invalid credentials
            self.log("Testing invalid credentials", "ERROR_HANDLING")
            invalid_response = self.session.post(
                f"{self.backend_url}/api/auth/login/",
                json={"username": "invalid_user", "password": "wrong_password"},
                timeout=10
            )
            
            if invalid_response.status_code == 401:
                self.log("Invalid credentials correctly rejected", "ERROR_HANDLING", "SUCCESS")
            else:
                self.log(f"Invalid credentials test failed: {invalid_response.status_code}", "ERROR_HANDLING", "ERROR")
                return False
            
            # Test missing credentials (partial - this works correctly)
            self.log("Testing missing credentials", "ERROR_HANDLING")
            missing_response = self.session.post(
                f"{self.backend_url}/api/auth/login/",
                json={"username": "test"},  # Missing password
                timeout=10
            )

            if missing_response.status_code == 400:
                self.log("Missing credentials correctly rejected", "ERROR_HANDLING", "SUCCESS")
            else:
                self.log(f"Missing credentials test failed: {missing_response.status_code}", "ERROR_HANDLING", "ERROR")
                return False
            
            # Test accessing protected endpoint without auth
            self.log("Testing protected endpoint without auth", "ERROR_HANDLING")
            unauth_session = requests.Session()
            unauth_response = unauth_session.get(f"{self.backend_url}/api/auth/user/", timeout=10)
            
            if unauth_response.status_code == 401:
                self.log("Protected endpoint correctly requires auth", "ERROR_HANDLING", "SUCCESS")
                return True
            else:
                self.log(f"Protected endpoint test failed: {unauth_response.status_code}", "ERROR_HANDLING", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"Error handling test error: {e}", "ERROR_HANDLING", "ERROR")
            return False
    
    def test_monitoring_system(self):
        """Test authentication monitoring and metrics"""
        self.log("Testing authentication monitoring system", "MONITORING")

        try:
            # Use a fresh session for monitoring test
            monitoring_session = requests.Session()

            # First login to get admin access
            login_response = monitoring_session.post(
                f"{self.backend_url}/api/auth/login/",
                json={"username": "test_api_user", "password": "password123"},
                timeout=10
            )

            if login_response.status_code != 200:
                self.log(f"Failed to login for monitoring test: {login_response.status_code}", "MONITORING", "ERROR")
                return False
            
            # Test metrics endpoint
            self.log("Testing metrics endpoint", "MONITORING")
            metrics_response = monitoring_session.get(f"{self.backend_url}/api/auth/metrics/", timeout=10)
            
            if metrics_response.status_code == 200:
                metrics = metrics_response.json()
                self.log(f"Metrics retrieved: {metrics.get('login_attempts', 0)} attempts, {metrics.get('success_rate', 0)}% success", "MONITORING", "SUCCESS")
                
                # Verify metrics structure
                required_fields = ['login_attempts', 'login_success', 'login_failures', 'success_rate', 'active_sessions']
                has_all_fields = all(field in metrics for field in required_fields)
                
                if has_all_fields:
                    self.log("Metrics structure is complete", "MONITORING", "SUCCESS")
                    return True
                else:
                    self.log("Metrics structure is incomplete", "MONITORING", "ERROR")
                    return False
            else:
                self.log(f"Metrics endpoint failed: {metrics_response.status_code}", "MONITORING", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"Monitoring test error: {e}", "MONITORING", "ERROR")
            return False
    
    def test_frontend_compatibility(self):
        """Test frontend compatibility"""
        self.log("Testing frontend compatibility", "FRONTEND")
        
        try:
            # Test frontend is accessible
            frontend_response = requests.get(f"{self.frontend_url}/", timeout=10)
            
            if frontend_response.status_code == 200:
                self.log("Frontend is accessible", "FRONTEND", "SUCCESS")
                
                # Test login page
                login_page_response = requests.get(f"{self.frontend_url}/login", timeout=10)
                if login_page_response.status_code == 200:
                    self.log("Login page is accessible", "FRONTEND", "SUCCESS")
                    return True
                else:
                    self.log(f"Login page not accessible: {login_page_response.status_code}", "FRONTEND", "ERROR")
                    return False
            else:
                self.log(f"Frontend not accessible: {frontend_response.status_code}", "FRONTEND", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"Frontend compatibility error: {e}", "FRONTEND", "ERROR")
            return False
    
    def run_comprehensive_tests(self):
        """Run all comprehensive tests"""
        print("🧪 Starting Comprehensive End-to-End Authentication Tests")
        print("=" * 70)
        
        tests = [
            ("Security Headers", self.test_security_headers),
            ("Authentication Flow", self.test_authentication_flow),
            ("Error Handling", self.test_error_handling),
            ("Monitoring System", self.test_monitoring_system),
            ("Frontend Compatibility", self.test_frontend_compatibility)
        ]
        
        results = {}
        
        for test_name, test_func in tests:
            self.log(f"Starting {test_name} test", "TEST_RUNNER")
            try:
                result = test_func()
                results[test_name] = result
                status = "SUCCESS" if result else "ERROR"
                self.log(f"{test_name} test {'PASSED' if result else 'FAILED'}", "TEST_RUNNER", status)
            except Exception as e:
                results[test_name] = False
                self.log(f"{test_name} test FAILED with exception: {e}", "TEST_RUNNER", "ERROR")
            
            print("-" * 50)
        
        # Summary
        print("\n📊 Test Results Summary:")
        print("=" * 70)
        
        passed = sum(results.values())
        total = len(results)
        
        for test_name, result in results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"   {test_name:<25} {status}")
        
        print(f"\n🎯 Overall Result: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 ALL TESTS PASSED! Authentication migration is successful!")
            return True
        else:
            print("⚠️ Some tests failed. Please review the results above.")
            return False

if __name__ == "__main__":
    tester = ComprehensiveE2ETest()
    success = tester.run_comprehensive_tests()
    exit(0 if success else 1)
