#!/usr/bin/env node
/**
 * Frontend Authentication Integration Test
 * Tests the frontend authentication flow using Node.js fetch with cookie support
 */

const fs = require('fs');

// Simple cookie jar implementation
class CookieJar {
  constructor() {
    this.cookies = new Map();
  }

  setCookie(cookieString, url) {
    const cookies = cookieString.split(',').map(c => c.trim());
    cookies.forEach(cookie => {
      const [nameValue, ...attributes] = cookie.split(';').map(s => s.trim());
      const [name, value] = nameValue.split('=');
      
      if (name && value) {
        const cookieObj = { value };
        attributes.forEach(attr => {
          const [key, val] = attr.split('=');
          if (key) {
            cookieObj[key.toLowerCase()] = val || true;
          }
        });
        this.cookies.set(name, cookieObj);
      }
    });
  }

  getCookieHeader() {
    const validCookies = [];
    for (const [name, cookie] of this.cookies.entries()) {
      // Simple expiration check (not comprehensive)
      validCookies.push(`${name}=${cookie.value}`);
    }
    return validCookies.join('; ');
  }

  clear() {
    this.cookies.clear();
  }
}

class FrontendAuthTester {
  constructor(baseUrl = 'http://localhost:8000') {
    this.baseUrl = baseUrl;
    this.cookieJar = new CookieJar();
  }

  log(message, level = 'INFO') {
    const timestamp = new Date().toLocaleTimeString();
    console.log(`[${timestamp}] ${level}: ${message}`);
  }

  async makeRequest(endpoint, options = {}) {
    const url = `${this.baseUrl}${endpoint}`;
    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      ...options.headers
    };

    // Add cookies to request
    const cookieHeader = this.cookieJar.getCookieHeader();
    if (cookieHeader) {
      headers['Cookie'] = cookieHeader;
    }

    const response = await fetch(url, {
      ...options,
      headers
    });

    // Extract and store cookies from response
    const setCookieHeader = response.headers.get('set-cookie');
    if (setCookieHeader) {
      this.cookieJar.setCookie(setCookieHeader, url);
    }

    return response;
  }

  async testLogin(username, password) {
    this.log('Testing frontend-style login with httpOnly cookies...');
    
    try {
      const response = await this.makeRequest('/api/auth/login/', {
        method: 'POST',
        body: JSON.stringify({ username, password })
      });

      this.log(`Login response status: ${response.status}`);

      if (response.status === 200) {
        const data = await response.json();
        this.log(`Login successful for user: ${data.user?.username || 'unknown'}`);
        
        // Check if we have cookies (simulating httpOnly behavior)
        const hasCookies = this.cookieJar.cookies.size > 0;
        this.log(`Cookies received: ${hasCookies ? 'Yes' : 'No'} (${this.cookieJar.cookies.size} cookies)`);
        
        return true;
      } else {
        const errorData = await response.json().catch(() => ({}));
        this.log(`Login failed: ${errorData.message || 'Unknown error'}`, 'ERROR');
        return false;
      }
    } catch (error) {
      this.log(`Login test failed: ${error.message}`, 'ERROR');
      return false;
    }
  }

  async testTokenVerification() {
    this.log('Testing token verification via cookies...');
    
    try {
      const response = await this.makeRequest('/api/auth/verify/');
      
      this.log(`Token verification response status: ${response.status}`);
      
      if (response.status === 200) {
        const data = await response.json();
        this.log(`Token verification successful - User: ${data.username} (ID: ${data.id})`);
        return true;
      } else {
        const errorData = await response.json().catch(() => ({}));
        this.log(`Token verification failed: ${errorData.message || 'Unknown error'}`, 'ERROR');
        return false;
      }
    } catch (error) {
      this.log(`Token verification test failed: ${error.message}`, 'ERROR');
      return false;
    }
  }

  async testApiCall() {
    this.log('Testing authenticated API call...');
    
    try {
      const response = await this.makeRequest('/api/auth/user/');
      
      this.log(`API call response status: ${response.status}`);
      
      if (response.status === 200) {
        const data = await response.json();
        this.log(`API call successful - User: ${data.username}`);
        return true;
      } else {
        const errorData = await response.json().catch(() => ({}));
        this.log(`API call failed: ${errorData.message || 'Unknown error'}`, 'ERROR');
        return false;
      }
    } catch (error) {
      this.log(`API call test failed: ${error.message}`, 'ERROR');
      return false;
    }
  }

  async testTokenRefresh() {
    this.log('Testing token refresh...');
    
    try {
      const response = await this.makeRequest('/api/auth/refresh/', {
        method: 'POST'
      });
      
      this.log(`Token refresh response status: ${response.status}`);
      
      if (response.status === 200) {
        const data = await response.json();
        this.log(`Token refresh successful: ${data.message || 'Success'}`);
        return true;
      } else {
        const errorData = await response.json().catch(() => ({}));
        this.log(`Token refresh failed: ${errorData.message || 'Unknown error'}`, 'ERROR');
        return false;
      }
    } catch (error) {
      this.log(`Token refresh test failed: ${error.message}`, 'ERROR');
      return false;
    }
  }

  async testLogout() {
    this.log('Testing logout...');
    
    try {
      const response = await this.makeRequest('/api/auth/logout/', {
        method: 'POST'
      });
      
      this.log(`Logout response status: ${response.status}`);
      
      if (response.status === 200) {
        const data = await response.json();
        this.log(`Logout successful: ${data.message || 'Success'}`);
        
        // Clear local cookies (simulating browser behavior)
        this.cookieJar.clear();
        
        return true;
      } else {
        const errorData = await response.json().catch(() => ({}));
        this.log(`Logout failed: ${errorData.message || 'Unknown error'}`, 'ERROR');
        return false;
      }
    } catch (error) {
      this.log(`Logout test failed: ${error.message}`, 'ERROR');
      return false;
    }
  }

  async runFullTest(username, password) {
    this.log('='.repeat(60));
    this.log('STARTING FRONTEND AUTHENTICATION INTEGRATION TEST');
    this.log('='.repeat(60));

    const results = {};

    // Test 1: Login
    results.login = await this.testLogin(username, password);

    if (results.login) {
      // Test 2: Token verification
      results.tokenVerification = await this.testTokenVerification();

      // Test 3: API call
      results.apiCall = await this.testApiCall();

      // Test 4: Token refresh
      results.tokenRefresh = await this.testTokenRefresh();

      // Test 5: Verify still works after refresh
      results.postRefreshVerification = await this.testTokenVerification();

      // Test 6: Logout
      results.logout = await this.testLogout();

      // Test 7: Verify logout worked
      results.postLogoutVerification = !(await this.testTokenVerification());
    } else {
      this.log('Skipping authenticated tests due to login failure', 'WARN');
      Object.assign(results, {
        tokenVerification: false,
        apiCall: false,
        tokenRefresh: false,
        postRefreshVerification: false,
        logout: false,
        postLogoutVerification: false
      });
    }

    return results;
  }

  printSummary(results) {
    this.log('='.repeat(60));
    this.log('FRONTEND TEST RESULTS SUMMARY');
    this.log('='.repeat(60));

    const passed = Object.values(results).filter(Boolean).length;
    const total = Object.keys(results).length;

    Object.entries(results).forEach(([test, passed]) => {
      const status = passed ? 'PASS' : 'FAIL';
      this.log(`${test.replace(/([A-Z])/g, ' $1').trim()}: ${status}`);
    });

    this.log('-'.repeat(60));
    this.log(`OVERALL: ${passed}/${total} tests passed (${((passed/total)*100).toFixed(1)}%)`);

    if (passed === total) {
      this.log('🎉 ALL FRONTEND TESTS PASSED! Authentication integration is working correctly.', 'SUCCESS');
    } else {
      this.log(`❌ ${total - passed} tests failed. Please review the frontend authentication integration.`, 'ERROR');
    }

    return passed === total;
  }
}

// Main execution
async function main() {
  if (process.argv.length < 4) {
    console.log('Usage: node test_frontend_auth.js <username> <password>');
    console.log('Example: node test_frontend_auth.js admin password123');
    process.exit(1);
  }

  const username = process.argv[2];
  const password = process.argv[3];

  const tester = new FrontendAuthTester();
  const results = await tester.runFullTest(username, password);
  const success = tester.printSummary(results);

  process.exit(success ? 0 : 1);
}

if (require.main === module) {
  main().catch(error => {
    console.error('Test failed with error:', error);
    process.exit(1);
  });
}
