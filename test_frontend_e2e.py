#!/usr/bin/env python3
"""
End-to-End Frontend Authentication Test
Tests the complete frontend authentication flow with httpOnly cookies
"""

import requests
import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

class FrontendE2ETest:
    def __init__(self):
        self.frontend_url = "http://localhost:5177"
        self.backend_url = "http://localhost:8000"
        self.driver = None
        
    def setup_driver(self):
        """Setup Chrome driver with appropriate options"""
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        # chrome_options.add_argument("--headless")  # Uncomment for headless mode
        
        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.implicitly_wait(10)
            return True
        except Exception as e:
            print(f"❌ Failed to setup Chrome driver: {e}")
            return False
    
    def test_login_flow(self):
        """Test the complete login flow"""
        print("\n🔐 Testing Frontend Login Flow...")
        
        try:
            # Navigate to login page
            self.driver.get(f"{self.frontend_url}/login")
            print("   📍 Navigated to login page")
            
            # Wait for login form to load
            wait = WebDriverWait(self.driver, 10)
            username_field = wait.until(
                EC.presence_of_element_located((By.NAME, "username"))
            )
            password_field = self.driver.find_element(By.NAME, "password")
            login_button = self.driver.find_element(By.TYPE, "submit")
            
            print("   📝 Login form loaded successfully")
            
            # Fill in credentials
            username_field.clear()
            username_field.send_keys("test_api_user")
            password_field.clear()
            password_field.send_keys("password123")
            
            print("   ✏️ Credentials entered")
            
            # Submit login form
            login_button.click()
            print("   🚀 Login form submitted")
            
            # Wait for redirect or success
            time.sleep(3)
            
            # Check if we're redirected to dashboard or authenticated area
            current_url = self.driver.current_url
            if "/login" not in current_url:
                print(f"   ✅ Login successful - redirected to: {current_url}")
                return True
            else:
                print("   ❌ Login failed - still on login page")
                return False
                
        except TimeoutException:
            print("   ❌ Login form not found - timeout")
            return False
        except Exception as e:
            print(f"   ❌ Login test failed: {e}")
            return False
    
    def test_authenticated_navigation(self):
        """Test navigation while authenticated"""
        print("\n🧭 Testing Authenticated Navigation...")
        
        try:
            # Try to access a protected route
            self.driver.get(f"{self.frontend_url}/admin/dashboard")
            time.sleep(2)
            
            current_url = self.driver.current_url
            if "/login" in current_url:
                print("   ❌ Redirected to login - authentication failed")
                return False
            else:
                print(f"   ✅ Successfully accessed protected route: {current_url}")
                return True
                
        except Exception as e:
            print(f"   ❌ Navigation test failed: {e}")
            return False
    
    def test_logout_flow(self):
        """Test the logout functionality"""
        print("\n🚪 Testing Logout Flow...")
        
        try:
            # Look for logout button or user menu
            logout_elements = [
                "//button[contains(text(), 'Logout')]",
                "//button[contains(text(), 'تسجيل الخروج')]",
                "//a[contains(text(), 'Logout')]",
                "//a[contains(text(), 'تسجيل الخروج')]",
                "[data-testid='logout-button']",
                ".logout-button"
            ]
            
            logout_button = None
            for selector in logout_elements:
                try:
                    if selector.startswith("//"):
                        logout_button = self.driver.find_element(By.XPATH, selector)
                    elif selector.startswith("["):
                        logout_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    else:
                        logout_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    break
                except NoSuchElementException:
                    continue
            
            if logout_button:
                logout_button.click()
                print("   🖱️ Logout button clicked")
                time.sleep(2)
                
                # Check if redirected to login
                current_url = self.driver.current_url
                if "/login" in current_url:
                    print("   ✅ Logout successful - redirected to login")
                    return True
                else:
                    print(f"   ⚠️ Logout clicked but not redirected: {current_url}")
                    return False
            else:
                print("   ⚠️ Logout button not found - checking manual logout")
                # Try manual logout via API
                self.driver.execute_script("""
                    fetch('/api/auth/logout/', {
                        method: 'POST',
                        credentials: 'include'
                    }).then(() => {
                        window.location.href = '/login';
                    });
                """)
                time.sleep(2)
                return "/login" in self.driver.current_url
                
        except Exception as e:
            print(f"   ❌ Logout test failed: {e}")
            return False
    
    def test_cookie_security(self):
        """Test that authentication cookies are properly secured"""
        print("\n🍪 Testing Cookie Security...")
        
        try:
            # Check if tokens are accessible via JavaScript (they shouldn't be)
            token_accessible = self.driver.execute_script("""
                try {
                    const hasLocalToken = !!localStorage.getItem('access_token');
                    const hasSessionToken = !!sessionStorage.getItem('access_token');
                    const cookieMatch = document.cookie.match(/access_token=([^;]+)/);
                    
                    return {
                        localStorage: hasLocalToken,
                        sessionStorage: hasSessionToken,
                        accessibleCookie: !!cookieMatch
                    };
                } catch (e) {
                    return { error: e.message };
                }
            """)
            
            if token_accessible.get('localStorage'):
                print("   ❌ SECURITY ISSUE: Tokens found in localStorage")
                return False
            elif token_accessible.get('sessionStorage'):
                print("   ❌ SECURITY ISSUE: Tokens found in sessionStorage")
                return False
            elif token_accessible.get('accessibleCookie'):
                print("   ⚠️ WARNING: Access tokens accessible via JavaScript")
                return False
            else:
                print("   ✅ Security check passed - tokens not accessible via JavaScript")
                return True
                
        except Exception as e:
            print(f"   ❌ Cookie security test failed: {e}")
            return False
    
    def run_all_tests(self):
        """Run all frontend tests"""
        print("🧪 Starting Frontend End-to-End Tests")
        print("=" * 50)
        
        if not self.setup_driver():
            return False
        
        try:
            results = {
                'login': self.test_login_flow(),
                'navigation': self.test_authenticated_navigation(),
                'security': self.test_cookie_security(),
                'logout': self.test_logout_flow()
            }
            
            print(f"\n📊 Test Results:")
            print(f"   🔐 Login Flow: {'✅' if results['login'] else '❌'}")
            print(f"   🧭 Navigation: {'✅' if results['navigation'] else '❌'}")
            print(f"   🍪 Security: {'✅' if results['security'] else '❌'}")
            print(f"   🚪 Logout: {'✅' if results['logout'] else '❌'}")
            
            success_count = sum(results.values())
            total_tests = len(results)
            
            print(f"\n🎯 Overall Result: {success_count}/{total_tests} tests passed")
            
            if success_count == total_tests:
                print("🎉 All frontend tests passed!")
                return True
            else:
                print("⚠️ Some frontend tests failed")
                return False
                
        finally:
            if self.driver:
                self.driver.quit()

if __name__ == "__main__":
    tester = FrontendE2ETest()
    success = tester.run_all_tests()
    exit(0 if success else 1)
