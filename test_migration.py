#!/usr/bin/env python3
"""
Test Authentication Migration
Verifies that the httpOnly cookie migration is working correctly
"""

import requests
import json

def test_migration():
    base_url = "http://localhost:8000"
    session = requests.Session()
    
    print("🔄 Testing Authentication Migration")
    print("=" * 50)
    
    # Test 1: Login and verify httpOnly cookies
    print("\n1️⃣ Testing Login with httpOnly Cookies...")
    try:
        login_response = session.post(
            f"{base_url}/api/auth/login/",
            json={
                "username": "test_api_user",
                "password": "password123"
            },
            timeout=10
        )
        
        if login_response.status_code == 200:
            data = login_response.json()
            print(f"   ✅ Login successful for user: {data['user']['username']}")
            
            # Check httpOnly cookies
            set_cookies = login_response.headers.get('Set-Cookie', '')
            has_httponly = 'HttpOnly' in set_cookies
            has_access_token = 'access_token' in set_cookies
            has_refresh_token = 'refresh_token' in set_cookies
            
            print(f"   🔒 HttpOnly cookies: {'✅' if has_httponly else '❌'}")
            print(f"   🔑 Access token: {'✅' if has_access_token else '❌'}")
            print(f"   🔄 Refresh token: {'✅' if has_refresh_token else '❌'}")
            
            # Verify tokens are NOT in response body (security improvement)
            response_has_tokens = 'access_token' in data and 'refresh_token' in data
            print(f"   📦 Response includes tokens: {'⚠️ Yes (legacy)' if response_has_tokens else '✅ No (secure)'}")
            
        else:
            print(f"   ❌ Login failed: {login_response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Login error: {str(e)}")
        return False
    
    # Test 2: API call using httpOnly cookies
    print("\n2️⃣ Testing API Call with httpOnly Cookies...")
    try:
        api_response = session.get(
            f"{base_url}/api/auth/verify/",
            timeout=10
        )
        
        if api_response.status_code == 200:
            user_data = api_response.json()
            print(f"   ✅ API call successful via httpOnly cookies")
            print(f"   👤 User: {user_data['username']}")
            print(f"   🏢 Role: {user_data['role']['name']}")
        else:
            print(f"   ❌ API call failed: {api_response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ API call error: {str(e)}")
        return False
    
    # Test 3: Logout and cookie cleanup
    print("\n3️⃣ Testing Logout and Cookie Cleanup...")
    try:
        logout_response = session.post(
            f"{base_url}/api/auth/logout/",
            timeout=10
        )
        
        if logout_response.status_code == 200:
            print(f"   ✅ Logout successful")
            
            # Check if cookies were cleared
            set_cookies = logout_response.headers.get('Set-Cookie', '')
            cookies_cleared = 'access_token=' in set_cookies
            print(f"   🧹 Cookies cleared: {'✅' if cookies_cleared else '❌'}")
            
        else:
            print(f"   ❌ Logout failed: {logout_response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Logout error: {str(e)}")
        return False
    
    # Test 4: Verify authentication is cleared
    print("\n4️⃣ Testing Post-Logout Authentication...")
    try:
        verify_response = session.get(
            f"{base_url}/api/auth/verify/",
            timeout=10
        )
        
        if verify_response.status_code == 401:
            print(f"   ✅ Authentication properly cleared (401)")
        else:
            print(f"   ⚠️ Unexpected status: {verify_response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Verification error: {str(e)}")
    
    print("\n🎉 Migration test completed successfully!")
    print("✅ httpOnly cookie authentication is working correctly")
    print("🔒 Enhanced security: Tokens are no longer accessible via JavaScript")
    return True

if __name__ == "__main__":
    test_migration()
