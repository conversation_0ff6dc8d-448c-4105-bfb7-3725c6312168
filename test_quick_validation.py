#!/usr/bin/env python3
"""
Quick validation test for authentication endpoints
"""

import requests
import json

def test_validation():
    base_url = "http://localhost:8000"
    
    print("🧪 Testing Authentication Validation")
    print("=" * 40)
    
    # Test 1: Missing credentials
    print("\n1️⃣ Testing missing credentials...")
    try:
        response = requests.post(
            f"{base_url}/api/auth/login/",
            json={},
            timeout=10
        )
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.text}")
        
        if response.status_code == 400:
            print("   ✅ Missing credentials correctly rejected (400)")
        else:
            print(f"   ❌ Expected 400, got {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 2: Partial credentials
    print("\n2️⃣ Testing partial credentials...")
    try:
        response = requests.post(
            f"{base_url}/api/auth/login/",
            json={"username": "test"},
            timeout=10
        )
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.text}")
        
        if response.status_code == 400:
            print("   ✅ Partial credentials correctly rejected (400)")
        else:
            print(f"   ❌ Expected 400, got {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 3: Invalid credentials
    print("\n3️⃣ Testing invalid credentials...")
    try:
        response = requests.post(
            f"{base_url}/api/auth/login/",
            json={"username": "invalid", "password": "wrong"},
            timeout=10
        )
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.text}")
        
        if response.status_code == 401:
            print("   ✅ Invalid credentials correctly rejected (401)")
        else:
            print(f"   ❌ Expected 401, got {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 4: Valid credentials
    print("\n4️⃣ Testing valid credentials...")
    try:
        response = requests.post(
            f"{base_url}/api/auth/login/",
            json={"username": "test_api_user", "password": "password123"},
            timeout=10
        )
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Valid credentials accepted: {data['user']['username']}")
        else:
            print(f"   ❌ Expected 200, got {response.status_code}")
            print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")

if __name__ == "__main__":
    test_validation()
